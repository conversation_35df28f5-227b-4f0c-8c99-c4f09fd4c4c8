[/Script/EngineSettings.GameMapsSettings]
GameDefaultMap=/Game/Maps/Roguelike/Title/Rogue_Title.Rogue_Title
GlobalDefaultGameMode=/Game/Core/FrameWork/Base/GameMode/AW_GameMode_DebugEnter.AW_GameMode_DebugEnter_C
GameInstanceClass=/Game/Core/FrameWork/Base/AW_GameInstance_BP.AW_GameInstance_BP_C
EditorStartupMap=/Game/Maps/Debug/DebugEnter.DebugEnter

[/Script/HardwareTargeting.HardwareTargetingSettings]
TargetedHardwareClass=Desktop
AppliedTargetedHardwareClass=Desktop
DefaultGraphicsPerformance=Maximum
AppliedDefaultGraphicsPerformance=Maximum

[/Script/Engine.Engine]
+ActiveGameNameRedirects=(OldGameName="TP_Blank",NewGameName="/Script/TheAwakener_FO")
+ActiveGameNameRedirects=(OldGameName="/Script/ProjectAwaker",NewGameName="/Script/TheAwakener_FO")
+ActiveGameNameRedirects=(OldGameName="/Script/TP_Blank",NewGameName="/Script/TheAwakener_FO")
+ActiveClassRedirects=(OldClassName="TP_BlankGameModeBase",NewClassName="TheAwakener_FOGameModeBase")
+NetDriverDefinitions=(DefName="GameNetDriver",DriverClassName="OnlineSubsystemSteam.SteamNetDriver",DriverClassNameFallback="OnlineSubsystemUtils.IpNetDriver")
MaximumLoopIterationCount=1000000
GameUserSettingsClassName=/Script/TheAwakener_FO.RogueGameSetting
GameViewportClientClassName=/Script/TheAwakener_FO.AwGameViewportClient
gc.VerifyUObjectsAreNotUnreachable=true
gc.AllowParallelGC=False

[OnlineSubsystem]
DefaultPlatformService=Steam

[Save]
FixupStandaloneFlags=1

[OnlineSubsystemSteam]
bEnabled=true
SteamDevAppId=2527660
StoveEnv="LIVE"
StoveAppKey="44159d392b99f7b6c4152b37965d4e3209e966cee0a39597c91efa53c458bbb9"
StoveSecretKey="2b9bbc19be57dd8076f566575a533a39e4eb2e041f84fbd639f77298d22197efd4468c8d6e3d0a7ccc16c2c34ae295fe"
StoveGameId="THEAWAKENERFORGOTTENOATH_IND"
StoveLogLevel=4
StoveLogPath=""
; Set DevAppId to your actual game id shown on steamworks
Achievement_0_Id="ACH_KILL_DEATHLORD"

Achievement_1_Id="ACH_KILL_LAVAGOLEM"

Achievement_2_Id="ACH_KILL_BONEBREAKER"

Achievement_3_Id="ACH_KILL_DEATHLORDEX"

Achievement_4_Id="ACH_UNLOCK_HENRIK"

Achievement_5_Id="ACH_UNLOCK_SOLA"

Achievement_6_Id="ACH_GET_GRAIL"

Achievement_7_Id="ACH_CLEAR_REALENDING"

Achievement_8_Id="ACH_CLEAR_CURSE_LEVEL_1"

Achievement_9_Id="ACH_CLEAR_CURSE_LEVEL_5"

; Set the string corresponds to ApI Name used on steamworks

[/Script/OnlineSubsystemSteam.SteamNetDriver]
NetConnectionClassName="OnlineSubsystemSteam.SteamNetConnection"

[/Script/Engine.CollisionProfile]
-Profiles=(Name="NoCollision",CollisionEnabled=NoCollision,ObjectTypeName="WorldStatic",CustomResponses=((Channel="Visibility",Response=ECR_Ignore),(Channel="Camera",Response=ECR_Ignore)),HelpMessage="No collision",bCanModify=False)
-Profiles=(Name="BlockAll",CollisionEnabled=QueryAndPhysics,ObjectTypeName="WorldStatic",CustomResponses=,HelpMessage="WorldStatic object that blocks all actors by default. All new custom channels will use its own default response. ",bCanModify=False)
-Profiles=(Name="OverlapAll",CollisionEnabled=QueryOnly,ObjectTypeName="WorldStatic",CustomResponses=((Channel="WorldStatic",Response=ECR_Overlap),(Channel="Pawn",Response=ECR_Overlap),(Channel="Visibility",Response=ECR_Overlap),(Channel="WorldDynamic",Response=ECR_Overlap),(Channel="Camera",Response=ECR_Overlap),(Channel="PhysicsBody",Response=ECR_Overlap),(Channel="Vehicle",Response=ECR_Overlap),(Channel="Destructible",Response=ECR_Overlap)),HelpMessage="WorldStatic object that overlaps all actors by default. All new custom channels will use its own default response. ",bCanModify=False)
-Profiles=(Name="BlockAllDynamic",CollisionEnabled=QueryAndPhysics,ObjectTypeName="WorldDynamic",CustomResponses=,HelpMessage="WorldDynamic object that blocks all actors by default. All new custom channels will use its own default response. ",bCanModify=False)
-Profiles=(Name="OverlapAllDynamic",CollisionEnabled=QueryOnly,ObjectTypeName="WorldDynamic",CustomResponses=((Channel="WorldStatic",Response=ECR_Overlap),(Channel="Pawn",Response=ECR_Overlap),(Channel="Visibility",Response=ECR_Overlap),(Channel="WorldDynamic",Response=ECR_Overlap),(Channel="Camera",Response=ECR_Overlap),(Channel="PhysicsBody",Response=ECR_Overlap),(Channel="Vehicle",Response=ECR_Overlap),(Channel="Destructible",Response=ECR_Overlap)),HelpMessage="WorldDynamic object that overlaps all actors by default. All new custom channels will use its own default response. ",bCanModify=False)
-Profiles=(Name="IgnoreOnlyPawn",CollisionEnabled=QueryOnly,ObjectTypeName="WorldDynamic",CustomResponses=((Channel="Pawn",Response=ECR_Ignore),(Channel="Vehicle",Response=ECR_Ignore)),HelpMessage="WorldDynamic object that ignores Pawn and Vehicle. All other channels will be set to default.",bCanModify=False)
-Profiles=(Name="OverlapOnlyPawn",CollisionEnabled=QueryOnly,ObjectTypeName="WorldDynamic",CustomResponses=((Channel="Pawn",Response=ECR_Overlap),(Channel="Vehicle",Response=ECR_Overlap),(Channel="Camera",Response=ECR_Ignore)),HelpMessage="WorldDynamic object that overlaps Pawn, Camera, and Vehicle. All other channels will be set to default. ",bCanModify=False)
-Profiles=(Name="Pawn",CollisionEnabled=QueryAndPhysics,ObjectTypeName="Pawn",CustomResponses=((Channel="Visibility",Response=ECR_Ignore)),HelpMessage="Pawn object. Can be used for capsule of any playerable character or AI. ",bCanModify=False)
-Profiles=(Name="Spectator",CollisionEnabled=QueryOnly,ObjectTypeName="Pawn",CustomResponses=((Channel="WorldStatic",Response=ECR_Block),(Channel="Pawn",Response=ECR_Ignore),(Channel="Visibility",Response=ECR_Ignore),(Channel="WorldDynamic",Response=ECR_Ignore),(Channel="Camera",Response=ECR_Ignore),(Channel="PhysicsBody",Response=ECR_Ignore),(Channel="Vehicle",Response=ECR_Ignore),(Channel="Destructible",Response=ECR_Ignore)),HelpMessage="Pawn object that ignores all other actors except WorldStatic.",bCanModify=False)
-Profiles=(Name="CharacterMesh",CollisionEnabled=QueryOnly,ObjectTypeName="Pawn",CustomResponses=((Channel="Pawn",Response=ECR_Ignore),(Channel="Vehicle",Response=ECR_Ignore),(Channel="Visibility",Response=ECR_Ignore)),HelpMessage="Pawn object that is used for Character Mesh. All other channels will be set to default.",bCanModify=False)
-Profiles=(Name="PhysicsActor",CollisionEnabled=QueryAndPhysics,ObjectTypeName="PhysicsBody",CustomResponses=,HelpMessage="Simulating actors",bCanModify=False)
-Profiles=(Name="Destructible",CollisionEnabled=QueryAndPhysics,ObjectTypeName="Destructible",CustomResponses=,HelpMessage="Destructible actors",bCanModify=False)
-Profiles=(Name="InvisibleWall",CollisionEnabled=QueryAndPhysics,ObjectTypeName="WorldStatic",CustomResponses=((Channel="Visibility",Response=ECR_Ignore)),HelpMessage="WorldStatic object that is invisible.",bCanModify=False)
-Profiles=(Name="InvisibleWallDynamic",CollisionEnabled=QueryAndPhysics,ObjectTypeName="WorldDynamic",CustomResponses=((Channel="Visibility",Response=ECR_Ignore)),HelpMessage="WorldDynamic object that is invisible.",bCanModify=False)
-Profiles=(Name="Trigger",CollisionEnabled=QueryOnly,ObjectTypeName="WorldDynamic",CustomResponses=((Channel="WorldStatic",Response=ECR_Overlap),(Channel="Pawn",Response=ECR_Overlap),(Channel="Visibility",Response=ECR_Ignore),(Channel="WorldDynamic",Response=ECR_Overlap),(Channel="Camera",Response=ECR_Overlap),(Channel="PhysicsBody",Response=ECR_Overlap),(Channel="Vehicle",Response=ECR_Overlap),(Channel="Destructible",Response=ECR_Overlap)),HelpMessage="WorldDynamic object that is used for trigger. All other channels will be set to default.",bCanModify=False)
-Profiles=(Name="Ragdoll",CollisionEnabled=QueryAndPhysics,ObjectTypeName="PhysicsBody",CustomResponses=((Channel="Pawn",Response=ECR_Ignore),(Channel="Visibility",Response=ECR_Ignore)),HelpMessage="Simulating Skeletal Mesh Component. All other channels will be set to default.",bCanModify=False)
-Profiles=(Name="Vehicle",CollisionEnabled=QueryAndPhysics,ObjectTypeName="Vehicle",CustomResponses=,HelpMessage="Vehicle object that blocks Vehicle, WorldStatic, and WorldDynamic. All other channels will be set to default.",bCanModify=False)
-Profiles=(Name="UI",CollisionEnabled=QueryOnly,ObjectTypeName="WorldDynamic",CustomResponses=((Channel="WorldStatic",Response=ECR_Overlap),(Channel="Pawn",Response=ECR_Overlap),(Channel="Visibility",Response=ECR_Block),(Channel="WorldDynamic",Response=ECR_Overlap),(Channel="Camera",Response=ECR_Overlap),(Channel="PhysicsBody",Response=ECR_Overlap),(Channel="Vehicle",Response=ECR_Overlap),(Channel="Destructible",Response=ECR_Overlap)),HelpMessage="WorldStatic object that overlaps all actors by default. All new custom channels will use its own default response. ",bCanModify=False)
+Profiles=(Name="NoCollision",CollisionEnabled=NoCollision,bCanModify=False,ObjectTypeName="WorldStatic",CustomResponses=((Channel="Visibility",Response=ECR_Ignore),(Channel="Camera",Response=ECR_Ignore)),HelpMessage="No collision")
+Profiles=(Name="BlockAll",CollisionEnabled=QueryAndPhysics,bCanModify=False,ObjectTypeName="WorldStatic",CustomResponses=,HelpMessage="WorldStatic object that blocks all actors by default. All new custom channels will use its own default response. ")
+Profiles=(Name="OverlapAll",CollisionEnabled=QueryOnly,bCanModify=False,ObjectTypeName="WorldStatic",CustomResponses=((Channel="WorldStatic",Response=ECR_Overlap),(Channel="Pawn",Response=ECR_Overlap),(Channel="Visibility",Response=ECR_Overlap),(Channel="WorldDynamic",Response=ECR_Overlap),(Channel="Camera",Response=ECR_Overlap),(Channel="PhysicsBody",Response=ECR_Overlap),(Channel="Vehicle",Response=ECR_Overlap),(Channel="Destructible",Response=ECR_Overlap)),HelpMessage="WorldStatic object that overlaps all actors by default. All new custom channels will use its own default response. ")
+Profiles=(Name="BlockAllDynamic",CollisionEnabled=QueryAndPhysics,bCanModify=False,ObjectTypeName="WorldDynamic",CustomResponses=,HelpMessage="WorldDynamic object that blocks all actors by default. All new custom channels will use its own default response. ")
+Profiles=(Name="OverlapAllDynamic",CollisionEnabled=QueryOnly,bCanModify=False,ObjectTypeName="WorldDynamic",CustomResponses=((Channel="WorldStatic",Response=ECR_Overlap),(Channel="Pawn",Response=ECR_Overlap),(Channel="Visibility",Response=ECR_Overlap),(Channel="WorldDynamic",Response=ECR_Overlap),(Channel="Camera",Response=ECR_Overlap),(Channel="PhysicsBody",Response=ECR_Overlap),(Channel="Vehicle",Response=ECR_Overlap),(Channel="Destructible",Response=ECR_Overlap)),HelpMessage="WorldDynamic object that overlaps all actors by default. All new custom channels will use its own default response. ")
+Profiles=(Name="IgnoreOnlyPawn",CollisionEnabled=QueryOnly,bCanModify=False,ObjectTypeName="WorldDynamic",CustomResponses=((Channel="Pawn",Response=ECR_Ignore),(Channel="Vehicle",Response=ECR_Ignore)),HelpMessage="WorldDynamic object that ignores Pawn and Vehicle. All other channels will be set to default.")
+Profiles=(Name="OverlapOnlyPawn",CollisionEnabled=QueryOnly,bCanModify=False,ObjectTypeName="WorldDynamic",CustomResponses=((Channel="Pawn",Response=ECR_Overlap),(Channel="Vehicle",Response=ECR_Overlap),(Channel="Camera",Response=ECR_Ignore)),HelpMessage="WorldDynamic object that overlaps Pawn, Camera, and Vehicle. All other channels will be set to default. ")
+Profiles=(Name="Pawn",CollisionEnabled=QueryAndPhysics,bCanModify=False,ObjectTypeName="Pawn",CustomResponses=((Channel="Visibility",Response=ECR_Ignore)),HelpMessage="Pawn object. Can be used for capsule of any playerable character or AI. ")
+Profiles=(Name="Spectator",CollisionEnabled=QueryOnly,bCanModify=False,ObjectTypeName="Pawn",CustomResponses=((Channel="WorldStatic"),(Channel="Pawn",Response=ECR_Ignore),(Channel="Visibility",Response=ECR_Ignore),(Channel="WorldDynamic",Response=ECR_Ignore),(Channel="Camera",Response=ECR_Ignore),(Channel="PhysicsBody",Response=ECR_Ignore),(Channel="Vehicle",Response=ECR_Ignore),(Channel="Destructible",Response=ECR_Ignore)),HelpMessage="Pawn object that ignores all other actors except WorldStatic.")
+Profiles=(Name="CharacterMesh",CollisionEnabled=QueryOnly,bCanModify=False,ObjectTypeName="Pawn",CustomResponses=((Channel="Pawn",Response=ECR_Ignore),(Channel="Vehicle",Response=ECR_Ignore),(Channel="Visibility",Response=ECR_Ignore)),HelpMessage="Pawn object that is used for Character Mesh. All other channels will be set to default.")
+Profiles=(Name="PhysicsActor",CollisionEnabled=QueryAndPhysics,bCanModify=False,ObjectTypeName="PhysicsBody",CustomResponses=,HelpMessage="Simulating actors")
+Profiles=(Name="Destructible",CollisionEnabled=QueryAndPhysics,bCanModify=False,ObjectTypeName="Destructible",CustomResponses=,HelpMessage="Destructible actors")
+Profiles=(Name="InvisibleWall",CollisionEnabled=QueryAndPhysics,bCanModify=False,ObjectTypeName="WorldStatic",CustomResponses=((Channel="Visibility",Response=ECR_Ignore)),HelpMessage="WorldStatic object that is invisible.")
+Profiles=(Name="InvisibleWallDynamic",CollisionEnabled=QueryAndPhysics,bCanModify=False,ObjectTypeName="WorldDynamic",CustomResponses=((Channel="Visibility",Response=ECR_Ignore)),HelpMessage="WorldDynamic object that is invisible.")
+Profiles=(Name="Trigger",CollisionEnabled=QueryOnly,bCanModify=False,ObjectTypeName="WorldDynamic",CustomResponses=((Channel="WorldStatic",Response=ECR_Overlap),(Channel="Pawn",Response=ECR_Overlap),(Channel="Visibility",Response=ECR_Ignore),(Channel="WorldDynamic",Response=ECR_Overlap),(Channel="Camera",Response=ECR_Overlap),(Channel="PhysicsBody",Response=ECR_Overlap),(Channel="Vehicle",Response=ECR_Overlap),(Channel="Destructible",Response=ECR_Overlap)),HelpMessage="WorldDynamic object that is used for trigger. All other channels will be set to default.")
+Profiles=(Name="Ragdoll",CollisionEnabled=QueryAndPhysics,bCanModify=False,ObjectTypeName="PhysicsBody",CustomResponses=((Channel="Pawn",Response=ECR_Ignore),(Channel="Visibility",Response=ECR_Ignore)),HelpMessage="Simulating Skeletal Mesh Component. All other channels will be set to default.")
+Profiles=(Name="Vehicle",CollisionEnabled=QueryAndPhysics,bCanModify=False,ObjectTypeName="Vehicle",CustomResponses=,HelpMessage="Vehicle object that blocks Vehicle, WorldStatic, and WorldDynamic. All other channels will be set to default.")
+Profiles=(Name="UI",CollisionEnabled=QueryOnly,bCanModify=False,ObjectTypeName="WorldDynamic",CustomResponses=((Channel="WorldStatic",Response=ECR_Overlap),(Channel="Pawn",Response=ECR_Overlap),(Channel="Visibility"),(Channel="WorldDynamic",Response=ECR_Overlap),(Channel="Camera",Response=ECR_Overlap),(Channel="PhysicsBody",Response=ECR_Overlap),(Channel="Vehicle",Response=ECR_Overlap),(Channel="Destructible",Response=ECR_Overlap)),HelpMessage="WorldStatic object that overlaps all actors by default. All new custom channels will use its own default response. ")
+Profiles=(Name="AwCharacter",CollisionEnabled=QueryOnly,bCanModify=True,ObjectTypeName="Pawn",CustomResponses=((Channel="Visibility",Response=ECR_Overlap),(Channel="Camera",Response=ECR_Ignore),(Channel="TerrainTrace"),(Channel="AttackComplexTrace")),HelpMessage="Needs description")
+Profiles=(Name="AttackHitBox",CollisionEnabled=QueryOnly,bCanModify=True,ObjectTypeName="AttackHitBox",CustomResponses=((Channel="WorldStatic",Response=ECR_Ignore),(Channel="WorldDynamic",Response=ECR_Overlap),(Channel="Pawn",Response=ECR_Ignore),(Channel="Visibility",Response=ECR_Ignore),(Channel="Camera",Response=ECR_Ignore),(Channel="PhysicsBody",Response=ECR_Ignore),(Channel="Vehicle",Response=ECR_Ignore),(Channel="Destructible",Response=ECR_Ignore),(Channel="Foliage",Response=ECR_Ignore),(Channel="AttackHitBox",Response=ECR_Ignore),(Channel="Bullet",Response=ECR_Overlap),(Channel="AwCharacter",Response=ECR_Ignore)),HelpMessage="Character Attack Hit Box")
+Profiles=(Name="CharacterHitBox",CollisionEnabled=QueryOnly,bCanModify=True,ObjectTypeName="CharacterHitBox",CustomResponses=((Channel="WorldStatic",Response=ECR_Ignore),(Channel="WorldDynamic",Response=ECR_Overlap),(Channel="Pawn",Response=ECR_Ignore),(Channel="Visibility",Response=ECR_Ignore),(Channel="Camera",Response=ECR_Ignore),(Channel="PhysicsBody",Response=ECR_Ignore),(Channel="Vehicle",Response=ECR_Ignore),(Channel="Destructible",Response=ECR_Ignore),(Channel="Foliage",Response=ECR_Ignore),(Channel="CharacterHitBox",Response=ECR_Ignore),(Channel="Bullet",Response=ECR_Overlap),(Channel="AwCharacter",Response=ECR_Ignore)),HelpMessage="As character body area can be hurt")
+Profiles=(Name="Terrain",CollisionEnabled=QueryAndPhysics,bCanModify=True,ObjectTypeName="Terrain",CustomResponses=((Channel="WorldDynamic",Response=ECR_Ignore),(Channel="Pawn",Response=ECR_Ignore),(Channel="PhysicsBody",Response=ECR_Ignore),(Channel="Foliage",Response=ECR_Ignore),(Channel="AttackHitBox",Response=ECR_Ignore),(Channel="CharacterHitBox",Response=ECR_Ignore),(Channel="AwCharacter"),(Channel="TerrainTrace")),HelpMessage="All map collisions")
+Profiles=(Name="AOE",CollisionEnabled=QueryOnly,bCanModify=True,ObjectTypeName="AOE",CustomResponses=((Channel="WorldStatic",Response=ECR_Ignore),(Channel="WorldDynamic",Response=ECR_Overlap),(Channel="Pawn",Response=ECR_Overlap),(Channel="Visibility",Response=ECR_Overlap),(Channel="Camera",Response=ECR_Ignore),(Channel="PhysicsBody",Response=ECR_Overlap),(Channel="Vehicle",Response=ECR_Overlap),(Channel="Destructible",Response=ECR_Overlap),(Channel="Foliage",Response=ECR_Overlap),(Channel="Terrain",Response=ECR_Overlap),(Channel="AwCharacter",Response=ECR_Ignore)),HelpMessage="Needs description")
+Profiles=(Name="Bullet",CollisionEnabled=QueryOnly,bCanModify=True,ObjectTypeName="Bullet",CustomResponses=((Channel="WorldDynamic",Response=ECR_Overlap),(Channel="Pawn",Response=ECR_Overlap),(Channel="Visibility",Response=ECR_Overlap),(Channel="Camera",Response=ECR_Ignore),(Channel="PhysicsBody",Response=ECR_Overlap),(Channel="Vehicle",Response=ECR_Overlap),(Channel="Destructible",Response=ECR_Overlap),(Channel="Foliage",Response=ECR_Overlap),(Channel="Terrain",Response=ECR_Overlap),(Channel="AwCharacter",Response=ECR_Ignore)),HelpMessage="Needs description")
+Profiles=(Name="InteractCatcher",CollisionEnabled=QueryOnly,bCanModify=True,ObjectTypeName="InteractCatcher",CustomResponses=((Channel="WorldStatic",Response=ECR_Ignore),(Channel="WorldDynamic",Response=ECR_Ignore),(Channel="Pawn",Response=ECR_Ignore),(Channel="Visibility",Response=ECR_Ignore),(Channel="Camera",Response=ECR_Ignore),(Channel="PhysicsBody",Response=ECR_Ignore),(Channel="Vehicle",Response=ECR_Ignore),(Channel="Destructible",Response=ECR_Ignore),(Channel="Foliage",Response=ECR_Ignore),(Channel="AttackHitBox",Response=ECR_Ignore),(Channel="CharacterHitBox",Response=ECR_Ignore),(Channel="Terrain",Response=ECR_Ignore),(Channel="AOE",Response=ECR_Ignore),(Channel="Bullet",Response=ECR_Ignore),(Channel="InteractCatcher",Response=ECR_Ignore),(Channel="AwCharacter",Response=ECR_Ignore)),HelpMessage="Interact catching targets")
+Profiles=(Name="InteractActor",CollisionEnabled=QueryOnly,bCanModify=True,ObjectTypeName="InteractActor",CustomResponses=((Channel="WorldStatic",Response=ECR_Ignore),(Channel="WorldDynamic",Response=ECR_Ignore),(Channel="Pawn",Response=ECR_Ignore),(Channel="Visibility",Response=ECR_Ignore),(Channel="Camera",Response=ECR_Ignore),(Channel="PhysicsBody",Response=ECR_Ignore),(Channel="Vehicle",Response=ECR_Ignore),(Channel="Destructible",Response=ECR_Ignore),(Channel="Foliage",Response=ECR_Ignore),(Channel="AttackHitBox",Response=ECR_Ignore),(Channel="CharacterHitBox",Response=ECR_Ignore),(Channel="Terrain",Response=ECR_Ignore),(Channel="AOE",Response=ECR_Ignore),(Channel="Bullet",Response=ECR_Ignore),(Channel="InteractActor",Response=ECR_Ignore),(Channel="AwCharacter",Response=ECR_Ignore)),HelpMessage="Interact been caught")
+Profiles=(Name="DragActorMove",CollisionEnabled=QueryOnly,bCanModify=True,ObjectTypeName="DragActorMove",CustomResponses=((Channel="WorldStatic",Response=ECR_Ignore),(Channel="WorldDynamic",Response=ECR_Ignore),(Channel="Pawn",Response=ECR_Ignore),(Channel="Visibility",Response=ECR_Ignore),(Channel="Camera",Response=ECR_Ignore),(Channel="PhysicsBody",Response=ECR_Ignore),(Channel="Vehicle",Response=ECR_Ignore),(Channel="Destructible",Response=ECR_Ignore),(Channel="Foliage",Response=ECR_Ignore),(Channel="AttackHitBox",Response=ECR_Ignore),(Channel="CharacterHitBox",Response=ECR_Ignore),(Channel="Terrain",Response=ECR_Ignore),(Channel="AOE",Response=ECR_Ignore),(Channel="Bullet",Response=ECR_Ignore),(Channel="InteractCatcher",Response=ECR_Ignore),(Channel="InteractActor",Response=ECR_Ignore),(Channel="AwCharacter",Response=ECR_Ignore),(Channel="DragActorMove",Response=ECR_Ignore)),HelpMessage="Can Drag Actor Move")
+Profiles=(Name="ActorCanBeDraggedMove",CollisionEnabled=QueryOnly,bCanModify=True,ObjectTypeName="ActorCanBeDraggedMove",CustomResponses=((Channel="WorldStatic",Response=ECR_Ignore),(Channel="WorldDynamic",Response=ECR_Ignore),(Channel="Pawn",Response=ECR_Ignore),(Channel="Visibility",Response=ECR_Ignore),(Channel="Camera",Response=ECR_Ignore),(Channel="PhysicsBody",Response=ECR_Ignore),(Channel="Vehicle",Response=ECR_Ignore),(Channel="Destructible",Response=ECR_Ignore),(Channel="Foliage",Response=ECR_Ignore),(Channel="AttackHitBox",Response=ECR_Ignore),(Channel="CharacterHitBox",Response=ECR_Ignore),(Channel="Terrain",Response=ECR_Ignore),(Channel="AOE",Response=ECR_Ignore),(Channel="Bullet",Response=ECR_Ignore),(Channel="InteractCatcher",Response=ECR_Ignore),(Channel="InteractActor",Response=ECR_Ignore),(Channel="AwCharacter",Response=ECR_Ignore),(Channel="ActorCanBeDraggedMove",Response=ECR_Ignore)),HelpMessage="Actor Can Be Dragged By DragActorMove")
+Profiles=(Name="Squeeze",CollisionEnabled=QueryOnly,bCanModify=True,ObjectTypeName="Squeeze",CustomResponses=((Channel="WorldStatic",Response=ECR_Ignore),(Channel="WorldDynamic",Response=ECR_Ignore),(Channel="Pawn",Response=ECR_Ignore),(Channel="Visibility",Response=ECR_Ignore),(Channel="Camera",Response=ECR_Ignore),(Channel="PhysicsBody",Response=ECR_Ignore),(Channel="Vehicle",Response=ECR_Ignore),(Channel="Destructible",Response=ECR_Ignore),(Channel="Foliage",Response=ECR_Ignore),(Channel="AttackHitBox",Response=ECR_Ignore),(Channel="CharacterHitBox",Response=ECR_Ignore),(Channel="Terrain",Response=ECR_Ignore),(Channel="AOE",Response=ECR_Ignore),(Channel="Bullet",Response=ECR_Ignore),(Channel="InteractCatcher",Response=ECR_Ignore),(Channel="InteractActor",Response=ECR_Ignore),(Channel="AwCharacter",Response=ECR_Ignore),(Channel="ActorCanBeDraggedMove",Response=ECR_Ignore),(Channel="DragActorMove",Response=ECR_Ignore),(Channel="Squeeze",Response=ECR_Overlap),(Channel="SqueezeTrace",Response=ECR_Overlap),(Channel="BothCatherAndBeCaught",Response=ECR_Ignore)),HelpMessage="Squeeze")
+Profiles=(Name="BothCatherAndBeCaught",CollisionEnabled=QueryOnly,bCanModify=True,ObjectTypeName="BothCatherAndBeCaught",CustomResponses=((Channel="WorldStatic",Response=ECR_Ignore),(Channel="WorldDynamic",Response=ECR_Ignore),(Channel="Pawn",Response=ECR_Ignore),(Channel="Visibility",Response=ECR_Ignore),(Channel="Camera",Response=ECR_Ignore),(Channel="PhysicsBody",Response=ECR_Ignore),(Channel="Vehicle",Response=ECR_Ignore),(Channel="Destructible",Response=ECR_Ignore),(Channel="Foliage",Response=ECR_Ignore),(Channel="Terrain",Response=ECR_Ignore),(Channel="Bullet",Response=ECR_Overlap),(Channel="InteractCatcher",Response=ECR_Ignore),(Channel="InteractActor",Response=ECR_Ignore),(Channel="AwCharacter",Response=ECR_Ignore),(Channel="ActorCanBeDraggedMove",Response=ECR_Ignore),(Channel="DragActorMove",Response=ECR_Ignore)),HelpMessage="Needs description")
+Profiles=(Name="TowerAthenaHitBox",CollisionEnabled=QueryOnly,bCanModify=True,ObjectTypeName="CharacterHitBox",CustomResponses=((Channel="WorldStatic",Response=ECR_Ignore),(Channel="WorldDynamic",Response=ECR_Overlap),(Channel="Pawn",Response=ECR_Ignore),(Channel="Visibility",Response=ECR_Ignore),(Channel="Camera",Response=ECR_Ignore),(Channel="PhysicsBody",Response=ECR_Ignore),(Channel="Vehicle",Response=ECR_Ignore),(Channel="Destructible",Response=ECR_Ignore),(Channel="Foliage",Response=ECR_Ignore),(Channel="CharacterHitBox",Response=ECR_Ignore),(Channel="Bullet",Response=ECR_Overlap),(Channel="AwCharacter",Response=ECR_Ignore)),HelpMessage="Special navigation set,for walk in middle of road.")
+Profiles=(Name="AthenaDetectEnemy",CollisionEnabled=QueryOnly,bCanModify=True,ObjectTypeName="AwCharacter",CustomResponses=((Channel="WorldStatic",Response=ECR_Ignore),(Channel="WorldDynamic",Response=ECR_Ignore),(Channel="Pawn",Response=ECR_Ignore),(Channel="Visibility",Response=ECR_Ignore),(Channel="Camera",Response=ECR_Ignore),(Channel="PhysicsBody",Response=ECR_Ignore),(Channel="Vehicle",Response=ECR_Ignore),(Channel="Destructible",Response=ECR_Ignore),(Channel="Foliage",Response=ECR_Ignore),(Channel="AttackHitBox",Response=ECR_Ignore),(Channel="CharacterHitBox",Response=ECR_Ignore),(Channel="Terrain",Response=ECR_Ignore),(Channel="AOE",Response=ECR_Ignore),(Channel="Bullet",Response=ECR_Ignore),(Channel="InteractCatcher",Response=ECR_Ignore),(Channel="InteractActor",Response=ECR_Ignore),(Channel="ActorCanBeDraggedMove",Response=ECR_Ignore),(Channel="DragActorMove",Response=ECR_Ignore),(Channel="BothCatherAndBeCaught",Response=ECR_Ignore)),HelpMessage="Needs description")
+Profiles=(Name="DropLoot",CollisionEnabled=QueryAndPhysics,bCanModify=True,ObjectTypeName="InteractActor",CustomResponses=((Channel="WorldDynamic",Response=ECR_Ignore),(Channel="Pawn",Response=ECR_Ignore),(Channel="Visibility",Response=ECR_Ignore),(Channel="Camera",Response=ECR_Ignore),(Channel="PhysicsBody",Response=ECR_Ignore),(Channel="Vehicle",Response=ECR_Ignore),(Channel="Destructible",Response=ECR_Ignore),(Channel="Foliage",Response=ECR_Ignore),(Channel="AttackHitBox",Response=ECR_Ignore),(Channel="CharacterHitBox",Response=ECR_Ignore),(Channel="Terrain",Response=ECR_Ignore),(Channel="AOE",Response=ECR_Ignore),(Channel="Bullet",Response=ECR_Ignore),(Channel="InteractActor",Response=ECR_Ignore),(Channel="AwCharacter",Response=ECR_Ignore)),HelpMessage="掉落道具")
+Profiles=(Name="Occupation",CollisionEnabled=QueryOnly,bCanModify=True,ObjectTypeName="InteractActor",CustomResponses=((Channel="WorldStatic",Response=ECR_Ignore),(Channel="WorldDynamic",Response=ECR_Ignore),(Channel="Pawn",Response=ECR_Ignore),(Channel="Visibility",Response=ECR_Ignore),(Channel="Camera",Response=ECR_Ignore),(Channel="PhysicsBody",Response=ECR_Ignore),(Channel="Vehicle",Response=ECR_Ignore),(Channel="Destructible",Response=ECR_Ignore),(Channel="Foliage",Response=ECR_Ignore),(Channel="AttackHitBox",Response=ECR_Ignore),(Channel="CharacterHitBox",Response=ECR_Ignore),(Channel="Terrain",Response=ECR_Ignore),(Channel="AOE",Response=ECR_Ignore),(Channel="Bullet",Response=ECR_Ignore),(Channel="InteractActor",Response=ECR_Ignore),(Channel="AwCharacter",Response=ECR_Ignore),(Channel="ActorCanBeDraggedMove",Response=ECR_Ignore),(Channel="DragActorMove",Response=ECR_Ignore),(Channel="BothCatherAndBeCaught",Response=ECR_Ignore)),HelpMessage="Needs description")
+DefaultChannelResponses=(Channel=ECC_GameTraceChannel1,DefaultResponse=ECR_Block,bTraceType=False,bStaticObject=False,Name="Foliage")
+DefaultChannelResponses=(Channel=ECC_GameTraceChannel2,DefaultResponse=ECR_Overlap,bTraceType=False,bStaticObject=False,Name="AttackHitBox")
+DefaultChannelResponses=(Channel=ECC_GameTraceChannel3,DefaultResponse=ECR_Overlap,bTraceType=False,bStaticObject=False,Name="CharacterHitBox")
+DefaultChannelResponses=(Channel=ECC_GameTraceChannel4,DefaultResponse=ECR_Block,bTraceType=False,bStaticObject=False,Name="Terrain")
+DefaultChannelResponses=(Channel=ECC_GameTraceChannel5,DefaultResponse=ECR_Overlap,bTraceType=False,bStaticObject=False,Name="AOE")
+DefaultChannelResponses=(Channel=ECC_GameTraceChannel6,DefaultResponse=ECR_Block,bTraceType=False,bStaticObject=False,Name="Bullet")
+DefaultChannelResponses=(Channel=ECC_GameTraceChannel7,DefaultResponse=ECR_Overlap,bTraceType=False,bStaticObject=False,Name="InteractCatcher")
+DefaultChannelResponses=(Channel=ECC_GameTraceChannel8,DefaultResponse=ECR_Overlap,bTraceType=False,bStaticObject=False,Name="InteractActor")
+DefaultChannelResponses=(Channel=ECC_GameTraceChannel9,DefaultResponse=ECR_Overlap,bTraceType=False,bStaticObject=False,Name="AwCharacter")
+DefaultChannelResponses=(Channel=ECC_GameTraceChannel10,DefaultResponse=ECR_Overlap,bTraceType=False,bStaticObject=False,Name="ActorCanBeDraggedMove")
+DefaultChannelResponses=(Channel=ECC_GameTraceChannel11,DefaultResponse=ECR_Overlap,bTraceType=False,bStaticObject=False,Name="DragActorMove")
+DefaultChannelResponses=(Channel=ECC_GameTraceChannel12,DefaultResponse=ECR_Ignore,bTraceType=False,bStaticObject=False,Name="Squeeze")
+DefaultChannelResponses=(Channel=ECC_GameTraceChannel13,DefaultResponse=ECR_Ignore,bTraceType=True,bStaticObject=False,Name="SqueezeTrace")
+DefaultChannelResponses=(Channel=ECC_GameTraceChannel14,DefaultResponse=ECR_Ignore,bTraceType=True,bStaticObject=False,Name="TerrainTrace")
+DefaultChannelResponses=(Channel=ECC_GameTraceChannel15,DefaultResponse=ECR_Ignore,bTraceType=True,bStaticObject=False,Name="AttackComplexTrace")
+DefaultChannelResponses=(Channel=ECC_GameTraceChannel16,DefaultResponse=ECR_Overlap,bTraceType=False,bStaticObject=False,Name="BothCatherAndBeCaught")
+EditProfiles=(Name="BlockAllDynamic",CustomResponses=((Channel="TerrainTrace")))
+EditProfiles=(Name="BlockAll",CustomResponses=((Channel="TerrainTrace",Response=ECR_Ignore),(Channel="EngineTraceChannel1"),(Channel="AttackHitBox"),(Channel="CharacterHitBox"),(Channel="AOE"),(Channel="InteractCatcher"),(Channel="InteractActor"),(Channel="AwCharacter"),(Channel="ActorCanBeDraggedMove"),(Channel="DragActorMove"),(Channel="Squeeze",Response=ECR_Ignore),(Channel="SqueezeTrace",Response=ECR_Ignore)))
+EditProfiles=(Name="NoCollision",CustomResponses=((Channel="WorldStatic",Response=ECR_Ignore),(Channel="WorldDynamic",Response=ECR_Ignore),(Channel="Pawn",Response=ECR_Ignore),(Channel="PhysicsBody",Response=ECR_Ignore),(Channel="Vehicle",Response=ECR_Ignore),(Channel="Destructible",Response=ECR_Ignore),(Channel="EngineTraceChannel2",Response=ECR_Ignore),(Channel="EngineTraceChannel3",Response=ECR_Ignore),(Channel="EngineTraceChannel4",Response=ECR_Ignore),(Channel="EngineTraceChannel5",Response=ECR_Ignore),(Channel="EngineTraceChannel6",Response=ECR_Ignore),(Channel="Foliage",Response=ECR_Ignore),(Channel="AttackHitBox",Response=ECR_Ignore),(Channel="CharacterHitBox",Response=ECR_Ignore),(Channel="Terrain",Response=ECR_Ignore),(Channel="AOE",Response=ECR_Ignore),(Channel="Bullet",Response=ECR_Ignore),(Channel="InteractCatcher",Response=ECR_Ignore),(Channel="InteractActor",Response=ECR_Ignore),(Channel="AwCharacter",Response=ECR_Ignore),(Channel="ActorCanBeDraggedMove",Response=ECR_Ignore),(Channel="DragActorMove",Response=ECR_Ignore),(Channel="GameTraceChannel15",Response=ECR_Ignore),(Channel="GameTraceChannel16",Response=ECR_Ignore),(Channel="GameTraceChannel17",Response=ECR_Ignore),(Channel="GameTraceChannel18",Response=ECR_Ignore)))
+EditProfiles=(Name="OverlapAll",CustomResponses=((Channel="EngineTraceChannel1",Response=ECR_Overlap),(Channel="EngineTraceChannel2",Response=ECR_Overlap),(Channel="EngineTraceChannel3",Response=ECR_Overlap),(Channel="EngineTraceChannel4",Response=ECR_Overlap),(Channel="EngineTraceChannel5",Response=ECR_Overlap),(Channel="EngineTraceChannel6",Response=ECR_Overlap),(Channel="Foliage",Response=ECR_Overlap),(Channel="Terrain",Response=ECR_Overlap),(Channel="Bullet",Response=ECR_Overlap),(Channel="Squeeze",Response=ECR_Overlap),(Channel="SqueezeTrace",Response=ECR_Overlap),(Channel="TerrainTrace",Response=ECR_Overlap),(Channel="GameTraceChannel15",Response=ECR_Overlap),(Channel="GameTraceChannel16",Response=ECR_Overlap),(Channel="GameTraceChannel17",Response=ECR_Overlap),(Channel="GameTraceChannel18",Response=ECR_Overlap),(Channel="AttackComplexTrace",Response=ECR_Overlap)))
-ProfileRedirects=(OldName="BlockingVolume",NewName="InvisibleWall")
-ProfileRedirects=(OldName="InterpActor",NewName="IgnoreOnlyPawn")
-ProfileRedirects=(OldName="StaticMeshComponent",NewName="BlockAllDynamic")
-ProfileRedirects=(OldName="SkeletalMeshActor",NewName="PhysicsActor")
-ProfileRedirects=(OldName="InvisibleActor",NewName="InvisibleWallDynamic")
+ProfileRedirects=(OldName="BlockingVolume",NewName="InvisibleWall")
+ProfileRedirects=(OldName="InterpActor",NewName="IgnoreOnlyPawn")
+ProfileRedirects=(OldName="StaticMeshComponent",NewName="BlockAllDynamic")
+ProfileRedirects=(OldName="SkeletalMeshActor",NewName="PhysicsActor")
+ProfileRedirects=(OldName="InvisibleActor",NewName="InvisibleWallDynamic")
+ProfileRedirects=(OldName="Squeeze",NewName="SqueezeThing")
-CollisionChannelRedirects=(OldName="Static",NewName="WorldStatic")
-CollisionChannelRedirects=(OldName="Dynamic",NewName="WorldDynamic")
-CollisionChannelRedirects=(OldName="VehicleMovement",NewName="Vehicle")
-CollisionChannelRedirects=(OldName="PawnMovement",NewName="Pawn")
+CollisionChannelRedirects=(OldName="Static",NewName="WorldStatic")
+CollisionChannelRedirects=(OldName="Dynamic",NewName="WorldDynamic")
+CollisionChannelRedirects=(OldName="VehicleMovement",NewName="Vehicle")
+CollisionChannelRedirects=(OldName="PawnMovement",NewName="Pawn")

[/Script/NavigationSystem.RecastNavMesh]
CellSize=10.000000
CellHeight=15.000000
AgentRadius=50.000000
RuntimeGeneration=Dynamic

[/Script/NavigationSystem.NavigationSystemV1]
DefaultAgentName=None
CrowdManagerClass=/Script/AIModule.CrowdManager
bAutoCreateNavigationData=True
bSpawnNavDataInNavBoundsLevel=False
bAllowClientSideNavigation=False
bShouldDiscardSubLevelNavData=True
bTickWhilePaused=False
bInitialBuildingLocked=False
bSkipAgentHeightCheckWhenPickingNavData=False
GeometryExportVertexCountWarningThreshold=1000000
bGenerateNavigationOnlyAroundNavigationInvokers=False
ActiveTilesUpdateInterval=1.000000
DataGatheringMode=Instant
DirtyAreaWarningSizeThreshold=-1.000000
GatheringNavModifiersWarningLimitTime=-1.000000
+SupportedAgents=(Name="Default",Color=(B=0,G=255,R=140,A=164),DefaultQueryExtent=(X=50.000000,Y=50.000000,Z=5000.000000),NavDataClass="/Script/NavigationSystem.RecastNavMesh",AgentRadius=100.000000,AgentHeight=144.000000,AgentStepHeight=35.000000,NavWalkingSearchHeightScale=0.500000,PreferredNavData="/Script/NavigationSystem.RecastNavMesh",bCanCrouch=False,bCanJump=False,bCanWalk=False,bCanSwim=False,bCanFly=False)
SupportedAgentsMask=(bSupportsAgent0=True,bSupportsAgent1=True,bSupportsAgent2=True,bSupportsAgent3=True,bSupportsAgent4=True,bSupportsAgent5=True,bSupportsAgent6=True,bSupportsAgent7=True,bSupportsAgent8=True,bSupportsAgent9=True,bSupportsAgent10=True,bSupportsAgent11=True,bSupportsAgent12=True,bSupportsAgent13=True,bSupportsAgent14=True,bSupportsAgent15=True)

[/Script/Engine.RendererSettings]
r.Shadow.RectLightDepthBias=0.15
r.VirtualTextures=True
r.VirtualTexturedLightmaps=False
r.GenerateMeshDistanceFields=True
r.DefaultFeature.AutoExposure=False
r.DefaultFeature.AutoExposure.Method=0
r.DefaultFeature.MotionBlur=False
r.PrecomputedVisibilityWarning=False
r.AllowStaticLighting=False
r.SSGI.Enable=True
r.vt.FeedbackFactor=4
r.DefaultFeature.AutoExposure.Bias=0.000000
r.CustomDepth=3
r.Streaming.PoolSize=3000
r.Shadow.PointLightDepthBias=0.15
r.ReflectionMethod=1
r.DynamicGlobalIlluminationMethod=1
r.SkinCache.CompileShaders=True
r.RayTracing=False
r.Lumen.HardwareRayTracing=False
r.RayTracing.Shadows=False
r.RayTracing.Skylight=False
r.DefaultFeature.Bloom=True
r.VT.AnisotropicFiltering=False
r.VT.TileBorderSize=8
r.VT.EnableAutoImport=False
r.Shadow.Virtual.Enable=1
r.Lumen.TranslucencyReflections.FrontLayer.EnableForProject=False
r.DefaultFeature.AutoExposure.ExtendDefaultLuminanceRange=True
r.Lumen.TraceMeshSDFs=0
r.Lumen.DiffuseIndirect.SSAO=1
r.AntiAliasingMethod=2

[CoreRedirects]
+ClassRedirects=(OldName="/Awaker",NewName="/TheAwakener_FO")
+ClassRedirects=(OldName="/Script/ProjectAwaker.MontageNatureLoop",NewName="/Script/TheAwakener_FO.MontageSpecEvent")
+ClassRedirects=(OldName="/Script/ProjectAwaker.HitGroundTerminator",NewName="/Script/TheAwakener_FO.ActionFallingCheck")
+ClassRedirects=(OldName="/Script/ProjectAwaker.AnimNotifyState_AttackHitBox",NewName="/Script/TheAwakener_FO.ActiveHitBox")
+ClassRedirects=(OldName="/Script/ProjectAwaker.BulletLauncher",NewName="/Script/TheAwakener_FO.BulletLauncher")
+ClassRedirects=(OldName="/Script/ProjectAwaker.UIThingIcon",NewName="/Script/TheAwakener_FO.ThingIcon")
+ClassRedirects=(OldName="/Script/ProjectAwaker.AwTankMoveComponent",NewName="/Script/TheAwakener_FO.TrackMoveComponent")
+ClassRedirects=(OldName="/Script/ProjectAwaker.SqueezeWeigthModifer",NewName="/Script/TheAwakener_FO.TrackSqueezeWeigthModifer")
+ClassRedirects=(OldName="/Script/ProjectAwaker.ResourceFunctionLibrary",NewName="/Script/TheAwakener_FO.ResourceFuncLib")
+ClassRedirects=(OldName="/Script/ProjectAwaker.CallFunctionLibrary",NewName="/Script/TheAwakener_FO.CallFuncLib")
+ClassRedirects=(OldName="/Script/ProjectAwaker.DataFunctionLibrary",NewName="/Script/TheAwakener_FO.DataFuncLib")
+ClassRedirects=(OldName="/Script/ProjectAwaker.DateTimeFunctionLibrary",NewName="/Script/TheAwakener_FO.DateTimeFuncLib")
+ClassRedirects=(OldName="/Script/ProjectAwaker.FileFunctionLibrary",NewName="/Script/TheAwakener_FO.FileFuncLib")
+ClassRedirects=(OldName="/Script/ProjectAwaker.GameFunctionLibrary",NewName="/Script/TheAwakener_FO.GameFuncLib")
+ClassRedirects=(OldName="/Script/ProjectAwaker.GameplayFunctionLibrary",NewName="/Script/TheAwakener_FO.GameplayFuncLib")
+ClassRedirects=(OldName="/Script/ProjectAwaker.MathFunctionLibrary",NewName="/Script/TheAwakener_FO.MathFuncLib")
+ClassRedirects=(OldName="/Script/ProjectAwaker.CameraSpringArmSetting",NewName="/Script/TheAwakener_FO.CameraOffsetModify")
+ClassRedirects=(OldName="/Script/ProjectAwaker.BeDamagedComponent",NewName="/Script/TheAwakener_FO.DamageTakenComponent")
+ClassRedirects=(OldName="/Script/ProjectAwaker.BeDamagedManager",NewName="/Script/TheAwakener_FO.BeOffended")
+ClassRedirects=(OldName="/Script/ProjectAwaker.CapsuleHitBox",NewName="/Script/TheAwakener_FO.CapsuleBeHitBox")
+ClassRedirects=(OldName="/Script/ProjectAwaker.UBeCaughtHitBox",NewName="/Script/TheAwakener_FO.BeCaughtHitBox")
+ClassRedirects=(OldName="/Script/ProjectAwaker.UCharacterHitBoxData",NewName="/Script/TheAwakener_FO.CharacterHitBoxData")
+ClassRedirects=(OldName="/Script/ProjectAwaker.AITargettingScript",NewName="/Script/TheAwakener_FO.AIPickTargetScript")
+ClassRedirects=(OldName="/Script/ProjectAwaker.MapPathNode",NewName="/Script/TheAwakener_FO.MapScenePosition")
+EnumRedirects=OldName="/Awaker",NewName="/TheAwakener_FO")
+FunctionRedirects=(OldName="/Awaker",NewName="/TheAwakener_FO")
+StructRedirects=(OldName="/Awaker",NewName="/TheAwakener_FO")
+ClassRedirects=(OldName="/Script/ProjectAwaker.AAwCharacterNew",NewName="/Script/TheAwakener_FO.AwCharacterNew")
+StructRedirects=(OldName="/Script/ProjectAwaker.BattleRecoid",NewName="/Script/TheAwakener_FO.BattleRecord")
+StructRedirects=(OldName="/Script/ProjectAwaker.MyStruct",NewName="/Script/TheAwakener_FO.RogueDataInfo")
+ClassRedirects=(OldName="/Script/ProjectAwaker.DynamicText",NewName="/Script/TheAwakener_FO.DynamicNumber")
+ClassRedirects=(OldName="/Script/ProjectAwaker.DynamicNumText",NewName="/Script/TheAwakener_FO.DynamicNumber")
+StructRedirects=(OldName="/Script/ProjectAwaker.RogueBattleStyleSkill",NewName="/Script/TheAwakener_FO.RogueBattleStyleAbility")
+EnumRedirects=(OldName="/Script/ProjectAwaker.ERogueSkillType",NewName="/Script/TheAwakener_FO.ERogueAbilitySlot")
+EnumRedirects=(OldName="/Script/ProjectAwaker.ERogueAbilityTag",NewName="/Script/TheAwakener_FO.ERogueBattleTag")
+ClassRedirects=(OldName="/Script/ProjectAwaker.Rogue_RewardMain",NewName="/Script/TheAwakener_FO.Rogue_RewardMain")
+StructRedirects=(OldName="/Script/ProjectAwaker.RogueCardInfo_BattleStyle",NewName="/Script/TheAwakener_FO.RogueCardInfo_BattleStyleUpgrade")
+StructRedirects=(OldName="/Script/ProjectAwaker.RogueCardInfo_BattleStyleUpgarde",NewName="/Script/TheAwakener_FO.RogueCardInfo_BattleStyleUpgrade")
+StructRedirects=(OldName="/Script/ProjectAwaker.BattleUpgrade",NewName="/Script/TheAwakener_FO.RogueBattleUpgrade")
+StructRedirects=(OldName="/Script/ProjectAwaker.BattleAbility",NewName="/Script/TheAwakener_FO.BattleAbilityIds")
+EnumRedirects=(OldName="/Script/ProjectAwaker.ERogueAbilityType",NewName="/Script/TheAwakener_FO.ERogueAbilitySlot")
+EnumRedirects=(OldName="/Script/ProjectAwaker.ERogueAbilityAddType",NewName="/Script/TheAwakener_FO.ERogueAbilityType")
+PropertyRedirects=(OldName="/Script/TheAwakener_FO.RogueClassRecord.ChallengeClearLevel",NewName="/Script/TheAwakener_FO.RogueClassRecord.DifficultyClearLevel")
+FunctionRedirects=(OldName="/Script/TheAwakener_FO.SurvivorEventFunc.ReachTowerPoint",NewName="/Script/TheAwakener_FO.SurvivorEventFunc.CheckTowerReachPoint")
+FunctionRedirects=(OldName="/Script/TheAwakener_FO.SurvivorEventFunc.CheckTowerNotReachPoint",NewName="/Script/TheAwakener_FO.SurvivorEventFunc.CheckTowerNotAtPoint")
+FunctionRedirects=(OldName="/Script/TheAwakener_FO.GameplayFuncLib.GetMyAwPlayerCharacter",NewName="/Script/TheAwakener_FO.GameplayFuncLib.GetLocalAwPlayerCharacter")
+FunctionRedirects=(OldName="/Script/TheAwakener_FO.GameplayFuncLib.GetMyAwPlayerController",NewName="/Script/TheAwakener_FO.GameplayFuncLib.GetLocalAwPlayerController")
+FunctionRedirects=(OldName="/Script/TheAwakener_FO.GameplayFuncLib.GetPlayerController",NewName="/Script/TheAwakener_FO.GameplayFuncLib.GetPlayerControllerByComp")
+FunctionRedirects=(OldName="/Script/TheAwakener_FO.GameplayFuncLib.GetAllMyAwPlayerControllers",NewName="/Script/TheAwakener_FO.GameplayFuncLib.GetAllLocalAwPlayerControllers")
+FunctionRedirects=(OldName="/Script/TheAwakener_FO.GameplayFuncLib.GetMyAwPlayerState",NewName="/Script/TheAwakener_FO.GameplayFuncLib.GetLocalAwPlayerState")
+FunctionRedirects=(OldName="/Script/TheAwakener_FO.AwPlayerController.GetLocalPlayerControllerIndex",NewName="/Script/TheAwakener_FO.AwPlayerController.GetLocalPCIndex")
+FunctionRedirects=(OldName="/Script/TheAwakener_FO.AwRogueTalentSubSystem.ClearTalent",NewName="/Script/TheAwakener_FO.AwRogueTalentSubSystem.ClearALlPlayerTalent")
+PropertyRedirects=(OldName="/Script/TheAwakener_FO.AwGameState.MyCharacter",NewName="/Script/TheAwakener_FO.AwGameState.GetMyCharacter()")
+FunctionRedirects=(OldName="/Script/TheAwakener_FO.AwGameInstance.GetWorkingPlayerControllerIndex",NewName="/Script/TheAwakener_FO.AwGameInstance.GetWorkingLocalPlayerControllerIndex")
+FunctionRedirects=(OldName="/Script/TheAwakener_FO.AwCharacter.SetupAfterPossess",NewName="/Script/TheAwakener_FO.AwCharacter.SetupCharacter")
+FunctionRedirects=(OldName="/Script/TheAwakener_FO.GameplayFuncLib.CameraFadeOut",NewName="/Script/TheAwakener_FO.GameplayFuncLib.UnifiedCameraFadeOut")
+FunctionRedirects=(OldName="/Script/TheAwakener_FO.GameplayFuncLib.CameraFadeIn",NewName="/Script/TheAwakener_FO.GameplayFuncLib.UnifiedCameraFadeIn")
+FunctionRedirects=(OldName="/Script/TheAwakener_FO.AwPlayerController.AttachWidgetToMonsterBone",NewName="/Script/TheAwakener_FO.AwPlayerController.AttachLockSignToMonster")
+FunctionRedirects=(OldName="/Script/TheAwakener_FO.AwAIComponent.GetClosestDistanceEnemy_Svl",NewName="/Script/TheAwakener_FO.AwAIComponent.GetClosestDistancePlayer_Svl")
+FunctionRedirects=(OldName="/Script/TheAwakener_FO.MobAIScript.CheckEnemyInRangeSvl",NewName="/Script/TheAwakener_FO.MobAIScript.CheckPlayerInRangeSvl")
+PropertyRedirects=(OldName="/Script/TheAwakener_FO.TrAnimationState.Time",NewName="/Script/TheAwakener_FO.TrAnimationState.StartTime")
+StructRedirects=(OldName="/Script/TheAwakener_FO.AC_TouchGround",NewName="/Script/TheAwakener_FO.AC_Landed")
+StructRedirects=(OldName="/Script/TheAwakener_FO.AS_AirLoop",NewName="/Script/TheAwakener_FO.AS_InAir")
+StructRedirects=(OldName="/Script/TheAwakener_FO.AS_AirLoop_Hitted",NewName="/Script/TheAwakener_FO.AS_InAir_Hitted")
+PropertyRedirects=(OldName="/Script/TheAwakener_FO.MechanicRunner.isSpawned",NewName="/Script/TheAwakener_FO.MechanicRunner.IsDirt")
+FunctionRedirects=(OldName="/Script/TheAwakener_FO.MonsterReplaceMgr.CheckMonsterToActive",NewName="/Script/TheAwakener_FO.MonsterReplaceMgr.CheckMonsterToActive_Old")
+FunctionRedirects=(OldName="/Script/TheAwakener_FO.WaveManager.IsMonsterSpawnable",NewName="/Script/TheAwakener_FO.WaveManager.GetMonsterSpawnAvailability")
+FunctionRedirects=(OldName="/Script/TheAwakener_FO.DialogCondition.CheckDLCPurchased",NewName="/Script/TheAwakener_FO.DialogCondition.IfDLCPurchased")
+StructRedirects=(OldName="/Script/TheAwakener_FO.AoeConfig",NewName="/Script/TheAwakener_FO.ElemAbilityConfig")
+FunctionRedirects=(OldName="/Script/TheAwakener_FO.ItemInstanceSubsystem.GetEquiptedItems",NewName="/Script/TheAwakener_FO.ItemInstanceSubsystem.GetEquippedItems")

[/Script/Engine.UserInterfaceSettings]
RenderFocusRule=NavigationOnly
HardwareCursors=()
SoftwareCursors=((Default, "/Game/Core/UI/Roguelike/00-Formal/WBP_AwMouseCursor.WBP_AwMouseCursor_C"))
ApplicationScale=1.000000
UIScaleRule=Custom
CustomScalingRuleClass=/Script/TheAwakener_FO.AwDPICustomScalingRule
UIScaleCurve=(EditorCurveData=(Keys=((Time=480.000000,Value=0.187500),(Time=1280.000000,Value=0.500000),(Time=1920.000000,Value=0.750000,ArriveTangent=0.000391,LeaveTangent=0.000391),(Time=2560.000000,Value=1.000000,ArriveTangent=0.000937,LeaveTangent=0.000937),(Time=3840.000000,Value=1.500000),(Time=5120.000000,Value=2.000000),(Time=8640.000000,Value=6.000000)),DefaultValue=340282346638528859811704183484516925440.000000,PreInfinityExtrap=RCCE_Constant,PostInfinityExtrap=RCCE_Constant),ExternalCurve=None)
bAllowHighDPIInGameMode=False
DesignScreenSize=(X=2560,Y=1440)
bLoadWidgetsOnDedicatedServer=True

[/Script/LuminRuntimeSettings.LuminRuntimeSettings]
IconModelPath=(Path="")
IconPortalPath=(Path="")

[/Script/Engine.StreamingSettings]
s.AsyncLoadingThreadEnabled=True


[/Script/AndroidFileServerEditor.AndroidFileServerRuntimeSettings]
bEnablePlugin=True
bAllowNetworkConnection=True
SecurityToken=FD6FC7BA43973EAF2D4B9EB9779434E2
bIncludeInShipping=False
bAllowExternalStartInShipping=False
bCompileAFSProject=False
bUseCompression=False
bLogFiles=False
bReportStats=False
ConnectionType=USBOnly
bUseManualIPAddress=False
ManualIPAddress=

[/Script/WindowsTargetPlatform.WindowsTargetSettings]
DefaultGraphicsRHI=DefaultGraphicsRHI_DX12
-D3D12TargetedShaderFormats=PCD3D_SM5
+D3D12TargetedShaderFormats=PCD3D_SM5
+D3D12TargetedShaderFormats=PCD3D_SM6
-D3D11TargetedShaderFormats=PCD3D_SM5
+D3D11TargetedShaderFormats=PCD3D_SM5
Compiler=Default
AudioSampleRate=48000
AudioCallbackBufferFrameSize=1024
AudioNumBuffersToEnqueue=1
AudioMaxChannels=0
AudioNumSourceWorkers=4
SpatializationPlugin=
SourceDataOverridePlugin=
ReverbPlugin=
OcclusionPlugin=
CompressionOverrides=(bOverrideCompressionTimes=False,DurationThreshold=5.000000,MaxNumRandomBranches=0,SoundCueQualityIndex=0)
CacheSizeKB=65536
MaxChunkSizeOverrideKB=0
bResampleForDevice=False
MaxSampleRate=48000.000000
HighSampleRate=32000.000000
MedSampleRate=24000.000000
LowSampleRate=12000.000000
MinSampleRate=8000.000000
CompressionQualityModifier=1.000000
AutoStreamingThreshold=0.000000
SoundCueCookQualityIndex=-1

[/Script/Engine.AudioSettings]
DefaultSoundConcurrencyName=None

