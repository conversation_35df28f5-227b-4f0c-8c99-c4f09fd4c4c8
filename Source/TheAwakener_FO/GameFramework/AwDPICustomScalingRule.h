// The copyright of this project belongs to Shanghai Minghuan Software Technology Co., Ltd

#pragma once

#include "CoreMinimal.h"
#include "Engine/DPICustomScalingRule.h"
#include "AwDPICustomScalingRule.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UAwDPICustomScalingRule : public UDPICustomScalingRule
{
	GENERATED_BODY()

   virtual float GetDPIScaleBasedOnSize(FIntPoint Size) const override;
};
