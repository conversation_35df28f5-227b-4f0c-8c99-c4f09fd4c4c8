// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "AwSessionInfo.generated.h"

/**
 * 
 */

USTRUCT(BlueprintType)
struct FAwSessionInfo
{
	GENERATED_BODY()
public:
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
		FString ServerName;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
		FString HostPlayerName;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
		int CurPublicPlayerNum = 0;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
		int MaxPublicPlayerNum = 0;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
		int CurPrivatePlayerNum = 0;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
		int MaxPrivatePlayerNum = 0;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
		bool bUseLAN = false;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
		FString Password;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
		FString Tag;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
		FString DungeonID;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
		FString Remark;
};
