// Fill out your copyright notice in the Description page of Project Settings.


#include "AwPlayerController.h"

#include "EngineUtils.h"
#include "Components/SkeletalMeshComponent.h"
#include "Framework/Application/NavigationConfig.h"
#include "GameFramework/PlayerInput.h"
#include "Kismet/KismetMathLibrary.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "Net/UnrealNetwork.h"
#include "TheAwakener_FO/FunctionLibrary/DateTimeFuncLib.h"
#include "TheAwakener_FO/UI/HUD/PopText.h"
#include "TheAwakener_FO/GamePlay/GameMode/AwGameMode_RandomDungeon.h"
#include "TheAwakener_FO/GamePlay/Move/UICloneLocation.h"
void AAwPlayerController::CreateWidgetComponent()  
{  
 LockSignWidget = this->CreateDefaultSubobject<UWidgetComponent>(TEXT("LockSign"));  
 if (LockSignWidget)  
 {  
     LockSignWidget->SetOwnerPlayer(Cast<ULocalPlayer>(Player));
     LockSignWidget->SetWidgetSpace(EWidgetSpace::Screen);  
     LockSignWidget->SetVisibility(true);  
     LockSignWidget->SetCollisionEnabled(ECollisionEnabled::NoCollision);  
 }  
}
AAwPlayerController::AAwPlayerController()
{
	CreateWidgetComponent();
}

void AAwPlayerController::BeginPlay()
{
	Super::BeginPlay();

	if (IsLocalController())
	{
		const auto Navigation = MakeShared<FNavigationConfig>();
		Navigation->bTabNavigation = false;
		FSlateApplication::Get().SetNavigationConfig(Navigation);
		if (GetPawn()) {
	        SetupUI();
	    }
	}
}

void AAwPlayerController::OnPossess(APawn* InPawn)
{
	Super::OnPossess(InPawn);
	CurCharacter = Cast<AAwCharacter>(InPawn);
	SetupUI();
	CurPlayerState = GetAwPlayerState();
	if (CurPlayerState)
		CurPlayerState->SetupAfterPossess();
	CurCharacter->SetupCharacter();
}

void AAwPlayerController::SetupUI()
{
	if(UAwGameInstance::Instance)
		UAwGameInstance::Instance->UIManager->InitUI(this);
}

void AAwPlayerController::GetLifetimeReplicatedProps(TArray< FLifetimeProperty >& OutLifetimeProps) const
{
	Super::GetLifetimeReplicatedProps(OutLifetimeProps);

	DOREPLIFETIME(AAwPlayerController, CurCharacter);
}

void AAwPlayerController::Destroyed()
{
	Super::Destroyed();
	if (CurCharacter)
		CurCharacter->Destroy(true, true);
}

bool AAwPlayerController::InputKey(const FInputKeyParams& Params)
{
	if (IsGamepad != Params.IsGamepad())
	{
		IsGamepad = Params.IsGamepad();
		UpdateGameInstanceIsGamepad(Params);
	}

	// --- Log ---
	// FString EventTypeName = "";
	// switch (Params.Event)
	// {
	// 	case IE_Pressed:	EventTypeName = "IE_Pressed"; break;
	// 	case IE_Released:	EventTypeName = "IE_Released"; break;
	// 	case IE_Repeat:		EventTypeName = "IE_Repeat"; break;
	// 	case IE_DoubleClick:EventTypeName = "IE_DoubleClick"; break;
	// 	case IE_Axis:		EventTypeName = "IE_Axis"; break;
	// 	case IE_MAX:		EventTypeName = "IE_MAX"; break;
	// }
	// const int64 Timestamp = UDateTimeFuncLib::GetTimestamp();
	// UE_LOG(LogTemp, Log, TEXT("InputKey: %s, %s, (%hs), %lld"), *Params.Key.GetFName().ToString(), *EventTypeName, Params.IsGamepad()?"Gamepad":"Keyboard", Timestamp);
	// --- --- ---

	if (Params.Event == IE_Pressed)
	{
		if (Params.NumSamples <= 0)
		{
			OnPressKey.Broadcast(Params.Key.GetFName().ToString());
			AddInputKey(Params.Key.GetFName().ToString());
		}
	}
	else if (Params.Event == IE_Released)
		RemoveInputKey(Params.Key.GetFName().ToString());

	return Super::InputKey(Params);
}

void AAwPlayerController::UpdateGameInstanceIsGamepad(const FInputKeyParams& Params) const
{
	if (UAwGameInstance::Instance)
	{
		bool isGamepad = true;
		for (auto pc : UGameplayFuncLib::GetAllLocalAwPlayerControllers())
		{
			if (!pc->IsGamepad)
			{
				isGamepad = false;
				break;
			}
		}
		if (UAwGameInstance::Instance->bIsGamepad != isGamepad)
		{
			UAwGameInstance::Instance->bIsGamepad = isGamepad;
			UAwGameInstance::Instance->InputTypeChangeDelegate.Broadcast();
		}
	}
}

void AAwPlayerController::Tick(float DeltaSeconds)
{
	Super::Tick(DeltaSeconds);
	
	// ---------- 移动相机辅助 ----------
	// --- Debug log ---
	// UKismetSystemLibrary::PrintString(this, "InputMoveDir: " + InputMoveDir.ToString(), true, true, FLinearColor::Yellow, 0);
	// UKismetSystemLibrary::PrintString(this, "InputCameraDir: " + InputCameraDir.ToString(), true, true, FLinearColor::Yellow, 0);
	// const float ArmLength = CameraPawn ? CameraPawn->GetSpring()->TargetArmLength : -1;
	// UKismetSystemLibrary::PrintString(this, "Spring: "+FString::SanitizeFloat(ArmLength), true, true, FLinearColor::Yellow, 0);
	// const float Degree_log = UMathFuncLib::GetDegreeBetweenTwoVector(FVector::ForwardVector, FVector(InputMoveDir.X, InputMoveDir.Y , 0));
	// UKismetSystemLibrary::PrintString(this, "Degree: "+FString::SanitizeFloat(Degree_log), true, true, FLinearColor::Yellow, 0);
	// const FRotator CameraRotator = CameraPawn ? CameraPawn->GetActorRotation() : FRotator::ZeroRotator;
	// UKismetSystemLibrary::PrintString(this, "CameraRotator: "+ CameraRotator.ToString(), true, true, FLinearColor::Yellow, 0);
	// --- --- ---

	if (!InputMove.IsNearlyZero() && IsValid(CurCharacter) && CurCharacter->OnGround() &&
		!CurCharacter->GetActionComponent()->IsMontageActionPlaying() &&
		InputCamera.IsNearlyZero())
		InputMoveDur += DeltaSeconds;
	else
		InputMoveDur = 0;

	// --- Debug log ---
	// if (CurCharacter && CurCharacter->GetActionComponent()->CurrentActiveMontage())
	// {
	// 	const FString MontageName = CurCharacter->GetActionComponent()->CurrentActiveMontage() ?
	// 		CurCharacter->GetActionComponent()->CurrentActiveMontage()->Montage->GetName() : "";
	// 	UKismetSystemLibrary::PrintString(this, "MontageName: "+ MontageName, true, true, FLinearColor::Yellow, 0);
	// 	const FString IsMontagePlaying = CurCharacter->GetActionComponent()->IsMontageActionPlaying()?"True":"False";
	// 	UKismetSystemLibrary::PrintString(this, "IsMontagePlaying: "+ IsMontagePlaying, true, true, FLinearColor::Yellow, 0);
	// }
	// --- --- ---

	// 移动相机辅助
	if (!UGameplayFuncLib::IsRogueMode() &&
		GameControlState == EGameControlState::Game && InputMoveDur >= 0.5f && 
		CurCharacter && CurCharacter->OnGround())
	{
		const float Degree = UMathFuncLib::GetDegreeBetweenTwoVector(FVector::ForwardVector, FVector(InputLeftStick.X, InputLeftStick.Y , 0));
		
		float Speed = 1 - 1 * (FMath::Abs(FMath::Abs(Degree) - 90) / 90);
		if (Speed < 0) Speed = 0;
		
		Speed *= 1 - FMath::Clamp<float>((CurCharacter->GetAwCameraComponent()->GetSpring()->TargetArmLength - 280)/300, 0, 1) * 0.5;

		// UKismetSystemLibrary::PrintString(this, "Speed: "+FString::SanitizeFloat(Speed), true, true, FLinearColor::Blue, 0);
		
		FRotator AddRotator = FRotator::ZeroRotator;
		const FRotator OriRotator = CurCharacter->GetAwCameraComponent()->GetCameraRotate();
		
		if (!FMath::IsNearlyZero(OriRotator.Pitch, 0.001f) && (FMath::Abs(Degree) <= 45 || FMath::Abs(Degree) >= 135))
		{
			const float TargetPitch = OriRotator.Pitch > 180 ? 360 : 0;
			const float ToPitch = FMath::FInterpTo(OriRotator.Pitch, TargetPitch, DeltaSeconds, 1);
			const float AddPitch = ToPitch - OriRotator.Pitch;
			AddRotator.Pitch = AddPitch;
		}

		const float Abs = Degree == 0 ? 1 : Degree/FMath::Abs(Degree);
		const float AddYaw = 1 * Speed * Abs;
		AddRotator.Yaw = AddYaw;
		CurCharacter->GetAwCameraComponent()->SetCameraRotation(OriRotator + AddRotator);
		// CurCharacter->GetAwCameraComponent()->AddYawInput(AddYaw); // 这个放在这里无效，只能用上面那种办法了。
	}
	// ---------- ---------- ----------

	if (CurCharacter)
	{
		for (UCmdComponent* CmdComp : ControlledCmdComps)
		{
			CmdComp->SetMoveDir(InputMove);
			if (CurCharacter->GetAwCameraComponent())
				CmdComp->SetRotateDir(CurCharacter->GetAwCameraComponent()->GetRotateDir());
			CmdComp->CheckSprintInput(DeltaSeconds);
		}
	}
}

void AAwPlayerController::SetInputEnable(bool Enable)
{
	CanInput = Enable;
	InputCamera = FRotator::ZeroRotator;
	InputMove = FVector2D::ZeroVector;
}

void AAwPlayerController::OnRep_CurCharacter()
{
	/*FString ClassId = CurCharacter->BattleClass.Id;
	FClassInfo ClassInfo = UGameplayFuncLib::GetAwDataManager()->GetClassInfoById(ClassId);
	CurCharacter->SetupAsPlayerCharacter(ClassInfo);
	CameraPawn->TargetCharacter = CurCharacter;*/
}

void AAwPlayerController::InitInClient_Implementation(AAwCharacter* NewCharacter)
{
	if (NewCharacter)
	{
		SetMyCharacter(NewCharacter);
		Possess(NewCharacter);
	}
}

void AAwPlayerController::InitPlayerCharacter_Implementation(FTransform PlayerStart)
{

}

void AAwPlayerController::GiveBackCamera(float InSec)
{
	if (CurCharacter) 
		SetViewTargetWithBlend(CurCharacter, InSec);
}

void AAwPlayerController::GetAimedTargetFromScreenCenter(float LineDistance)
{
	const FVector CameraLoc = this->PlayerCameraManager->GetCameraLocation();
	const FRotator CameraRot = this->PlayerCameraManager->GetCameraRotation();
	FVector TraceStart = CameraLoc + CameraRot.Vector() * 25;
	FVector TraceEnd = TraceStart + CameraRot.Vector() * LineDistance;
	TArray<AActor*> ActorsToIgnore;
	ActorsToIgnore.Add(this);
	ActorsToIgnore.Add(CurCharacter);
	FHitResult HitResult = FHitResult();
	TArray<TEnumAsByte<EObjectTypeQuery>> TraceList;
	TraceList.Add(EObjectTypeQuery::ObjectTypeQuery10);
	TraceList.Add(EObjectTypeQuery::ObjectTypeQuery15);
	UKismetSystemLibrary::LineTraceSingleForObjects(this,TraceStart, TraceEnd,
		TraceList, true, ActorsToIgnore, EDrawDebugTrace::None,HitResult,true,
		FLinearColor::Red,FLinearColor::Green, 20);
	if(IsValid(HitResult.GetActor()))
	{
		AAwCharacter* HitCharacter = Cast<AAwCharacter>(HitResult.GetActor());
		
		if(HitCharacter)
		{
			//检测到目标Character
			UGameplayFuncLib::GetAwGameState()->AimCharacter = HitCharacter;
			UGameplayFuncLib::GetAwGameState()->AimLocation = FVector::ZeroVector;
			//UKismetSystemLibrary::PrintString(this,HitCharacter->GetName());
		}
		else
		{
			//检测到地面坐标
			UGameplayFuncLib::GetAwGameState()->AimCharacter = nullptr;
			UGameplayFuncLib::GetAwGameState()->AimLocation = HitResult.ImpactPoint;
			//UKismetSystemLibrary::PrintString(this,HitResult.ImpactPoint.ToString());
		}
	}
	else
	{
		//没有检测到物体，向下发射线获得地面坐标
		TraceStart = TraceEnd;
		TraceEnd = TraceStart + FVector(0,0, -1) * 500;
		TraceList.Remove(EObjectTypeQuery::ObjectTypeQuery15);
		UKismetSystemLibrary::LineTraceSingleForObjects(this,TraceStart, TraceEnd,
		TraceList, true, ActorsToIgnore,EDrawDebugTrace::None,HitResult,true,
		FLinearColor::Red,FLinearColor::Green, 20);
		if(IsValid(HitResult.GetActor()))
		{
			UGameplayFuncLib::GetAwGameState()->AimCharacter = nullptr;
			UGameplayFuncLib::GetAwGameState()->AimLocation = HitResult.ImpactPoint;
			UKismetSystemLibrary::PrintString(this,HitResult.ImpactPoint.ToString());
		}
		else
		{
			//还没检测到，就清零吧
			UGameplayFuncLib::GetAwGameState()->AimCharacter = nullptr;
			UGameplayFuncLib::GetAwGameState()->AimLocation = FVector::ZeroVector;
		}
	}
}

void AAwPlayerController::SetMyCharacter(AAwCharacter* NewCharacter)
{
	if (!UGameplayFuncLib::GetAwGameState()->GetPlayerCharacters().Contains(NewCharacter))
		UGameplayFuncLib::GetAwGameState()->AddPlayerCharacters(NewCharacter);
	CurCharacter = NewCharacter;
	NewCharacter->OwnerPlayerController = this;
	NewCharacter->PreloadAssets();
}

//TODO: Test 创建 Avatar Clone
// const AAvatarCharacter* Ava = NewObject<AAvatarCharacter>();
// UClass* BpClass = Ava->GetClass();
// if (BpClass)
// {
// 	FActorSpawnParameters ActorSpawnParameters;
// 	ActorSpawnParameters.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AlwaysSpawn;
// 	AAvatarCharacter* GuyAva = GWorld->SpawnActor<AAvatarCharacter>(BpClass, NewCharacter->GetActorTransform(), ActorSpawnParameters);
// 	GuyAva->CloneFromAwCharacter(NewCharacter);
// 	UGameplayFuncLib::GetAwGameState()->MyUIClone = GuyAva;
//
// 	TArray<AActor*> Acts;
// 	TArray<AActor*> Cams;
// 	if (GetWorld())
// 	{
// 		UGameplayStatics::GetAllActorsOfClass(GetWorld(), AUICloneLocation::StaticClass(), Acts);
// 		if (Acts.Num() > 0)
// 		{
// 			GuyAva->SetActorTransform(Acts[0]->GetActorTransform(), false);
// 		}
// 		UGameplayStatics::GetAllActorsOfClass(GetWorld(), AAwUICamera::StaticClass(), Cams);
// 		if (Cams.Num() > 0)
// 		{
// 			UGameplayFuncLib::GetAwGameState()->UICamera = Cast<AAwUICamera>(Cams[0]);
// 		}
// 	}
// }

void AAwPlayerController::AddInputKey_Implementation(const FString& InputKey)
{
	if (!CanInput) return;
	
	for (UCmdComponent* CmdComp : ControlledCmdComps)
	{
		CmdComp->AddInputKey(InputKey);
	}
}

void AAwPlayerController::RemoveInputKey_Implementation(const FString& InputKey)
{
	for (UCmdComponent* CmdComp : ControlledCmdComps)
		CmdComp->RemoveInputKey(InputKey);
}

void AAwPlayerController::AddInputKeyByActionCmdId(const FString ActionCmdId, const bool IsByGamePad)
{
	FActionCmd ActionCmd = UGameplayFuncLib::GetAwDataManager()->GetActionCmdById(ActionCmdId);
	FDefaultKeyMapping KeyMapping = UGameplayFuncLib::GetAwDataManager()->GetKeyMappingById(ActionCmd.ActionKey[0]);
	for (UCmdComponent* CmdComp : ControlledCmdComps)
	{
		if (IsByGamePad)
		{
			for (FString Key : KeyMapping.Gamepad)
			{
				CmdComp->AddInputKey(Key);
				CmdComp->RemoveInputKey(Key);
			}
		}
		else
			for (FString Key : KeyMapping.Keyboard)
			{
				CmdComp->AddInputKey(Key);
				CmdComp->RemoveInputKey(Key);
			}
	}
}

void AAwPlayerController::AttachLockSignToMonster(AAwCharacter* Monster)
{
    if (Monster && Monster->GetMesh())
    {
        if (!LockSignWidget)
        {
            CreateWidgetComponent();
        }

        // 将 WidgetComponent 附加到怪物的指定骨骼上
        LockSignWidget->AttachToComponent(
            Monster->GetMesh(),              // 怪物的 SkeletalMeshComponent
            FAttachmentTransformRules::SnapToTargetIncludingScale, // 变换规则
            Monster->LockSignParentSocket                         // 绑定到的骨骼名（例如 "Spine02"）
        );
		UE_LOG(LogTemp, Log, TEXT("AttachWidgetToMonsterBone: %s Bonename:%s"), *Monster->GetName(), *Monster->LockSignParentSocket.ToString());
        
        // 你还可以设置 WidgetComponent 的相对偏移量
        // LockSignWidget->SetRelativeLocation(FVector(0.0f, 0.0f, 50.0f)); // 偏移位置，调整显示效果
    }
}

void AAwPlayerController::UpdateLockSignOwnerPlayer()
{
	LockSignWidget->GetOwner()->SetHidden(false);
	LockSignWidget->SetOwnerPlayer(Cast<ULocalPlayer>(Player));
}

void AAwPlayerController::HideLockSign() const
{
	LockSignWidget->SetVisibility(false);
}

int AAwPlayerController::GetLocalPCIndex()
{
	if (LocalPCIndex>=0) return LocalPCIndex;
	if (ULocalPlayer* LocalPlayer = Cast<ULocalPlayer>(Player))
    {
        if (UGameInstance* GameInstance = GetGameInstance())
        {
            const TArray<ULocalPlayer*>& LocalPlayers = GameInstance->GetLocalPlayers();
            LocalPCIndex = LocalPlayers.IndexOfByKey(LocalPlayer);
        }
    }
    // LocalPCIndex = -1;
	return LocalPCIndex;
}

AAwPlayerState* AAwPlayerController::GetAwPlayerState()
{
	if (!CurPlayerState)
		CurPlayerState = GetPlayerState<AAwPlayerState>();
	return CurPlayerState;
}

void AAwPlayerController::SetInputMoveDir(FVector2D V2, FVector2D LeftStick)
{
	InputMove = V2;
	InputLeftStick = LeftStick;
}

void AAwPlayerController::SetInputCameraDir(FRotator Rotator, FVector2D RightStick)
{
	InputCamera = Rotator;
	InputRightStick = RightStick;

	if (!CurCharacter) return;
	if (UAwCameraComponent* CameraComponent = CurCharacter->GetAwCameraComponent())
	{
		// 有锁定目标，不能手动控制镜头
		if (!CameraComponent->GetOnLockTarget().IsValid())
		{
			if(UGameplayFuncLib::GetAwGameInstance()->bIsGamepad)
			{
				Rotator.Yaw *= URogueGameSetting::GetRogueGameSettings()->GetbIsInvertX_Axis()?
					URogueGameSetting::GetRogueGameSettings()->GetGamepadXSensitivity()*-1:URogueGameSetting::GetRogueGameSettings()->GetGamepadXSensitivity()*1;
				Rotator.Yaw *= 0.7;
				Rotator.Pitch *= URogueGameSetting::GetRogueGameSettings()->GetbIsInvertY_Axis()?
					URogueGameSetting::GetRogueGameSettings()->GetGamepadYSensitivity()*-1:URogueGameSetting::GetRogueGameSettings()->GetGamepadYSensitivity()*1;
				Rotator.Pitch *= 0.7;
			}			
			else
			{
				Rotator.Yaw *= URogueGameSetting::GetRogueGameSettings()->GetMouseXSensitivity();
				Rotator.Pitch *= URogueGameSetting::GetRogueGameSettings()->GetMouseYSensitivity();
			}
			
			
			/*FString Temp = FString::Printf(TEXT("Rotator.Yaw : %f,Rotator.Pitch : %f,Rotator.Roll : %f"),Rotator.Yaw,Rotator.Pitch,Rotator.Roll);
			UKismetSystemLibrary::PrintString(this,Temp,true,true,FLinearColor(0,0.66,1),0);*/
			CameraComponent->AddYawInput(Rotator.Yaw);
			CameraComponent->AddPitchInput(Rotator.Pitch);
			const FRotator CameraRotate = CameraComponent->GetCameraRotate();
			if (CameraRotate.Pitch < 180 && CameraRotate.Pitch > 70)
				CameraComponent->SetCameraRotation(FRotator(70, CameraRotate.Yaw, CameraRotate.Roll));
			else if(CameraRotate.Pitch >= 180 && CameraRotate.Pitch < 290)
				CameraComponent->SetCameraRotation(FRotator(290, CameraRotate.Yaw, CameraRotate.Roll));
		}
	}
}

void AAwPlayerController::ShowPopText(AAwCharacter* OnCharacter, const FString& Text, FLinearColor TextColor)
{
	if(UGameplayFuncLib::GetUiManager()->PopText)
		UGameplayFuncLib::GetUiManager()->PopText->PopText(OnCharacter, Text, TextColor);
}
void AAwPlayerController::ShowPopText(USceneComponent* OnBox, const FString& Text, FLinearColor TextColor)
{
	if(UGameplayFuncLib::GetUiManager()->PopText)
		UGameplayFuncLib::GetUiManager()->PopText->PopText(OnBox, Text, TextColor);
}

void AAwPlayerController::OnLockTargetSwitch(bool bSwitchLeft)
{
	AAwCharacter* Me = CurCharacter;
	if (!Me) return;

	if (Me->Implements<UAwCameraInterface>())
	{
		IAwCameraInterface::Execute_OnLockTargetSwitch(Me, bSwitchLeft);	
	}
	CheckLockSign();
}
void AAwPlayerController::CheckLockSign()
{
	if (CurCharacter && CurCharacter->GetAwCameraComponent())
	{
		auto Target = Cast<AAwCharacter>(CurCharacter->GetAwCameraComponent()->GetOnLockTarget());
		bool socketValid = false;
		if (Target) {
			AttachLockSignToMonster(Target);
			socketValid = !Target->LockSignParentSocket.IsNone() && Target->LockSignParentSocket.GetStringLength() > 0;
		}
		LockSignWidget->SetVisibility(CurCharacter->GetAwCameraComponent()->GetOnLockTarget().IsValid()&& socketValid);
	}
}
void AAwPlayerController::UpdateSessionInfoToCurClient_Implementation(FAwSessionInfo SessionInfo)
{
	UAwGameInstance* CurGameInstance = UGameplayFuncLib::GetAwGameInstance();
	if (CurGameInstance)
	{
		CurGameInstance->ChangeSessionInfo(SessionInfo);
	}
}

void AAwPlayerController::BreakOnlineLink_Implementation(const FString& BreakReason)
{
	UAwGameInstance* CurGameInstance = UGameplayFuncLib::GetAwGameInstance();
	if (CurGameInstance)
	{ 
		CurGameInstance->LeaveOnline();
	}
}

void AAwPlayerController::LoadAllDungeonLevelFinish_Implementation()
{
	AGameStateBase* GameState = UGameplayStatics::GetGameState(this);
	if (GameState)
	{
		AAwGameMode_RandomDungeon* DungeonGameMode = Cast<AAwGameMode_RandomDungeon>(GameState);
		if (DungeonGameMode)
		{
			DungeonGameMode->PlayerClientLoadFinish(this);
		}
	}
}

void AAwPlayerController::LoadDungeonAllRoomLevel_Implementation()
{
	UAwGameInstance* CurGameInstance = UGameplayFuncLib::GetAwGameInstance();
	if (CurGameInstance)
	{
		CurGameInstance->UIManager->ShowLoading();
		CurGameInstance->LoadAllDungeonLevel();
	}
}

void AAwPlayerController::UnLoadAllDungeonRoomLevel_Implementation()
{
	UAwGameInstance* CurGameInstance = UGameplayFuncLib::GetAwGameInstance();
	if (CurGameInstance)
	{
		CurGameInstance->UnLoadAllDungeonLevel();
	}
}

void AAwPlayerController::LeaveDungeon_Implementation(const FString& DungeonName)
{
	UAwGameInstance* CurGameInstance = UGameplayFuncLib::GetAwGameInstance();
	if (CurGameInstance)
	{
		CurGameInstance->ClearDungeonLevelInfo();
	}
}

void AAwPlayerController::ClientShowPopText_Implementation(AAwCharacter* OnCharacter, const FString& Text, FLinearColor TextColor)
{
	// if (GetHUD())
	// {
	// 	AAW_HUD* CurHUD = Cast<AAW_HUD>(GetHUD());
	// 	if (CurHUD->GetFormByNormal("PopText"))
	// 	{
	// 		UPopText* CurPopText = Cast<UPopText>(CurHUD->GetFormByNormal("PopText"));
	// 		CurPopText->PopText(OnCharacter, Text, TextColor, Enlarge);
	// 	}
	// }
	if(UGameplayFuncLib::GetUiManager()->PopText)
		UGameplayFuncLib::GetUiManager()->PopText->PopText(OnCharacter, Text, TextColor);
}
