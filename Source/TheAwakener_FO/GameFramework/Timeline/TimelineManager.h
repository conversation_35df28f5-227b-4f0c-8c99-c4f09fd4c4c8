// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "TimelineNode.h"
#include "TimelineManager.generated.h"

/**
 * 
 */
USTRUCT(BlueprintType)
struct FTimeSlomoData
{
	GENERATED_BODY()
public:
	//存在了多久
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float LivedTime= 0.f;
	//持续时间
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float Duration = 0.f;
	//速度修改成多少倍
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float Rate = 0.f;
	//结束时还原成什么倍率
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float BackRate = 1.f;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int Priority =0;
	
	
	bool bIsActive = false;
};

UCLASS(Blueprintable)
class THEAWAKENER_FO_API UTimelineManager : public UObject
{
	GENERATED_BODY()
public:
	UPROPERTY(BlueprintReadOnly)
	TArray<UTimelineNode*> Nodes;

	UPROPERTY(BlueprintReadOnly)
	TArray<FString>UniqueIds;
	
	UFUNCTION(BlueprintCallable)
	void Update(float DeltaTime);

	UFUNCTION(BlueprintCallable)
	void AddNode(UTimelineNode* Node);
	
	UFUNCTION(BlueprintCallable)
	void SetGlobalTimeSlomo(FTimeSlomoData SlomoData);
private:
	FTimeSlomoData CurSlomo = FTimeSlomoData();
};
