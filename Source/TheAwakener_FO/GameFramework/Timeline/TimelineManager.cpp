// Fill out your copyright notice in the Description page of Project Settings.


#include "TimelineManager.h"
#include "Kismet/GameplayStatics.h"
#include "TheAwakener_FO/FunctionLibrary/CallFuncLib.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"

void UTimelineManager::Update(float DeltaTime)
{
	if (Nodes.Num())
	{
		TArray<UTimelineNode*> RemoveNodes;
		for (int i = 0; i < Nodes.Num(); i++)
		{
			if(Nodes[i])
			{
				Nodes[i]->DelayTIme-=DeltaTime;
				if (Nodes[i]->DelayTIme<=0.f)
				{
					if (!Nodes[i]->FuncClassPath.IsEmpty() && !Nodes[i]->FuncName.IsEmpty())
					{
						UFunction* Func = UCallFuncLib::GetUFunction(Nodes[i]->FuncClassPath, Nodes[i]->FuncName);
						if (Func)
						{
							struct
							{
								float TimeElapsed;
								TArray<FString> Params;
								bool Result = false;
							}FuncParam;
							FuncParam.TimeElapsed = Nodes[i]->TimeElapsed;
							FuncParam.Params = Nodes[i]->Params;
							this->ProcessEvent(Func, &FuncParam);
							if (FuncParam.Result == true)
							{
								if (Nodes[i]->NextNodes.Num())
								{
									for (int j = 0; j < Nodes[i]->NextNodes.Num(); j++)
									{
										Nodes.Add(Nodes[i]->NextNodes[j]);
									}
								}
								RemoveNodes.Add(Nodes[i]);
							}
							Nodes[i]->TimeElapsed += DeltaTime;
							continue;
						}
					}			
				}
			}
			RemoveNodes.Add(Nodes[i]);
		}
		if (RemoveNodes.Num())
		{
			for (int i = 0; i < RemoveNodes.Num(); i++)
			{
				UTimelineNode* Temp = RemoveNodes[i];
				if (!Temp->MyUniqueID.IsEmpty())
				{
					UniqueIds.Remove(Temp->MyUniqueID);
				}
				Nodes.Remove(Temp);
				Temp->MarkAsGarbage();
			}
		}
	}
	if (CurSlomo.bIsActive)
	{
		CurSlomo.Duration-=DeltaTime;
		CurSlomo.LivedTime+=DeltaTime;
		if (CurSlomo.Duration<=0.f)
		{
			for (auto pc : UGameplayFuncLib::GetAllLocalAwPlayerControllers())
			{
				if(pc)
				{
					UGameplayStatics::SetGlobalTimeDilation(pc,CurSlomo.BackRate);
				}
			}
			CurSlomo.bIsActive = false;
		}
	}
}

void UTimelineManager::AddNode(UTimelineNode* Node)
{
	if(Node)
	{
		if (!Node->FuncClassPath.IsEmpty() && !Node->FuncName.IsEmpty())
		{
			if (Node->MyUniqueID.IsEmpty())
			{
				Nodes.Add(Node);
			}
			else
			{
				if (!UniqueIds.Contains(Node->MyUniqueID))
				{
					Nodes.Add(Node);
				}
			}
		}
	}
}

void UTimelineManager::SetGlobalTimeSlomo(FTimeSlomoData SlomoData)
{
	UWorld* World = nullptr;
	for (auto pc : UGameplayFuncLib::GetAllLocalAwPlayerControllers())
	{
		if (pc)
		{ 
			World = pc->GetWorld();
		}
		if (!World)
		{
			return;
		}
		if (!World->IsGameWorld())
		{
			return;
		}
	
		if (SlomoData.Duration<=0.f)
		{
			return;
		}
	
		if (!CurSlomo.bIsActive)
		{
			CurSlomo = SlomoData;
			CurSlomo.bIsActive = true;
			if(World&&World->IsGameWorld())
			{
				UGameplayStatics::SetGlobalTimeDilation(pc,CurSlomo.Rate);
			}
		}
		else if (SlomoData.Priority >= CurSlomo.Priority)
		{
			CurSlomo = SlomoData;
			CurSlomo.bIsActive = true;

			if(World&&World->IsGameWorld())
			{
				UGameplayStatics::SetGlobalTimeDilation(pc,CurSlomo.Rate);
			}
		}
	}
	
}


