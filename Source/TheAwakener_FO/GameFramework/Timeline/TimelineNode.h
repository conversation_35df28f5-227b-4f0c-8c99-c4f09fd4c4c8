// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TimelineNode.generated.h"

/**
 * 
 */
 //TimelineManager在Tick中调用每个Node的Func,把TimeElapsed作为参数导入，当返回值为true时，检测这个Node的NextNode加入Nodes列表,并删除这个当前Node
UCLASS(Blueprintable)
class THEAWAKENER_FO_API UTimelineNode : public UObject
{
	GENERATED_BODY()
public:
	UTimelineNode();

	//唯一id  某些node只能同时存在一个 此id不为空 则在manager中add时 会辨识唯一性
	UPROPERTY(VisibleAnywhere, BlueprintReadWrite)
	FString MyUniqueID = "";
	UPROPERTY(VisibleAnywhere, BlueprintReadWrite)
	TArray<FString> Tags;
	
	UPROPERTY(VisibleAnywhere, BlueprintReadWrite)
	UObject* Owner = nullptr;

	UPROPERTY(VisibleAnywhere, BlueprintReadWrite)
	float TimeElapsed = 0;

	UPROPERTY(VisibleAnywhere, BlueprintReadWrite)
	float DelayTIme = 0.f;
	
	UPROPERTY(VisibleAnywhere, BlueprintReadWrite)
	TArray<UTimelineNode*> NextNodes;
	
	UPROPERTY(VisibleAnywhere, BlueprintReadWrite)
	FString FuncClassPath = "";
	UPROPERTY(VisibleAnywhere, BlueprintReadWrite)
	FString FuncName = "";
	UPROPERTY(VisibleAnywhere, BlueprintReadWrite)
	TArray<FString> Params;

	UFUNCTION(BlueprintCallable, Category = "AW|Framework|Timeline")
	void AddNextNode(UTimelineNode* Node);

	UFUNCTION(BlueprintCallable, Category = "AW|Framework|Timeline")
	void BeNextNode(UTimelineNode* Node);
};
