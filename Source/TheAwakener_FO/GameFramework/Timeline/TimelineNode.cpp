// Fill out your copyright notice in the Description page of Project Settings.


#include "TimelineNode.h"

#include "TheAwakener_FO/FunctionLibrary/DateTimeFuncLib.h"


UTimelineNode::UTimelineNode()
{
	MyUniqueID = UDateTimeFuncLib::CreateUniqueId("TimeLineNode");
}

void UTimelineNode::AddNextNode(UTimelineNode* Node)
{
	if (Node)
	{
		NextNodes.Add(Node);
	}
}

void UTimelineNode::BeNextNode(UTimelineNode* Node)
{
	if (Node)
	{
		Node->NextNodes.Add(this);
	}
}
