// Fill out your copyright notice in the Description page of Project Settings.


#include "AW_HUD.h"

#include "Blueprint/UserWidget.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"


void AAW_HUD::BeginPlay()
{
	Super::BeginPlay();
	this->SetActorTickEnabled(true);
}

void AAW_HUD::Tick(float DeltaSeconds)
{
	Super::Tick(DeltaSeconds);

	// TODO: 之后需要移到相应的ui里面么?
	UGameplayFuncLib::GetLootManager()->TickChoseWillLootActor();
}

void AAW_HUD::RemoveFromHUD(UBaseForm* Find)
{
	int FindWidgetIndex = 0;
	for (auto& Elem : this->Normal)
	{
		if (Elem.Value == Find)
		{
			break;
		}
		FindWidgetIndex++;
	}
	TArray<FString> Keys;
	this->Normal.GenerateKeyArray(Keys);
	this->Normal.Remove(Keys[FindWidgetIndex]);
	this->FormsStack.Remove(Find);
}
