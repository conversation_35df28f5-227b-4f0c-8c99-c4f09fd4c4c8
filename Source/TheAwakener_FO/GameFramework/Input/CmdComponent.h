// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "EnumCmd.h"
#include "GameControlState.h"
#include "Components/ActorComponent.h"
#include "CmdComponent.generated.h"

class AAwCharacter;
class UAwDataManager;


//AI移动请求的记录
USTRUCT()
struct FAIMoveApplicationRecord
{
	GENERATED_BODY()
public:
	//时间点
	UPROPERTY()
	int64 Timestamp = 0;

	//移动方向
	UPROPERTY()
	FVector2D MoveDir = FVector2D::ZeroVector;

	//速度档次
	UPROPERTY()
	int SpeedLevel = 1;

	//面向
	UPROPERTY()
	FVector2D FaceDir = FVector2D::ZeroVector;

	// 生成了多少帧，0帧不删除
	UPROPERTY()
	int FrameTimer = 0;
	
	FAIMoveApplicationRecord(){};
	FAIMoveApplicationRecord(int64 Time, FVector2D DirOfMove, int LevelOfSpeed, FVector2D DirOfFace):
		Timestamp(Time), MoveDir(DirOfMove), SpeedLevel(LevelOfSpeed), FaceDir(DirOfFace){};
};

//摇把操作的记录
USTRUCT()
struct FStickRecord
{
	GENERATED_BODY()
public:
	//时间点
	UPROPERTY()
	int64 Timestamp = 0;

	//方向
	UPROPERTY()
	FVector2D Input = FVector2D::ZeroVector;

	//处于什么操作状态得到的这个按键
	UPROPERTY()
	EGameControlState ReceivedUnderState = EGameControlState::Game;

	FStickRecord():Timestamp(0), Input(FVector2D::ZeroVector), ReceivedUnderState(EGameControlState::Game){};
	FStickRecord(int64 Time, FVector2D StickInput, EGameControlState UnderState):
		Timestamp(Time), Input(StickInput), ReceivedUnderState(UnderState){};
	
};

UCLASS(ClassGroup=(Custom), meta=(BlueprintSpawnableComponent))
class THEAWAKENER_FO_API UCmdComponent : public UActorComponent
{
	GENERATED_BODY()

protected:
	virtual void BeginPlay() override;
	
private:
	UPROPERTY()
	AAwCharacter* OwnerCharacter;

	// 这里的按键删除速度影响按键预输入的手感，250甚好（0.25秒）
	const int RemoveInterval = 250;
	const float WalkAxisDis = 0.6f;
	const float RunAxisDis = 0.9f;
	const int64 HoldingInterval = 100;
	
	/**
	 * @param FString InputKey
	 * @param int64 0 - Timestamp
	 * @param bool  1 - Is wait to remove
	 * @param int64 2 - Timestamp on released
	 * @param bool  3 - HasCheckPress
	 */
	TMap<FString, TTuple<int64, bool, int64, bool>> InputKeyList;
	int SpeedLevel = 0;
	
	TArray<FAIMoveApplicationRecord> AIMoveRec;	//AI行动纪录请求
	FAIMoveApplicationRecord LastMoveRec;
	int MaxAIAppRec = 5;	//记录5条Ai移动请求

	//更换状态后冷却时间设置为
	UPROPERTY()
	float CooldownAfterChangeStage = 0.4f;
	//多久后命令有效
	UPROPERTY()
	float CmdCooldown = 0;
	UPROPERTY()
	EGameControlState WasState = EGameControlState::Game;

	// 移动朝向
	FVector2D MoveDir = FVector2D::ZeroVector;
	// 旋转朝向
	FVector2D RotateDir = FVector2D(1,0);

	TArray<FString> AiKeyList;
	FVector2D AiMoveDir = FVector2D::ZeroVector;
	int AiSpeedLevel = 0;
	FVector2D AiFaceDir = FVector2D::ZeroVector; //TODO: 这个目前是没有用的，需要清理，考虑这些变量是否应该放在CmdComponent里面

	bool HasInputKey(FString InputKey) const;

	UPROPERTY()
	UAwDataManager* dataManager;
	UAwDataManager* GetDataManager();

	
public:
	
	UCmdComponent();

	void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

	//一些子状态
	bool InCanInteraction = false;	//处于有可交互物品的状态
	bool InAiming = false;	//处于开镜状态，这时候期望方向会发生变化
	bool IsSprint = false;	//是否是冲刺
	float SprintTime = HoldingInterval/1000.0f;

	// 按下的时候键入，需要 RemoveInputKey() 配合
	UFUNCTION(NetMulticast, Reliable)
	void AddInputKey(const FString& InputKey);
	// 抬起的时候键入，需要 AddInputKey() 配合
	UFUNCTION(NetMulticast, Reliable)
	void RemoveInputKey(const FString& InputKey);
	
	// 设置身体朝向
	void SetRotateDir(FVector2D Dir);

	//设置移动方向，AI直接调用这个，玩家操作需要根据状态从LeftStickDir和RightStickDir返回
	UFUNCTION(NetMulticast, Reliable)
	void SetMoveDir(FVector2D Dir);
	UFUNCTION()
	void SetAIMoveAndFaceDir(FVector2D AIMoveDir, FVector2D AIFaceDir, int AISpeedLevel);
	UFUNCTION()
	void CheckSprintInput(float DeltaSeconds);
	UFUNCTION()
	void OnPressSprintAction();
	
	void AddAIAction(FString ActionId);

	void CleanPlayerInput()
	{
		InputKeyList.Empty();
	}

	// FString - ActionId
	// get<0> - int - Timestamp
	// get<1> - int - Current Interval
	// get<2> - bool - is first check holding
	TMap<FString, TTuple<int64, int64, bool>> HoldingRecord;
	UFUNCTION(BlueprintCallable)
	bool IsActionHoldingOccur(FString ActionCmdId, int64 FirstInterval = 200, int64 MinInterval = 10);
	
	/**
	 * @brief 判断是否有 Action 在cmdList里面
	 * @param ActionCmdId
	 * @param InputState 按键的对应状态，按下还是按住还是不按
	 * @param RemoveInputs 是否在完成这次访问后，删除对应的按键记录
	 * @return 
	 */
	UFUNCTION(BlueprintCallable)
	bool IsActionOccur(FString ActionCmdId, EAwInputState InputState = EAwInputState::Press, bool RemoveInputs = false);

	/**
	 * @brief 判断是否有 Action 在cmdList里面
	 * @param CheckActions 所有的Action条件TArray<TTuple<ActionCmdId, EAwInputState>>，注意！不支持方向键
	 * @return 只要有一条符合了，就会返回true
	 */
	bool AnyActionOccur(TArray<TTuple<FString, EAwInputState>> CheckActions);
	
	virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

	void ClearAiCmd();
	
	FVector2D GetMoveDir();
	FVector2D GetRotateDir() const;
	int GetSpeedLevel();

	UFUNCTION(BlueprintCallable)
	bool IsHoldingAim();
};
