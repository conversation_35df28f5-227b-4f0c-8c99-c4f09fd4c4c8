// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GameControlState.generated.h"

/**
 * 游戏的操作状态
 */
UENUM(BlueprintType)
enum class EGameControlState: uint8
{
	//正在一些切换状态的状态，比如要播放一顿动画之类的，他不是马上到一个新的状态的
	ChangingState,
	//正常游戏状态
    Game,
	//玩家访问商店状态
	Shopping,
	//正在对话中状态
	Dialog,
	//暂停菜单
	PauseMenu,
	//更换技能或职业
	ChangeSkillOrClass,
	//对话框
	MessageDialog,
	//进入游戏之前
	Title,
	//新手引导时
	NewbieHint,
	//死亡时
	Death,						

	//======================== RoguelikeState ================================

	//选房间
	SelectionRoom,
	//选觉醒技能
	AwakeSkill,
	//提醒（需要确认或取消时的状态用）
	Reminder,
	//选奖励
	RogueRewards,
	//Rogue商店
	RogueShop,
	//天赋
	RogueTalent,
	//Rogue切换角色
	RogueChangePawn,
	//Rogue切换技能
	RogueChangeSkill,
	//Rogue切换觉醒技能
	RogueChangeAwakeSkill,
	//Rogue设置木桩
	RogueSetStakePawn,
	//Rogue 职业
	RogueCareer,
	//Rogue 升级血瓶
	UpgradeHealingPotion,
	//Rogue升级主动道具
	UpgradeActiveItem,
	//Rogue 更换技能
	RogueSkillSelection,
	//Rogue 祈祷对象选择
	RoguePrayerTargetSelection,
	//Rogue 游戏暂停
	RogueGamePaused,
	//Rogue 获取奖励和选房间
	RogueRewardsAndRoom,
	//Rogue UI 通用状态
	RogueOnUI,
	//Rogue UI 通用二级状态
	RogueOnSecondaryUI,
	//Rogue UI 通用三级状态
	RogueOnTertiaryUI,
	//Rogue设置主界面
	RogueSettingMain,
	//Rogue设置界面
	RogueSetting,
	//过场动画
	Sequence,
	//新手教程关卡
	RogueNewbieLevel
};////


/**
 * UI的操作状态
 */
UENUM()
enum class EGameUIControlState: uint8
{
	MainUIState,			//主级UI
	SecondaryUIState,		//二级UI
	TertiaryMenu,			//三级UI
	Changing				//更换中
};
