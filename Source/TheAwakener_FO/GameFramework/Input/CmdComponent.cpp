// Fill out your copyright notice in the Description page of Project Settings.


#include "CmdComponent.h"

#include "Kismet/KismetSystemLibrary.h"
#include "TheAwakener_FO/FunctionLibrary/DateTimeFuncLib.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GameFramework/Manager/AwDataManager.h"

UCmdComponent::UCmdComponent()
{
	PrimaryComponentTick.bCanEverTick = true;
}

void UCmdComponent::GetLifetimeReplicatedProps(TArray< FLifetimeProperty >& OutLifetimeProps) const
{
	Super::GetLifetimeReplicatedProps(OutLifetimeProps);
}

UAwDataManager* UCmdComponent::GetDataManager()
{
	if (!IsValid(dataManager))
		dataManager = UGameplayFuncLib::GetDataManager();
	return dataManager;
}

void UCmdComponent::AddInputKey_Implementation(const FString& InputKey)
{
	const int64 Timestamp = UDateTimeFuncLib::GetTimestamp();
	if (InputKeyList.Contains(InputKey))
		InputKeyList[InputKey] = TTuple<int64, bool, int64, bool>(Timestamp, false, Timestamp, false);
	else
		InputKeyList.Add(InputKey, TTuple<int64, bool, int64, bool>(Timestamp, false, Timestamp, false));
}

void UCmdComponent::RemoveInputKey_Implementation(const FString& InputKey)
{
	if (InputKeyList.Contains(InputKey))
	{
		InputKeyList[InputKey].Get<1>() = true;
		const int64 Timestamp = UDateTimeFuncLib::GetTimestamp();
		InputKeyList[InputKey].Get<2>() = Timestamp;
	}
}

void UCmdComponent::SetMoveDir_Implementation(FVector2D Dir)
{	
	int NewSpeedLevel = 0;

	if (Dir.IsNearlyZero())
		IsSprint = false;
	
	if (!Dir.IsNearlyZero())
	{
		if (InAiming == false && IsSprint)
			// 瞄准模式不能冲锋
			NewSpeedLevel = 3;
		else
		{
			const float SizeSqr = Dir.SizeSquared();
			if (SizeSqr >= FMath::Square(RunAxisDis))
				NewSpeedLevel = 2;
			else if (SizeSqr >= FMath::Square(WalkAxisDis))
				NewSpeedLevel = 1;
			else
				NewSpeedLevel = 0;
			}
	}
	SpeedLevel = NewSpeedLevel;
	MoveDir = Dir;
}

void UCmdComponent::SetRotateDir(FVector2D Dir)
{
	RotateDir = Dir;
}

void UCmdComponent::AddAIAction(FString ActionId)
{
	const FActionInfo* ActionInfo = OwnerCharacter->GetActionById(ActionId);
	if (ActionInfo)
	{
		bool Added = false;
		for (const FString ActionCmd : ActionInfo->Commands)
		{
			if (Added) break;
			if (AiKeyList.Contains(ActionCmd) == false)
			{
				AiKeyList.Add(ActionCmd);
				Added = true;
			}
		}
	}
}

void UCmdComponent::BeginPlay()
{
	Super::BeginPlay();

	OwnerCharacter = Cast<AAwCharacter>(GetOwner());

	this->SetTickableWhenPaused(true);
}

void UCmdComponent::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
	Super::TickComponent(DeltaTime, TickType, ThisTickFunction);
	auto pc = UGameplayFuncLib::GetPlayerControllerByComp(this,false);
	{
		if (!pc)
		{
			// UE_LOG(LogTemp, Error, TEXT("UCmdComponent::TickComponent PlayerController Not Found in %s"), *this->GetOwner()->GetFullName());
			return;
		}
		//如果已经在CD那就不管是否是换状态了
		if (this->CmdCooldown > 0)
		{
			CmdCooldown -= DeltaTime;
		}
		else
		{
			if (pc && pc->GameControlState != this->WasState)
			{
				CmdCooldown = CooldownAfterChangeStage;
			}
		}
		if (pc)
			this->WasState = pc->GameControlState;
	}

	// UKismetSystemLibrary::PrintString(this, "~~~~~~~~~~~~~~~~~~~");
	
	// remove input key pressed 100ms(RemoveInterval) ago. 
	TArray<FString> WillRemoveList;
	const int64 Timestamp = UDateTimeFuncLib::GetTimestamp();
	for (TTuple<FString, TTuple<int64, bool, int64, bool>> InputKey : InputKeyList)
	{
		if(InputKey.Value.Get<1>() && Timestamp - InputKey.Value.Get<2>() >= RemoveInterval)
			WillRemoveList.Add(InputKey.Key);
	}
	for (FString Str : WillRemoveList)
		InputKeyList.Remove(Str);
	
	for (int i = 0; i < AIMoveRec.Num(); i++)
		AIMoveRec[i].FrameTimer += 1;
}

void UCmdComponent::ClearAiCmd()
{
	AiKeyList.Empty();
	AiMoveDir = FVector2D::ZeroVector;
	AiFaceDir = FVector2D::ZeroVector;
	AiSpeedLevel = 0;
	
	// AIMoveRec.Empty();
	int i = 0;
	while (i < AIMoveRec.Num())
	{
		if (AIMoveRec[i].FrameTimer > 0)
			AIMoveRec.RemoveAt(i);
		else
			i ++;
	}
}

FVector2D UCmdComponent::GetMoveDir()
{
	if (
		!this->OwnerCharacter ||
		this->OwnerCharacter->ControlState.CanMove == EControlStateType::NoControl ||
		this->OwnerCharacter->ControlState.CanMove == EControlStateType::OutOfControl
	)
		return FVector2D::ZeroVector;

	
	FAIMoveApplicationRecord AICtrl;
	if(AIMoveRec.Num() > 0)
	{
		AICtrl = AIMoveRec[AIMoveRec.Num() - 1];
		LastMoveRec = AIMoveRec[AIMoveRec.Num() - 1];
	}else if(LastMoveRec.Timestamp != 0)
	{
		AICtrl = LastMoveRec;
		LastMoveRec = FAIMoveApplicationRecord();
	}
	else
		AICtrl = FAIMoveApplicationRecord();
	

	if (this->OwnerCharacter->ControlState.CanMove == EControlStateType::OnlyAI)
		return AICtrl.MoveDir;

	if (!this->OwnerCharacter->UnderPlayerControl())
		return AICtrl.MoveDir;

	if (SpeedLevel <= 0)
		return FVector2D::ZeroVector;
	
	return MoveDir;
}

FVector2D UCmdComponent::GetRotateDir() const
{
	return RotateDir;
}

int UCmdComponent::GetSpeedLevel()
{
	if (
		!this->OwnerCharacter ||
		this->OwnerCharacter->ControlState.CanMove == EControlStateType::NoControl ||
		this->OwnerCharacter->ControlState.CanMove == EControlStateType::OutOfControl)
			return 0;

	FAIMoveApplicationRecord AICtrl;
	if(AIMoveRec.Num() > 0)
	{
		AICtrl = AIMoveRec[AIMoveRec.Num() - 1];
		LastMoveRec = AIMoveRec[AIMoveRec.Num() - 1];
	}else if(LastMoveRec.Timestamp != 0)
	{
		AICtrl = LastMoveRec;
		LastMoveRec = FAIMoveApplicationRecord();
	}
	else
		AICtrl = FAIMoveApplicationRecord();

	if (this->OwnerCharacter->ControlState.CanMove == EControlStateType::OnlyAI)
		return AICtrl.SpeedLevel;

	if (!this->OwnerCharacter->UnderPlayerControl())
		return AICtrl.SpeedLevel;

	return SpeedLevel;
}

bool UCmdComponent::IsHoldingAim()
{
	return IsActionOccur("Aim", EAwInputState::Hold);
}

bool UCmdComponent::AnyActionOccur(TArray<TTuple<FString, EAwInputState>> CheckActions)
{
	if (!IsValid(OwnerCharacter))
		return false;

	for (TTuple<FString, EAwInputState> Action : CheckActions)
	{
		const FString ActionCmdId = Action.Get<0>();
		const EAwInputState AInState = Action.Get<1>();
		if (IsActionOccur(ActionCmdId, AInState))
			return true;
	}

	return false;
}

bool UCmdComponent::IsActionHoldingOccur(FString ActionCmdId, int64 FirstInterval, int64 MinInterval)
{
	if (IsActionOccur(ActionCmdId, EAwInputState::Hold))
	{
		const int64 NowTimeStamp = UDateTimeFuncLib::GetTimestamp();
		if (HoldingRecord.Contains(ActionCmdId))
		{
			const int64 NowInterval = NowTimeStamp - HoldingRecord[ActionCmdId].Get<0>();
			const int CheckInterval =  HoldingRecord[ActionCmdId].Get<1>();
			
			if (NowInterval >= CheckInterval)
			{
				HoldingRecord[ActionCmdId].Get<0>() = NowTimeStamp;
				if (!HoldingRecord[ActionCmdId].Get<2>())
				{
					int64 NewInterval = HoldingRecord[ActionCmdId].Get<1>() / 2;
					if (NewInterval < MinInterval)
						NewInterval = MinInterval;
					HoldingRecord[ActionCmdId].Get<1>() = NewInterval;
				}
				HoldingRecord[ActionCmdId].Get<2>() = false;
				return  true;
			}
			else
			{
				return  false;
			}
		}
		else
		{
			TTuple<int64, int64, bool> Record;
			Record.Get<0>() = NowTimeStamp;
			Record.Get<1>() = FirstInterval;
			Record.Get<2>() = true;
			HoldingRecord.Add(ActionCmdId, Record);
			return true;
		}
	}
	else
	{
		if (IsActionOccur(ActionCmdId, EAwInputState::Press, true))
		{
			const int64 NowTimeStamp = UDateTimeFuncLib::GetTimestamp();
			TTuple<int64, int64, bool> Record;
			Record.Get<0>() = NowTimeStamp;
			Record.Get<1>() = FirstInterval;
			Record.Get<2>() = true;
			HoldingRecord.Add(ActionCmdId, Record);
			return true;
		}
	}
	
	return false;
}

bool UCmdComponent::IsActionOccur(FString ActionCmdId, EAwInputState InputState, bool RemoveInputs)
{
	if (this->CmdCooldown > 0) return false;
	if (ActionCmdId.IsEmpty() || !IsValid(OwnerCharacter))
		return false;

	// 检查 AI cmd
	if (AiKeyList.Num()>0)
	{
		if(AiKeyList.Contains(ActionCmdId))
		{
			//UE_LOG(LogTemp, Error, TEXT("Septsaber: %s contains Action %s"),*GetOwner()->GetName(), *ActionCmdId)
			return true;
		}
	}
	
	FActionCmd ActionCmd = GetDataManager()->GetActionCmdById(ActionCmdId);
	if (ActionCmd.Action == "")
		return InputState == EAwInputState::None; //肯定不能按下没有的键，所以自然就返回true了，毕竟或关系

	auto pc = UGameplayFuncLib::GetPlayerControllerByComp(this,false);
	//必须这个Action允许发生的状态中包含了当前的游戏操作状态，否则就不发生
	if (!pc|| ActionCmd.AllowState.Contains(pc->GameControlState) == false)
		return false;

	bool CheckKeyPass = false;
	TArray<FString> FoundInputKeys;
	
	for (const FString ActionKey : ActionCmd.ActionKey)
	{
		//以下是之前正常的操作
		if(ActionKey.Contains("AI_")) //不检测 AI ActionKey
			continue;
		
		FDefaultKeyMapping KeyMapping = GetDataManager()->GetKeyMappingById(ActionKey);
		const int64 NowTimeStamp = UDateTimeFuncLib::GetTimestamp();
		
		if (KeyMapping.ActionKey != "")
		{
			bool CheckKeyboardPass = true;
			if (KeyMapping.Keyboard.IsEmpty())
				CheckKeyboardPass &= false;
			// 是否是组合键，如果是组合键，就忽略之前按键标记为删除的标记
			bool bIsCombinationKey = KeyMapping.Keyboard.Num() > 1;
			for (FString Keyboard : KeyMapping.Keyboard)
			{
				if (HasInputKey(Keyboard))
				{
					//不该按这个的按了就不能循环了
					if (InputState == EAwInputState::None)
						return false;

					const int64 ThatTimeStamp = InputKeyList[Keyboard].Get<0>();
					const bool WaitToRemove = InputKeyList[Keyboard].Get<1>();
					
					if (InputState == EAwInputState::Hold && !WaitToRemove && NowTimeStamp - ThatTimeStamp >= HoldingInterval)
						CheckKeyboardPass &= true;
					else if (InputState == EAwInputState::Press &&
							 (bIsCombinationKey || !InputKeyList[Keyboard].Get<3>()) &&
							 NowTimeStamp - ThatTimeStamp < RemoveInterval)
					{
						// FString Str = Keyboard;
						// Str.Append(" - ").Append(FString::FromInt(NowTimeStamp - ThatTimeStamp));
						// UKismetSystemLibrary::PrintString(this, Str);
						
						FoundInputKeys.Add(Keyboard);
						CheckKeyboardPass &= true;
					}
					else
						CheckKeyboardPass &= false;
				}
				else
					CheckKeyboardPass &= false;
			}
			
			bool CheckGamepadPass = true;
			if (KeyMapping.Gamepad.IsEmpty())
				CheckGamepadPass &= false;
			// 是否是组合键，如果是组合键，就忽略之前按键标记为删除的标记
			bIsCombinationKey = KeyMapping.Gamepad.Num() > 1;
			for (FString Gamepad : KeyMapping.Gamepad)
			{
				if (HasInputKey(Gamepad))
				{
					//不该按这个的按了就不能循环了
					if (InputState == EAwInputState::None)
						return false;
					const int64 ThatTimeStamp = InputKeyList[Gamepad].Get<0>();
					const bool WaitToRemove = InputKeyList[Gamepad].Get<1>();
					
					if (InputState == EAwInputState::Hold && !WaitToRemove && NowTimeStamp - ThatTimeStamp >= HoldingInterval)
						CheckGamepadPass &= true;
					else if (InputState == EAwInputState::Press &&
							 (bIsCombinationKey || !InputKeyList[Gamepad].Get<3>()) &&
							 NowTimeStamp - ThatTimeStamp < RemoveInterval)
					{
						FoundInputKeys.Add(Gamepad);
						CheckGamepadPass &= true;
					}
					else
						CheckGamepadPass &= false;
				}
				else
					CheckGamepadPass &= false;
			}
			
			CheckKeyPass |= CheckKeyboardPass || CheckGamepadPass;
		}
	}
	
	if (CheckKeyPass && RemoveInputs)
		for (FString Key : FoundInputKeys)
			if (InputKeyList.Contains(Key))
				InputKeyList[Key].Get<3>() = true;
	
	return CheckKeyPass;
}

bool UCmdComponent::HasInputKey(FString InputKey) const
{
	if (InputKey.IsEmpty())
		return false;

	// Other
    	return InputKeyList.Contains(InputKey);
}

void UCmdComponent::SetAIMoveAndFaceDir(FVector2D AIMoveDir, FVector2D AIFaceDir, int AISpeedLevel)
{
	const int64 Timestamp = UDateTimeFuncLib::GetTimestamp();
	const FAIMoveApplicationRecord AIMove = FAIMoveApplicationRecord(Timestamp, AIMoveDir, AISpeedLevel, AIFaceDir);
	if (AIMoveRec.Num() >= MaxAIAppRec)
	{
		AIMoveRec.RemoveAt(0);
	}
	AIMoveRec.Add(AIMove);
}

void UCmdComponent::CheckSprintInput(float DeltaSeconds)
{
	if (SprintTime >= HoldingInterval/1000.0f)
	{
		// if (IsActionOccur("Sprint", EAwInputState::Press, false))
		// {
		// 	IsSprint = !IsSprint;
		// 	SprintTime = 0;
		// }
	}
	else
		SprintTime += DeltaSeconds;
}

void UCmdComponent::OnPressSprintAction()
{
	// if (SprintTime >= HoldingInterval/1000.0f)
	// {
		IsSprint = !IsSprint;
	// 	SprintTime = 0;
	// }
}
