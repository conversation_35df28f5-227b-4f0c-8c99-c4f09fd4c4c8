#pragma once

#include "CoreMinimal.h"
#include "GameControlState.h"
#include "TheAwakener_FO/FunctionLibrary/DataFuncLib.h"
#include "ActionCmd.generated.h"

USTRUCT(BlueprintType)
struct FCanCustomAction
{
	GENERATED_BODY()

public:
	// 按键描述
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString KeyDesc;

	// 按键 ActionCmd
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString ActionCmd;

	// 按键 ActionKeyMap
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString ActionKeyMap;

	// 手柄是否能改？
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	bool CanChangeInGamepad;
};

/**
 * 
 */
USTRUCT(BlueprintType)
struct FActionCmd
{
	GENERATED_USTRUCT_BODY()
public:
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString Action = "";
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FString> ActionKey;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<EGameControlState> AllowState;
	
	static FActionCmd FromJson(TSharedPtr<FJsonObject> JsonObj)
	{
		FActionCmd Model;
		Model.Action = UDataFuncLib::AwGetStringField(JsonObj, "Action");
		for (const TSharedPtr<FJsonValue> Value : UDataFuncLib::AwGetArrayField(JsonObj, "ActionKey"))
			Model.ActionKey.Add(Value->AsString());
		for (const TSharedPtr<FJsonValue> Value : UDataFuncLib::AwGetArrayField(JsonObj, "State"))
			Model.AllowState.Add(UDataFuncLib::FStringToEnum<EGameControlState>(Value->AsString()));
		return Model;
	}
};

USTRUCT(BlueprintType)
struct FDefaultKeyMapping
{
	GENERATED_USTRUCT_BODY()
public:
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString ActionKey = "";
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FString> Gamepad;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FString> Keyboard;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString Gamepad_Icon;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString Keyboard_Icon;
	
	static FDefaultKeyMapping FromJson(TSharedPtr<FJsonObject> JsonObj)
	{
		FDefaultKeyMapping Model;
		Model.ActionKey = UDataFuncLib::AwGetStringField(JsonObj, "ActionKey");
		for (const TSharedPtr<FJsonValue> Value : UDataFuncLib::AwGetArrayField(JsonObj, "Gamepad"))
			Model.Gamepad.Add(Value->AsString());
		for (const TSharedPtr<FJsonValue> Value : UDataFuncLib::AwGetArrayField(JsonObj, "Keyboard"))
			Model.Keyboard.Add(Value->AsString());
		Model.Gamepad_Icon = UDataFuncLib::AwGetStringField(JsonObj, "GamepadIcon");
		Model.Keyboard_Icon = UDataFuncLib::AwGetStringField(JsonObj, "KeyboardIcon");
		return Model;
	}
};

