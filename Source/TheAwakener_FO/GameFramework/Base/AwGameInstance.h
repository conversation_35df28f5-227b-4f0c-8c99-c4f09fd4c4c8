// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/GameFramework/Manager/AwVFXManager.h"
#include "TheAwakener_FO/GameFramework/Manager/AwSFXManager.h"
#include "TheAwakener_FO/GameFramework/Manager/AwTeamManager.h"
#include "TheAwakener_FO/Gameframework/Timeline/TimelineManager.h"
#include "TheAwakener_FO/GamePlay/DamageVolume/DamageManager.h"
#include "TheAwakener_FO/GamePlay/Trigger/TriggerManager.h"
#include "Engine/GameInstance.h"
#include "TheAwakener_FO/GamePlay/Role/AwRoleInfo.h"
#include "Engine/LevelStreamingDynamic.h"
#include "TheAwakener_FO/GameFramework/Manager/AwLootManager.h"
#include "TheAwakener_FO/GameFramework/Manager/AwUIManager.h"
#include "TheAwakener_FO/GameFramework/Manager/NewbieManager.h"
#include "TheAwakener_FO/GameFramework/Manager/ScoreManager.h"
#include "TheAwakener_FO/GamePlay/Characters/AI/AwAvoidanceManager.h"
#include "TheAwakener_FO/GamePlay/Characters/AvatarCharacter/AvatarCharacter.h"
#include "TheAwakener_FO/GamePlay/Characters/AvatarCharacter/BreakableClone.h"
#include "TheAwakener_FO/GamePlay/FX/BGMManager.h"
#include "TheAwakener_FO/GamePlay/Map/DungeonMap.h"
#include "TheAwakener_FO/GamePlay/Roguelike/Map/RogueMapChallenge.h"
#include "TheAwakener_FO/GamePlay/Setting/RogueGameSetting.h"
#include "TheAwakener_FO/UI/HUD/PopUpText.h"
#include "TheAwakener_FO/UI/HUD/TalkBubble.h"
#include "AwGameInstance.generated.h"

struct FAvatarInPool;
/**
 * 
 */
//Switch改变时的动态多播委托
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FSwitchChangeDelegate,FString,SwitchKey);
DECLARE_DYNAMIC_MULTICAST_DELEGATE(FInputTypeChangeDelegate);
DECLARE_DYNAMIC_MULTICAST_DELEGATE(FLanguageChangeDelegate);
DECLARE_DYNAMIC_MULTICAST_DELEGATE(FChangeToSequenceState);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FDebugShowEnemyHPDelegate,bool,DebugShowEnemyHP);
UCLASS()
class THEAWAKENER_FO_API UAwGameInstance : public UGameInstance
{
	GENERATED_BODY()

	virtual void Init() override;

	virtual void Shutdown() override;
private:
	void OnLoadMapFinished(FString LevelName);
	int IndexWorkingPlayerController = 0;
public:
	static UAwGameInstance* Instance;
	bool bIsInit = false;
	UFUNCTION(BlueprintCallable)
	int GetWorkingLocalPlayerControllerIndex(){return IndexWorkingPlayerController;}
	UFUNCTION(BlueprintCallable)
	void SetWorkingPlayerControllerIndex(const int PlayerIndex){IndexWorkingPlayerController = PlayerIndex;}
	// 当前语言
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	ELanguage Language = ELanguage::English;
	// 当前手边按钮图片类型
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString GamepadButtonType = "PlayStation";
	// 当前手边按钮图片类型
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	bool bIsFirstLaunchGame = false;
	//Switch改变时的动态多播委托
	UPROPERTY(BlueprintAssignable,Category = "Events")
	FSwitchChangeDelegate SwitchChangeDelegate;

	//语言改变时的动态多播委托
	UPROPERTY(BlueprintAssignable,Category = "Events")
	FLanguageChangeDelegate LanguageChangeDelegate;

	//玩法数据版本
	UPROPERTY(BlueprintReadWrite,Category = "Gameplay")
	bool isSurvivor;
	UPROPERTY(BlueprintReadWrite,Category = "Gameplay")
	bool isP2Mode;
	UFUNCTION(BlueprintCallable)
	void AlignPlayerNum(bool IsTitle);
	//Debug用，显示怪物血量
	UPROPERTY(BlueprintAssignable,BlueprintCallable,Category = "Events")
	FDebugShowEnemyHPDelegate DebugShowEnemyHPDelegate;

	//Debug用，显示怪物血量
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	bool DebugShowEnemy = false;
	
	UFUNCTION(BlueprintCallable, Category="AW|Manager")
	void CreateAndInitManagers();
	
	UPROPERTY(BlueprintReadOnly, Category="AW|Manager")
	UAwDataManager* DataManager;

	UPROPERTY(BlueprintReadOnly, Category = "AW|Manager")
	UTimelineManager* TimelineManager;

	UPROPERTY(BlueprintReadOnly, Category = "AW|Manager")
	UBGMManager* BGMManager;

	UPROPERTY(BlueprintReadOnly, Category = "AW|Manager")
	UNewbieManager* NewbieManager;

	UPROPERTY(BlueprintReadOnly, Category = "AW|Manager")
	UAwVFXManager* VFXManager;

	UPROPERTY(BlueprintReadOnly, Category = "AW|Manager")
	UAwSFXManager* SFXManager;

	UPROPERTY(BlueprintReadOnly, Category = "AW|Manager")
	UDamageManager* DamageManager;

	UPROPERTY(BlueprintReadOnly, Category="AW|Manager")
	UAwUIManager* UIManager;

	UPROPERTY(BlueprintReadOnly, Category="AW|Manager")
	UScoreManager* ScoreManager;
	
	UPROPERTY(BlueprintReadOnly, Category = "AW|Manager")
	UTriggerManager* TriggerManager;

	UPROPERTY(BlueprintReadOnly, Category = "AW|Manager")
	UAwTeamManager* TeamManager;

	UPROPERTY(BlueprintReadOnly, Category = "AW|Manager")
	UAwLootManager* LootManager;

	UPROPERTY(BlueprintReadOnly, Category = "AW|Manager")
	UAwAvoidanceManager* AvoidanceManager;
	
	UPROPERTY(BlueprintReadOnly, Category = "AW|Switch")
	TMap<FString,int> SwitchList;
	//联机时，所有玩家的CharacterInfo
	UPROPERTY(BlueprintReadWrite, Category = "AW|Info")
	TMap<AAwPlayerController*, FAwCharacterInfo> CharacterInfoList;
	//本机玩家的CharacterInfo
	UPROPERTY(BlueprintReadWrite, Category = "AW|Info")
	FAwCharacterInfo SelfCharacterInfo;
	//当前地图信息
	UPROPERTY(BlueprintReadWrite, Category = "AW|Info")
	FAwMapInfo CurMapInfo;

	UPROPERTY(BlueprintReadWrite)
	bool bIsGamepad = true;
	UPROPERTY(BlueprintAssignable,Category = "Events")
	FInputTypeChangeDelegate InputTypeChangeDelegate;

	UPROPERTY(BlueprintAssignable,Category = "Events")
	FChangeToSequenceState ChangeToSequenceStateDelegate;
	
	UPROPERTY()
	AAwPlayerController* HostPlayerController;

	UPROPERTY(BlueprintReadWrite)
	FString CurLevelName;

	UPROPERTY(BlueprintReadWrite)
	FString LastLevelName;

	UPROPERTY(BlueprintReadWrite)
	int NeedLoadCurMainLevelNum = 1;
	UPROPERTY(BlueprintReadWrite)
	int LoadedCurMainLevelNum = 0;
	UPROPERTY(BlueprintReadWrite)
	bool LoadCurMainLevelFinish = false;

	UPROPERTY(BlueprintReadWrite)
	int NeedLoadCurBlockLevelNum = 1;
	UPROPERTY(BlueprintReadWrite)
	int LoadedCurBlockLevelNum = 0;
	UPROPERTY(BlueprintReadWrite)
	bool LoadCurBlockLevelFinish = false;

	UPROPERTY(BlueprintReadWrite)
	int NeedLoadCurScriptLevelNum = 1;
	UPROPERTY(BlueprintReadWrite)
	int LoadedCurScriptLevelNum = 0;
	UPROPERTY(BlueprintReadWrite)
	bool LoadCurScriptLevelFinish = false;

	UPROPERTY(BlueprintReadWrite)
	int NeedLoadCurSequencerLevelNum = 1;
	UPROPERTY(BlueprintReadWrite)
	int LoadedCurSequencerLevelNum = 0;
	UPROPERTY(BlueprintReadWrite)
	bool LoadCurSequencerLevelFinish = false;
	
	UPROPERTY(BlueprintReadWrite)
	bool bHaveSave = false;

	UPROPERTY(BlueprintReadWrite)
	int CurRoleIndex = 0;

	UPROPERTY(BlueprintReadWrite)
	FAwRoleInfo RoleInfo; //TODO: 初始化

	UPROPERTY(BlueprintReadWrite)
	bool bNetworked;

	UPROPERTY(BlueprintReadWrite)
	FAwSessionInfo CurSeesionInfo;

	UPROPERTY(BlueprintReadWrite, Category = "AW|Dungeon")
		FString CurDungeonName = "TestDungeon";

	UPROPERTY(BlueprintReadWrite, Category = "AW|Dungeon")
	int LoadFinishDungeonLevelNum = 0;
	
	UPROPERTY(BlueprintReadWrite, Category = "AW|Dungeon")
		TMap<FString, ULevelStreamingDynamic*> DungeonRoomLevelList;
	UPROPERTY(BlueprintReadWrite, Category = "AW|Dungeon")
		TMap<FString, ULevelStreamingDynamic*> DungeonRoomBlockLevelList;
	UPROPERTY(BlueprintReadWrite, Category = "AW|Dungeon")
	TMap<FString, ULevelStreamingDynamic*> DungeonRoomScriptLevelList;

	UPROPERTY(BlueprintReadWrite, Category = "AW|Dungeon")
		int CurClientLoadDungeonFinishNum = 0;

	UPROPERTY(BlueprintReadWrite, Category = "AW|Level")
	int NextLevelPlayerStartIndex = 0;

	// UPROPERTY(BlueprintReadWrite, Category = "AW|Level")
	// AActor* RespawnPoint = nullptr;

	UPROPERTY(BlueprintReadWrite, Category = "AW|Level")
	int RespawnPointIndex;

	UPROPERTY(BlueprintReadWrite, Category = "Rogue")
	int Rogue_RoomStep = 0;

	//通关后的难度挑战
	UPROPERTY(BlueprintReadWrite, Category = "Rogue")
	TMap<ERogueChallengeType, int> Rogue_ChallengeList;
	
	UFUNCTION(BlueprintCallable)
	void Tick(float DeltaTime);

	//Session
	UFUNCTION(BlueprintImplementableEvent, Category = "AW|Session")
	void ChangeSessionInfo(FAwSessionInfo SessionInfo);

	UFUNCTION(BlueprintImplementableEvent, Category = "AW|Session")
	void LeaveOnline();

	//Level
	UFUNCTION(BlueprintCallable, Category = "Level")
	void ChangeLevelByLevelName(FString LevelName, bool bAbsolute, FString Options);

	UFUNCTION(BlueprintCallable, Category = "Level")
	void ChangeLevelByReference(const TSoftObjectPtr<UWorld> Level, bool bAbsolute, FString Options);

	UFUNCTION(BlueprintCallable, Category = "Level")
	void AW_ServerTravel(const FString& InURL, bool bAbsolute = false, bool bShouldSkipGameNotify = false);

	UFUNCTION(BlueprintCallable, Category = "Level")
	void AllCharacterReady();

	UFUNCTION(BlueprintCallable, Category = "Level")
	void SetNeedLoadLevelNum(int NeedLoadMainNum, int NeedLoadBlockNum, int NeedLoadScriptNum, int NeedLoadSeqNum)
	{
		NeedLoadCurMainLevelNum = NeedLoadMainNum;
		if(NeedLoadMainNum <= 0)
			LoadCurMainLevelFinish = true;
		NeedLoadCurBlockLevelNum = NeedLoadBlockNum;
		if(NeedLoadBlockNum <= 0)
			LoadCurBlockLevelFinish = true;
		NeedLoadCurScriptLevelNum = NeedLoadScriptNum;
		if(NeedLoadScriptNum <= 0)
			LoadCurScriptLevelFinish = true;
		NeedLoadCurSequencerLevelNum = NeedLoadSeqNum;
		if(NeedLoadSeqNum <= 0)
			LoadCurSequencerLevelFinish = true;
	};
	
	//非副本Level在LoadFinish时调用
	UFUNCTION(BlueprintCallable, Category = "Level")
	void LoadLevelFinish(EAwLevelType LevelType);

	//Dungeon
	void LoadAllDungeonLevel();

	UFUNCTION(BlueprintCallable, Category = "AW|Dungeon")
	void UnLoadAllDungeonLevel();

	UFUNCTION(BlueprintNativeEvent, Category = "AW|Dungeon")
	void LoadDungeonRoomLevel(FDungeonTile CurRoomInfo, const FString& LevelName);

	UFUNCTION(BlueprintCallable, Category = "AW|Dungeon")
	AActor* SpawnDungeonRoad(FDungeonRoadInfo CurRoadInfo);

	UFUNCTION(BlueprintCallable, Category = "AW|Dungeon")
	void SpawnDungeonDoors(FDungeonTile CurRoomInfo, const FString LevelName);

	UFUNCTION(BlueprintCallable, Category = "AW|Dungeon")
	void ClearDungeonLevelInfo();

	//副本Level在LoadFinish时调用
	UFUNCTION(BlueprintCallable, Category = "AW|Dungeon")
	void LoadDungeonLevelFinish(FString DungeonLevelName);

	UFUNCTION(BlueprintCallable)
	void StartDungeonSwitch();
	
	//Character Clones
	//角色克隆体池子<角色，克隆信息>
	UPROPERTY()
	TMap<AAwCharacter*, FAvatarInPool> CharacterClones;
	//角色的带攻击性质的克隆体池子<角色，克隆>
	UPROPERTY()
	TMap<AAwCharacter*, FBreakableCloneInPool> CharacterBreakableClones;

	/**
	 * 创建一个克隆体
	 * @param CloneFrom 克隆的是谁
	 * @param StayTime 克隆存在多久，小于等于0后果自负
	 * @param UseTransparent 是否用透明沟边的材质
	 * @param TransColor 如果使用透明勾边材质，则材质颜色是什么
	 */
	UFUNCTION(BlueprintCallable, Category="Clone")
	AAvatarCharacter* MakeCharacterClone(AAwCharacter* CloneFrom, float StayTime, bool UseTransparent = false, FLinearColor TransColor = FLinearColor::Blue);

	/**
	 * 创建一个可以被破坏的克隆体
	 * @param CloneFrom 克隆的是谁
	 * @param Offset 创建出来之后偏移多少坐标，X前后，Y左右，Z上下
	 * @param StayTime 克隆存在多久，小于等于0后果自负
	 * @param DoOffense 克隆体是否会攻击
	 * @param OffenseInfo 克隆体的攻击信息
	 * @param DefenseInfo 克隆体的防御信息
	 * @param ChaResource 克隆体的血量信息
	 * @param UseTransparent 是否用透明沟边的材质
	 * @param TransColor 如果使用透明勾边材质，则材质颜色是什么
	 */
	UFUNCTION(BlueprintCallable, Category="Clone")
	ABreakableClone* MakeBreakableClone(
		AAwCharacter* CloneFrom, FVector Offset, float StayTime,
		bool DoOffense, FOffenseInfo OffenseInfo, FDefenseInfo DefenseInfo, FChaResource ChaResource,
		bool UseTransparent = false, FLinearColor TransColor = FLinearColor::Blue);

	//跳数字
	UPROPERTY()
	TArray<FPopTextRecorder> PopUpTexts;
	UFUNCTION(BlueprintCallable, Category="PopText")
	APopUpText* PopText(FVector Position, FString Text, int Priority = 0,  int TextSize = 80, FLinearColor TextColor = FLinearColor::Red);

	//角色头顶泡泡
	UPROPERTY()
	TArray<ATalkBubble*> TalkingBubbles;
	UFUNCTION()
	ATalkBubble* ShowTalkBubble(AAwCharacter* OnCha, FString ChaName, FString Text, float InSec);

	//对副本的势力值进行修改
	UFUNCTION(BlueprintCallable, Category="DungeonCamp")
	void CampProgressModify(FString DungeonId, FString CampId, int Value);
	//对当前所在的副本的势力值进行修改
	UFUNCTION(BlueprintCallable, Category="DungeonCamp")
	void CurDungeonCampProgressModify(FString CampId, int Value);

	UFUNCTION(BlueprintImplementableEvent, BlueprintCallable)
	void BackToVillage();

	UFUNCTION(BlueprintImplementableEvent, BlueprintCallable)
	void SetMainSoundVolume(float Value);

	UFUNCTION(BlueprintImplementableEvent, BlueprintCallable)
	void SetBGMVolume(float Value);

	UFUNCTION(BlueprintImplementableEvent, BlueprintCallable)
	void SetSFXVolume(float Value);

	UFUNCTION(BlueprintImplementableEvent,BlueprintCallable)
	void SetVoiceVolume(float Value);

	UFUNCTION(BlueprintImplementableEvent,BlueprintCallable)
	void SetUISoundVolume(float Value);

	UFUNCTION(BlueprintCallable)
	int GetRoleSwitch(FString SwitchKey);

	UFUNCTION(BlueprintCallable)
	void SetupPathNodeQueueInfo(FString LevelName);

	UFUNCTION(BlueprintCallable)
	void SetLanguage(ELanguage ToLanguage);

public:
	//Cmd
	UFUNCTION(Exec)
	static  void GiveRogueCurrency(FString Id,int Num);
};