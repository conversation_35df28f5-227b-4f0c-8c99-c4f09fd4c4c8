// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/PlayerState.h"
#include "TheAwakener_FO/GamePlay/Characters/Skill/AwSkill.h"
#include "AwPlayerState.generated.h"

/**
 * 
 */
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FActionSkillChangeDelegate,FAwActionSkillInfo,ActionSkillInfo);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FActionSkillArrayChangeDelegate,TArray<FAwActionSkillInfo>,NewSkillInfoArray);
DECLARE_DYNAMIC_MULTICAST_DELEGATE(FActionSkillDelegate);

UCLASS()
class THEAWAKENER_FO_API AAwPlayerState : public APlayerState
{
	GENERATED_BODY()
public:
	UFUNCTION(BlueprintCallable)
	virtual void BeginPlay() override;
	
	UFUNCTION()
	void SetupAfterPossess();
	//----------觉醒技能----------
	FAwActionSkillInfo* CurAwakeSkill = nullptr;
	//觉醒技能列表
	UPROPERTY(BlueprintReadWrite,EditAnywhere,Category= "AwakeSkill")
	TMap<FString,FAwActionSkillInfo> AllAwakeSkills;
	//解锁列表
	UPROPERTY(BlueprintReadWrite,EditAnywhere,Category= "AwakeSkill")
	TArray<FString> UnLockAwakeSkills;
	
	UPROPERTY(BlueprintAssignable,Category= "AwakeSkill")
	FActionSkillChangeDelegate OnAwakeSkillChange;
	UPROPERTY(BlueprintAssignable,Category= "AwakeSkill")
	FActionSkillArrayChangeDelegate OnAwakeSkillUnlock;
	UPROPERTY(BlueprintAssignable,Category= "AwakeSkill")
	FActionSkillDelegate ClearAwakeSkillCreature;
	//获取当前觉醒技能
	FAwActionSkillInfo* GetCurAwakeSkill()const 	{return CurAwakeSkill;}
	UFUNCTION(BlueprintCallable,Category= "AwakeSkill")
	FAwActionSkillInfo K2_GetCurAwakeSkill();
	//是否还有觉醒技能未解锁
	UFUNCTION(BlueprintCallable,Category= "AwakeSkill")
	bool HasLockAwakeSkill();
	//是否觉醒技能满足最小cost
	UFUNCTION(BlueprintPure,Category= "AwakeSkill")
	bool CheckAwakeSkillCostEnough();
	//设置当前觉醒技能
	UFUNCTION(BlueprintCallable,Category= "AwakeSkill")
	void SetCurAwakeSkill(FString NewSkillId);

	//尝试解锁新的觉醒技能
	UFUNCTION(BlueprintCallable,Category= "AwakeSkill")
	bool TryUnlockPlayerAwakeSkill(FString SkillId);
	
	//解锁新的觉醒技能
	UFUNCTION(BlueprintCallable,Category= "AwakeSkill")
	void UnlockPlayerAwakeSkill(FString SkillId);
	//激活当前觉醒技能
	UFUNCTION(BlueprintCallable,Category= "AwakeSkill")
	void ActivePlayerCurAwakeSkill(bool bOpen);

	//扣除当前觉醒技能最小起始消耗
	UFUNCTION(BlueprintCallable,Category= "AwakeSkill")
	void ReduceCurAwakeSkillMinCost();
	
	//清空当前觉醒技能
	UFUNCTION(BlueprintCallable,Category= "AwakeSkill")
	void ClearPlayerCurAwakeSkill();

	UFUNCTION(BlueprintCallable,Category= "AwakeSkill")
	void LoadAwakeSkillSaveData();
	
	bool AfterLoadSaveData = false;
	//----------觉醒技能结束----------
	void CusPrint();
};
