// Fill out your copyright notice in the Description page of Project Settings.


#include "AwGameState.h"

#include "RenderCore.h"
#include "Components/SkeletalMeshComponent.h"
#include "TheAwakener_FO/Gameframework/Base/AwGameModeBase.h"
#include "Kismet/GameplayStatics.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/FunctionLibrary/MathFuncLib.h"
#include "Net/UnrealNetwork.h"

void AAwGameState::BeginPlay()
{
	Super::BeginPlay();
	AAwGameModeBase* CurGameMode = Cast<AAwGameModeBase>(UGameplayStatics::GetGameMode(GWorld));
	if (CurGameMode)
		CurGameMode->SyncDataToGameState(this);
	this->CharacterCamps = UGameplayFuncLib::GetAwDataManager()->AllDefaultCamps();
}

void AAwGameState::AddPlayerCharacters(AAwCharacter* AwCharacter)
{
	PlayerCharacters.Add(AwCharacter);
	CleanInvalidPlayerCharacter();
}

void AAwGameState::GetLifetimeReplicatedProps(TArray< FLifetimeProperty >& OutLifetimeProps) const
{
	Super::GetLifetimeReplicatedProps(OutLifetimeProps);
}

void AAwGameState::RoundTimeReset()
{
	RoundTime = 0;
}

void AAwGameState::RoundTimeCount(float Deltatime)
{
	if(!GetWorld()->IsPaused() && IsTimeCountStart)
	{
		RoundTime += Deltatime * RoundTimeSpeedMulti;
	}
}

void AAwGameState::SetRoundSpeedMultiplyer(float Multy)
{
	RoundTimeSpeedMulti = Multy;
}

void AAwGameState::ClearAll()
{
	PlayerCharacters.Empty();
	AllCharacters.Empty();
	AOEList.Empty();
	BulletList.Empty();
	MapPointList.Empty();
	AwMapPathNodes.Empty();
	
	for (const FDurationParticleComp DurationParticleComp : DurationParticleCompList)
		DurationParticleComp.ThisParticle->DestroyComponent();
	DurationParticleCompList.Empty();
}

float AAwGameState::GetPlayTime()
{
	// return UGameplayStatics::GetAudioTimeSeconds(this) - RoundStartTime;
	return RoundTime;
}

void AAwGameState::StartPlayTimeCount()
{
	if (!IsTimeCountStart)
	{
		IsTimeCountStart = true;
	}
	else
	{
		UE_LOG(LogTemp,Log,TEXT("StartPlayTimeCount failed!"));
	}
}

void AAwGameState::GetAllMapPointInCurMap(FString LevelName, FTransform LevelTrans, FString LevelPath)
{
	FString ParamsStr;
	FString CurPath;
	if (!LevelPath.Split(".", &CurPath, &ParamsStr))
	{
		CurPath = LevelPath;
	}

	TArray<FPresetMapPoint> PointList = UGameplayFuncLib::GetAwDataManager()->GetMapInfoByLevelPath(CurPath).Points;
	for (const auto& CurPoint : PointList)
	{
		MapPointList.Add(FMapSetPoint(CurPoint.Id, LevelPath, CurPoint.Offset, LevelName, LevelTrans));
	}
}

TArray<FMapSetPoint> AAwGameState::GetMapPointsByRoomId(FString RoomId, FString PointId)
{
	TArray<FMapSetPoint> PointList;
	for (FMapSetPoint& CurPoint : MapPointList)
	{
		if (CurPoint.RoomId == RoomId && CurPoint.Id.Contains(PointId))
			PointList.Add(CurPoint);
	}
	return PointList;
}

AAwCharacter* AAwGameState::FindAwCharacter(AAwCharacter* Character)
{
	AAwCharacter* Res = nullptr;
	for (const auto EachCharacter : AllCharacters)
	{
		if (EachCharacter.Key == Character)
			Res = EachCharacter.Key;
	}
	return Res;
}

AAwCharacter* AAwGameState::GetNearlyCharacter(FString LevelName, FVector Center, float MinDis)
{
	AAwCharacter* Res = nullptr;
	float MinDisSqr = MinDis * MinDis + 1;
	for (auto Character : AllCharacters)
	{
		if (Character.Value == LevelName)
		{
			const float TempDisSqr = FVector::DistSquared(Center, Character.Key->GetActorLocation());
			if (TempDisSqr <= MinDis * MinDis && TempDisSqr <= MinDisSqr)
			{
				Res = Character.Key;
				MinDisSqr = TempDisSqr;
			}
		}
	}

	return Res;
}

TArray<AAwCharacter*> AAwGameState::GetCanPickUpCharacters(AThingPackageActor* ThingPackageActor)
{
	TArray<AAwCharacter*> Res;
	for (TTuple<AAwCharacter*, FString> Cha : AllCharacters)
	{
		if (IsValid(Cha.Key) && Cha.Key->IsPlayerCharacter() &&
			CanPickUpLoot(Cha.Key->GetActorLocation() - FVector(0, 0, Cha.Key->GetCapsuleComponent()->GetScaledCapsuleHalfHeight() + 1),
				Cha.Key->GetActorForwardVector(), Cha.Key->PickUpInfo, ThingPackageActor))
			Res.Add(Cha.Key);
	}
	return Res;
}

bool AAwGameState::CanPickUpLoot(FVector Center, FVector FaceTo, FPickUpInfo PickUpInfo, AThingPackageActor* ThingPackageActor)
{
	return UMathFuncLib::IsInsideSector(Center,
		FVector2D(FaceTo),
		200, 120, 0,
		PickUpInfo.PickUpDistance,
		ThingPackageActor->GetActorLocation());
}

TArray<AAwCharacter*> AAwGameState::GetCharactersByTag(FString Tag)
{
	TArray<AAwCharacter*> Res;
	if(!Tag.IsEmpty())
	{
		for (TTuple<AAwCharacter*, FString> Character : AllCharacters)
		{
			if(Character.Key && Character.Value == Tag)
				Res.Add(Character.Key);
		}
	}
	return Res;
}

TArray<AActor*> AAwGameState::GetImportantActorByName(FString Name)
{
	TArray<AActor*> Res;
	for (TTuple<FString, AActor*> IActor : this->ImportantActors)
	{
		if (IActor.Key.Contains(Name) && IActor.Value)
		{
			Res.Add(IActor.Value);
		}
	}
	return Res;
}

bool AAwGameState::IsEnemy(AAwCharacter* Me, AAwCharacter* Target)
{
	if (!Me  || !Target) return false;	//有一方不存在就不可能是敌对了
	if (Me == Target || Me->Side == Target->Side) return false;	//自己，或者Side完全相同的人之间，不会互殴

	const FString TargetNpcId = Target->IsPlayerCharacter() ? FNpcInfo::PlayerNpcId() : (Target->NpcInfo.IsNpc() ? Target->NpcInfo.Id : FString());
	
	if (Me->NpcInfo.IsNpc() && TargetNpcId.IsEmpty() == false && Me->NpcInfo.Personality.IsRacist == false)
	{
		if (Me->NpcInfo.Personality.Enemy.Contains(TargetNpcId))
			return true;	//恨死他了，并且不是种族主义者
		if (Me->NpcInfo.Personality.Friend.Contains(TargetNpcId))
			return false;	//不是种族主义者，所以我的朋友就是朋友
	}
	
	FCharacterCamp MeCamp;
	FCharacterCamp TarCamp;
	for (FCharacterCamp CharacterCamp : this->CharacterCamps)
	{
		if (CharacterCamp.Sides.Contains(Me->Side)) MeCamp = CharacterCamp;
		if (CharacterCamp.Sides.Contains(Target->Side)) TarCamp = CharacterCamp;
	}
	if (MeCamp.CampId == TarCamp.CampId) return false;	//同一个阵营不会是敌人
	return (MeCamp.CanAttackCampId.Contains(TarCamp.CampId)); 
}

AAwCharacter* AAwGameState::GetBoss()
{
	for (const TTuple<AAwCharacter*, FString> Character : AllCharacters)
		if (IsValid(Character.Key) && !Character.Key->MobClassId.IsEmpty() &&
			Character.Key->CharacterObj.MobRank == EMobRank::Boss &&
			!Character.Key->Dead(true) && Character.Key->GotReady)
			return Character.Key;
	return nullptr;
}

TArray<AAwCharacter*> AAwGameState::GetElites()
{
	TArray<AAwCharacter*> Res;
	for (const TTuple<AAwCharacter*, FString> Character : AllCharacters)
		if (IsValid(Character.Key) && !Character.Key->MobClassId.IsEmpty() &&
			Character.Key->CharacterObj.MobRank == EMobRank::Elite &&
			!Character.Key->Dead(true) && Character.Key->GotReady)
				Res.Add(Character.Key);
	return Res;
}

TArray<AAwCharacter*> AAwGameState::GetNormalEnemy()
{
	TArray<AAwCharacter*> Res;
	for (const TTuple<AAwCharacter*, FString> Character : AllCharacters)
		if (IsValid(Character.Key) && !Character.Key->MobClassId.IsEmpty() &&
			Character.Key->CharacterObj.MobRank == EMobRank::Normal &&
			!Character.Key->Dead(true) && Character.Key->GotReady)
				Res.Add(Character.Key);
	return Res;
}

TArray<AAwCharacter*> AAwGameState::GetAllEnemy(bool CheckRenderInView, float RenderTolerance)
{
	TArray<AAwCharacter*> Res;
	for (const TTuple<AAwCharacter*, FString> Character : AllCharacters)
		if (IsValid(Character.Key) && !Character.Key->MobClassId.IsEmpty() &&
			!Character.Key->Dead(true) && Character.Key->GotReady)
		{
			//注 模型渲染判断包括了 模型的阴影渲染的计算区域 所以不等同于直觉中的渲染区域
			if (Character.Key->GetMesh()&&CheckRenderInView)
			{
				if (!Character.Key->GetMesh()->CastShadow)
				{
					if (Character.Key->GetMesh()->WasRecentlyRendered(RenderTolerance))
					{
						Res.Add(Character.Key);
					}
				}
				else
				{
					FHitResult TraceResult;
					for (auto pc : UGameplayFuncLib::GetAllLocalAwPlayerControllers())
					{
						if (!pc)continue;
						FVector Start = pc->PlayerCameraManager->GetCameraLocation();
						FVector End = Character.Key->GetActorLocation();
						TArray<AActor*> Ignores;
						Ignores.Add(Character.Key);
						Ignores.Add(pc->CurCharacter);
						UKismetSystemLibrary::LineTraceSingle(this,Start,End,ETraceTypeQuery::TraceTypeQuery1,
							false,Ignores,EDrawDebugTrace::None,TraceResult,true);
						if (!TraceResult.bBlockingHit)
						{
							Res.Add(Character.Key);
						}
					}
				}
			}
		}
	return Res;
	
}
