// The copyright of this project belongs to Shanghai Minghuan Software Technology Co., Ltd

#pragma once

#include "CoreMinimal.h"
#include "Engine/GameViewportClient.h"
#include "AwGameViewportClient.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UAwGameViewportClient : public UGameViewportClient
{
	GENERATED_BODY()
public:
    bool bDisableSwapGamepadDevice;
	virtual void RemapControllerInput(FInputKeyEventArgs& InOutKeyEvent) override;
};
