// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/GameState.h"
#include "TheAwakener_FO/FunctionLibrary/MathFuncLib.h"
#include "TheAwakener_FO/GamePlay/AnimNotifyState/BeamEffect.h"
#include "TheAwakener_FO/GamePlay/Map/DungeonMap.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "TheAwakener_FO/GamePlay/AOE/AWAoeBase.h"
#include "TheAwakener_FO/GamePlay/Bullet/AWBullet.h"
#include "TheAwakener_FO/GamePlay/Camera/AwUICamera.h"
#include "TheAwakener_FO/GamePlay/Characters/AvatarCharacter/AvatarCharacter.h"
#include "TheAwakener_FO/GamePlay/Dialog/DialogModeActor.h"
#include "TheAwakener_FO/GamePlay/Map/AwMapScenePosition.h"
#include "TheAwakener_FO/GamePlay/Map/MapSetPoint.h"
#include "TheAwakener_FO/GamePlay/Relationship/CharacterCamp.h"
#include "TheAwakener_FO/GamePlay/Thing/ThingPackageActor.h"
#include "TheAwakener_FO/GamePlay/Trigger/AwSceneItem.h"
#include "AwGameState.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API AAwGameState : public AGameState
{
	GENERATED_BODY()
public:
	virtual	void BeginPlay() override;
	UPROPERTY(EditAnywhere,BlueprintReadWrite,Category="Gameplay")
	bool bIsLevelClear;
	void AddPlayerCharacters(AAwCharacter* AwCharacter);
	UPROPERTY(EditAnywhere,BlueprintReadWrite,Category="Gameplay")
	float RoundStartTime;
	UPROPERTY(EditAnywhere,BlueprintReadOnly,Category="Gameplay")
	float RoundTime;
	UPROPERTY(EditAnywhere,BlueprintReadOnly,Category="Gameplay")
	float RoundTimeSpeedMulti = 1;
	UPROPERTY(EditAnywhere,BlueprintReadWrite,Category="Gameplay")
	bool IsTimeCountStart;
	UPROPERTY(EditAnywhere,BlueprintReadWrite,Category="Gameplay")
	int CurMonsterNumber;	
	void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;
	
	UFUNCTION(BlueprintCallable)
		void RoundTimeReset();
	UFUNCTION(BlueprintCallable)
		void RoundTimeCount(float Deltatime);
	UFUNCTION(BlueprintCallable)
		void SetRoundSpeedMultiplyer(float Multy);
	UFUNCTION(BlueprintCallable)
		void ClearAll();
	UFUNCTION(BlueprintCallable,BlueprintPure, Category = "Gameplay")
		float GetPlayTime();
	UFUNCTION(BlueprintCallable, Category = "Gameplay")
		void StartPlayTimeCount();
	UFUNCTION(BlueprintCallable)
		void DestroyCharacter(AAwCharacter* AwCharacter)
	{
		if (AllCharacters.Contains(AwCharacter))
			AllCharacters.Remove(AwCharacter);
		if (PlayerCharacters.Contains(AwCharacter))
			PlayerCharacters.Remove(AwCharacter);
		CleanInvalidPlayerCharacter();
		AwCharacter->Destroy();
	};
	UFUNCTION()
	void CleanInvalidPlayerCharacter()
	{
		PlayerCharacters.RemoveAll([](ACharacter* Char)
		{
		    return !IsValid(Char);
		});
	}

	UFUNCTION(BlueprintCallable)
		void GetAllMapPointInCurMap(FString LevelName, FTransform LevelTrans, FString LevelPath);

	UFUNCTION(BlueprintCallable)
		TArray<FMapSetPoint> GetMapPointsByRoomId(FString RoomId, FString PointId);

	UFUNCTION(BlueprintCallable)
		AAwCharacter* FindAwCharacter(AAwCharacter* Character);

	UFUNCTION(BlueprintCallable)
		AAwCharacter* GetNearlyCharacter(FString LevelName, FVector Center, float MinDis);

	UFUNCTION(BlueprintCallable)
		TArray<AAwCharacter*> GetCanPickUpCharacters(AThingPackageActor* ThingPackageActor);

	UFUNCTION(BlueprintCallable)
		static bool CanPickUpLoot(FVector Center, FVector FaceTo, FPickUpInfo PickUpInfo, AThingPackageActor* ThingPackageActor);


	//我操作的角色
	UFUNCTION(BlueprintCallable,BlueprintPure)
	AAwCharacter* GetMyCharacter()
	{
		int playerIndex = UAwGameInstance::Instance->GetWorkingLocalPlayerControllerIndex();
		if(UAwGameInstance::Instance)
		{
			// UE_LOG(LogTemp,Log,TEXT("Local players count: %d"),UAwGameInstance::Instance->GetNumLocalPlayers())
			auto players = UAwGameInstance::Instance->GetLocalPlayers();
			if (players.Num()>playerIndex)
			{
				auto pc = Cast<AAwPlayerController>(UAwGameInstance::Instance->GetLocalPlayerByIndex(playerIndex)->GetPlayerController(UAwGameInstance::Instance->GetWorld()));
				if (pc)
					return pc->CurCharacter;
				else
					return nullptr;
			}
		}
		return nullptr;
	}
	

	//玩家最后一次瞄准的角色
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		AAwCharacter* AimCharacter = nullptr;

	//玩家最后一次瞄准的坐标
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		FVector AimLocation = FVector::ZeroVector;

	//UI用的角色（我的角色克隆出来的）
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		AAvatarCharacter* MyUIClone = nullptr;

	//UI的角色站的那个点
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		AActor* UICloneStandPoint = nullptr;

	//拍摄UI的Camera
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		AAwUICamera* UICamera = nullptr;

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		TMap<AAwCharacter*, FString> AllCharacters;

	//根据GameState.AllCharacter.Value查找对应的Character
	UFUNCTION(BlueprintCallable)
		TArray<AAwCharacter*> GetCharactersByTag(FString Tag);

	//这一帧死亡的Character,每帧都会清空
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		TMap<AAwCharacter*, FString> ThisTickDeathCharacters;

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		TMap<AAWAoe*, FString> AOEList;

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		TMap<AAwBullet*, FString> BulletList;

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		TArray<FMapSetPoint> MapPointList;

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		ADialogModeActor* DialogModeActor = nullptr;

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		TMap<FString, FNPCSpawnInfo> NPCSpawnInfoList;

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		TMap<FString, ATriggerCatcher*> TriggerCatcherList;

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		TMap<FString, AAwSceneItem*> SceneItemList;

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		TMap<FString, AAwSceneItem*> ThisTickDestroySceneItemList;

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FDurationParticleComp> DurationParticleCompList;
	
	//当前地图中所有PathNode
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		TMap<FString, FAwMapPathNode> AwMapPathNodes;

	//当前地图中所有PathNodeQueue
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TMap<FString, FPathNodeQueue> AwMapPathNodeQueues;

	//当前场景中比较重要的Actor们<Actor.GetName(), AActor*>
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TMap<FString, AActor*> ImportantActors;

	//因为UE会在名字后面加个数字，所以…………这样一来同名的也会有多个
	TArray<AActor*> GetImportantActorByName(FString Name);
	
	//---Dungeon---//

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dungeon")
	TArray<FDungeonTile> DungeonRoomsInfo;
	
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dungeon")
	TArray<FDungeonRoadInfo> DungeonRoadsInfo;
	
	//---Dungeon---//

	//只在该地图使用并且不会保存进RoleInfo的Switch
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		TMap<FString, int> SwitchList;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Relationship")
	TArray<FCharacterCamp> CharacterCamps;

	UFUNCTION(BlueprintCallable, Category= "Relationship")
	bool IsEnemy(AAwCharacter* Me, AAwCharacter* Target);

	UFUNCTION(BlueprintCallable)
	AAwCharacter* GetBoss();

	UFUNCTION(BlueprintCallable)
	TArray<AAwCharacter*> GetElites();
	
	UFUNCTION(BlueprintCallable)
	TArray<AAwCharacter*> GetNormalEnemy();

	UFUNCTION(BlueprintCallable)
	TArray<AAwCharacter*> GetAllEnemy(bool CheckRenderInView = false,float RenderTolerance = 0.1);

	UFUNCTION(BlueprintCallable)
	TArray<AAwCharacter*> GetPlayerCharacters()
	{
		TArray<AAwCharacter*> Characters;
		for (auto Character : PlayerCharacters)
		{
			if (IsValid(Character))
				Characters.AddUnique(Character);
		}
		return Characters;
	}
private:
	TArray<AAwCharacter*> PlayerCharacters;
};
