// Fill out your copyright notice in the Description page of Project Settings.


#include "AwGameInstance.h"
#include "Kismet/GameplayStatics.h"
#include "AwGameState.h"
#include "Kismet/KismetMathLibrary.h"
#include "TheAwakener_FO/FunctionLibrary/ResourceFuncLib.h"
#include "TheAwakener_FO/GameFramework/Manager/AwDataManager.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/FunctionLibrary/DateTimeFuncLib.h"
#include "TheAwakener_FO/GameFramework/Manager/AwRogueDataSystem.h"
#include "TheAwakener_FO/GamePlay/GameMode/AwGameMode_RandomDungeon.h"
#include "TheAwakener_FO/GamePlay/GameSave/AwakerSaveData.h"
#include "TheAwakener_FO/GamePlay/Map/MapSwitchManager.h"
#include "TheAwakener_FO/GamePlay/Quest/AwQuestManager.h"
#include "TheAwakener_FO/GamePlay/Roguelike/BattleStyle/RogueBattleStyleSubSystem.h"
#include "TheAwakener_FO/GamePlay/Roguelike/BattleUpgrade/RogueBattleUpgradeSubSystem.h"
#include "TheAwakener_FO/GamePlay/Setting/RogueGameSetting.h"


UAwGameInstance* UAwGameInstance::Instance = nullptr;

void UAwGameInstance::Init()
{
	Super::Init();
	
	UAwGameInstance::Instance = this;

/*
#if UE_BUILD_SHIPPING||UE_BUILD_DEBUG
	IOnlineSubsystem* OnlineSub = IOnlineSubsystem::Get();
	if (OnlineSub)
	{
		FOnlineSubsystemSteam* SteamSysteam =  static_cast<FOnlineSubsystemSteam*>(OnlineSub);
		if (!SteamSysteam)
		{
			UE_LOG(LogTemp, Error, TEXT("You cant play game thought fake way"));
		}		
		 if(!SteamSysteam->Init())
		 {
		 	FPlatformMisc::RequestExit(false);
		 	UE_LOG(LogTemp, Error, TEXT("You cant play game thought fake way"));
		 }
		
	}
#endif
*/
	CreateAndInitManagers();
	bIsInit = true;	
	
	// 肉鸽版本
	if (UGameplayFuncLib::IsRogueMode())
	{
		UAwRogueDataSystem* RogueDataSystem = GetSubsystem<UAwRogueDataSystem>();
		bool bHaveFile;
		RogueDataSystem->LoadSaveData(bHaveFile);
		if (!bHaveFile)
		{
			bIsFirstLaunchGame = true;
		}
		//Language = RogueDataSystem->GetSettingData().Language;
		GamepadButtonType = RogueDataSystem->GetSettingData().GamepadButtonType;
		
		/*SetMainSoundVolume(RogueDataSystem->GetSettingData().MainVolume);
		SetBGMVolume(RogueDataSystem->GetSettingData().BGMVolume);
		SetSFXVolume(RogueDataSystem->GetSettingData().SFXVolume);
		SetVoiceVolume(RogueDataSystem->GetSettingData().VoiceVolume);
		SetUISoundVolume(RogueDataSystem->GetSettingData().UISoundVolume);*/
	}
	// 正式版本
	else
	{
		const UAwakerSaveData* GameData = Cast<UAwakerSaveData>(UGameplayStatics::LoadGameFromSlot(TEXT("GameSaveData"), 0));
		if(GameData)
		{
			UKismetSystemLibrary::PrintString(this, FString("Has GameData!"));
			bool HasSave = bHaveSave;
			this->RoleInfo = UGameplayFuncLib::LoadGame(HasSave, 0);
			this->SelfCharacterInfo = RoleInfo.MainCharacter;
			bHaveSave = HasSave;
			if (HasSave)
			{
				//Language = this->RoleInfo.CurLanguage;
				GamepadButtonType = this->RoleInfo.CurGamepadButtonType;
			}
			else
			{
				//Language = DataManager->DefaultConfig.Language;
				GamepadButtonType = DataManager->DefaultConfig.GamepadButtonType;
			}
		}
		else
		{
			//Language = DataManager->DefaultConfig.Language;
			GamepadButtonType = DataManager->DefaultConfig.GamepadButtonType;
			UKismetSystemLibrary::PrintString(this, FString("Do Not Has GameData!"));
		}

		SetMainSoundVolume(RoleInfo.MainVolume);
		SetBGMVolume(RoleInfo.BGMVolume);
		SetSFXVolume(RoleInfo.SFXVolume);
		SetVoiceVolume(RoleInfo.VoiceVolume);
		SetUISoundVolume(RoleInfo.UISoundVolume);
	}
}

void UAwGameInstance::Shutdown()
{
	Super::Shutdown();
	UAwGameInstance::Instance = nullptr;
	bIsInit = false;
}

void UAwGameInstance::AlignPlayerNum(bool IsTitle)
{
	// 获取本地玩家数量
	int32 LocalPlayerCount = GEngine->GetGamePlayers(GetWorld()).Num();
	int NumLimit = 1;
	if (!IsTitle && isP2Mode)
	{
		NumLimit = 2;
	}
	if (NumLimit<=LocalPlayerCount)
	{
		// 删除 0 号以外的玩家
		for (int32 i = LocalPlayerCount - 1; i >= NumLimit; --i)
		{
			ULocalPlayer* PlayerToRemove = GEngine->GetGamePlayers(GetWorld())[i];
			if (PlayerToRemove)
			{
				RemoveLocalPlayer(PlayerToRemove);
			}
		}
	}
	else
	{
		FString OutError;
		CreateLocalPlayer(1,OutError,true);
	}
}

void UAwGameInstance::CreateAndInitManagers()
{
	DataManager = NewObject<UAwDataManager>();
	DataManager->ParseData();
	DataManager->bIsRogueMode=UAwDataManager::ConfigValue("GameConfig","GameModeConfig","bRogueLike").Equals("true",ESearchCase::IgnoreCase);
	
	VFXManager = NewObject<UAwVFXManager>();
	SFXManager = NewObject<UAwSFXManager>();
	SFXManager->Init(DataManager);
	
	TimelineManager = NewObject<UTimelineManager>();
	DamageManager = NewObject<UDamageManager>();

	TriggerManager = NewObject<UTriggerManager>();
	TriggerManager->Init(DataManager, "");//ToDo:待完善储存CurrentMap

	TeamManager = NewObject<UAwTeamManager>();
	
	LootManager = NewObject<UAwLootManager>();

	UIManager = NewObject<UAwUIManager>();

	ScoreManager = NewObject<UScoreManager>();

	AvoidanceManager = NewObject<UAwAvoidanceManager>();

	NewbieManager = NewObject<UNewbieManager>();
	//TODO 写死的数据
	NewbieManager->AddImage("Newbie_DrawWeapon", "Core/UI/Newbie/Newbie_DrawWeapon");
	NewbieManager->AddImage("Newbie_Attack", "Core/UI/Newbie/Newbie_Attack");
	NewbieManager->AddImage("Newbie_DamageAvoidance", "Core/UI/Newbie/Newbie_DamageAvoidance");
	NewbieManager->AddImage("Newbie_ChangeClass", "Core/UI/Newbie/Newbie_ChangeClass");
	NewbieManager->AddImage("Newbie_BreakDoodad", "Core/UI/Newbie/Newbie_BreakDoodad");
	NewbieManager->AddImage("Newbie_GatherKeyItem", "Core/UI/Newbie/Newbie_GatherKeyItem");
	NewbieManager->AddImage("Newbie_UseItem", "Core/UI/Newbie/Newbie_UseItem");
	NewbieManager->AddImage("Newbie_ChangeEquipment", "Core/UI/Newbie/Newbie_ChangeEquipment");

	//Quest 暂时的 后续流程未必在这处理
	if (IsValid(GetSubsystem<UAwQuestManager>()))
		GetSubsystem<UAwQuestManager>()->InitManager();

	if (IsValid(GetSubsystem<UAwRogueDataSystem>()))
		GetSubsystem<UAwRogueDataSystem>()->InitSubSystem();
	
	// 注意顺序
	if (IsValid(GetSubsystem<URogueBattleStyleSubSystem>()))
		GetSubsystem<URogueBattleStyleSubSystem>()->InitSubSystem();

	if (IsValid(GetSubsystem<URogueBattleUpgradeSubSystem>()))
		GetSubsystem<URogueBattleUpgradeSubSystem>()->InitSubSystem();
	
	if (IsValid(GetSubsystem<UAwRelicSubSystem>()))
		GetSubsystem<UAwRelicSubSystem>()->InitSubSystem();
	
	if (IsValid(GetSubsystem<UAwRogueItemSubSystem>()))
		GetSubsystem<UAwRogueItemSubSystem>()->InitSubSystem();
	
	if (IsValid(GetSubsystem<UAwRogueTalentSubSystem>()))
		GetSubsystem<UAwRogueTalentSubSystem>()->InitSubSystem();
}

void UAwGameInstance::Tick(float DeltaTime)
{
	//if (DamageManager) 
		//DamageManager->Update(DeltaTime);
	if (TriggerManager)
		TriggerManager->Tick(DeltaTime);
	if (TimelineManager)
		TimelineManager->Update(DeltaTime);
	if(AvoidanceManager)
		AvoidanceManager->Tick(DeltaTime);
}

void UAwGameInstance::ChangeLevelByLevelName(FString LevelName, bool bAbsolute, FString Options)
{
	UGameplayFuncLib::SaveGame();
	TriggerManager->TriggerAllMapOnRemoved(LevelName);
	TriggerManager->ClearAllNotOverAllTrigger();
	UGameplayFuncLib::GetAwGameState()->ClearAll();
	CurClientLoadDungeonFinishNum = 0;
	LoadCurMainLevelFinish = false;
	LoadCurBlockLevelFinish = false;
	LoadCurScriptLevelFinish = false;
	LoadCurSequencerLevelFinish = false;
	NeedLoadCurMainLevelNum = 1;
	LoadedCurMainLevelNum = 0;
	NeedLoadCurBlockLevelNum = 1;
	LoadedCurBlockLevelNum = 0;
	NeedLoadCurScriptLevelNum = 1;
	LoadedCurScriptLevelNum = 0;
	NeedLoadCurSequencerLevelNum = 1;
	LoadedCurSequencerLevelNum = 0;
	//RespawnPoint = nullptr;
	if (bNetworked && Options == "listen") 
	{
		const FString LevelURL = UResourceFuncLib::GetFileNameByPath(LevelName);
		AW_ServerTravel(LevelURL, bAbsolute, false);
	}
	else
	{
		UKismetSystemLibrary::PrintString(GWorld, FString("Load Level: ").Append(LevelName));
		UGameplayStatics::OpenLevel(GWorld, FName(*LevelName), bAbsolute, Options);
		CharacterInfoList.Empty();
	}
	LastLevelName = CurLevelName;
	CurLevelName = LevelName;
	CurMapInfo = UGameplayFuncLib::GetAwDataManager()->GetMapInfoByLevelPath(LevelName);
	UGameplayFuncLib::GetAwGameState()->GetAllMapPointInCurMap(LevelName, FTransform(),  LevelName);

	if(UIManager->LoadingUI)
		UIManager->LoadingUI->SetBG(CurMapInfo.MapId);
}

void UAwGameInstance::ChangeLevelByReference(const TSoftObjectPtr<UWorld> Level, bool bAbsolute, FString Options)
{
	FString LevelName = FPackageName::ObjectPathToPackageName(Level.ToString());
	
	UGameplayFuncLib::SaveGame();
	TriggerManager->TriggerAllMapOnRemoved(LevelName);
	TriggerManager->ClearAllNotOverAllTrigger();
	UGameplayFuncLib::GetAwGameState()->ClearAll();
	CurClientLoadDungeonFinishNum = 0;
	LoadCurMainLevelFinish = false;
	LoadCurBlockLevelFinish = false;
	LoadCurScriptLevelFinish = false;
	LoadCurSequencerLevelFinish = false;
	NeedLoadCurMainLevelNum = 1;
	LoadedCurMainLevelNum = 0;
	NeedLoadCurBlockLevelNum = 1;
	LoadedCurBlockLevelNum = 0;
	NeedLoadCurScriptLevelNum = 1;
	LoadedCurScriptLevelNum = 0;
	//RespawnPoint = nullptr;
	if (bNetworked && Options == "listen") 
	{
		const FString LevelURL = UResourceFuncLib::GetFileNameByPath(LevelName);
		AW_ServerTravel(LevelURL, bAbsolute, false);
	}
	else
	{
		UKismetSystemLibrary::PrintString(GWorld, FString("Load Level: ").Append(LevelName));
		UGameplayStatics::OpenLevelBySoftObjectPtr(GWorld,Level,bAbsolute,Options);
		CharacterInfoList.Empty();
	}
	LastLevelName = CurLevelName;
	CurLevelName = LevelName;
	CurMapInfo = UGameplayFuncLib::GetAwDataManager()->GetMapInfoByLevelPath(LevelName);
	UGameplayFuncLib::GetAwGameState()->GetAllMapPointInCurMap(LevelName, FTransform(), LevelName);
}

void UAwGameInstance::AW_ServerTravel(const FString& InURL, bool bAbsolute, bool bShouldSkipGameNotify)
{
	GetWorld()->ServerTravel(InURL, bAbsolute, bShouldSkipGameNotify);
}

void UAwGameInstance::AllCharacterReady()
{
	for (auto& Character : UGameplayFuncLib::GetAwGameState()->AllCharacters)
	{
		if (Character.Key)
		{
			Character.Key->GetReady();
		}
	}
}

void UAwGameInstance::LoadLevelFinish(EAwLevelType LevelType)
{
	if(LevelType == EAwLevelType::Main)
	{
		LoadedCurMainLevelNum++;
		if(LoadedCurMainLevelNum >= NeedLoadCurMainLevelNum)
			LoadCurMainLevelFinish = true;
	}
	else if(LevelType == EAwLevelType::Block)
	{
		LoadedCurBlockLevelNum++;
		if(LoadedCurBlockLevelNum >= NeedLoadCurBlockLevelNum)
			LoadCurBlockLevelFinish = true;
	}
	else if(LevelType == EAwLevelType::Script)
	{
		LoadedCurScriptLevelNum++;
		if(LoadedCurScriptLevelNum >= NeedLoadCurScriptLevelNum)
			LoadCurScriptLevelFinish = true;
	}
	else if (LevelType == EAwLevelType::LevelSequencer)
	{
		LoadedCurSequencerLevelNum++;
		if (LoadedCurSequencerLevelNum >= NeedLoadCurSequencerLevelNum)
			LoadCurSequencerLevelFinish = true;
	}
	if(LoadCurMainLevelFinish && LoadCurBlockLevelFinish && LoadCurScriptLevelFinish && LoadCurSequencerLevelFinish)
	{
		//地图上拉的点信息给到地图信息
		OnLoadMapFinished(CurLevelName);
		UGameplayFuncLib::GetAwGameState()->GetAllMapPointInCurMap(CurLevelName,FTransform::Identity,CurLevelName);
	
		this->TriggerManager->TriggerAllMapOnCreate(CurLevelName,FTransform::Identity);
	
		this->AllCharacterReady();
		this->StartDungeonSwitch();
	}
}

void UAwGameInstance::LoadAllDungeonLevel()
{
	UnLoadAllDungeonLevel();
	AAwGameMode_RandomDungeon* DungeonGameMode = Cast<AAwGameMode_RandomDungeon>(UGameplayStatics::GetGameMode(GWorld));
	if (DungeonGameMode)
	{
		//RoomLevel
		int RoomIndex = 0;
		UKismetSystemLibrary::PrintString(this, FString("DungeonLevel Num : ").Append(FString::FromInt(DungeonGameMode->RoomList.Num())));
		for (auto& CurRoom : DungeonGameMode->RoomList)
		{
			FString CurRoomLevelName = "Level";
			CurRoomLevelName.Append(FString::FromInt(RoomIndex));
			//现在地下城中不会出现相同的房间两次，所以先不用CurRoomLevelName
			LoadDungeonRoomLevel(CurRoom, CurRoomLevelName);
			SpawnDungeonDoors(CurRoom,CurRoomLevelName);
			RoomIndex++;
		}
		//Road
		for (auto& CurRoadInfo : DungeonGameMode->RoadList)
		{
			SpawnDungeonRoad(CurRoadInfo);
		}
	}
}

void UAwGameInstance::UnLoadAllDungeonLevel()
{
	LoadFinishDungeonLevelNum = 0;
}

void UAwGameInstance::LoadDungeonRoomLevel_Implementation(FDungeonTile CurRoomInfo, const FString& LevelName)
{
	bool LoadLevelSuccess = false;
	//LoadLevel
	//UKismetSystemLibrary::PrintString(GWorld, FString("Load Dungeon Level: ").Append(CurRoomInfo.LevelPath));
	ULevelStreamingDynamic* CurLevel = ULevelStreamingDynamic::LoadLevelInstance(this, CurRoomInfo.LevelPath, CurRoomInfo.RoomLocation, FRotator(0,CurRoomInfo.LevelRotYaw,0), LoadLevelSuccess, LevelName);
	if (CurLevel)
	{
		DungeonRoomLevelList.Add(CurRoomInfo.LevelPath, CurLevel);
		//OnCreateScript
		FTransform LevelTrans = FTransform();
		LevelTrans.SetLocation(CurRoomInfo.RoomLocation);
		LevelTrans.SetRotation(FRotator(0,CurRoomInfo.LevelRotYaw,0).Quaternion());
		LevelTrans.SetScale3D(FVector(1, 1, 1));
		TriggerManager->TriggerCurMapOnCreate(CurRoomInfo.LevelPath, LevelTrans);
		//LoadBlockLevel
		const FString BlockLevelPath = FString(CurRoomInfo.LevelPath + "_Block");
		const FString BlockLevelName = FString(LevelName + "_Block");
		//UKismetSystemLibrary::PrintString(GWorld, FString("Load Dungeon Level: ").Append(BlockLevelPath));
		ULevelStreamingDynamic* CurBlockLevel = ULevelStreamingDynamic::LoadLevelInstance(this, BlockLevelPath, CurRoomInfo.RoomLocation, FRotator(0,CurRoomInfo.LevelRotYaw,0), LoadLevelSuccess, BlockLevelName);
		if (CurBlockLevel)
		{
			DungeonRoomBlockLevelList.Add(BlockLevelPath, CurBlockLevel);
		}
		//LoadScriptLevel
		const FString ScriptLevelPath = FString(CurRoomInfo.LevelPath + "_Script");
		const FString ScriptLevelName = FString(LevelName + "_Script");
		//UKismetSystemLibrary::PrintString(GWorld, FString("Load Dungeon Level: ").Append(ScriptLevelPath));
		ULevelStreamingDynamic* CurScriptLevel = ULevelStreamingDynamic::LoadLevelInstance(this, ScriptLevelPath, CurRoomInfo.RoomLocation, FRotator(0,CurRoomInfo.LevelRotYaw,0), LoadLevelSuccess, ScriptLevelName);
		if (CurScriptLevel)
		{
			DungeonRoomScriptLevelList.Add(ScriptLevelPath, CurScriptLevel);
		}
	}
}

AActor* UAwGameInstance::SpawnDungeonRoad(FDungeonRoadInfo CurRoadInfo)
{
	AActor* Road = nullptr;
	const FString BpPath = UResourceFuncLib::GetBpAssetPath(CurRoadInfo.MeshPath);
	UClass* BpClass = LoadClass<AActor>(nullptr,*BpPath);
	if (BpClass)
	{
		FActorSpawnParameters ActorSpawnParameters;
		ActorSpawnParameters.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AlwaysSpawn;
		FTransform SpawnTrans = FTransform();
		SpawnTrans.SetLocation(CurRoadInfo.Location);
		SpawnTrans.SetRotation(CurRoadInfo.Rotation.Quaternion());
		SpawnTrans.SetScale3D(FVector(1,1,1));
		Road = GWorld->SpawnActor<AActor>(BpClass, SpawnTrans, ActorSpawnParameters);
	}
	return Road;
}

void UAwGameInstance::SpawnDungeonDoors(FDungeonTile CurRoomInfo, const FString LevelName)
{
	AActor* Road = nullptr;
	const FString BpPath = UResourceFuncLib::GetBpAssetPath(UGameplayFuncLib::GetAwDataManager()->GetDungeonLevelInfoByPath(CurRoomInfo.LevelPath).DoorMeshPath);
	UClass* BpClass = LoadClass<AActor>(nullptr,*BpPath);
	if (BpClass)
	{
		for (FDungeonDoorInfo ClosedDoor : CurRoomInfo.ClosedDoors)
		{
			FActorSpawnParameters ActorSpawnParameters;
			ActorSpawnParameters.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AlwaysSpawn;
			FTransform SpawnTrans = FTransform();
			SpawnTrans.SetLocation(ClosedDoor.Location);
			SpawnTrans.SetRotation(UKismetMathLibrary::MakeRotFromX(FVector(ClosedDoor.Direction * -1,0)).Quaternion());
			SpawnTrans.SetScale3D(FVector(1,1,1));
			Road = GWorld->SpawnActor<AActor>(BpClass, SpawnTrans, ActorSpawnParameters);
		}
	}
}

void UAwGameInstance::ClearDungeonLevelInfo()
{
	//Level
	for (auto& CurLevel : DungeonRoomLevelList)
	{
		if (CurLevel.Value)
		{
			CurLevel.Value->SetIsRequestingUnloadAndRemoval(true);
		}
	}
	DungeonRoomLevelList.Empty();
	//BlockLevel
	for (auto& CurBlockLevel : DungeonRoomBlockLevelList)
	{
		if (CurBlockLevel.Value)
		{
			CurBlockLevel.Value->SetIsRequestingUnloadAndRemoval(true);
		}
	}
	DungeonRoomLevelList.Empty();
	//ScriptLevel
	for (auto& CurScriptLevel : DungeonRoomScriptLevelList)
	{
		if (CurScriptLevel.Value)
		{
			CurScriptLevel.Value->SetIsRequestingUnloadAndRemoval(true);
		}
	}
	DungeonRoomScriptLevelList.Empty();
}

void UAwGameInstance::StartDungeonSwitch()
{
	TArray<AActor*> GetActors;
	UGameplayStatics::GetAllActorsOfClass(this,AMapSwitchManager::StaticClass(),GetActors);
	for (AActor* Actor : GetActors)
	{
		AMapSwitchManager* SwitchManager = Cast<AMapSwitchManager>(Actor);
		if(SwitchManager)
			SwitchManager->StartSwitch();
	}
}

void UAwGameInstance::LoadDungeonLevelFinish(FString DungeonLevelName)
{
	this->LoadFinishDungeonLevelNum++;
	FString PrintLog = DungeonLevelName;
	PrintLog.Append(" LoadFinish");
	UKismetSystemLibrary::PrintString(this, PrintLog);
	
	FTransform LevelTrans = FTransform::Identity;
	if(DungeonRoomLevelList.Contains(DungeonLevelName))
		LevelTrans = DungeonRoomLevelList[DungeonLevelName]->LevelTransform;
	else if(DungeonRoomBlockLevelList.Contains(DungeonLevelName))
		LevelTrans = DungeonRoomBlockLevelList[DungeonLevelName]->LevelTransform;
	else if(DungeonRoomScriptLevelList.Contains(DungeonLevelName))
		LevelTrans = DungeonRoomScriptLevelList[DungeonLevelName]->LevelTransform;
	this->TriggerManager->TriggerCurMapOnCreate(DungeonLevelName,LevelTrans);
	//GetMapPoint
	UGameplayFuncLib::GetAwGameState()->GetAllMapPointInCurMap(DungeonLevelName, LevelTrans, DungeonLevelName);
	//获得每个房间Level上拉的点
	OnLoadMapFinished(DungeonLevelName);
	
	if(LoadFinishDungeonLevelNum >= (DungeonRoomLevelList.Num() + DungeonRoomBlockLevelList.Num() + DungeonRoomScriptLevelList.Num()))
	{
		//彻底加载完毕之后
		UGameplayFuncLib::GetAwGameMode()->PlayerClientLoadFinish(HostPlayerController);
		this->TriggerManager->TriggerAllMapOnCreate(CurLevelName,FTransform::Identity);
		UGameplayFuncLib::GetUiManager()->HideLoading();
		this->AllCharacterReady();
		this->StartDungeonSwitch();
		for (auto pc : UGameplayFuncLib::GetAllLocalAwPlayerControllers())
		{
			if (!pc)continue;
			pc->SetInputEnable(true);
		}
	}
}

ABreakableClone* UAwGameInstance::MakeBreakableClone(
		AAwCharacter* CloneFrom, FVector Offset, float StayTime,
		bool DoOffense, FOffenseInfo OffenseInfo, FDefenseInfo DefenseInfo, FChaResource ChaResource,
		bool UseTransparent, FLinearColor TransColor)
{
	//先检查所有的内容处理该被删除的
	TArray<AAwCharacter*> ToBeRmv;
	for (TTuple<AAwCharacter*, FBreakableCloneInPool> CloneInfo : this->CharacterBreakableClones)
	{
		if (CloneInfo.Key == nullptr)
		{
			for (ABreakableClone* Avatar : CloneInfo.Value.Clones)
			{
				Avatar->DestroyMe();
			}
			ToBeRmv.Add(CloneInfo.Key);
		}
	}
	for (AAwCharacter* BeRmv : ToBeRmv) CharacterBreakableClones.Remove(BeRmv);
	
	//正式办事儿
	if (!CloneFrom) return nullptr;
	if (this->CharacterBreakableClones.Contains(CloneFrom))
	{
		for (ABreakableClone* Clone : CharacterBreakableClones[CloneFrom].Clones)
		{
			if (Clone->ToBeRemoved == true)
			{
				Clone->RefreshEquipmentsByClone();
				Clone->ToBeRemoved = false;
				Clone->SetLifeDuration(StayTime);
				Clone->SetActorTransform(CloneFrom->GetActorTransform());
				Clone->SetToShown(true);
				if (UseTransparent)
				{
					Clone->SetTransparentMaterial(TransColor);
				}else
				{
					Clone->ResetTransparentMaterial();
				}
				Clone->Set(Offset, DoOffense, OffenseInfo, DefenseInfo, FChaResource(CloneFrom->CurrentHP()));
				return Clone;
			}
		}
		if (CharacterBreakableClones[CloneFrom].Clones.Num() >= 10)
		{
			return nullptr;	//最多10个克隆，太多就从这里干掉一点就行了
		}

		UClass* BpClass = NewObject<ABreakableClone>()->GetClass();
		if (BpClass)
		{
			FActorSpawnParameters ActorSpawnParameters;
			ActorSpawnParameters.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AlwaysSpawn;
			ABreakableClone* GuyClone = GWorld->SpawnActor<ABreakableClone>(BpClass, CloneFrom->GetActorTransform(), ActorSpawnParameters);
			GuyClone->CloneFromAwCharacter(CloneFrom, StayTime);
			GuyClone->PlayCloneAnim();
			
			this->CharacterBreakableClones[CloneFrom].Clones.Add(GuyClone);
			if (UseTransparent)
			{
				GuyClone->SetTransparentMaterial(TransColor);
			}else
			{
				GuyClone->ResetTransparentMaterial();
			}
			GuyClone->Set(Offset, DoOffense, OffenseInfo, DefenseInfo, FChaResource(CloneFrom->CurrentHP()));
			return GuyClone;
		}
	}else
	{
		UClass* BpClass = NewObject<ABreakableClone>()->GetClass();
		if (BpClass)
		{
			FActorSpawnParameters ActorSpawnParameters;
			ActorSpawnParameters.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AlwaysSpawn;
			ABreakableClone* GuyAva = GWorld->SpawnActor<ABreakableClone>(BpClass, CloneFrom->GetActorTransform(), ActorSpawnParameters);
			GuyAva->CloneFromAwCharacter(CloneFrom, StayTime);
			GuyAva->PlayCloneAnim();

			FBreakableCloneInPool Ava = FBreakableCloneInPool();
			Ava.Clones.Add(GuyAva);
			this->CharacterBreakableClones.Add(CloneFrom, Ava);
			if (UseTransparent)
			{
				GuyAva->SetTransparentMaterial(TransColor);
			}else
			{
				GuyAva->ResetTransparentMaterial();
			}
			GuyAva->Set(Offset, DoOffense, OffenseInfo, DefenseInfo, FChaResource(CloneFrom->CurrentHP()));
			return GuyAva;
		}
	}
	return nullptr;
}

AAvatarCharacter* UAwGameInstance::MakeCharacterClone(AAwCharacter* CloneFrom, float StayTime, bool UseTransparent, FLinearColor TransColor)
{
	//先检查所有的内容处理该被删除的
	TArray<AAwCharacter*> ToBeRmv;
	for (TTuple<AAwCharacter*, FAvatarInPool> CloneInfo : this->CharacterClones)
	{
		if (CloneInfo.Key == nullptr)
		{
			for (AAvatarCharacter* Avatar : CloneInfo.Value.Avatars)
			{
				Avatar->DestroyMe();
			}
			ToBeRmv.Add(CloneInfo.Key);
		}
	}
	for (const AAwCharacter* BeRmv : ToBeRmv) CharacterClones.Remove(BeRmv);
	
	//正式办事儿
	if (!CloneFrom) return nullptr;
	if (this->CharacterClones.Contains(CloneFrom))
	{
		for (AAvatarCharacter* Avatar : CharacterClones[CloneFrom].Avatars)
		{
			if (Avatar->ToBeRemoved == true)
			{
				Avatar->RefreshEquipmentsByClone();
				Avatar->ToBeRemoved = false;
				Avatar->SetLifeDuration(StayTime);
				Avatar->SetActorTransform(CloneFrom->GetActorTransform());
				Avatar->SetToShown(true);
				if (UseTransparent)
				{
					Avatar->SetTransparentMaterial(TransColor);
				}else
				{
					Avatar->ResetTransparentMaterial();
				}
				return Avatar;
			}
		}
		if (CharacterClones[CloneFrom].Avatars.Num() >= 10)
		{
			return nullptr;	//最多10个克隆，太多就从这里干掉一点就行了
		}

		UClass* BpClass = NewObject<AAvatarCharacter>()->GetClass();
		if (BpClass)
		{
			FActorSpawnParameters ActorSpawnParameters;
			ActorSpawnParameters.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AlwaysSpawn;
			AAvatarCharacter* GuyAva = GWorld->SpawnActor<AAvatarCharacter>(BpClass, CloneFrom->GetActorTransform(), ActorSpawnParameters);
			GuyAva->CloneFromAwCharacter(CloneFrom, StayTime);
			GuyAva->PlayCloneAnim();
			
			this->CharacterClones[CloneFrom].Avatars.Add(GuyAva);
			if (UseTransparent)
			{
				GuyAva->SetTransparentMaterial(TransColor);
			}else
			{
				GuyAva->ResetTransparentMaterial();
			}
			return GuyAva;
		}
	}else
	{
		UClass* BpClass = NewObject<AAvatarCharacter>()->GetClass();
		if (BpClass)
		{
			FActorSpawnParameters ActorSpawnParameters;
			ActorSpawnParameters.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AlwaysSpawn;
			AAvatarCharacter* GuyAva = GWorld->SpawnActor<AAvatarCharacter>(BpClass, CloneFrom->GetActorTransform(), ActorSpawnParameters);
			GuyAva->CloneFromAwCharacter(CloneFrom, StayTime);
			GuyAva->PlayCloneAnim();

			FAvatarInPool Ava = FAvatarInPool();
			Ava.Avatars.Add(GuyAva);
			this->CharacterClones.Add(CloneFrom, Ava);
			if (UseTransparent)
			{
				GuyAva->SetTransparentMaterial(TransColor);
			}else
			{
				GuyAva->ResetTransparentMaterial();
			}
			return GuyAva;
		}
	}
	return nullptr;
}

APopUpText* UAwGameInstance::PopText(FVector Position, FString Text,  int Priority, int TextSize, FLinearColor TextColor)
{
	int i = 0;
	while (i < PopUpTexts.Num())
	{
		if (PopUpTexts[i].PopText)
		{
			if (PopUpTexts[i].PopText->Working == false)
			{
				PopUpTexts[i].Timestamp = UDateTimeFuncLib::GetTimestamp();
				PopUpTexts[i].Priority = Priority;
				PopUpTexts[i].PopText->PopText(Position, Text, TextSize, TextColor);
				return PopUpTexts[i].PopText;
			}
			i++;
		}
		else
			PopUpTexts.RemoveAt(i);
	}
	
	if (PopUpTexts.Num() >= 20) //最多20个，不服就改
	{
		PopUpTexts.Sort([](const FPopTextRecorder& A, const FPopTextRecorder& B)
		{
			if (A.Priority < B.Priority) return true;
			if (B.Priority < A.Priority) return false;
			return A.Timestamp > B.Timestamp;
		});
		PopUpTexts[0].Timestamp = UDateTimeFuncLib::GetTimestamp();
		PopUpTexts[0].Priority = Priority;
		PopUpTexts[0].PopText->PopText(Position, Text, TextSize, TextColor);
		return PopUpTexts[0].PopText;
	}

	const FString BpPath = UResourceFuncLib::GetBpAssetPath("Core/UI/PopUpText/BP_PopUpText");
	UClass* BpClass = LoadClass<AActor>(nullptr,*BpPath);
	APopUpText* Item = nullptr;
	if (BpClass)
	{
		FActorSpawnParameters ActorSpawnParameters;
		ActorSpawnParameters.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AlwaysSpawn;
		FTransform Transform = FTransform::Identity;
		Transform.SetLocation(Position);
		Item = Cast<APopUpText>(GWorld->SpawnActor<AActor>(BpClass, Transform, ActorSpawnParameters));
		Item->PopText(Position, Text, TextSize, TextColor);
	}
	PopUpTexts.Add(FPopTextRecorder(
		Item,
		UDateTimeFuncLib::GetTimestamp(),
		Priority
	));
	return Item;
}

ATalkBubble* UAwGameInstance::ShowTalkBubble(AAwCharacter* OnCha, FString ChaName, FString Text, float InSec)
{
	if (!OnCha) return nullptr;
	const FVector Pos = OnCha->TextBubblePoint ? OnCha->TextBubblePoint->GetComponentLocation() : OnCha->GetActorLocation();
	for (ATalkBubble* Bubble : this->TalkingBubbles)
	{
		if (Bubble->IsFree() || Bubble->IsMine(OnCha))
		{
			Bubble->ShowText(OnCha, ChaName, Text, InSec);
			Bubble->SetActorLocation(Pos);
			return Bubble;
		}
	}
	const FString BpPath = UResourceFuncLib::GetBpAssetPath("Core/UI/BP_TextBubble");
	UClass* BpClass = LoadClass<AActor>(nullptr,*BpPath);
	ATalkBubble* Item = nullptr;
	if (BpClass)
	{
		FActorSpawnParameters ActorSpawnParameters;
		ActorSpawnParameters.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AlwaysSpawn;
		FTransform Transform = FTransform::Identity;
		Transform.SetLocation(Pos);
		Item = Cast<ATalkBubble>(GWorld->SpawnActor<AActor>(BpClass, Transform, ActorSpawnParameters));
		Item->ShowText(OnCha, ChaName, Text, InSec);
	}
	TalkingBubbles.Add(Item);
	return Item;
}

void UAwGameInstance::CurDungeonCampProgressModify(FString CampId, int Value)
{
	const AAwGameMode_RandomDungeon* DungeonGameMode = Cast<AAwGameMode_RandomDungeon>(UGameplayFuncLib::GetAwGameMode());
	if (!DungeonGameMode) return;
	const FString DungeonId = DungeonGameMode->CurDungeonInfo.Id;
	CampProgressModify(DungeonId, CampId, Value);
}

void UAwGameInstance::CampProgressModify(FString DungeonId, FString CampId, int Value)
{
	//获取全部的Event
	FAwDungeonModel DungeonInfo = UGameplayFuncLib::GetAwDataManager()->GetDungeonModelById(DungeonId);
	TArray<FAwDungeonCampEvent> AllEventList;
	for (FAwDungeonCampModel CampModel : DungeonInfo.Camps)
	{
		if(CampModel.CampId == CampId)
		{
			AllEventList = CampModel.EventList;
			break;
		}
	}
	for (int i = 0; i < this->RoleInfo.DungeonRecords.Num(); i++)
	{
		if(this->RoleInfo.DungeonRecords[i].DungeonId == DungeonId)
		{
			for(int j = 0; j < this->RoleInfo.DungeonRecords[i].Camps.Num(); j++)
			{
				if(this->RoleInfo.DungeonRecords[i].Camps[j].CampId == CampId)
				{
					//计算CampProgress值
					int CampProgress = this->RoleInfo.DungeonRecords[i].Camps[j].CampProgress;
					if(CampProgress + Value > 1000)
						this->RoleInfo.DungeonRecords[i].Camps[j].CampProgress = 1000;
					//目前先不让势力值会到0，暂时先最低50
					else if(CampProgress + Value < 50)
						this->RoleInfo.DungeonRecords[i].Camps[j].CampProgress = 50;
					else
						this->RoleInfo.DungeonRecords[i].Camps[j].CampProgress = CampProgress + Value;

					//检测触发了哪些CampEvent
					for (FAwDungeonCampEvent CampEvent : AllEventList)
					{
						if(CampEvent.ProgressMin <= RoleInfo.DungeonRecords[i].Camps[j].CampProgress
							&& CampEvent.ProgressMax >= RoleInfo.DungeonRecords[i].Camps[j].CampProgress)
						{
							//在RoleInfo中添加触发该Event的记录
							bool CanTriggerEvent = true;
							bool HasRecordEvent = false;
							FCampEventRecord CurRecord = FCampEventRecord();
							for(int k = 0 ; k < RoleInfo.DungeonRecords[i].Camps[j].EventRecordList.Num(); k++)
							{
								if(RoleInfo.DungeonRecords[i].Camps[j].EventRecordList[k].EventId == CampEvent.CampEventId)
								{
									if(RoleInfo.DungeonRecords[i].Camps[j].EventRecordList[k].TriggerTime >= CampEvent.Limit)
										CanTriggerEvent = false;
									else
									{
										RoleInfo.DungeonRecords[i].Camps[j].EventRecordList[k].TriggerTime += 1;
										CurRecord = RoleInfo.DungeonRecords[i].Camps[j].EventRecordList[k];
										HasRecordEvent = true;
									}
									break;
								}
							}
							if(!HasRecordEvent)
							{
								CurRecord.EventId = CampEvent.CampEventId;
								CurRecord.TriggerTime = 1;
								RoleInfo.DungeonRecords[i].Camps[j].EventRecordList.Add(CurRecord);
							}
							if(CanTriggerEvent)
							{
								//TriggerFunc
								for(const FJsonFuncData Trigger : CampEvent.TirggerFuncList)
								{
									UFunction* Func = UCallFuncLib::GetUFunction(Trigger.ClassPath, Trigger.FunctionName);
									if (Func)
									{
										struct
										{
											FString DungeonId;
											FString CampId;
											FAwDungeonCampEvent EventInfo;
											int CurTriggerTime;
											int CurProgress;
											TArray<FString> Params;
										}FuncParam;
										FuncParam.DungeonId = DungeonId;
										FuncParam.CampId = CampId;
										FuncParam.EventInfo = CampEvent;
										FuncParam.CurTriggerTime = CurRecord.TriggerTime;
										FuncParam.CurProgress = this->RoleInfo.DungeonRecords[i].Camps[j].CampProgress;
										FuncParam.Params = Trigger.Params;
										this->ProcessEvent(Func, &FuncParam);
									}
								}
							}
						}
					}
					FString PrintCampId = CampId;
					UKismetSystemLibrary::PrintString(this, PrintCampId.Append(FString(" : ")).Append(FString::FromInt(this->RoleInfo.DungeonRecords[i].Camps[j].CampProgress)));
					break;
				}
			}
			break;
		}
	}
}

void UAwGameInstance::OnLoadMapFinished(FString LevelName)
{
	SetupPathNodeQueueInfo(LevelName);
}

int UAwGameInstance::GetRoleSwitch(FString SwitchKey)
{
	return RoleInfo.GetSwitch(SwitchKey);
}

void UAwGameInstance::SetupPathNodeQueueInfo(FString LevelName)
{
	//地图上拉的点信息给到地图信息
	for (FPathNodeQueueInfo QueueInfo : UGameplayFuncLib::GetAwDataManager()->GetMapInfoByLevelPath(LevelName).PathNodeQueueInfos)
	{
		FPathNodeQueue Queue;
		Queue.Id = QueueInfo.Id;
		Queue.Loop = QueueInfo.Loop;
		for (FString NodeId : QueueInfo.NodeIds)
		{
			if (UGameplayFuncLib::GetAwGameState()->AwMapPathNodes.Contains(NodeId))
			{
				Queue.Nodes.Add(UGameplayFuncLib::GetAwGameState()->AwMapPathNodes[NodeId].Position);
			}
		}
		UGameplayFuncLib::GetAwGameState()->AwMapPathNodeQueues.Add(Queue.Id, Queue);
	}
}

void UAwGameInstance::SetLanguage(ELanguage ToLanguage)
{
	if(Language != ToLanguage)
	{
		Language = ToLanguage;
		LanguageChangeDelegate.Broadcast();	
	}
}

void UAwGameInstance::GiveRogueCurrency(FString Id, int Num)
{
	UAwRogueDataSystem* SubSystem =  GWorld->GetGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	if (!SubSystem)
	{
		return ;
	}

	const int NewNum = FMath::Clamp(Num, 0, Num);
	SubSystem->AddCurrencyCount(NewNum, Id, true);
}
