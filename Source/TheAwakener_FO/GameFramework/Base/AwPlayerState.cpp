// Fill out your copyright notice in the Description page of Project Settings.


#include "AwPlayerState.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GameFramework/Manager/AwRogueDataSystem.h"


bool AAwPlayerState::HasLockAwakeSkill()
{
	TArray<FString> MetaKeys;
	AllAwakeSkills.GenerateKeyArray(MetaKeys);
	return UnLockAwakeSkills == MetaKeys;
}

bool AAwPlayerState::CheckAwakeSkillCostEnough()
{
	if (CurAwakeSkill&&!CurAwakeSkill->Id.IsEmpty())
	{
		const AAwCharacter* PlayerCharacter = Cast<AAwPlayerController>(GetPlayerController())->CurCharacter;
		if (PlayerCharacter)
		{
			if (CurAwakeSkill->MinEnergyCost > 0)
				return  PlayerCharacter->CharacterObj.CurrentRes.AP >= CurAwakeSkill->MinEnergyCost;
			else
				return  PlayerCharacter->CharacterObj.CurrentRes.AP > 0;
		}
	}
	return  false;
}


void AAwPlayerState::BeginPlay()
{
	Super::BeginPlay();
	
}

void AAwPlayerState::SetupAfterPossess()
{
	AllAwakeSkills =UGameplayFuncLib::GetAwDataManager()->GetAllAwakeSkillnfo();
	LoadAwakeSkillSaveData();
}

FAwActionSkillInfo AAwPlayerState::K2_GetCurAwakeSkill()
{
	FAwActionSkillInfo Res = FAwActionSkillInfo();
	if (CurAwakeSkill&&!CurAwakeSkill->Id.IsEmpty())
	{
		Res = *CurAwakeSkill;
	}
	return Res;
}

void AAwPlayerState::SetCurAwakeSkill(FString NewSkillId)
{
	if (!UnLockAwakeSkills.Contains(NewSkillId))
	{
		if (UnLockAwakeSkills.IsEmpty())
		{
			UE_LOG(LogTemp, Warning, TEXT("AwPlayerState Skill Set:%s  But UnlockAwakeSkills Not Contains it"), *NewSkillId);
		}
		return;
	}
	if (CurAwakeSkill)
	{
		if (CurAwakeSkill->Id!=NewSkillId)
		{
			CurAwakeSkill->ReSet();
			auto pc = Cast<AAwPlayerController>(GetOwner());
			if (pc)
			{
				if(pc->CurCharacter)
					pc->CurCharacter->RemoveBuffByTag("Awake");
			}
			//UGameplayFuncLib::GetAwGameInstance()->RoleInfo.CurAwakeSkillId = NewSkillId;
		}
	}
	CurAwakeSkill = AllAwakeSkills.Find(NewSkillId);
}

bool AAwPlayerState::TryUnlockPlayerAwakeSkill(FString SkillId)
{
	FAwActionSkillInfo UnlockSkill = AllAwakeSkills.Contains(SkillId)?*AllAwakeSkills.Find(SkillId):FAwActionSkillInfo();
	if(UnlockSkill.Id.IsEmpty())
	{
		return false;
	}
	UAwGameInstance* GameInstance =  UGameplayFuncLib::GetAwGameInstance();
	if (!GameInstance)
	{
		return  false;
	}
	UAwRogueDataSystem* SubSystem = GetGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	if (!SubSystem)
	{
		return false;
	}
	
	//匹配玩家 对应资源持有量>
	bool bCostEnought = !(SubSystem->GetCurrencyCount(UnlockSkill.CostSourceId, false)<UnlockSkill.UnlockCostNum);

	if (bCostEnought)
	{
		SubSystem->AddCurrencyCount(-1 * UnlockSkill.UnlockCostNum, UnlockSkill.CostSourceId, false);	
	}
	else
	{
		return false;
	}
	UnlockPlayerAwakeSkill(SkillId);
	return  true;
}

void  AAwPlayerState::UnlockPlayerAwakeSkill(FString SkillId)
{
	FAwActionSkillInfo UnlockSkill = AllAwakeSkills.Contains(SkillId)?*AllAwakeSkills.Find(SkillId):FAwActionSkillInfo();
	if(UnlockSkill.Id.IsEmpty())
	{
		UE_LOG(LogTemp, Warning, TEXT("AwPlayerState Skill Unlock:%s  But AllAwakeSkills Not Contains it"), *SkillId);
		return ;
	}
	
	UnLockAwakeSkills.Add(SkillId);
	//UnLockAwakeSkills.GenerateKeyArray(UGameplayFuncLib::GetAwGameInstance()->RoleInfo.UnLockAwakeSkills);
	TArray<FAwActionSkillInfo> NewUnlocks;
	for (auto key:UnLockAwakeSkills)
	{
		NewUnlocks.Add(*AllAwakeSkills.Find(key));
	}
	OnAwakeSkillUnlock.Broadcast(NewUnlocks);
}

void AAwPlayerState::ActivePlayerCurAwakeSkill(bool bOpen)
{
	if (!CurAwakeSkill&&bOpen==CurAwakeSkill->bUsing)
	{
		return;
	}
	
	CurAwakeSkill->bUsing = bOpen;

	OnAwakeSkillChange.Broadcast(*CurAwakeSkill);
}

void AAwPlayerState::ReduceCurAwakeSkillMinCost()
{
	if (CurAwakeSkill&&!CurAwakeSkill->Id.IsEmpty())
	{
		AAwCharacter* PlayerCharacter = Cast<AAwPlayerController>(GetPlayerController())->CurCharacter;
		if (PlayerCharacter)
		{
			if (CheckAwakeSkillCostEnough())
			{
				PlayerCharacter->CharacterObj.CurrentRes.AP -= CurAwakeSkill->MinEnergyCost;
			}
		}
	}
}

void AAwPlayerState::ClearPlayerCurAwakeSkill()
{
	if(CurAwakeSkill)
		CurAwakeSkill->bUsing =false;
	CurAwakeSkill = nullptr;
	//UGameplayFuncLib::GetAwGameInstance()->RoleInfo.CurAwakeSkillId = "";
	OnAwakeSkillChange.Broadcast(FAwActionSkillInfo());
}

void AAwPlayerState::LoadAwakeSkillSaveData()
{
	UAwRogueDataSystem* DataSystem = nullptr;
	
	if(GetWorld()&&GetGameInstance())
	{
		DataSystem = GetGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	}
	else
	{
		return;
	}
	if (UGameplayFuncLib::GetAwDataManager())
	{
		AllAwakeSkills =UGameplayFuncLib::GetAwDataManager()->GetAllAwakeSkillnfo();		
	}
	else
	{
		return;
	}
	if (DataSystem&&DataSystem->HasCurRogueData())
	{
		bool Temp = true;
		UnLockAwakeSkills = DataSystem->GetCurRogueData(Temp).UnLockAwakeSkills;
		UE_LOG(LogTemp, Log, TEXT("AwPlayerState Load SaveData Skills Num:%d"),DataSystem->GetCurRogueData(Temp).UnLockAwakeSkills.Num());
		int PlayerIndex = 0;
		auto PC = Cast<AAwPlayerController>(GetPlayerController());
		if (PC)
			PlayerIndex = PC->GetLocalPCIndex();
		SetCurAwakeSkill(DataSystem->GetAwakeSkillId(DataSystem->GetCurRogueData(Temp),PlayerIndex));
		AfterLoadSaveData= true;
	}
	else
	{
		UE_LOG(LogTemp, Warning, TEXT("AwPlayerState Load SaveData Failed"));
	}
}

void AAwPlayerState::CusPrint()
{
	UE_LOG(LogTemp, Warning, TEXT("AwPlayerState bFromPreviousLevel :%s"), IsFromPreviousLevel());
}
