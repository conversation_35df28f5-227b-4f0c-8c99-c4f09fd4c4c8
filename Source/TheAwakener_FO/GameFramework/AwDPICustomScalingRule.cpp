// The copyright of this project belongs to Shanghai Minghuan Software Technology Co., Ltd

#include "AwDPICustomScalingRule.h"

#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GamePlay/Setting/RogueGameSetting.h"

float UAwDPICustomScalingRule::GetDPIScaleBasedOnSize(FIntPoint Size) const
{
	float NominalAspectRatio = (2560.0f / 1440.0f);

	if (Size.X == 0 || Size.Y == 0)
	{
		return 1;
	}
	else if (Size.X / Size.Y > NominalAspectRatio)
	{
		return (Size.Y / 1440.0f);
	}
	else
	{
		return (Size.X / 2560.0f);
	}
	
}
