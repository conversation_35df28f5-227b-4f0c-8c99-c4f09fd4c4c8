// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Base/AwPlayerState.h"
// #include "TheAwakener_FO/GamePlay/Camera/AwCameraPawn.h"
#include "Components/WidgetComponent.h"
#include "GameFramework/PlayerController.h"
#include "TheAwakener_FO/GameFramework/Network/AwSessionInfo.h"
#include "Input/CmdComponent.h"
#include "Input/GameControlState.h"
#include "AwPlayerController.generated.h"

class AAwCharacter;

/**
 * 
 */

DECLARE_DYNAMIC_MULTICAST_DELEGATE(FChangeRoguePawn);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnPressKeyDelegate, FString, PressKey);

UCLASS()
class THEAWAKENER_FO_API AAwPlayerController : public APlayerController
{
	GENERATED_BODY()
private:
	FVector2D InputMove = FVector2D::ZeroVector;
	FRotator InputCamera = FRotator::ZeroRotator;
	float InputMoveDur = 0;
	FVector2D InputLeftStick = FVector2D::ZeroVector;
	FVector2D InputRightStick = FVector2D::ZeroVector;
	
	UFUNCTION(Server, Reliable)
	void AddInputKey(const FString& InputKey);
	UFUNCTION(Server, Reliable)
	void RemoveInputKey(const FString& InputKey);
	int LocalPCIndex = -1;
	bool IsGamepad = false;
	UPROPERTY(Category=PlayerWidget, VisibleAnywhere, BlueprintReadOnly, meta=(AllowPrivateAccess = "true"))
	UWidgetComponent* LockSignWidget = nullptr;

	void CreateWidgetComponent();
public:
	AAwPlayerController();
	UPROPERTY(BlueprintAssignable)
	FChangeRoguePawn OnChangeRoguePawn;
	UFUNCTION(BlueprintPure)
	bool GetIsGamepad(){return IsGamepad;}
	virtual void BeginPlay() override;
	virtual void OnPossess(APawn* InPawn) override;
	void SetupUI();
	void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

	virtual void Destroyed() override;
	
	virtual bool InputKey(const FInputKeyParams& Params) override;
	void UpdateGameInstanceIsGamepad(const FInputKeyParams& Params) const;
	virtual void Tick(float DeltaSeconds) override;
	
	// 根据 ActionCmd 添加键入。
	// 比如：添加 "Action1" 的键入
	// 添加 Pressed 的键入
	UFUNCTION(BlueprintCallable)
	void AddInputKeyByActionCmdId(FString ActionCmdId, bool IsByGamePad = true);

	UFUNCTION(BlueprintCallable)
	void AttachLockSignToMonster(AAwCharacter* Monster);
	UFUNCTION(BlueprintCallable)
	void UpdateLockSignOwnerPlayer();
	UFUNCTION()
	void HideLockSign() const;
	
	UFUNCTION(BlueprintCallable,BlueprintPure)
	int GetLocalPCIndex();
	UFUNCTION(BlueprintCallable,BlueprintPure)
	AAwPlayerState* GetAwPlayerState();
	
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	EGameControlState GameControlState = EGameControlState::Game;

	//UI操作状态   TODO:弃用
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	EGameUIControlState GameUIControlState = EGameUIControlState::MainUIState;
	
	UFUNCTION(BlueprintCallable)
	void SetInputMoveDir(FVector2D V2, FVector2D LeftStick);
	UFUNCTION(BlueprintCallable)
	void SetInputCameraDir(FRotator Rotator, FVector2D RightStick);
	
	FRotator GetInputCameraDir() const { return InputCamera; };
	
	UPROPERTY(BlueprintReadOnly, Category = "AW|PlayerController")
	bool CanInput = true;

	UFUNCTION(BlueprintCallable)
	void SetInputEnable(bool Enable);
	
	UPROPERTY(BlueprintReadWrite, ReplicatedUsing=OnRep_CurCharacter, Category = "AW|PlayerController")
	AAwCharacter* CurCharacter;
	UPROPERTY()
	AAwPlayerState* CurPlayerState;

	UFUNCTION()
	void OnRep_CurCharacter();

	// --- Init ---
	UPROPERTY()
	TArray<UCmdComponent*> ControlledCmdComps;

	void SetMyCharacter(AAwCharacter* NewCharacter);
	
	UFUNCTION(BlueprintCallable, Client, Reliable, Category = "AW|Camera")
	void InitInClient(AAwCharacter* NewCharacter);

	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Character")
	void InitPlayerCharacter(FTransform PlayerStart);
	// --- --- ---
	
	// --- Camera ---
	//把相机恢复到CameraPawn，一些UI会使用其他相机，需要还原
	void GiveBackCamera(float InSec = 1.0f);

	//获取以屏幕中心点射出的射线指到的人物或坐标，如果没有射到东西会尝试向下发射线检测地面，保存在GameState.AimCharacter和AimLocation中
	UFUNCTION(BlueprintCallable)
	void GetAimedTargetFromScreenCenter(float LineDistance);
	// --- --- ---

	// --- Show Pop Text --- TODO:需要移动到 UIManager 里面去
	UFUNCTION(BlueprintCallable, Category = "AW|UI")
	void ShowPopText(AAwCharacter* OnCharacter, const FString& Text, FLinearColor TextColor = FLinearColor::Red);
	UFUNCTION(BlueprintCallable)
	void CheckLockSign();
	void ShowPopText(USceneComponent* OnBox, const FString& Text, FLinearColor TextColor = FLinearColor::Red);

	UFUNCTION(NetMulticast, Reliable)
	void ClientShowPopText(AAwCharacter* OnCharacter, const FString& Text, FLinearColor TextColor = FLinearColor::Red);
	// --- --- ---

	// --- NetWork --- TODO:是否需要移动
	UFUNCTION(BlueprintCallable, Client, Reliable)
	void UpdateSessionInfoToCurClient(FAwSessionInfo SessionInfo);

	UFUNCTION(BlueprintCallable, Client, Reliable)
	void BreakOnlineLink(const FString& BreakReason);
	// --- --- ---
	
	// --- Dungeon ---// TODO:是否需要移动
	UFUNCTION(BlueprintCallable, Server, Reliable)
	void LoadAllDungeonLevelFinish();

	UFUNCTION(BlueprintCallable, Client, Reliable)
	void LoadDungeonAllRoomLevel();

	UFUNCTION(BlueprintCallable, Client, Reliable)
	void UnLoadAllDungeonRoomLevel();

	UFUNCTION(BlueprintCallable, Client, Reliable)
	void LeaveDungeon(const FString& DungeonName);
	// --- Dungeon ---//

	UFUNCTION(BlueprintCallable)
	void OnLockTargetSwitch(bool bSwitchLeft);

	UPROPERTY(BlueprintAssignable, BlueprintCallable, Category = "Events")
	FOnPressKeyDelegate OnPressKey;
};
