// Fill out your copyright notice in the Description page of Project Settings.


#include "AwTeamManager.h"

void UAwTeamManager::AddPlayer(AAwPlayerController* NewPlayer)
{
	if (!PlayerList.Contains(NewPlayer))
	{
		PlayerList.Add(NewPlayer);
	}
}

void UAwTeamManager::DeletePlayer(AAwPlayerController* DeletePlayer)
{
	if (PlayerList.Contains(DeletePlayer))
	{
		PlayerList.Remove(DeletePlayer);
	}
}

void UAwTeamManager::AddTeamMate(AAwCharacter* Character)
{
	if (!TeamMateList.Contains(Character))
	{
		TeamMateList.Add(Character);
	}
}

void UAwTeamManager::DeleteTeamMate(AAwCharacter* Character)
{
	if (TeamMateList.Contains(Character))
	{
		TeamMateList.Remove(Character);
	}
}
