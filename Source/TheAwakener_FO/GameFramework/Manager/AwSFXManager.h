// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "Components/AudioComponent.h"
#include "AwSFXManager.generated.h"

class UAwDataManager;

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UAwSFXManager : public UObject
{
	GENERATED_BODY()
public:
	//音量相关
	//主音量
	UPROPERTY(BlueprintReadOnly)
		float MainVolume = 1;
	//BGM音量
	UPROPERTY(BlueprintReadOnly)
		float BGMVolume = 1;
	//系统音音量
	UPROPERTY(BlueprintReadOnly)
		float SystemAudioVolume = 1;
	//音效音量
	UPROPERTY(BlueprintReadOnly)
		float SFXVolume = 1;
	//语音音量
	UPROPERTY(BlueprintReadOnly)
		float VoiceVolume = 1;
	//调整音量
	void SetMainVolume(float NewVolume);
	void SetBGMVolume(float NewVolume);
	void SetSystemAudioVolume(float NewVolume);
	void SetSFXVolume(float NewVolume);
	void SetVoiceVolume(float NewVolume);

	//获取音量
	float GetMainVolume() const {return MainVolume;}
	float GetBGMVolume() const {return BGMVolume;}
	float GetSystemAudioVolume() const {return SystemAudioVolume;}
	float GetSFXVolume() const {return SFXVolume;}
	float GetVoiceVolume() const {return VoiceVolume;}

	UFUNCTION()
	void Init(UAwDataManager* DataManager);
	
	//BGM
	UPROPERTY(BlueprintReadOnly)
	UAudioComponent* BGMAudio;
	//因为需要fadeout前一个BGM，所以后一个BGM的延迟播放
	UPROPERTY(BlueprintReadOnly)
	UAudioComponent* BGMDelayToPlay;

	//播放BGM
	void PlayBGM(USoundBase* Sound, float VolumeMultiplier, float PitchMultiplier, float StartTime, bool bPersistAcrossLevelTransition,
		float LastFadeOutTime = 1.0f);
	void PlayBgmByKey(FString Key, float VolumeMultiplier, float PitchMultiplier, float StartTime, bool bPersistAcrossLevelTransition,
		float LastFadeOutTime = 1.0f);
	//停止播放BGM
	void StopBGM(float FadeOutTime = 1.0f);
	//延迟播放BGM
	void DelayToPlayBGMTimer();

	// BGM单，所有的音乐
	UPROPERTY()
	TMap<FString, USoundBase*> BGMList;
	
	//系统音池
	UPROPERTY(BlueprintReadOnly)
	TArray<UAudioComponent*> SystemAudioPool;
	//系统音池最大容量
	UPROPERTY(BlueprintReadOnly)
	int SystemAudioPoolMaxSize = 10;

	//音效池(打击音、技能音效等)
	UPROPERTY(BlueprintReadOnly)
	TArray<UAudioComponent*> SFXPool;
	//音效池最大容量
	UPROPERTY(BlueprintReadOnly)
	int SFXPoolMaxSize = 25;

	//语音池
	UPROPERTY(BlueprintReadOnly)
	TArray<UAudioComponent*> VoicePool;
	//语音池最大容量
	UPROPERTY(BlueprintReadOnly)
	int VoicePoolMaxSize = 10;

	//去除系统音池里已结束的特效以及空值
	void UpdateSystemAudioPool();
	//去除音效池里已结束的特效以及空值
	void UpdateSFXPool();
	//去除音效池里已结束的特效以及空值
	void UpdateVoicePool();

	// 获取当前音效池里面的各种音效右几个
	TMap<FString, int> GetSFXNumberInPool();
	
	//根据优先级排序，关闭移除相应数量的低优先级系统音
	void RemoveMiniPrioritySystemAudio(int RemoveNum = 1);

	//根据优先级排序，关闭移除相应数量的低优先级音效
	void RemoveMiniPrioritySFX(int RemoveNum = 1);

	//根据优先级排序，关闭移除相应数量的低优先级语音
	void RemoveMiniPriorityVoice(int RemoveNum = 1);

	//播放系统音
	UAudioComponent* PlaySystemAudio(USoundBase* Sound, float VolumeMultiplier, float PitchMultiplier, float StartTime, bool bPersistAcrossLevelTransition);
	//停止所有系统音
	void StopAllSystemAudio(float FadeOutTime = 0.5f);

	//播放音效
	UAudioComponent* PlaySFXatLocation(USoundBase* Sound, FVector Location = FVector::ZeroVector, FRotator Rotation = FRotator::ZeroRotator,
		float VolumeMultiplier = 1, float PitchMultiplier = 1, float StartTime = 0, USoundAttenuation* SoundAttenuation = nullptr, USoundConcurrency* SoundConcurrency = nullptr);

	UAudioComponent* PlaySFXAttached(USoundBase* Sound, USceneComponent* AttachtoComponent,
		FName AttachPointName = FName("None"), FVector Location = FVector::ZeroVector, FRotator Rotation = FRotator::ZeroRotator, 
		EAttachLocation::Type LocationType = EAttachLocation::KeepRelativeOffset, bool StopWhenAttachedtoDestroyed = false,
		float VolumeMultiplier = 1, float PitchMultiplier = 1, float StartTime = 0, USoundAttenuation* SoundAttenuation = nullptr, USoundConcurrency* SoundConcurrency = nullptr);
	
	//停止所有音效
	void StopAllSFX(float FadeOutTime = 0.5f);

	//播放语音
	UAudioComponent* PlayVoiceatLocation(USoundBase* Sound, FVector Location = FVector::ZeroVector, FRotator Rotation = FRotator::ZeroRotator,
		float VolumeMultiplier = 1, float PitchMultiplier = 1, float StartTime = 0, USoundAttenuation* SoundAttenuation = nullptr, USoundConcurrency* SoundConcurrency = nullptr);

	UAudioComponent* PlayVoiceAttached(USoundBase* Sound, USceneComponent* AttachtoComponent,
		FName AttachPointName = FName("None"), FVector Location = FVector::ZeroVector, FRotator Rotation = FRotator::ZeroRotator, 
		EAttachLocation::Type LocationType = EAttachLocation::KeepRelativeOffset, bool StopWhenAttachedtoDestroyed = false,
		float VolumeMultiplier = 1, float PitchMultiplier = 1, float StartTime = 0, USoundAttenuation* SoundAttenuation = nullptr, USoundConcurrency* SoundConcurrency = nullptr);

	UAudioComponent* PlayVoice2D(USoundBase* Sound, float VolumeMultiplier = 1, float PitchMultiplier = 1, float StartTime = 0);

	//停止所有语音
	void StopAllVoice(float FadeOutTime = 0.5f);
	
private:
	//BGM延迟播放的TimerHamdle
	FTimerHandle MyTimerHandle;
};