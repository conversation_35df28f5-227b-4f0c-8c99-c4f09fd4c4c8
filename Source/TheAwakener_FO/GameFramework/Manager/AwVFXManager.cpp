// Fill out your copyright notice in the Description page of Project Settings.


#include "AwVFXManager.h"
#include "Kismet/GameplayStatics.h"


void UAwVFXManager::UpdatePool()
{
	TArray<int> RemovedList;
	for (int i = 0; i < PSCPool.Num(); i++)
	{
		if (PSCPool[i])
		{
			if (PSCPool[i]->HasCompleted())
			{
				RemovedList.Add(i);
			}
		}
		else
		{
			RemovedList.Add(i);
		}
	}
	for (int i = 0; i < RemovedList.Num(); i++)
	{
		PSCPool.RemoveAt(RemovedList[i] - i);
	}
}

UParticleSystemComponent* UAwVFXManager::GetMiniPriorityPSC()
{
	//ToDo:待确定特效优先级方法
	for (int i = 0; i < PSCPool.Num(); i++)
	{
		if (PSCPool[i])
		{
			return PSCPool[i];
		}
	}
	return nullptr;
}

void UAwVFXManager::RemoveMiniPriorityPSCS(int RemoveNum)
{
	for (int i = 0; i < RemoveNum; i++)
	{
		UParticleSystemComponent* RemovedPSC = GetMiniPriorityPSC();
		if (RemovedPSC)
		{
			PSCPool.Remove(RemovedPSC);
			RemovedPSC->Deactivate();
			RemovedPSC->ReleaseToPool();
		}
	}
}

UParticleSystemComponent* UAwVFXManager::CreateVFXatLocation(UParticleSystem* Template, FTransform Transform, bool AutoDestroy, bool AutoActivateSystem)
{
	UpdatePool();
	if (Template)
	{
		//UKismetSystemLibrary::PrintString(GWorld, FString("VFXPoolNum:").Append(FString::FromInt(PSCPool.Num())));
		if (PSCPool.Num() >= PoolMaxSize)
		{
			RemoveMiniPriorityPSCS(PSCPool.Num()- PoolMaxSize + 1);
		}
		if (PSCPool.Num() < PoolMaxSize)
		{
			UParticleSystemComponent* NewVFX = UGameplayStatics::SpawnEmitterAtLocation(GWorld, Template, Transform, AutoDestroy, EPSCPoolMethod::AutoRelease, AutoActivateSystem);
			if(NewVFX)
				PSCPool.Add(NewVFX);
			return NewVFX;
		}
	}
	return nullptr;
}

UParticleSystemComponent* UAwVFXManager::CreateVFXAttached(UParticleSystem* Template, USceneComponent* AttachToComponent, FName AttachPointName, FTransform Transform, EAttachLocation::Type LocationType, bool AutoDestroy, bool AutoActivateSystem)
{
	UpdatePool();
	if (Template)
	{
		if (PSCPool.Num() >= PoolMaxSize)
		{
			RemoveMiniPriorityPSCS(PSCPool.Num() - PoolMaxSize + 1);
		}
		if (PSCPool.Num() < PoolMaxSize)
		{
			UParticleSystemComponent* NewVFX = UGameplayStatics::SpawnEmitterAttached(Template, AttachToComponent, AttachPointName, Transform.GetLocation(), Transform.GetRotation().Rotator(), Transform.GetScale3D(), LocationType, AutoDestroy, EPSCPoolMethod::AutoRelease, AutoActivateSystem);
			if (NewVFX)
				PSCPool.Add(NewVFX);
			return NewVFX;
		}
	}
	return nullptr;
}
