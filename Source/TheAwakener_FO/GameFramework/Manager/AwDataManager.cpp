// Fill out your copyright notice in the Description page of Project Settings.


#include "AwDataManager.h"

#include "Misc/ConfigCacheIni.h"
#include "Misc/FileHelper.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GameData/MobProp.h"
#include "TheAwakener_FO/GameFramework/Input/ActionCmd.h"
#include "TheAwakener_FO/GamePlay/ApparatusCustom/MonsterReplaceMgr.h"
#include "TheAwakener_FO/GamePlay/DropLoot/DropLoot.h"
#include "TheAwakener_FO/GamePlay/Roguelike/BattleStyle/RogueBattleStyleUpgrade.h"

UAwDataManager::UAwDataManager()
{
	
}

void UAwDataManager::BeginDestroy()
{
	UObject::BeginDestroy();
	
	if (UAwGameInstance::Instance) 
		UAwGameInstance::Instance->DataManager = nullptr;
}

FCWaveMonsterSpawn UAwDataManager::FindWaveMonster(bool& IsFound, FString Id)
{
	if(WaveMonsterData.Contains(Id))
	{
		IsFound = true;
		return WaveMonsterData[Id];
	}
	IsFound = false;
	return FCWaveMonsterSpawn();
}

FCWaveEvent UAwDataManager::FindWaveEvent(bool& IsFound,FString Id)
{
	if(WaveEventData.Contains(Id))
	{
		IsFound = true;
		return WaveEventData[Id];
	}
	IsFound = false;
	return FCWaveEvent();
}

FBlueprintJsonObject UAwDataManager::ParseJsonPathToBpJsonObj(const FString JsonPath)
{
	const TSharedPtr<FJsonObject> JsonObject = ParseJsonPathToJsonObj(JsonPath);

	FBlueprintJsonObject Object;
	if (JsonObject.IsValid())
		Object.Object = JsonObject;
	return Object;
}

TSharedPtr<FJsonObject> UAwDataManager::ParseJsonPathToJsonObj(const FString JsonPath)
{
	FString JsonStr;
	FFileHelper::LoadFileToString(JsonStr, *JsonPath);

	TSharedPtr<FJsonObject> JsonObject = ParseJsonStrToJsonObj(JsonStr);
	
	if (!JsonObject)
		UE_LOG(LogTemp, Error, TEXT("[AW] Parse json (%s) failed"), *JsonPath);

	return JsonObject;
}

TSharedPtr<FJsonObject> UAwDataManager::ParseJsonStrToJsonObj(const FString JsonStr)
{
	TSharedPtr<FJsonObject> JsonObject = nullptr;
	
	const TSharedRef<TJsonReader<TCHAR>> JsonReader = TJsonReaderFactory<TCHAR>::Create(JsonStr);
	const bool bIsSe = FJsonSerializer::Deserialize(JsonReader, JsonObject);
	if (!bIsSe)
		UE_LOG(LogTemp, Error, TEXT("[AW] Parse json (%s) failed"), *JsonStr);
	
	return JsonObject;
}

FString UAwDataManager::GetJsonDirPath()
{
	FString Path =FPaths::ProjectContentDir() + "Json/MainMode";

	
	if (GetConfigValue("GameConfig","GameModeConfig","bRogueLike").Equals("true",ESearchCase::IgnoreCase))
	{
		Path =FPaths::ProjectContentDir() + "Json/RogueMode";
	}
	
	return Path;
}

/**
 * @brief 暂时使用简单的ini方式使用数据调试，之后需要改成正式的excel和json去控制数据
 */
FString UAwDataManager::GetConfigValue(const FString& ConfigName, const FString& Section, const FString& Key)
{
	FConfigFile* ConfigFile = GetConfigFile(ConfigName);
	if (ConfigFile != nullptr && ConfigFile->Num() > 0)
	{
		if (Key.IsEmpty())
		{
			UE_LOG(LogTemp, Error, TEXT("Key is NONE! (from: %s, Section: %s, Key: %s)"), *ConfigFile->Name.ToString(), *Section, *Key);
			return "";
		}

		if (!Section.IsEmpty())
		{
			const FConfigSection* TmpSection = ConfigFile->Find(Section);
			if (TmpSection && TmpSection->Contains(FName(*Key)))
				return TmpSection->Find(FName(*Key))->GetValue().Replace(TEXT("\\n"), TEXT("\n"));

			UE_LOG(LogTemp, Error, TEXT("Can not find the key! (from: %s, Section: %s, Key: %s)"), *ConfigFile->Name.ToString(), *Section, *Key);
			return "";
		}
		else
		{
			for (const TPair<FString, FConfigSection> TmpPair : *ConfigFile)
			{
				if (TmpPair.Value.Contains(FName(*Key)))
					return TmpPair.Value.Find(FName(*Key))->GetValue().Replace(TEXT("\\n"), TEXT("\n"));
			}
			UE_LOG(LogTemp, Error, TEXT("Can not find the key! (from: %s, Section: %s, Key: %s)"), *ConfigFile->Name.ToString(), *Section, *Key);
			return "";
		}
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("%s is null"), *ConfigFile->Name.ToString());
		return "";
	}
}

FString UAwDataManager::ConfigValue(const FString& ConfigName, const FString& Section, const FString& Key)
{
	FConfigFile* ConfigFile = new FConfigFile();
	const FString ConfigPath = FPaths::ProjectConfigDir() + ConfigName + TEXT(".ini");
	ConfigFile->Read(ConfigPath);
	
	if (ConfigFile != nullptr && ConfigFile->Num() > 0)
	{
		if (Key.IsEmpty())
		{
			UE_LOG(LogTemp, Error, TEXT("Key is NONE! (from: %s, Section: %s, Key: %s)"), *ConfigFile->Name.ToString(), *Section, *Key);
			return "";
		}

		if (!Section.IsEmpty())
		{
			const FConfigSection* TmpSection = ConfigFile->Find(Section);
			if (TmpSection && TmpSection->Contains(FName(*Key)))
				return TmpSection->Find(FName(*Key))->GetValue().Replace(TEXT("\\n"), TEXT("\n"));

			UE_LOG(LogTemp, Error, TEXT("Can not find the key! (from: %s, Section: %s, Key: %s)"), *ConfigFile->Name.ToString(), *Section, *Key);
			return "";
		}
		else
		{
			for (const TPair<FString, FConfigSection> TmpPair : *ConfigFile)
			{
				if (TmpPair.Value.Contains(FName(*Key)))
					return TmpPair.Value.Find(FName(*Key))->GetValue().Replace(TEXT("\\n"), TEXT("\n"));
			}
			UE_LOG(LogTemp, Error, TEXT("Can not find the key! (from: %s, Section: %s, Key: %s)"), *ConfigFile->Name.ToString(), *Section, *Key);
			return "";
		}
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("%s is null"), *ConfigFile->Name.ToString());
		return "";
	}
}

void UAwDataManager::ParseData()
{
	if (JsonObjsMap.Num() > 0)
	{
		UE_LOG(LogTemp, Warning, TEXT("[AW] JsonObjsMap has parsed!"));
		return;
	}

	FString JsonDirPath = GetJsonDirPath();
	UE_LOG(LogTemp, Log, TEXT("[AW] =============================="));
	UE_LOG(LogTemp, Log, TEXT("[AW] Begin to parse json data (%s)!"), *JsonDirPath);

	IFileManager& MyFileManager = IFileManager::Get();
	if (MyFileManager.DirectoryExists(*JsonDirPath))
	{
		TArray<FString> JsonPaths;
		MyFileManager.FindFilesRecursive(JsonPaths, *JsonDirPath, TEXT("*.json"), true, false, true);
		if (JsonPaths.Num() > 0)
		{
			UE_LOG(LogTemp, Log, TEXT("[AW] Find %d jsons in (%s)"), JsonPaths.Num(), *JsonDirPath);
			for (FString JsonPath : JsonPaths)
			{
				ReadJsonFile(JsonPath);
			}
		}
		else
			UE_LOG(LogTemp, Error, TEXT("[AW] Don't find jsons in (%s)"), *JsonDirPath);
	}
	else
		UE_LOG(LogTemp, Error, TEXT("[AW] Don't find the path (%s) "), *JsonDirPath);

	UE_LOG(LogTemp, Log, TEXT("[AW] Parse json completed!"));
	UE_LOG(LogTemp, Log, TEXT("[AW] =============================="));
}

void UAwDataManager::PrintAllTables()
{
	if (JsonObjsMap.Num() > 0)
	{
		TArray<FString> Keys;
		JsonObjsMap.GetKeys(Keys);
		for (FString Key : Keys)
			PrintTable(Key);
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("[AW] JsonObjsMap is empty!"));
	}
}

void UAwDataManager::PrintTable(FString JsonName)
{
	FString OutputString;
	TSharedRef<TJsonWriter<TCHAR, TPrettyJsonPrintPolicy<TCHAR>>> JsonWriter = TJsonWriterFactory<TCHAR, TPrettyJsonPrintPolicy<TCHAR>>::Create(&OutputString);
	bool bResult = FJsonSerializer::Serialize(JsonObjsMap[JsonName].Object.ToSharedRef(), JsonWriter);
	UE_LOG(LogTemp, Log, TEXT("[AW] Print ==> %s"), *JsonName);
	if (bResult)
	{
		UE_LOG(LogTemp, Log, TEXT("[AW] %s"), *OutputString);
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("[AW] Serialize failed!"));
	}
}

FBlueprintJsonObject UAwDataManager::GetTable(FString JsonName)
{
	TArray<FString> Keys;
	JsonObjsMap.GetKeys(Keys);
	if(Keys.Contains(JsonName))
		return JsonObjsMap[JsonName];
	
	UE_LOG(LogTemp, Error, TEXT("[AW] Don't find the table:%s"), *JsonName);
	FBlueprintJsonObject JsonObject;
	return JsonObject;
}

FMobModel UAwDataManager::GetMobModelById(FString Id, FString AlterId)
{
	if (!AlterId.IsEmpty() && MobModels.Contains(Id+AlterId))
		return MobModels[Id+AlterId];
	if (MobModels.Contains(Id))
		return MobModels[Id];
	UE_LOG(LogTemp, Error, TEXT("Can not find the MobModel: %s Alter:%s"), *Id,*AlterId);
	return FMobModel();
}

FMobDropLootInfo UAwDataManager::GetDropLootByMobId(FString Id, FString AlterId)
{
	if (!AlterId.IsEmpty() && MobDropLootInfos.Contains(Id+AlterId))
		return MobDropLootInfos[Id+AlterId];
	UE_LOG(LogTemp, Error, TEXT("Can not find the DropLoot: %s Alter:%s"), *Id,*AlterId);
	if (MobDropLootInfos.Contains(Id))
		return MobDropLootInfos[Id];

	return FMobDropLootInfo();
}
int UAwDataManager::GetMobExp(FString Id, FString AlterId)
{
	const FMobModel MobModel = GetMobModelById(Id,AlterId);
	const FMobProp MobProp = GetMobProp(Id,AlterId);
	if (MobProp.Id.IsEmpty() == false && MobProp.Exp >= 0)
		return MobProp.Exp;
	return MobModel.ExpGiven;
}

float UAwDataManager::GetAbilityElemDmgMultiplier(int AbilityLevel)
{
	if (ElemAbilityConfig.DamageMultipliers.Num()>AbilityLevel)
	{
		return ElemAbilityConfig.DamageMultipliers[AbilityLevel<4?AbilityLevel:3];
	}
	return 1;
}

FDefaultKeyMapping UAwDataManager::GetKeyMappingById(FString Id)
{
	FDefaultKeyMapping KeyMapping = FDefaultKeyMapping();
	if (DefaultKeyMappings.Contains(Id))
		KeyMapping = DefaultKeyMappings[Id];

	// TempKey Gamepad
	if (URogueGameSetting::GetRogueGameSettings()->GetTempKey_Gamepad().Contains(KeyMapping.ActionKey))
	{
		KeyMapping.Gamepad.Empty();
		KeyMapping.Gamepad.Add(URogueGameSetting::GetRogueGameSettings()->GetTempKey_Gamepad()[KeyMapping.ActionKey]);
	}
	// CustomKey Gamepad
	else if (URogueGameSetting::GetRogueGameSettings()->GetCustomKey_Gamepad().Contains(KeyMapping.ActionKey))
	{
		KeyMapping.Gamepad.Empty();
		KeyMapping.Gamepad.Add(URogueGameSetting::GetRogueGameSettings()->GetCustomKey_Gamepad()[KeyMapping.ActionKey]);
	}

	// TempKye Keyboard
	if (URogueGameSetting::GetRogueGameSettings()->GetTempKey_Keyboard().Contains(KeyMapping.ActionKey))
	{
		KeyMapping.Keyboard.Empty();
		KeyMapping.Keyboard.Add(URogueGameSetting::GetRogueGameSettings()->GetTempKey_Keyboard()[KeyMapping.ActionKey]);
	}
	// CustomKey Keyboard
	else if (URogueGameSetting::GetRogueGameSettings()->GetCustomKey_Keyboard().Contains(KeyMapping.ActionKey))
	{
		KeyMapping.Keyboard.Empty();
		KeyMapping.Keyboard.Add(URogueGameSetting::GetRogueGameSettings()->GetCustomKey_Keyboard()[KeyMapping.ActionKey]);
	}
	
	return KeyMapping;
}


FString UAwDataManager::GetDlcId(FString InTypeId)
{
	for (FRoleType Type : RoleCreation.RoguePawns)
		if (Type.Id == InTypeId)
		{
			return Type.DLCIdSteam;
		}
	return "";
}

FString UAwDataManager::GetVideoPath(FString Id)
{
	if (VideoPaths.Contains(Id))
		return VideoPaths[Id];
	return "";
}

TMap<FString, FRogueWeaponInfo> UAwDataManager::GetAllRogueWeaponInfo()
{
	return this->RogueWeaponInfos;
}

FRogueWeaponInfo UAwDataManager::GetRogueWeaponInfo(FString RogueWeaponInfoId)
{
	return this->RogueWeaponInfos[RogueWeaponInfoId];
}

TMap<FString, FClassWeapons> UAwDataManager::GetAllRogueClassWeapons()
{
	return this->ClassWeapons;
}

FClassWeapons UAwDataManager::GetRogueClassWeapons(FString ClassId)
{
	return this->ClassWeapons[ClassId];
}

TMap<FString, FWeaponAtkBox> UAwDataManager::GetAllRogueWeaponAtkBox()
{
	return this->RogueWeaponAtkBoxes;
}

FWeaponAtkBox UAwDataManager::GetWeaponAtkBoxes(const FString& WeaponId)
{
	if (this->RogueWeaponAtkBoxes.Contains(WeaponId))
		return this->RogueWeaponAtkBoxes[WeaponId];
	return FWeaponAtkBox();
}

FWeaponDefaultInfo UAwDataManager::GetWeaponDefaultInfo()
{
	return this->WeaponDefaultInfo;
}

TMap<FString, int> UAwDataManager::GetWeaponDefaultInfo_UnlockWeapon()
{
	return this->WeaponDefaultInfo.UnlockWeapons;
}

TMap<FString, FString> UAwDataManager::GetWeaponDefaultInfo_CurrWeapon()
{
	return this->WeaponDefaultInfo.CurrWeapon;
}

FDefaultConfig UAwDataManager::GetDefaultConfig()
{
	return this->DefaultConfig;
}

// FJsonFuncData UAwDataManager::SplitFuncNameAndParams(FString InString)
// {
// 	FJsonFuncData TempFuncParam;
// 	//ClassPath
// 	FString ClassPath;
// 	FString ParamsStr;
// 	if (!(InString.Split(".", &ClassPath, &ParamsStr)))
// 	{
// 		UE_LOG(LogTemp, Log, TEXT("拆分ClassPath失败"));
// 		return TempFuncParam;
// 	}
// 	//FuncName
// 	FString FunctionName;
// 	if (!(ParamsStr.Split("(", &FunctionName, &ParamsStr)))
// 	{
// 		UE_LOG(LogTemp, Log, TEXT("拆分FuncName失败"));
// 		return TempFuncParam;
// 	}
// 	TempFuncParam.FunctionName = FunctionName;
// 	//Params
// 	if (ParamsStr.Len() > 0)
// 	{
// 		FString DiscardString;
// 		ParamsStr.Split(")", &ParamsStr, &DiscardString);
// 		ParamsStr.ParseIntoArray(TempFuncParam.Params, TEXT(","), true);
// 	}
// 	return TempFuncParam;
// }

TArray<FLevelSelectionData> UAwDataManager::GetLevelsByType(ELevelType Type) const
{
	TArray<FLevelSelectionData> Result;
	
	for (const auto& LevelData : LevelSelections)
	{
		if (LevelData.Value.Type == Type)
		{
			Result.Add(LevelData.Value);
		}
	}
	
	return Result;
}

TArray<FLevelSelectionData> UAwDataManager::GetLevelsByGroupID(const FString& GroupID) const
{
	TArray<FLevelSelectionData> Result;
	
	for (const auto& LevelData : LevelSelections)
	{
		if (LevelData.Value.GroupdID == GroupID)
		{
			Result.Add(LevelData.Value);
		}
	}
	
	return Result;
}

FLevelSelectionData UAwDataManager::FindLevelByID(const FString& ID)
{
	if (LevelSelections.Contains(ID))
		return LevelSelections[ID];
	
	return FLevelSelectionData();
}

FLevelIntroduce UAwDataManager::FindLevelIntroByID(const FString& ID)
{
	if (LevelIntroduces.Contains(ID))
		return LevelIntroduces[ID];
	
	return FLevelIntroduce();
}

FConfigFile* UAwDataManager::GetConfigFile(const FString& ConfigName)
{
	if (ConfigFiles.Contains(ConfigName))
	{
		return ConfigFiles[ConfigName];
	}
	else
	{
		FConfigFile* ConfigFile = new FConfigFile();
		const FString ConfigPath = FPaths::ProjectConfigDir() + ConfigName + TEXT(".ini");
		ConfigFile->Read(ConfigPath);
		if (ConfigFile->Num() <= 0)
		{
			UE_LOG(LogTemp, Error, TEXT("Can not find the ConfigFile: %s"), *ConfigPath);
			return nullptr;
		}
		else
		{
			UE_LOG(LogTemp, Log, TEXT("Find Config(%s)! ConfigNum is %d"), *ConfigPath, ConfigFile->Num());
			ConfigFiles.Add(ConfigName, ConfigFile);
			return ConfigFiles[ConfigName];
		}
	}
}

float UAwDataManager::GetRandWeightByMF(EItemRarity Rarity,double MagicFoundRate)
{
	if (ItemRarityWeights.Contains(Rarity))
	{
		return ItemRarityWeights[Rarity].WeightByMf(MagicFoundRate);
	}

	return 0.0f;
}

FItemAffixModel UAwDataManager::GetAffixModel(FString Id)
{
	if (ItemInstanceAffixes.Contains(Id))
	{
		return ItemInstanceAffixes[Id];
	}
	return FItemAffixModel();
}

FItemInstanceModel UAwDataManager::GetItemInstanceModel(FString Id)
{
	if (ItemInstanceModels.Contains(Id))
	{
		return ItemInstanceModels[Id];
	}
	return FItemInstanceModel();
}

TMap<FString, FMobProp> UAwDataManager::GetAllMobProp()
{
	return MobProps;
}

FMobProp UAwDataManager::GetMobProp(FString Id,FString AlterId)
{
	if (AlterId.IsEmpty() && MobProps.Contains(Id))
		return MobProps[Id];
	if (MobProps.Contains(Id+AlterId))
		return MobProps[Id+AlterId];
	UE_LOG(LogTemp, Error, TEXT("Can not find the MobProp: %s Alter:%s"), *Id,*AlterId);
	if (MobProps.Contains(Id))
		return MobProps[Id];
	return FMobProp();
}

void UAwDataManager::ReadJsonFile(FString JsonPath)
{
	TSharedPtr<FJsonObject> JsonObj = ParseJsonPathToJsonObj(JsonPath);
	if (JsonObj)
	{
		//Aoe
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "AOE"))
		{
			FAOEModel AoeModel = FAOEModel::FromJson(JsonValue->AsObject());
			if (AoeModel.Id.IsEmpty())
			{
				continue;
			}
			AoeModels.Add(AoeModel.Id, AoeModel);
		}

		//Bullet
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "Bullet"))
		{
			FBulletModel BulletModel = FBulletModel::FromJson(JsonValue->AsObject());
			BulletModels.Add(BulletModel.Id, BulletModel);
		}

		//Buff
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "Buff"))
		{
			FBuffModel BuffModel = FBuffModel::FromJson(JsonValue->AsObject());
			if (BuffModel.Id.IsEmpty())
			{
				continue;
			}
			BuffModels.Add(BuffModel.Id, BuffModel);
		}

		//Class
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "Class"))
		{
			FBattleClassModel ClassInfo = FBattleClassModel::FromJson(JsonValue->AsObject());
			ClassInfos.Add(ClassInfo.Id, ClassInfo);
		}

		//Mob
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "Mob"))
		{
			FMobModel Mob = FMobModel::FromJson(JsonValue->AsObject());
			MobModels.Add(Mob.Id, Mob);
		}
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj,"ElemAbility"))
		{
			ElemAbilityConfig = FElemAbilityConfig::FromJson(JsonValue->AsObject());
		}
		//Shop
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "Shop"))
		{
			FTrading ThisShop = FTrading::FromJson(JsonValue->AsObject());
			Shops.Add(ThisShop.Id, ThisShop);
		}

		//BaseThingUIInfo
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "ThingUIInfo"))
		{
			FThingUIInfo ThingUIInfo = FThingUIInfo::FromJson(JsonValue->AsObject());
			BaseThingUIInfo.Add(ThingUIInfo);
		}
		
		//ActionToCmd
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "ActionCmd"))
		{
			FActionCmd ActionCmd = FActionCmd::FromJson(JsonValue->AsObject());
			if(!UAwGameInstance::Instance->isSurvivor && ActionCmd.Action.Equals("AwakeSkill"))continue;;
			ActionCmds.Add(ActionCmd.Action, ActionCmd);
		}

		// CanCustomActions
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "CanCustomAction"))
		{
			FCanCustomAction CanCustomAction = FCanCustomAction();
			CanCustomAction.KeyDesc = UDataFuncLib::AwGetStringField(JsonValue->AsObject(), "KeyDesc");
			CanCustomAction.ActionCmd = UDataFuncLib::AwGetStringField(JsonValue->AsObject(), "ActionCmd");
			CanCustomAction.ActionKeyMap = UDataFuncLib::AwGetStringField(JsonValue->AsObject(), "ActionKeyMap");
			CanCustomAction.CanChangeInGamepad = UDataFuncLib::AwGetBoolField(JsonValue->AsObject(), "CanChangeInGamepad", true);
			CanCustomActions.Add(CanCustomAction);
		}
		
		// CanUseKey_Gamepad
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "CanUseKey_Gamepad"))
			CanUseKey_Gamepad.Add(JsonValue->AsString());
		
		// CanUseKey_Keyboard
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "CanUseKey_Keyboard"))
			CanUseKey_Keyboard.Add(JsonValue->AsString());
		
		//DefaultKeyMapping
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "DefaultKeyMapping"))
		{
			FDefaultKeyMapping DefaultKeyMapping = FDefaultKeyMapping::FromJson(JsonValue->AsObject());
			DefaultKeyMappings.Add(DefaultKeyMapping.ActionKey, DefaultKeyMapping);
		}

		//Text
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "GameTexts"))
		{
			FString JKey = JsonValue->AsObject()->GetStringField("Key");
			if (JKey.IsEmpty() == false)
			{
				FString Chinese = JsonValue->AsObject()->GetStringField("Chinese");
				FString English = JsonValue->AsObject()->GetStringField("English");
				FString Korean = JsonValue->AsObject()->GetStringField("Korean");
				FString Japanese = JsonValue->AsObject()->GetStringField("Japanese");
				if (GameTexts_CN.Contains(JKey))
					GameTexts_CN[JKey] = Chinese;
				else
					GameTexts_CN.Add(JKey, Chinese);

				if (GameTexts_EN.Contains(JKey))
					GameTexts_EN[JKey] = English;
				else
					GameTexts_EN.Add(JKey, English);
				
				if (GameTexts_KR.Contains(JKey))
					GameTexts_KR[JKey] = Korean;
				else
					GameTexts_KR.Add(JKey, Korean);
				
				if (GameTexts_JP.Contains(JKey))
					GameTexts_JP[JKey] = Japanese;
				else
					GameTexts_JP.Add(JKey, Japanese);
				
			}
		}

		//VoiceAudio
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "VoiceAudio"))
		{
			FString JKey = JsonValue->AsObject()->GetStringField("Key");
			if (JKey.IsEmpty() == false)
			{
				FString Chinese = JsonValue->AsObject()->GetStringField("Chinese");
				FString English = JsonValue->AsObject()->GetStringField("English");
				if (DialogueAudios_CN.Contains(JKey))
					DialogueAudios_CN[JKey] = Chinese;
				else
					DialogueAudios_CN.Add(JKey, Chinese);

				if (DialogueAudios_EN.Contains(JKey))
					DialogueAudios_EN[JKey] = English;
				else
					DialogueAudios_EN.Add(JKey, English);
			}
		}
		
		//AIScript
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "AIScript"))
		{
			FAIScriptPart AIScript = FAIScriptPart::FromJson(JsonValue->AsObject());
			AIScripts.Add(AIScript.Id, AIScript);
		}

		//BaseActions
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "CharacterBaseAction"))
		{
			FString Id;
			if(JsonValue->AsObject()->TryGetStringField("Id", Id))
			{
				TArray<FActionInfo> Actions;
				
				for (TSharedPtr<FJsonValue> ActionJsonValue : UDataFuncLib::AwGetArrayField(JsonValue->AsObject(), "Actions"))
					Actions.Add(FActionInfo::FromJson(ActionJsonValue->AsObject()));
				
				if (BaseActionType.Contains(Id))
					BaseActionType[Id].Append(Actions);
				else
					BaseActionType.Add(Id, Actions);
			}
			else
				UE_LOG(LogTemp, Error, TEXT("Can not find [Id] in json [CharacterBaseAction] !"));
		}

		// RogueItemActions
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "RogueItemAction"))
		{
			FActionInfo ActionInfo = FActionInfo::FromJson(JsonValue->AsObject());
			RogueItemActions.Add(ActionInfo.Id, ActionInfo);
		}
		
		//UI上的SkillInfo
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "SkillInfo"))
		{
			FSkillInfo SkillInfo = FSkillInfo::FromJson(JsonValue->AsObject());
			SkillInfos.Add(SkillInfo.Id, SkillInfo);
		}

		//实际动作的skillinfo
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "ActionSkillInfo"))
		{
			FAwActionSkillInfo ActionSkillInfo = FAwActionSkillInfo::FromJson(JsonValue->AsObject());
			ActionSkillInfos.Add(ActionSkillInfo.Id, ActionSkillInfo);
		}
		
		//SceneItem
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "SceneItem"))
		{
			FSceneItemModel SceneItemModel = FSceneItemModel::FromJson(JsonValue->AsObject());
			SceneItemModels.Add(SceneItemModel.Id, SceneItemModel);
		}

		//Elemental Talent
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "ElementalTalent"))
		{
			FElementalTalent Tal = FElementalTalent::FromJson(JsonValue->AsObject());
			ElementalTalents.Add(Tal.Id, Tal);
		}
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "ElementalTalentDesc"))
		{
			FElementalTalentUIInfo Tal = FElementalTalentUIInfo::FromJson(JsonValue->AsObject());
			ElementalTalentUIInfos.Add(Tal.Id, Tal);
		}
		
		//Trigger
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "Trigger"))
		{
			FAwTrigger TriggerModel = FAwTrigger::FromJson(JsonValue->AsObject());
			TriggerModels.Add(TriggerModel.Id, TriggerModel);
		}
		
		//Equipments
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "Equipment"))
		{
			FEquipment Equ = FEquipment::FromJson(JsonValue->AsObject());
			Equipments.Add(Equ.Id, Equ);
		}
		
		//Weapons
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "Weapon"))
		{
			FWeaponModel Wea = FWeaponModel::FromJson(JsonValue->AsObject());
			WeaponModels.Add(Wea.Id, Wea);
		}
		//Items
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "Item"))
		{
			FItemModel Item = FItemModel::FromJson(JsonValue->AsObject());
			Items.Add(Item.Id, Item);
		}
		//DungeonRoomLevelInfo
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "DungeonRoomLevel"))
		{
			FDungeonRoomLevelInfo LevelInfo = FDungeonRoomLevelInfo::FromJson(JsonValue->AsObject());
			DungeonRoomLevels.Add(LevelInfo.LevelPath, LevelInfo);
		}
		//DungeonRoadInfo
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "DungeonRoad"))
		{
			FDungeonRoadModel DungeonRoad = FDungeonRoadModel::FromJson(JsonValue->AsObject());
			DungeonRoadModels.Add(DungeonRoad.MeshPath, DungeonRoad);
		}

		//MapInfo
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "MapInfo"))
		{
			FAwMapInfo NewMapInfo = FAwMapInfo::FromJson(JsonValue->AsObject());
			MapInfos.Add(NewMapInfo.LevelPath, NewMapInfo);
		}
		
		//DungeonInfo
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "DungeonInfo"))
		{
			FAwDungeonModel DungeonInfo = FAwDungeonModel::FromJson(JsonValue->AsObject());
			DungeonInfos.Add(DungeonInfo.Id, DungeonInfo);
		}

		//AI 选动作
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "MobActionValue"))
		{
			FAIPickActionInfo AInfo = FAIPickActionInfo::FromJson(JsonValue->AsObject());
			AIPickActionInfos.Add(AInfo.Id, AInfo);
		}

		//Rogue AI 选动作
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "RogueMobActionValue"))
		{
			FRogueAIPickActionInfo AInfo = FRogueAIPickActionInfo::FromJson(JsonValue->AsObject());
			RogueAIPickActionInfos.Add(AInfo.Id, AInfo);
		}
		
		// LootPackage
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "LootPackage"))
		{
			FLootPackage LootPackage = FLootPackage::FromJson(JsonValue->AsObject());
			LootPackages.Add(LootPackage.Id, LootPackage);
		}
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "DropLoots"))
		{
			FMobDropLootInfo MobLootInfo = FMobDropLootInfo::FromJson(JsonValue->AsObject());
			MobDropLootInfos.Add(MobLootInfo.MobId, MobLootInfo);
		}
		// ActionSelection
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "ActionSelections"))
		{
			FActionSelection ActionSelection = FActionSelection::FromJson(JsonValue->AsObject());
			ActionSelections.Add(ActionSelection.Id, ActionSelection);
		}
		// ActionLink
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "ActionLink"))
		{
			FActionLink ActionLink = FActionLink::FromJson(JsonValue->AsObject());
			ActionLinks.Add(ActionLink.Id, ActionLink);
		}
		// ActionSelectionUI
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "ActionSelectionUI"))
		{
			FActionSelectionUIInfo ActionSelection = FActionSelectionUIInfo::FromJson(JsonValue->AsObject());
			ActionSelectionUIInfo.Add(ActionSelection.Id, ActionSelection);
		}
		// ActionLinkUI
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "ActionLinkUI"))
		{
			FActionLinkUIInfo ActionLink = FActionLinkUIInfo::FromJson(JsonValue->AsObject());
			ActionLinkUIInfo.Add(ActionLink.Id, ActionLink);
		}

		//WidgetInfo
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "WidgetInfo"))
		{
			FAwWidgetInfo WidgetInfo = FAwWidgetInfo::FromJson(JsonValue->AsObject());
			WidgetInfos.Add(WidgetInfo.Id, WidgetInfo);
		}

		//Achievements
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "Achievements"))
		{
			FAchievementModel Ach = FAchievementModel::FromJson(JsonValue->AsObject());
			if (Ach.Id.IsEmpty() == false)	AchievementModels.Add(Ach);
		}

		//Camps
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "Camps"))
		{
			FCharacterCamp Camp = FCharacterCamp::FromJson(JsonValue->AsObject());
			CampInfos.Add(Camp.CampId, Camp);
		}

		//Dialog DialogModels
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "Dialogs"))
		{
			FDialogScriptModel Dialog = FDialogScriptModel::FromJson(JsonValue->AsObject());
			DialogModels.Add(Dialog.Id, Dialog);
		}

		//Dialog DialogBubbles
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "DialogBubbles"))
		{
			FDialogBubbleGroupMobel DialogBubbleGroup = FDialogBubbleGroupMobel::FromJson(JsonValue->AsObject());
			DialogBubbleGroups.Add(DialogBubbleGroup.DialogBubbleGroupID, DialogBubbleGroup);
		}

		//DebugConfig
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "DebugConfig"))
		{
			DebugConfig = FDebugConfig::FromJson(JsonValue->AsObject());
		}
		//关卡怪物配置
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "WaveMonster"))
		{
			FCWaveMonsterSpawn Event = FCWaveMonsterSpawn::FromJson(JsonValue->AsObject());
			WaveMonsterData.Add(Event.Id, Event);
		}
		//关卡阶段配置
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "WaveEvent"))
		{
			FCWaveEvent Event = FCWaveEvent::FromJson(JsonValue->AsObject());
			WaveEventData.Add(Event.Id, Event);
		}
		
		//CanUseDebug
		if (JsonObj->HasField("CanUseDebug")) 
			CanUseDebug = UDataFuncLib::AwGetBoolField(JsonObj, "CanUseDebug", false);
		
		//DefaultConfig
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "DefaultConfig"))
		{
			DefaultConfig = FDefaultConfig::FromJson(JsonValue->AsObject());
		}

		//RoleCreation
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "RoleCreation"))
		{
			RoleCreation = FRoleCreation::FromJson(JsonValue->AsObject());
		}
		//New Role Items
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "RoleInit"))
		{
			FNewRoleItem NewRole = FNewRoleItem::FromJson(JsonValue->AsObject());
			NewRoleItems.Add(NewRole.CharacterType, NewRole);
		}

		//ListItems
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "FListItemElementInfo"))
		{
			FListItemElementInfo ListItemElementInfo = FListItemElementInfo::FromJson(JsonValue->AsObject());
			
			ListAllItems.Add(ListItemElementInfo.ListId,ListItemElementInfo);
		}

		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "ClassIcon"))
		{
			FClassIcon WeaponIcon = FClassIcon::FromJson(JsonValue->AsObject());

			PlayerClassIcons.Add(WeaponIcon.Id,WeaponIcon);
		}

		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "ItemIcon"))
		{
			FItemIcon ItemIcon = FItemIcon::FromJson(JsonValue->AsObject());

			ItemIcons.Add(ItemIcon.Id,ItemIcon);
		}

		// ui audio
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "UIAudio"))
		{
			FUIAudio UIAudio = FUIAudio::FromJson(JsonValue->AsObject());

			UIAudios.Add(UIAudio.AudioId,UIAudio);
		}

		// BgmList
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "BgmList"))
		{
			FString Key =  UDataFuncLib::AwGetStringField(JsonValue->AsObject(), "Key", "");
			FString Path = UDataFuncLib::AwGetStringField(JsonValue->AsObject(), "Path", "");
			BgmList.Add(Key, Path);
		}
		
		// EquipmentSets
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "EquipSet"))
		{
			FEquipmentSet EquipSet = FEquipmentSet::FromJson(JsonValue->AsObject());
			if (!EquipSet.Id.IsEmpty())
				EquipmentSets.Add(EquipSet.Id, EquipSet);
		}

		// CreateBigMapNeedVaribles
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "FCreateBigMapNeedVaribles"))
		{
			FCreateBigMapNeedVariable CreateBigMapNeedVariable = FCreateBigMapNeedVariable::FromJson(JsonValue->AsObject());
			CreateBigMapNeedVaribles.Add(CreateBigMapNeedVariable.MapId,CreateBigMapNeedVariable);
		}

		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "FSubtitleGroup"))
		{
			FSubtitleGroup SubtitleGroup = FSubtitleGroup::FromJson(JsonValue->AsObject());
			SubtitleGroups.Add(SubtitleGroup.SubtitleGroupId,SubtitleGroup);
		}

		//QuestTargets
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "QuestTargetInfo"))
		{
			FAwQuestTarget QuestTarget = FAwQuestTarget::FromJson(JsonValue->AsObject());
			QuestTargets.Add(QuestTarget.Id, QuestTarget);
		}
		//Quest must behind Targets
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "QuestInfo"))
		{
			FAwQuest Quest = FAwQuest::FromJson(JsonValue->AsObject());
			Quests.Add(Quest.Id, Quest);
		}

		//---------------幸存者相关------------------
		//rogue relic
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "RelicInfo_Survivor"))
		{
			FAwRelicInfo RelicInfo = FAwRelicInfo::FromJson(JsonValue->AsObject());
			if (RelicInfo.Id.IsEmpty())
			{
				continue;
			}
			RelicInfos_Survivor.Add(RelicInfo.Id, RelicInfo);
		}
		//必须在道具初始化后面
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "RogueItem_Survivor"))
		{
			FAwRogueItemInfo RogueItemInfo = FAwRogueItemInfo::FromJson(JsonValue->AsObject());
			if (RogueItemInfo.Item.Model.Id.IsEmpty())
			{
				continue;
			}
			RogueItemInfos_Survivor.Add(RogueItemInfo.Item.Model.Id, RogueItemInfo);
		}
		// RogueBattleUpgrade 肉鸽角色战斗强化
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "BattleUpgrade_Survivor"))
		{
			FRogueBattleUpgrade BattleUpgrade = FRogueBattleUpgrade::FromJson(JsonValue->AsObject());
			this->RogueBattleUpgrades_Survivor.Add(BattleUpgrade.Id, BattleUpgrade);
		}
		
		//---------------肉鸽相关------------------
		//rogue relic
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "RelicInfo"))
		{
			FAwRelicInfo RelicInfo = FAwRelicInfo::FromJson(JsonValue->AsObject());
			if (RelicInfo.Id.IsEmpty())
			{
				continue;
			}
			RelicInfos.Add(RelicInfo.Id, RelicInfo);
		}
		//必须在道具初始化后面
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "RogueItem"))
		{
			FAwRogueItemInfo RogueItemInfo = FAwRogueItemInfo::FromJson(JsonValue->AsObject());
			if (RogueItemInfo.Item.Model.Id.IsEmpty())
			{
				continue;
			}
			RogueItemInfos.Add(RogueItemInfo.Item.Model.Id, RogueItemInfo);
		}
		//rogue RoomStep Config
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "RougeRoomStepConfig"))
		{
			FRogueRoomStepInfo RoomStepConfig = FRogueRoomStepInfo::FromJson(JsonValue->AsObject());
			RogueRoomStepConfig.Add(RoomStepConfig.RoomStep, RoomStepConfig);
		}
		//rogue Cleared RoomStep Config
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "RougeClearedRoomStepConfig"))
		{
			FRogueRoomStepInfo RoomStepConfig = FRogueRoomStepInfo::FromJson(JsonValue->AsObject());
			RogueClearedRoomStepConfig.Add(RoomStepConfig.RoomStep, RoomStepConfig);
		}
		//rogue talent
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "RogueTalentInfo"))
		{
			FAwRogueTalentInfo TalentInfo = FAwRogueTalentInfo::FromJson(JsonValue->AsObject());
			RogueTalentInfos.Add(TalentInfo.Id, TalentInfo);
		}
		//rogue Level Info
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "RougeLevelInfo"))
		{
			FRogueLevelInfo LevelInfo = FRogueLevelInfo::FromJson(JsonValue->AsObject());
			RogueLevelList.Add(LevelInfo.LevelName, LevelInfo);
		}
		//Rogue Room Relic Drop
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "RougeRoomRelicDrop"))
		{
			FAwRoomRelicDrop Info = FAwRoomRelicDrop::FromJson(JsonValue->AsObject());
			RogueRoomRelicDrop.Add(Info.Id, Info);
		}
		
		// RogueBattleStyle 肉鸽的战斗风格
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "RogueBattleStyle"))
		{
			FRogueBattleStyle BattleStyle = FRogueBattleStyle::FromJson(JsonValue->AsObject());
			this->RogueBattleStyle.Add(BattleStyle.Id, BattleStyle);
		}

		// RogueBattleStyleUpgrade 肉鸽战斗风格强化
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "BattleStyleUpgrade"))
		{
			TSharedPtr<FJsonObject> UpgradeObject = JsonValue->AsObject();
			FString BattleStyle = UDataFuncLib::AwGetStringField(UpgradeObject, "BattleStyle");
			TMap<FString, FRogueBattleStyleUpgrade> Upgrades;
			for (TSharedPtr<FJsonValue> UpgradeValue : UDataFuncLib::AwGetArrayField(UpgradeObject, "Upgrades"))
			{
				FRogueBattleStyleUpgrade Upgrade = FRogueBattleStyleUpgrade::FromJson(UpgradeValue->AsObject());
				Upgrades.Add(Upgrade.Id, Upgrade);
			}
			this->RogueBattleStyleUpgrade.Add(BattleStyle, Upgrades);
		}

		// AbilityLevelWeights
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "AbilityLevelWeight"))
		{
			FAbilityLevelWeight Res = FAbilityLevelWeight();
			Res.RoomType = UDataFuncLib::AwGetEnumField(JsonValue->AsObject(),"RoomType",ERogueRoomType::Normal);
			Res.MinLevel = UDataFuncLib::AwGetNumberField(JsonValue->AsObject(),"MinLevel",0);
			Res.MaxLevel = UDataFuncLib::AwGetNumberField(JsonValue->AsObject(),"MaxLevel",0);
			Res.Id =  UDataFuncLib::EnumToFString(Res.RoomType) + "_"+FString::FromInt(Res.MinLevel)+"_"+FString::FromInt(Res.MaxLevel );
			Res.Lv1 = UDataFuncLib::AwGetNumberField(JsonValue->AsObject(), "Lv1", 1);
			Res.Lv2 = UDataFuncLib::AwGetNumberField(JsonValue->AsObject(), "Lv2", 1);
			Res.Lv3 = UDataFuncLib::AwGetNumberField(JsonValue->AsObject(), "Lv3", 1);
			this->AbilityLevelWeights.Add(Res);
		}

		// ActionLv3Buffs
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "ActionLv3Buffs"))
		{
			FActionLv3Buffs Res;
			Res.Id = UDataFuncLib::AwGetStringField(JsonValue->AsObject(), "Id", "");
			TSharedPtr<FJsonObject> ElementBuff = JsonValue->AsObject()->GetObjectField("ElementBuff");
			Res.ElementBuff.Add(EAbilityElement::Fire, UDataFuncLib::AwGetStringField(ElementBuff, "Fire", ""));
			Res.ElementBuff.Add(EAbilityElement::Ice, UDataFuncLib::AwGetStringField(ElementBuff, "Ice", ""));
			Res.ElementBuff.Add(EAbilityElement::Wind, UDataFuncLib::AwGetStringField(ElementBuff, "Wind", ""));
			Res.ElementBuff.Add(EAbilityElement::Thunder, UDataFuncLib::AwGetStringField(ElementBuff, "Thunder", ""));
			Res.ElementBuff.Add(EAbilityElement::Light, UDataFuncLib::AwGetStringField(ElementBuff, "Light", ""));
			Res.ElementBuff.Add(EAbilityElement::Darkness, UDataFuncLib::AwGetStringField(ElementBuff, "Darkness", ""));

			this->ActionLv3Buffs.Add(Res.Id, Res);
		}

		// RogueBattleUpgrade 肉鸽角色战斗强化
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "BattleUpgrade"))
		{
			FRogueBattleUpgrade BattleUpgrade = FRogueBattleUpgrade::FromJson(JsonValue->AsObject());
			this->RogueBattleUpgrades.Add(BattleUpgrade.Id, BattleUpgrade);
		}

		// VideoPath
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "VideoPath"))
		{
			FString Id = UDataFuncLib::AwGetStringField(JsonValue->AsObject(), "Id", "");
			FString Path = UDataFuncLib::AwGetStringField(JsonValue->AsObject(), "Path", "");
			this->VideoPaths.Add(Id, Path);
		}

		// RogueWeaponInfos
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "RogueWeaponInfo"))
		{
			FRogueWeaponInfo RogueWeaponInfo = FRogueWeaponInfo::FromJson(JsonValue->AsObject());
			this->RogueWeaponInfos.Add(RogueWeaponInfo.Id, RogueWeaponInfo);
		}

		// RogueWeaponAtkBoxes
		if (JsonObj->HasField("WeaponAtkBox"))
		{ 
			for (TTuple<FString, TSharedPtr<FJsonValue, ESPMode::ThreadSafe>> JsonValue : JsonObj->GetObjectField("WeaponAtkBox")->Values)
			{
				FWeaponAtkBox Res;
				Res.Id = JsonValue.Key;
				for (const TSharedPtr<FJsonValue> Each : JsonValue.Value->AsArray())
				{
					FString Id = UDataFuncLib::AwGetStringField(Each->AsObject(), "Id", "");
					FString ChangeTo = UDataFuncLib::AwGetStringField(Each->AsObject(), "ChangeTo", "");
					Res.ChangeToAttackBox.Add(Id, ChangeTo);	
				}
				this->RogueWeaponAtkBoxes.Add(Res.Id, Res);
			}
		}

		// ClassWeapons
		if (JsonObj->HasField("ClassWeapons"))
		{
			for (TTuple<FString, TSharedPtr<FJsonValue, ESPMode::ThreadSafe>> JsonValue : JsonObj->GetObjectField("ClassWeapons")->Values)
			{
				FClassWeapons Res;
				Res.Id = JsonValue.Key;
				for (const TSharedPtr<FJsonValue> Each : JsonValue.Value->AsArray())
					Res.Weapons.Add(Each->AsString());
				
				this->ClassWeapons.Add(Res.Id, Res);
			}
		}

		// WeaponDefaultInfo
		if (JsonObj->HasField("WeaponDefaultInfo"))
		{
			TSharedPtr<FJsonObject> WeaponDefaultJsonObj = JsonObj->GetObjectField("WeaponDefaultInfo");
			
			for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(WeaponDefaultJsonObj, "UnlockWeapons"))
			{
				FString Id = UDataFuncLib::AwGetStringField(JsonValue->AsObject(),"Id", "");
				int Level = UDataFuncLib::AwGetNumberField(JsonValue->AsObject(), "Level", 0);
				this->WeaponDefaultInfo.UnlockWeapons.Add(Id, Level);
			}

			for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(WeaponDefaultJsonObj, "CurrWeapon"))
			{
				FString ClassId = UDataFuncLib::AwGetStringField(JsonValue->AsObject(),"ClassId", "");
				FString WeaponId = UDataFuncLib::AwGetStringField(JsonValue->AsObject(), "WeaponId", "");
				this->WeaponDefaultInfo.CurrWeapon.Add(ClassId, WeaponId);
			}
		}

		//房间出怪数据
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "RogueRoomWaveGroup"))
		{
			FRogueRoomWaveGroup GroupInfo = FRogueRoomWaveGroup::FromJson(JsonValue->AsObject());
			this->RogueRoomWaveGroups.Add(GroupInfo.GroupId, GroupInfo);
		}

		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "RogueRoomMobInfo"))
		{
			FRogueRoomMobInfo RoomMobInfo = FRogueRoomMobInfo::FromJson(JsonValue->AsObject());
			this->RogueRoomWaveInfos.Add(RoomMobInfo);
		}
		//暗黑式装备数据
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "ItemInstanceModel"))
		{
			FItemInstanceModel ItemInstanceModel = FItemInstanceModel::FromJson(JsonValue->AsObject());
			this->ItemInstanceModels.Add(ItemInstanceModel.Id,ItemInstanceModel);
		}
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "ItemAffixInfo"))
		{
			FItemAffixModel ItemAffixModel = FItemAffixModel::FromJson(JsonValue->AsObject());
			this->ItemInstanceAffixes.Add(ItemAffixModel.Id,ItemAffixModel);
		}
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "ItemRarityWeight"))
		{
			FItemRarityMultiplier ItemAffixModel = FItemRarityMultiplier::FromJson(JsonValue->AsObject());
			this->ItemRarityWeights.Add(ItemAffixModel.Rarity,ItemAffixModel);
		}

		// --------- 表数据修改表 ---------
		
		// MobProp
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "MobProp"))
		{
			FMobProp MobProp = FMobProp::FromJson(JsonValue->AsObject());
			MobProps.Add(MobProp.Id, MobProp);
		}
		
		//---------------肉鸽结束------------------

		//---------------幸 存 者------------------
		// LevelSelections
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "LevelSelections"))
		{
			FLevelSelectionData LevelSelection = FLevelSelectionData::FromJson(JsonValue->AsObject());
			LevelSelections.Add(LevelSelection.ID, LevelSelection);
		}
		// LevelIntroduce
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "LevelIntroduces"))
		{
			FLevelIntroduce LevelIntroduce = FLevelIntroduce::FromJson(JsonValue->AsObject());
			LevelIntroduces.Add(LevelIntroduce.ID, LevelIntroduce);
		}
	}
}
