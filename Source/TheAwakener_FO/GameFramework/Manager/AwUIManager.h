// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/FunctionLibrary/ResourceFuncLib.h"
#include "TheAwakener_FO/GamePlay/Roguelike/BattleStyle/RogueBattleTag.h"
#include "TheAwakener_FO/UI/LoadingUI.h"
#include "TheAwakener_FO/UI/Toast.h"
#include "TheAwakener_FO/UI/HUD/MessageDialog.h"
#include "TheAwakener_FO/UI/HUD/PopText.h"
#include "TheAwakener_FO/UI/Roguelike/Common/CmdTipPopUp.h"
#include "UObject/Object.h"
#include "AwUIManager.generated.h"

class UAwGameInstance;
class AAwPlayerController;
class AAwGameState;
class UGameMain;

// 触发 BattleTag 的多播委托
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnCheckRougeBattleTag, ERogueBattleTag, Tag);

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UAwUIManager : public UObject
{
	GENERATED_BODY()
	UPROPERTY()
	UToast* Toast;


	UPROPERTY()
	float ScreenCenterY;
	UPROPERTY()
	UGameMain* MainHUD;

	AAwPlayerController* GetMyPlayerController();
	
public:
	UPROPERTY(BlueprintReadWrite)
	TMap<FString, FString> AllWidgetsPath = TMap<FString, FString>();
	
	//只有确定和取消的对话框
	UPROPERTY()
	UMessageDialog* ConfirmDialog;
	
	UPROPERTY(BlueprintReadWrite)
	UUserWidget* FocusedWidget;
	UPROPERTY(BlueprintReadWrite)
	TMap<FString, UUserWidget*> OpenedWidgets = TMap<FString, UUserWidget*>();
	
	UPROPERTY()
	UPopText* PopText;
	
	UPROPERTY()
	AAwPlayerController* OwnerPC;
	UFUNCTION()
	void InitUI(AAwPlayerController* PC);
	
	template<typename T>
	T* LoadUserWidget(FString Path)
	{
		T* UI = nullptr;
		FString UIPath = UResourceFuncLib::GetWbpAssetPath(Path);
		UClass* Class = LoadClass<T>(nullptr, *UIPath);

		if (Class)
			UI = Cast<T>(CreateWidget<UUserWidget>(GetMyPlayerController(), Class));
		else
		{
			UE_LOG(LogTemp, Error, TEXT("Load WBP failed! (path: %s )"), *UIPath);
			UKismetSystemLibrary::PrintString(this,UIPath.Append(" : Load Failed!"));
		}
		
		return UI;
	};

	UFUNCTION(BlueprintCallable)
	void ShowToast(FString TextKey);

	// 打开 提示 UI
	UFUNCTION(BlueprintCallable)
	void ShowPrompt(FString Message, FString ActionId = "");

	// 关闭 提示 UI
	UFUNCTION(BlueprintCallable)
	void HidePrompt();

	// 当前 提示 UI 的目标完成
	UFUNCTION(BlueprintCallable)
	void PromptMissionClear();
	
	/**
	 * 显示一个只有Yes和Cancel的对话框
	 * @param TextKey 对话框文字的key，指向Chinese的内容
	 * @param Yes 是Yes按钮要执行的所有回调函数 (TArray<FString> Params)=>void
	 * @param Cancel 是Cancel按钮要执行的所有回调 (TArray<FString> Params)=>void
	 * @param YesKey 确定按钮的字儿
	 * @param CancelKey 否定按钮上的字儿
	 */
	UFUNCTION(BlueprintCallable)
	void ShowMessageDialog(FString TextKey, TArray<FString> Yes, TArray<FString> Cancel, FString YesKey = "MessageDialog_Yes", FString CancelKey = "MessageDialog_Cancel");

	UFUNCTION(BlueprintCallable)
	void HideMessageDialog();
	
	UFUNCTION(BlueprintCallable)
	FString GetWbpPathById(FString Id);

	UFUNCTION(BlueprintCallable)
	UUserWidget* ShowNewUI(FString UIName, int ZOrder = 0);
	UFUNCTION(BlueprintCallable)
	void ShowNewUserWidget(FString UIName, UUserWidget* NewUserWidget);
	UFUNCTION(BlueprintCallable)
	UUserWidget* Show(FString UIName, int ZOrder = 0);
	UFUNCTION(BlueprintCallable,BlueprintPure)
	UUserWidget* Find(FString UIName,bool& bFound);
	UFUNCTION(BlueprintCallable)
	void Hide(FString UIName);
	UFUNCTION(BlueprintCallable)
	void HideWithAnimation(FString UIName);
	UFUNCTION(BlueprintCallable)
	void SetFocus(FString UIName);
	
	UFUNCTION(BlueprintCallable)
	void SetAllOpenedWidgetsVisible(ESlateVisibility SlateVisibility);
	
	UPROPERTY()
	ULoadingUI* LoadingUI;
	UFUNCTION(BlueprintCallable)
	void ShowLoading();
	UFUNCTION(BlueprintCallable)
	void HideLoading();

	UFUNCTION(BlueprintCallable)
	void SetGameMainVisibility(ESlateVisibility Visibility);
	

	//因为在Widget的NativeTick中拿不到Gamestate,所以在这里存一个GameState引用给Widget使用
	UPROPERTY()
	AAwGameState* AwGameState;
	
	// void OpenLoadingUI();
	// void OpenTitleUI();
	// void OpenMenu();

	// 触发 BattleTag 的多播委托
	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnCheckRougeBattleTag OnCheckRougeBattleTag;

	UFUNCTION(blueprintCallable, category = "Events")
	void TriggerRogueBattleTag(ERogueBattleTag Tag) const;

	UFUNCTION(BlueprintCallable)
	void OnTriggerRougeBattleTag_ByFStr(FString Str);

	UFUNCTION(BlueprintCallable)
	void ShowCmdTipPopUp(TArray<FRogueCmdTipInfo> CmdTipInfo, float Duration);

	UFUNCTION(BlueprintCallable)
	void HideCmdTipPopUp();
};
