// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "AwDataManager.h"
#include "TheAwakener_FO/GameFramework/Base/AwGameInstance.h"
#include "TheAwakener_FO/GamePlay/Roguelike/RogueCardInfo.h"
#include "TheAwakener_FO/GamePlay/Roguelike/BattleStyle/RogueBattleStyle.h"
#include "TheAwakener_FO/GamePlay/Roguelike/Item/AwRogueItemSubSystem.h"
#include "TheAwakener_FO/GamePlay/Roguelike/Map/RogueMapChallenge.h"
#include "TheAwakener_FO/GamePlay/Roguelike/Map/RogueMapConfig.h"
#include "TheAwakener_FO/GamePlay/Thing/ItemInstance.h"
#include "AwRogueDataSystem.generated.h"

class UAwRogueSaveGame;

USTRUCT(BlueprintType)
struct FRogueShopReward
{
	GENERATED_BODY()

public:
	//是否激活商店存档
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool isActive = false;
	
	// 圣遗物Id列表
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FString> RelicList;
	
	// 法器Id列表
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TMap<FString,int> ItemList;

	//动作列表
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FRougeAbilityLevelInfo> ActionList;

	//商品价格信息表
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TMap<FString,FThingObj> PropPriceList;

	//商品信息表
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TMap<FString,FThingObj> PropGoodsList;

	//遗物刷新次数
	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	int RelicRefreshCount = 1;

	//遗物刷新基础消耗
	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	int RelicRefreshCost = 50;

	//法器刷新消耗
	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	int ItemRefreshCount = 1;

	//法器刷新基础消耗
	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	int ItemRefreshCost = 50;

	//技能刷新消耗
	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	int SkillRefreshCount = 1;

	//技能刷新基础消耗
	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	int SkillRefreshCost = 50;
};

USTRUCT(BlueprintType)
struct FRandomReward
{
	GENERATED_BODY()

public:
	// 是否已拿取
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool IsGot = false;
	
	// 0:遗物
	// 1:法器
	// 2:动作
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int RewardType = -1;

	// 随机出来的遗物或法器的 Id 和 等级
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TMap<FString,int> Rewards;
	
	// 随机出来的动作
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
    TArray<FRougeAbilityLevelInfo> ActionRewards;
};

// 当前房间奖励，随机出来的
USTRUCT(BlueprintType)
struct FRogueRoomReward
{
	GENERATED_BODY()

public:
	// 金币数量，-1则已拿取
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int CoinCount = 0;
	// 额外的金币数量，-1则已拿取
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int ExtraCoinCount = 0;
	// 残响数量，-1则已拿取
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int KeyCount = 0;
	
	// 魂晶数量，-1则已拿取
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int SoulCount = 0;

	// 神之碎片数量，-1则已拿取
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int ShardCount = 0;
	
	// 遗物 & 法器
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FRandomReward> Rewards;
	
	// 商店数据结构
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FRogueShopReward ShopInfo;
	
	// 祈祷的遗物信息
	// 按照顺序
	// 0-火 伊格尼
	// 1-水 伊尔姆
	// 2-雷 波尔提克
	// 3-风 赞提娅
	// 4-光 阿泽姆
	// 5-暗 艾敏达妮斯
	// 6-人 基础、属性、等
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TMap<FString,FRandomReward> PrayReward;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool UpgradeHealingPotionIsActive = false;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool PrayRewardsIsActive = false;

	FRogueRoomReward()
	{
		FRandomReward Relic = FRandomReward();
		Relic.RewardType = 0;
		Rewards.Add(Relic);
		FRandomReward Item = FRandomReward();
		Item.RewardType = 1;
		Rewards.Add(Item);
		FRandomReward Action = FRandomReward();
        Action.RewardType = 2;
        Rewards.Add(Action);
	}
};

// 战斗风格强化奖励
USTRUCT(BlueprintType)
struct FBattleStyleUpgradeReward
{
	GENERATED_BODY()

	// 点数
	// 普通房间增加1点
	// 精英和Boss房增加2点
	// 商店和升级房间不增加
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int Point = 0;

	// 点数上限（1~3）
	// 每次满了之后都会涨一点，最高3点
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int MaxPoint = 1;
	
	// 随机到的强化id
	// 为空就是没有随机过
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FString> RandomUpgradeIds;

	// 是否获得过点数
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool HasGotPoint = false;
	
	// 是否获得过强化
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool HasGot = false;
};

USTRUCT(BlueprintType)
struct FUpgradeInfos
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FString> Ids;
};

USTRUCT(BlueprintType)
struct FBattleAbilityIds
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString NormalAttack = "";
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString Ground1 = "";
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FString Ground2 = "";
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString Air1 = "";
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString Air2 = "";
};

USTRUCT(BlueprintType)
struct FUnlockAbilityInfo
{
	GENERATED_BODY()

	// 解锁的技能 Ids
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FString> UnlockAbilityIds;
};

// 当前的战斗信息
USTRUCT(BlueprintType)
struct FBattleDataInfo
{
	GENERATED_BODY()

	// 是否有战斗信息，开始进入战斗房间即开启
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool IsActive = false;
	
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int RevivedChanceHasUse = 0;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int RevivedChance = 0;
	
	// 本次战斗的总时长
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float CurBattleTime = 0.0f;

	// 使用的角色Id（职业Id）
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString PawnClassId = "Swordsman_Gerasso";

	// P2使用的角色Id（职业Id）
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString PawnClassId2 = "BladeDancer_Henrik";
	// 局内货币
	// Rogue_Coin
	// Rogue_Soul
	// Rogue_Key
	// Rogue_Shard
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TMap<FString, int> Currency;
	
	// 已获得的遗物
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TMap<FString, int> RelicHasGot;

	// 血瓶
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FAwRogueItemInfo HealingPotion;

	// 当前一号主动道具
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FAwRogueItemInfo MainItem;

	// 当前二号主动道具
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FAwRogueItemInfo SecondItem;
	
	// 波数，或者叫房间数
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int Step = 0;
	// 已经经过的关卡
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FString> UsedLeveList;
	// 当前关卡名称
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString LevelName = "";
	// 房间名称
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString RoomName = "";
	// 当前房间的类型
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	ERogueRoomType RoomType = ERogueRoomType::Normal;
	// 当前房间的奖励类型
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<ERogueRoomReward> RoomReward;
	// 当前房间是否清除怪物
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool IsClearThisRoom = false;
	// 当前挑战房间是否挑战成功
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool IsChallengeRoomSuccess = false;
	// 当前房间奖励数据
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FRogueRoomReward RogueRoomReward;
	//下个房间的随机数据
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FRogueRoomInfo> NextRoomList;
	//已经随出过的精英房数量
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int HasRandEliteRoomNum = 0;
	//已经随出过的挑战房数量
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int HasRandChallengeRoomNum = 0;

	//当前战斗血量
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int HP = 0;

	//当前战斗觉醒值
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int AP = 0;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float ItemEnergy = 0;
	
	// 战斗风格强化信息
	// FString - 战斗风格Id BattleStyleId
	// TArray<FString> - 该战斗风格强化了那些？ Id的集合
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TMap<FString, FUpgradeInfos> BattleStyleUpgradeInfo;

	// 战斗风格强化奖励
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FBattleStyleUpgradeReward BattleStyleUpgradeReward;
	
	// --- 角色战斗强化 ---
	// 角色当前使用的技能
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FBattleAbilityIds CurBattleAbilityIds;

	// 带等级 带元素 的角色技能
	// 0 - NormalAttack
	// 1 - Ground1
	// 2 - Ground2
	// 3 - Air1
	// 4 - Air2
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TMap<ERogueAbilitySlot, FRougeAbilityLevelInfo> AbilityLevelInfos;
	
	// 替换掉的技能Id
	// 被放弃的技能的权重改为1
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FString> GiveUpBattleAbilityIds;

	// 战斗强化列表
	// <UpgradeId, Level>
	// level 0~4 (1级为0)
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TMap<FString, int> BattleUpgradeIds;

	//杀死的boss数量
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int TotalKilledBossNum = 0;

	//杀死的精英数量
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int TotalKilledEliteNum = 0;

	//杀死的小怪数量
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int TotalKilledNormalNum = 0;

	//挑战房通过次数
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int ChallengeTimes = 0;

	//当前的难度挑战
	UPROPERTY(BlueprintReadWrite)
	TMap<ERogueChallengeType, int> Rogue_ChallengeList;

	//初始祝福是否已选
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool SelectedStartBless = false;

	FBattleDataInfo()
	{
		// 使用的角色Id（职业Id）
		// P2使用的角色Id（职业Id）
		if (UAwDataManager::ConfigValue("GameConfig","GameModeConfig","bSurvivor").Equals("true",ESearchCase::IgnoreCase))
		{
			PawnClassId = "WuJiang_Kwang";
			PawnClassId2 = "Jianke_Tang";
		}
		else
		{
			PawnClassId = "Swordsman_Gerasso";
			PawnClassId2 = "BladeDancer_Henrik";
		}
	}
};

// 各职业的战斗记录
USTRUCT(BlueprintType)
struct FRogueClassRecord
{
	GENERATED_BODY()

	//该职业总游戏时间
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float TotalGameTime = 0.0f;

	//该职业最快通关时间
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float FastestClearTime = 0.0f;

	//该职业通关时最多圣遗物数量
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int MaxClearRelicNum = 0;

	//该职业的最高单次伤害
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int MaxDamage = 0;

	//该职业通关次数
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int ClearedGameTimes = 0;

	//该职业的游戏次数
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int TotalGameTimes = 0;

	//该职业的经过的房间总数
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int TotalRoomNum = 0;

	//该职业的杀死的boss数量
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int TotalKilledBossNum = 0;

	//该职业的杀死的精英数量
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int TotalKilledEliteNum = 0;

	//该职业的杀死的小怪数量
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int TotalKilledNormalNum = 0;

	//该职业的挑战房通过次数
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int ChallengeTimes = 0;

	//最高通关难度等级
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int DifficultyClearLevel = 0;
};

// 战斗记录
USTRUCT(BlueprintType)
struct FBattleRecord
{
	GENERATED_BODY()

	//各个职业的战斗记录(Key = ClassId)
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TMap<FString,FRogueClassRecord> ClassRecords;
	
	//总游戏时间
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float TotalGameTime = 0.0f;

	//最快通关时间
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float FastestClearTime = 0.0f;

	//通关时最多圣遗物数量
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int MaxClearRelicNum = 0;

	// 总通关次数
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int ClearCount = 0;

	// 总游戏次数
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int GameCount = 0;
	
	//经过的房间总数
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int TotalRoomNum = 0;

	//杀死的boss数量
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int TotalKilledBossNum = 0;

	//杀死的精英数量
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int TotalKilledEliteNum = 0;

	//杀死的小怪数量
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int TotalKilledNormalNum = 0;

	//挑战房通过次数
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int ChallengeTimes = 0;

	FBattleRecord()
	{
		ClassRecords.Add("Swordsman_Gerasso",FRogueClassRecord());
		ClassRecords.Add("Warrior_Tierdagon",FRogueClassRecord());
		ClassRecords.Add("BladeDancer_Henrik",FRogueClassRecord());
		ClassRecords.Add("Spearman_Sola",FRogueClassRecord());
		ClassRecords.Add("WuJiang_Kwang",FRogueClassRecord());
		ClassRecords.Add("Jianke_Tang",FRogueClassRecord());
		ClassRecords.Add("Warrior_Caelynn",FRogueClassRecord());
		ClassRecords.Add("CiKe_Yin",FRogueClassRecord());
		ClassRecords.Add("Monk_Wukong",FRogueClassRecord());
		//ClassRecords.Add("Warrior_Caelynn", FRogueClassRecord());
	}
};

// Rogue存档
USTRUCT(BlueprintType)
struct FRogueDataInfo
{
	GENERATED_BODY()

	// 是否是新存档
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool IsNew = true;
	
	// 当前选择的角色Id（职业Id）
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString CurPawnClassId = "Swordsman_Gerasso";

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString CurPawnClassIdP2 = "BladeDancer_Henrik";
	// 使用的皮肤ID
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TMap<FString, int> SkinId;
	
	// 解锁的皮肤信息
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TMap<int, bool> UnlockSkins;
	
	// 关系到游戏整体进度的Switch
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TMap<FString, int> Switch;

	// 货币
	// Rogue_Coin
	// Rogue_Soul
	// Rogue_Key
	// Rogue_Shard
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TMap<FString, int> Currency;
	
	// 天赋
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TMap<FString,int> UnlockTalent;

	// 当前选择觉醒技能Id
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FString> AwakeSkillId = {};
	// 已解锁的觉醒技能Id
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FString> UnLockAwakeSkills;

	// 已解锁的职业Id
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FString> UnLockClassId;

	// 各个角色对应的战斗风格
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TMap<FString, FString> BattleStyle;

	// 已经解锁的技能信息
	// Key - 角色 Id
	// Value - 解锁的技能信息
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TMap<FString, FUnlockAbilityInfo> UnlockAbilityId;
	
	// 战斗记录
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FBattleRecord BattleRecord = FBattleRecord();

	// 当前战斗数据
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FBattleDataInfo CurBattleDataInfo = FBattleDataInfo();

	//大厅里预设的初始动作
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TMap<FString, FBattleAbilityIds> PreviewAbilityInfos;

	//遗物是否已获取过（图鉴）
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TMap<FString,int> HasGotRelic;

	//法器是否已获取
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TMap<FString,int> HasGotMagicItem;

	//是否已通关(一周目，用于解锁31关)
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool bGameCleared = false;

	// 当前各个职业装备的武器Id
	// ClassId - WeaponId
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TMap<FString, FString> CurrWeapon;
	
	// 解锁的武器信息，Id - 等级
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TMap<FString, int> UnlockWeapons;
	// 暗黑式武器信息 - 仓库
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FItemInstance> InventoryItemInstances;
	// 暗黑式武器信息 - 携带 0 武器 1-2-3 饰品
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FItemInstance> EquippedItemInstances;
	UPROPERTY()
	double ItemInstanceLastID = 2;

	void InitDefualtItemInstance();

	FRogueDataInfo()
	{
		//展会版货币先给1000，正式版都填0
		Currency.Add("Rogue_Soul", 0);
		Currency.Add("Rogue_Key", 0);
		Currency.Add("Rogue_Shard", 0);
		UnLockClassId.Add("Swordsman_Gerasso");
		InitDefualtItemInstance();
		// UnLockClassId.Add("WuJiang_Kwang");
		// UnLockClassId.Add("Warrior_Tierdagon");
		// UnLockClassId.Add("BladeDancer_Henrik");
		// UnLockClassId.Add("Spearman_Sola");
		//解锁职业功能做完后，下面3个职业就不默认添加了
		//UnLockClassId.Add("Warrior_Tierdagon");
		//UnLockClassId.Add("BladeDancer_Henrik");
		//UnLockClassId.Add("Spearman_Sola");
		
		//展会版先把觉醒技能全部解锁
		UnLockAwakeSkills.Add("Earthquake");
		//UnLockAwakeSkills.Add("BloodlyThirsty");
		//UnLockAwakeSkills.Add("Berserker");
		//UnLockAwakeSkills.Add("DragonSummon");
		
		//EA版Switch
		//Switch.Add("VersionEA",1);
		if (UAwDataManager::ConfigValue("GameConfig","GameModeConfig","bSurvivor").Equals("true",ESearchCase::IgnoreCase))
		{
			CurPawnClassId = "WuJiang_Kwang";
			CurPawnClassIdP2 = "Jianke_Tang";
		}
		else
		{
			CurPawnClassId = "Swordsman_Gerasso";
			CurPawnClassIdP2 = "BladeDancer_Henrik";
		}
	}
};

USTRUCT(BlueprintType)
struct FSettingDataInfo
{
	GENERATED_BODY()

	// 语言
	FString Language = "Chinese";
	// 手柄图标类型
	FString GamepadButtonType = "PlayStation";

	// 音量相关
	// 主音量
	UPROPERTY(BlueprintReadOnly)
	float MainVolume = 1.0f;
	// BGM音量
	UPROPERTY(BlueprintReadOnly)
	float BGMVolume = 1.0f;
	// 系统音音量
	UPROPERTY(BlueprintReadOnly)
	float SystemAudioVolume = 1.0f;
	// 音效音量
	UPROPERTY(BlueprintReadOnly)
	float SFXVolume = 1.0f;
	// 语音音量
	UPROPERTY(BlueprintReadOnly)
	float VoiceVolume = 1.0f;
	// UI音效音量
	UPROPERTY(BlueprintReadOnly)
	float UISoundVolume = 1.0f;
};

/**
 * Rogue 存档系统
 */
UCLASS()
class THEAWAKENER_FO_API UAwRogueDataSystem : public UGameInstanceSubsystem
{
	GENERATED_BODY()
private:
	//旧存档名字为"AwRogueGameSaveData"
	FString Aw_Rogue_GameSaveData = "AwRogueGameSaveData_2";


public:
	// 选择了第几个数据，现在默认为0
	int RogueDataIndex = 0;
	// 幸存者单局进度的Switch
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TMap<FString, int> SwitchRoundSvl;
	// 游戏数据
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	UAwRogueSaveGame* RogueSaveGame;
	
	UAwRogueDataSystem()
	{
		if (UAwDataManager::ConfigValue("GameConfig","GameModeConfig","bSurvivor").Equals("true",ESearchCase::IgnoreCase))
		{
			Aw_Rogue_GameSaveData = "AwBTGameSave";
		}
		else
		{
			Aw_Rogue_GameSaveData = "AwRogueGameSaveData_2";
		}
	}

	// 初始化
	void InitSubSystem();
	
	// 读取存档
	UFUNCTION(BlueprintCallable)
	UAwRogueSaveGame* LoadSaveData(bool& HaveSaveFile);
	// 保存存档
	UFUNCTION(BlueprintCallable)
	void SaveData();
	// 删除存档
	UFUNCTION(BlueprintCallable)
	bool DeleteSaveData(int Index = 0);

	// 大厅初始化角色
	// 重新获取角色效果（天赋，遗物，觉醒技能，技能A，技能B）
	UFUNCTION(BlueprintCallable)
	void PostLoadSaveDataInHall(int PlayerIndex);
	// 战斗房间初始化角色
	// 重新获取角色效果（天赋，遗物，觉醒技能，技能A，技能B）
	UFUNCTION(BlueprintCallable)
	void PostLoadSaveDataInBattle(int PlayerIndex);
	
	UFUNCTION(BlueprintCallable)
	void StartBattle();
	UFUNCTION(BlueprintCallable)
	void ClearBattle();
	
	// Check一下，没有存档的话新建一个初始化存档
	void CheckRogueSaveGame();

	// 获得当前游戏存档
	UFUNCTION(BlueprintPure)
	FRogueDataInfo& GetCurRogueData(bool& hasData);
	// 更具ID获取当前游戏存档（目前不做多存档模式）
	UFUNCTION(BlueprintPure)
	FRogueDataInfo& GetCurRogueDataByIndex(bool& hasData);
	// 获取设置存档
	UFUNCTION(BlueprintPure)
	FSettingDataInfo GetSettingData();

	// 是否拥有游戏存档
	UFUNCTION(BlueprintPure)
	bool HasCurRogueData() const;

	// 存档是否是新的
	UFUNCTION(BlueprintPure)
	bool GetIsNew() const;
	// 设置存档是否是新的 2025-6-25 没起作用
	UFUNCTION(BlueprintCallable)
	void SetNotNew() const;

	// 当前使用的角色Id（大厅里使用）
	UFUNCTION(BlueprintPure)
	FString GetCurPawnClassId(int index) const;
	
	// 当前使用的角色Id（大厅里使用）
	UFUNCTION(BlueprintCallable)
	void SetCurPawnClassId(FString PawnClassId,int PlayerIndex);

	UFUNCTION(BlueprintCallable)
	int GetCurSkinId(FString ClassId) const;
	UFUNCTION(BlueprintCallable)
	void SetCurSkinId(int SkinId,FString ClassId);

	// 游戏的整体Switch
	UFUNCTION(BlueprintPure)
	TMap<FString, int> GetAllSwitch() const;
	// 游戏的整体Switch
	UFUNCTION(BlueprintPure)
	int GetSwitch(FString Key) const;
	// 游戏的整体Switch
	UFUNCTION(BlueprintCallable)
	void SetSwitch(FString Key, int Value);
	// 游戏的整体Switch
	UFUNCTION(BlueprintCallable)
	void RemoveSwitch(FString Key);
	//获取是否通关(通过一周目，关卡从30关变成31关)
	UFUNCTION(BlueprintPure)
	bool GetIsClearedGame();
	//设置已通关
	UFUNCTION(BlueprintCallable)
	void SetIsClearedGame(bool bCleared);

	// ---------- Currency ----------
	// 通货类资源都归1P
	UFUNCTION(BlueprintPure)
	TMap<FString, int> GetAllCurrencyCount(bool bIsInBattle = false) const;
	// 魂晶-Rogue_Soul，钥匙-Rogue_Key，金币-Rogue_Coin，神之碎片-Rogue_Shard
	UFUNCTION(BlueprintPure)
	int GetCurrencyCount(FString Key, bool bIsInBattle = false) const;
	// 魂晶-Rogue_Soul，钥匙-Rogue_Key，金币-Rogue_Coin，神之碎片-Rogue_Shard
	UFUNCTION(BlueprintCallable)
	bool AddCurrencyCount(int Count, FString Key, bool bIsInBattle = false) const;
	// 魂晶-Rogue_Soul，钥匙-Rogue_Key，金币-Rogue_Coin，神之碎片-Rogue_Shard
	UFUNCTION(BlueprintCallable)
	void SetCurrencyCount(int Count, FString Key, bool bIsInBattle = false) const;
	
	// --- Soul ---
	/**
	 * @brief 获取 Rogue Soul 数量
	 * @param bIsInBattle 是否在战斗中
	 * @return 返回数量
	 */
	UFUNCTION(BlueprintPure)
	int GetCurrency_Soul(bool bIsInBattle = false) const;
	/**
	 * @brief 增减 Rogue Soul 
	 * @param Count 增减的数量
	 * @param bIsInBattle 是否在战斗中
	 * @return 返回增减是否成功
	 */
	UFUNCTION(BlueprintCallable)
	bool AddCurrency_Soul(int Count, bool bIsInBattle = false) const;
	// --- --- ---
	
	// --- Key ---
	/**
	 * @brief 获取 Rogue Key 数量
	 * @param bIsInBattle 是否在战斗中
	 * @return 返回数量
	 */
	UFUNCTION(BlueprintPure)
	int GetCurrency_Key(bool bIsInBattle = false) const;
	/**
	 * @brief 增减 Rogue Key 
	 * @param Count 增减的数量
	 * @param bIsInBattle 是否在战斗中
	 * @return 返回增减是否成功
	 */
	UFUNCTION(BlueprintCallable)
	bool AddCurrency_Key(int Count, bool bIsInBattle = false) const;
	// --- --- ---

	// --- Shard ---
	/**
	 * @brief 获取 Rogue Shard 数量
	 * @param bIsInBattle 是否在战斗中
	 * @return 返回数量
	 */
	UFUNCTION(BlueprintPure)
	int GetCurrency_Shard(bool bIsInBattle = false) const;
	/**
	 * @brief 增减 Rogue Shard
	 * @param Count 增减的数量
	 * @param bIsInBattle 是否在战斗中
	 * @return 返回增减是否成功
	 */
	UFUNCTION(BlueprintCallable)
	bool AddCurrency_Shard(int Count, bool bIsInBattle = false) const;
	// --- --- ---
	
	// --- Coin ---
	/**
	 * @brief 获取 Rogue Soul 数量
	 * @return 返回数量
	 */
	UFUNCTION(BlueprintPure)
	int GetCurrency_Coin() const;
	/**
	 * @brief 增减 Rogue Soul 
	 * @param Count 增减的数量
	 * @return 返回增减是否成功
	 */
	UFUNCTION(BlueprintCallable)
	bool AddCurrency_Coin(int Count) const;
	// 设置战斗中金币数量
	UFUNCTION(BlueprintCallable)
	void SetCurrency_Coin(int Count) const;
	// --- --- ---
	// ---------- ---------- ----------
	
	// 已经解锁的天赋
	UFUNCTION(BlueprintPure)
	TMap<FString, int> GetAllUnlockTalent() const;
	// 已经解锁的天赋
	UFUNCTION(BlueprintPure)
	int GetUnlockTalent(FString Key) const;
	// 已经解锁的天赋
	UFUNCTION(BlueprintCallable)
	void SetUnlockTalent(FString Key, int Value);
	UFUNCTION(BlueprintCallable)
	void ClearUnlockTalent();

	//设置觉醒技能Id
	UFUNCTION(BlueprintCallable)
	void SetAwakeSkillId(FString Id,int PlayerIndex);

	//设置解锁的觉醒技能Id
	void SetUnlockAwakeSkill(TArray<FString> Ids);
	
	// 战斗记录
	UFUNCTION(BlueprintPure)
	FBattleRecord GetBattleRecord() const;
	
	// 获得职业使用次数
	UFUNCTION(BlueprintPure)
	int GetBattleCount(FString PawnClassId) const;
	// 职业使用次数+1
	UFUNCTION(BlueprintCallable)
	void AddBattleCount(FString PawnClassId);

	// 获得该职业的通关次数
	UFUNCTION(BlueprintPure)
	int GetClearCount(FString PawnClassId) const;
	// 获得所有职业的通关总次数
	UFUNCTION(BlueprintPure)
	int GetAllClearCount() const;
	// 该职业的通关次数+1
	UFUNCTION(BlueprintCallable)
	void AddClearCount(FString PawnClassId);

	//通关难度
	UFUNCTION(BlueprintPure)
	int GetBattleClearDifficulty(FString PawnClassId) const;
	UFUNCTION(BlueprintPure)
	int GetMaxBattleClearDifficulty(FString PawnClassId) const;
	UFUNCTION(BlueprintCallable)
	void AddBattleClearDifficulty(FString PawnClassId);
	UFUNCTION(BlueprintCallable)
	void SetBattleClearDifficulty(FString PawnClassId,int NewClearDifficulty);
	//获得当前战斗的诅咒等级
	UFUNCTION(BlueprintPure)
	int GetDifficultyLevel_CurBattle();
	
	// 把当前战斗记录数据添加到总的战斗记录中（在主界面放弃该次游戏、死亡、通关时调用）
	UFUNCTION(BlueprintCallable)
	void SetCurBattleDataToClassRecord(bool bClearedGame);

	// 当前战斗信息
	UFUNCTION(BlueprintPure)
	FBattleDataInfo GetCurBattleData() const;
	// 当前战斗信息
	UFUNCTION(BlueprintCallable)
	void SetCurBattleData(FBattleDataInfo NewBattleDataInfo);

	// 当前战斗信息是否开启
	UFUNCTION(BlueprintPure)
	bool GetCurBattleDataIsActive() const;
	// 当前战斗信息是否开启
	UFUNCTION(BlueprintCallable)
	void SetCurBattleDataIsActive(bool IsActive);

	// 获取当前战斗时长
	UFUNCTION(BlueprintPure)
	float GetCurBattleTime();
	// 设置当前战斗时长
	UFUNCTION(BlueprintCallable)
	void SetCurBattleTime(float BattleTime);

	//增加当前游戏杀死的BOSS数量
	UFUNCTION(BlueprintCallable)
	void AddCurBattleKillBossNum();

	//增加当前游戏杀死的BOSS数量
	UFUNCTION(BlueprintCallable)
	void AddCurBattleKillEliteNum();

	//增加当前游戏杀死的BOSS数量
	UFUNCTION(BlueprintCallable)
	void AddCurBattleKillNormalNum();

	//增加当前游戏通过挑战的次数
	UFUNCTION(BlueprintCallable)
	void AddCurBattleChallengeTime();
	
	// 当前战斗的角色Id
	UFUNCTION(BlueprintPure)
	FString GetPawnClassId_CurBattle(int PlayerIndex) const;
	// 当前战斗的角色Id
	UFUNCTION(BlueprintCallable)
	void SetPawnClassId_CurBattle(FString PawnClassId,int PlayerIndex);
	
	// 当前战斗已经使用的复活次数
	UFUNCTION(BlueprintPure)
	int GetRevivedChanceHasUse_CurBattle() const;
	// 当前战斗已经使用的复活次数
	UFUNCTION(BlueprintCallable)
	void SetRevivedChanceHasUse_CurBattle(int Num);

	// 当前战斗最大的复活次数
	UFUNCTION(BlueprintPure)
	int GetRevivedChance_CurBattle() const;
	// 当前战斗最大的复活次数
	UFUNCTION(BlueprintCallable)
	void SetRevivedChance_CurBattle(int Num);
	
	
	// 获取所有当前战斗的遗物
	UFUNCTION(BlueprintPure)
	TMap<FString, int> GetAllRelicHasGet_CurBattle() const;
	// 获取某个当前战斗的遗物
	UFUNCTION(BlueprintPure)
	int GetRelicHasGet_CurBattle(FString RelicId) const;
	// 设置某个当前战斗的遗物
	UFUNCTION(BlueprintCallable)
	void SetRelicHasGet_CurBattle(FString RelicId, int Count);
	// 去除某个当前战斗的遗物
	UFUNCTION(BlueprintCallable)
	void RemoveRelicHasGet_CurBattle(FString RelicId);
	// 清空当前战斗的遗物
	UFUNCTION(BlueprintCallable)
	void ClearRelicHasGet_CurBattle();
	
	// 当前战斗的血瓶
	UFUNCTION(BlueprintPure)
	FAwRogueItemInfo GetHealingPotion_CurBattle() const;
	// 当前战斗的血瓶
	UFUNCTION(BlueprintCallable)
	void SetHealingPotion_CurBattle(FAwRogueItemInfo HealingPotion);
	
	// 当前战斗的法器
	UFUNCTION(BlueprintPure)
	FAwRogueItemInfo GetCurItem_CurBattle(bool bMainItem = true) const;
	// 当前战斗的法器
	UFUNCTION(BlueprintCallable)
	void SetCurItem_CurBattle(FAwRogueItemInfo CurItem,bool bMainItem = true);
	
	// 当前战斗的房间数
	UFUNCTION(BlueprintPure)
	int GetStep_CurBattle() const;
	// 当前战斗的房间数
	UFUNCTION(BlueprintCallable)
	void SetStep_CurBattle(int Step);

	// 获取当前已经过的关卡
	UFUNCTION(BlueprintPure)
	TArray<FString> GetUsedLevelList_CurBattle() const;
	// 设置当前已经过的关卡
	UFUNCTION(BlueprintCallable)
	void SetUesdLevelList_CurBattle(TArray<FString> LevelList);

	// 当前关卡名称
	UFUNCTION(BlueprintPure)
	FString GetLevelName_CurBattle() const;
	// 当前关卡名称
	UFUNCTION(BlueprintCallable)
	void SetLevelName_CurBattle(FString LevelName);

	// 当前房间名称
	UFUNCTION(BlueprintPure)
	FString GetRoomName_CurBattle() const;
	// 当前房间名称
	UFUNCTION(BlueprintCallable)
	void SetRoomName_CurBattle(FString RoomName);

	// 当前房间类型
	UFUNCTION(BlueprintPure)
	ERogueRoomType GetRoomType_CurBattle() const;
	// 当前房间类型
	UFUNCTION(BlueprintCallable)
	void SetRoomType_CurBattle(ERogueRoomType RoomType);

	// 当前房间奖励类型
	UFUNCTION(BlueprintPure)
	TArray<ERogueRoomReward> GetRoomReward_CurBattle() const;
	// 当前房间奖励类型
	UFUNCTION(BlueprintCallable)
	void SetRoomReward_CurBattle(TArray<ERogueRoomReward> RoomReward);

	// 当前房间是否已清完怪物
	UFUNCTION(BlueprintPure)
	bool GetIsClearThisRoom_CurBattle() const;
	// 设置房间是否已清完怪物
	UFUNCTION(BlueprintCallable)
	void SetIsClearThisRoom_CurBattle(bool bClearRoom);
	
	// 当前挑战房间是否成功
	UFUNCTION(BlueprintPure)
	bool GetChallengeRoomSuccess_CurBattle() const;
	// 设置挑战房间是否成功
	UFUNCTION(BlueprintCallable)
	void SetChallengeRoomSuccess_CurBattle(bool bSuccess);

	// 获取已经随出的精英房间数量
	UFUNCTION(BlueprintPure)
	int GetRandEliteRoomNum_CurBattle() const;
	// 设置已经随出的精英房间数量
	UFUNCTION(BlueprintCallable)
	void SetRandEliteRoomNum_CurBattle(int Num);

	// 获取已经随出的挑战房间数量
	UFUNCTION(BlueprintPure)
	int GetRandChallengeRoomNum_CurBattle() const;
	// 设置已经随出的挑战房间数量
	UFUNCTION(BlueprintCallable)
	void SetRandChallengeRoomNum_CurBattle(int Num);

	// 获取当前房间金币奖励
	UFUNCTION(BlueprintPure)
	int GetRoomRewardCoin_CurBattle() const;

	// 设置当前房间金币奖励
	UFUNCTION(BlueprintCallable)
	void SetRoomRewardCoin_CurBattle(int CoinCount);

	// 获取当前房间额外的金币奖励
	UFUNCTION(BlueprintPure)
	int GetRoomRewardExtraCoin_CurBattle() const;

	// 设置当前房间额外的金币奖励
	UFUNCTION(BlueprintCallable)
	void SetRoomRewardExtraCoin_CurBattle(int CoinCount);

	// 获取当前房间残响奖励
	UFUNCTION(BlueprintPure)
	int GetRoomRewardKey_CurBattle() const;

	// 设置当前房间残响奖励
	UFUNCTION(BlueprintCallable)
	void SetRoomRewardKey_CurBattle(int KeyCount);

	// 获取当前房间魂晶奖励
	UFUNCTION(BlueprintPure)
	int GetRoomRewardSoul_CurBattle() const;

	// 设置当前房间魂晶奖励
	UFUNCTION(BlueprintCallable)
	void SetRoomRewardSoul_CurBattle(int SoulCount);

	// 获取当前房间神之碎片奖励
	UFUNCTION(BlueprintPure)
	int GetRoomRewardShard_CurBattle() const;

	// 设置当前房间神之碎片奖励
	UFUNCTION(BlueprintCallable)
	void SetRoomRewardShard_CurBattle(int ShardCount);

	//获取当前房间是否已拿取圣遗物奖励
	UFUNCTION(BlueprintPure)
	bool HasGotRelicReward_CurRoom() const;

	//设置当前房间是否已拿取圣遗物奖励
	UFUNCTION(BlueprintCallable)
	void SetHasGotRelicReward_CurRoom(bool IsGot);

	//获取是否有圣遗物房间奖励的存档
	UFUNCTION(BlueprintPure)
	bool GetHasRelicReward_CurRoom() const;

	//获取存档的圣遗物房间奖励
	UFUNCTION(BlueprintPure)
	TMap<FString,int> GetRelicReward_CurRoom() const;

	// 设置存档的圣遗物房间奖励
	UFUNCTION(BlueprintCallable)
	void SetRelicReward_CurRoom(TMap<FString,int> RelicList);

	// 清空存档的圣遗物房间奖励
	UFUNCTION(BlueprintCallable)
	void ClearRelicReward_CurRoom();

	//获取当前房间是否已拿取法器奖励
	UFUNCTION(BlueprintPure)
	bool HasGotItemReward_CurRoom() const;

	//设置当前房间是否已拿取法器奖励
	UFUNCTION(BlueprintCallable)
	void SetHasGotItemReward_CurRoom(bool IsGot);

	//获取是否有法器房间奖励的存档
	UFUNCTION(BlueprintPure)
	bool GetHasItemReward_CurRoom() const;

	//获取存档的法器房间奖励
	UFUNCTION(BlueprintPure)
	TMap<FString,int> GetItemReward_CurRoom() const;

	// 设置存档的法器房间奖励
	UFUNCTION(BlueprintCallable)
	void SetItemReward_CurRoom(TMap<FString,int> ItemList);

	// 清空存档的法器房间奖励
	UFUNCTION(BlueprintCallable)
	void ClearItemReward_CurRoom();

	//获取当前房间是否已拿取动作奖励
	UFUNCTION(BlueprintPure)
	bool HasGotActionReward_CurRoom() const;

	//设置当前房间是否已拿取动作奖励
	UFUNCTION(BlueprintCallable)
	void SetHasGotActionReward_CurRoom(bool IsGot);

	//获取是否有动作房间奖励的存档
	UFUNCTION(BlueprintPure)
	bool GetHasActionReward_CurRoom() const;

	//获取存档的动作房间奖励
	UFUNCTION(BlueprintPure)
	TArray<FRougeAbilityLevelInfo> GetActionReward_CurRoom() const;

	// 设置存档的动作房间奖励
	UFUNCTION(BlueprintCallable)
	void SetActionReward_CurRoom(TArray<FRougeAbilityLevelInfo> ActionList);

	// 清空存档的动作房间奖励
	UFUNCTION(BlueprintCallable)
	void ClearActionReward_CurRoom();

	//获取商店存档是否激活
	UFUNCTION(BlueprintPure)
	bool GetShopIsActive_CurBattle() const;

	//设置商店存档是否激活
	UFUNCTION(BlueprintCallable)
	void SetShopIsActive_CurBattle(bool IsActive);

	//获取商店存档
	UFUNCTION(BlueprintPure)
	FRogueShopReward GetShopInfo_CurBattle() const;

	//设置商店存档
	UFUNCTION(BlueprintCallable)
	void SetShopInfo_CurBattle(FRogueShopReward ShopInfo);

	// 获得某个角色的战斗风格
	UFUNCTION(BlueprintPure)
	FString GetBattleStyle(FString PawnClassId) const;
	// 设置某个角色的战斗风格
	UFUNCTION(BlueprintCallable)
	void SetBattleStyle(FString PawnClassId, FString StyleId) const;
	
	//设置当前房间祈祷的所有奖励
	UFUNCTION(BlueprintCallable)
	void SetPrayReward_CurRoom(TMap<FString,FRandomReward> PrayReward);

	//获取当前房间祈祷的所有奖励
	UFUNCTION(BlueprintPure)
	TMap<FString,FRandomReward> GetPrayReward_CurRoom() const;

	//当前房间是否祈祷奖励的存档
	UFUNCTION(BlueprintPure)
	bool GetHasPrayReward_CurRoom() const;

	//清除当前房间祈祷奖励的存档
	UFUNCTION(BlueprintCallable)
	void ClearPrayReward_CurRoom();

	//设置是否已经升级血瓶
	UFUNCTION(BlueprintCallable)
	void SetUpgradeHealingPotionIsActive_CurRoom(bool isActive);

	//获取是否已经升级血瓶
	UFUNCTION(BlueprintPure)
	bool GetUpgradeHealingPotionIsActive_CurRoom() const;

	//设置是否已经选择了祈祷奖励
	UFUNCTION(BlueprintCallable)
	void SetPrayRewardsIsActive_CurRoom(bool isActive);

	//获取是否已经选择了祈祷奖励
	UFUNCTION(BlueprintPure)
	bool GetPrayRewardsIsActive_CurRoom() const;

	//获取保存的下个房间的选择列表
	UFUNCTION(BlueprintPure)
	TArray<FRogueRoomInfo> GetNextRoomList_CurRoom() const;
	
	//设置下个房间的选择列表
	UFUNCTION(BlueprintCallable)
	void SetNextRoomList_CurRoom(TArray<FRogueRoomInfo> RoomList);

	//清空下个房间的选择列表
	UFUNCTION(BlueprintCallable)
	void ClearNextRoomList_CurRoom();

	//设置当前战斗时的血量
	UFUNCTION(BlueprintCallable)
	void SetHP_CurBattle(int hp);

	//获取当前战斗时的血量
	UFUNCTION(BlueprintPure)
	int GetHP_CurBattle() const;

	//设置当前战斗时的觉醒值
	UFUNCTION(BlueprintCallable)
	void SetAP_CurBattle(int ap);

	//获取当前战斗时的觉醒值
	UFUNCTION(BlueprintPure)
	int GetAP_CurBattle() const;
	
	//设置当前道具充能
	UFUNCTION(BlueprintCallable)
	void SetItemEnergy_CurBattle(int Energy);

	//获取当前道具充能
	UFUNCTION(BlueprintPure)
	int GetItemEnergy_CurBattle() const;
	
	//设置已选择初始祝福（某个元素图鉴全开后获得）
	UFUNCTION(BlueprintCallable)
	void SetHasSelectStartBless(bool bSelected = true);

	//查看是否已选择初始祝福（某个元素图鉴全开后获得）
	UFUNCTION(BlueprintPure)
	bool GetHasSelectStartBless() const;

	// 添加强化
	UFUNCTION(BlueprintCallable)
	void AddUpgrade(FString StyleId, FString UpgradeId);

	// 去除强化
	UFUNCTION(BlueprintCallable)
	void RemoveUpgrade(FString StyleId, FString UpgradeId);
	
	// 清理强化
	UFUNCTION(BlueprintCallable)
	void ClearUpgrade(FString StyleId);

	// 获取某个风格当前的所有强化Ids
	UFUNCTION(BlueprintPure)
	TArray<FString> GetUpgrades(FString StyleId) const;

	// --------- Battle Style Upgrade ----------

	UFUNCTION(BlueprintPure)
	int GetBattleStyleUpgradePoint() const;
	UFUNCTION(BlueprintCallable)
	void SetBattleStyleUpgradePoint(int NewCount);
	UFUNCTION(BlueprintPure)
	int GetBattleStyleUpgradeMaxPoint() const;
	UFUNCTION(BlueprintCallable)
	void SetBattleStyleUpgradeMaxPoint(int NewCount);
	
	UFUNCTION(BlueprintPure)
	TArray<FString> GetBattleStyleUpgradeRandomReward() const;
	UFUNCTION(BlueprintCallable)
	void SetBattleStyleUpgradeRandomReward(TArray<FString> NewRandomRewards);

	UFUNCTION(BlueprintPure)
	bool GetBattleStyleUpgradeHasGot() const;
	UFUNCTION(BlueprintCallable)
	void SetBattleStyleUpgradeHasGot(bool HasGot);

	UFUNCTION(BlueprintPure)
	bool GetBattleStyleUpgradeHasGotPoint() const;
	UFUNCTION(BlueprintCallable)
	void SetBattleStyleUpgradeHasGotPoint(bool HasGot);
	
	// --------- --------- ---------

	// --------- Battle Upgrade ---------
	// 当前使用的技能
	UFUNCTION(BlueprintPure)
	FBattleAbilityIds GetCurBattleAbilityIds(int PlayerIndex) const;
	UFUNCTION(BlueprintPure)
	FString GetCurBattleAbilityId(ERogueAbilitySlot AbilitySlot,const int PlayerIndex) const;
	UFUNCTION(BlueprintCallable)
	void SetCurBattleAbilityIds(FBattleAbilityIds BattleAbilityIds,const int PlayerIndex);
	UFUNCTION(BlueprintCallable)
	void SetCurBattleAbilityId(ERogueAbilitySlot AbilitySlot, FString AbilityId,const int PlayerIndex);
	UFUNCTION(BlueprintCallable)
	void EmptyCurBattleAbilityId(const int PlayerIndex);

	// 各个角色对应的预览技能
	UFUNCTION(BlueprintPure)
	static FBattleAbilityIds GetDefaultBattleAbilityIds(const FString& PawnClassId);

	// --- FRougeAbilityLevelInfo ---
	UFUNCTION(BlueprintPure)
	static TMap<ERogueAbilitySlot, FRougeAbilityLevelInfo> GetDefaultAbilityLevelInfos(const FString& PawnClassId);
	UFUNCTION(BlueprintPure)
	static TMap<ERogueAbilitySlot, FRougeAbilityLevelInfo> GetDefaultAbilityLevelInfosByClassId(const FString& ClassId);
	// 各个角色的槽位的默认技能信息（等级0，元素无）
	UFUNCTION(BlueprintPure)
	static FRougeAbilityLevelInfo GetDefaultAbilityLevelInfo(const FString& PawnClassId, ERogueAbilitySlot AbilitySlot);
	UFUNCTION(BlueprintPure)
	TMap<ERogueAbilitySlot, FRougeAbilityLevelInfo> GetPreviewAbilityLevelInfos(const FString& PawnClassId) const;
	UFUNCTION(BlueprintPure)
	FRougeAbilityLevelInfo GetPreviewAbilityLevelInfo(const FString& PawnClassId, ERogueAbilitySlot AbilitySlot) const;
	UFUNCTION(BlueprintCallable)
	void SetPreviewAbilityLevelInfosByClassId(FString PawnClassId, TMap<ERogueAbilitySlot, FRougeAbilityLevelInfo> AbilityLevelInfos);
	UFUNCTION(BlueprintCallable)
	void SetPreviewAbilityLevelInfos(const int PlayerIndex,TMap<ERogueAbilitySlot, FRougeAbilityLevelInfo> AbilityLevelInfos);
	UFUNCTION(BlueprintCallable)
	void SetPreviewAbilityLevelInfo(const int PlayerIndex,ERogueAbilitySlot AbilitySlot, FRougeAbilityLevelInfo AbilityLevelInfo);
	UFUNCTION(BlueprintPure)
	TMap<ERogueAbilitySlot, FRougeAbilityLevelInfo> GetCurAbilityLevelInfos(const FString& PawnClassId) const;
	UFUNCTION(BlueprintPure)
	FRougeAbilityLevelInfo GetCurAbilityLevelInfo(ERogueAbilitySlot AbilitySlot, const FString& PawnClassId) const;
	UFUNCTION(BlueprintCallable)
	void SetCurAbilityLevelInfos(TMap<ERogueAbilitySlot, FRougeAbilityLevelInfo> AbilityLevelInfos);
	UFUNCTION(BlueprintCallable)
	void SetCurAbilityLevelInfo(ERogueAbilitySlot AbilitySlot, FRougeAbilityLevelInfo AbilityLevelInfo);
	UFUNCTION(BlueprintCallable)
	void EmptyCurAbilityLevelInfos();
	
	// 替换掉的技能
	UFUNCTION(BlueprintPure)
	TArray<FString> GetGiveUpBattleAbilityIds() const;
	UFUNCTION(BlueprintCallable)
	void AddGiveUpBattleAbilityId(FString AbilityId);
	void ClearGiveUpBattleAbilityIds() const;

	// 战斗强化
	UFUNCTION(BlueprintPure)
	TMap<FString, int> GetBattleUpgradeIds() const;
	UFUNCTION(BlueprintPure)
	int GetBattleUpgradeLevel(FString UpgradeId) const;
	UFUNCTION(BlueprintCallable)
	void SetBattleUpgradeId(FString UpgradeId, int Level,const FString& PawnClassId);
	UFUNCTION(BlueprintCallable)
	void RemoveBattleUpgradeId(FString UpgradeId);
	UFUNCTION(BlueprintCallable)
	void ClearBattleUpgradeIds();
	
	// 获取已解锁的技能信息
	UFUNCTION(BlueprintPure)
	FUnlockAbilityInfo GetUnlockAbilityInfo(const FString& ClassId) const;
	// 添加解锁技能
	UFUNCTION(BlueprintCallable)
	void AddUnlockAbilityId(FString ClassId, FString AbilityId);
	// 判断技能是否已经解锁
	UFUNCTION(BlueprintPure)
	bool IsAbilityUnlock(const FString& ClassId, const FString& AbilityId) const;
	// --------- --------- ---------

	//------------圣遗物和法器图鉴Switch相关------------------

	//获得该圣遗物是否获取过的Switch（图鉴用）
	UFUNCTION(BlueprintPure)
	int GetRelicHasGetRecord(FString RelicId);

	//设置该圣遗物已获取过的Switch（图鉴用）
	UFUNCTION(BlueprintCallable)
	void SetRelicHasGetRecord(FString RelicId);

	//获得该法器是否获取过的Switch（图鉴用）
	UFUNCTION(BlueprintPure)
	int GetMagicItemHasGetRecord(FString ItemId);

	//设置该法器已获取过的Switch（图鉴用）
	UFUNCTION(BlueprintCallable)
	void SetMagicItemHasGetRecord(FString ItemId);

	//------------------------------

	//-------------职业解锁相关-----------------

	UFUNCTION(BlueprintPure)
	bool GetHasUnlockClass(const FString& ClassId) const;

	UFUNCTION(BlueprintCallable)
	void SetClassUnlock(FString ClassId);

	//------------------------------

	//-------------武器相关-----------------

	UFUNCTION(BlueprintPure)
	FString GetCurrWeaponId(const FString& ClassId) const;

	UFUNCTION(BlueprintCallable)
	void SetCurrWeaponId(FString ClassId, FString WeaponId) const;

	UFUNCTION(BlueprintPure)
	bool CheckWeaponUnlock(const FString& WeaponId) const;
	

	UFUNCTION(BlueprintCallable)
	void SetWeaponUnlock(FString WeaponId) const;

	// 默认100、200这样的编号是默认解锁的
	// 其他默认皮肤日后再说
	UFUNCTION(BlueprintPure)
	bool CheckSkinUnlock(const int WeaponId) const;
	
	UFUNCTION(BlueprintCallable)
	void SetSkinUnlock(int SkinId) const;

	// Unlock return -1
	UFUNCTION(BlueprintPure)
	int GetWeaponLevel(const FString& WeaponId) const;

	// Return new level or max level
	// Returning -1 means setting failed.
	UFUNCTION(BlueprintCallable)
	int SetWeaponLevel(FString WeaponId, int NewLevel);
		
	//------------------------------

	static FString GetAwakeSkillId(FRogueDataInfo DataInfo,const int PlayerIndex)
	{
		while (DataInfo.AwakeSkillId.Num()<=PlayerIndex)
			DataInfo.AwakeSkillId.Add(FString("Earthquake"));
		return DataInfo.AwakeSkillId[PlayerIndex];
	}
	void AddItemInsts(TArray<FItemInstance> Items);
	TArray<FItemInstance> GetInventoryItems() const;
	TArray<FItemInstance> GetEquippedItems() const;
	double GetNewItemInstanceID();
	
private:
	void ResetOldSaveGame(UAwRogueSaveGame*OldSave,UAwRogueSaveGame* NewSave);
};
