// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "Particles/ParticleSystemComponent.h"
#include "AwVFXManager.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UAwVFXManager : public UObject
{
	GENERATED_BODY()
public:
	//特效池最大容量
	UPROPERTY(BlueprintReadOnly)
	int PoolMaxSize = 50;
	//特效池
	UPROPERTY(BlueprintReadOnly)
	TArray<UParticleSystemComponent*> PSCPool;
	//去除特效池里已结束的特效以及空值
	void UpdatePool();
	//得到优先级最低的特效
	UParticleSystemComponent* GetMiniPriorityPSC();
	//根据优先级排序，关闭移除相应数量的低优先级特效
	void RemoveMiniPriorityPSCS(int RemoveNum = 1);

	UParticleSystemComponent* CreateVFXatLocation(UParticleSystem* Template, FTransform Transform, bool AutoDestroy, bool AutoActivateSystem);

	UParticleSystemComponent* CreateVFXAttached(UParticleSystem* Template, USceneComponent* AttachToComponent, FName AttachPointName,FTransform Transform, EAttachLocation::Type LocationType, bool AutoDestroy, bool AutoActivateSystem);
	
};
