// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/UI/GameMain/NewbieImage.h"
#include "UObject/Object.h"
#include "NewbieManager.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UNewbieManager : public UObject
{
	GENERATED_BODY()
private:
	//新手引导的图片，<Key, 图片>
	UPROPERTY()
	TMap<FString, FString> NewbieImages;

public:
	//添加一个到记录中
	UFUNCTION(BlueprintCallable)
	void AddImage(FString Key,  FString ImagePath);

	//如果有一个Key对应的图片，就会显示新手引导
	UFUNCTION(BlueprintCallable)
	void ShowNewbie(TArray<FString> Keys);
};
