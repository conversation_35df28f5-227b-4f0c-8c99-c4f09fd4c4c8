// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Subsystems/GameInstanceSubsystem.h"
#include "LevelSequenceActor.h"
#include "LevelSequencePlayer.h"
#include "TheAwakener_FO/GamePlay/LevelSequence/LevelSequenceInfo.h"
#include "AwSequenceManager.generated.h"

/**
 * 
 */
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnGlobalSequenceEvent, ALevelSequenceActor*, Sequence);

UCLASS()
class THEAWAKENER_FO_API UAwSequenceManager : public UGameInstanceSubsystem
{
	GENERATED_BODY()
public:
	virtual void Initialize(FSubsystemCollectionBase& Collection)override;


	UFUNCTION(BlueprintCallable, Category = "SequenceManager")
	FLevelSequenceInfo GetSequenceInfoFromDataManager(FString SequenceID);

	//注册和注销
	void RegisterSequencePlayer(ALevelSequenceActor* InSequenceActor, ESequenceType SequenceType);
	void UnRegisterSequencePlayer(ALevelSequenceActor *InSequenceActor, ESequenceType SequenceType);

	//全局LevelSequence
	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "SequenceManager")
		ALevelSequenceActor* GetGlobalSequenceActor() { return GlobalSequencePlayerInstance.Get(); }
	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "SequenceManager")
		ULevelSequencePlayer* GetGlobalSequencePlayer();


	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "SequenceManager")
		bool IsGlobalSequencePlaying();
	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "SequenceManager")
		bool IsGlobalSequencePaused();

	//Sequence 基础调用
	UFUNCTION(BlueprintCallable, Category = "SequenceManager")
		void PlaySequence(ALevelSequenceActor* SequenceActor, ESequenceType SequenceType = ESequenceType::Global);
	UFUNCTION(BlueprintCallable, Category = "SequenceManager")
		void PauseSequence(ALevelSequenceActor* SequenceActor);
	UFUNCTION(BlueprintCallable, Category = "SequenceManager")
		void StopSequence(ALevelSequenceActor* SequenceActor);

	//局部LevelSequence的调用
	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "SequenceManager")
		bool IsAnyLocalSequencePlaying();

	UFUNCTION()
		void OnGlobalSequencePlayerEndPlay(AActor* DestoryActor, EEndPlayReason::Type EndPlayReason);
	UFUNCTION()
		void OnLocalSequencePlayerEndPlay(AActor* DestoryActor, EEndPlayReason::Type EndPlayReason);
	
	//全局Sequence 委托代理
	UPROPERTY(BlueprintCallable,BlueprintAssignable, Category = "SequenceManager")
	FOnGlobalSequenceEvent OnGlobalSequencePlayEvent;
	UPROPERTY(BlueprintCallable,BlueprintAssignable, Category = "SequenceManager")
	FOnGlobalSequenceEvent OnGlobalSequenceStopEvent;
	UPROPERTY(BlueprintCallable,BlueprintAssignable, Category = "SequenceManager")
	FOnGlobalSequenceEvent OnGlobalSequencePauseEvent;

private:
	UFUNCTION()
		void GlobalSequenceStopEventBroadcast();
	UFUNCTION()
		void GlobalSequencePauseEventBroadcast();
	
	//全局性质的sequence的调用对象 只保留最后的一个
	TWeakObjectPtr<ALevelSequenceActor> GlobalSequencePlayerInstance = nullptr;
	
	//局部性质的sequence 可以同时存在复数个 根据信息 可能会变成TMap<FString,ALevelSequenceActor>
	TArray<TWeakObjectPtr<ALevelSequenceActor>> CurLocalSequencePlayers;
	FLevelSequenceInfo CurSequenceInfo;
};
