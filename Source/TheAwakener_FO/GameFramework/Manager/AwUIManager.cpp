// Fill out your copyright notice in the Description page of Project Settings.


#include "AwUIManager.h"

#include "Components/CanvasPanelSlot.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/UI/Shop.h"
#include "TheAwakener_FO/UI/Toast.h"
#include "AwDataManager.h"
#include "TheAwakener_FO/UI/Prompt.h"
#include "TheAwakener_FO/UI/GameMain/GameMain.h"

AAwPlayerController* UAwUIManager::GetMyPlayerController()
{
	if (OwnerPC)
		return OwnerPC;
	
	OwnerPC = UGameplayFuncLib::GetWorkingAwPlayerController();
	return OwnerPC;
}

void UAwUIManager::InitUI(AAwPlayerController* PC)
{
	if (OwnerPC==nullptr)
		OwnerPC = PC;
	else
	{
		UE_LOG(LogTemp,Warning,TEXT("UAwUIManager::InitUI double init UIManager by %s"),*PC->GetFullName());
		return;
	}
	UKismetSystemLibrary::PrintString(this, FString("UIManager Init"), true, true, FLinearColor::Red, 30);
	if (OwnerPC)
	{

		int32 FuckX = 0;
		int32 FuckY = 0;
		PC->GetViewportSize(FuckX, FuckY);
		ScreenCenterY = FuckY / 2.000f;
	}

	if (!UGameplayFuncLib::IsRogueMode())
	{
		Toast = UAwGameInstance::Instance->UIManager->LoadUserWidget<UToast>("Core/UI/WBP_Toast");
		ConfirmDialog = UAwGameInstance::Instance->UIManager->LoadUserWidget<UMessageDialog>("Core/UI/WBP_MessageDialog");
	}
	//
	//UTestUIButton* TestBtnUI = UAwGameInstance::Instance->UIManager->LoadUserWidget<UTestUIButton>("Core/UI/WBP_UITest");
	//if (TestBtnUI)
	//{
	//	TestBtnUI->AddToViewport();
	//}
	// MainHUD = UAwGameInstance::Instance->UIManager->LoadUserWidget<UGameMain>(GetWbpPathById("GameMain"));
	// if (MainHUD)
	// 	MainHUD->AddToViewport(10);

	PopText = UAwGameInstance::Instance->UIManager->LoadUserWidget<UPopText>(GetWbpPathById("PopText"));
	if (PopText)
		PopText->AddToViewport(99);
	AwGameState = Cast<AAwGameState>(UGameplayStatics::GetGameState(GWorld));
}

FString UAwUIManager::GetWbpPathById(FString Id)
{
	return UGameplayFuncLib::GetDataManager()->GetWidgetInfoById(Id).Path;
}

UUserWidget* UAwUIManager::ShowNewUI(FString UIName, int ZOrder)
{
	const FAwWidgetInfo WidgetInfo = UGameplayFuncLib::GetAwDataManager()->GetWidgetInfoById(UIName);
	//const FString UIPath = GetWbpPathById(UIName);
	UUserWidget* UI = LoadUserWidget<UUserWidget>(WidgetInfo.Path);
	if (UI)
	{
		if(WidgetInfo.ZOrder < 0)
		{
			UI->AddToViewport(0);
		}
		else
		{
			UI->AddToViewport(WidgetInfo.ZOrder);
		}
		OpenedWidgets.Add(UIName, UI);
	}
	return UI;
}

void UAwUIManager::ShowNewUserWidget(FString UIName, UUserWidget* NewUserWidget)
{
	NewUserWidget->AddToViewport();
	OpenedWidgets.Add(UIName, NewUserWidget);
}

UUserWidget* UAwUIManager::Show(FString UIName, int ZOrder)
{
	FAwWidgetInfo WidgetInfo = UGameplayFuncLib::GetAwDataManager()->GetWidgetInfoById(UIName);
	UUserWidget* UI = nullptr;
	if (UAwGameInstance::Instance->isSurvivor)
	{
		const FString tempName = UIName+"_Svl";
		const FAwWidgetInfo tempWidgetInfo = UGameplayFuncLib::GetAwDataManager()->GetWidgetInfoById(tempName);
		if (!tempWidgetInfo.Path.IsEmpty())
		{
			WidgetInfo = tempWidgetInfo;
		}
	}
	
	if(OpenedWidgets.Contains(UIName))
	{
		UI =  OpenedWidgets[UIName];
		if(!UI->IsInViewport())
		{
			if(WidgetInfo.ZOrder < 0)
			{
				UI->AddToViewport(0);
			}
			else
			{
				UI->AddToViewport(WidgetInfo.ZOrder);
			}
		}
	}
	else
	{
		//const FString UIPath = GetWbpPathById(UIName);
		UI = LoadUserWidget<UUserWidget>(WidgetInfo.Path);
		if (UI)
		{
			if(WidgetInfo.ZOrder < 0)
			{
				UI->AddToViewport(0);
			}
			else
			{
				UI->AddToViewport(WidgetInfo.ZOrder);
			}
			OpenedWidgets.Add(UIName, UI);
		}
		else
			UE_LOG(LogTemp, Error, TEXT("Can not open UI (%s)"), *WidgetInfo.Path);
		
	}
	
	return UI;
}

UUserWidget* UAwUIManager::Find(FString UIName, bool& bFound)
{
	if (OpenedWidgets.Contains(UIName))
	{
		bFound = true;
		return OpenedWidgets[UIName];
	}
	bFound = false;
	return nullptr;
}

void UAwUIManager::Hide(FString UIName)
{
	if (OpenedWidgets.Contains(UIName))
	{
		OpenedWidgets[UIName]->RemoveFromParent();
		OpenedWidgets.Remove(UIName);
	}
}

void UAwUIManager::HideWithAnimation(FString UIName)
{
	if (OpenedWidgets.Contains(UIName))
	{
		auto UI = Cast<UBaseUI>(OpenedWidgets[UIName]);
		UI->PlayHideAnimEvent();
		OpenedWidgets.Remove(UIName);
	}
}

void UAwUIManager::SetFocus(FString UIName)
{
	if (OpenedWidgets.Contains(UIName))
	{
		FocusedWidget = OpenedWidgets[UIName];
	}
}

void UAwUIManager::SetAllOpenedWidgetsVisible(ESlateVisibility SlateVisibility)
{
	for (const TTuple<FString, UUserWidget*> OpenedWidget : OpenedWidgets)
		OpenedWidget.Value->SetVisibility(SlateVisibility);
}

void UAwUIManager::ShowToast(FString TextKey)
{
	if (!Toast) return;
	Toast->AddToViewport(9999);
	Toast->Show(TextKey, ScreenCenterY);
}

void UAwUIManager::ShowPrompt(FString Message, FString ActionId)
{
	UPrompt* UI;
	if (OpenedWidgets.Contains("Prompt"))
		UI = Cast<UPrompt>(OpenedWidgets["Prompt"]);
	else
		UI = Cast<UPrompt>(Show("Prompt"));

	UI->ShowUI(Message, ActionId);
}

void UAwUIManager::HidePrompt()
{
	if (OpenedWidgets.Contains("Prompt"))
		Cast<UPrompt>(OpenedWidgets["Prompt"])->HideUI();
}

void UAwUIManager::PromptMissionClear()
{
	if (OpenedWidgets.Contains("Prompt"))
		Cast<UPrompt>(OpenedWidgets["Prompt"])->MissionClear();
}

void UAwUIManager::ShowMessageDialog(FString TextKey, TArray<FString> Yes, TArray<FString> Cancel, FString YesKey, FString CancelKey)
{
	if (!ConfirmDialog) return;
	ConfirmDialog->AddToViewport(9998);
	ConfirmDialog->Show(TextKey, YesKey, "", CancelKey, Yes, TArray<FString>(), Cancel);
}

void UAwUIManager::HideMessageDialog()
{
	if (ConfirmDialog)
		ConfirmDialog->Hide();
}

void UAwUIManager::ShowLoading()
{
	if (!LoadingUI)
		LoadingUI = LoadUserWidget<ULoadingUI>(GetWbpPathById("Loading"));

	if (LoadingUI)
		LoadingUI->AddToViewport(99);
}

void UAwUIManager::HideLoading()
{
	if (LoadingUI)
		LoadingUI->RemoveFromParent();
	LoadingUI = nullptr;
}

void UAwUIManager::SetGameMainVisibility(ESlateVisibility Visibility)
{
	if(OpenedWidgets.Contains("GameMain"))
	{
		UGameMain* GameMain = Cast<UGameMain>(OpenedWidgets["GameMain"]);
		if(GameMain)
			GameMain->SetVisibility(Visibility);
	}
}

void UAwUIManager::TriggerRogueBattleTag(ERogueBattleTag Tag) const
{
	OnCheckRougeBattleTag.Broadcast(Tag);
}

void UAwUIManager::OnTriggerRougeBattleTag_ByFStr(FString Str)
{
	if (Str.Contains("Bludgeon"))
	{
		// OnCheckRougeBattleTag.Broadcast(ERogueBattleTag::Bludgeon);
	}
	else if (Str.Contains("Slash"))
	{
		// OnCheckRougeBattleTag.Broadcast(ERogueBattleTag::Slash);
	}
	else if (Str.Contains("Pierce"))
	{
		// OnCheckRougeBattleTag.Broadcast(ERogueBattleTag::Pierce);
	}
	else if (Str.Contains("Below"))
		OnCheckRougeBattleTag.Broadcast(ERogueBattleTag::Below);
	else if (Str.Contains("Smash"))
		OnCheckRougeBattleTag.Broadcast(ERogueBattleTag::Smash);
	else if (Str.Contains("Dash"))
		OnCheckRougeBattleTag.Broadcast(ERogueBattleTag::Dash);
	else if (Str.Contains("Power"))
		OnCheckRougeBattleTag.Broadcast(ERogueBattleTag::Power);
	
	else if (Str.Contains("JustDodge"))
		OnCheckRougeBattleTag.Broadcast(ERogueBattleTag::JustDodge);
	else if (Str.Contains("JustDefense"))
		OnCheckRougeBattleTag.Broadcast(ERogueBattleTag::JustDefense);
	else if (Str.Contains("JustAttack"))
		OnCheckRougeBattleTag.Broadcast(ERogueBattleTag::JustAttack);
	else if (Str.Contains("DefenseCounter"))
		OnCheckRougeBattleTag.Broadcast(ERogueBattleTag::DefenseCounter);
	else if (Str.Contains("DodgeCounter"))
		OnCheckRougeBattleTag.Broadcast(ERogueBattleTag::DodgeCounter);
	else if (Str.Contains("LeapAttack"))
		OnCheckRougeBattleTag.Broadcast(ERogueBattleTag::LeapAttack);
}

void UAwUIManager::ShowCmdTipPopUp(TArray<FRogueCmdTipInfo> CmdTipInfo, float Duration)
{
	if (!OpenedWidgets.Contains("CmdPopUp"))
	{
		UCmdTipPopUp* UI = Cast<UCmdTipPopUp>(Show("CmdPopUp"));
		UI->Setup(CmdTipInfo, Duration);
		UI->ShowUI();
	}
}

void UAwUIManager::HideCmdTipPopUp()
{
	Hide("CmdPopUp");
}

