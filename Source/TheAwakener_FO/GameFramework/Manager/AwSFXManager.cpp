// Fill out your copyright notice in the Description page of Project Settings.

#include "AwSFXManager.h"

#include "AwDataManager.h"
#include "TimerManager.h"
#include "Kismet/GameplayStatics.h"
#include "Sound/SoundMix.h"
#include "Sound/SoundClass.h"

void UAwSFXManager::SetMainVolume(float NewVolume)
{
	MainVolume = FMath::Clamp<float>(NewVolume, 0, 1);
	float volume = FMath::Clamp<float>(NewVolume, 0.1, 1);
	USoundMix* InSoundMix = LoadObject<USoundMix>(nullptr, TEXT("/Game/Core/FrameWork/Audio/AwSoundMix.AwSoundMix"));
	USoundClass* MainSound = LoadObject<USoundClass>(nullptr, TEXT("/Game/Core/FrameWork/Audio/SC_Main.SC_Main"));
	
	UGameplayStatics::SetSoundMixClassOverride(GetWorld(), InSoundMix, MainSound, volume);
}

void UAwSFXManager::SetBGMVolume(float NewVolume)
{
	BGMVolume = FMath::Clamp<float>(NewVolume, 0, 1);
	float volume = FMath::Clamp<float>(NewVolume, 0.01, 1);
	USoundMix* InSoundMix = LoadObject<USoundMix>(nullptr, TEXT("/Game/Core/FrameWork/Audio/AwSoundMix.AwSoundMix"));
	USoundClass* BGMSound = LoadObject<USoundClass>(nullptr, TEXT("/Game/Core/FrameWork/Audio/SC_Bgm.SC_Bgm"));

	UGameplayStatics::SetSoundMixClassOverride(GetWorld(), InSoundMix, BGMSound, volume);
}

void UAwSFXManager::SetSystemAudioVolume(float NewVolume)
{
	SystemAudioVolume = FMath::Clamp<float>(NewVolume, 0, 1);
	float volume = FMath::Clamp<float>(NewVolume, 0.01, 1);
	USoundMix* InSoundMix = LoadObject<USoundMix>(nullptr, TEXT("/Game/Core/FrameWork/Audio/AwSoundMix.AwSoundMix"));
	USoundClass* SystemAudioSound = LoadObject<USoundClass>(nullptr, TEXT("/Game/Core/FrameWork/Audio/SC_System.SC_System"));

	UGameplayStatics::SetSoundMixClassOverride(GetWorld(), InSoundMix, SystemAudioSound, volume);
}

void UAwSFXManager::SetSFXVolume(float NewVolume)
{
	SFXVolume = FMath::Clamp<float>(NewVolume, 0, 1);
	float volume = FMath::Clamp<float>(NewVolume, 0.01, 1);
	USoundMix* InSoundMix = LoadObject<USoundMix>(nullptr, TEXT("/Game/Core/FrameWork/Audio/AwSoundMix.AwSoundMix"));
	USoundClass* SFXSound = LoadObject<USoundClass>(nullptr, TEXT("/Game/Core/FrameWork/Audio/SC_FX.SC_FX"));

	UGameplayStatics::SetSoundMixClassOverride(GetWorld(), InSoundMix, SFXSound, volume);
}

void UAwSFXManager::SetVoiceVolume(float NewVolume)
{
	VoiceVolume = FMath::Clamp<float>(NewVolume, 0, 1);
	float volume = FMath::Clamp<float>(NewVolume, 0.01, 1);
	USoundMix* InSoundMix = LoadObject<USoundMix>(nullptr, TEXT("/Game/Core/FrameWork/Audio/AwSoundMix.AwSoundMix"));
	USoundClass* VoiceSound = LoadObject<USoundClass>(nullptr, TEXT("/Game/Core/FrameWork/Audio/SC_Voice.SC_Voice"));

	UGameplayStatics::SetSoundMixClassOverride(GetWorld(), InSoundMix, VoiceSound, volume);
}

void UAwSFXManager::UpdateSystemAudioPool()
{
	TArray<int> RemovedList;
	for (int i = 0; i < SystemAudioPool.Num(); i++)
	{
		if (SystemAudioPool[i])
		{
			if (!SystemAudioPool[i]->IsPlaying())
			{
				RemovedList.Add(i);
			}
		}
		else
		{
			RemovedList.Add(i);
		}
	}
	for (int j = 0; j < RemovedList.Num(); j++)
	{
		if (SystemAudioPool[RemovedList[j] - j])
		{
			SystemAudioPool[RemovedList[j] - j]->DestroyComponent();
			SystemAudioPool.RemoveAt(RemovedList[j] - j);
		}
	}
}

void UAwSFXManager::UpdateSFXPool()
{
	TArray<int> RemovedList;
	for (int i = 0; i < SFXPool.Num(); i++)
	{
		if (SFXPool[i])
		{
			if (!SFXPool[i]->IsPlaying())
			{
				RemovedList.Add(i);
			}
		}
		else
		{
			RemovedList.Add(i);
		}
	}
	for (int j = 0; j < RemovedList.Num(); j++)
	{
		const int Index = RemovedList[j] - j;
		// if (SFXPool[Index]&&SFXPool[Index]->Sound)
		// {
		// 	UKismetSystemLibrary::PrintString(this,"Update Remove SFX ->"+SFXPool[Index]->Sound->GetName(),true,true,FColor::Red,10.f);
		// }
		if (SFXPool[Index]) SFXPool[Index]->DestroyComponent();
		SFXPool.RemoveAt(RemovedList[j] - j);
	}
}

void UAwSFXManager::UpdateVoicePool()
{
	TArray<int> RemovedList;
	for (int i = 0; i < VoicePool.Num(); i++)
	{
		if (VoicePool[i])
		{
			if (!VoicePool[i]->IsPlaying())
			{
				RemovedList.Add(i);
			}
		}
		else
		{
			RemovedList.Add(i);
		}
	}
	for (int j = 0; j < RemovedList.Num(); j++)
	{
		const int Index = RemovedList[j] - j;
		if (VoicePool[Index]) VoicePool[Index]->DestroyComponent();
		VoicePool.RemoveAt(RemovedList[j] - j);
	}
}

TMap<FString, int> UAwSFXManager::GetSFXNumberInPool()
{
	TMap<FString, int> Res;
	for (int i = 0; i < SFXPool.Num(); ++i)
	{
		FString Name = SFXPool[i]->GetSound()->GetName();
		if (Res.Contains(Name))
			Res.Add(Name, Res[Name] + 1);
		else
			Res.Add(Name, 1);
	}
	return Res;
}

void UAwSFXManager::RemoveMiniPrioritySystemAudio(int RemoveNum)
{
	//ToDo:待确定系统音优先级方法
	for (int i = 0; i < SystemAudioPool.Num(); i++)
	{
		if (SystemAudioPool[i])
		{
			UAudioComponent* RemoveAudio = SystemAudioPool[i];
			SystemAudioPool.Remove(RemoveAudio);
			RemoveAudio->Deactivate();
			RemoveAudio->DestroyComponent();
		}
		else
		{
			SystemAudioPool.RemoveAt(i);
		}
	}
}

void UAwSFXManager::RemoveMiniPrioritySFX(int RemoveNum)
{
	//ToDo:待确定音效优先级方法
	for (int i = 0; i < SFXPool.Num(); i++)
	{
		if (SFXPool[i])
		{
			UAudioComponent* RemoveSFX = SFXPool[i];
			//UKismetSystemLibrary::PrintString(this,"Remove SFX ->"+RemoveSFX->Sound->GetName(),true,true,FColor::Red,10.f);
			SystemAudioPool.Remove(RemoveSFX);
			RemoveSFX->Deactivate();
			RemoveSFX->DestroyComponent();
			
			return;
		}
	}
}

void UAwSFXManager::RemoveMiniPriorityVoice(int RemoveNum)
{
	//ToDo:待确定音效优先级方法
	for (int i = 0; i < VoicePool.Num(); i++)
	{
		if (VoicePool[i])
		{
			UAudioComponent* RemoveSFX = VoicePool[i];
			SystemAudioPool.Remove(RemoveSFX);
			RemoveSFX->Deactivate();
			RemoveSFX->DestroyComponent();
			return;
		}
	}
}

UAudioComponent* UAwSFXManager::PlaySystemAudio(USoundBase* Sound, float VolumeMultiplier, float PitchMultiplier, float StartTime, bool bPersistAcrossLevelTransition)
{
	UpdateSystemAudioPool();
	if (MainVolume && SystemAudioVolume && Sound)
	{
		if (SystemAudioPool.Num() >= SystemAudioPoolMaxSize)
		{
			RemoveMiniPrioritySystemAudio(SystemAudioPool.Num() - SystemAudioPoolMaxSize + 1);
		}
		if (SystemAudioPool.Num() < SystemAudioPoolMaxSize)
		{
			UAudioComponent* NewSystemAudio = UGameplayStatics::CreateSound2D(GWorld, Sound, VolumeMultiplier * SystemAudioVolume, PitchMultiplier, StartTime, nullptr, bPersistAcrossLevelTransition, true);
			if (NewSystemAudio)
			{
				NewSystemAudio->Play();
				SystemAudioPool.Add(NewSystemAudio);
			}
			return NewSystemAudio;
		}
	}
	return nullptr;
}

void UAwSFXManager::StopAllSystemAudio(float FadeOutTime)
{
	UpdateSystemAudioPool();
	for (int i = 0; i < SystemAudioPool.Num(); i++)
	{
		SystemAudioPool[i]->FadeOut(0.5f, 0.0f, EAudioFaderCurve::Linear);
	}
}

UAudioComponent* UAwSFXManager::PlaySFXatLocation(USoundBase* Sound, FVector Location, FRotator Rotation,
	float VolumeMultiplier, float PitchMultiplier, float StartTime, USoundAttenuation* SoundAttenuation, USoundConcurrency* SoundConcurrency)
{
	UpdateSFXPool();

	/*int HasCount = 0;
	TMap<FString, int> TempSFXPool = GetSFXNumberInPool();
	if (TempSFXPool.Contains(Sound->GetName()))
		HasCount = TempSFXPool[Sound->GetName()];
	if (HasCount > 3)
	{
		return nullptr;
	}*/
	
	if (MainVolume && SFXVolume && Sound)
	{
		if (SFXPool.Num() >= SFXPoolMaxSize)
		{
			RemoveMiniPrioritySFX(SFXPool.Num() - SFXPoolMaxSize + 1);
		}
		if (SFXPool.Num() < SFXPoolMaxSize)
		{
			UAudioComponent* NewAudio = UGameplayStatics::SpawnSoundAtLocation(GWorld, Sound, Location, Rotation,
				VolumeMultiplier * SFXVolume, PitchMultiplier, StartTime, SoundAttenuation, SoundConcurrency);
			if (NewAudio)
			{
				NewAudio->Play();
				SFXPool.Add(NewAudio);
			}
			return NewAudio;
		}
	}
	return nullptr;
}

UAudioComponent* UAwSFXManager::PlaySFXAttached(USoundBase* Sound, USceneComponent* AttachtoComponent, FName AttachPointName,
	FVector Location, FRotator Rotation, EAttachLocation::Type LocationType, bool StopWhenAttachedtoDestroyed,
	float VolumeMultiplier, float PitchMultiplier, float StartTime, USoundAttenuation* SoundAttenuation, USoundConcurrency* SoundConcurrency)
{
	UpdateSFXPool();
	if (MainVolume && SFXVolume && Sound)
	{
		if (SFXPool.Num() >= SFXPoolMaxSize)
		{
			RemoveMiniPrioritySFX(SFXPool.Num() - SFXPoolMaxSize + 1);
		}
		if (SFXPool.Num() < SFXPoolMaxSize)
		{
			UAudioComponent* NewAudio = UGameplayStatics::SpawnSoundAttached(Sound, AttachtoComponent, AttachPointName,
				Location, Rotation, LocationType, StopWhenAttachedtoDestroyed,
				VolumeMultiplier * SFXVolume, PitchMultiplier, StartTime, SoundAttenuation, SoundConcurrency);
			if (NewAudio)
			{
				NewAudio->Play();
				SFXPool.Add(NewAudio);
			}
			return NewAudio;
		}
	}
	return nullptr;
}

void UAwSFXManager::StopAllSFX(float FadeOutTime)
{
	UpdateSFXPool();
	for (int i = 0; i < SFXPool.Num(); i++)
	{
		SFXPool[i]->FadeOut(0.5f, 0.0f, EAudioFaderCurve::Linear);
	}
}

UAudioComponent* UAwSFXManager::PlayVoiceatLocation(USoundBase* Sound, FVector Location, FRotator Rotation,
	float VolumeMultiplier, float PitchMultiplier, float StartTime, USoundAttenuation* SoundAttenuation, USoundConcurrency* SoundConcurrency)
{
	UpdateVoicePool();
	if (MainVolume && VoiceVolume && Sound)
	{
		if (VoicePool.Num() >= VoicePoolMaxSize)
		{
			RemoveMiniPriorityVoice(VoicePool.Num() - VoicePoolMaxSize + 1);
		}
		if (VoicePool.Num() < VoicePoolMaxSize)
		{
			UAudioComponent* NewAudio = UGameplayStatics::SpawnSoundAtLocation(GWorld, Sound, Location, Rotation,
				VolumeMultiplier * VoiceVolume, PitchMultiplier, StartTime, SoundAttenuation, SoundConcurrency);
			if (NewAudio)
			{
				NewAudio->Play();
				VoicePool.Add(NewAudio);
			}
			return NewAudio;
		}
	}
	return nullptr;
}

UAudioComponent* UAwSFXManager::PlayVoiceAttached(USoundBase* Sound, USceneComponent* AttachtoComponent,
	FName AttachPointName, FVector Location, FRotator Rotation, EAttachLocation::Type LocationType,
	bool StopWhenAttachedtoDestroyed, float VolumeMultiplier, float PitchMultiplier, float StartTime, USoundAttenuation* SoundAttenuation, USoundConcurrency* SoundConcurrency)
{
	UpdateVoicePool();
	if (MainVolume && VoiceVolume && Sound)
	{
		if (VoicePool.Num() >= VoicePoolMaxSize)
		{
			RemoveMiniPriorityVoice(VoicePool.Num() - VoicePoolMaxSize + 1);
		}
		if (VoicePool.Num() < VoicePoolMaxSize)
		{
			UAudioComponent* NewAudio = UGameplayStatics::SpawnSoundAttached(Sound, AttachtoComponent, AttachPointName, Location, Rotation, LocationType,
				StopWhenAttachedtoDestroyed, VolumeMultiplier * VoiceVolume, PitchMultiplier, StartTime, SoundAttenuation, SoundConcurrency);
			if (NewAudio)
			{
				NewAudio->Play();
				VoicePool.Add(NewAudio);
			}
			return NewAudio;
		}
	}
	return nullptr;
}

UAudioComponent* UAwSFXManager::PlayVoice2D(USoundBase* Sound, float VolumeMultiplier, float PitchMultiplier, float StartTime)
{
	UpdateVoicePool();
	if (MainVolume && VoiceVolume && Sound)
	{
		if (VoicePool.Num() >= VoicePoolMaxSize)
		{
			RemoveMiniPriorityVoice(VoicePool.Num() - VoicePoolMaxSize + 1);
		}
		if (VoicePool.Num() < VoicePoolMaxSize)
		{
			UAudioComponent* NewAudio = UGameplayStatics::SpawnSound2D(GWorld, Sound, VolumeMultiplier * VoiceVolume, PitchMultiplier, StartTime);
			if (NewAudio)
			{
				NewAudio->Play();
				VoicePool.Add(NewAudio);
			}
			return NewAudio;
		}
	}
	return nullptr;
}

void UAwSFXManager::StopAllVoice(float FadeOutTime)
{
	UpdateVoicePool();
	for (int i = 0; i < VoicePool.Num(); i++)
	{
		VoicePool[i]->FadeOut(0.5f, 0.0f, EAudioFaderCurve::Linear);
	}
}

void UAwSFXManager::Init(UAwDataManager* DataManager)
{
	if (!DataManager) return;
	for (TTuple<FString, FString> Bgm : DataManager->GetBgmList())
	{
		if (!BGMList.Contains(Bgm.Key))
		{
			USoundBase* Sound = LoadObject<USoundBase>(nullptr, *UResourceFuncLib::GetAssetPath(Bgm.Value));
			if (Sound)
				BGMList.Add(Bgm.Key, Sound);
		}
	}
	
}

void UAwSFXManager::PlayBGM(USoundBase* Sound, float VolumeMultiplier, float PitchMultiplier, float StartTime, bool bPersistAcrossLevelTransition,
                            float LastFadeOutTime)
{
	if (MainVolume && BGMVolume && Sound)
	{
		if (BGMAudio && !BGMAudio->bIsFadingOut)
		{
			if (BGMAudio->IsPlaying() && BGMAudio->Sound == Sound)
			{
				//UKismetSystemLibrary::PrintString(this,"Play Same BGM Failed",true,true,FColor::Red,10.f);
				return;
			}
			StopBGM(LastFadeOutTime);
		}
		
		USoundConcurrency* SoundConcurrency = NewObject<USoundConcurrency>();
		SoundConcurrency->Concurrency.bLimitToOwner = true;
		//BGM 优先级稍高
		Sound->Priority = 2;
		UAudioComponent* NewSystemAudio = UGameplayStatics::CreateSound2D(GWorld, Sound, VolumeMultiplier * BGMVolume, PitchMultiplier, StartTime, SoundConcurrency, bPersistAcrossLevelTransition, true);
		if (NewSystemAudio)
		{
			if (!BGMAudio)
			{
				NewSystemAudio->Play();
				BGMAudio = NewSystemAudio;
			}
			else
			{
				BGMDelayToPlay = NewSystemAudio;
				if (MyTimerHandle.IsValid())
					MyTimerHandle.Invalidate();
				GWorld->GetTimerManager().SetTimer(MyTimerHandle, this, &UAwSFXManager::DelayToPlayBGMTimer, LastFadeOutTime, false);
				//UKismetSystemLibrary::PrintString(this,"Play Same BGM Delay:"+FString::SanitizeFloat(LastFadeOutTime),true,true,FColor::Red,10.f);
			}
			NewSystemAudio->SetTickableWhenPaused(true);
		}
		else
		{
			//UKismetSystemLibrary::PrintString(this,"Play Same BGM Failed Because Create Failed",true,true,FColor::Red,10.f);
		}
	}
}

void UAwSFXManager::PlayBgmByKey(FString Key, float VolumeMultiplier, float PitchMultiplier, float StartTime,
	bool bPersistAcrossLevelTransition, float LastFadeOutTime)
{
	if (BGMList.Contains(Key))
	{
		USoundBase* SoundBase = BGMList[Key];
		if (SoundBase) 
			PlayBGM(SoundBase, VolumeMultiplier, PitchMultiplier, StartTime, bPersistAcrossLevelTransition, LastFadeOutTime);
	}
}

void UAwSFXManager::StopBGM(float FadeOutTime)
{
	if (BGMAudio == nullptr)
		return;

	//UKismetSystemLibrary::PrintString(this,"Stop BGM",true,true,FColor::Red,10.f);

	if (BGMAudio->IsPlaying())
	{
		BGMAudio->FadeOut(FadeOutTime, 0.0f, EAudioFaderCurve::Linear);
	}
	else
	{
		BGMAudio->DestroyComponent();
		BGMAudio = nullptr;
	}
}

void UAwSFXManager::DelayToPlayBGMTimer()
{
	if (BGMDelayToPlay)
	{
		if (BGMAudio)
		{
			BGMAudio->Stop();
			BGMAudio->DestroyComponent();
		}
		BGMDelayToPlay->Play();
		BGMAudio = BGMDelayToPlay;
		BGMDelayToPlay = nullptr;
	}
}
