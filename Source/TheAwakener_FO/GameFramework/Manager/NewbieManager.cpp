// Fill out your copyright notice in the Description page of Project Settings.


#include "NewbieManager.h"

#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/FunctionLibrary/ResourceFuncLib.h"

void UNewbieManager::AddImage(FString Key, FString ImagePath)
{
	if (this->NewbieImages.Contains(Key) ) return;
	
	this->NewbieImages.Add(Key, ImagePath);
}

void UNewbieManager::ShowNewbie(TArray<FString> Keys)
{
	UNewbieImage* NewbieMain = nullptr;
	if(!UGameplayFuncLib::IsRogueMode())
	{
		if(!UGameplayFuncLib::GetUiManager()->OpenedWidgets.Contains("Newbie_Main"))
			NewbieMain = Cast<UNewbieImage>(UGameplayFuncLib::GetUiManager()->Show("Newbie_Main"));
	}
	else
	{
		if(!UGameplayFuncLib::GetUiManager()->OpenedWidgets.Contains("Rogue_Newbie_Main"))
			NewbieMain = Cast<UNewbieImage>(UGameplayFuncLib::GetUiManager()->Show("Rogue_Newbie_Main"));
	}

	for (FString Key : Keys)
	{
		if(UGameplayFuncLib::GetUiManager()->OpenedWidgets.Contains(Key)) return;
		//if (this->NewbieImages.Contains(Key) == false) return;
		UNewbieImage* Image = Cast<UNewbieImage>(UGameplayFuncLib::GetUiManager()->Show(Key));
		//const FString UIPath = UResourceFuncLib::GetWbpAssetPath(NewbieImages[Key]);
		//UClass* Class = LoadClass<UNewbieImage>(this, *UIPath);
		//UNewbieImage* Image = nullptr;
		/*AAwPlayerController* PC = UGameplayFuncLib::GetMyAwPlayerController();
		if (Class)
			Image = Cast<UNewbieImage>(CreateWidget<UUserWidget>(PC, Class));*/
		if (!Image) return;
		Image->PlayAnim();
		if(!NewbieMain) return;
		NewbieMain->WidgetSwitcherMain->AddChild(Image);
		NewbieMain->OpenedKeys.Add(Key);
		if(!UGameplayFuncLib::GetAwGameInstance()->RoleInfo.KnownNewbieArray.Contains(Key))
			UGameplayFuncLib::GetAwGameInstance()->RoleInfo.KnownNewbieArray.Add(Key);
	}
	NewbieMain->Show();
	NewbieMain->ChangeConfirmText();
	//Image->Show();
}
