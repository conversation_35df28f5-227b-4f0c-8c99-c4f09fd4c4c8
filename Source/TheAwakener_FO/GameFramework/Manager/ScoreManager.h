// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/Object.h"
#include "ScoreManager.generated.h"

/**
 * 积分管理器，负责记录连击、浮空等分数内容
 */
UCLASS()
class THEAWAKENER_FO_API UScoreManager : public UObject
{
	GENERATED_BODY()
private:
	//剩多少秒Combo结束
	UPROPERTY()
	float TimeToComboEnd = 0;

	//上次命中的时间
	UPROPERTY()
	int64 LastTimeComboHit = 0;

	//浮空最小计时（当前浮空时间-这个如果<=0就不算）
	UPROPERTY()
	float AerialTimeValve = 0;
public:
	//最后几次连击记录，越往后面的越新
	UPROPERTY()
	TArray<int> ComboRecords;
	
	//当前连击次数
	UPROPERTY()
	int CurrentCombo = 0;
	
	//当前连杀数
	UPROPERTY()
	int CurrentKillCombo = 0;
	
	//最后几次飞空时间记录
	UPROPERTY()
	TArray<float> AerialRecords;

	//当前浮空时间
	UPROPERTY()
	float CurrentAerialTime = 0;

	//强行结束当前的combo了
	void ComboTerminated();

	//一次浮空结束
	void AerialTerminated();

	//增加一次ComboHit，这个
	void AddCombo(float ComboNextTime);
	void AddCombo(int ComboNum,float ComboNextTime);

	//增加击杀计数
	UFUNCTION(BlueprintCallable)
	void AddKillCombo(int KillNum);

	UFUNCTION(BlueprintCallable)
	void ResetKillCombo()
	{
		CurrentKillCombo = 0;
	};

	//增加飞空时间
	void AddAerialTime(float Time);
};
