// Fill out your copyright notice in the Description page of Project Settings.


#include "AwSequenceManager.h"
#include "TheAwakener_FO/FunctionLibrary/GamePlayFuncLib.h"

void UAwSequenceManager::Initialize(FSubsystemCollectionBase& Collection)
{
	Super::Initialize(Collection);

}


ULevelSequencePlayer* UAwSequenceManager::GetGlobalSequencePlayer()
{
	{ if(!GlobalSequencePlayerInstance.IsValid()) return nullptr;
		return  GlobalSequencePlayerInstance->SequencePlayer; }
}

bool UAwSequenceManager::IsGlobalSequencePlaying()
{
	if (GlobalSequencePlayerInstance.IsValid())
	{
		//UE_LOG(LogTemp, Error, TEXT("GlobalLevelSequenceActor instance is not vaild"));
		return false;
	}
	if (!GlobalSequencePlayerInstance->SequencePlayer->IsValidLowLevel())
	{
		return false;
	}
	return GlobalSequencePlayerInstance->SequencePlayer->IsPlaying();
}

bool UAwSequenceManager::IsGlobalSequencePaused()
{
	if (GlobalSequencePlayerInstance.IsValid())
	{
		//UE_LOG(LogTemp, Error, TEXT("AwLevelSequenceActor instance is not vaild"));
		return false;
	}
	if (!GlobalSequencePlayerInstance->SequencePlayer->IsValidLowLevel())
	{
		return false;
	}
	return GlobalSequencePlayerInstance->SequencePlayer->IsPaused();
}

void UAwSequenceManager::PlaySequence(ALevelSequenceActor* SequenceActor, ESequenceType SequenceType)
{
	if (!SequenceActor->IsValidLowLevel())
	{
		UE_LOG(LogTemp, Error, TEXT("LevelSequenceActor is not vaild"));
		return;
	}
	else if (!SequenceActor->SequencePlayer->IsValidLowLevel())
	{
		UE_LOG(LogTemp, Error, TEXT("LevelSequencePlayer is not vaild"));
		return;
	}

	if (IsGlobalSequencePlaying())
	{
		StopSequence(GetGlobalSequenceActor());
	}

	RegisterSequencePlayer(SequenceActor,SequenceType);
	SequenceActor->SequencePlayer->Play();

	//全局Play多播广播 以供回调 避免tick检测
	if (OnGlobalSequencePlayEvent.IsBound())
	{
		OnGlobalSequencePlayEvent.Broadcast(SequenceActor);
	}

}


void UAwSequenceManager::PauseSequence(ALevelSequenceActor* SequenceActor)
{
	if (!SequenceActor->IsValidLowLevel())
	{
		UE_LOG(LogTemp, Error, TEXT("LevelSequenceActor is not vaild"));
		return;
	}
	else if (!SequenceActor->SequencePlayer->IsValidLowLevel())
	{
		UE_LOG(LogTemp, Error, TEXT("LevelSequencePlayer is not vaild"));
		return;
	}

	SequenceActor->SequencePlayer->Pause();
}

void UAwSequenceManager::StopSequence(ALevelSequenceActor* SequenceActor)
{
	if (!SequenceActor->IsValidLowLevel())
	{
		UE_LOG(LogTemp, Error, TEXT("LevelSequenceActor is not vaild"));
		return;
	}
	else if (!SequenceActor->SequencePlayer->IsValidLowLevel())
	{
		UE_LOG(LogTemp, Error, TEXT("LevelSequencePlayer is not vaild"));
		return;
	}
	SequenceActor->SequencePlayer->Stop();
}

FLevelSequenceInfo UAwSequenceManager::GetSequenceInfoFromDataManager(FString SequenceID)
{
	FLevelSequenceInfo result = FLevelSequenceInfo();
	//To Do 需要确定信息包含哪些 再由DataManager通过数据源获取的 信息表中 找到对应的sequence asset的信息
	return result;
}

void UAwSequenceManager::RegisterSequencePlayer(ALevelSequenceActor* InSequenceActor, ESequenceType SequenceType)
{
	if (InSequenceActor->IsValidLowLevel())
	{
		if (SequenceType == ESequenceType::Global)
		{
			GlobalSequencePlayerInstance = InSequenceActor;

			GlobalSequencePlayerInstance->OnEndPlay.AddUniqueDynamic(this, &UAwSequenceManager::OnGlobalSequencePlayerEndPlay);

			GlobalSequencePlayerInstance->SequencePlayer->OnStop.AddUniqueDynamic(this,&UAwSequenceManager::GlobalSequenceStopEventBroadcast);
			GlobalSequencePlayerInstance->SequencePlayer->OnPause.AddUniqueDynamic(this, &UAwSequenceManager::GlobalSequencePauseEventBroadcast);
		}
		else
		{
			if (!CurLocalSequencePlayers.Contains(InSequenceActor))
			{
				CurLocalSequencePlayers.Add(InSequenceActor);
				//此处 local绑定 注销相关
				InSequenceActor->OnEndPlay.AddUniqueDynamic(this, &UAwSequenceManager::OnLocalSequencePlayerEndPlay);

			}

		}		
	}
}

void UAwSequenceManager::UnRegisterSequencePlayer(ALevelSequenceActor* InSequenceActor, ESequenceType SequenceType)
{
	if(InSequenceActor== GlobalSequencePlayerInstance)
	{
		GlobalSequencePlayerInstance = nullptr;
	}
	else
	{
		if (CurLocalSequencePlayers.Contains(InSequenceActor))
		{
			CurLocalSequencePlayers.Remove(InSequenceActor);
		}
	}
}



bool UAwSequenceManager::IsAnyLocalSequencePlaying()
{
	bool result = false;
	for (auto Seq:CurLocalSequencePlayers)
	{
		if (Seq->IsValidLowLevel() && Seq->SequencePlayer->IsPlaying())
		{
			result = true;
			break;
		}
	}
	return result;
}


void UAwSequenceManager::OnGlobalSequencePlayerEndPlay(AActor* DestoryActor, EEndPlayReason::Type EndPlayReason)
{
	ALevelSequenceActor* InSequenceActor = Cast<ALevelSequenceActor>(DestoryActor);

	UnRegisterSequencePlayer(InSequenceActor, ESequenceType::Global);
}

void UAwSequenceManager::OnLocalSequencePlayerEndPlay(AActor* DestoryActor, EEndPlayReason::Type EndPlayReason)
{
	ALevelSequenceActor* InSequenceActor = Cast<ALevelSequenceActor>(DestoryActor);

	UnRegisterSequencePlayer(InSequenceActor, ESequenceType::Local);
}

void UAwSequenceManager::GlobalSequenceStopEventBroadcast()
{
	if (OnGlobalSequenceStopEvent.IsBound())
	{
		OnGlobalSequenceStopEvent.Broadcast(GetGlobalSequenceActor());
	}
}

void UAwSequenceManager::GlobalSequencePauseEventBroadcast()
{
	if (OnGlobalSequencePauseEvent.IsBound())
	{
		OnGlobalSequencePauseEvent.Broadcast(GetGlobalSequenceActor());
	}
}


