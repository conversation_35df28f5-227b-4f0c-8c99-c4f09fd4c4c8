// Fill out your copyright notice in the Description page of Project Settings.


#include "AwLootManager.h"

#include "Kismet/GameplayStatics.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/FunctionLibrary/ResourceFuncLib.h"
#include "TheAwakener_FO/GameFramework/Base/AwGameModeBase.h"

void UAwLootManager::DropLootPackageByLootPackageIds(TArray<FString> LootPackageIds, FVector Location)
{
	TArray<FThingObj> FThingObjs;
	for (const FString LootPackageId : LootPackageIds) 
		FThingObjs.Append(UGameplayFuncLib::GetDataManager()->GetLootPackageById(LootPackageId).GetOnePackageLoot());
	
	if (FThingObjs.Num() > 0)
	{
		FTransform Transform = FTransform::Identity;
		Transform.SetLocation(Location);

		//TODO 暂时不掉落包包，走下面的流程直接获得
		UGameMain* MainUI = nullptr;
	
		if (UGameplayFuncLib::GetUiManager()->OpenedWidgets.Contains("GameMain"))
			MainUI = Cast<UGameMain>(UGameplayFuncLib::GetUiManager()->OpenedWidgets["GameMain"]);
	
		UKismetSystemLibrary::PrintString(GWorld, "------------- Pick Up Loots!");
		for (FThingObj ThingObj : FThingObjs)
		{
			UGameplayFuncLib::GetAwGameInstance()->RoleInfo.GiveThing(ThingObj);
			if (ThingObj.Type != EThingType::Equipment &&
				ThingObj.Type != EThingType::WeaponModel &&
				ThingObj.Type != EThingType::WeaponObj)
			{
				if (MainUI)
					MainUI->NewThingHint(ThingObj);
				UKismetSystemLibrary::PrintString(GWorld,FString("ID:").Append(ThingObj.Id)
					.Append("   Count:").Append(FString::FromInt(ThingObj.Count)));
			}
		}

		//TODO 暂时不掉落包包，走上面的流程直接获得
		//CreateLootPackageActor(FThingObjs, Transform);
	}
}

void UAwLootManager::DropLootPackageFromMob(FString MobId, FVector Location)
{
	if (MobId == "")
		return;
	bool match = false;
	for (auto player : UGameplayFuncLib::GetAwGameState()->GetPlayerCharacters())
	{
		if(player && FVector::Dist(player->GetActorLocation(), Location) < 750)
		{
			match = true;
			break;
		}
	}
	if(!match)
	{
		return;
	}
	AAwGameModeBase* CurGameMode = Cast<AAwGameModeBase>(UGameplayStatics::GetGameMode(GWorld));
	
	TArray<FString> LootPackageIds;
	if (IsValid(CurGameMode))
		LootPackageIds.Append(CurGameMode->GetMobLootPackages());
	const FMobModel MobModel = UGameplayFuncLib::GetDataManager()->GetMobModelById(MobId);
	if(MobModel.Id != "")
	{
		LootPackageIds.Add(MobModel.LootPackageId);
	}
	
	DropLootPackageByLootPackageIds(LootPackageIds, Location);
}

void UAwLootManager::DropLootPackageFromChest(FVector Location)
{
	AAwGameModeBase* CurGameMode = Cast<AAwGameModeBase>(UGameplayStatics::GetGameMode(GWorld));
	
	TArray<FString> LootPackageIds;
	if (IsValid(CurGameMode))
		LootPackageIds.Append(CurGameMode->GetChestLootPackages());
	
	DropLootPackageByLootPackageIds(LootPackageIds, Location);
}

void UAwLootManager::PickUpThingObjs(TArray<FThingObj> ThingObjs)
{
	for (const FThingObj ThingObj : ThingObjs)
	{
		UAwGameInstance::Instance->RoleInfo.GiveThing(ThingObj);
		// switch (ThingObj.Type)
		// {
		// case EThingType::Character:
		// 	{
		// 		const FAwCharacterInfo CharacterInfo = FThingObj::CreateCharacterByThing(ThingObj);
		// 		for (int i = 0; i < ThingObj.Count; i++)
		// 			UAwGameInstance::Instance->RoleInfo.OtherCharacters.Add(CharacterInfo);
		// 		break;
		// 	}
		// case EThingType::Buff:
		// 	{
		// 		const FAddBuffInfo AddBuffInfo = FThingObj::CreateBuffByThing(ThingObj);
		// 		UGameplayFuncLib::GetAwGameState()->MyCharacter->AddBuff(AddBuffInfo);
		// 		//TODO: 感觉不对
		// 		break;
		// 	}
		// case EThingType::Currency:
		// 	{
		// 		UAwGameInstance::Instance->RoleInfo.Currency[ThingObj.Id] += ThingObj.Count;
		// 		break;
		// 	}
		// case EThingType::Equipment:
		// 	{
		// 		const FEquipment Equip = FThingObj::CreateEquipmentByThing(ThingObj);
		// 		for (int i = 0; i < ThingObj.Count; i++)
		// 			UAwGameInstance::Instance->RoleInfo.EquipmentObjs.Add(Equip);
		// 		break;
		// 	}
		// case EThingType::Item:
		// 	{
		// 		FItemModel ItemModel = FThingObj::CreateItemByThing(ThingObj);
		// 		FItemObj ItemObj = FItemObj(ItemModel);
		// 		for (int i = 0; i < ThingObj.Count; i++)
		// 			UAwGameInstance::Instance->RoleInfo.ItemObjs.Add(ItemObj);
		// 		break;
		// 	}
		// case EThingType::Switch:
		// 	{
		// 		//TODO:
		// 		break;
		// 	}
		// case EThingType::WeaponModel:
		// 	{
		// 		FWeaponModel WeaponModel = FThingObj::CreateWeaponByThing(ThingObj);
		// 		for (int i = 0; i < ThingObj.Count; i++)
		// 			UAwGameInstance::Instance->RoleInfo.WeaponObjs.Add(WeaponModel);
		// 		break;
		// 	}
		// case EThingType::Pointer:
		// 	{
		// 		//TODO:
		// 		break;
		// 	}
		// default: break;
		// }
	}
}

AThingPackageActor* UAwLootManager::FindWillPickUpLootActor(AAwCharacter* Character)
{
	float MinDisSqr = 100000;
	AThingPackageActor* WillActor = nullptr;
	for (AThingPackageActor* LootActor : LootPackageActors)
	{
		if (LootActor->ToBeRemoved || LootActor->SignedPlayerControllers.Contains(Character->OwnerPlayerController))
			continue;
		
		for (AAwCharacter* Cha : LootActor->CanPickUpChas)
			if (Cha == Character)
			{
				if (WillActor == nullptr)
				{
					WillActor = LootActor;
					MinDisSqr = FVector::DistSquared(LootActor->GetActorLocation(), Cha->GetActorLocation());
				}
				else
				{
					const float DisSqr = FVector::DistSquared(LootActor->GetActorLocation(), Cha->GetActorLocation());
					if (DisSqr < MinDisSqr)
					{
						WillActor = LootActor;
						MinDisSqr = DisSqr;
					}
				}
			}
	}
	
	return WillActor;
}

void UAwLootManager::TickChoseWillLootActor()
{
	AAwCharacter* MyCharacter = UGameplayFuncLib::GetAwGameState()->GetMyCharacter();
	
	if(!MyCharacter)
		return;
	
	if (IsValid(WillPickUpLootActor))
	{
		const float DistSqr = FVector::DistSquared(MyCharacter->GetActorLocation(), WillPickUpLootActor->GetActorLocation());
		const float CancelDist = FMath::Max(MyCharacter->PickUpInfo.PickUpDistance, CancelWillDist);
		if (DistSqr >= CancelDist*CancelDist)
		{
			WillPickUpLootActor->HideUI();
			WillPickUpLootActor = nullptr;
		}
	}
	else
	{
		// find will loot actor
		//WillPickUpLootActor = FindWillPickUpLootActor(MyCharacter);
	}
	
	if (IsValid(WillPickUpLootActor))
	{
		WillPickUpLootActor->ShowUI();
		//MyCharacter->GetCmdComponent()->bIsSomethingCanBePicked = true;
	}
	//else
		//MyCharacter->GetCmdComponent()->bIsSomethingCanBePicked = false;
		
}

void UAwLootManager::RemoveLootPackageActor(AThingPackageActor* LootPackageActor)
{
	if (WillPickUpLootActor) 
		WillPickUpLootActor->HideUI();
	WillPickUpLootActor = nullptr;
	LootPackageActors.Remove(LootPackageActor);
}

void UAwLootManager::CreateLootPackageActor(TArray<FThingObj> ThingObjs, FTransform Transform)
{
	AThingPackageActor* PackageActor = Cast<AThingPackageActor>(UGameplayFuncLib::CreateActorByBP(
		UResourceFuncLib::GetThingPackageActorBpPath(), Transform));
	LootPackageActors.Add(PackageActor);
	PackageActor->ThingObjs = ThingObjs;
	PackageActor->PlayDropAnim();
}
