// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "AwTeamManager.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UAwTeamManager : public UObject
{
	GENERATED_BODY()
public:
	UPROPERTY(BlueprintReadWrite)
	TArray<AAwPlayerController*> PlayerList;

	UPROPERTY(BlueprintReadWrite)
	TArray<AAwCharacter*> TeamMateList;
public:
	UFUNCTION(BlueprintCallable)
	void AddPlayer(AAwPlayerController* NewPlayer);
	UFUNCTION(BlueprintCallable)
	void DeletePlayer(AAwPlayerController* DeletePlayer);
	UFUNCTION(BlueprintCallable)
	void AddTeamMate(AAwCharacter* Character);
	UFUNCTION(BlueprintCallable)
	void DeleteTeamMate(AAwCharacter* Character);

	
};
