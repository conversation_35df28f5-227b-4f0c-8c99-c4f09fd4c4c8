// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "Engine/DataTable.h"
#include "DataTableStruct.generated.h"

USTRUCT(BlueprintType)
struct THEAWAKENER_FO_API FDataTableCharacterClass : public FTableRowBase
{
	GENERATED_USTRUCT_BODY()
	FDataTableCharacterClass(){}
	
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TMap<FString, TSubclassOf<AAwCharacter>> CharacterClass;
};

UCLASS()
class THEAWAKENER_FO_API UDataTableStruct : public UObject
{
	GENERATED_BODY()
};