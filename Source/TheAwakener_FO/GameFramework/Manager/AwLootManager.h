// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/GamePlay/Thing/ThingObj.h"
#include "TheAwakener_FO/GamePlay/Thing/ThingPackageActor.h"
#include "UObject/Object.h"
#include "AwLootManager.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UAwLootManager : public UObject
{
	GENERATED_BODY()

public:
	UFUNCTION(BlueprintCallable)
	void DropLootPackageByLootPackageIds(TArray<FString> LootPackageIds, FVector Location);


	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<AThingPackageActor*> LootPackageActors;
	
// -------------------- DROP -------------------- 
	UFUNCTION(BlueprintCallable)
	void DropLootPackageFromMob(FString MobId, FVector Location);

	UFUNCTION(BlueprintCallable)
	void DropLootPackageFromChest(FVector Location);

	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay")
	void CreateLootPackageActor(TArray<FThingObj> ThingObjs, FTransform Transform);

// -------------------- PICK UP --------------------
	static void PickUpThingObjs(TArray<FThingObj> ThingObjs);

	AThingPackageActor* FindWillPickUpLootActor(AAwCharacter* Character);

// -------------------- UI --------------------
	UPROPERTY()
	AThingPackageActor* WillPickUpLootActor;
	float CancelWillDist = 300;
	void TickChoseWillLootActor();
	
	void RemoveLootPackageActor(AThingPackageActor* LootPackageActor);
};
