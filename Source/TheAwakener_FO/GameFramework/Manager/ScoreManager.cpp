// Fill out your copyright notice in the Description page of Project Settings.


#include "ScoreManager.h"

#include "Kismet/KismetSystemLibrary.h"
#include "TheAwakener_FO/FunctionLibrary/DateTimeFuncLib.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/UI/Roguelike/FightingMain/RogueFightingMainUI.h"


void UScoreManager::ComboTerminated()
{
	this->ComboRecords.Add(this->CurrentCombo);
	while (ComboRecords.Num() > 15) ComboRecords.RemoveAt(0);
	this->CurrentCombo = 0;
}

void UScoreManager::AerialTerminated()
{
	this->AerialRecords.Add(this->CurrentAerialTime);
	while (AerialRecords.Num() > 15) AerialRecords.RemoveAt(0);
	this->CurrentAerialTime = 0;
}

void UScoreManager::AddCombo(float ComboNextTime)
{
	AddCombo(1,ComboNextTime);
}

void UScoreManager::AddCombo(int ComboNum, float ComboNextTime)
{
	
	const int64 Now = UDateTimeFuncLib::GetTimestamp();
	if (Now - this->LastTimeComboHit > this->TimeToComboEnd * 1000)
	{
		ComboTerminated();
	}
	CurrentCombo += ComboNum;
	this->TimeToComboEnd = ComboNextTime;
	this->LastTimeComboHit = Now;
	//RPG模式
	if (UGameplayFuncLib::GetUiManager()->OpenedWidgets.Contains("GameMain"))
	{
		const UGameMain* MainUI = Cast<UGameMain>(UGameplayFuncLib::GetUiManager()->OpenedWidgets["GameMain"]);
		if (MainUI && CurrentCombo > 1)
		{
			MainUI->ShowHitCombo(this->CurrentCombo, ComboNextTime);
		}
	}
	//幸存者模式
	if (UAwGameInstance::Instance->isSurvivor)
	{
		if (UGameplayFuncLib::GetUiManager()->OpenedWidgets.Contains("RogueFighting_Main"))
		{
			const URogueFightingMainUI* MainUI = Cast<URogueFightingMainUI>(UGameplayFuncLib::GetUiManager()->OpenedWidgets["RogueFighting_Main"]);
			if (MainUI && CurrentCombo > 1)
			{
				MainUI->ShowHitCombo(this->CurrentCombo, ComboNextTime);
			}
		}
	}
	//UKismetSystemLibrary::PrintString(this, FString::FromInt(this->CurrentCombo).Append(" Combo").Append(CurrentCombo > 1 ? "s":""),
	//	true, true, FLinearColor::Yellow, 10);

	if (UGameplayFuncLib::GetAwGameInstance() && UGameplayFuncLib::GetAwGameState() && UGameplayFuncLib::GetAwGameState()->GetMyCharacter())
	{
		const int BattleClassIndex = UGameplayFuncLib::GetAwGameInstance()->RoleInfo.GetBattleClassIndexById(UGameplayFuncLib::GetAwGameState()->GetMyCharacter()->PlayerClassId);
		if (BattleClassIndex >= 0)
		{
			const int ExpGain = FMath::FloorToInt(FMath::Pow(this->CurrentCombo, 1.3f));
			const int LevelUp = UGameplayFuncLib::GetAwGameInstance()->RoleInfo.BattleClassInfo[BattleClassIndex].LevelExp.AddExp(ExpGain);
			if (LevelUp > 0)
			{
				UKismetSystemLibrary::PrintString(this, FString("Class Level Up ").Append(FString::FromInt(LevelUp)),
					true, true, FLinearColor::Green, 30);
			}
		}
	}
}

void UScoreManager::AddKillCombo(int KillNum)
{
	const int64 Now = UDateTimeFuncLib::GetTimestamp();
	CurrentKillCombo += KillNum;
	if (UAwGameInstance::Instance->isSurvivor)
	{
		if (UGameplayFuncLib::GetUiManager()->OpenedWidgets.Contains("RogueFighting_Main"))
		{
			const URogueFightingMainUI* MainUI = Cast<URogueFightingMainUI>(UGameplayFuncLib::GetUiManager()->OpenedWidgets["RogueFighting_Main"]);
			if (MainUI && CurrentKillCombo > 1)
			{
				MainUI->ShowKillCombo(this->CurrentKillCombo, 3);
			}
		}
	}
}

void UScoreManager::AddAerialTime(float Time)
{
	this->CurrentAerialTime += Time;
}
