// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/HUD.h"
#include "AW_HUD.generated.h"

class UBaseForm;
class UPopText;
/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API AAW_HUD : public AHUD
{
	GENERATED_BODY()

	virtual void BeginPlay() override;
	
	virtual void Tick(float DeltaSeconds) override;
	
public:

	UPROPERTY(BlueprintReadWrite)
	TMap<FString, UBaseForm*> Normal;

	//Forms的栈（仅存放Popup和HideOther）
	UPROPERTY(BlueprintReadWrite)
	TArray<UBaseForm*> FormsStack;


public:
	UFUNCTION(BlueprintCallable)
	void RemoveFromHUD(UBaseForm* Find);

	UFUNCTION(BlueprintCallable)
	UBaseForm* GetFormByNormal(FString FormName)
	{
		if (Normal.Contains(FormName))
			return Normal[FormName];
		else
			return nullptr;
	};
	
};
