// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/GamePlay/Buff/Buff.h"
#include "UObject/NoExportTypes.h"
#include "IceDevilBuff.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UIceDevilBuff : public UObject
{
	GENERATED_BODY()
public:
	//【OnHit】命中敌人后给自己添加憎恨buff,添加的憎恨buff层数等于该buff的层数
	UFUNCTION()
	static FBuffDamageResult AddHateBuff(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);
	
	//【OnHit】给伤害对象添加冰霜buff,添加的buff层数等于该buff的层数
	UFUNCTION()
	static FBuffDamageResult AddFrostBuff(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【OnOffense】打到冰柱、冰镰刀等SceneItem时，直接打爆，并生成一个冰冻敌人的AOE
	UFUNCTION()
	static FBuffOffenseResult HitIceSceneItem(FBuffObj BuffObj, AActor* Target, FOffenseInfo OInfo, TArray<FString> Params);

	//【OnOccur】当冰霜buff到Params[0]层时，让角色进入冰冻状态Params[1]秒
	UFUNCTION()
	static FBuffRunResult SetFreezingWhenStackArrive(FBuffObj BuffObj, int WasStack, TArray<FString> Params);
	
};
