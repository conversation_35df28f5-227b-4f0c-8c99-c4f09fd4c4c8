// Fill out your copyright notice in the Description page of Project Settings.


#include "IceDevilBuff.h"

#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"

FBuffDamageResult UIceDevilBuff::AddHateBuff(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	FBuffModel BuffModel = UGameplayFuncLib::GetDataManager()->GetBuffModelById("IceDevilHate");
	if(BuffModel.Id != "")
	{
		FAddBuffInfo BuffInfo = FAddBuffInfo(BuffObj.Carrier, BuffObj.Carrier, BuffModel, BuffObj.Stack, 0, false, true);
		BuffObj.Carrier->AddBuff(BuffInfo);
	}
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	return Res;
}

FBuffDamageResult UIceDevilBuff::AddFrostBuff(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	if(!Target->Dead(true))
	{
		TArray<FBuffObj*> BuffList = Target->GetBuff("Standard_PlayerFrozen",TArray<AAwCharacter*>());
		if(BuffList.Num() == 0)
		{
			FBuffModel BuffModel = UGameplayFuncLib::GetDataManager()->GetBuffModelById("ForstBuff");
			if(BuffModel.Id != "")
			{
				FAddBuffInfo BuffInfo = FAddBuffInfo(BuffObj.Carrier, Target, BuffModel, BuffObj.Stack, 15, true);
				Target->AddBuff(BuffInfo);
			}
		}
	}
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	return Res;
}

FBuffOffenseResult UIceDevilBuff::HitIceSceneItem(FBuffObj BuffObj, AActor* Target, FOffenseInfo OInfo, TArray<FString> Params)
{
	FBuffOffenseResult Res = FBuffOffenseResult();
	Res.BuffObj = BuffObj;
	Res.OffenseInfo = OInfo; 
	AAwSceneItem* SceneItem = Cast<AAwSceneItem>(Target);
	if(SceneItem && SceneItem->Tags.Contains("IceDevilSceneItem"))
	{
		Res.OffenseInfo.AttackInfo.DamagePower = 999999;
		//生成一个AOE
		const AAWAoe* Aoe = UGameplayFuncLib::CreateAOE(BuffObj.Carrier, "IceDevilFreezingAOE", Target->GetActorLocation(), FVector(1, 0, 0), 0.1, "");
		if(Aoe)
		{
			Aoe->AttackHitManager->ActiveAllAttackHitBox();
		}
	}
	return Res;
}

FBuffRunResult UIceDevilBuff::SetFreezingWhenStackArrive(FBuffObj BuffObj, int WasStack, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	int TargetStack =  BuffObj.Model.MaxStack;
	if(Params.Num())
		TargetStack = FCString::Atoi(*Params[0]);
	UKismetSystemLibrary::PrintString(BuffObj.Carrier, FString("ForstBuff: ").Append(FString::FromInt(BuffObj.Stack)),
		true,false,FLinearColor::Red);
	if(BuffObj.Stack == TargetStack)
	{
		//给角色添加冰冻Buff
		FBuffModel FreezingBuffModel = UGameplayFuncLib::GetDataManager()->GetBuffModelById("Standard_PlayerFrozen");
		if(FreezingBuffModel.Id != "")
		{
			float FrozenTime = 5;
			if(Params.Num() > 1)
				FrozenTime = FCString::Atoi(*Params[1]);
			FAddBuffInfo BuffInfo = FAddBuffInfo(BuffObj.Caster, BuffObj.Carrier, FreezingBuffModel, 1, FrozenTime, true, false);
			BuffObj.Carrier->AddBuff(BuffInfo);
			UKismetSystemLibrary::PrintString(BuffObj.Carrier, BuffObj.Carrier->GetFName().ToString().Append(FString(": Freezing!")),
				true,false,FLinearColor::Red);
		}
		Res.BuffObj.Duration = 0;
		return Res;
	}
	return Res;
}

