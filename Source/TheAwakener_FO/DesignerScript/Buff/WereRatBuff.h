// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/GamePlay/Buff/Buff.h"
#include "UObject/NoExportTypes.h"
#include "WereRatBuff.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UWereRatBuff : public UObject
{
	GENERATED_BODY()
public:
	//【OnTick】一段时间没有攻击命中后添加优先闪避的BUFF
	UFUNCTION()
	static FBuffRunResult AddDodgeBuff(FBuffObj BuffObj, int32 Ticked, TArray<FString> Params);

	//【OnHit】在命中敌人后重新添加优先闪避的BUFF的时间
	UFUNCTION()
	static FBuffDamageResult ResetCheckDodgeBuff(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【OnOccur】删除CheeseAOE
	UFUNCTION()
	static FBuffRunResult DeleteCheeseAOE(FBuffObj BuffObj, int32 WasStack, TArray<FString> Params);

	//【OnOccur】查找最近的逃跑点
	UFUNCTION()
	static FBuffRunResult GetClosetEscapePoint(FBuffObj BuffObj, int32 WasStack, TArray<FString> Params);

	//【OnOccur】把buff的最大层数存在Buff的Param中
	UFUNCTION()
	static FBuffRunResult RecordMaxStack(FBuffObj BuffObj, int32 WasStack, TArray<FString> Params);

	//【OnRemoved】萨满记录场上的鼠人数量,Params[0] 为检测的范围
	UFUNCTION()
	static FBuffRunResult RecordWereRatNum(FBuffObj BuffObj, int32 WasStack, TArray<FString> Params);

	//【OnBeKilled】死亡时把减少一层添加者的RecordWereRatNum Buff
	UFUNCTION()
	static FBuffDamageResult ReduceWereRatNumRecord(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【OnBeHurt】累积伤害
	UFUNCTION()
	static FBuffDamageResult AccumulateDamage(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【OnBeHurt】添加生气BUFF
	UFUNCTION()
	static FBuffDamageResult AddAngryBuff(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);
};
