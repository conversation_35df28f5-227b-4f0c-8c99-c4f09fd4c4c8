// Fill out your copyright notice in the Description page of Project Settings.


#include "AwakeBuff.h"

#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GamePlay/Battle/OffenseManager.h"
#include "TheAwakener_FO/GamePlay/Buff/BuffManager.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"

FBuffDamageResult UAwakeBuff ::BloodlyThirsty(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
	TArray<FString> Params)
{
	//原本伤害信息
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	
	//暂时靠此 防止吸血触发吸血
	if (DamInfo.IsHeal)
	{
		return  Res;
	}
	//产生一次吸血
	float Percent =  Params.Num()>0?FCString::Atof(*Params[0]):0  ;
	
	FOffenseInfo Offense = FOffenseInfo();
	Offense.AttackInfo = FAttackInfo();
	Offense.AttackInfo.DamagePower =  Res.DamageInfo.DamagePower*Percent* BuffObj.Stack;
	if (Offense.AttackInfo.DamagePower.TotalDamage()<1)
	{
		Offense.AttackInfo.DamagePower = 1;
	}
	Offense.AttackInfo.IsHeal = true;
	Offense.AttackInfo.DamageSourceType = EAttackSource::Buff;
	Offense.AttackInfo.DamageType = EDamageType::ExtraDamage;
	Offense.SourceId = BuffObj.Model.Id;
	Offense.CanHitTimes = 0;
	Offense.Index = BuffObj.Duration;
	UOffenseManager::DoBuffOffense(Offense, BuffObj.Carrier,BuffObj.Carrier);
	
	
	return Res;
}

FBuffRunResult UAwakeBuff ::CheckAwakeBuffCost(FBuffObj BuffObj, int32 Ticked, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	Res.TimelineNode = nullptr;
	if (!BuffObj.Carrier||!BuffObj.Carrier->OwnerPlayerController)return Res;
	FAwActionSkillInfo* SkillInfo = UGameplayFuncLib::GetPlayerCurAwkeSkill(BuffObj.Carrier->OwnerPlayerController);
	if(!SkillInfo)
		return Res;
	int cost =FMath::Clamp(SkillInfo->EnergyCostPerSecond*BuffObj.Model .TickTime,1,BuffObj.Carrier->CharacterObj.CurrentRes.AP);

	bool bCostFit = BuffObj.Carrier->CharacterObj.CurrentRes.AP>0&&BuffObj.Carrier->CharacterObj.CurrentRes.AP>cost;
	if (bCostFit)
	{
		BuffObj.Carrier->CharacterObj.CurrentRes.AP = FMath::Max(0, BuffObj.Carrier->CharacterObj.CurrentRes.AP - cost);
	}
	else
	{
		BuffObj.Carrier->CharacterObj.CurrentRes.AP = 0;
		Res.BuffObj.ToBeRemoved = true;
		BuffObj.Carrier->RemoveBuffByTag("Awake");
		//通过carrier 尝试做一次动作
		BuffObj.Carrier->TryPreorderAction(SkillInfo->SkillCloseActionId);
	}
	return  Res;
}

FBuffRunResult UAwakeBuff::ActiveAwakeSkill(FBuffObj BuffObj, int WasStack, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	AAwPlayerState* PlayerState = nullptr;
	if (BuffObj.Carrier->OwnerPlayerController)
		PlayerState = BuffObj.Carrier->OwnerPlayerController->GetPlayerState<AAwPlayerState>();
	FString AwakeSkillId =  Params.Num()>0?Params[0]:"" ;
	if (!PlayerState)
	{
		return Res;
	}
	PlayerState->ActivePlayerCurAwakeSkill(true);
	return Res;
}

FBuffRunResult UAwakeBuff::CloseAwakeSkill(FBuffObj BuffObj,bool IsDispelled, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	AAwPlayerState* PlayerState = nullptr;
	if (BuffObj.Carrier->OwnerPlayerController)
		PlayerState = BuffObj.Carrier->OwnerPlayerController->GetPlayerState<AAwPlayerState>();
	FString AwakeSkillId =  Params.Num()>0?Params[0]:"" ;
	if (!PlayerState)
	{
		return Res;
	}
	PlayerState->ActivePlayerCurAwakeSkill(false);
	return  Res;
}

