// Fill out your copyright notice in the Description page of Project Settings.


#include "SceneItemBuff.h"

#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"

FBuffDamageResult USceneItemBuff::ProtectorIsDead(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
                                                  TArray<FString> Params)
{
	FBuffDamageResult Result = FBuffDamageResult(BuffObj, DamInfo, nullptr);
	const FString SceneItemId = "SceneItemId";
	if (BuffObj.Param.Contains(SceneItemId) && UGameplayFuncLib::GetAwGameState()->SceneItemList.Contains(BuffObj.Param[SceneItemId]))
	{
		AAwSceneItem* SceneItem = UGameplayFuncLib::GetAwGameState()->SceneItemList[BuffObj.Param[SceneItemId]];
		if (SceneItem)
			SceneItem->RemoveProtector(BuffObj.Carrier);
	}
	return Result;
}

FBuffRunResult USceneItemBuff::DoProtectAction(FBuffObj BuffObj, int32 Ticked, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult(BuffObj, nullptr);

	if (Params.Num() >= 1)
	{
		const int CheckStack = FCString::Atoi(*Params[0]);
		if (BuffObj.Stack >= CheckStack)
		{
			if (BuffObj.Carrier->HasAction("ProtectSceneItem"))
			{
				const FAICommand NewAiCommand = BuffObj.Carrier->GetAIComponent()->CreateUseActionCommand("ProtectSceneItem",
					BuffObj.Carrier->GetActorForwardVector());
				BuffObj.Carrier->GetAIComponent()->SetInputFromAICommand(NewAiCommand);
			}
		}
	}
	
	return Res;
}
