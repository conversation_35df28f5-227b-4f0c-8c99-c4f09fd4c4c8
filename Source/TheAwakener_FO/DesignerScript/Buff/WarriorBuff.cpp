// Fill out your copyright notice in the Description page of Project Settings.


#include "WarriorBuff.h"


FBuffDamageResult UWarriorBuff::ChargingHit(FBuffObj Buff, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params)
{
	if (DamInfo.DamageSourceType == EAttackSource::AttackAction)
	{
		DamInfo.DamagePower = DamInfo.DamagePower * FCString::Atof(*Params[0]);
	}
	
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = Buff;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;
	return Res;
}
