// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/GamePlay/Buff/Buff.h"
#include "UObject/Object.h"
#include "AwakeBuff.generated.h"

/**
 * Buff常用的函数
 */
UCLASS()
class THEAWAKENER_FO_API UAwakeBuff : public UObject
{

	GENERATED_BODY()
	
	//造成伤害并产生一定百分比吸血
	UFUNCTION()
		static FBuffDamageResult BloodlyThirsty(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//检查觉醒buff cost
	UFUNCTION(meta = (pc="Owning Player Controller"))
		static FBuffRunResult CheckAwakeBuffCost( FBuffObj BuffObj, int32 Ticked, TArray<FString> Params);
	//激活当前持续性觉醒技能
	UFUNCTION()
		static FBuffRunResult ActiveAwakeSkill(FBuffObj BuffObj, int WasStack, TArray<FString> Params);
	UFUNCTION()
		static FBuffRunResult CloseAwakeSkill(FBuffObj BuffObj, bool IsDispelled, TArray<FString> Params);

};
