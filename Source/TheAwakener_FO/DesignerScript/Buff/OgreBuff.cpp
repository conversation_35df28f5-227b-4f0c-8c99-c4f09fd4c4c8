// Fill out your copyright notice in the Description page of Project Settings.


#include "OgreBuff.h"

#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GamePlay/Buff/BuffManager.h"

FBuffRunResult UOgreBuff::DeletePillarSceneItem(FBuffObj BuffObj, int32 WasStack, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	if(BuffObj.Stack == 0) return Res;
	FString LevelName = "";
	if (UGameplayFuncLib::GetAwGameState()->AllCharacters.Contains(BuffObj.Carrier))
		LevelName = *UGameplayFuncLib::GetAwGameState()->AllCharacters.Find(BuffObj.Carrier);
	//找到最近的AOE柱子
	float MinDis = 0;
	AAwSceneItem* ClosetPillar = nullptr;
	const FString PillarObjUid = BuffObj.Carrier->GetAIComponent()->Params.Contains("PullingPillarObj") ?
		BuffObj.Carrier->GetAIComponent()->Params["PullingPillarObj"] : FString();
	for (auto& CurSceneItem : UGameplayFuncLib::GetAwGameState()->SceneItemList)
	{
		if (CurSceneItem.Value)
		{
			if (CurSceneItem.Value->Id == "OgrePillar")//Tags.Contains("OgrePillar"))
			{
				if (PillarObjUid.IsEmpty())
				{
					float CurDis = FVector::Distance(BuffObj.Carrier->GetActorLocation(), CurSceneItem.Value->GetActorLocation());
					if(CurDis <= 250)
					{
						if (!ClosetPillar)
						{
							ClosetPillar = CurSceneItem.Value;
							MinDis = CurDis;
						}
						else
						{
							if (CurDis < MinDis)
							{
								ClosetPillar = CurSceneItem.Value;
								MinDis = CurDis;
							}
						}
					}
				}else if (PillarObjUid == FString::FromInt(CurSceneItem.Value->GetUniqueID()))
				{
					ClosetPillar = CurSceneItem.Value;
				}
				
			}
		}
	}
	//删除柱子
	if (ClosetPillar)
	{
		ClosetPillar->TimeToBeRemoved = 0;
		ClosetPillar->Kill(false);
	}
	//巨魔身上长出柱子
	TArray<int> PillarDurability;
	PillarDurability.Add(10);
	PillarDurability.Add(20);
	BuffObj.Carrier->RestoreChaPart("Pillar", PillarDurability);

	return Res;
}

FBuffRunResult UOgreBuff::DeleteAttachedPillarAOE(FBuffObj BuffObj, int32 WasStack, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	if(BuffObj.Carrier)
		BuffObj.Carrier->BreakChaPart("Pillar");
	return Res;
}

FBuffRunResult UOgreBuff::SetExcitedWhenHPLess(FBuffObj BuffObj, int32 Ticked, TArray<FString> Params)
{
	float CheckPercent = 0.3f;
	if(Params.Num())
		CheckPercent = FCString::Atof(*Params[0]);
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	const float CurHP = BuffObj.Carrier->CharacterObj.CurrentRes.HP;
	const float MaxHP = BuffObj.Carrier->CharacterObj.CurProperty.HP;
	const float HPPercent = CurHP / MaxHP;
	if (HPPercent <= CheckPercent)
	{
		BuffObj.Carrier->FightingWill.ModifyLevel(2, false);
	}
	return Res;
}

