// 


#include "WeaponBuff.h"

#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GamePlay/Buff/Buff.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "TheAwakener_FO/GamePlay/DamageVolume/DamageManager.h"

FBuffDamageResult UWeaponBuff::DamageUp_CounterAction(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
                                                      TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;

	if (DamInfo.DamageSourceType == EAttackSource::CounterAction &&
		BuffObj.Carrier && !BuffObj.Carrier->Dead(true))
	{
		const float Rate = 1 + BuffObj.Stack * 0.01;
		Res.DamageInfo.DamagePower.Physical *= Rate;
	}

	return Res;
}

FBuffDamageResult UWeaponBuff::DamageUp_JustAttackAction(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
	TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;

	if (DamInfo.DamageSourceType == EAttackSource::JustAttackAction &&
		BuffObj.Carrier && !BuffObj.Carrier->Dead(true))
	{
		const float Rate = 1 + BuffObj.Stack * 0.01;
		Res.DamageInfo.DamagePower.Physical *= Rate;
	}

	return Res;
}

FBuffDamageResult UWeaponBuff::AddHpOnHit(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
	TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;

	if ((DamInfo.DamageSourceType == EAttackSource::AttackAction ||
		DamInfo.DamageSourceType == EAttackSource::CounterAction ||
		DamInfo.DamageSourceType == EAttackSource::JustAttackAction ||
		DamInfo.DamageSourceType == EAttackSource::PowerAction ||
		DamInfo.DamageSourceType == EAttackSource::MaxPowerAction) &&
		BuffObj.Carrier && !BuffObj.Carrier->Dead(true))
	{
		const float HealthValue = BuffObj.Stack;
		AAwCharacter* Carrier = BuffObj.Carrier;
		
		FDamageInfo NewDamageInfo = FDamageInfo();

		NewDamageInfo.Attacker = Carrier;
		NewDamageInfo.DamageSourceType = EAttackSource::Buff;
		NewDamageInfo.Elemental = EChaElemental::Physical;
		NewDamageInfo.DamageType = EDamageType::DirectDamage;
		NewDamageInfo.IsHeal = true;
		NewDamageInfo.DamagePower = FDamageValue(HealthValue, 0);

		UDamageManager::AddDamage(Carrier, Carrier, NewDamageInfo);
	}

	return Res;
}

FBuffRunResult UWeaponBuff::AttackUpOnAir(FBuffObj BuffObj, int32 Ticked, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	Res.TimelineNode = nullptr;

	const float Dur = Params.Num()>0 ? FCString::Atof(*Params[0]) : 1;
	const int AtkAdd = Params.Num()>1? FCString::Atoi(*Params[1]) : 0;
	const int AtkMaxAdd = Params.Num()>2? FCString::Atoi(*Params[2]) : 0;

	const int ChangeTime = FMath::FloorToInt(Dur / 0.1);
	const int MaxStack = AtkMaxAdd / AtkAdd * ChangeTime + 1;
	
	if (!BuffObj.Carrier->Dead(true))
	{
		if (BuffObj.Carrier->OnGround())
		{
			const int ReduceStack = Res.BuffObj.Stack - 1;
			const int ReduceTime = ReduceStack / ChangeTime;
			if (ReduceTime > 0)
			{
				const FBuffModel BuffModel = UGameplayFuncLib::GetAwDataManager()->GetBuffModelById("Rogue_AttackPercentUp");
				if (BuffModel.ValidBuffModel())
					BuffObj.Carrier->AddBuff(
						FAddBuffInfo(BuffObj.Carrier, BuffObj.Carrier, BuffModel, -1*ReduceTime*AtkAdd*100, 1, false, true));
			}
			Res.BuffObj.Stack = 1;
		}
		else
		{
			if (Res.BuffObj.Stack < MaxStack)
			{
				Res.BuffObj.Stack += 1;
				if ((Res.BuffObj.Stack-1) % ChangeTime == 0)
				{
					const FBuffModel BuffModel = UGameplayFuncLib::GetAwDataManager()->GetBuffModelById("Rogue_AttackPercentUp");
					if (BuffModel.ValidBuffModel())
						BuffObj.Carrier->AddBuff(
							FAddBuffInfo(BuffObj.Carrier, BuffObj.Carrier, BuffModel, AtkAdd*100, 1, false, true));
				}
			}
		}
	}
	
	return  Res;
}

FBuffDamageResult UWeaponBuff::DamageUp_PowerAction(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
	TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;

	if ((DamInfo.DamageSourceType == EAttackSource::PowerAction ||
		DamInfo.DamageSourceType == EAttackSource::MaxPowerAction) &&
		BuffObj.Carrier && !BuffObj.Carrier->Dead(true))
	{
		const float Rate = 1 + BuffObj.Stack * 0.01;
		Res.DamageInfo.DamagePower.Physical *= Rate;
	}

	return Res;
}

FBuffDamageResult UWeaponBuff::DamageDown_PowerAction(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
	TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;

	if ((DamInfo.DamageSourceType == EAttackSource::PowerAction ||
		DamInfo.DamageSourceType == EAttackSource::MaxPowerAction) &&
		BuffObj.Carrier && !BuffObj.Carrier->Dead(true))
	{
		const float Rate = 1 - BuffObj.Stack * 0.01;
		Res.DamageInfo.DamagePower.Physical *= Rate;
	}

	return Res;
}

