// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/GamePlay/Buff/Buff.h"
#include "UObject/Object.h"
#include "DungeonBuff.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UDungeonBuff : public UObject
{
	GENERATED_BODY()

public:
	//【OnKill】有几率出球，概率被Dungeon_HealthOrbRate影响
	UFUNCTION()
	static FBuffDamageResult DungeonChanceToCreateHealthOrb(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【BeHurt】格挡反弹伤害
	UFUNCTION()
	static FBuffDamageResult ThronsReflect(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【BeHurt】闪避受伤下降
	UFUNCTION()
	static FBuffDamageResult MasterDodge(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【OnHit】总伤害达到阈值产生血球
	UFUNCTION()
	static FBuffDamageResult BloodSeeker(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【OnHit】连续攻击伤害提高
	UFUNCTION()
	static FBuffDamageResult OffensiveTide(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【BeHurt】根据挨打的时候降低一定比例伤害，记录为下来反击用
	UFUNCTION()
	static FBuffDamageResult TurnbackStrength_Absorb(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);
	
	//【OnHit】根据Dungeon_TurnbackStrength层数和参数来进行伤害
	UFUNCTION()
	static FBuffDamageResult TurnbackStrength(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【OnHit】有几率取消原本应该的弹刀
	UFUNCTION()
	static FBuffDamageResult SteadyAsMountain_NotBounced(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【BeHitByAction】有几率取消原本应该的平衡崩坏（TODO：新模式下已经无法实现，待删除）
	//UFUNCTION()
	//static FBuffActionHitResult SteadyAsMountain_NotBreakAction(FBuffObj Buff, FActionHitResult ActionHitInfo, TArray<FString> Params);

	//【BeKilled】免死金牌
	UFUNCTION()
	static FBuffDamageResult FirmSprite(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【OnHit】伤害达到阈值获得一次Standard_IgnoreDamageOnce
	UFUNCTION()
	static FBuffDamageResult DeathProofHitRecord(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【OnHit】命中多个敌人造成额外伤害
	UFUNCTION()
	static FBuffDamageResult SweepAllBeforeMe(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【OnHit】每第n次命中敌人，无视2点平衡值
	UFUNCTION()
	static FBuffDamageResult CarryAll(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【OnHit】下次伤害造成n倍，并且无视n点平衡（前提是Param.Ready==true)
	UFUNCTION()
	static FBuffDamageResult SepratesHeaven_HitEffect(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);
	//【OnHit】下次伤害造成n倍，并且无视n点平衡（前提是Param.Ready==true) TODO：新模式下无法实现，待删除
	//UFUNCTION()
	//static FBuffActionHitResult SepratesHeaven_ActionHitEffect(FBuffObj BuffObj, FActionHitResult HitInfo, TArray<FString> Params);

	//【OnKill】检查房间是否还有人，如果没人，Param.Ready=true
	UFUNCTION()
	static FBuffDamageResult SepratesHeaven_FoeChecker(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【OnHit】对满血敌人造成n倍伤害，命中头部为n+2倍
	UFUNCTION()
	static FBuffDamageResult HeadOnBlow(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【OnHit】对30%以下的敌人造成n倍伤害
	UFUNCTION()
	static FBuffDamageResult MaltreatInjury(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【OnKill】杀死敌人获得加速（Standard_MoveSpeedUp和Standard_ActionSpeedUp）
	UFUNCTION()
	static FBuffDamageResult LikeTheWind(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【OnKill】击杀额外获得金币，每第n次额外获得更多金币
	UFUNCTION()
	static FBuffDamageResult BattleFieldCleaner(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【OnKill】获得额外的英勇作战经验（这个这里不能实现），根据Dungeon_Courage层数回血
	UFUNCTION()
	static FBuffDamageResult HighMorale(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【OnKill】自身满血受伤减半，并且反噬攻击者
	UFUNCTION()
	static FBuffDamageResult ThornsGuardian(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);
};
