// Fill out your copyright notice in the Description page of Project Settings.


#include "BuffUtils.h"

#include "Components/SkeletalMeshComponent.h"
#include "Kismet/KismetMathLibrary.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GamePlay/Battle/OffenseManager.h"
#include "TheAwakener_FO/GamePlay/Buff/BuffManager.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "TheAwakener_FO/GamePlay/Roguelike/Item/RogueInterface.h"

FBuffRunResult UBuffUtils::RemoveBuffObj(FBuffObj BuffObj, FActionInfo WasAction, FActionInfo CurrentAction, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	Res.TimelineNode = nullptr;
	if (Params.Num() <= 0) return Res;
	const FString BuffId = Params[0];
	BuffObj.Carrier->RemoveBuffById(BuffId);
	UKismetSystemLibrary::PrintString(BuffObj.Carrier, FString("Buff On Change Action Run ").Append(BuffId));
	return Res;
}

FBuffRunResult UBuffUtils::AddStackSelf(FBuffObj BuffObj, int32 Ticked, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult(BuffObj, nullptr);
	// UKismetSystemLibrary::PrintString(BuffObj.Carrier, " -  "+ BuffObj.Model.Id + FString::FromInt(BuffObj.Stack));
	const int Stack = Params.Num() >= 1 ? FCString::Atoi(*Params[0]) : 0;
	if (BuffObj.Carrier && Stack > 0)
	{
		Res.BuffObj.Stack += 1;
		Res.BuffObj.Stack = FMath::Min(Res.BuffObj.Stack, Res.BuffObj.Model.MaxStack);
	}
	return Res;
}

FBuffRunResult UBuffUtils::SelfAddBuff(FBuffObj BuffObj, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	Res.TimelineNode = nullptr;
	
	const FString BuffId = Params.Num() > 0?Params[0]:"";
	const int AddStack = Params.Num() > 1?FCString::Atoi(*Params[1]):0;
	const float AddTime =Params.Num() > 2? FCString::Atof(*Params[2]):0.f;
	const bool TimeInfinity = Params.Num() > 3 ? Params[3].ToBool() : false;
	const bool SetToDuration = Params.Num() > 4 ? Params[4].ToBool() : false;
	const FBuffModel BuffModel = UGameplayFuncLib::GetAwDataManager()->GetBuffModelById(BuffId);
	if (BuffModel.ValidBuffModel()&&IsValid(BuffObj.Carrier))
		BuffObj.Carrier->AddBuff(FAddBuffInfo(BuffObj.Carrier, BuffObj.Carrier, BuffModel, AddStack, AddTime, SetToDuration, TimeInfinity));
	return Res;
}

FBuffRunResult UBuffUtils::AddSubBuffObj(FBuffObj BuffObj, int WasStack, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	Res.TimelineNode = nullptr;
	//防止已经最大层数 刷新时间时 Occur事件依然添加Buff
	if (WasStack<BuffObj.Model.MaxStack)
	{
		Res = SelfAddBuff(BuffObj,Params);
	}
	return Res;
}

FBuffRunResult UBuffUtils::RemoveSubBuffObjStack(FBuffObj BuffObj, bool IsDispelled, TArray<FString> Params)
{
	int AddStack = 0;
	if (Params.Num()>1)
	{
		AddStack = -1 * FMath::Abs(FCString::Atoi(*Params[1])*BuffObj.Stack);
		Params[1] = FString::FromInt(AddStack);
	}
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	Res.TimelineNode = nullptr;
	
	const FString BuffId = Params.Num() > 0?Params[0]:"";

	const FBuffModel BuffModel = UGameplayFuncLib::GetAwDataManager()->GetBuffModelById(BuffId);
	if (BuffModel.ValidBuffModel()&&IsValid(BuffObj.Carrier))
	{
		FBuffObj* pRes = BuffObj.Carrier->AddBuff(FAddBuffInfo(BuffObj.Carrier, BuffObj.Carrier, BuffModel, AddStack,0));
		if (pRes)
		{
			Res.BuffObj = *pRes;
		}
		else
		{
			Res.BuffObj.ToBeRemoved = true;
		}
	}
	return Res;
}

FBuffRunResult UBuffUtils::InitBreakBuff(FBuffObj BuffObj, int WasStack, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	Res.TimelineNode = nullptr;
	if(WasStack == 0)
	{
		float BreakScale = 1;
		if(Params.Num() > 0)
			BreakScale = FCString::Atof(*Params[0]);
		Res.BuffObj.Stack = BuffObj.Carrier->CharacterObj.CurProperty.HP * BreakScale;
		Res.BuffObj.Param.Add("InitialStack", FString::FromInt(Res.BuffObj.Stack));
		UKismetSystemLibrary::PrintString(BuffObj.Carrier, FString("Buff InitalStack : ").Append(FString::FromInt(Res.BuffObj.Stack)));
	}
	return Res;
}

FBuffRunResult UBuffUtils::RemoveSubBuffObj(FBuffObj BuffObj, int WasStack, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	Res.TimelineNode = nullptr;
	if (Params.Num() <= 0) return Res;
	const FString BuffId = Params[0];
	BuffObj.Carrier->RemoveBuffById(BuffId);
	return Res;
}

FBuffRunResult UBuffUtils::AddBuffObj(FBuffObj BuffObj, int WasStack, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	Res.TimelineNode = nullptr;
	
	Res = SelfAddBuff(BuffObj,Params);
	
	return Res;
}

FBuffRunResult UBuffUtils::DamageOverTime(FBuffObj BuffObj, int32 Ticked, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	Res.TimelineNode = nullptr;
	if (Params.Num() < 2) return Res;

	int StartEffect = Params.Num() >= 2 ? FCString::Atoi(*Params[2]) : 0;
	UKismetSystemLibrary::PrintString(BuffObj.Carrier, FString("Will Be Damage After ").Append(FString::FromInt(StartEffect - Ticked)).Append(FString(" Ticks")));
	if (Ticked < StartEffect) return Res;

	const FString DamageTypeKey = Params[0];
	const int DamageValue = FCString::Atoi(*Params[1]);
	
	FOffenseInfo Offense = FOffenseInfo();
	Offense.AttackInfo = FAttackInfo();
	Offense.AttackInfo.DamagePower = DamageValue*BuffObj.Stack;
	Offense.AttackInfo.IsHeal = false;
	Offense.AttackInfo.DamageSourceType = EAttackSource::Buff;
	Offense.AttackInfo.DamageType = EDamageType::PeriodDamage;
	
	EChaElemental ElementalType = UDataFuncLib::FStringToEnum<EChaElemental>(DamageTypeKey);
	Offense.AttackInfo.Elemental = ElementalType>EChaElemental::Physical?ElementalType:EChaElemental::Physical;
	
	Offense.SourceId = BuffObj.Model.Id;
	Offense.CanHitTimes = 1;
	Offense.Index = BuffObj.Duration;
	UOffenseManager::DoBuffOffense(Offense, BuffObj.Carrier, BuffObj.Caster);
	//BuffObj.Caster->BeOffended(Offense, BuffObj.Caster, BuffObj.Carrier->MostPriorityHitBox());
	
	return Res;
}

FBuffRunResult UBuffUtils::CreateAOEOnRemove(FBuffObj BuffObj, bool IsDispelled, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	Res.TimelineNode = nullptr;
	if(Params.Num() > 1)
		UGameplayFuncLib::CreateAOE(BuffObj.Caster,Params[0],BuffObj.Carrier->GetActorLocation(),
		BuffObj.Carrier->GetActorForwardVector(),FCString::Atof(*Params[1]),"");
	
	return Res;
}

FBuffDamageResult UBuffUtils::AddBuffToBuffCaster(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;
	if(BuffObj.Caster && Params.Num() > 0)
	{
		FBuffModel AddBuffModel = UGameplayFuncLib::GetDataManager()->GetBuffModelById(Params[0]);
		const int AddBuffStack = Params.Num() > 1 ? FCString::Atoi(*Params[1]) : 1;
		const float AddBuffDuration = Params.Num() > 2 ? FCString::Atof(*Params[2]) : 5.0f;
		const bool AddBuffSetToDuration = Params.Num() > 3 ? FCString::ToBool(*Params[3]) : false;
		const bool AddBuffInfinity = Params.Num() > 4 ? FCString::ToBool(*Params[4]) : false;
		FAddBuffInfo AddBuffInfo = FAddBuffInfo(BuffObj.Caster, BuffObj.Caster,AddBuffModel, AddBuffStack,
			AddBuffDuration,AddBuffSetToDuration,AddBuffInfinity);
		BuffObj.Caster->AddBuff(AddBuffInfo);
	}
	return Res;
}

FBuffDamageResult UBuffUtils::ShieldProtect(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params)
{
	const FString ShieldKey = "HP";
	const int ShieldHP = BuffObj.Param.Contains(ShieldKey) ? FCString::Atoi(*BuffObj.Param[ShieldKey]) : 0;
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;	//TODO 这个不该是空了，到时候处理一下受击特效作为参数就好，有空的时候搞定他
	if (ShieldHP <= 0)
	{
		Res.BuffObj.Param[ShieldKey] = FString::FromInt(0);
		Res.BuffObj.ToBeRemoved = true;
		return Res;
	}
	if (ShieldHP > DamInfo.FinalDamage())
	{
		BuffObj.Param[ShieldKey] = FString::FromInt(ShieldHP - DamInfo.FinalDamage());
		Res.DamageInfo.DamagePower.SetDamageZero();
	}else
	{
		Res.BuffObj.Param[ShieldKey] = FString::FromInt(0);
		//TODO DamageInfo被一个盾减少，这个盾减少的规则待定，本周搞掉他
		Res.BuffObj.Param[ShieldKey] = FString::FromInt(0);
		Res.BuffObj.ToBeRemoved = true;
	}
	return Res;
}

FBuffDamageResult UBuffUtils::SelfAddBuff_InDamage(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params)
{
	FBuffRunResult BR = UBuffUtils::SelfAddBuff(BuffObj, Params);
	return FBuffDamageResult(BuffObj, DamInfo, BR.TimelineNode);
};

FBuffDamageResult UBuffUtils::DamageTimesUp(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;
	if (DamInfo.IsHeal)
	{
		return Res;
	}
	if (Params.Num() <= 0) return Res;
	const float Times = FCString::Atof(*Params[0]) * BuffObj.Stack;
	if (Res.DamageInfo.ValuePowerArea.Contains(EDamageArea::CauseDamagePower))
	{
		Res.DamageInfo.ValuePowerArea[EDamageArea::CauseDamagePower] += Times;
		Res.DamageInfo.CheckMinDamagePowerArea();
	}
	return Res;
}

FBuffDamageResult UBuffUtils::DamageTimesDown(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
	TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;
	if (DamInfo.IsHeal)
	{
		return Res;
	}
	if (Params.Num() <= 0) return Res;
	 float Times = FCString::Atof(*Params[0]) * BuffObj.Stack ;
	if (Res.DamageInfo.ValuePowerArea.Contains(EDamageArea::CauseDamagePower))
	{
		Res.DamageInfo.ValuePowerArea[EDamageArea::CauseDamagePower] -= Times;
		Res.DamageInfo.CheckMinDamagePowerArea();
	}
	return Res;
}

FBuffDamageResult UBuffUtils::DamageTimesUpInSky(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
                                                 TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;
	if (DamInfo.IsHeal)
	{
		return Res;
	}
	if (Params.Num() <= 0) return Res;
	if (BuffObj.Carrier && !BuffObj.Carrier->OnGround())
	{
		const float Times = FCString::Atof(*Params[0]) * BuffObj.Stack ;	
		if (Res.DamageInfo.ValuePowerArea.Contains(EDamageArea::CauseDamagePower))
		{
			Res.DamageInfo.ValuePowerArea[EDamageArea::CauseDamagePower] += Times;
			Res.DamageInfo.CheckMinDamagePowerArea();
		}
		return Res;
	}
	return  Res;
}

FBuffDamageResult UBuffUtils::DamageTimesUpOnGround(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
	TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;
	if (DamInfo.IsHeal)
	{
		return Res;
	}
	if (Params.Num() <= 0) return Res;
	if (BuffObj.Carrier && BuffObj.Carrier->OnGround())
	{
		const float Times = FCString::Atof(*Params[0]) * BuffObj.Stack ;	
		if (Res.DamageInfo.ValuePowerArea.Contains(EDamageArea::CauseDamagePower))
		{
			Res.DamageInfo.ValuePowerArea[EDamageArea::CauseDamagePower] += Times;
			Res.DamageInfo.CheckMinDamagePowerArea();
		}
		return Res;
	}
	return  Res;
}

FBuffDamageResult UBuffUtils::PhysicalDamageTimesUpInSky(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
	TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;
	if (DamInfo.IsHeal)
	{
		return Res;
	}
	if (Params.Num() <= 0) return Res;
	if (BuffObj.Carrier && !BuffObj.Carrier->OnGround()&&DamInfo.Elemental==EChaElemental::Physical )
	{
		const float Times = FCString::Atof(*Params[0]) * BuffObj.Stack ;	
		if (Res.DamageInfo.ValuePowerArea.Contains(EDamageArea::PhysicalDamagePower))
		{
			Res.DamageInfo.ValuePowerArea[EDamageArea::PhysicalDamagePower] += Times;
			Res.DamageInfo.CheckMinDamagePowerArea();
		}
		return Res;
	}
	return  Res;
}

FBuffDamageResult UBuffUtils::PhysicalDamageTimesUpOnGround(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
	TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;
	if (DamInfo.IsHeal)
	{
		return Res;
	}
	if (Params.Num() <= 0) return Res;
	if (BuffObj.Carrier && BuffObj.Carrier->OnGround()&&DamInfo.Elemental==EChaElemental::Physical )
	{
		const float Times = FCString::Atof(*Params[0]) * BuffObj.Stack ;	
		if (Res.DamageInfo.ValuePowerArea.Contains(EDamageArea::PhysicalDamagePower))
		{
			Res.DamageInfo.ValuePowerArea[EDamageArea::PhysicalDamagePower] += Times;
			Res.DamageInfo.CheckMinDamagePowerArea();
		}
		return Res;
	}
	return  Res;
}

FBuffDamageResult UBuffUtils::RemoveSelfBuffStackOnHit(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
                                                       TArray<FString> Params)
{
	const FString BuffId = Params[0];
	const int RemoveStack = FCString::Atoi(*Params[1]);
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;
	Res.BuffObj = RemoveSubBuffObjStack(Res.BuffObj,false,Params).BuffObj;
	return Res;
}

FBuffDamageResult UBuffUtils::AddBuffToHurtSourceByChance(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;

	if (DamInfo.DamageType!=EDamageType::DirectDamage
		||DamInfo.IsHeal
		|| !Target)
	{
		return Res;
	}
	float Chance =Params.Num()>0?FCString::Atof(*Params[0]):0;
	Chance *=BuffObj.Stack;
	FString BuffId =  Params.Num()>1?Params[1]:"";
	int BuffStack = Params.Num()>2?FCString::Atof(*Params[2]) :1;
	float BuffTime = Params.Num()>3?FCString::Atof(*Params[3]):0;	
	bool SetToDurantion = Params.Num()>4?Params[4].Equals("true",ESearchCase::IgnoreCase):false; 
	bool bInfinity = Params.Num()>5?Params[5].Equals("true",ESearchCase::IgnoreCase):false;


	if(FMath::RandRange(0.f,1.f)>Chance)
	{
		return  Res;
	}
	
	FBuffModel BuffModel = UGameplayFuncLib::GetAwDataManager()->GetBuffModelById(BuffId);
	
	Target->AddBuff(FAddBuffInfo(Res.BuffObj.Carrier, Target, BuffModel, BuffStack, BuffTime, SetToDurantion ,bInfinity ));
	return Res;
}

FBuffDamageResult UBuffUtils::AddBuffToHitTarget(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
	TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;

	if ((DamInfo.DamageType!=EDamageType::DirectDamage&&DamInfo.DamageType!=EDamageType::SpecDamage)|| DamInfo.IsHeal
		|| DamInfo.BeDodged
		|| !Target
		)
	{
		return Res;
	}
	if(Target->GetHitBoxInJustDefense(DamInfo.HitBox->GetName()))
		return Res;
	FString BuffId =  Params.Num()>0?Params[0]:"";
	int BuffStack = Params.Num()>1?FCString::Atof(*Params[1]) :1;
	float BuffTime = Params.Num()>2?FCString::Atof(*Params[2]):0;	
	bool SetToDurantion = Params.Num()>3?Params[3].Equals("true",ESearchCase::IgnoreCase):false; 
	bool bInfinity = Params.Num()>4?Params[4].Equals("true",ESearchCase::IgnoreCase):false;
	
	FBuffModel BuffModel = UGameplayFuncLib::GetAwDataManager()->GetBuffModelById(BuffId);
	
	Target->AddBuff(FAddBuffInfo(Res.BuffObj.Carrier, Target, BuffModel, BuffStack*BuffObj.Stack, BuffTime, SetToDurantion ,bInfinity ));
	return Res;
}

FBuffDamageResult UBuffUtils::AddBuffToHitTargetByChance(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
                                                         TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;

	if ((DamInfo.DamageType!=EDamageType::DirectDamage&&DamInfo.DamageType!=EDamageType::SpecDamage)|| DamInfo.IsHeal
		|| DamInfo.BeDodged
		|| !Target
		)
	{
		return Res;
	}
	if(Target->GetHitBoxInJustDefense(DamInfo.HitBox->GetName()))
		return Res;
	float Chance =Params.Num()>0?FCString::Atof(*Params[0]):0;
	Chance *=BuffObj.Stack;
	FString BuffId =  Params.Num()>1?Params[1]:"";
	int BuffStack = Params.Num()>2?FCString::Atof(*Params[2]) :1;
	float BuffTime = Params.Num()>3?FCString::Atof(*Params[3]):0;	
	bool SetToDurantion = Params.Num()>4?Params[4].Equals("true",ESearchCase::IgnoreCase):false; 
	bool bInfinity = Params.Num()>5?Params[5].Equals("true",ESearchCase::IgnoreCase):false;


	if(FMath::RandRange(0.f,1.f)>Chance)
	{
		return  Res;
	}
	
	FBuffModel BuffModel = UGameplayFuncLib::GetAwDataManager()->GetBuffModelById(BuffId);
	
	Target->AddBuff(FAddBuffInfo(Res.BuffObj.Carrier, Target, BuffModel, BuffStack, BuffTime, SetToDurantion ,bInfinity ));
	return Res;
}

FBuffDamageResult UBuffUtils::CreateAoeOnHitPos(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;
	if (!Res.BuffObj.IsBuffVaild())
	{
		return  Res;
	}
	if (DamInfo.DamageType!=EDamageType::DirectDamage||DamInfo.IsHeal)
	{
		return Res;
	}
	FString AoeId = Params.Num()>0?Params[0]:"";
	float LifeTime = Params.Num()>1?FCString::Atof(*Params[1]):0;
	FString TweenFuncName = Params.Num()>2?Params[2]:"";
	FVector Pos =  DamInfo.HitBox->GetComponentLocation()+DamInfo.HitLocationOffset;
	UGameplayFuncLib::CreateAOE(BuffObj.Carrier,AoeId,Pos,FVector::ZeroVector,LifeTime,TweenFuncName);
	return  Res;
}

FBuffDamageResult UBuffUtils::DoExtraOffense(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
	TArray<FString> Params)
{
	//原本伤害信息
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	
	if (DamInfo.DamageType!=EDamageType::DirectDamage||DamInfo.IsHeal)
	{
		return  Res;
	}
	//产生一次额外伤害
	//伤害数值类型
	float ExtraRate =  Params.Num()>0?FCString::Atof(*Params[0]):0  ;
	float ExtraNum =  Params.Num()>1?FCString::Atoi(*Params[1]):0  ;
	const FString DamageType= Params.Num()>2?Params[2]:"" ;
	const FString DamageElement = Params.Num()>3?Params[3]:"" ;

	if (ExtraNum<=0)
	{
		return  Res;
	}
	
	FOffenseInfo Offense = FOffenseInfo();
	Offense.AttackInfo = FAttackInfo();
	Offense.AttackInfo.DamagePower = ExtraRate* BuffObj.Carrier->CharacterObj.CurProperty.PAttack;
	Offense.AttackInfo.IsHeal = false;
	Offense.AttackInfo.DamageSourceType = EAttackSource::Buff;

	EDamageType DamageTypeEnum = UDataFuncLib::FStringToEnum<EDamageType>(DamageType);
	Offense.AttackInfo.DamageType = DamageTypeEnum;
	
	EChaElemental ElementalType = UDataFuncLib::FStringToEnum<EChaElemental>(DamageElement);
	Offense.AttackInfo.Elemental = ElementalType>EChaElemental::Physical?ElementalType:EChaElemental::Physical;
	Offense.SourceId = BuffObj.Model.Id;
	Offense.Index = BuffObj.Duration;

	for (int i=0;i<ExtraNum;i++)
	{
		Offense.OffensePosOffset = UKismetMathLibrary::RandomUnitVector()*20;
		UOffenseManager::DoBuffOffense(Offense,Target, BuffObj.Carrier);
	}
	return  Res;
}

FBuffDamageResult UBuffUtils::TakeExtraDamage(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
                                              TArray<FString> Params)
{
	//原本伤害信息
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	
	if (DamInfo.DamageType!=EDamageType::DirectDamage)
	{
		return  Res;
	}
	//产生一次额外伤害
	//伤害数值类型
	const FString DamageTypeKey = Params.Num()>0?Params[0]:"" ;
	float Extra=  Params.Num()>1?FCString::Atof(*Params[1]):0  ;
	

	FOffenseInfo Offense = FOffenseInfo();
	Offense.AttackInfo = FAttackInfo();
	Offense.AttackInfo.DamagePower = Extra* BuffObj.Stack;
	Offense.AttackInfo.IsHeal = false;
	Offense.AttackInfo.DamageSourceType = EAttackSource::Buff;
	Offense.AttackInfo.DamageType = EDamageType::ExtraDamage;
	
	EChaElemental ElementalType = UDataFuncLib::FStringToEnum<EChaElemental>(DamageTypeKey);
	Offense.AttackInfo.Elemental = ElementalType>EChaElemental::Physical?ElementalType:EChaElemental::Physical;
	
	Offense.SourceId = BuffObj.Model.Id;
	Offense.Index = BuffObj.Duration;
	UOffenseManager::DoBuffOffense(Offense,BuffObj.Carrier, BuffObj.Caster);
	return  Res;
}


FBuffDamageResult UBuffUtils::TakeDamage999999(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.DamageInfo.DamagePower.Physical = 999999;
	Res.TimelineNode = nullptr;
	return Res;
}

FBuffRunResult UBuffUtils::SelfAura(FBuffObj BuffObj, int WasStack, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	//TODO 琢磨一下怎么搞
	return Res;
}

FBuffRunResult UBuffUtils::PlayFrozen(FBuffObj BuffObj, int WasStack, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	BuffObj.Carrier->GetMesh()->bPauseAnims = true;
	return Res;
}

FBuffRunResult UBuffUtils::StopFrozen(FBuffObj BuffObj,bool IsDispelled, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	BuffObj.Carrier->GetMesh()->bPauseAnims = false;

	return Res;
}

FBuffDamageResult UBuffUtils::HurtDamageUp(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
                                           TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;
	if (DamInfo.IsHeal)
	{
		return  Res;
	}
	if (Params.Num() <= 0) return Res;
	const float Times = FCString::Atof(*Params[0]) * BuffObj.Stack ;	
	if (Res.DamageInfo.ValuePowerArea.Contains(EDamageArea::InjuredDamagePower))
	{
		Res.DamageInfo.ValuePowerArea[EDamageArea::InjuredDamagePower] += Times;
		Res.DamageInfo.CheckMinDamagePowerArea();
	}
	
	return Res;
}

FBuffDamageResult UBuffUtils::IgnoreDamage(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;
	Res.DamageInfo.DamagePower.SetDamageZero();
	//Res.DamageInfo.AttackInfo.BreakPower = -1;	//这样就无法击破动作了
	return Res;
}

FBuffDamageResult UBuffUtils::IgnoreDamageWhenStackGreater(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
	TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;
	if(Params.Num() > 0)
	{
		int CheckStack = FCString::Atoi(*Params[0]);
		if(BuffObj.Stack >= CheckStack)
			Res.DamageInfo.DamagePower.SetDamageZero();
	}
	//Res.DamageInfo.AttackInfo.BreakPower = -1;	//这样就无法击破动作了
	return Res;
}

FBuffDamageResult UBuffUtils::IgnoreDamageWhenHpLess(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
                                                     TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;
	if(!DamInfo.IsHeal && Params.Num() > 0)
	{
		const float CheckHpPercent = FCString::Atof(*Params[0]);
		const float CurHP = BuffObj.Carrier->CharacterObj.CurrentRes.HP;
		const float MaxHP = BuffObj.Carrier->CharacterObj.CurProperty.HP;
		if((CurHP - DamInfo.FinalDamage()) / MaxHP <= CheckHpPercent)
		{
			Res.DamageInfo.DamagePower.SetDamageZero();
			BuffObj.Carrier->CharacterObj.CurrentRes.HP =  CheckHpPercent*MaxHP;
			BuffObj.Carrier->AttrRecheck();
		}
	}
	//Res.DamageInfo.AttackInfo.BreakPower = -1;	//这样就无法击破动作了
	return Res;
}

FBuffDamageResult UBuffUtils::IgnoreDeathDamage(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
                                                TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;
	if(!DamInfo.IsHeal)
	{
		if(BuffObj.Carrier->CharacterObj.CurrentRes.HP - DamInfo.FinalDamage() <= 1)
		{
			Res.DamageInfo.DamagePower.SetDamageZero();
		}
	}
	//Res.DamageInfo.AttackInfo.BreakPower = -1;	//这样就无法击破动作了
	return Res;
}

FBuffDamageResult UBuffUtils::TakeNoDamageAndPrint(FBuffObj Buff, FDamageInfo Dam, AAwCharacter* Target, TArray<FString> Params)
{
	UKismetSystemLibrary::PrintString(Buff.Carrier, FString("Damage Taking (And Ignored):").Append(FString::FromInt(Dam.FinalDamage()))
		,true, true, FLinearColor::Yellow, 30);
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = Buff;
	Res.DamageInfo = Dam;
	Res.DamageInfo.DamagePower.SetDamageZero();
	Res.TimelineNode = nullptr;
	//Res.DamageInfo.AttackInfo.BreakPower = -1;	//这样就无法击破动作了
	return Res;
}

FBuffDamageResult UBuffUtils::ModifyFightWillWhenNormalStage(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	if(BuffObj.Carrier->FightingWill.Level == 1)
	{
		int ChangeValue = FMath::Clamp(DamInfo.FinalDamage(), 5, 1000);
		if(Params.Num())
			ChangeValue = ChangeValue * FCString::Atof(*Params[0]);
		BuffObj.Carrier->FightingWill.ModifyValue(ChangeValue * -1 ,1, true);
	}
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	return Res;
}

FBuffDamageResult UBuffUtils::AccumulateDamageAndHurt(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
	TArray<FString> Params)
{
	AAwCharacter* Character = BuffObj.Carrier;
	if(!Character->Dead(true) && DamInfo.FinalDamage() > 0 &&
		Character->CharacterObj.CurrentRes.HP >= DamInfo.FinalDamage())
	{
		const int MaxDamage = Params.Num()>0 ? FCString::Atoi(*Params[0]) : 0;
		const FString ActionId = Params.Num()>1 ? Params[1] : "";
		
		bool ShouldDoAction = false;
		
		BuffObj.Stack += DamInfo.FinalDamage();
		if (BuffObj.Stack >= MaxDamage)
		{
			BuffObj.Stack = 0;
			ShouldDoAction = true;
		}

		// UKismetSystemLibrary::PrintString(Character, "----------", true, true, FLinearColor::Red);
		// UKismetSystemLibrary::PrintString(Character, "AccumulateDamage: " + FString::FromInt(BuffObj.Stack), true, true, FLinearColor::Red);

		if (ShouldDoAction)
			Character->PreorderAction(ActionId);
	}
	
	return FBuffDamageResult(BuffObj, DamInfo, nullptr);
}

FBuffDamageResult UBuffUtils::AccumulateBreakDamageAndHurt(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
	TArray<FString> Params)
{
	AAwCharacter* Character = BuffObj.Carrier;
	if(!Character->Dead(true) && DamInfo.FinalDamage() > 0 &&
		Character->CharacterObj.CurrentRes.HP >= DamInfo.FinalDamage() &&
		DamInfo.DamageType != EDamageType::OtherDamage &&
		DamInfo.DamageType != EDamageType::PeriodDamage)
	{
		const FString ActionId = Params.Num()> 0 ? Params[0] : "";
		
		float BreakValue = DamInfo.FinalDamage() * (FMath::Clamp(DamInfo.AttackInfoPriority, 1, 10) * FMath::Clamp((10 -DamInfo.DefendInfoPriority), 1, 10));
		float ClassRatio = 0;
		if(DamInfo.Attacker->IsPlayerCharacter())
		{
			ClassRatio = UGameplayFuncLib::GetAwDataManager()->GetRolePawnByClassId(DamInfo.Attacker->CharacterObj.ClassId).BreakRatio;
		}
		if (DamInfo.ValuePowerArea.Contains(EDamageArea::BreakDamagePower))
		{
			ClassRatio*= DamInfo.ValuePowerArea[EDamageArea::BreakDamagePower];
		}
		BreakValue = BreakValue * ClassRatio;
		
		if(DamInfo.DamageType != EDamageType::DirectDamage)
			BreakValue = BreakValue / 2;

		float MaxBreak = FCString::Atof(*BuffObj.Param["InitialStack"]);
		BreakValue = FMath::Clamp(BreakValue, 0 ,( MaxBreak * 0.2 + 200));

		UKismetSystemLibrary::PrintString(Character, "Damage: " + FString::FromInt(DamInfo.FinalDamage()), true, true, FLinearColor::Red);
		UKismetSystemLibrary::PrintString(Character, "BreakValue: " + FString::FromInt(BreakValue), true, true, FLinearColor::Red);
		
		BuffObj.Stack -= BreakValue;
		if (BuffObj.Stack <= 0)
		{
			BuffObj.Stack = 0;
			Character->PreorderAction(ActionId);
			UBuffManager::RemoveBuff(BuffObj);
		}

		UKismetSystemLibrary::PrintString(Character, "AccumulateBreakDamage: " + FString::FromInt(BuffObj.Stack), true, true, FLinearColor::Red);
			
	}
	
	return FBuffDamageResult(BuffObj, DamInfo, nullptr);
}

FBuffDamageResult UBuffUtils::AddStackOnBeHurt(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
	TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	if(DamInfo.DamageType == EDamageType::DirectDamage)
		Res.BuffObj.Stack++;
	Res.DamageInfo = DamInfo;
	return Res;
}

FBuffDamageResult UBuffUtils::BreakDamageUp(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	float AddValue = Params.Num()>0?FCString::Atof(*Params[0]):0;
	if (DamInfo.ValuePowerArea.Contains(EDamageArea::BreakDamagePower))
	{
		DamInfo.ValuePowerArea[EDamageArea::BreakDamagePower]+= AddValue*BuffObj.Stack;
		DamInfo.CheckMinDamagePowerArea();
	}
	Res.DamageInfo = DamInfo;
	return  Res;
}

FBuffRunResult UBuffUtils::PrintBuffDuration(FBuffObj BuffObj, int32 Ticked, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	UKismetSystemLibrary::PrintString(BuffObj.Carrier, BuffObj.Model.Id.Append(FString::FromInt(BuffObj.Stack)).Append(FString(" Duration:")).Append(FString::SanitizeFloat(BuffObj.Duration)),
									true,true,FLinearColor::Red,20);
	return Res;
}

//【OnOccur】角色播放视觉特效 [0]赋予的句柄(string) [1]特效路径(string，必须是粒子) [2]所处绑点类型(EVFXBindPointType) [3]是否播放一次(bool) [4]开始播放的层数
FBuffRunResult UBuffUtils::PlayVFXOnCha_Occur(FBuffObj BuffObj, int WasStack, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult(BuffObj, nullptr);
	if (!BuffObj.Carrier) return Res;

	const FString VfxId = Params.Num() > 0 ? Params[0] : "";
	const FString VfxPath = Params.Num() > 1 ? Params[1] : "";
	const EVFXBindPointType BPType = Params.Num() > 2 ? UDataFuncLib::FStringToEnum<EVFXBindPointType>(Params[2]) : EVFXBindPointType::Head;
	const bool PlayOnce = Params.Num() > 3 ? Params[3].ToBool() : true;
	const int StartStack = Params.Num() > 4 ? FCString::Atoi(*Params[4]) : 0;

	if (VfxId.IsEmpty() || VfxPath.IsEmpty() || StartStack > BuffObj.Stack) return Res;

	BuffObj.Carrier->PlayAttachedVFX(VfxPath, VfxId, BPType, PlayOnce);

	return Res;
}

//【OnRemove】角色停止视觉特效 [0]句柄(fstring)
FBuffRunResult UBuffUtils::StopVFXOnCha_Remove(FBuffObj BuffObj, bool IsDispelled, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult(BuffObj, nullptr);
	if (!BuffObj.Carrier) return Res;

	const FString VfxId = Params.Num() > 0 ? Params[0] : "";
	BuffObj.Carrier->StopAttachedVFX(VfxId);
	
	return Res;
}

FBuffDamageResult UBuffUtils::StopVFXOnOnBeKilled_Remove(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
	TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.DamageInfo = DamInfo;
	Res.BuffObj = BuffObj;
	if (!BuffObj.Carrier) return Res;

	const FString VfxId = Params.Num() > 0 ? Params[0] : "";
	BuffObj.Carrier->StopAttachedVFX(VfxId);
	
	return Res;
}

FBuffRunResult UBuffUtils::SetCharacterScale_Occur(FBuffObj BuffObj, int WasStack, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult(BuffObj, nullptr);
	if (!BuffObj.Carrier) return Res;

	if(Params.Num() < 3) return Res;
	FVector TargetScale = FVector(FCString::Atof(*Params[0]),FCString::Atof(*Params[1]),FCString::Atof(*Params[2]));
	BuffObj.Carrier->SetActorScale3D(TargetScale);
	return Res;
}

FBuffRunResult UBuffUtils::SetCharacterScale_Remove(FBuffObj BuffObj, bool IsDispelled, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult(BuffObj, nullptr);
	if (!BuffObj.Carrier) return Res;

	if(Params.Num() < 3) return Res;
	FVector TargetScale = FVector(FCString::Atof(*Params[0]),FCString::Atof(*Params[1]),FCString::Atof(*Params[2]));
	BuffObj.Carrier->SetActorScale3D(TargetScale);
	return Res;
}

FBuffRunResult UBuffUtils::CreateAoeOnCarrierOnTick(FBuffObj BuffObj, int32 WasStack, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult(BuffObj, nullptr);
	if (!BuffObj.Carrier)
	{
		return  Res;
	}
	FString AoeId = Params.Num()>0?Params[0]:"";
	//生成周期 不需要可以为0
	float CreateTime =  Params.Num()>1?FCString::Atof(*Params[1]):0;
	//生命周期 
	float LifeSpan =  Params.Num()>2?FCString::Atof(*Params[2]):0;
	FString PosString =  Params.Num()>3?Params[3]:BuffObj.Carrier->GetActorLocation().ToString();
	FString RotationString =Params.Num()>4?Params[4]:FRotator::ZeroRotator.ToString();
	bool bAttach =Params.Num()>5?Params[5].Equals("True",ESearchCase::IgnoreCase):false;
	FString BindPoint  =Params.Num()>6?Params[6]:"";
    //NeedModfiy
	float   FloatTimes= BuffObj.Time/CreateTime;
	int       IntTimes= BuffObj.Time/CreateTime;
	
	if (!UKismetMathLibrary::NearlyEqual_FloatFloat(FloatTimes,IntTimes,BuffObj.Model.TickTime)&&CreateTime!=0.f)
	{
		return  Res;
	}
	
	FRotator Rotator  = FRotator::ZeroRotator;
	Rotator.InitFromString(RotationString);
	FVector Pos = FVector::ZeroVector;
	Pos.InitFromString(PosString);
	if (PosString.Equals("Root",ESearchCase::IgnoreCase))
	{
		Pos = FVector(0,0,-1*BuffObj.Carrier->GetCapsuleComponent()->GetScaledCapsuleHalfHeight());
	}
	
	 AAWAoe* Aoe =UGameplayFuncLib::CreateAOE(BuffObj.Carrier,AoeId,Pos,FVector::ZeroVector,LifeSpan,"");
	if (Rotator!=FRotator::ZeroRotator)
	{
		Aoe->SetActorRotation(Rotator);
	}
	if (bAttach)
	{
		Aoe->SetActorLocation(Pos);
		USceneComponent* AttachPoint = nullptr;
		if(BuffObj.Carrier->AllEquipmentBindPoints().Contains(BindPoint))
		{
			AttachPoint = *(BuffObj.Carrier->AllEquipmentBindPoints().Find(BindPoint));
		}
		if (!AttachPoint)
		{
			Aoe->AttachToActor(BuffObj.Carrier,FAttachmentTransformRules(FAttachmentTransformRules::KeepRelativeTransform));
		}
		else
		{
			Aoe->AttachToComponent(AttachPoint,FAttachmentTransformRules(FAttachmentTransformRules::KeepRelativeTransform));
		}
	}
	else
	{
		Aoe->SetActorLocation(Pos+BuffObj.Carrier->GetActorLocation());
	}
	return Res;
}

FBuffRunResult UBuffUtils::CreateAoeOnSocket(FBuffObj BuffObj, int32 WasStack, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult(BuffObj, nullptr);
	if (!BuffObj.Carrier)
	{
		return  Res;
	}
	auto Ability = BuffObj.Carrier->GetCurrentActionAbilityLevelInfo();
	int Level = Ability.AbilityInfoId.IsEmpty()?3:Ability.Level;
	FString AoeId = Params.Num()>0?Params[0]:"";
	float Durantion =  Params.Num()>1?FCString::Atof(*Params[1]):0;
	FString SocketName = Params.Num()>2?Params[2]:"";
	FString Tween = Params.Num()>3?Params[3]:"";
	bool Attach = Params.Num()>4?Params[4].ToBool():false;
	
	FVector Pos = FVector::ZeroVector; ;
	FRotator Rot = FRotator::ZeroRotator;
	FVector Direction = FVector::ZeroVector;
	FRotator NewRot = FRotator::ZeroRotator;
	bool bUseNewRot = false;
	

	bool bUseAnimTransform = Params.Contains("AnimParams");
	FRotator AnimRotator = FRotator::ZeroRotator;
	FVector AnimPos = FVector::ZeroVector;
	if (bUseAnimTransform)
	{
		int Index = Params.IndexOfByKey("AnimParams");
		if(Params.Num()>Index+1)
		{
			AnimRotator.InitFromString(Params[Index+1]);
			AnimRotator=UKismetMathLibrary::TransformRotation(BuffObj.Carrier->GetTransform(),AnimRotator);
		}
		if (Params.Num()>Index+2)
		{
			AnimPos.InitFromString(Params[Index+2]);
			AnimPos=UKismetMathLibrary::TransformLocation(BuffObj.Carrier->GetTransform(),AnimPos);
		}
	}
	//使用骨骼空间
	if (BuffObj.Carrier->GetMesh()&&BuffObj.Carrier->GetMesh()->DoesSocketExist(FName(SocketName)))
	{
		 Pos = BuffObj.Carrier->GetMesh()->GetSocketLocation(FName(SocketName));
		 Rot = BuffObj.Carrier->GetMesh()->GetSocketRotation(FName(SocketName));
		 FVector Forward = BuffObj.Carrier->GetActorForwardVector();
		 FVector Up =  UKismetMathLibrary::GetUpVector(Rot);
		 FVector Right =  UKismetMathLibrary::GetRightVector(Rot);
		NewRot = bUseAnimTransform? AnimRotator:UKismetMathLibrary::MakeRotationFromAxes(Forward,Right,Up);
		Direction = UKismetMathLibrary::GetForwardVector(NewRot);
		bUseNewRot= true;
	}
	else
	{
		//基础空间信息
		Pos = BuffObj.Carrier->GetActorLocation();
		Direction =bUseAnimTransform?UKismetMathLibrary::GetForwardVector(AnimRotator): BuffObj.Carrier->GetActorForwardVector();
	}
	Direction.Normalize();
	Pos = bUseAnimTransform?AnimPos:Pos;
	AAWAoe* Aoe =UGameplayFuncLib::CreateAOE(BuffObj.Carrier,AoeId,Pos,Direction,Durantion,Tween,Level);


	if (IsValid(Aoe)&&Aoe->Implements<URogueInterface>())
	{
		TMap<FString,FString>InterfaceParams ;
		InterfaceParams = BuffObj.Param;
		InterfaceParams.Add("EffectLevel",FString::FromInt(BuffObj.Stack));
		IRogueInterface::Execute_GiveInfoMap(Aoe,InterfaceParams);
	}
	
	if (bUseNewRot)
	{
		Aoe->SetActorRotation(NewRot);
	}
	if (Attach)
	{
		Aoe->AttachToActor(Res.BuffObj.Carrier,FAttachmentTransformRules::KeepWorldTransform);
	}
	return Res;
}

FBuffRunResult UBuffUtils::CreateAoeOnSocketByCoolDown(FBuffObj BuffObj, int32 WasStack, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult(BuffObj, nullptr);
	if (!BuffObj.Carrier)
	{
		return  Res;
	}
	float CoolDown =  Params.Num()>0?FCString::Atof(*Params[0]):0;
	FString AoeId = Params.Num()>1?Params[1]:"";
	float Durantion =  Params.Num()>2?FCString::Atof(*Params[2]):0;
	FString SocketName = Params.Num()>3?Params[3]:"";
	FString Tween = Params.Num()>4?Params[4]:"";
	bool Attach = Params.Num()>5?Params[5].ToBool():false;
	
	FVector Pos = FVector::ZeroVector; ;
	FRotator Rot = FRotator::ZeroRotator;
	FVector Direction = FVector::ZeroVector;
	FRotator NewRot = FRotator::ZeroRotator;
	bool bUseNewRot = false;

	if (Res.BuffObj.Time<CoolDown)
	{
		return  Res;
	}
	else
	{
		Res.BuffObj.Time = 0;
	}
	
bool bUseAnimTransform = Params.Contains("AnimParams");
	FRotator AnimRotator = FRotator::ZeroRotator;
	FVector AnimPos = FVector::ZeroVector;
	if (bUseAnimTransform)
	{
		int Index = Params.IndexOfByKey("AnimParams");
		if(Params.Num()>Index+1)
		{
			AnimRotator.InitFromString(Params[Index+1]);
			AnimRotator=UKismetMathLibrary::TransformRotation(BuffObj.Carrier->GetTransform(),AnimRotator);
		}
		if (Params.Num()>Index+2)
		{
			AnimPos.InitFromString(Params[Index+2]);
			AnimPos=UKismetMathLibrary::TransformLocation(BuffObj.Carrier->GetTransform(),AnimPos);
		}
	}
	//使用骨骼空间
	if (BuffObj.Carrier->GetMesh()&&BuffObj.Carrier->GetMesh()->DoesSocketExist(FName(SocketName)))
	{
		 Pos = BuffObj.Carrier->GetMesh()->GetSocketLocation(FName(SocketName));
		 Rot = BuffObj.Carrier->GetMesh()->GetSocketRotation(FName(SocketName));
		 FVector Forward = BuffObj.Carrier->GetActorForwardVector();
		 FVector Up =  UKismetMathLibrary::GetUpVector(Rot);
		 FVector Right =  UKismetMathLibrary::GetRightVector(Rot);
		NewRot = bUseAnimTransform? AnimRotator:UKismetMathLibrary::MakeRotationFromAxes(Forward,Right,Up);
		Direction = UKismetMathLibrary::GetForwardVector(NewRot);
		bUseNewRot= true;
	}
	else
	{
		//基础空间信息
		float HeightOffset = BuffObj.Carrier->GetCapsuleComponent()?BuffObj.Carrier->GetCapsuleComponent()->GetScaledCapsuleHalfHeight():0;
		Pos = BuffObj.Carrier->GetActorLocation();
		Pos.Z -=HeightOffset;
		Direction =bUseAnimTransform?UKismetMathLibrary::GetForwardVector(AnimRotator): BuffObj.Carrier->GetActorForwardVector();
	}
	Direction.Normalize();
	Pos = bUseAnimTransform?AnimPos:Pos;
	AAWAoe* Aoe =UGameplayFuncLib::CreateAOE(BuffObj.Carrier,AoeId,Pos,Direction,Durantion,Tween);
	if (bUseNewRot)
	{
		Aoe->SetActorRotation(NewRot);
	}
	if (Attach)
	{
		Aoe->AttachToActor(Res.BuffObj.Carrier,FAttachmentTransformRules::KeepWorldTransform);
	}
	if (IsValid(Aoe)&&Aoe->Implements<URogueInterface>())
	{
		TMap<FString,FString>InterfaceParams ;
		InterfaceParams = BuffObj.Param;
		InterfaceParams.Add("EffectLevel",FString::FromInt(BuffObj.Stack));
		IRogueInterface::Execute_GiveInfoMap(Aoe,InterfaceParams);
	}
	return Res;
}

FBuffDamageResult UBuffUtils::CreateAoeOnBeKilled(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
                                                  TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	if (!BuffObj.Carrier)
	{
		return  Res;
	}
	FString AoeId = Params.Num()>0?Params[0]:"";
	float Durantion =  Params.Num()>1?FCString::Atof(*Params[1]):0;
	FString SocketName = Params.Num()>2?Params[2]:"";
	FString Tween = Params.Num()>3?Params[3]:"";
	bool Attach = Params.Num()>4?Params[4].ToBool():false;
	
	FVector Pos = FVector::ZeroVector; ;
	FRotator Rot = FRotator::ZeroRotator;
	FVector Direction = FVector::ZeroVector;
	FRotator NewRot = FRotator::ZeroRotator;
	bool bUseNewRot = false;
	
	
	//使用骨骼空间
	if (BuffObj.Carrier->GetMesh()&&BuffObj.Carrier->GetMesh()->DoesSocketExist(FName(SocketName)))
	{
		 Pos = BuffObj.Carrier->GetMesh()->GetSocketLocation(FName(SocketName));
		 Rot = BuffObj.Carrier->GetMesh()->GetSocketRotation(FName(SocketName));
		 FVector Forward = BuffObj.Carrier->GetActorForwardVector();
		 FVector Up =  UKismetMathLibrary::GetUpVector(Rot);
		 FVector Right =  UKismetMathLibrary::GetRightVector(Rot);
		NewRot = UKismetMathLibrary::MakeRotationFromAxes(Forward,Right,Up);
		Direction = UKismetMathLibrary::GetForwardVector(NewRot);
		bUseNewRot= true;
	}
	else
	{
		//基础空间信息
		Pos = BuffObj.Carrier->GetActorLocation();
		Direction =BuffObj.Carrier->GetActorForwardVector();
	}
	Direction.Normalize();
	AAWAoe* Aoe =UGameplayFuncLib::CreateAOE(BuffObj.Carrier,AoeId,Pos,Direction,Durantion,Tween);
	if (bUseNewRot)
	{
		Aoe->SetActorRotation(NewRot);
	}
	if (Attach)
	{
		Aoe->AttachToActor(Res.BuffObj.Carrier,FAttachmentTransformRules::KeepWorldTransform);
	}
	return Res;
}

FBuffRunResult UBuffUtils::CreateBulletOnSocket(FBuffObj BuffObj, int WasStack, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult(BuffObj, nullptr);
	if (!BuffObj.Carrier)
	{
		return  Res;
	}
	auto Ability = BuffObj.Carrier->GetCurrentActionAbilityLevelInfo();
	int Level = Ability.AbilityInfoId.IsEmpty()?3:Ability.Level;
	FString BulletId = Params.Num()>0?Params[0]:"";
	float Durantion =  Params.Num()>1?FCString::Atof(*Params[1]):0;
	FString SocketName = Params.Num()>2?Params[2]:"";
	FJsonFuncData Tween = UDataFuncLib::SplitFuncNameAndParams(Params.Num()>3?Params[3]:"");

	
	
	FBulletModel  BulletModel = UGameplayFuncLib::GetDataManager()->GetBulletModelById(BulletId);

	FVector Pos = FVector::ZeroVector; ;
	FRotator Rot = FRotator::ZeroRotator;
	FVector Direction = FVector::ZeroVector;
	FRotator NewRot = FRotator::ZeroRotator;
	bool bUseNewRot = false;

	//取动画事件传参数
	bool bUseAnimTransform = Params.Contains("AnimParams");
	FRotator AnimRotator = FRotator::ZeroRotator;
	FVector AnimPos = FVector::ZeroVector;
	if (bUseAnimTransform)
	{
		int Index = Params.IndexOfByKey("AnimParams");
		if(Params.Num()>Index+1)
		{
			AnimRotator.InitFromString(Params[Index+1]);
			AnimRotator=UKismetMathLibrary::TransformRotation(BuffObj.Carrier->GetTransform(),AnimRotator);
		}
		if (Params.Num()>Index+2)
		{
			AnimPos.InitFromString(Params[Index+2]);
			AnimPos=UKismetMathLibrary::TransformLocation(BuffObj.Carrier->GetTransform(),AnimPos);
		}
	}
	//使用骨骼空间
	if (BuffObj.Carrier->GetMesh()&&BuffObj.Carrier->GetMesh()->DoesSocketExist(FName(SocketName)))
	{
		 Pos = BuffObj.Carrier->GetMesh()->GetSocketLocation(FName(SocketName));
		 Rot = BuffObj.Carrier->GetMesh()->GetSocketRotation(FName(SocketName));
		 FVector Forward = BuffObj.Carrier->GetActorForwardVector();
		 FVector Up =  UKismetMathLibrary::GetUpVector(Rot);
		 FVector Right =  UKismetMathLibrary::GetRightVector(Rot);
		NewRot = bUseAnimTransform? AnimRotator:UKismetMathLibrary::MakeRotationFromAxes(Forward,Right,Up);
		Direction = UKismetMathLibrary::GetForwardVector(NewRot);
		bUseNewRot= true;
	}
	else
	{
		//基础空间信息
		Pos = BuffObj.Carrier->GetActorLocation();
		Direction =bUseAnimTransform?UKismetMathLibrary::GetForwardVector(AnimRotator): BuffObj.Carrier->GetActorForwardVector();
	}
	Direction.Normalize();
	
	FBulletLauncher Launcher = FBulletLauncher();
	Launcher.Model = BulletModel;
	Launcher.Position =bUseAnimTransform?AnimPos:Pos;
	Launcher.Direction = Direction;
	Launcher.TweenFunc = Tween;
	Launcher.Caster = BuffObj.Carrier;

	AAwBullet* Bullet = UGameplayFuncLib::CreateBullet(Launcher,Level);

	if (IsValid(Bullet)&&Bullet->Implements<URogueInterface>())
	{
		TMap<FString,FString>InterfaceParams ;
		InterfaceParams = BuffObj.Param;
		InterfaceParams.Add("EffectLevel",FString::FromInt(BuffObj.Stack));
		IRogueInterface::Execute_GiveInfoMap(Bullet,InterfaceParams);
	}
	
	if (bUseNewRot)
	{
		Bullet->SetActorRotation(NewRot);
	}
	
	return  Res;
}

FBuffRunResult UBuffUtils::AddStackByPlayerDistance(FBuffObj BuffObj, int32 Ticked, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	AAwCharacter* Self = BuffObj.Carrier;
	AAwCharacter* Player = UGameplayFuncLib::GetAwGameState()->GetMyCharacter();
	if(Self && Player && Params.Num() > 0)
	{
		const float CurDis = FVector::Dist2D(Self->GetActorLocation(), Player->GetActorLocation());
		const float CheckDis = FCString::Atof(*Params[0]);
		if(CurDis >= CheckDis)
			Res.BuffObj.Stack += 1;
		else
			Res.BuffObj.Stack = 1;
	}
	return Res;
}

