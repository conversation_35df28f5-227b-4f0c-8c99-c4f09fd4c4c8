// Fill out your copyright notice in the Description page of Project Settings.


#include "DungeonBuff.h"

#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GamePlay/Battle/OffenseManager.h"


//【OnKill】有几率出球，概率被Dungeon_HealthOrbRate影响
FBuffDamageResult UDungeonBuff:: DungeonChanceToCreateHealthOrb(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult(BuffObj, DamInfo, nullptr);

	float Rate = BuffObj.Stack * 0.05f;
	TArray<FBuffObj*> CheckBuffs = BuffObj.Carrier->GetBuff("Dungeon_HealthOrbRate");
	if (CheckBuffs.Num() > 0)
	{
		int TotalStack = 0;
		for (const FBuffObj* TheBuff : CheckBuffs)
		{
			TotalStack += TheBuff->Stack;
		}
		Rate += TotalStack * 0.01f;
	}
	if (FMath::RandRange(0.000f, 1.000f) < Rate)
	{
		//TODO 出球，等做好球的aoe
	}

	return Res;
}

//【BeHurt】格挡反弹伤害
FBuffDamageResult UDungeonBuff::ThronsReflect(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult(BuffObj, DamInfo, nullptr);

	if (Target && Target->IsEnemy(BuffObj.Carrier) && DamInfo.DamageType == EDamageType::DirectDamage &&
		DamInfo.HitBox && DamInfo.HitBoxData->Type == ECharacterHitBoxType::Guard)
	{
		//算是格挡，反弹伤害
		const int ToDoDamage = BuffObj.Carrier->CharacterObj.CurProperty.PAttack * BuffObj.Stack * 0.075f + 0.125f;
		//TODO: 反弹一个伤害
		// UGameplayFuncLib::GetDamageManager()->AddDamage(
		// 	FDamageDealer(BuffObj.Carrier, Target, FDamageInfo::FromAttackOnly(nullptr, FAttackInfo(), BuffObj.Caster->CurrentAction()))
		// FDamageInfo(
		// 	BuffObj.Carrier, Target, FDamageValue(0,ToDoDamage), FAttackInfo(), EDamageType::ReflectDamage
		// ));
	}

	return Res;
}

//【BeHurt】闪避受伤下降
FBuffDamageResult UDungeonBuff::MasterDodge(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult(BuffObj, DamInfo, nullptr);

	//TODO 得有办法判断我是闪避动作，这个想好了再做，实在不行只能干掉

	return Res;
}

//【OnHit】总伤害达到阈值产生血球
FBuffDamageResult UDungeonBuff::BloodSeeker(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult(BuffObj, DamInfo, nullptr);

	if (DamInfo.IsHeal == false && Target && Target->Dead(true) == false && Target->IsEnemy(BuffObj.Carrier))
	{
		const int DamageValve = 2200 - 200 * BuffObj.Stack;
		int DealtDamage = BuffObj.Param.Contains("DamageDealt") ? FCString::Atoi(*BuffObj.Param["DamageDealt"]) : 0 + DamInfo.FinalDamage();
		//这里不能做while do，是因为每次攻击只能发动一次效果，所以让他累积着吧
		if (DealtDamage >= DamageValve)
		{
			//TODO 血球aoe，然后创造之
			
			DealtDamage -= DamageValve;
		}
		if (Res.BuffObj.Param.Contains("DamageDealt"))
		{
			Res.BuffObj.Param["DamageDealt"] = FString::FromInt(DealtDamage);
		}else
		{
			Res.BuffObj.Param.Add("DamageDealt", FString::FromInt(DealtDamage));
		}
	}
	

	return Res;
}

//【OnHit】连续攻击伤害提高
FBuffDamageResult UDungeonBuff::OffensiveTide(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult(BuffObj, DamInfo, nullptr);

	TArray<FString> BuffTags;
	BuffTags.Add("Dungeon");
	BuffTags.Add("PropertyUp");
	FBuffModel PowerUp = FBuffModel(
		FString("OffensiveTideDamage_").Append(FString::FromInt(BuffObj.Stack)), BuffTags, 1, 10
	);
	float DamageTimes = BuffObj.Stack * 0.040f;
	PowerUp.OnHit.Add(UCallFuncLib::StringToJsonFuncData(
		FString("BuffUtils.DamageTimesUp(").Append(FString::SanitizeFloat(DamageTimes)).Append(")")
	));
	BuffObj.Carrier->AddBuff(FAddBuffInfo(
		BuffObj.Carrier, BuffObj.Carrier, PowerUp, 1, 1.0f, true   //1.0f秒太长，就缩短
	));

	return Res;
}

//【BeHurt】根据挨打的时候降低一定比例伤害，记录为下来反击用
FBuffDamageResult UDungeonBuff::TurnbackStrength_Absorb(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult(BuffObj, DamInfo, nullptr);

	if (DamInfo.DamageType == EDamageType::DirectDamage && DamInfo.FinalDamage() > 0)
	{
		const float ReduceRate = BuffObj.Stack * 0.020f;
		const int WasDam = DamInfo.FinalDamage();
		Res.DamageInfo.DamagePower = Res.DamageInfo.DamagePower * (1.000f - ReduceRate);
		int Absorb = WasDam - Res.DamageInfo.FinalDamage();
		if (Res.BuffObj.Param.Contains("DamageAbsorb"))
		{
			Res.BuffObj.Param["DamageAbsorb"] = FString::FromInt(FCString::Atoi(*Res.BuffObj.Param["DamageAbsorb"]) + Absorb);
		}else
		{
			Res.BuffObj.Param.Add("DamageAbsorb", FString::FromInt(Absorb));
		}
	}

	return Res;
}

//【OnHit】根据Dungeon_TurnbackStrength层数和参数来进行伤害
FBuffDamageResult UDungeonBuff::TurnbackStrength(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult(BuffObj, DamInfo, nullptr);

	if (DamInfo.IsHeal == false && DamInfo.DamageSourceType == EAttackSource::AttackAction)
	{
		const int AbsorbValue = BuffObj.Param.Contains("DamageAbsorb") ? FCString::Atoi(*BuffObj.Param["DamageAbsorb"] ) : 0;
		if (AbsorbValue > 0)
		{
			Res.BuffObj.Param["DamageAbsorb"] = "0";
			Res.DamageInfo.DamagePower.Physical += AbsorbValue;
		}
	}

	return Res;
}

//【OnHit】有几率取消原本应该的弹刀
FBuffDamageResult UDungeonBuff::SteadyAsMountain_NotBounced(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult(BuffObj, DamInfo, nullptr);

	//TODO 弹刀和破坏动作有关，所以…………这效果得看看能不能重构出来

	return Res;
}

//【BeHurt】有几率取消原本应该的平衡崩坏 TODO：新模式下无法实现，待删除
// FBuffActionHitResult UDungeonBuff::SteadyAsMountain_NotBreakAction(FBuffObj Buff, FActionHitResult ActionHitInfo, TArray<FString> Params)
// {
// 	FBuffActionHitResult Res = FBuffActionHitResult(Buff, ActionHitInfo);
//
// 	const float Chance = Buff.Stack * 0.050f;
// 	if (FMath::RandRange(0.000f, 1.000f) < Chance)
// 	{
// 		//Res.HitInfo.HitInfo.PushPower.CanBlow = false;
// 	}
//
// 	return Res;
// }

//【BeKilled】免死金牌
FBuffDamageResult UDungeonBuff::FirmSprite(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult(BuffObj, DamInfo, nullptr);

	if (BuffObj.Carrier->Dead(true) == false && BuffObj.Carrier->CharacterObj.CurrentRes.HP <= DamInfo.FinalDamage() &&
		(BuffObj.Param.Contains("Used") == false || BuffObj.Param["Used"].IsEmpty()))
	{
		Res.DamageInfo.DamagePower.SetDamageZero();
		if (Res.BuffObj.Param.Contains("Used"))
		{
			Res.BuffObj.Param["Used"] = "1";
		}else
		{
			Res.BuffObj.Param.Add("Used", "1");
		}
	}

	return Res;
}

//【OnHit】伤害达到阈值获得一次Standard_IgnoreDamageOnce
FBuffDamageResult UDungeonBuff::DeathProofHitRecord(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult(BuffObj, DamInfo, nullptr);

	if (DamInfo.IsHeal == false && Target && Target->Dead(true) == false && Target->IsEnemy(BuffObj.Carrier))
	{
		const int DamageValve = 2200 - 200 * BuffObj.Stack;
		int DealtDamage = BuffObj.Param.Contains("DamageDealt") ? FCString::Atoi(*BuffObj.Param["DamageDealt"]) : 0 + DamInfo.FinalDamage();
		//这里不能做while do，是因为每次攻击只能发动一次效果，所以让他累积着吧
		if (DealtDamage >= DamageValve)
		{
			FBuffModel ToAdd = UGameplayFuncLib::GetAwDataManager()->GetBuffModelById("Standard_IgnoreDamageOnce");
			if (ToAdd.ValidBuffModel())
			{
				BuffObj.Carrier->AddBuff(FAddBuffInfo(BuffObj.Carrier, BuffObj.Carrier, ToAdd, 1, 3.000f));	//3秒不够短就再缩短
			}
			DealtDamage -= DamageValve;
		}
		if (Res.BuffObj.Param.Contains("DamageDealt"))
		{
			Res.BuffObj.Param["DamageDealt"] = FString::FromInt(DealtDamage);
		}else
		{
			Res.BuffObj.Param.Add("DamageDealt", FString::FromInt(DealtDamage));
		}
	}

	return Res;
}

//【OnHit】命中多个敌人造成额外伤害
FBuffDamageResult UDungeonBuff::SweepAllBeforeMe(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult(BuffObj, DamInfo, nullptr);

	//TODO 得想个别的办法，如果实在不行，就只能放弃了

	return Res;
}

//【OnHit】每第n次命中敌人，无视2点平衡值
FBuffDamageResult UDungeonBuff::CarryAll(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult(BuffObj, DamInfo, nullptr);

	if (DamInfo.IsHeal == false && Target && Target->Dead(true) == false && Target->IsEnemy(BuffObj.Carrier))
	{
		const int DamageValve = 2200 - 200 * BuffObj.Stack;
		int DealtDamage = BuffObj.Param.Contains("DamageDealt") ? FCString::Atoi(*BuffObj.Param["DamageDealt"]): 0 + DamInfo.FinalDamage();
		//这里不能做while do，是因为每次攻击只能发动一次效果，所以让他累积着吧
		if (DealtDamage >= DamageValve)
		{
			//Res.DamageInfo.AttackInfo.BreakPower += 2;
			DealtDamage -= DamageValve;
		}
		if (Res.BuffObj.Param.Contains("DamageDealt"))
		{
			Res.BuffObj.Param["DamageDealt"] = FString::FromInt(DealtDamage);
		}else
		{
			Res.BuffObj.Param.Add("DamageDealt", FString::FromInt(DealtDamage));
		}
	}

	return Res;
}

//【OnHit】下次伤害造成n倍，并且无视n点平衡（前提是Param.Ready=="1")
FBuffDamageResult UDungeonBuff::SepratesHeaven_HitEffect(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult(BuffObj, DamInfo, nullptr);

	if (DamInfo.IsHeal == false && Target && BuffObj.Param.Contains("Ready") && BuffObj.Param["Ready"] == "1")
	{
		BuffObj.Param["Ready"] = "0";
		const int DamageTimes = BuffObj.Stack * 0.800f + 1.000f;
		Res.DamageInfo.DamagePower = Res.DamageInfo.DamagePower * DamageTimes;
	}

	return Res;
}

// FBuffActionHitResult UDungeonBuff::SepratesHeaven_ActionHitEffect(FBuffObj BuffObj, FActionHitResult HitInfo, TArray<FString> Params)
// {
// 	FBuffActionHitResult Res = FBuffActionHitResult(BuffObj, HitInfo);
// 	if (HitInfo.TouchInfo.BeTouchedCharacter && BuffObj.Param.Contains("BreakReady") && BuffObj.Param["BreakReady"] == "1")
// 	{
// 		const int BalanceIgnore = BuffObj.Stack > 3 ? 3 : 2;
// 		BuffObj.Param["BreakReady"] = "0";
// 		//Res.HitInfo.HitInfo.BreakData.BreakPower += BalanceIgnore;
// 	}
// 	return Res;
// }


//【OnKill】检查房间是否还有人，如果没人，Param.Ready="1"
FBuffDamageResult UDungeonBuff::SepratesHeaven_FoeChecker(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult(BuffObj, DamInfo, nullptr);

	//TODO 判断是否清空了房间要等阿宏
	const bool RoomClean = true;
	if (RoomClean == true)
	{
		if (Res.BuffObj.Param.Contains("Ready") )
		{
			Res.BuffObj.Param["Ready"] = "1";
		}else
		{
			Res.BuffObj.Param.Add("Ready", "1");
		}
	}

	return Res;
}

//【OnHit】对满血敌人造成n倍伤害，命中头部为n+2倍
FBuffDamageResult UDungeonBuff::HeadOnBlow(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult(BuffObj, DamInfo, nullptr);

	if (DamInfo.IsHeal == false && Target && Target->HealthFull())
	{
		float DamageTimes = BuffObj.Stack * 0.500f + 1.000f;
		if (DamInfo.HitBox && DamInfo.HitBoxData->BelongsToPart && DamInfo.HitBoxData->BelongsToPart->PartType == EChaPartType::Head)
		{
			DamageTimes += 1.000f;
		}
		Res.DamageInfo.DamagePower = Res.DamageInfo.DamagePower * DamageTimes;
	}

	return Res;
}

//【OnHit】对30%以下的敌人造成n倍伤害
FBuffDamageResult UDungeonBuff::MaltreatInjury(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult(BuffObj, DamInfo, nullptr);

	if (DamInfo.IsHeal == false && Target->Dead(true) == false && Target->HealthPercentage() <= 0.300f)
	{
		const float DamageTimes = BuffObj.Stack *  0.100f + 1.200f; //提升 30% 40% 50% 60% 70%
		Res.DamageInfo.DamagePower = Res.DamageInfo.DamagePower * DamageTimes;
	}

	return Res;
}

//【OnKill】杀死敌人获得加速（Standard_MoveSpeedUp和Standard_ActionSpeedUp）
FBuffDamageResult UDungeonBuff::LikeTheWind(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult(BuffObj, DamInfo, nullptr);

	TArray<FString> BuffTags;
	BuffTags.Add("Dungeon");
	BuffTags.Add("PropertyUp");
	BuffTags.Add("MoveSpeedUp");
	BuffTags.Add("ActionSpeedUp");
	FBuffModel PowerUp = FBuffModel(
		FString("LikeTheWindPropUp_").Append(FString::FromInt(BuffObj.Stack)),
		BuffTags, 1
	);
	PowerUp.CharacterPropertyModify[1].ActionSpeed = BuffObj.Stack * 500;	//等级x5
	
	PowerUp.CharacterPropertyModify[1].MoveSpeed[0] += BuffObj.Stack * 500;	//等级x5
	PowerUp.CharacterPropertyModify[1].MoveSpeed[1] += BuffObj.Stack * 500;	//等级x5
	PowerUp.CharacterPropertyModify[1].MoveSpeed[2] += BuffObj.Stack * 500;	//等级x5
	
	BuffObj.Carrier->AddBuff(FAddBuffInfo(
		BuffObj.Carrier, BuffObj.Carrier, PowerUp, 1, 10.000f, false //可以叠加所以false
	));
	
	return Res;
}

//【OnKill】击杀额外获得金币，每第n次额外获得更多金币
FBuffDamageResult UDungeonBuff::BattleFieldCleaner(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult(BuffObj, DamInfo, nullptr);

	//TODO 等磨砂猫的给钱函数

	return Res;
}

//【OnKill】获得额外的英勇作战经验（这个这里不能实现），根据Dungeon_Courage层数回血
FBuffDamageResult UDungeonBuff::HighMorale(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult(BuffObj, DamInfo, nullptr);

	const int ToHealValue = BuffObj.Carrier->HealthOfPercentage(0.050f * BuffObj.Stack);

	FOffenseInfo Offense = FOffenseInfo();
	Offense.AttackInfo = FAttackInfo();
	Offense.AttackInfo.DamagePower = FDamageValue(ToHealValue);
	Offense.AttackInfo.IsHeal = true;
	Offense.AttackInfo.DamageSourceType = EAttackSource::Buff;
	Offense.AttackInfo.DamageType = EDamageType::PeriodDamage;
	Offense.AttackInfo.Elemental = BuffObj.Caster->GetElemental(EChaElemental::Physical, EElementalPickMethod::Set, ESlotInWeaponObj::MainWeapon);
	Offense.SourceId = BuffObj.Model.Id;
	Offense.CanHitTimes = 1;
	Offense.Index = BuffObj.Duration;
	//BuffObj.Caster->BeOffended(Offense, BuffObj.Caster, BuffObj.Carrier->MostPriorityHitBox());
	UOffenseManager::DoBuffOffense(Offense, BuffObj.Carrier, BuffObj.Caster);

	return Res;
}

//【OnKill】自身满血受伤减半，并且反噬攻击者
FBuffDamageResult UDungeonBuff::ThornsGuardian(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult(BuffObj, DamInfo, nullptr);

	if (DamInfo.FinalDamage() > 0 && BuffObj.Carrier && BuffObj.Carrier->HealthFull() == true)
	{
		const int ToDealDam = FMath::CeilToInt(Res.DamageInfo.FinalDamage() / 2.000f * BuffObj.Stack);
		if (ToDealDam >= 0 && BuffObj.Caster != BuffObj.Carrier)
		{
			//TODO: 反伤
			// UGameplayFuncLib::GetDamageManager()->AddDamage(FDamageInfo(
			// 	BuffObj.Caster, Target,
			// 	FDamageValue(0, ToDealDam),
			// 	FAttackInfo(), EDamageType::ReflectDamage
			// ));
			Res.DamageInfo.DamagePower = Res.DamageInfo.DamagePower / 2;
		}
	}

	return Res;
}