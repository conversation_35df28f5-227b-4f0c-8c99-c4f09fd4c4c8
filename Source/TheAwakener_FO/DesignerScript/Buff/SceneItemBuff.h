// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/GamePlay/Buff/Buff.h"
#include "UObject/Object.h"
#include "SceneItemBuff.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API USceneItemBuff : public UObject
{
	GENERATED_BODY()

	//【OnBeKilled】死亡时 SceneItem 去掉一个 保护者 Protector
	UFUNCTION()
	static FBuffDamageResult ProtectorIsDead(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【OnTick】层数大于等于n层的时候，执行保护动作
	// [0] 层数
	UFUNCTION()
	static FBuffRunResult DoProtectAction(FBuffObj BuffObj, int32 Ticked, TArray<FString> Params); 
};
