// Fill out your copyright notice in the Description page of Project Settings.


#include "DialogBuffs.h"

#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"

FBuffDamageResult UDialogBuffs::SpeakOnDissatisfy(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult(
		BuffObj, DamInfo, nullptr
	);

	const FString SpokenKey = "Spoken";
	const bool Spoken = BuffObj.Param.Contains(SpokenKey) ? BuffObj.Param[SpokenKey].IsEmpty() == false : false;
	if (!Target || Target->IsPlayerCharacter() == false
		|| !BuffObj.Carrier || Target->GetNpcId().IsEmpty() || Spoken == true) return Res;
	
	//参数：[是友好度(0)还是敌对度(1)，百分比(0-1)，说话人名(string)，说话内容(string)，显示多久（float）]
	const bool IsFriendCheck = Params.Num() > 0 ? FCString::Atoi(*Params[0]) == 0 : true;
	const float PerCheck = Params.Num() > 1 ? FCString::Atof(*Params[1]) : 0;
	const FString SpeakerName = Params.Num() > 2 ? Params[2] : "<Unnamed>";
	const FString SpeakDialog = Params.Num() > 3 ? Params[3] : "";
	const float SpeakDuration = Params.Num() >  4 ? FCString::Atof(*Params[4]) : 10;

	if (IsFriendCheck == true)
	{
		//友好度是检查<=传入百分比
		const float CurPer = BuffObj.Carrier->NpcInfo.Personality.FriendlyPercentage(Target->GetNpcId());
		UKismetSystemLibrary::PrintString(Target, FString("Hate Me ? ").Append(FString::SanitizeFloat(CurPer)).Append("/").Append(FString::SanitizeFloat(PerCheck)),
			true, true, FLinearColor::White, SpeakDuration);
		if (CurPer <= PerCheck)
		{
			BuffObj.Carrier->ShowTextBubble(SpeakDialog, SpeakerName, SpeakDuration);
			Res.BuffObj.Param.Add(SpokenKey, "1");
		}
	}else
	{
		//地对度检查是>=传入百分比
		const float CurPer = BuffObj.Carrier->NpcInfo.Personality.HostilePercentage(Target->GetNpcId());
		if (CurPer >= PerCheck)
		{
			BuffObj.Carrier->ShowTextBubble(SpeakDialog, SpeakerName, SpeakDuration);
			Res.BuffObj.Param.Add(SpokenKey, "1");
		}
	}

	return Res;
}
