// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/GamePlay/Buff/Buff.h"
#include "UObject/Object.h"
#include "BuffUtils.generated.h"

/**
 * Buff常用的函数
 */
UCLASS()
class THEAWAKENER_FO_API UBuffUtils : public UObject
{
	/**
	 * Buff携带着给自己添加buff，Param
	 * [0] String BuffId
	 * [1] int AddStack
	 * [2] float AddTime
	 * [3] bool Infinity (默认false)
	 * [4] bool setTime
	 */
	GENERATED_BODY()
	static FBuffRunResult SelfAddBuff(FBuffObj BuffObj, TArray<FString> Params);
public:
	//【OnOccur】给自己上个子buff 参数同SelfAddBuff
	UFUNCTION()
	static FBuffRunResult AddSubBuffObj(FBuffObj BuffObj, int WasStack, TArray<FString> Params);
	//【OnRemove】移除n层子Buff 参数同AddBuffObj  移除层数为母Buff层数*配置层数
	UFUNCTION()
	static FBuffRunResult RemoveSubBuffObjStack(FBuffObj BuffObj, bool IsDispelled, TArray<FString> Params);

	//【OnOccur】根据MaxHP设置Buff层数，记录Buff的初始层数
	UFUNCTION()
	static FBuffRunResult InitBreakBuff(FBuffObj BuffObj, int WasStack, TArray<FString> Params);
	
	//OnAnimNotfiyEnd】移除同id的子Buff 
	UFUNCTION()
	static FBuffRunResult RemoveSubBuffObj(FBuffObj BuffObj, int WasStack, TArray<FString> Params);

	//【OnOccur】【OnRemove】【OnAnimNotfiyBegin】给自己上个子buff 参数同SelfAddBuff
	UFUNCTION()
	static FBuffRunResult AddBuffObj(FBuffObj BuffObj, int WasStack, TArray<FString> Params);
	//【OnChangeAction】删除所有的同id的BuffObj
	UFUNCTION()
	static FBuffRunResult RemoveBuffObj(FBuffObj BuffObj, FActionInfo WasAction, FActionInfo CurrentAction, TArray<FString> Params);

	//【OnTick】buff自己添加层数
	// [0] int AddStack
	UFUNCTION()
	static FBuffRunResult AddStackSelf(FBuffObj BuffObj, int32 Ticked, TArray<FString> Params); 
	
	//【OnTick】逐渐掉血
	UFUNCTION()
	static FBuffRunResult DamageOverTime(FBuffObj BuffObj, int32 Ticked, TArray<FString> Params);

	//【OnRemove】造成一次伤害
	UFUNCTION()
	static FBuffRunResult CreateAOEOnRemove(FBuffObj BuffObj, bool IsDispelled, TArray<FString> Params);

	//【OnBeKilled】 给Buff的Caster添加Buff
	//Params[0]:BuffId
	//Params[1]:BuffStack
	//Params[2]:BuffDuration
	//Params[3]:BuffSetToDuration
	//Params[4]:BuffInfinity
	UFUNCTION()
	static FBuffDamageResult AddBuffToBuffCaster(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【OnBeHurt】标准的护盾吸收伤害（牧师的真言术盾）
	UFUNCTION()
	static FBuffDamageResult ShieldProtect(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【OnTick】改变Buff
	UFUNCTION()
	static FBuffRunResult SelfAddBuff_OnTick(FBuffObj BuffObj, int32 Ticked, TArray<FString> Params) {return UBuffUtils::SelfAddBuff(BuffObj, Params);};
	//【OnChangeAction】改变Buff
	UFUNCTION()
	static FBuffRunResult SelfAddBuff_OnChangeAction(FBuffObj BuffObj, FActionInfo WasAction, FActionInfo CurrentAction, TArray<FString> Params){return UBuffUtils::SelfAddBuff(BuffObj, Params);};
	//【伤害流程】改变Buff
	UFUNCTION()
	static FBuffDamageResult SelfAddBuff_InDamage(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【OnHit】造成的伤害倍数提高，参数：[0]提高的倍数，0.1=+10%
	UFUNCTION()
	static FBuffDamageResult DamageTimesUp(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【OnHit】造成的伤害倍数减少，参数：[0]减少的倍数，0.1=+10%
	UFUNCTION()
	static FBuffDamageResult DamageTimesDown(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);
	
	//【OnHit】空中造成的伤害倍数提高，参数：[0]提高的倍数，0.1=+10%
	UFUNCTION()
	static FBuffDamageResult DamageTimesUpInSky(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【OnHit】地面造成的伤害倍数提高，参数：[0]提高的倍数，0.1=+10%
	UFUNCTION()
	static FBuffDamageResult DamageTimesUpOnGround(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);


	//【OnHit】空中造成的物理伤害倍数提高，参数：[0]提高的倍数，0.1=+10%
	UFUNCTION()
	static FBuffDamageResult PhysicalDamageTimesUpInSky(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【OnHit】地面造成的物理伤害倍数提高，参数：[0]提高的倍数，0.1=+10%
	UFUNCTION()
	static FBuffDamageResult PhysicalDamageTimesUpOnGround(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	
	//【OnHit】击中时移除自身Buff目标层数
	UFUNCTION()
	static FBuffDamageResult RemoveSelfBuffStackOnHit(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);
	
	//【OnBeHurt】被击中时给来源一个Buff
	UFUNCTION()
	static FBuffDamageResult AddBuffToHurtSourceByChance (FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);


	//【OnHit】概率给击中目标上个buff
	UFUNCTION()
	static FBuffDamageResult AddBuffToHitTarget (FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);
	
	//【OnHit】概率给击中目标上个buff
	UFUNCTION()
	static FBuffDamageResult AddBuffToHitTargetByChance (FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);
	
	//【OnHit】击中时生成AOE
	UFUNCTION()
	static FBuffDamageResult CreateAoeOnHitPos(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【OnHit】击中时生成额外Offense
	UFUNCTION()
	static FBuffDamageResult DoExtraOffense(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	
	//【OnBeHurt】受到额外伤害
	UFUNCTION()
	static FBuffDamageResult TakeExtraDamage(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);
	
	//【OnHit】伤害为999999
	UFUNCTION()
	static FBuffDamageResult TakeDamage999999(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);
	
	//【OnOccur】角色自己给自己开启光环（创建aoe），参数：[0]aoeModelId, [1]持续时间，[2]半径
	UFUNCTION()
	static FBuffRunResult SelfAura(FBuffObj BuffObj, int WasStack, TArray<FString> Params);

	//【OnOccur】角色动作停滞 不包括玩家
	UFUNCTION()
	static FBuffRunResult PlayFrozen(FBuffObj BuffObj, int WasStack, TArray<FString> Params);

	//【OnRemove】角色恢复动作 不包括玩家
	UFUNCTION()
	static FBuffRunResult StopFrozen(FBuffObj BuffObj, bool IsDispelled, TArray<FString> Params);
	
	//【BeHurt】增加受到的伤害
	UFUNCTION()
	static FBuffDamageResult HurtDamageUp(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);
	
	//【BeHurt】免疫一次伤害
	UFUNCTION()
	static FBuffDamageResult IgnoreDamage(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【BeHurt】在Buff层数大于等于Params[0]时，免疫伤害
	UFUNCTION()
	static FBuffDamageResult IgnoreDamageWhenStackGreater(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【OnBeHurt】 检测血量小于Params[0]时，免疫伤害
	UFUNCTION()
	static FBuffDamageResult IgnoreDamageWhenHpLess(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【BeHurt】免疫致死伤害
	UFUNCTION()
	static FBuffDamageResult IgnoreDeathDamage(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);
	
	//【BeHurt】伤害化为0，但是打出log来
	UFUNCTION()
	static FBuffDamageResult TakeNoDamageAndPrint(FBuffObj Buff, FDamageInfo Dam, AAwCharacter* Target, TArray<FString> Params);

	//【OnBeHurt】在boss普通状态时，收到伤害会改变FightWill.Value
	UFUNCTION()
	static FBuffDamageResult ModifyFightWillWhenNormalStage(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【OnBeHurt】 积累xxx伤害，做一次xxx动作
	UFUNCTION()
	static FBuffDamageResult AccumulateDamageAndHurt(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【OnBeHurt】 积累xxx Break伤害，做一次xxx动作(Break伤害值根据实际伤害*AttackInfo.)
	UFUNCTION()
	static FBuffDamageResult AccumulateBreakDamageAndHurt(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【OnBeHurt】 受击时增加Buff层数
	UFUNCTION()
	static FBuffDamageResult AddStackOnBeHurt(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【OnHit】提高BreakDamage倍率
	UFUNCTION()
	static FBuffDamageResult BreakDamageUp(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);
	
	//【OnOccur】角色播放视觉特效 [0]赋予的句柄(string) [1]特效路径(string，必须是粒子) [2]所处绑点类型(EVFXBindPointType) [3]是否播放一次(bool) [4]开始播放的层数
	UFUNCTION()
	static FBuffRunResult PlayVFXOnCha_Occur(FBuffObj BuffObj, int WasStack, TArray<FString> Params);

	//【OnRemove】角色停止视觉特效 [0]句柄(fstring)
	UFUNCTION()
	static FBuffRunResult StopVFXOnCha_Remove(FBuffObj BuffObj, bool IsDispelled, TArray<FString> Params);

	//【OnBeKilled】角色停止视觉特效 [0]句柄(fstring)
	UFUNCTION()
	static FBuffDamageResult StopVFXOnOnBeKilled_Remove(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);
	
	//【OnOccur】对角色进行缩放
	UFUNCTION()
	static FBuffRunResult SetCharacterScale_Occur(FBuffObj BuffObj, int WasStack, TArray<FString> Params);

	//【OnRemove】对角色进行缩放
	UFUNCTION()
	static FBuffRunResult SetCharacterScale_Remove(FBuffObj BuffObj,bool IsDispelled, TArray<FString> Params);

	//【OnTick】
	//在携带者身上创建AOE
	UFUNCTION()
	static FBuffRunResult CreateAoeOnCarrierOnTick(FBuffObj BuffObj, int32 WasStack, TArray<FString> Params);
	
	//[OnAnimNotfiy]
	UFUNCTION()
	static FBuffRunResult CreateAoeOnSocket(FBuffObj BuffObj, int32 WasStack, TArray<FString> Params);

	//[OnAnimNotfiy]
	UFUNCTION()
	static FBuffRunResult CreateAoeOnSocketByCoolDown(FBuffObj BuffObj, int32 WasStack, TArray<FString> Params);
	
	//【OnBeKilled】 被击杀时创建Aoe
	UFUNCTION()
	static FBuffDamageResult CreateAoeOnBeKilled(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);
	
	//在携带者socket处生成Bullet
	//【OnAnimNotfiy】
	UFUNCTION()
	static FBuffRunResult CreateBulletOnSocket(FBuffObj BuffObj, int WasStack, TArray<FString> Params);

	//【OnTick】
	//检测玩家是否在BUFF携带者的Params[0]距离外
	//在距离外的buff层数+1
	//在距离内的buff层数设为1
	UFUNCTION()
	static FBuffRunResult AddStackByPlayerDistance(FBuffObj BuffObj, int32 Ticked, TArray<FString> Params);
	
	// 打印
	UFUNCTION()
	static FBuffRunResult PrintLogOnOccur(FBuffObj BuffObj, int32 WasStack, TArray<FString> Params)
	{
		UE_LOG(LogTemp, Log, TEXT("%s"), *Params[0]);
		return FBuffRunResult();
	}
	// 打印
	UFUNCTION()
	static FBuffRunResult PrintLogOnTick(FBuffObj BuffObj, int32 Ticked, TArray<FString> Params)
	{
		UE_LOG(LogTemp, Log, TEXT("%s"), *Params[0]);
		return FBuffRunResult();
	}
	// 打印
	UFUNCTION()
	static FBuffRunResult PrintLogOnRemoved(FBuffObj BuffObj, bool IsDispelled, TArray<FString> Params)
	{
		UE_LOG(LogTemp, Log, TEXT("%s"), *Params[0]);
		return FBuffRunResult();
	}
	// 打印
	UFUNCTION()
	static FBuffDamageResult PrintLogOnHit(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
	                                       TArray<FString> Params)
	{
		UE_LOG(LogTemp, Log, TEXT("%s"), *Params[0]);
		return FBuffDamageResult();
	}
	// 打印
	UFUNCTION()
	static FBuffDamageResult PrintLogOnBeHurt(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
	                                          TArray<FString> Params)
	{
		UE_LOG(LogTemp, Log, TEXT("%s"), *Params[0]);
		return FBuffDamageResult();
	}
	// 打印
	UFUNCTION()
	static FBuffDamageResult PrintLogOnKill(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
	                                        TArray<FString> Params)
	{
		UE_LOG(LogTemp, Log, TEXT("%s"), *Params[0]);
		return FBuffDamageResult();
	}
	// 打印
	UFUNCTION()
	static FBuffDamageResult PrintLogOnBeKilled(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
	                                            TArray<FString> Params)
	{
		UE_LOG(LogTemp, Log, TEXT("%s"), *Params[0]);
		return FBuffDamageResult();
	}
	// 打印
	UFUNCTION()
	static FBuffRunResult PrintLogOnChangeAction(FBuffObj BuffObj, FActionInfo WasAction, TArray<FString> Params)
	{
		UE_LOG(LogTemp, Log, TEXT("%s"), *Params[0]);
		return FBuffRunResult();
	}
	// 打印
	UFUNCTION()
	static FBuffOffenseResult PrintLogOnOffense(FBuffObj BuffObj, AActor* Target, FOffenseInfo UsingOffenseHitTheTarget,
	                                            TArray<FString> Params)
	{
		UE_LOG(LogTemp, Log, TEXT("%s"), *Params[0]);
		return FBuffOffenseResult();
	}

	//【OnTick】Print Buff当前的Duration
	UFUNCTION()
	static FBuffRunResult PrintBuffDuration(FBuffObj BuffObj, int32 Ticked, TArray<FString> Params);
	
};
