// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/Gameframework/Timeline/TimelineNode.h"
#include "TheAwakener_FO/GamePlay/Buff/Buff.h"
#include "UObject/Object.h"
#include "WarriorBuff.generated.h"

/**
 * 战士专有buff的效果
 */
UCLASS()
class THEAWAKENER_FO_API UWarriorBuff : public UObject
{
	GENERATED_BODY()
public:
	UFUNCTION()
	static FBuffDamageResult ChargingHit(FBuffObj Buff, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);
};
