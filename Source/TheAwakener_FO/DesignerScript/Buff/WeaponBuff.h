// 

#pragma once

#include "CoreMinimal.h"
#include "UObject/Object.h"
#include "WeaponBuff.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UWeaponBuff : public UObject
{
	GENERATED_BODY()

public:
	
	//【OnHit】
	// 盾反伤害提高 1层1%
	UFUNCTION()
	static FBuffDamageResult DamageUp_CounterAction(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【OnHit】
	// JustAttack伤害提高 1层1%
	UFUNCTION()
	static FBuffDamageResult DamageUp_JustAttackAction(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【OnHit】
	// 每次武器命中 有几层回几点血
	UFUNCTION()
	static FBuffDamageResult AddHpOnHit(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【OnTick】在空中每停留Dur秒，攻击力增加AtkAdd%，最多AtkMaxAdd%
	// Params[0] Dur
	// Params[1] AtkAdd
	// Params[2] AtkMaxAdd
	UFUNCTION()
	static FBuffRunResult AttackUpOnAir(FBuffObj BuffObj, int32 Ticked, TArray<FString> Params);

	//【OnHit】
    // 蓄力伤害提高 1层1%
    UFUNCTION()
    static FBuffDamageResult DamageUp_PowerAction(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【OnHit】
	// 蓄力伤害提高 1层1%
	UFUNCTION()
	static FBuffDamageResult DamageDown_PowerAction(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);
};
