// Fill out your copyright notice in the Description page of Project Settings.


#include "TitanGolemBuff.h"

#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"

FBuffRunResult UTitanGolemBuff::ShowStone(FBuffObj BuffObj, int WasStack, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	if(BuffObj.Stack == 0) return Res;
	FString LevelName = "";
	if (UGameplayFuncLib::GetAwGameState()->AllCharacters.Contains(BuffObj.Carrier))
		LevelName = *UGameplayFuncLib::GetAwGameState()->AllCharacters.Find(BuffObj.Carrier);

	TArray<int> Durability;
	Durability.Add(10);
	Durability.Add(20);
	BuffObj.Carrier->RestoreChaPart("Stone", Durability);

	return Res;
}

FBuffRunResult UTitanGolemBuff::HideStone(FBuffObj BuffObj, int WasStack, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	if(BuffObj.Carrier)
		BuffObj.Carrier->BreakChaPart("Stone");
	return Res;
}
