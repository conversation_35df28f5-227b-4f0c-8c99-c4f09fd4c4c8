// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/GamePlay/Buff/Buff.h"
#include "TheAwakener_FO/GamePlay/Roguelike/Map/RogueMapConfig.h"
#include "UObject/Object.h"
#include "RogueBuff.generated.h"

/**
 * Buff常用的函数
 */
UCLASS()
class THEAWAKENER_FO_API URogueBuff : public UObject
{
	GENERATED_BODY()

public:
	//【OnOccur】提升肉鸽血瓶治疗效果 [0] 提升量
	UFUNCTION()
		static FBuffRunResult PotionHealUp(FBuffObj BuffObj,int WasStack , TArray<FString> Params);
	//【OnOccur】减少肉鸽血瓶治疗效果 0] 减少量
	UFUNCTION()
		static FBuffRunResult PotionHealDown(FBuffObj BuffObj,int WasStack, TArray<FString> Params);

	//【OnRemove】提升肉鸽血瓶治疗效果 [0] 提升量
	UFUNCTION()
		static FBuffRunResult PotionHealUp_Remove(FBuffObj BuffObj,bool IsDispelled , TArray<FString> Params);
	//【OnRemove】减少肉鸽血瓶治疗效果 0] 减少量
	UFUNCTION()
		static FBuffRunResult PotionHealDown_Remove(FBuffObj BuffObj,bool IsDispelled, TArray<FString> Params);
	
	//【OnOccur】提升肉鸽血瓶百分比等级治疗效果 [0] 提升量
	UFUNCTION()
		static FBuffRunResult PotionHealLevelUp(FBuffObj BuffObj,int WasStack , TArray<FString> Params);
	//【OnOccur】减少肉鸽血瓶百分比等级治疗效果 0] 减少量
	UFUNCTION()
		static FBuffRunResult PotionHealLevelDown(FBuffObj BuffObj,int WasStack, TArray<FString> Params);

	//【OnRemove】提升肉鸽血瓶百分比等级治疗效果 [0] 提升量
	UFUNCTION()
		static FBuffRunResult PotionHealLevelUp_Remove(FBuffObj BuffObj,bool IsDispelled, TArray<FString> Params);
	//【OnRemove】减少肉鸽血瓶百分比等级治疗效果 0] 减少量
	UFUNCTION()
		static FBuffRunResult PotionHealLevelDown_Remove(FBuffObj BuffObj,bool IsDispelled, TArray<FString> Params);

	//【OnOccur】减少肉鸽血瓶百分比总治疗效果 [0] 减少量
	UFUNCTION()
		static FBuffRunResult PotionHealValueDown(FBuffObj BuffObj,int WasStack , TArray<FString> Params);
	//【OnRemove】减少肉鸽血瓶百分比总治疗效果 [0] 减少量
	UFUNCTION()
		static FBuffRunResult PotionHealValueDown_Remove(FBuffObj BuffObj,bool IsDispelled, TArray<FString> Params);
	
	//【OnOccur】提升肉鸽血瓶数量 [0]提升数量
	UFUNCTION()
		static FBuffRunResult PotionNumUp(FBuffObj BuffObj,int WasStack, TArray<FString> Params);
	//【OnOccur】降低肉鸽血瓶数量  [0]降低数量
	UFUNCTION()
		static FBuffRunResult PotionNumDown(FBuffObj BuffObj,int WasStack, TArray<FString> Params);

	//【OnRemove】提升肉鸽血瓶数量 [0]提升数量
	UFUNCTION()
		static FBuffRunResult PotionNumUp_Remove(FBuffObj BuffObj,bool IsDispelled, TArray<FString> Params);
	//【OnRemove】降低肉鸽血瓶数量  [0]降低数量
	UFUNCTION()
		static FBuffRunResult PotionNumDown_Remove(FBuffObj BuffObj,bool IsDispelled, TArray<FString> Params);
	
	//【OnOccur】肉鸽回满血瓶
	UFUNCTION()
		static FBuffRunResult PotionNumRecover(FBuffObj BuffObj,int WasStack, TArray<FString> Params);

	//【OnOccur】肉鸽道具充能倍率速度提升
	UFUNCTION()
		static FBuffRunResult RogueItemRecoverUp_OnOccur(FBuffObj BuffObj,int WasStack, TArray<FString> Params);

	//【OnRemove】肉鸽道具充能倍率速度提升
	UFUNCTION()
		static FBuffRunResult RogueItemRecoverUp_OnRemove(FBuffObj BuffObj,bool IsDispelled, TArray<FString> Params);

	//【OnOccur】肉鸽道具充能倍率速度降低
	UFUNCTION()
		static FBuffRunResult RogueItemRecoverDown_OnOccur(FBuffObj BuffObj,int WasStack, TArray<FString> Params);

	//【OnRemove】肉鸽道具充能倍率速度降低
	UFUNCTION()
		static FBuffRunResult RogueItemRecoverDown_OnRemove(FBuffObj BuffObj,bool IsDispelled, TArray<FString> Params);

	//【OnOccur】肉鸽道具充能速度固定提升
	UFUNCTION()
		static FBuffRunResult RogueItemRecoverValueUp_OnOccur(FBuffObj BuffObj,int WasStack, TArray<FString> Params);

	//【OnRemove】肉鸽道具充能速度固定提升
	UFUNCTION()
		static FBuffRunResult RogueItemRecoverValueUp_OnRemove(FBuffObj BuffObj,bool IsDispelled, TArray<FString> Params);

	//【OnOccur】肉鸽道具充能速度固定降低
	UFUNCTION()
		static FBuffRunResult RogueItemRecoverValueDown_OnOccur(FBuffObj BuffObj,int WasStack, TArray<FString> Params);

	//【OnRemove】肉鸽道具充能速度固定降低
	UFUNCTION()
		static FBuffRunResult RogueItemRecoverValueDown_OnRemove(FBuffObj BuffObj,bool IsDispelled, TArray<FString> Params);

	
	//【OnOccur】Buff持续时间计算对应抗性buff进行衰减 【0】目标抗性buff 1层=0.01%
	UFUNCTION()
		static FBuffRunResult BuffTimeCaluTargetResistance(FBuffObj BuffObj,int WasStack, TArray<FString> Params);

	//【OnOccur】Buff属性计算 对应抗性【0】目标抗性buff 1层=0.01%
	UFUNCTION()
		static FBuffRunResult BuffPropCaluTargetResistance(FBuffObj BuffObj,int WasStack, TArray<FString> Params);

	//【OnOccur】当Buff的给予者有Buff.Id == Params[0]的Buff,使当前Buff的持续时间增加 (Params[0]的Buff层数 * Params[1])s
	UFUNCTION()
		static FBuffRunResult AddBasicElementalBuffDuration(FBuffObj BuffObj,int WasStack, TArray<FString> Params);
	
	//【OnOccur】 引爆肉鸽具有Tag的buff效果 并根据层数给与n*Param秒的 新buff
	// 【0】概率  【1】 SourceBuff 【2】 目标Buff  【3】  转化率  【4】 最小层数 【5】已有则不转化 【6】覆盖原有时间 【7】 是否完全消耗目标buff 不消耗则每个目标只触发一次
	UFUNCTION()
	static FBuffRunResult BuffChanceCastById_StackToTime(FBuffObj BuffObj,int WasStack, TArray<FString> Params);

	//【OnHit】 概率将击中目标的Buff  转化成目标层数的Buff
	// 【0】概率  【1】 SourceBuff 【2】 目标Buff  【3】  转化率  【4】 最小层数 【5】已有则不转化 【6】覆盖原有时间 【7】 是否完全消耗目标buff 不消耗则每个目标只触发一次
	UFUNCTION()
	static FBuffDamageResult BuffChanceCastById_StackToTimeOnHit(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);
	
	//【OnHit】 概率将击中目标的Buff  转化成生命恢复
	// 【0】 SourceBuff 【1】 生命恢复固定值 【2】  生命恢复百分比  【3】 最小层数 【4】 是否完全消耗目标buff 不消耗则每个目标只触发一次
	UFUNCTION()
	static FBuffDamageResult BuffCastById_StackToHpRecoverOnHit(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);
	
	//【OnHit】 概率将击中目标的Buff  转化成目标层数的Buff
	//【0】概率  【1】 SourceBuff 【2】 目标Buff  【3】  转化率  【4】 最小层数 【5】持续时间 【6】是否无限时间 【7】覆盖原有时间  【8】已有则不转化【9】 是否完全消耗目标buff 不消耗则每个目标只触发一次
	UFUNCTION()
	static FBuffDamageResult BuffChanceCastById_StackToStack(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);
	
	//【OnHit】概率将击中目标的Buff转换为Aoe
	UFUNCTION()
	static FBuffDamageResult BuffChanceCastById_StackToAoe (FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【OnHit】概率将击中目标的Buff转换为Bullet
	UFUNCTION()
	static FBuffDamageResult BuffChanceCastById_StackToBullet (FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【OnHit】概率将击中目标的Buff转换为风蚀 (Rogue限定)
	UFUNCTION()
	static FBuffDamageResult BuffChanceCastById_StackToWindErosion (FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【OnHit】概率将击中目标的Buff转换为风蚀Buff (Rogue限定)
	UFUNCTION()
	static FBuffDamageResult BuffChanceCastById_StackToWindErosionBuff (FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【OnHit】击中时在对方身上生成AOE
	UFUNCTION()
	static FBuffDamageResult CreateAoeOnHitTarget(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【OnBeHurt】受到额外伤害
	UFUNCTION()
	static FBuffDamageResult TakeExtraDamageByCasterProp(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【OnBeHurt】受到道具回复量减少
	UFUNCTION()
	static FBuffDamageResult TakeItemHealDown(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);
	
	//【OnTick】每次tick 消耗层数 变成对自己的一次伤害
	UFUNCTION()
	static FBuffRunResult CostStackToOffenseSelf(FBuffObj BuffObj, int32 Ticked, TArray<FString> Params);

	//【OnOccur】 消耗层数 变成对自己最大生命值相关的一次伤害
	UFUNCTION()
	static FBuffRunResult CostAllStackToMaxLifeOffense(FBuffObj BuffObj, int32 Ticked, TArray<FString> Params);
	
	//【】 给玩家肉鸽资源
    UFUNCTION()
    static FBuffRunResult GiveRogueCurrencyOnOccur(FBuffObj BuffObj,int WasStack, TArray<FString> Params);
	
	//【OnBeHurt】 给玩家肉鸽资源
	UFUNCTION()
	static FBuffDamageResult GiveRogueCurrencyOnHurt(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【OnBeHurt】空中或特定风切状态下受到的暴击概率提高，参数：[0]提高的倍数，0.1=+10%
	UFUNCTION()
	static FBuffDamageResult HurtCriticalChanceUpInSkyOrWind(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【OnHit】对空中或特定风切状态下的敌人造成的伤害倍数提高，参数：[0]提高的倍数，0.1=+10%
	UFUNCTION()
	static FBuffDamageResult DamageTimesUpToSkyOrWind(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);
	
	//【OnBeHurt】 根据伤害元素类型[0]进行伤害抗性 1层 = [1]%
	UFUNCTION()
	static FBuffDamageResult DamageResistanceByDamageType(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);
	
	//【OnBeHurt】 闪避时创建Aoe
	UFUNCTION()
	static FBuffDamageResult CreateAoeOnDodge(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【OnBeHurt】 【OnHit】完美闪避时创建Aoe
	UFUNCTION()
	static FBuffDamageResult CreateAoeOnJustDodgeSocket(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);	

	//【OnOcuur】 【OnAnimNotify】完美闪避动画时创建Aoe
	UFUNCTION()
	static FBuffRunResult CreateAoeOnJustDodgeSocketOnAnimNotify(FBuffObj BuffObj,int WasStack, TArray<FString> Params);	
	
	//【OnBeHurt】 【OnHit】被打创建Aoe
	UFUNCTION()
	static FBuffDamageResult CreateAoeOnBeHurtSocket(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);	

	//【OnBeHurt】 【OnHit】被打创建Aoe
	UFUNCTION()
	static FBuffDamageResult CreateAoeOnBeHurtSocketByCoolDown(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);	

	//【OnBeHurt】 【OnHit】被打在敌人处创建Aoe
	UFUNCTION()
	static FBuffDamageResult CreateAoeOnEnemySocketByCoolDown(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);	
	
	//【OnBeHurt】 受到伤害的时候根据Buff携带者信息反弹伤害
	UFUNCTION()
	static FBuffDamageResult CounterattackOnHurtByCarrierProp(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);
	
	//【OnBeHurt】 受到伤害的时候根据携带者信息以及Buff层数 反弹伤害
	UFUNCTION()
	static FBuffDamageResult CounterAttackOnHurt(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);
	
	//[OnKill] 杀敌加血道具充能
	UFUNCTION()
	static FBuffDamageResult AddItemRecoverOnKill(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,TArray<FString> Params);
	
	//[OnKill] 杀敌加血上限
	UFUNCTION()
	static FBuffDamageResult AddMaxLifeOnKill(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,TArray<FString> Params);

	//[OnOcuur] 杀敌加血读存档
	UFUNCTION()
	static FBuffRunResult AddMaxLifByKillOnOccur(FBuffObj BuffObj,int WasStack, TArray<FString> Params);
	
	//[OnKill] 杀敌加个临时Buff
	UFUNCTION()
	static FBuffDamageResult AddBuffOnKill(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,TArray<FString> Params);

	//[OnKill] 杀敌概率生成Aoe
	UFUNCTION()
	static FBuffDamageResult ChanceCreateAoeOnKill(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,TArray<FString> Params);

	//【OnKill】击杀时回复[0]生命
	UFUNCTION()
		static FBuffDamageResult HealHpOnKill(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);
	
	//【OnHit】反击命中时回复[0]生命
	UFUNCTION()
		static FBuffDamageResult HealHpOnCounter(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);
	
	//【OnHit】浑身 血量高于[0]%   伤害提高
	UFUNCTION()
	static FBuffDamageResult DamageTimesUpInNoInjury(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【OnHit】背水 血量少于[0]%   每1% *[1] 伤害提高
	UFUNCTION()
	static FBuffDamageResult DamageTimesUpInBackwater(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【OnHit】背水 血量少于[0]%   每1% *[1] 伤害提高
	UFUNCTION()
	static FBuffDamageResult DamageTimesUpInBackwaterPower(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);
	
	//【OnHit】对精英以上 伤害加成 【0】加成百分比
	UFUNCTION()
	static FBuffDamageResult EliteDamageUp(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【OnHit】对普通敌人 伤害加成 【0】加成百分比
	UFUNCTION()
	static FBuffDamageResult NormalDamageUp(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【OnHit】对被控制敌人 伤害加成 【0】加成百分比
	UFUNCTION()
	static FBuffDamageResult ControlledDamageUp(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【OnHit】对敌人 元素伤害加成 【0】加成百分比
	UFUNCTION()
	static FBuffDamageResult ElementalDamageUp(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【OnHit】对敌人 物理伤害加成 【0】加成百分比
	UFUNCTION()
	static FBuffDamageResult PhysicalDamageUp(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【OnHit】对敌人反弹伤害加成 【0】加成百分比
	UFUNCTION()
	static FBuffDamageResult ReflectDamageUp(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【OnHit】对敌人反击伤害加成 【0】加成百分比
	UFUNCTION()
	static FBuffDamageResult CounterDamageUp(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);
	
	//【OnHit】对敌人 直接伤害加成 【0】加成百分比
	UFUNCTION()
	static FBuffDamageResult DirectDamageUp(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);
	
	//【OnHit】对敌人 一级元素伤害加成 【0】加成百分比
	UFUNCTION()
	static FBuffDamageResult BasicElementalDamageUp(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【OnHit】对敌人 二级元素伤害加成 【0】加成百分比
	UFUNCTION()
	static FBuffDamageResult AdvancedElementalDamageUp(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【OnHit】对敌人造成Debuff 层数越多时间越长
	UFUNCTION()
	static FBuffDamageResult AddBuffToHitTargetStackToTime (FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);
	
	//【OnHit】对持有负面buff的敌人伤害提高
	UFUNCTION()
	static FBuffDamageResult DebuffCarrierDamageUp(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【OnHit】持有负面buff的敌人伤害提高(提高倍率为Params[0] * 1% * 目标Debuff数量)
	UFUNCTION()
	static FBuffDamageResult DamageUpByTargetDebuffNum(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【OnHit】对当前break值低于[0]%或没有break值的敌人暴击率提高[1]%
	UFUNCTION()
	static FBuffDamageResult LowBreakEnemyCriticalChanceUp(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【OnHit】对当前BreakDown的敌人伤害提高[1]%
	UFUNCTION()
	static FBuffDamageResult BreakDownEnemyDamageChanceUp(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【OnHit】对当前血量低于[0]%的敌人造成伤害提高[1]%
	UFUNCTION()
	static FBuffDamageResult DamageUpToLowHpEnemy(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);
	
	//【OnHit】每过n秒下一次造成的直接伤害提高[1]%
	UFUNCTION()
	static FBuffDamageResult TimeIntervalDamageUp(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//[OnHit] 造成伤害时 [0]概率对[2]同种元素的道具增加[1]%充能
	UFUNCTION()
	static FBuffDamageResult ChanceAddItemRecoverOnSameElementHit(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,TArray<FString> Params);

	//[OnHit] 使元素类型伤害可以产生暴击
	UFUNCTION()
	static FBuffDamageResult EnableElementalCrit(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,TArray<FString> Params);

	//[OnHit] 使特定[0]类型伤害可以产生暴击
	UFUNCTION()
	static FBuffDamageResult EnableDamageTypeCrit(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,TArray<FString> Params);
	
	//【OnCrit】造成暴击时回复[0]生命
	UFUNCTION()
		static FBuffDamageResult HealHpOnCrit(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【OnCrit】造成暴击时回复法器充能
	UFUNCTION()
		static FBuffDamageResult RecoverItemOnCrit(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);
	
	//【OnCrit】造成暴击时生成Aoe[0]
	UFUNCTION()
		static FBuffDamageResult CreateAoeOnHitTargetOnCrit(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	
	//【OnOccur】 根据buff层数设置最大复活次数
	UFUNCTION()
	static FBuffRunResult SetRevivedLifeChanceInData(FBuffObj BuffObj,int WasStack, TArray<FString> Params);
	//[OnBeKilled] 被杀时开始计时复活
	UFUNCTION()
	static FBuffDamageResult StartRevivedCarrierOnBeKilled(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);
	//[OnTick] 被杀时计时到了复活
	UFUNCTION()
	static FBuffRunResult RevivedCarrierOnBeKilled(FBuffObj BuffObj, int32 Ticked, TArray<FString> Params);
	
	//【OnRogueRoomEnd】房间结束时 恢复血量
	UFUNCTION()
	static FBuffRunResult HealOnRoomEnd(FBuffObj BuffObj,int RoomLevel,ERogueRoomType RoomType, TArray<FString> Params);

	//【OnRogueRoomEnd】房间结束时 百分比恢复AP
	UFUNCTION()
	static FBuffRunResult RecoverApPercentOnRoomEnd(FBuffObj BuffObj,int RoomLevel,ERogueRoomType RoomType, TArray<FString> Params);
	
	//【OnRogueRoomEnd】房间结束时 给予金币
	UFUNCTION()
	static FBuffRunResult GetCoinOnRoomEnd(FBuffObj BuffObj,int RoomLevel,ERogueRoomType RoomType, TArray<FString> Params);

	//[OnTick] 将圣遗物数量转换 提升的攻击力百分比
	UFUNCTION()
	static FBuffRunResult CastRelicNumToAttackPercent(FBuffObj BuffObj, int32 Ticked, TArray<FString> Params);

	//[OnTick] 将圣遗物种类Tag数量转换 提升的攻击力百分比
	UFUNCTION()
	static FBuffRunResult CastRelicGodTagToAttackPercent(FBuffObj BuffObj, int32 Ticked, TArray<FString> Params);

	//[OnTick] 将圣遗物数量转换 提升的暴击率
	UFUNCTION()
	static FBuffRunResult CastRelicNumToCriticalChance(FBuffObj BuffObj, int32 Ticked, TArray<FString> Params);

	//[OnTick] 将圣遗物种类Tag数量转换 提升的暴击率
	UFUNCTION()
	static FBuffRunResult CastRelicGodTagToCriticalChance(FBuffObj BuffObj, int32 Ticked, TArray<FString> Params);

	//[OnTick] 将圣遗物数量转换 提升的暴击倍率
	UFUNCTION()
	static FBuffRunResult CastRelicNumToCriticalRate(FBuffObj BuffObj, int32 Ticked, TArray<FString> Params);

	//[OnTick] 将圣遗物种类Tag数量转换 提升的暴击倍率
	UFUNCTION()
	static FBuffRunResult CastRelicGodTagToCriticalRate(FBuffObj BuffObj, int32 Ticked, TArray<FString> Params);

	//[OnTick] 将圣遗物数量转换 提升的最大生命
	UFUNCTION()
	static FBuffRunResult CastRelicNumToMaxHp(FBuffObj BuffObj, int32 Ticked, TArray<FString> Params);

	//[OnTick] 将圣遗物种类Tag数量转换 提升的最大生命
	UFUNCTION()
	static FBuffRunResult CastRelicGodTagToMaxHp(FBuffObj BuffObj, int32 Ticked, TArray<FString> Params);

	
	//[OnTick]  肉鸽主动道具CD时提升暴击率 []
	UFUNCTION()
	static FBuffRunResult CriticalChanceUpWhenRogueItemInCoolDown(FBuffObj BuffObj, int32 Ticked, TArray<FString> Params);

	//[OnTick]  肉鸽主动道具CD时提升暴击伤害 [0]
	UFUNCTION()
	static FBuffRunResult CriticalRateUpWhenRogueItemInCoolDown(FBuffObj BuffObj, int32 Ticked, TArray<FString> Params);
	
	//[OnTick] 将金币数量转换 提升的攻击力百分比
	UFUNCTION()
	static FBuffRunResult CastCoinNumToAttackPercent(FBuffObj BuffObj, int32 Ticked, TArray<FString> Params);


	//【OnTick】
	//在携带者身上创建Bullet
	UFUNCTION()
	static FBuffRunResult CreateBulletOnCarrierOnTick(FBuffObj BuffObj, int32 WasStack, TArray<FString> Params);
	
	//----------------------------------Rogue新手教程-------------------------------------------------------//

	//【OnHit】在命中时判断当前攻击的SourceId以及上次添加层数的时间，如果都符合，buff层数+1
	UFUNCTION()
	static FBuffDamageResult CheckNewbieActionHit(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//[OnTick] 检测当前ActionId以及上次添加层数的时间，如果都符合，buff层数+1
	UFUNCTION()
	static FBuffRunResult CheckNewbieCurActionId(FBuffObj BuffObj, int32 Ticked, TArray<FString> Params);
};
