// Fill out your copyright notice in the Description page of Project Settings.


#include "WereRatBuff.h"

#include <string>

#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"

FBuffRunResult UWereRatBuff::AddDodgeBuff(FBuffObj BuffObj, int32 Ticked, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	Res.TimelineNode = nullptr;
	if (Params.Num() < 1) return Res;

	int CheckTime = FCString::Atoi(*Params[0]);
	int DurationTime = BuffObj.Time * 10000;
	
	
	if (BuffObj.Param.Contains("StartCheckTime"))
	{
		TArray<AAwCharacter*> ChaList;
		if (BuffObj.Carrier->GetBuff("WereRat_DodgeFirst", ChaList).Num())
		{
			Res.BuffObj.Param["StartCheckTime"] = FString::FromInt(DurationTime);
			return Res;
		}

		int StartCheckTime = FCString::Atoi(*BuffObj.Param["StartCheckTime"]);
		if (DurationTime - StartCheckTime >= CheckTime)
		{
			Res.BuffObj.Param["StartCheckTime"] = FString::FromInt(DurationTime);
			//添加优先闪避BUFF
			FBuffModel BuffModel = UGameplayFuncLib::GetAwDataManager()->GetBuffModelById("WereRat_DodgeFirst");
			if (BuffModel.ValidBuffModel() == false) return Res;
			BuffObj.Carrier->AddBuff(FAddBuffInfo(BuffObj.Carrier, BuffObj.Carrier, BuffModel, 1, 20));
			
		}
	}
	else
	{
		Res.BuffObj.Param.Add(FString("StartCheckTime"), FString::FromInt(DurationTime));
	}

	return Res;
}

FBuffDamageResult UWereRatBuff::ResetCheckDodgeBuff(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.TimelineNode = nullptr;

	int DurationTime = BuffObj.Time * 10000;
	if (BuffObj.Param.Contains("StartCheckTime"))
	{
		int StartCheckTime = FCString::Atoi(*BuffObj.Param["StartCheckTime"]);
		if (DurationTime - StartCheckTime >= 30000)
		{
			BuffObj.Param["StartCheckTime"] = FString::FromInt(DurationTime);
		}
	}
	else
	{
		BuffObj.Param.Add("StartCheckTime", FString::FromInt(DurationTime));
	}

	Res.DamageInfo =DamInfo;
	
	return Res;
}

FBuffRunResult UWereRatBuff::DeleteCheeseAOE(FBuffObj BuffObj, int32 WasStack, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	float MinDis = 0;
	AAWAoe* ClosetAOE = nullptr;
	for (auto& CurAOE : UGameplayFuncLib::GetAwGameState()->AOEList)
	{
		if (CurAOE.Key && CurAOE.Key->Tags.Contains("Cheese"))
		{
			for (FBeCaughtActorInfo CurCatchedCharacter : CurAOE.Key->GetCaughtCharacters(true,true))
			{
				if (CurCatchedCharacter.BeCaughtActor == BuffObj.Carrier)
				{
					float CurDis = FVector::DistSquared(BuffObj.Carrier->GetActorLocation(), CurAOE.Key->GetActorLocation());
					if (!ClosetAOE)
					{
						ClosetAOE = CurAOE.Key;
						MinDis = CurDis;
					}
					else
					{
						if (CurDis < MinDis)
						{
							ClosetAOE = CurAOE.Key;
							MinDis = CurDis;
						}
					}
				}
			}
		}
	}
	if (ClosetAOE)
	{
		ClosetAOE->Destroy();
	}
	Res.BuffObj = BuffObj;
	return Res;
}

FBuffRunResult UWereRatBuff::GetClosetEscapePoint(FBuffObj BuffObj, int32 WasStack, TArray<FString> Params)
{
	FString LevelName;
	if (UGameplayFuncLib::GetAwGameState()->AllCharacters.Contains(BuffObj.Carrier))
		LevelName = *UGameplayFuncLib::GetAwGameState()->AllCharacters.Find(BuffObj.Carrier);

	TArray<FMapSetPoint> MapEscapePointList;
	for (FMapSetPoint CurPoint : UGameplayFuncLib::GetAwGameState()->MapPointList)
	{
		if (CurPoint.Id.Contains("EscapePoint"))
		{
			if (LevelName != "")
			{
				if (CurPoint.RoomId == LevelName)
					MapEscapePointList.Add(CurPoint);
			}
			else
				MapEscapePointList.Add(CurPoint);
		}
	}

	float MinDis = 0.0f;
	FVector TargetLoc = FVector();

	for (FMapSetPoint CurPoint : MapEscapePointList)
	{
		float CurDis = FVector::DistSquared(CurPoint.Location, BuffObj.Carrier->GetActorLocation());
		if (MinDis == 0.0f || CurDis <= MinDis)
		{
			MinDis = CurDis;
			TargetLoc = CurPoint.Location;
		}
	}

	FBuffRunResult BuffResult = FBuffRunResult();
	BuffResult.BuffObj = BuffObj;
	BuffResult.BuffObj.Param.Add("TargetLocation", TargetLoc.ToString());

	return BuffResult;
}

FBuffRunResult UWereRatBuff::RecordMaxStack(FBuffObj BuffObj, int32 WasStack, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	if(BuffObj.Param.Contains("MaxStack"))
	{
		const int WasMaxStack = FCString::Atoi(*BuffObj.Param["MaxStack"]);
		if(BuffObj.Stack > WasMaxStack)
			Res.BuffObj.Param["MaxStack"] = FString::FromInt(BuffObj.Stack);
	}
	else
	{
		Res.BuffObj.Param.Add("MaxStack", FString::FromInt(BuffObj.Stack));
	}
	return Res;
}

FBuffRunResult UWereRatBuff::RecordWereRatNum(FBuffObj BuffObj, int32 WasStack, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	int WereRatNum = 0;
	for (const TTuple<AAwCharacter*, FString> EachCharacter : UGameplayFuncLib::GetAwGameState()->AllCharacters)
	{
		if(!EachCharacter.Key) continue;
		float CheckDis = 3000;
		if(Params.Num())
			CheckDis = FCString::Atof(*Params[0]);
		if(!BuffObj.Carrier->IsEnemy(EachCharacter.Key)
			&& !EachCharacter.Key->Dead()
			&& FVector::Dist(BuffObj.Carrier->GetActorLocation(),EachCharacter.Key->GetActorLocation()) <= CheckDis)
		{
			FBuffModel BuffModel = UGameplayFuncLib::GetDataManager()->GetBuffModelById("ReduceWereRatNumWhenKill");
			if(BuffModel.Id != "")
			{
				FAddBuffInfo BuffInfo = FAddBuffInfo(BuffObj.Carrier, EachCharacter.Key, BuffModel, 1, 5, false,true);
				EachCharacter.Key->AddBuff(BuffInfo);
				WereRatNum++;
			}
		}
	}
	if(WereRatNum > 0)
	{
		FBuffModel BuffModel = UGameplayFuncLib::GetDataManager()->GetBuffModelById("RecordWereRatNum");
		if(BuffModel.Id != "")
		{
			FAddBuffInfo BuffInfo = FAddBuffInfo(BuffObj.Carrier, BuffObj.Carrier, BuffModel, WereRatNum, 5, false,true);
			BuffObj.Carrier->AddBuff(BuffInfo);
		}
	}
	return Res;
}

FBuffDamageResult UWereRatBuff::ReduceWereRatNumRecord(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	if(BuffObj.Caster)
	{
		FBuffModel BuffModel = UGameplayFuncLib::GetDataManager()->GetBuffModelById("RecordWereRatNum");
		if(BuffModel.Id != "")
		{
			FAddBuffInfo BuffInfo = FAddBuffInfo(BuffObj.Caster, BuffObj.Caster, BuffModel, -1, 5, false,true);
			BuffObj.Caster->AddBuff(BuffInfo);
		}
	}
	return Res;
}

FBuffDamageResult UWereRatBuff::AccumulateDamage(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	if(!BuffObj.Carrier->Dead(true))
	{
		{
			FBuffModel BuffModel = UGameplayFuncLib::GetDataManager()->GetBuffModelById("WereRat_AccumulateDamage");
			if(BuffModel.Id != "")
			{
				int Damage = DamInfo.FinalDamage();
				float BuffDuration = 0;
				if(BuffObj.Carrier->GetBuff("WereRat_AccumulateDamage").Num() <= 0)
				{
					BuffDuration = 15;
					if(Params.Num())
						BuffDuration = FCString::Atof(*Params[0]);
				}
				FAddBuffInfo BuffInfo = FAddBuffInfo(BuffObj.Carrier, BuffObj.Carrier, BuffModel, Damage, BuffDuration, false);
				BuffObj.Carrier->AddBuff(BuffInfo);
			}
		}
	}
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	return Res;
}

FBuffDamageResult UWereRatBuff::AddAngryBuff(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params)
{
	FBuffDamageResult BuffResult = FBuffDamageResult();
	BuffResult.BuffObj = BuffObj;
	BuffResult.DamageInfo = DamInfo;
	FBuffModel BuffModel = UGameplayFuncLib::GetAwDataManager()->GetBuffModelById("WereRat_Angry");
	if (BuffModel.ValidBuffModel() == false) return BuffResult;
	BuffObj.Carrier->AddBuff(FAddBuffInfo(BuffObj.Carrier, BuffObj.Carrier, BuffModel, 1, 5,false,true));
	return BuffResult;
}
