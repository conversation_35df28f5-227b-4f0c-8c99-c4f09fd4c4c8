// Fill out your copyright notice in the Description page of Project Settings.


#include "RogueBuff.h"

#include "BuffUtils.h"
#include "Algo/Accumulate.h"
#include "Components/SkeletalMeshComponent.h"
#include "Kismet/KismetMathLibrary.h"
#include "TheAwakener_FO/FunctionLibrary/CommonFuncLib.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GameFramework/Manager/AwRogueDataSystem.h"
#include "TheAwakener_FO/GamePlay/Battle/OffenseManager.h"
#include "TheAwakener_FO/GamePlay/Buff/BuffManager.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "TheAwakener_FO/GamePlay/Roguelike/Item/RogueInterface.h"


FBuffRunResult URogueBuff::PotionHealUp(FBuffObj BuffObj, int WasStack, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	Res.TimelineNode = nullptr;
	UAwRogueItemSubSystem* SubSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueItemSubSystem>();
	if (!SubSystem)
	{
		return Res;
	}
	float AddValue =  Params.Num()>0?FCString::Atof(*Params[0]):0  ;
	FAwRogueItemInfo Source = SubSystem->CurHealingPotion;
	Source.ModifyValue.CurEffectValue +=  (BuffObj.Stack - WasStack)*AddValue;
	SubSystem->ModifyRogueItem(Source.UID,Source,false);
	
	return  Res;
}

FBuffRunResult URogueBuff::PotionHealDown(FBuffObj BuffObj, int WasStack, TArray<FString> Params)
{	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	Res.TimelineNode = nullptr;
	UAwRogueItemSubSystem* SubSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueItemSubSystem>();
	if (!SubSystem)
	{
		return Res;
	}
	float AddValue =  Params.Num()>0?FCString::Atof(*Params[0]):0  ;
	FAwRogueItemInfo Source = SubSystem->CurHealingPotion;
	int StackOffset = Res.BuffObj.Stack;
	if (Res.BuffObj.IsBuffVaild())
	{
		StackOffset = WasStack - Res.BuffObj.Stack;
	}
	Source.ModifyValue.CurEffectValue -=  StackOffset*AddValue;
	SubSystem->ModifyRogueItem(Source.UID,Source,false);
	
	return  Res;
}

FBuffRunResult URogueBuff::PotionHealUp_Remove(FBuffObj BuffObj, bool IsDispelled, TArray<FString> Params)
{
	FBuffRunResult Res = PotionHealUp(BuffObj,BuffObj.Stack,Params);

	return Res;
}

FBuffRunResult URogueBuff::PotionHealDown_Remove(FBuffObj BuffObj, bool IsDispelled, TArray<FString> Params)
{
	FBuffRunResult Res = PotionHealDown(BuffObj,BuffObj.Stack,Params);

	return Res;
}

FBuffRunResult URogueBuff::PotionHealLevelUp(FBuffObj BuffObj, int WasStack, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	Res.TimelineNode = nullptr;
	UAwRogueItemSubSystem* SubSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueItemSubSystem>();
	if (!SubSystem)
	{
		return Res;
	}
	float AddValue =  Params.Num()>0?FCString::Atof(*Params[0]):0  ;
	FAwRogueItemInfo Source = SubSystem->CurHealingPotion;
	Source.ModifyValue.CurEffectLevel +=  (BuffObj.Stack - WasStack)*AddValue;
	SubSystem->ModifyRogueItem(Source.UID,Source,false);
	
	return  Res;
}

FBuffRunResult URogueBuff::PotionHealLevelDown(FBuffObj BuffObj, int WasStack, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	Res.TimelineNode = nullptr;
	UAwRogueItemSubSystem* SubSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueItemSubSystem>();
	if (!SubSystem)
	{
		return Res;
	}
	float AddValue =  Params.Num()>0?FCString::Atof(*Params[0]):0  ;
	FAwRogueItemInfo Source = SubSystem->CurHealingPotion;
	int StackOffset = Res.BuffObj.Stack;
	if (Res.BuffObj.IsBuffVaild())
	{
		StackOffset = WasStack - Res.BuffObj.Stack;
	}
	Source.ModifyValue.CurEffectLevel -=  StackOffset*AddValue;
	SubSystem->ModifyRogueItem(Source.UID,Source,false);
	
	return  Res;
}

FBuffRunResult URogueBuff::PotionHealLevelUp_Remove(FBuffObj BuffObj, bool IsDispelled, TArray<FString> Params)
{
	FBuffRunResult Res = PotionHealLevelUp(BuffObj,BuffObj.Stack,Params);

	return Res;
}

FBuffRunResult URogueBuff::PotionHealLevelDown_Remove(FBuffObj BuffObj, bool IsDispelled, TArray<FString> Params)
{
	FBuffRunResult Res = PotionHealLevelDown(BuffObj,BuffObj.Stack,Params);

	return Res;
}

FBuffRunResult URogueBuff::PotionHealValueDown(FBuffObj BuffObj, int WasStack, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	Res.TimelineNode = nullptr;
	UAwRogueItemSubSystem* SubSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueItemSubSystem>();
	if (!SubSystem)
	{
		return Res;
	}
	float AddValue =  Params.Num()>0?FCString::Atof(*Params[0]):0  ;
	FAwRogueItemInfo Source = SubSystem->CurHealingPotion;
	int StackOffset = Res.BuffObj.Stack;
	if (Res.BuffObj.IsBuffVaild())
	{
		StackOffset = Res.BuffObj.Stack - WasStack;
	}
	Source.ModifyValue.CurEffectValue -=  StackOffset*AddValue;
	SubSystem->ModifyRogueItem(Source.UID,Source,false);
	
	return  Res;
}

FBuffRunResult URogueBuff::PotionHealValueDown_Remove(FBuffObj BuffObj, bool IsDispelled, TArray<FString> Params)
{
	FBuffRunResult Res = PotionHealValueDown(BuffObj,BuffObj.Stack,Params);

	return Res;
}

FBuffRunResult URogueBuff::PotionNumUp(FBuffObj BuffObj, int WasStack, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	Res.TimelineNode = nullptr;
	UAwRogueItemSubSystem* SubSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueItemSubSystem>();
	if (!SubSystem)
	{
		return Res;
	}
	int AddValue =  Params.Num()>0?FCString::Atof(*Params[0]):0  ;
	FAwRogueItemInfo Source = SubSystem->CurHealingPotion;
	Source.ModifyValue.MaxEnergy =  (BuffObj.Stack - WasStack)*AddValue*Source.CostEnergy;
	if(!UAwRelicSubSystem::IsReGet)
	{
		Source.CurEnergy += Source.CostEnergy* (BuffObj.Stack - WasStack);
	}
	
	Source.CurEnergy = FMath::Clamp(Source.CurEnergy,0,Source.GetSelfAfterModify().MaxEnergy);
	SubSystem->ModifyRogueItem(Source.UID,Source,false);
	
	return  Res;
}

FBuffRunResult URogueBuff::PotionNumDown(FBuffObj BuffObj, int WasStack, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	Res.TimelineNode = nullptr;
	UAwRogueItemSubSystem* SubSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueItemSubSystem>();
	if (!SubSystem)
	{
		return Res;
	}
	int AddValue =  Params.Num()>0?FCString::Atof(*Params[0]):0  ;
	FAwRogueItemInfo Source = SubSystem->CurHealingPotion;
	int StackOffset = Res.BuffObj.Stack;
	if (Res.BuffObj.IsBuffVaild())
	{
		StackOffset = WasStack - Res.BuffObj.Stack;
	}
	Source.ModifyValue.MaxEnergy =  StackOffset*AddValue*Source.CostEnergy;

	Source.CurEnergy = FMath::Clamp(Source.CurEnergy,0,Source.GetSelfAfterModify().MaxEnergy);
	SubSystem->ModifyRogueItem(Source.UID,Source,false);
	
	return  Res;
}

FBuffRunResult URogueBuff::PotionNumUp_Remove(FBuffObj BuffObj, bool IsDispelled, TArray<FString> Params)
{
	FBuffRunResult Res = PotionNumUp(BuffObj,BuffObj.Stack,Params);

	return Res;
}

FBuffRunResult URogueBuff::PotionNumDown_Remove(FBuffObj BuffObj, bool IsDispelled, TArray<FString> Params)
{
	FBuffRunResult Res = PotionNumDown(BuffObj,BuffObj.Stack,Params);

	return Res;
}

FBuffRunResult URogueBuff::PotionNumRecover(FBuffObj BuffObj, int WasStack, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	Res.TimelineNode = nullptr;
	UAwRogueItemSubSystem* SubSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueItemSubSystem>();
	if (!SubSystem)
	{
		return Res;
	}
	FAwRogueItemInfo Source = SubSystem->CurHealingPotion;
	Source.CurEnergy = Source.GetSelfAfterModify().MaxEnergy;
	SubSystem->ModifyRogueItem(Source.UID,Source,false);
	
	return  Res;
}

FBuffRunResult URogueBuff::RogueItemRecoverUp_OnOccur(FBuffObj BuffObj, int WasStack, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	Res.TimelineNode = nullptr;
	UAwRogueItemSubSystem* SubSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueItemSubSystem>();
	if (!SubSystem)
	{
		return Res;
	}
	float AddValue =  Params.Num()>0?FCString::Atof(*Params[0]):0  ;

	FAwRogueItemInfo Source  = FAwRogueItemInfo();
	Source.ModifyPower.EnergyRecovery  = SubSystem->GlobalItemModifyPower.EnergyRecovery;
	Source.ModifyPower.EnergyRecovery += (BuffObj.Stack - WasStack)*AddValue;
	SubSystem->ModifyRogueItem(Source.UID,Source,true);
	
	return  Res;
}

FBuffRunResult URogueBuff::RogueItemRecoverUp_OnRemove(FBuffObj BuffObj, bool IsDispelled, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	Res.TimelineNode = nullptr;
	UAwRogueItemSubSystem* SubSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueItemSubSystem>();
	if (!SubSystem)
	{
		return Res;
	}
	FAwRogueItemInfo Source  = FAwRogueItemInfo();
	float AddValue =  Params.Num()>0?FCString::Atof(*Params[0]):0  ;
	
	Source.ModifyPower.EnergyRecovery  = SubSystem->GlobalItemModifyPower.EnergyRecovery;
	Source.ModifyPower.EnergyRecovery += BuffObj.Stack * AddValue;
	SubSystem->ModifyRogueItem(Source.UID,Source,true);
	
	return  Res;
}

FBuffRunResult URogueBuff::RogueItemRecoverDown_OnOccur(FBuffObj BuffObj, int WasStack, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	Res.TimelineNode = nullptr;
	UAwRogueItemSubSystem* SubSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueItemSubSystem>();
	if (!SubSystem)
	{
		return Res;
	}
	FAwRogueItemInfo Source  = FAwRogueItemInfo();
	float AddValue =  Params.Num()>0?FCString::Atof(*Params[0]):0 ;
	Source.ModifyPower.EnergyRecovery  = SubSystem->GlobalItemModifyPower.EnergyRecovery;
	
	Source.ModifyPower.EnergyRecovery -= (BuffObj.Stack - WasStack)*AddValue;
	SubSystem->ModifyRogueItem(Source.UID,Source,true);
	return  Res;
}

FBuffRunResult URogueBuff::RogueItemRecoverDown_OnRemove(FBuffObj BuffObj, bool IsDispelled, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	Res.TimelineNode = nullptr;
	UAwRogueItemSubSystem* SubSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueItemSubSystem>();
	if (!SubSystem)
	{
		return Res;
	}
	FAwRogueItemInfo Source  = FAwRogueItemInfo();

	float AddValue =  Params.Num()>0?FCString::Atof(*Params[0]):0  ;
	
	Source.ModifyPower.EnergyRecovery  = SubSystem->GlobalItemModifyPower.EnergyRecovery;
	Source.ModifyPower.EnergyRecovery -= BuffObj.Stack*AddValue;
	SubSystem->ModifyRogueItem(Source.UID,Source,true);
	
	return  Res;
}

FBuffRunResult URogueBuff::RogueItemRecoverValueUp_OnOccur(FBuffObj BuffObj, int WasStack, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	Res.TimelineNode = nullptr;
	UAwRogueItemSubSystem* SubSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueItemSubSystem>();
	if (!SubSystem)
	{
		return Res;
	}
	float AddValue =  Params.Num()>0?FCString::Atof(*Params[0]):0  ;

	FAwRogueItemInfo Source  = FAwRogueItemInfo();
	Source.ModifyValue.EnergyRecovery  = SubSystem->GlobalItemModifyValue.EnergyRecovery;
	Source.ModifyValue.EnergyRecovery += (BuffObj.Stack - WasStack)*AddValue;
	SubSystem->ModifyRogueItem(Source.UID,Source,true);
	
	return  Res;
}

FBuffRunResult URogueBuff::RogueItemRecoverValueUp_OnRemove(FBuffObj BuffObj, bool IsDispelled, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	Res.TimelineNode = nullptr;
	UAwRogueItemSubSystem* SubSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueItemSubSystem>();
	if (!SubSystem)
	{
		return Res;
	}
	FAwRogueItemInfo Source  = FAwRogueItemInfo();
	float AddValue =  Params.Num()>0?FCString::Atof(*Params[0]):0  ;
	
	Source.ModifyValue.EnergyRecovery  = SubSystem->GlobalItemModifyValue.EnergyRecovery;
	Source.ModifyValue.EnergyRecovery += BuffObj.Stack * AddValue;
	SubSystem->ModifyRogueItem(Source.UID,Source,true);
	
	return  Res;
}

FBuffRunResult URogueBuff::RogueItemRecoverValueDown_OnOccur(FBuffObj BuffObj, int WasStack, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	Res.TimelineNode = nullptr;
	UAwRogueItemSubSystem* SubSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueItemSubSystem>();
	if (!SubSystem)
	{
		return Res;
	}
	FAwRogueItemInfo Source  = FAwRogueItemInfo();
	float AddValue =  Params.Num()>0?FCString::Atof(*Params[0]):0 ;
	
	Source.ModifyValue.EnergyRecovery  = SubSystem->GlobalItemModifyValue.EnergyRecovery;
	Source.ModifyValue.EnergyRecovery -= (BuffObj.Stack - WasStack)*AddValue;
	SubSystem->ModifyRogueItem(Source.UID,Source,true);
	return  Res;
}

FBuffRunResult URogueBuff::RogueItemRecoverValueDown_OnRemove(FBuffObj BuffObj, bool IsDispelled,
	TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	Res.TimelineNode = nullptr;
	UAwRogueItemSubSystem* SubSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueItemSubSystem>();
	if (!SubSystem)
	{
		return Res;
	}
	FAwRogueItemInfo Source  = FAwRogueItemInfo();

	float AddValue =  Params.Num()>0?FCString::Atof(*Params[0]):0  ;
	
	Source.ModifyValue.EnergyRecovery  = SubSystem->GlobalItemModifyValue.EnergyRecovery;
	Source.ModifyValue.EnergyRecovery -= BuffObj.Stack*AddValue;
	SubSystem->ModifyRogueItem(Source.UID,Source,true);
	
	return  Res;
}

FBuffRunResult URogueBuff::BuffTimeCaluTargetResistance(FBuffObj BuffObj, int WasStack, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	Res.TimelineNode = nullptr;
	FString TargetId = Params.Num()>0?Params[0]:"";
	int Stack = 0;
	float Rate = 0;
	for (auto Buff:Res.BuffObj.Carrier->CharacterObj.Buff)
	{
		if (Buff.Model.Id == TargetId)
		{
			Stack += Buff.Stack;
		}
	}
	Rate = 1 - Stack*0.0001;
	Rate = FMath::Clamp(Rate,0,Rate);
	Res.BuffObj.Duration*=Rate;

	return  Res;
}

FBuffRunResult URogueBuff::BuffPropCaluTargetResistance(FBuffObj BuffObj, int WasStack, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	Res.TimelineNode = nullptr;
	FString TargetId = Params.Num()>0?Params[0]:"";
	int Stack = 0;
	float Rate = 0;
	for (auto Buff:Res.BuffObj.Carrier->CharacterObj.Buff)
	{
		if (Buff.Model.Id == TargetId)
		{
			Stack += Buff.Stack;
		}
	}
	Rate = 1 - Stack*0.0001;

	if (Res.BuffObj.Model.CharacterPropertyModify.Num()<2)
	{
		return  Res;
	}
	UAwDataManager* Manager = UGameplayFuncLib::GetAwDataManager();
	FBuffModel MetaData =  Manager->GetBuffModelById(Res.BuffObj.Model.Id);
	Res.BuffObj.Model.CharacterPropertyModify[0] =  MetaData.CharacterPropertyModify[0]*Rate;
	Res.BuffObj.Model.CharacterPropertyModify[1]= MetaData.CharacterPropertyModify[1]*Rate;
	return  Res;
	
}

FBuffRunResult URogueBuff::AddBasicElementalBuffDuration(FBuffObj BuffObj, int WasStack, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	Res.TimelineNode = nullptr;
	FString TargetId = Params.Num()>0 ? Params[0] : "";
	float AddTimePerStack = Params.Num()>1 ? FCString::Atof(*Params[1]) : 0;
	float AddTime = 0;
	if (!IsValid(BuffObj.Caster))
	{
		return Res;
	}
	for (auto Buff : BuffObj.Caster->CharacterObj.Buff)
	{
		if (Buff.Model.Id == TargetId)
		{
			AddTime += AddTimePerStack * Buff.Stack;
		}
	}
	Res.BuffObj.Duration += AddTime;

	return  Res;
}

FBuffRunResult URogueBuff::BuffChanceCastById_StackToTime(FBuffObj BuffObj, int WasStack, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	Res.TimelineNode = nullptr;

	float Chance =Params.Num()>0?FCString::Atof(*Params[0]):0;
	FString TargetId = Params.Num()>1?Params[1]:"";
	FString TargetBuffId = Params.Num()>2?Params[2]:"";
	float CastRate = Params.Num()>3?FCString::Atof(*Params[3]):0;
	int MinStack = Params.Num()>4?FCString::Atof(*Params[4]):1;
	bool CastOnce = Params.Num()>5?FCString::ToBool(*Params[5]):true;
	bool bSetDurantion = Params.Num()>6?FCString::ToBool(*Params[6]):true;
	bool bCostBuff = Params.Num()>7?Params[7].ToBool():true;
	
	if(FMath::RandRange(0.f,1.f)>Chance)
	{
		return  Res;
	}
	//目标当前层数
	int Stack = 0;
	//即将被转化移除的Buff
	FBuffObj* RemovedBuff = nullptr;
	//要转化成的目标Buff
	FBuffObj* TargetBuff = nullptr;
	
	for (auto &Buff:BuffObj.Carrier->CharacterObj.Buff)
	{
		if (Buff.Model.Id==TargetId)
		{
			Stack += Buff.Stack>0?Buff.Stack:0;
			RemovedBuff = &Buff;
		}
		else if (Buff.Model.Id == TargetBuffId)
		{
			if (CastOnce)
			{
				return  Res;
			}
			else
			{
				TargetBuff  = &Buff;
			}
		}
	}
	if (Stack <MinStack)
	{
		return  Res;
	}

	float BuffTime = Stack*CastRate;

	TArray<FString> NewParams;
	NewParams.Add(TargetBuffId);
	NewParams.Add("1");
	if (TargetBuff)
	{
		BuffTime = FMath::Max(BuffTime,TargetBuff->Duration);
	}
	NewParams.Add(FString::SanitizeFloat(BuffTime));
	NewParams.Add("false");
	NewParams.Add(bSetDurantion?"true":"false");

	if (RemovedBuff&&bCostBuff)
	{
		RemovedBuff->Stack = 0;
		UBuffManager::RemoveBuff(*RemovedBuff,false);
	}
	UBuffUtils::AddSubBuffObj(BuffObj,0,NewParams);
	
	return  Res;
}

FBuffDamageResult URogueBuff::BuffChanceCastById_StackToTimeOnHit(FBuffObj BuffObj, FDamageInfo DamInfo,
	AAwCharacter* Target, TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;
	if (!Target)
	{
		return Res;
	}
	if (DamInfo.DamageType!=EDamageType::DirectDamage&&DamInfo.DamageType!=EDamageType::SpecDamage||DamInfo.IsHeal)
	{
		return Res;
	}

	float Chance =Params.Num()>0?FCString::Atof(*Params[0]):0;
	FString TargetId = Params.Num()>1?Params[1]:"";
	FString TargetBuffId = Params.Num()>2?Params[2]:"";
	float CastRate = Params.Num()>3?FCString::Atof(*Params[3]):0;
	int MinStack = Params.Num()>4?FCString::Atof(*Params[4]):1;
	bool CastOnce = Params.Num()>5?FCString::ToBool(*Params[5]):true;
	bool bSetDurantion = Params.Num()>6?FCString::ToBool(*Params[6]):true;
	bool bCostBuff = Params.Num()>7?Params[7].ToBool():true;

	//防止无消耗下 同一目标反复触发
	FString TargetUId = FString::FromInt(Target->GetUniqueID());
        
	if (!bCostBuff&&Res.BuffObj.Param.Contains(TargetUId))
	{
		return  Res;
	}
	
	if(FMath::RandRange(0.f,1.f)>Chance)
	{
		return  Res;
	}
	//目标当前层数
	int Stack = 0;
	//即将被转化移除的Buff
	FBuffObj* RemovedBuff = nullptr;
	
	if (!Target)
	{
		return  Res;
	}
	
	for (auto &Buff:Target->CharacterObj.Buff)
	{
		if (Buff.Model.Id==TargetId)
		{
			Stack += Buff.Stack>0?Buff.Stack:0;
			RemovedBuff = &Buff;
		}
		else if (Buff.Model.Id == TargetBuffId)
		{
			if (CastOnce)
			{
				return  Res;
			}
		}
	}
	if (Stack <MinStack)
	{
		return  Res;
	}

	float BuffTime = Stack*CastRate;
	
	if (RemovedBuff&&bCostBuff)
	{
		RemovedBuff->Stack = 0;
		UBuffManager::RemoveBuff(*RemovedBuff,false);
	}

	//防止无消耗下 同一目标添加标记
	if (!bCostBuff&&Target)
	{
		Res.BuffObj.Param.Add(TargetUId,TargetUId);
	}
	
	const FBuffModel BuffModel = UGameplayFuncLib::GetAwDataManager()->GetBuffModelById(TargetBuffId);
    if (BuffModel.ValidBuffModel()&&IsValid(BuffObj.Carrier))
    	BuffObj.Carrier->AddBuff(FAddBuffInfo(BuffObj.Carrier, Target, BuffModel, 1, BuffTime, bSetDurantion, false));
    		
	return  Res;
	
}

FBuffDamageResult URogueBuff::BuffCastById_StackToHpRecoverOnHit(FBuffObj BuffObj, FDamageInfo DamInfo,
	AAwCharacter* Target, TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;

	if (DamInfo.DamageType!=EDamageType::DirectDamage&&DamInfo.DamageType!=EDamageType::SpecDamage||DamInfo.IsHeal)
	{
		return Res;
	}
	
	FString TargetBuffId = Params.Num()>0?Params[0]:"";
	float RecoverValue = Params.Num()>1?FCString::Atof(*Params[1]):0;
	float RecoverRate = Params.Num()>2?FCString::Atof(*Params[2]):1;
	int MinStack =  Params.Num()>3?FCString::Atoi(*Params[3]):1;
	bool bCostBuff = Params.Num()>4?Params[4].ToBool():true;

	//目标当前层数
	int Stack = 0;
	//即将被转化移除的Buff
	FBuffObj* RemovedBuff = nullptr;

	for (auto &Buff:Target->CharacterObj.Buff)
	{
		if (Buff.Model.Id==TargetBuffId)
		{
			Stack += Buff.Stack>0?Buff.Stack:0;
			RemovedBuff = &Buff;
		}
	}
	if (RemovedBuff&&bCostBuff)
	{
		RemovedBuff->Stack = 0;
		UBuffManager::RemoveBuff(*RemovedBuff,false);
	}
	if (Stack <MinStack)
	{
		return  Res;
	}
	float RecoverHpValue = Stack*RecoverValue+Stack*RecoverRate*BuffObj.Carrier->CharacterObj.CurProperty.HP;

	FOffenseInfo Offense = FOffenseInfo();
	Offense.AttackInfo = FAttackInfo();
	Offense.AttackInfo.DamagePower =  RecoverHpValue;
	if (Offense.AttackInfo.DamagePower.TotalDamage()<1)
	{
		Offense.AttackInfo.DamagePower = 1;
	}
	
	Offense.AttackInfo.IsHeal = true;
	Offense.AttackInfo.DamageSourceType = EAttackSource::Buff;
	Offense.AttackInfo.DamageType = EDamageType::ExtraDamage;
	Offense.SourceId = BuffObj.Model.Id;
	Offense.CanHitTimes = 0;
	Offense.Index = BuffObj.Duration;
	
	UOffenseManager::DoBuffOffense(Offense, BuffObj.Carrier,BuffObj.Carrier);
	
	return Res;
}

FBuffDamageResult URogueBuff::BuffChanceCastById_StackToStack(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;
	if (!Target)
	{
		return Res;
	}
	if ((DamInfo.DamageType!=EDamageType::DirectDamage&&DamInfo.DamageType!=EDamageType::SpecDamage)||DamInfo.IsHeal)
	{
		return Res;
	}
	float Chance =Params.Num()>0?FCString::Atof(*Params[0]):0;
	FString TargetId = Params.Num()>1?Params[1]:"";
	FString TargetBuffId = Params.Num()>2?Params[2]:"";
	float CastRate = Params.Num()>3?FCString::Atof(*Params[3]):0;
	int MinStack = Params.Num()>4?FCString::Atof(*Params[4]):1;
	float LifeTime = Params.Num()>5?FCString::Atof(*Params[5]):0;
	bool bTimeInfinity = Params.Num()>6?FCString::ToBool(*Params[6]):true;
	bool bSetDurantion = Params.Num()>7?FCString::ToBool(*Params[7]):true;
	bool CastOnce = Params.Num()>8?FCString::ToBool(*Params[8]):false;
	bool bCostBuff = Params.Num()>9?Params[9].ToBool():true;

	//防止无消耗下 同一目标反复触发
	FString TargetUId = FString::FromInt(Target->GetUniqueID());
        
	if (!bCostBuff&&Res.BuffObj.Param.Contains(TargetUId))
	{
		return  Res;
	}
	
	Chance *=BuffObj.Stack;
	if(FMath::RandRange(0.f,1.f)>Chance)
	{
		return  Res;
	}
	//目标当前层数
	int Stack = 0;
	//即将被转化移除的Buff
	FBuffObj* RemovedBuff = nullptr;
	//最小转换层数判断
	for (auto &Buff:Target->CharacterObj.Buff)
	{
		if (Buff.Model.Id==TargetId)
		{
			Stack += Buff.Stack>0?Buff.Stack:0;
			RemovedBuff = &Buff;
		}
		else if (Buff.Model.Id == TargetBuffId)
		{
			if (CastOnce)
			{
				return  Res;
			}
		}
	}
	if (Stack <MinStack)
	{
		return  Res;
	}
	if (RemovedBuff&&bCostBuff)
	{
		RemovedBuff->Stack = 0;
		UBuffManager::RemoveBuff(*RemovedBuff,false);
	}

	//防止无消耗下 同一目标添加标记
	if (!bCostBuff&&Target)
	{
		Res.BuffObj.Param.Add(TargetUId,TargetUId);
	}
	
	//转换成的目标层数
	int NewStack = Stack*CastRate;

	const FBuffModel BuffModel = UGameplayFuncLib::GetAwDataManager()->GetBuffModelById(TargetBuffId);

	if (BuffModel.ValidBuffModel()&&IsValid(BuffObj.Carrier))
		BuffObj.Carrier->AddBuff(FAddBuffInfo(BuffObj.Carrier, Target, BuffModel, NewStack, LifeTime, bSetDurantion, bTimeInfinity));
	
	return  Res;
}

FBuffRunResult URogueBuff::GiveRogueCurrencyOnOccur(FBuffObj BuffObj, int WasStack, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	Res.TimelineNode = nullptr;
	
	FString Key =Params.Num() >0? Params[0]:"" ;
	const int Num = Params.Num() >1?FCString::Atof(*Params[1]) * BuffObj.Stack:0 ;
	const UAwRogueDataSystem* SubSystem =  GWorld->GetGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	if (!SubSystem)
	{
		return Res;
	}

	const int NewNum = FMath::Clamp(Num, 0, Num);
	SubSystem->AddCurrencyCount(NewNum, Key, true);
	return  Res;
}

FBuffDamageResult URogueBuff::BuffChanceCastById_StackToAoe(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
                                                            TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;
	if (!Target)
	{
		return Res;
	}
	if ((DamInfo.DamageType!=EDamageType::DirectDamage&&DamInfo.DamageType!=EDamageType::SpecDamage)||DamInfo.IsHeal)
	{
		return Res;
	}
	float Chance =Params.Num()>0?FCString::Atof(*Params[0]):0;
	FString TargetId = Params.Num()>1?Params[1]:"";
	FString AoeId = Params.Num()>2?Params[2]:"";
	int MinStack = Params.Num()>3?FCString::Atof(*Params[3]):1;
	float LifeTime = Params.Num()>4?FCString::Atof(*Params[4]):0;
	FString TweenFuncName = Params.Num()>5?Params[5]:"";
	bool bCostBuff = Params.Num()>6?Params[6].ToBool():true;

	//防止无消耗下 同一目标反复触发
	FString TargetUId = FString::FromInt(Target->GetUniqueID());
	
	if (!bCostBuff&&Res.BuffObj.Param.Contains(TargetUId))
	{
		return  Res;
	}
	
	Chance *=BuffObj.Stack;
	if(FMath::RandRange(0.f,1.f)>Chance)
	{
		return  Res;
	}
	//目标当前层数
	int Stack = 0;
	//即将被转化移除的Buff
	FBuffObj* RemovedBuff = nullptr;
	//最小转换层数判断
	for (auto &Buff:Target->CharacterObj.Buff)
	{
		if (Buff.Model.Id==TargetId)
		{
			Stack += Buff.Stack>0?Buff.Stack:0;
			RemovedBuff = &Buff;
		}
	}
	if (Stack <MinStack)
	{
		return  Res;
	}
	if (RemovedBuff&&bCostBuff)
	{
		RemovedBuff->Stack = 0;
		UBuffManager::RemoveBuff(*RemovedBuff,false);
	}

	//防止无消耗下 同一目标添加标记
	if (!bCostBuff&&Target)
	{
		Res.BuffObj.Param.Add(TargetUId,TargetUId);
	}
	FVector Pos = IsValid(DamInfo.HitBox)?DamInfo.HitBox->GetComponentLocation()+DamInfo.HitLocationOffset:Target->GetActorLocation();
	AAWAoe* Aoe = UGameplayFuncLib::CreateAOE(BuffObj.Carrier,AoeId,Pos,FVector::ZeroVector,LifeTime,TweenFuncName);

	
	
	//接口传参
	if (Aoe&&Aoe->Implements<URogueInterface>())
	{
		TMap<FString,FString>InterfaceParams = BuffObj.Param;
		InterfaceParams.Add("Stack",FString::SanitizeFloat(Stack));
		IRogueInterface::Execute_GiveInfoMap(Aoe,InterfaceParams);
	}
	return  Res;
}

FBuffDamageResult URogueBuff::BuffChanceCastById_StackToBullet(FBuffObj BuffObj, FDamageInfo DamInfo,
	AAwCharacter* Target, TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;
	if (!Target)
	{
		return Res;
	}
	if ((DamInfo.DamageType!=EDamageType::DirectDamage&&DamInfo.DamageType!=EDamageType::SpecDamage)||DamInfo.IsHeal)
	{
		return Res;
	}
	float Chance =Params.Num()>0?FCString::Atof(*Params[0]):0;
	FString TargetId = Params.Num()>1?Params[1]:"";
	FString BulletId = Params.Num()>2?Params[2]:"";
	int MinStack = Params.Num()>3?FCString::Atof(*Params[3]):1;
	float LifeTime = Params.Num()>4?FCString::Atof(*Params[4]):0;
	FString TweenFuncName = Params.Num()>5?Params[5]:"";
	bool bCostBuff = Params.Num()>6?Params[6].ToBool():true;

	//防止无消耗下 同一目标反复触发
	FString TargetUId = FString::FromInt(Target->GetUniqueID());
        
	if (!bCostBuff&&Res.BuffObj.Param.Contains(TargetUId))
	{
		return  Res;
	}
	
	Chance *=BuffObj.Stack;
	if(FMath::RandRange(0.f,1.f)>Chance)
	{
		return  Res;
	}
	//目标当前层数
	int Stack = 0;
	//即将被转化移除的Buff
	FBuffObj* RemovedBuff = nullptr;
	//最小转换层数判断
	for (auto &Buff:Target->CharacterObj.Buff)
	{
		if (Buff.Model.Id==TargetId)
		{
			Stack += Buff.Stack>0?Buff.Stack:0;
			RemovedBuff = &Buff;
		}
	}
	if (Stack <MinStack)
	{
		return  Res;
	}
	if (RemovedBuff&&bCostBuff)
	{
		RemovedBuff->Stack = 0;
		UBuffManager::RemoveBuff(*RemovedBuff,false);
	}
	//防止无消耗下 同一目标添加标记
	if (!bCostBuff&&Target)
	{
		Res.BuffObj.Param.Add(TargetUId,TargetUId);
	}
	
	FBulletModel  BulletModel = UGameplayFuncLib::GetDataManager()->GetBulletModelById(BulletId);
	FVector Pos = IsValid(DamInfo.HitBox)?DamInfo.HitBox->GetComponentLocation()+DamInfo.HitLocationOffset:Target->GetActorLocation();
	FBulletLauncher Launcher = FBulletLauncher();
	Launcher.Model = BulletModel;
	Launcher.Position =Pos;
	Launcher.Direction =  -1*DamInfo.HitLocationOffset.GetSafeNormal();
	Launcher.TweenFunc =  UDataFuncLib::SplitFuncNameAndParams(TweenFuncName);
	Launcher.Duration = LifeTime;
	Launcher.Caster = BuffObj.Carrier;

	//Launcher.Model.Life = Stack;
	AAwBullet*  Bullet = UGameplayFuncLib::CreateBullet(Launcher);

	//接口传参
	if (Bullet&&Bullet->Implements<URogueInterface>())
	{
		TMap<FString,FString>InterfaceParams;
		InterfaceParams.Add("Stack",FString::SanitizeFloat(Stack));
		IRogueInterface::Execute_GiveInfoMap(Bullet,InterfaceParams);
	}
	return  Res;
}

FBuffDamageResult URogueBuff::BuffChanceCastById_StackToWindErosion(FBuffObj BuffObj, FDamageInfo DamInfo,
	AAwCharacter* Target, TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;
	if (!Target)
	{
		return Res;
	}
	if ((DamInfo.DamageType!=EDamageType::DirectDamage&&DamInfo.DamageType!=EDamageType::SpecDamage)||DamInfo.IsHeal)
	{
		return Res;
	}
	float Chance =Params.Num()>0?FCString::Atof(*Params[0]):0;
	FString TargetId = Params.Num()>1?Params[1]:"";
	int MinStack = Params.Num()>2?FCString::Atof(*Params[2]):1;
	float DamageRate = Params.Num()>3?FCString::Atof(*Params[3]):0;
	float DamageLifeRate = Params.Num()>4?FCString::Atof(*Params[4]):0;
	const FString VFXAoe= Params.Num() > 5 ? Params[5] : "";
	const FString VFXAoeLife= Params.Num() > 6 ? Params[6] : "0";
	bool bCostBuff = Params.Num()>7?Params[7].ToBool():true;

	//防止无消耗下 同一目标反复触发
	FString TargetUId = FString::FromInt(Target->GetUniqueID());
        
	if (!bCostBuff&&Res.BuffObj.Param.Contains(TargetUId))
	{
		return  Res;
	}
	
	if (!BuffObj.Caster)
	{
		return  Res;
	}
	Chance *=BuffObj.Stack;
	if(FMath::RandRange(0.f,1.f)>Chance)
	{
		return  Res;
	}
	//目标当前层数
	int Stack = 0;
	//即将被转化移除的Buff
	FBuffObj* RemovedBuff = nullptr;
	//最小转换层数判断
	for (auto &Buff:Target->CharacterObj.Buff)
	{
		if (Buff.Model.Id==TargetId)
		{
			Stack += Buff.Stack>0?Buff.Stack:0;
			RemovedBuff = &Buff;
		}
	}
	if (Stack <MinStack)
	{
		return  Res;
	}
	if (RemovedBuff&&bCostBuff)
	{
		RemovedBuff->Stack = 0;
		UBuffManager::RemoveBuff(*RemovedBuff,false);
	}

	//防止无消耗下 同一目标添加标记
	if (!bCostBuff&&Target)
	{
		Res.BuffObj.Param.Add(TargetUId,TargetUId);
	}
	
	FOffenseInfo Offense = FOffenseInfo();
	Offense.AttackInfo = FAttackInfo();
	Offense.AttackInfo.DamagePower =DamageRate*BuffObj.Caster->CharacterObj.CurProperty.PAttack+DamageLifeRate*BuffObj.Caster->CharacterObj.CurProperty.HP ;
	Offense.AttackInfo.DamageSourceType = EAttackSource::Buff;
	Offense.AttackInfo.DamageType = EDamageType::RogueSeniorDamage;
	Offense.AttackInfo.Elemental = EChaElemental::Wind;
	
	Offense.SourceId = BuffObj.Model.Id;
	Offense.CanHitTimes = 1;
	Offense.Index = BuffObj.Duration;
	for (int i=0;i<Stack;i++)
	{
		Offense.AttackHitBoxName.Add(DamInfo.HitBoxData->Id);
		Offense.OffensePosOffset  = i* FVector( FMath::RandRange(-10.f,10.f),FMath::RandRange(-10.f,10.f),10);
		UOffenseManager::DoBuffOffense(Offense, Target, BuffObj.Caster);
	}

	if (Params.Num()>5)
	{
		TArray<FString> NewParams;
		NewParams.Add(VFXAoe);
		NewParams.Add(	VFXAoeLife);
		Res = UBuffUtils::CreateAoeOnHitPos(Res.BuffObj,Res.DamageInfo,Target,NewParams);
	}
	
	return Res;
}

FBuffDamageResult URogueBuff::BuffChanceCastById_StackToWindErosionBuff(FBuffObj BuffObj, FDamageInfo DamInfo,
	AAwCharacter* Target, TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;
	if (!Target)
	{
		return Res;
	}
	if ((DamInfo.DamageType!=EDamageType::DirectDamage&&DamInfo.DamageType!=EDamageType::SpecDamage)||DamInfo.IsHeal)
	{
		return Res;
	}
	float Chance =Params.Num()>0?FCString::Atof(*Params[0]):0;
	FString TargetId = Params.Num()>1?Params[1]:"";
	FString TargetBuffId = Params.Num()>2?Params[2]:"";
	float CastRate = Params.Num()>3?FCString::Atof(*Params[3]):0;
	int MinStack = Params.Num()>4?FCString::Atof(*Params[4]):1;
	float LifeTime = Params.Num()>5?FCString::Atof(*Params[5]):0;
	bool bTimeInfinity = Params.Num()>6?FCString::ToBool(*Params[6]):true;
	bool bSetDurantion = Params.Num()>7?FCString::ToBool(*Params[7]):true;
	bool bCostBuff = Params.Num()>8?Params[8].ToBool():true;
	Chance *=BuffObj.Stack;
	if(FMath::RandRange(0.f,1.f)>Chance)
	{
		return  Res;
	}
	//目标当前层数
	int Stack = 0;
	//即将被转化移除的Buff
	FBuffObj* RemovedBuff = nullptr;
	//最小转换层数判断
	for (auto &Buff:Target->CharacterObj.Buff)
	{
		if (Buff.Model.Id==TargetId)
		{
			Stack += Buff.Stack>0?Buff.Stack:0;
			RemovedBuff = &Buff;
		}
	}
	if (Stack <MinStack)
	{
		return  Res;
	}
	if (RemovedBuff&&bCostBuff)
	{
		RemovedBuff->Stack = 0;
		UBuffManager::RemoveBuff(*RemovedBuff,false);
	}
	
	//转换成的目标层数
	int NewStack = Stack*CastRate;

	const FBuffModel BuffModel = UGameplayFuncLib::GetAwDataManager()->GetBuffModelById(TargetBuffId);
	if (BuffModel.ValidBuffModel()&&IsValid(BuffObj.Carrier))
		BuffObj.Carrier->AddBuff(FAddBuffInfo(BuffObj.Carrier, Target, BuffModel, NewStack, LifeTime, bSetDurantion, bTimeInfinity));
	
	if (Params.Num()>9)
	{
		TArray<FString> NewParams;
		for (int i =9;i<Params.Num();++i)
		{
			NewParams.Add(Params[i]);
		}
		Res = UBuffUtils::CreateAoeOnHitPos(Res.BuffObj,Res.DamageInfo,Target,NewParams);
	}

	return  Res;
}

FBuffDamageResult URogueBuff::CreateAoeOnHitTarget(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
	TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;
	if (!Target)
	{
		return Res;
	}
	if (!Res.BuffObj.IsBuffVaild())
	{
		return  Res;
	}
	if (DamInfo.DamageType!=EDamageType::DirectDamage&&DamInfo.DamageType!=EDamageType::SpecDamage||DamInfo.IsHeal)
	{
		return Res;
	}
	if (!BuffObj.Carrier)
	{
		return  Res;
	}
	FString AoeId = Params.Num()>0?Params[0]:"";
	float Durantion =  Params.Num()>1?FCString::Atof(*Params[1]):0;
	FString SocketName = Params.Num()>2?Params[2]:"";
	FString Tween = Params.Num()>3?Params[3]:"";
	bool Attach = Params.Num()>4?Params[4].ToBool():false;
	
	FVector Pos = FVector::ZeroVector; ;
	FRotator Rot = FRotator::ZeroRotator;
	FVector Direction = FVector::ZeroVector;
	FRotator NewRot = FRotator::ZeroRotator;
	bool bUseNewRot = false;
	
	//使用骨骼空间
	if (Target->GetMesh()&&Target->GetMesh()->DoesSocketExist(FName(SocketName)))
	{
		 Pos = Target->GetMesh()->GetSocketLocation(FName(SocketName));
		 Rot = Target->GetMesh()->GetSocketRotation(FName(SocketName));
		 FVector Forward = BuffObj.Carrier->GetActorForwardVector();
		 FVector Up =  UKismetMathLibrary::GetUpVector(Rot);
		 FVector Right =  UKismetMathLibrary::GetRightVector(Rot);
		NewRot =UKismetMathLibrary::MakeRotationFromAxes(Forward,Right,Up);
		Direction = UKismetMathLibrary::GetForwardVector(NewRot);
		bUseNewRot= true;
	}
	else
	{
		//基础空间信息
		Pos = IsValid(DamInfo.HitBox)?DamInfo.HitBox->GetComponentLocation()+DamInfo.HitLocationOffset:Target->GetActorLocation();
		Direction =Res.DamageInfo.HitBox->GetComponentLocation() -Res.DamageInfo.HitLocationOffset ;
	}
	Direction.Normalize();
	AAWAoe* Aoe =UGameplayFuncLib::CreateAOE(BuffObj.Carrier,AoeId,Pos,Direction,Durantion,Tween);
	if (bUseNewRot)
	{
		Aoe->SetActorRotation(NewRot);
	}
	if (Attach)
	{
		Aoe->AttachToActor(Target,FAttachmentTransformRules::KeepWorldTransform);
	}
	return  Res;
}

FBuffDamageResult URogueBuff::TakeExtraDamageByCasterProp(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
	TArray<FString> Params)
{
	//原本伤害信息
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	
	if (DamInfo.DamageType!=EDamageType::DirectDamage&&DamInfo.DamageType!=EDamageType::SpecDamage||DamInfo.IsHeal)
	{
		return  Res;
	}
	//产生一次额外伤害
	//伤害数值类型
	const FString DamageTypeKey = Params.Num()>0?Params[0]:"" ;
	float BaseDamage=  Params.Num()>1?FCString::Atof(*Params[1]):0  ;
	float Extra=  Params.Num()>2?FCString::Atof(*Params[2]):0  ;

	FOffenseInfo Offense = FOffenseInfo();
	Offense.AttackInfo = FAttackInfo();
	float PropDamage = Extra*(DamInfo.Attacker?DamInfo.Attacker->CharacterObj.CurProperty.PAttack:0);
	Offense.AttackInfo.DamagePower = (BaseDamage+PropDamage)* BuffObj.Stack;
	Offense.AttackInfo.IsHeal = false;
	Offense.AttackInfo.DamageSourceType = EAttackSource::Buff;
	Offense.AttackInfo.DamageType = EDamageType::ExtraDamage;
	
	EChaElemental ElementalType = UDataFuncLib::FStringToEnum<EChaElemental>(DamageTypeKey);
	Offense.AttackInfo.Elemental = ElementalType>EChaElemental::Physical?ElementalType:EChaElemental::Physical;
	
	Offense.SourceId = BuffObj.Model.Id;
	Offense.Index = BuffObj.Duration;
	UOffenseManager::DoBuffOffense(Offense,BuffObj.Carrier, BuffObj.Caster);
	return  Res;
}

FBuffDamageResult URogueBuff::TakeItemHealDown(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
	TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	if (!DamInfo.IsHeal)
	{
		return  Res;
	}
	if(DamInfo.DamageSourceType!=EAttackSource::Item)
	{
		return  Res;
	}
	float Power =  Params.Num()>0?FCString::Atof(*Params[0]):0  ;
	
	if (Res.DamageInfo.ValuePowerArea.Contains(EDamageArea::CauseDamagePower))
	{
		Res.DamageInfo.ValuePowerArea[EDamageArea::InjuredDamagePower] -= Power*BuffObj.Stack;	
		Res.DamageInfo.CheckMinDamagePowerArea();
	}
	
	return  Res;
}


FBuffRunResult URogueBuff::CostStackToOffenseSelf(FBuffObj BuffObj, int32 Ticked, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	Res.TimelineNode = nullptr;

	const int CostNum = Params.Num()>0?FCString::Atoi(*Params[0]):0;
	const int DamageValue =Params.Num()>1? FCString::Atoi(*Params[1]):0;
	const float DamageRate =Params.Num()>2? FCString::Atof(*Params[2]):0;
	const FString DamageTypeKey = Params.Num()>3?Params[3]:"Physical" ;
	
	if (BuffObj.Stack<CostNum)
	{
		return  Res;
	}
	Res.BuffObj.Stack -= CostNum;
	if (Res.BuffObj.Stack<=0)
	{
		UBuffManager::RemoveBuff(BuffObj,false);
	}
	else
	{
		Res.BuffObj.ToBeRemoved = false;
	}
	FOffenseInfo Offense = FOffenseInfo();
	Offense.AttackInfo = FAttackInfo();
	Offense.AttackInfo.DamagePower = DamageValue ;
	if (BuffObj.Caster)
	{
		Offense.AttackInfo.DamagePower = DamageValue + DamageRate*BuffObj.Caster->CharacterObj.CurProperty.PAttack;
	}
	Offense.AttackInfo.DamageSourceType = EAttackSource::Buff;
	Offense.AttackInfo.DamageType = EDamageType::RogueSeniorDamage;
	Offense.OffensePosOffset  = FVector( FMath::RandRange(-10.f,10.f),FMath::RandRange(-10.f,10.f),10);
	EChaElemental ElementalType = UDataFuncLib::FStringToEnum<EChaElemental>(DamageTypeKey);
	Offense.AttackInfo.Elemental = ElementalType>EChaElemental::Physical?ElementalType:EChaElemental::Physical;
	Offense.SourceId = BuffObj.Model.Id;
	Offense.CanHitTimes = 1;
	Offense.Index = BuffObj.Duration;

	UOffenseManager::DoBuffOffense(Offense, BuffObj.Carrier, BuffObj.Caster);
	return  Res;
}

FBuffRunResult URogueBuff::CostAllStackToMaxLifeOffense(FBuffObj BuffObj, int32 Ticked, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	Res.TimelineNode = nullptr;

	const int MinStack = Params.Num()>0?FCString::Atoi(*Params[0]):0;
	const int DamageValue =Params.Num()>1? FCString::Atoi(*Params[1]):0;
	const float DamageRate =Params.Num()>2? FCString::Atof(*Params[2]):0;
	float LifeDamageRate =Params.Num()>3? FCString::Atof(*Params[3]):0;
	float MaxLimitPower =Params.Num()>4? FCString::Atof(*Params[4]):10;
	const FString DamageTypeKey = Params.Num()>5?Params[5]:"Physical" ;
	
	int Stack = BuffObj.Stack;
	if (BuffObj.Stack<MinStack)
	{
		return  Res;
	}
	Res.BuffObj.Stack = 0;
	UBuffManager::RemoveBuff(BuffObj,false);
	
	FOffenseInfo Offense = FOffenseInfo();
	Offense.AttackInfo = FAttackInfo();
	Offense.AttackInfo.DamagePower = DamageValue ;
	if (BuffObj.Caster)
	{
		LifeDamageRate = LifeDamageRate*Stack;
		Offense.AttackInfo.DamagePower = DamageValue + DamageRate*Stack*BuffObj.Caster->CharacterObj.CurProperty.PAttack+FMath::Clamp(BuffObj.Carrier->CharacterObj.CurProperty.HP*LifeDamageRate,0,BuffObj.Caster->CharacterObj.CurProperty.PAttack*MaxLimitPower);
	}
	Offense.AttackInfo.DamageSourceType = EAttackSource::Buff;
	Offense.AttackInfo.DamageType = EDamageType::RogueSeniorDamage;
	Offense.OffensePosOffset  = FVector( FMath::RandRange(-10.f,10.f),FMath::RandRange(-10.f,10.f),10);
	EChaElemental ElementalType = UDataFuncLib::FStringToEnum<EChaElemental>(DamageTypeKey);
	Offense.AttackInfo.Elemental = ElementalType>EChaElemental::Physical?ElementalType:EChaElemental::Physical;
	Offense.SourceId = BuffObj.Model.Id;
	Offense.CanHitTimes = 1;
	Offense.Index = BuffObj.Duration;

	UOffenseManager::DoBuffOffense(Offense, BuffObj.Carrier, BuffObj.Caster);
	return  Res;
}


FBuffDamageResult URogueBuff::GiveRogueCurrencyOnHurt(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
                                                      TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;
	
	FString Key =Params.Num() >0? Params[0]:"" ;
	const int Num = Params.Num() >1?FCString::Atof(*Params[1]) * BuffObj.Stack:0 ;
	UAwRogueDataSystem* SubSystem =  GWorld->GetGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	if (!SubSystem)
	{
		return Res;
	}
	if (DamInfo.IsHeal||DamInfo.DamagePower.TotalDamage()<1)
	{
		return Res;
	}
	const int NewNum = FMath::Clamp(Num, 0, Num);
	SubSystem->AddCurrencyCount(NewNum, Key, true);
	
	return Res;
}

FBuffDamageResult URogueBuff::HurtCriticalChanceUpInSkyOrWind(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
	TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;
	if (DamInfo.IsHeal)
	{
		return Res;
	}
	const float AddRate =Params.Num()>0? FCString::Atof(*Params[0]):0;
	bool bTargetInWind = false;
	for (auto Buff :BuffObj.Carrier->CharacterObj.Buff)
	{
		if (Buff.Model.Tags.Contains("Wind"))
		{
			bTargetInWind = true;
			break;
		}
	}
	bool bWindCastSky = false;
	if (Target)
	{
		for (auto Buff :Target->CharacterObj.Buff)
		{
			if (Buff.Model.Tags.Contains("WindCastSky"))
			{
				bWindCastSky = true;
				break;
			}
		}
	}

	if (BuffObj.Carrier&&(!BuffObj.Carrier->OnGround()||(bTargetInWind&&bWindCastSky)))
	{
		Res.DamageInfo.CriticalChance += AddRate * BuffObj.Stack ;	
	}
	return  Res;
}

FBuffDamageResult URogueBuff::DamageTimesUpToSkyOrWind(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
	TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;
	if (DamInfo.IsHeal)
	{
		return Res;
	}
	const float DamageRate =Params.Num()>0? FCString::Atof(*Params[0]):0;

	bool bTargetInWind = false;
	for (auto Buff :Target->CharacterObj.Buff)
	{
		if (Buff.Model.Tags.Contains("Wind"))
		{
			bTargetInWind = true;
			break;
		}
	}
	bool bWindCastSky = false;
	if (BuffObj.Carrier)
	{
		for (auto Buff :BuffObj.Carrier->CharacterObj.Buff)
		{
			if (Buff.Model.Tags.Contains("WindCastSky"))
			{
				bWindCastSky = true;
				break;
			}
		}
	}
	if (BuffObj.Carrier&&(!BuffObj.Carrier->OnGround()||(bTargetInWind&&bWindCastSky)))
	{
		const float Times = DamageRate * BuffObj.Stack ;	
		if (Res.DamageInfo.ValuePowerArea.Contains(EDamageArea::InjuredDamagePower)) 
		{
			Res.DamageInfo.ValuePowerArea[EDamageArea::InjuredDamagePower] += Times*BuffObj.Stack;
			Res.DamageInfo.CheckMinDamagePowerArea();
		}
	}
	return  Res;
}

FBuffDamageResult URogueBuff::DamageResistanceByDamageType(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
                                                           TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;

	FString DamageTypeKey =Params.Num() >0? Params[0]:"" ;
	float CastRate =  Params.Num()>1?FCString::Atof(*Params[1]):0 ;

	if (DamInfo.IsHeal)
	{
		return Res;
	}
	EChaElemental ElementalType = UDataFuncLib::FStringToEnum<EChaElemental>(DamageTypeKey);
	if (DamInfo.Elemental == ElementalType)
	{
		 float DamageRate = Res.BuffObj.Stack*CastRate;
		if (Res.DamageInfo.ValuePowerArea.Contains(EDamageArea::InjuredDamagePower))
		{
			Res.DamageInfo.ValuePowerArea[EDamageArea::InjuredDamagePower] -=DamageRate ;
			Res.DamageInfo.CheckMinDamagePowerArea();
		}
	}
	
	return Res;
}

FBuffDamageResult URogueBuff::CreateAoeOnDodge(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
                                               TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;
	if (!Res.BuffObj.IsBuffVaild())
	{
		return  Res;
	}
	if (DamInfo.DamageType!=EDamageType::DirectDamage||DamInfo.IsHeal)
	{
		return Res;
	}
	FString AoeId = Params.Num()>0?Params[0]:"";
	float LifeTime = Params.Num()>1?FCString::Atof(*Params[1]):0;
	FString TweenFuncName = Params.Num()>2?Params[2]:"";
	FVector 	Pos = IsValid(DamInfo.HitBox)?DamInfo.HitBox->GetComponentLocation()+DamInfo.HitLocationOffset:Target->GetActorLocation();
	UGameplayFuncLib::CreateAOE(BuffObj.Carrier,AoeId,Pos,FVector::ZeroVector,LifeTime,TweenFuncName);
	return  Res;
	
}

FBuffDamageResult URogueBuff::CreateAoeOnJustDodgeSocket(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
	TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;
	if (DamInfo.DamageSourceType== EAttackSource::Buff||DamInfo.DamageSourceType== EAttackSource::AoE)
	{
		return  Res;
	}
	if (!Res.BuffObj.IsBuffVaild()||!DamInfo.BeDodged)
	{
		return  Res;
	}
	FString AoeId = Params.Num()>0?Params[0]:"";
	float Durantion =  Params.Num()>1?FCString::Atof(*Params[1]):0;
	FString SocketName = Params.Num()>2?Params[2]:"";
	FString Tween = Params.Num()>3?Params[3]:"";

	
	FVector Pos = FVector::ZeroVector; ;
	FRotator Rot = FRotator::ZeroRotator;
	FVector Direction = FVector::ZeroVector;
	FRotator NewRot = FRotator::ZeroRotator;
	bool bUseNewRot = false;
	
	//使用骨骼空间
	if (BuffObj.Carrier->GetMesh()&&BuffObj.Carrier->GetMesh()->DoesSocketExist(FName(SocketName)))
	{
		 Pos = BuffObj.Carrier->GetMesh()->GetSocketLocation(FName(SocketName));
		 Rot = BuffObj.Carrier->GetMesh()->GetSocketRotation(FName(SocketName));
		 FVector Forward = BuffObj.Carrier->GetActorForwardVector();
		 FVector Up =  UKismetMathLibrary::GetUpVector(Rot);
		 FVector Right =  UKismetMathLibrary::GetRightVector(Rot);
		NewRot = UKismetMathLibrary::MakeRotationFromAxes(Forward,Right,Up);
		Direction = UKismetMathLibrary::GetForwardVector(NewRot);
		bUseNewRot= true;
	}
	else
	{
		//基础空间信息
		Pos = BuffObj.Carrier->GetActorLocation();
		Direction =BuffObj.Carrier->GetActorForwardVector();
	}
	Direction.Normalize();
	AAWAoe* Aoe =UGameplayFuncLib::CreateAOE(BuffObj.Carrier,AoeId,Pos,Direction,Durantion,Tween);
	return Res;
}

FBuffRunResult URogueBuff::CreateAoeOnJustDodgeSocketOnAnimNotify(FBuffObj BuffObj, int WasStack,
	TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	Res.TimelineNode = nullptr;
	if (!Res.BuffObj.IsBuffVaild())
	{
		return  Res;
	}
	FString AoeId = Params.Num()>0?Params[0]:"";
	float Durantion =  Params.Num()>1?FCString::Atof(*Params[1]):0;
	FString SocketName = Params.Num()>2?Params[2]:"";
	FString Tween = Params.Num()>3?Params[3]:"";

	
	FVector Pos = FVector::ZeroVector; ;
	FRotator Rot = FRotator::ZeroRotator;
	FVector Direction = FVector::ZeroVector;
	FRotator NewRot = FRotator::ZeroRotator;
	bool bUseNewRot = false;
	
	//使用骨骼空间
	if (BuffObj.Carrier->GetMesh()&&BuffObj.Carrier->GetMesh()->DoesSocketExist(FName(SocketName)))
	{
		Pos = BuffObj.Carrier->GetMesh()->GetSocketLocation(FName(SocketName));
		Rot = BuffObj.Carrier->GetMesh()->GetSocketRotation(FName(SocketName));
		FVector Forward = BuffObj.Carrier->GetActorForwardVector();
		FVector Up =  UKismetMathLibrary::GetUpVector(Rot);
		FVector Right =  UKismetMathLibrary::GetRightVector(Rot);
		NewRot = UKismetMathLibrary::MakeRotationFromAxes(Forward,Right,Up);
		Direction = UKismetMathLibrary::GetForwardVector(NewRot);
		bUseNewRot= true;
	}
	else
	{
		//基础空间信息
		Pos = BuffObj.Carrier->GetActorLocation();
		Direction =BuffObj.Carrier->GetActorForwardVector();
	}
	Direction.Normalize();
	AAWAoe* Aoe =UGameplayFuncLib::CreateAOE(BuffObj.Carrier,AoeId,Pos,Direction,Durantion,Tween);
	if (IsValid(Aoe)&&Aoe->Implements<URogueInterface>())
	{
		TMap<FString,FString>InterfaceParams ;
		InterfaceParams = BuffObj.Param;
		InterfaceParams.Add("EffectLevel",FString::FromInt(BuffObj.Stack));
		IRogueInterface::Execute_GiveInfoMap(Aoe,InterfaceParams);
	}
	return Res;
}

FBuffDamageResult URogueBuff::CreateAoeOnBeHurtSocket(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
                                                      TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;
	if (!Res.BuffObj.IsBuffVaild())
	{
		return  Res;
	}
	if (DamInfo.DamageType==EDamageType::PeriodDamage||DamInfo.IsHeal)
	{
		return Res;
	}
	FString AoeId = Params.Num()>0?Params[0]:"";
	float Durantion =  Params.Num()>1?FCString::Atof(*Params[1]):0;
	FString SocketName = Params.Num()>2?Params[2]:"";
	FString Tween = Params.Num()>3?Params[3]:"";

	
	FVector Pos = FVector::ZeroVector; ;
	FRotator Rot = FRotator::ZeroRotator;
	FVector Direction = FVector::ZeroVector;
	FRotator NewRot = FRotator::ZeroRotator;
	bool bUseNewRot = false;
	
	//使用骨骼空间
	if (BuffObj.Carrier->GetMesh()&&BuffObj.Carrier->GetMesh()->DoesSocketExist(FName(SocketName)))
	{
		Pos = BuffObj.Carrier->GetMesh()->GetSocketLocation(FName(SocketName));
		Rot = BuffObj.Carrier->GetMesh()->GetSocketRotation(FName(SocketName));
		FVector Forward = BuffObj.Carrier->GetActorForwardVector();
		FVector Up =  UKismetMathLibrary::GetUpVector(Rot);
		FVector Right =  UKismetMathLibrary::GetRightVector(Rot);
		NewRot = UKismetMathLibrary::MakeRotationFromAxes(Forward,Right,Up);
		Direction = UKismetMathLibrary::GetForwardVector(NewRot);
		bUseNewRot= true;
	}
	else
	{
		//基础空间信息
		Pos = BuffObj.Carrier->GetActorLocation();
		Direction =BuffObj.Carrier->GetActorForwardVector();
	}
	Direction.Normalize();
	AAWAoe* Aoe =UGameplayFuncLib::CreateAOE(BuffObj.Carrier,AoeId,Pos,Direction,Durantion,Tween);
	return Res;
}

FBuffDamageResult URogueBuff::CreateAoeOnBeHurtSocketByCoolDown(FBuffObj BuffObj, FDamageInfo DamInfo,
	AAwCharacter* Target, TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;
	if (!Res.BuffObj.IsBuffVaild())
	{
		return  Res;
	}
	if (DamInfo.DamageType==EDamageType::PeriodDamage||DamInfo.IsHeal)
	{
		return Res;
	}
	FString AoeId = Params.Num()>0?Params[0]:"";
	float Durantion =  Params.Num()>1?FCString::Atof(*Params[1]):0;
	FString SocketName = Params.Num()>2?Params[2]:"";
	float CoolDown =  Params.Num()>3?FCString::Atof(*Params[3]):0;
	bool bAttach =Params.Num()>4?Params[4].Equals("True",ESearchCase::IgnoreCase):false;
	FString BindPoint  =Params.Num()>5?Params[5]:"";
	
	if (BuffObj.Param.Contains("CoolDown"))
	{
		float CurCoolDownTime = FCString::Atof(*BuffObj.Param["CoolDown"]);
		if ((BuffObj.Time-CurCoolDownTime)<CoolDown)
		{
			return Res;
		}
	}
	Res.BuffObj.Param.Add("CoolDown",FString::SanitizeFloat(BuffObj.Time));

	FVector Pos = FVector::ZeroVector; ;
	FRotator Rot = FRotator::ZeroRotator;
	FVector Direction = FVector::ZeroVector;
	FRotator NewRot = FRotator::ZeroRotator;
	bool bUseNewRot = false;
	
	//使用骨骼空间
	if (BuffObj.Carrier->GetMesh()&&BuffObj.Carrier->GetMesh()->DoesSocketExist(FName(SocketName)))
	{
		Pos = BuffObj.Carrier->GetMesh()->GetSocketLocation(FName(SocketName));
		Rot = BuffObj.Carrier->GetMesh()->GetSocketRotation(FName(SocketName));
		FVector Forward = BuffObj.Carrier->GetActorForwardVector();
		FVector Up =  UKismetMathLibrary::GetUpVector(Rot);
		FVector Right =  UKismetMathLibrary::GetRightVector(Rot);
		NewRot = UKismetMathLibrary::MakeRotationFromAxes(Forward,Right,Up);
		Direction = UKismetMathLibrary::GetForwardVector(NewRot);
		bUseNewRot= true;
	}
	else
	{
		//基础空间信息
		Pos = BuffObj.Carrier->GetActorLocation();
		Direction =BuffObj.Carrier->GetActorForwardVector();
	}
	Direction.Normalize();
	AAWAoe* Aoe =UGameplayFuncLib::CreateAOE(BuffObj.Carrier,AoeId,Pos,Direction,Durantion,"");
	if (bAttach)
	{
		Aoe->SetActorLocation(Pos);
		USceneComponent* AttachPoint = nullptr;
		if(BuffObj.Carrier->AllEquipmentBindPoints().Contains(BindPoint))
		{
			AttachPoint = *(BuffObj.Carrier->AllEquipmentBindPoints().Find(BindPoint));
		}
		if (!AttachPoint)
		{
			Aoe->AttachToActor(BuffObj.Carrier,FAttachmentTransformRules(FAttachmentTransformRules::KeepWorldTransform));
		}
		else
		{
			Aoe->AttachToComponent(AttachPoint,FAttachmentTransformRules(FAttachmentTransformRules::KeepWorldTransform));
		}
	}
	return Res;
}

FBuffDamageResult URogueBuff::CreateAoeOnEnemySocketByCoolDown(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
	TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;
	if (!Target)
	{
		return Res;
	}
	if (!Res.BuffObj.IsBuffVaild())
	{
		return  Res;
	}
	if (DamInfo.DamageType==EDamageType::PeriodDamage||DamInfo.IsHeal)
	{
		return Res;
	}
	FString AoeId = Params.Num()>0?Params[0]:"";
	float Durantion =  Params.Num()>1?FCString::Atof(*Params[1]):0;
	FString SocketName = Params.Num()>2?Params[2]:"";
	float CoolDown =  Params.Num()>3?FCString::Atof(*Params[3]):0;
	bool bAttach =Params.Num()>4?Params[4].Equals("True",ESearchCase::IgnoreCase):false;
	FString BindPoint  =Params.Num()>5?Params[5]:"";
	
	if (BuffObj.Param.Contains("CoolDown"))
	{
		float CurCoolDownTime = FCString::Atof(*BuffObj.Param["CoolDown"]);
		if ((BuffObj.Time-CurCoolDownTime)<CoolDown)
		{
			return Res;
		}
	}
	Res.BuffObj.Param.Add("CoolDown",FString::SanitizeFloat(BuffObj.Time));

	FVector Pos = FVector::ZeroVector; ;
	FRotator Rot = FRotator::ZeroRotator;
	FVector Direction = FVector::ZeroVector;
	FRotator NewRot = FRotator::ZeroRotator;
	bool bUseNewRot = false;
	
	//使用骨骼空间
	if (Target->GetMesh()&&Target->GetMesh()->DoesSocketExist(FName(SocketName)))
	{
		Pos = Target->GetMesh()->GetSocketLocation(FName(SocketName));
		Rot = Target->GetMesh()->GetSocketRotation(FName(SocketName));
		FVector Forward = Target->GetActorForwardVector();
		FVector Up =  UKismetMathLibrary::GetUpVector(Rot);
		FVector Right =  UKismetMathLibrary::GetRightVector(Rot);
		NewRot = UKismetMathLibrary::MakeRotationFromAxes(Forward,Right,Up);
		Direction = UKismetMathLibrary::GetForwardVector(NewRot);
		bUseNewRot= true;
	}
	else
	{
		//基础空间信息
		Pos = Target->GetActorLocation();
		Direction =Target->GetActorForwardVector();
	}
	Direction.Normalize();
	AAWAoe* Aoe =UGameplayFuncLib::CreateAOE(BuffObj.Carrier,AoeId,Pos,Direction,Durantion,"");

	if (Aoe&&Aoe->Implements<URogueInterface>())
	{
		TMap<FString,FString>InterfaceParams = BuffObj.Param;
		IRogueInterface::Execute_GiveInfoMap(Aoe,InterfaceParams);
	}
	if (bAttach)
	{
		Aoe->SetActorLocation(Pos);
		USceneComponent* AttachPoint = nullptr;
		if(Target->AllEquipmentBindPoints().Contains(BindPoint))
		{
			AttachPoint = *(Target->AllEquipmentBindPoints().Find(BindPoint));
		}
		if (!AttachPoint)
		{
			Aoe->AttachToActor(Target,FAttachmentTransformRules(FAttachmentTransformRules::KeepWorldTransform));
		}
		else
		{
			Aoe->AttachToComponent(AttachPoint,FAttachmentTransformRules(FAttachmentTransformRules::KeepWorldTransform));
		}
	}
	return Res;
}

FBuffDamageResult URogueBuff::CounterattackOnHurtByCarrierProp(FBuffObj BuffObj, FDamageInfo DamInfo,
                                                               AAwCharacter* Target, TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;
	if (!Res.BuffObj.IsBuffVaild()||!BuffObj.Carrier)
	{
		return  Res;
	}
	if (DamInfo.DamageType!=EDamageType::DirectDamage||DamInfo.IsHeal)
	{
		return  Res;
	}
	
	float Extra=  Params.Num()>0?FCString::Atof(*Params[0]):0  ;
	float DamageRate = Params.Num()>1?FCString::Atof(*Params[1]):0;
	const FString DamageTypeKey = Params.Num()>2?Params[2]:"Physical" ;
	
	FOffenseInfo Offense = FOffenseInfo();
	Offense.AttackInfo = FAttackInfo();
	Offense.AttackInfo.DamagePower = Extra+BuffObj.Carrier->CharacterObj.CurProperty.PAttack*DamageRate;
	Offense.AttackInfo.DamageSourceType = EAttackSource::Buff;
	Offense.AttackInfo.DamageType = EDamageType::ExtraDamage;
	EChaElemental ElementalType = UDataFuncLib::FStringToEnum<EChaElemental>(DamageTypeKey);
	Offense.AttackInfo.Elemental = ElementalType>EChaElemental::Physical?ElementalType:EChaElemental::Physical;
	
	Offense.SourceId = BuffObj.Model.Id;
	Offense.Index = BuffObj.Duration;
	UOffenseManager::DoBuffOffense(Offense,Target, BuffObj.Carrier);
	return  Res;
}

FBuffDamageResult URogueBuff::CounterAttackOnHurt(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
	TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;
	if (!Res.BuffObj.IsBuffVaild()||!BuffObj.Carrier)
	{
		return  Res;
	}
	if (DamInfo.IsHeal)
	{
		return Res;
	}
	if (DamInfo.DamageSourceType == EAttackSource::Buff||DamInfo.DamageSourceType == EAttackSource::None)
	{
		return  Res;
	}
	
	float Extra=  Params.Num()>0?FCString::Atof(*Params[0]):0  ;
	float DamageRate = Params.Num()>1?FCString::Atof(*Params[1]):0;
	const FString DamageTypeKey = Params.Num()>2?Params[2]:"Physical" ;

	DamageRate = DamageRate* BuffObj.Stack;
	
	FOffenseInfo Offense = FOffenseInfo();
	Offense.AttackInfo = FAttackInfo();
	Offense.AttackInfo.DamagePower = Extra+BuffObj.Carrier->CharacterObj.CurProperty.PAttack*DamageRate;
	Offense.AttackInfo.DamageSourceType = EAttackSource::Buff;
	Offense.AttackInfo.DamageType = EDamageType::ExtraDamage;
	EChaElemental ElementalType = UDataFuncLib::FStringToEnum<EChaElemental>(DamageTypeKey);
	Offense.AttackInfo.Elemental = ElementalType>EChaElemental::Physical?ElementalType:EChaElemental::Physical;
	
	Offense.SourceId = BuffObj.Model.Id;
	Offense.Index = BuffObj.Duration;
	UOffenseManager::DoBuffOffense(Offense,Target, BuffObj.Carrier);
	return  Res;
}

FBuffDamageResult URogueBuff::AddItemRecoverOnKill(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
                                                   TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;
	UAwRogueItemSubSystem* SubSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueItemSubSystem>();
	if (!SubSystem)
	{
		return Res;
	}
	FAwRogueItemInfo Source  = FAwRogueItemInfo();
	float AddValue =  Params.Num()>0?FCString::Atof(*Params[0]):0;
	
	if (SubSystem->MainItem.UID.IsEmpty())
	{
		return Res;
	}
	else
	{
		FAwRogueItemInfo  ItemInfo = SubSystem->GetRogueItemAfterGlobalModify(SubSystem->MainItem);
		SubSystem->MainItem.CurEnergy+= ItemInfo.CostEnergy*AddValue*BuffObj.Stack;
		SubSystem->MainItem.CurEnergy = FMath::Clamp(SubSystem->MainItem.CurEnergy,0,ItemInfo.MaxEnergy);
	}
	
	if (SubSystem->SecondItem.UID.IsEmpty())
	{
		return Res;
	}
	else
	{
		FAwRogueItemInfo  ItemInfo = SubSystem->GetRogueItemAfterGlobalModify(SubSystem->SecondItem);
		SubSystem->SecondItem.CurEnergy+= ItemInfo.CostEnergy*AddValue*BuffObj.Stack;
		SubSystem->SecondItem.CurEnergy = FMath::Clamp(SubSystem->SecondItem.CurEnergy,0,ItemInfo.MaxEnergy);
	}

	
	
	return  Res;
}

FBuffDamageResult URogueBuff::AddMaxLifeOnKill(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
                                               TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;
	
	EMobRank Rank = Params.Num() >0?UDataFuncLib::FStringToEnum<EMobRank>(Params[0]):EMobRank::Normal ;
	int AddNum =Params.Num() >1? FCString::Atoi(*Params[1]) :0;
	int Max =Params.Num() >2? FCString::Atoi(*Params[2]) :0 ;
	bool FitRank = Target->CharacterObj.MobRank>= Rank;
	if (FitRank)
	{
		while (Res.BuffObj.Model.CharacterPropertyModify.Num()<2)
		{
			Res.BuffObj.Model.CharacterPropertyModify.Add(FChaProp());
		}
		Res.BuffObj.Model.CharacterPropertyModify[0].HP +=AddNum;
		Res.BuffObj.Model.CharacterPropertyModify[0].HP = FMath::Clamp(Res.BuffObj.Model.CharacterPropertyModify[0].HP,0,Max);
		Res.BuffObj.Carrier->AttrRecheck();
	}
	
	return Res;
}

FBuffRunResult URogueBuff::AddMaxLifByKillOnOccur(FBuffObj BuffObj, int WasStack, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	Res.TimelineNode = nullptr;
	if (!UGameplayFuncLib::GetAwGameInstance())
	{
		return Res;
	}
	UAwRogueDataSystem* DataSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>();

	int AddNum =Params.Num() >0? FCString::Atoi(*Params[0]) :0;
	int Max =Params.Num() >1? FCString::Atoi(*Params[1]) :0 ;
	if (DataSystem)
	{
		int KillNum = DataSystem->GetCurBattleData().TotalKilledBossNum+ DataSystem->GetCurBattleData().TotalKilledEliteNum;
		while (Res.BuffObj.Model.CharacterPropertyModify.Num()<2)
		{
			Res.BuffObj.Model.CharacterPropertyModify.Add(FChaProp());
		}
		Res.BuffObj.Model.CharacterPropertyModify[0].HP = FMath::Clamp(AddNum*KillNum,0,Max);
		Res.BuffObj.Carrier->AttrRecheck();
	}
	
	return  Res;
}

FBuffDamageResult URogueBuff::AddBuffOnKill(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
                                            TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;
	
	const FString BuffId = Params.Num() > 0?Params[0]:"";
	const int AddStack = Params.Num() > 1?FCString::Atoi(*Params[1]):0;
	const float AddTime =Params.Num() > 2? FCString::Atof(*Params[2]):0.f;
	const bool TimeInfinity = Params.Num() > 3 ? Params[3].ToBool() : false;
	const bool SetToDuration = Params.Num() > 4 ? Params[4].ToBool() : false;
	const FBuffModel BuffModel = UGameplayFuncLib::GetAwDataManager()->GetBuffModelById(BuffId);
	if (BuffModel.ValidBuffModel()&&IsValid(BuffObj.Carrier))
		BuffObj.Carrier->AddBuff(FAddBuffInfo(BuffObj.Carrier, BuffObj.Carrier, BuffModel, AddStack, AddTime, SetToDuration, TimeInfinity));
	
	return Res;
}

FBuffDamageResult URogueBuff::ChanceCreateAoeOnKill(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
	TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;
	if (DamInfo.IsHeal)
	{
		return Res;
	}
	float Chance =Params.Num()>0?FCString::Atof(*Params[0]):0;
	FString AoeId = Params.Num()>1?Params[1]:"";
	float Durantion =  Params.Num()>2?FCString::Atof(*Params[2]):0;
	FString SocketName = Params.Num()>3?Params[3]:"";
	FString Tween = Params.Num()>4?Params[4]:"";
	bool Attach = Params.Num()>5?Params[5].ToBool():false;

	Chance *=BuffObj.Stack;
	if(FMath::RandRange(0.f,1.f)>Chance)
	{
		return  Res;
	}
	
	FVector Pos = FVector::ZeroVector; ;
	FRotator Rot = FRotator::ZeroRotator;
	FVector Direction = FVector::ZeroVector;
	FRotator NewRot = FRotator::ZeroRotator;
	bool bUseNewRot = false;
	
	//使用骨骼空间
	if (BuffObj.Carrier->GetMesh()&&BuffObj.Carrier->GetMesh()->DoesSocketExist(FName(SocketName)))
	{
		 Pos = BuffObj.Carrier->GetMesh()->GetSocketLocation(FName(SocketName));
		 Rot = BuffObj.Carrier->GetMesh()->GetSocketRotation(FName(SocketName));
		 FVector Forward = BuffObj.Carrier->GetActorForwardVector();
		 FVector Up =  UKismetMathLibrary::GetUpVector(Rot);
		 FVector Right =  UKismetMathLibrary::GetRightVector(Rot);
		NewRot = UKismetMathLibrary::MakeRotationFromAxes(Forward,Right,Up);
		Direction = UKismetMathLibrary::GetForwardVector(NewRot);
		bUseNewRot= true;
	}
	else
	{
		//基础空间信息
		if (IsValid(DamInfo.HitBox))
		{
			Pos = DamInfo.HitLocationOffset+DamInfo.HitBox->GetComponentLocation();
			Direction =DamInfo.HitLocationOffset;
		}
		else
		{
			Pos = BuffObj.Carrier->GetActorLocation();
			Direction = BuffObj.Carrier->GetActorForwardVector();
		}
	}
	Direction.Normalize();
	AAWAoe* Aoe =UGameplayFuncLib::CreateAOE(BuffObj.Carrier,AoeId,Pos,Direction,Durantion,Tween);
	if (bUseNewRot)
	{
		Aoe->SetActorRotation(NewRot);
	}
	if (Attach)
	{
		Aoe->AttachToActor(Res.BuffObj.Carrier,FAttachmentTransformRules::KeepWorldTransform);
	}
	return Res;
}

FBuffDamageResult URogueBuff::HealHpOnKill(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
	TArray<FString> Params)
{
	//原本伤害信息
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	
	//回复值
	float AddValue =  Params.Num()>0?FCString::Atof(*Params[0]):0  ;
	
	FOffenseInfo Offense = FOffenseInfo();
	Offense.AttackInfo = FAttackInfo();
	Offense.AttackInfo.DamagePower = AddValue* BuffObj.Stack;
	Offense.AttackInfo.IsHeal = true;
	Offense.AttackInfo.DamageSourceType = EAttackSource::Buff;
	Offense.AttackInfo.DamageType = EDamageType::ExtraDamage;
	Offense.SourceId = BuffObj.Model.Id;
	Offense.CanHitTimes = 0;
	Offense.Index = BuffObj.Duration;
	UOffenseManager::DoBuffOffense(Offense, BuffObj.Carrier,BuffObj.Carrier);
	return Res;
}

FBuffDamageResult URogueBuff::HealHpOnCounter(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
	TArray<FString> Params)
{
	//原本伤害信息
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	
	//回复值
	if (DamInfo.DamageSourceType != EAttackSource::CounterAction)
	{
		return Res;
	}
	float AddValue =  Params.Num()>0?FCString::Atof(*Params[0]):0  ;
	
	FOffenseInfo Offense = FOffenseInfo();
	Offense.AttackInfo = FAttackInfo();
	Offense.AttackInfo.DamagePower = AddValue* BuffObj.Stack;
	Offense.AttackInfo.IsHeal = true;
	Offense.AttackInfo.DamageSourceType = EAttackSource::Buff;
	Offense.AttackInfo.DamageType = EDamageType::ExtraDamage;
	Offense.SourceId = BuffObj.Model.Id;
	Offense.CanHitTimes = 0;
	Offense.Index = BuffObj.Duration;
	UOffenseManager::DoBuffOffense(Offense, BuffObj.Carrier,BuffObj.Carrier);
	return Res;
}

FBuffDamageResult URogueBuff::DamageTimesUpInNoInjury(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
                                                      TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;
	if (DamInfo.IsHeal)
	{
		return Res;
	}
	float Percent = Params.Num()>0?FCString::Atof(*Params[0]):0;
	float Power = Params.Num()>1?FCString::Atof(*Params[1]):0;	
	if (BuffObj.Carrier&&BuffObj.Carrier->HealthPercentage()>=Percent)
	{
		if (Res.DamageInfo.ValuePowerArea.Contains(EDamageArea::CauseDamagePower))
		{
			Res.DamageInfo.ValuePowerArea[EDamageArea::CauseDamagePower] += Power*BuffObj.Stack;
			Res.DamageInfo.CheckMinDamagePowerArea();
		}
	}
	return  Res;
}

FBuffDamageResult URogueBuff::DamageTimesUpInBackwater(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
	TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;
	if (DamInfo.IsHeal)
	{
		return Res;
	}
	float Percent = Params.Num()>0?FCString::Atof(*Params[0]):0;
	float Power = Params.Num()>1?FCString::Atof(*Params[1]):0;	
	if (BuffObj.Carrier&&BuffObj.Carrier->HealthPercentage()<=Percent)
	{
		//int Level = (Percent- BuffObj.Carrier->HealthPercentage())*100;
		int Level = 1;
		if (Res.DamageInfo.ValuePowerArea.Contains(EDamageArea::CauseDamagePower))
		{
			Res.DamageInfo.ValuePowerArea[EDamageArea::CauseDamagePower] += Power*BuffObj.Stack*Level;
			Res.DamageInfo.CheckMinDamagePowerArea();
		}
	}
	return  Res;
}

FBuffDamageResult URogueBuff::DamageTimesUpInBackwaterPower(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
	TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;
	if (DamInfo.IsHeal)
	{
		return Res;
	}
	float Percent = Params.Num()>0?FCString::Atof(*Params[0]):0;
	float Power = Params.Num()>1?FCString::Atof(*Params[1]):0;	
	if (BuffObj.Carrier&&BuffObj.Carrier->HealthPercentage()<=Percent)
	{
		int Level = (Percent- BuffObj.Carrier->HealthPercentage())*100;
		//int Level = 1;
		if (Res.DamageInfo.ValuePowerArea.Contains(EDamageArea::CauseDamagePower))
		{
			Res.DamageInfo.ValuePowerArea[EDamageArea::CauseDamagePower] += Power*BuffObj.Stack*Level;
			Res.DamageInfo.CheckMinDamagePowerArea();
		}
	}
	return  Res;
}

FBuffDamageResult URogueBuff::EliteDamageUp(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
                                            TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;
	if (DamInfo.IsHeal)
	{
		return Res;
	}
	float Power = Params.Num()>0?FCString::Atof(*Params[0]):0;
	if (BuffObj.Carrier&&Target->CharacterObj.MobRank>=EMobRank::Elite)
	{
		if (Res.DamageInfo.ValuePowerArea.Contains(EDamageArea::CauseDamagePower))
		{
			Res.DamageInfo.ValuePowerArea[EDamageArea::CauseDamagePower] += Power*BuffObj.Stack;	
			Res.DamageInfo.CheckMinDamagePowerArea();
		}
	}
	return  Res;
}

FBuffDamageResult URogueBuff::NormalDamageUp(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
	TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;
	if (DamInfo.IsHeal)
	{
		return Res;
	}
	float Power = Params.Num()>0?FCString::Atof(*Params[0]):0;
	if (BuffObj.Carrier&&Target->CharacterObj.MobRank==EMobRank::Normal)
	{
		if (Res.DamageInfo.ValuePowerArea.Contains(EDamageArea::CauseDamagePower))
		{
			Res.DamageInfo.ValuePowerArea[EDamageArea::CauseDamagePower] += Power*BuffObj.Stack;	
			Res.DamageInfo.CheckMinDamagePowerArea();
		}
	}
	return  Res;
}

FBuffDamageResult URogueBuff::ControlledDamageUp(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
                                                 TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;
	if (DamInfo.IsHeal)
	{
		return Res;
	}
	float Power = Params.Num()>0?FCString::Atof(*Params[0]):0;
	bool InControlled = Target->ControlState.CanMove >= EControlStateType::OutOfControl;
	if (BuffObj.Carrier&& InControlled)
	{
		if (Res.DamageInfo.ValuePowerArea.Contains(EDamageArea::CauseDamagePower))
		{
			Res.DamageInfo.ValuePowerArea[EDamageArea::CauseDamagePower] += Power*BuffObj.Stack;	
			Res.DamageInfo.CheckMinDamagePowerArea();
		}
	}
	return  Res;
}

FBuffDamageResult URogueBuff::ElementalDamageUp(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
	TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;
	if (DamInfo.IsHeal)
	{
		return Res;
	}
	float Power = Params.Num()>0?FCString::Atof(*Params[0]):0;

	if (BuffObj.Carrier&& DamInfo.Elemental!=EChaElemental::Physical)
	{
		if (Res.DamageInfo.ValuePowerArea.Contains(EDamageArea::ElementalDamagePower))
		{
			Res.DamageInfo.ValuePowerArea[EDamageArea::ElementalDamagePower] += Power*BuffObj.Stack;	
			Res.DamageInfo.CheckMinDamagePowerArea();
		}
	}
	return  Res;
}

FBuffDamageResult URogueBuff::PhysicalDamageUp(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
	TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;
	if (DamInfo.IsHeal)
	{
		return Res;
	}
	float Power = Params.Num()>0?FCString::Atof(*Params[0]):0;

	if (BuffObj.Carrier&& DamInfo.Elemental == EChaElemental::Physical)
	{
		if (Res.DamageInfo.ValuePowerArea.Contains(EDamageArea::PhysicalDamagePower))
		{
			Res.DamageInfo.ValuePowerArea[EDamageArea::PhysicalDamagePower] += Power*BuffObj.Stack;	
			Res.DamageInfo.CheckMinDamagePowerArea();
		}
	}
	return  Res;
}

FBuffDamageResult URogueBuff::ReflectDamageUp(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
	TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;
	if (DamInfo.IsHeal)
	{
		return Res;
	}
	float Power = Params.Num()>0?FCString::Atof(*Params[0]):0;

	if (BuffObj.Carrier&& DamInfo.DamageType == EDamageType::ReflectDamage)
	{
		if (Res.DamageInfo.ValuePowerArea.Contains(EDamageArea::CauseDamagePower))
		{
			Res.DamageInfo.ValuePowerArea[EDamageArea::CauseDamagePower] += Power*BuffObj.Stack;	
			Res.DamageInfo.CheckMinDamagePowerArea();
		}
	}
	return  Res;
}

FBuffDamageResult URogueBuff::CounterDamageUp(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
	TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;
	if (DamInfo.IsHeal)
	{
		return Res;
	}
	float Power = Params.Num()>0?FCString::Atof(*Params[0]):0;

	if (BuffObj.Carrier&& DamInfo.DamageSourceType == EAttackSource::CounterAction)
	{
		if (Res.DamageInfo.ValuePowerArea.Contains(EDamageArea::CauseDamagePower))
		{
			Res.DamageInfo.ValuePowerArea[EDamageArea::CauseDamagePower] += Power*BuffObj.Stack;	
			Res.DamageInfo.CheckMinDamagePowerArea();
		}
	}
	return  Res;
}

FBuffDamageResult URogueBuff::DirectDamageUp(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
                                             TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;
	if (DamInfo.IsHeal)
	{
		return Res;
	}
	float Power = Params.Num()>0?FCString::Atof(*Params[0]):0;

	if (BuffObj.Carrier&& DamInfo.DamageType==EDamageType::DirectDamage)
	{
		if (Res.DamageInfo.ValuePowerArea.Contains(EDamageArea::CauseDamagePower))
		{
			Res.DamageInfo.ValuePowerArea[EDamageArea::CauseDamagePower] += Power*BuffObj.Stack;	
			Res.DamageInfo.CheckMinDamagePowerArea();
		}
	}
	return  Res;
}

FBuffDamageResult URogueBuff::BasicElementalDamageUp(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
                                                     TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;
	if (DamInfo.IsHeal)
	{
		return Res;
	}
	float Power = Params.Num()>0?FCString::Atof(*Params[0]):0;

	if (BuffObj.Carrier&& DamInfo.Elemental!=EChaElemental::Physical)
	{
		if (Res.DamageInfo.ValuePowerArea.Contains(EDamageArea::ElementalDamagePower) && Res.DamageInfo.DamageType != EDamageType::RogueSeniorDamage)
		{
			Res.DamageInfo.ValuePowerArea[EDamageArea::ElementalDamagePower] += Power*BuffObj.Stack;	
			Res.DamageInfo.CheckMinDamagePowerArea();
		}
	}
	return  Res;
}

FBuffDamageResult URogueBuff::AdvancedElementalDamageUp(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
	TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;
	if (DamInfo.IsHeal)
	{
		return Res;
	}
	float Power = Params.Num()>0?FCString::Atof(*Params[0]):0;

	if (BuffObj.Carrier&& DamInfo.Elemental!=EChaElemental::Physical)
	{
		if (Res.DamageInfo.ValuePowerArea.Contains(EDamageArea::ElementalDamagePower) && Res.DamageInfo.DamageType == EDamageType::RogueSeniorDamage)
		{
			Res.DamageInfo.ValuePowerArea[EDamageArea::ElementalDamagePower] += Power*BuffObj.Stack;	
			Res.DamageInfo.CheckMinDamagePowerArea();
		}
	}
	return  Res;
}

FBuffDamageResult URogueBuff::AddBuffToHitTargetStackToTime(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
	TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;

	if ((DamInfo.DamageType!=EDamageType::DirectDamage&&DamInfo.DamageType!=EDamageType::SpecDamage)|| DamInfo.IsHeal
		|| DamInfo.BeDodged
		)
	{
		return Res;
	}
	if(Target->GetHitBoxInJustDefense(DamInfo.HitBox->GetName()))
		return Res;
	FString BuffId =  Params.Num()>0?Params[0]:"";
	int BuffStack = Params.Num()>1?FCString::Atof(*Params[1]) :1;
	float BuffTime = Params.Num()>2?FCString::Atof(*Params[2]):0;	
	bool SetToDurantion = Params.Num()>3?Params[3].Equals("true",ESearchCase::IgnoreCase):false; 
	bool bInfinity = Params.Num()>4?Params[4].Equals("true",ESearchCase::IgnoreCase):false;
	
	FBuffModel BuffModel = UGameplayFuncLib::GetAwDataManager()->GetBuffModelById(BuffId);
	
	Target->AddBuff(FAddBuffInfo(Res.BuffObj.Carrier, Target, BuffModel, BuffStack, BuffTime*BuffObj.Stack, SetToDurantion ,bInfinity ));
	return Res;
}

FBuffDamageResult URogueBuff::DebuffCarrierDamageUp(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
                                                    TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;
	if (DamInfo.IsHeal)
	{
		return Res;
	}
	float Power = Params.Num()>0?FCString::Atof(*Params[0]):0;

	bool bHasDeBuff = false;

	for (auto buff:Target->CharacterObj.Buff)
	{
		if (buff.Model.Tags.Contains("Rogue_Harm"))
		{
			bHasDeBuff = true;
			break;
		}
	}
	
	if (BuffObj.Carrier&& bHasDeBuff)
	{
		if (Res.DamageInfo.ValuePowerArea.Contains(EDamageArea::CauseDamagePower))
		{
			Res.DamageInfo.ValuePowerArea[EDamageArea::CauseDamagePower] += Power*BuffObj.Stack;	
			Res.DamageInfo.CheckMinDamagePowerArea();
		}
	}
	return  Res;
}

FBuffDamageResult URogueBuff::DamageUpByTargetDebuffNum(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
	TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;
	if (DamInfo.IsHeal)
	{
		return Res;
	}
	float Power = Params.Num()>0?FCString::Atof(*Params[0]):0;

	int DeBuffNum = 0;

	for (auto buff:Target->CharacterObj.Buff)
	{
		if (buff.Model.Tags.Contains("Rogue_Harm"))
		{
			DeBuffNum++;
		}
	}
	
	if (BuffObj.Carrier&& DeBuffNum > 0)
	{
		if (Res.DamageInfo.ValuePowerArea.Contains(EDamageArea::CauseDamagePower))
		{
			Res.DamageInfo.ValuePowerArea[EDamageArea::CauseDamagePower] += Power * BuffObj.Stack * DeBuffNum;
			Res.DamageInfo.CheckMinDamagePowerArea();
		}
	}
	return  Res;
}

FBuffDamageResult URogueBuff::LowBreakEnemyCriticalChanceUp(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
                                                            TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;

	int BreakNum = Target->GetBreakSystemComponent()->CurrentBreakValue;
	//防止除0
	int MaxBreakNum = 1;
	if(Target->GetBreakSystemComponent()->IsActive())
		MaxBreakNum = Target->GetBreakSystemComponent()->MaxBreakValue;
	
	float LimitPercent = Params.Num()>0?FCString::Atof(*Params[0]):0;
	float  AddPercent = Params.Num()>1?FCString::Atof(*Params[1]):0;

	float CurPercent = (float)BreakNum/MaxBreakNum;
	if (CurPercent<= LimitPercent)
	{
		Res.DamageInfo.CriticalChance +=AddPercent*BuffObj.Stack;
	}
	return  Res;
}

FBuffDamageResult URogueBuff::BreakDownEnemyDamageChanceUp(FBuffObj BuffObj, FDamageInfo DamInfo,
	AAwCharacter* Target, TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	if (!Target)
	{
		UE_LOG(LogTemp,Log,TEXT("ECS Buff have no Target"));
		return Res;
	}
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;
	
	float AddPercent = Params.Num()>0?FCString::Atof(*Params[0]):0;
	
	if (Target->GetBreakSystemComponent()->bInBreak)
	{
		if (Res.DamageInfo.ValuePowerArea.Contains(EDamageArea::InjuredDamagePower)) 
		{
			Res.DamageInfo.ValuePowerArea[EDamageArea::InjuredDamagePower] += AddPercent*BuffObj.Stack;
			//Res.DamageInfo.CheckMinDamagePowerArea();
		}
	}
	return  Res;
}

FBuffDamageResult URogueBuff::DamageUpToLowHpEnemy(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
                                                   TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;

	if (DamInfo.IsHeal)
	{
		return Res;
	}

	float LimitPercent = Params.Num()>0?FCString::Atof(*Params[0]):0;
	float  AddPercent = Params.Num()>1?FCString::Atof(*Params[1]):0;

	float CurPercent = Target->HealthPercentage();
	if (CurPercent<= LimitPercent)
	{
		if (Res.DamageInfo.ValuePowerArea.Contains(EDamageArea::CauseDamagePower))
		{
			Res.DamageInfo.ValuePowerArea[EDamageArea::CauseDamagePower] += AddPercent*BuffObj.Stack;	
			Res.DamageInfo.CheckMinDamagePowerArea();
		}
	}
	return  Res;
}

FBuffDamageResult URogueBuff::TimeIntervalDamageUp(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
                                                   TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;
	
	float TimeInterval = Params.Num()>0?FCString::Atof(*Params[0]):0;
	float  AddPercent = Params.Num()>1?FCString::Atof(*Params[1]):0;

	if (DamInfo.IsHeal)
	{
		return Res;
	}
	
	if (Res.BuffObj.Time<TimeInterval)
	{
		return  Res;
	}
	if (DamInfo.DamageType!=EDamageType::DirectDamage)
	{
		return  Res;
	}
	
	if (Res.DamageInfo.ValuePowerArea.Contains(EDamageArea::CauseDamagePower))
	{
		Res.DamageInfo.ValuePowerArea[EDamageArea::CauseDamagePower] += AddPercent*BuffObj.Stack;	
		Res.DamageInfo.CheckMinDamagePowerArea();
	}

	Res.BuffObj.Time = 0;
	return  Res;
}

FBuffDamageResult URogueBuff::ChanceAddItemRecoverOnSameElementHit(FBuffObj BuffObj, FDamageInfo DamInfo,
	AAwCharacter* Target, TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;
	UAwRogueItemSubSystem* SubSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueItemSubSystem>();
	if (!SubSystem)
	{
		return Res;
	}
	FAwRogueItemInfo Source  = FAwRogueItemInfo();
	float Chance =Params.Num()>0?FCString::Atof(*Params[0]):0;
	float AddValue =  Params.Num()>1?FCString::Atof(*Params[1]):0;
	FString TargetElement = Params.Num()>2?Params[2]:"";
	if (TargetElement.IsEmpty())
	{
		return Res;
	}
	Chance *=BuffObj.Stack;
	if(FMath::RandRange(0.f,1.f)>Chance)
	{
		return  Res;
	}
	if (DamInfo.Elemental!=UDataFuncLib::FStringToEnum<EChaElemental>(TargetElement))
	{
		return Res;
	}

	if (SubSystem->MainItem.UID.IsEmpty())
	{
		return Res;
	}
	else
	{
		FAwRogueItemInfo  ItemInfo = SubSystem->GetRogueItemAfterGlobalModify(SubSystem->MainItem);
		SubSystem->MainItem.CurEnergy+= ItemInfo.CostEnergy*AddValue;
		SubSystem->MainItem.CurEnergy = FMath::Clamp(SubSystem->MainItem.CurEnergy,0,ItemInfo.MaxEnergy);
	}
	
	if (SubSystem->SecondItem.UID.IsEmpty())
	{
		return Res;
	}
	else
	{
		FAwRogueItemInfo  ItemInfo = SubSystem->GetRogueItemAfterGlobalModify(SubSystem->SecondItem);
		SubSystem->SecondItem.CurEnergy+= ItemInfo.CostEnergy*AddValue;
		SubSystem->SecondItem.CurEnergy = FMath::Clamp(SubSystem->SecondItem.CurEnergy,0,ItemInfo.MaxEnergy);
	}
	
	return  Res;
}

FBuffDamageResult URogueBuff::EnableElementalCrit(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
	TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;

	for (auto Param:Params)
	{
		Res.DamageInfo.CanCriticElemental.AddUnique(UDataFuncLib::FStringToEnum<EChaElemental>(Param));
	}
	
	return  Res;
}

FBuffDamageResult URogueBuff::EnableDamageTypeCrit(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
	TArray<FString> Params)
{

	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	
	for (auto Param:Params)
	{
		Res.DamageInfo.CanCriticTypes.AddUnique(UDataFuncLib::FStringToEnum<EDamageType>(Param));
	}
	
	return  Res;
	
}

FBuffDamageResult URogueBuff::HealHpOnCrit(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
                                           TArray<FString> Params)
{
	//原本伤害信息
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	
	if (DamInfo.DamageType!=EDamageType::DirectDamage&&DamInfo.DamageType!=EDamageType::SpecDamage)
	{
		return  Res;
	}
	
	//回复值
	float AddValue =  Params.Num()>0?FCString::Atof(*Params[0]):0  ;
	
	FOffenseInfo Offense = FOffenseInfo();
	Offense.AttackInfo = FAttackInfo();
	Offense.AttackInfo.DamagePower = AddValue* BuffObj.Stack;
	Offense.AttackInfo.IsHeal = true;
	Offense.AttackInfo.DamageSourceType = EAttackSource::Buff;
	Offense.AttackInfo.DamageType = EDamageType::ExtraDamage;
	Offense.SourceId = BuffObj.Model.Id;
	Offense.CanHitTimes = 0;
	Offense.Index = BuffObj.Duration;
	UOffenseManager::DoBuffOffense(Offense, BuffObj.Carrier,BuffObj.Carrier);
	return Res;
}

FBuffDamageResult URogueBuff::RecoverItemOnCrit(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
	TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;
	UAwRogueItemSubSystem* SubSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueItemSubSystem>();
	if (!SubSystem)
	{
		return Res;
	}
	FAwRogueItemInfo Source  = FAwRogueItemInfo();
	float AddValue =  Params.Num()>0?FCString::Atof(*Params[0]):0;
	
	if (SubSystem->MainItem.UID.IsEmpty())
	{
		return Res;
	}
	else
	{
		FAwRogueItemInfo  ItemInfo = SubSystem->GetRogueItemAfterGlobalModify(SubSystem->MainItem);
		SubSystem->MainItem.CurEnergy+= ItemInfo.CostEnergy*AddValue*BuffObj.Stack;
		SubSystem->MainItem.CurEnergy = FMath::Clamp(SubSystem->MainItem.CurEnergy,0,ItemInfo.MaxEnergy);
	}
	
	if (SubSystem->SecondItem.UID.IsEmpty())
	{
		return Res;
	}
	else
	{
		FAwRogueItemInfo  ItemInfo = SubSystem->GetRogueItemAfterGlobalModify(SubSystem->SecondItem);
		SubSystem->SecondItem.CurEnergy+= ItemInfo.CostEnergy*AddValue*BuffObj.Stack;
		SubSystem->SecondItem.CurEnergy = FMath::Clamp(SubSystem->SecondItem.CurEnergy,0,ItemInfo.MaxEnergy);
	}

	return Res;
}

FBuffDamageResult URogueBuff::CreateAoeOnHitTargetOnCrit(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
                                                         TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;
	if (!Target)
	{
		return Res;
	}
	if (!Res.BuffObj.IsBuffVaild())
	{
		return  Res;
	}
	if (DamInfo.IsHeal||DamInfo.DamageType ==EDamageType::RogueSeniorDamage)
	{
		return Res;
	}
	if (!BuffObj.Carrier)
	{
		return  Res;
	}
	FString AoeId = Params.Num()>0?Params[0]:"";
	float Durantion =  Params.Num()>1?FCString::Atof(*Params[1]):0;
	FString SocketName = Params.Num()>2?Params[2]:"";
	FString Tween = Params.Num()>3?Params[3]:"";
	bool Attach = Params.Num()>4?Params[4].ToBool():false;
	
	FVector Pos = FVector::ZeroVector; ;
	FRotator Rot = FRotator::ZeroRotator;
	FVector Direction = FVector::ZeroVector;
	FRotator NewRot = FRotator::ZeroRotator;
	bool bUseNewRot = false;
	
	//使用骨骼空间
	if (Target->GetMesh()&&Target->GetMesh()->DoesSocketExist(FName(SocketName)))
	{
		Pos = Target->GetMesh()->GetSocketLocation(FName(SocketName));
		Rot = Target->GetMesh()->GetSocketRotation(FName(SocketName));
		FVector Forward = BuffObj.Carrier->GetActorForwardVector();
		FVector Up =  UKismetMathLibrary::GetUpVector(Rot);
		FVector Right =  UKismetMathLibrary::GetRightVector(Rot);
		NewRot =UKismetMathLibrary::MakeRotationFromAxes(Forward,Right,Up);
		Direction = UKismetMathLibrary::GetForwardVector(NewRot);
		bUseNewRot= true;
	}
	else
	{
		//基础空间信息
		Pos =  IsValid(Res.DamageInfo.HitBox)? Res.DamageInfo.HitLocationOffset+Res.DamageInfo.HitBox->GetComponentLocation():Target->GetActorLocation();
		Direction =IsValid(Res.DamageInfo.HitBox)? Res.DamageInfo.HitBox->GetComponentLocation() -Res.DamageInfo.HitLocationOffset:Target->GetActorLocation() ;
	}
	Direction.Normalize();
	AAWAoe* Aoe =UGameplayFuncLib::CreateAOE(BuffObj.Carrier,AoeId,Pos,Direction,Durantion,Tween);
	if (bUseNewRot)
	{
		Aoe->SetActorRotation(NewRot);
	}
	if (Attach)
	{
		Aoe->AttachToActor(Target,FAttachmentTransformRules::KeepWorldTransform);
	}
	if (IsValid(Aoe)&&Aoe->Implements<URogueInterface>())
	{
		TMap<FString,FString>InterfaceParams ;
		InterfaceParams = BuffObj.Param;
		InterfaceParams.Add("EffectLevel",FString::FromInt(BuffObj.Stack));
		IRogueInterface::Execute_GiveInfoMap(Aoe,InterfaceParams);
	}
	return  Res;
	
}

FBuffRunResult URogueBuff::SetRevivedLifeChanceInData(FBuffObj BuffObj, int WasStack, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	Res.TimelineNode = nullptr;
	UAwRogueDataSystem* SubSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	if (SubSystem)
	{
		SubSystem->SetRevivedChance_CurBattle(Res.BuffObj.Stack);
	}

	return  Res;
}

FBuffDamageResult URogueBuff::StartRevivedCarrierOnBeKilled(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
                                                            TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;
	float DelayTime = Params.Num()>0?FCString::Atof(*Params[0]):0;
	
	UAwRogueDataSystem* SubSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	if (!SubSystem)
	{
		return  Res;
	}
	
	int RevivedChance  = SubSystem->GetRevivedChance_CurBattle();
	int RevivedChanceHasUse = SubSystem->GetRevivedChanceHasUse_CurBattle();
	if (RevivedChance-RevivedChanceHasUse<1)
	{
		return  Res;		
	}

	
	Res.BuffObj.Model.TickTime = DelayTime;
	Res.BuffObj.Time = 0;
	
	return  Res;
}

FBuffRunResult URogueBuff::RevivedCarrierOnBeKilled(FBuffObj BuffObj, int32 Ticked, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	Res.TimelineNode = nullptr;

	UAwRogueDataSystem* SubSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>();

	if (!SubSystem)
	{
		return  Res;
	}
	int RevivedChance  = SubSystem->GetRevivedChance_CurBattle();
	int RevivedChanceHasUse = SubSystem->GetRevivedChanceHasUse_CurBattle();
	if (BuffObj.Carrier&&Res.BuffObj.Stack>0&&RevivedChance>RevivedChanceHasUse&&BuffObj.Carrier->HealthPercentage()<=0)
	{
		Res.BuffObj.Carrier->RevivedOnSecondWind();
		Res.BuffObj.Carrier->CharacterObj.CurrentRes.HP = BuffObj.Carrier->CharacterObj.CurProperty.HP ;
		Res.BuffObj.Stack--;
		Res.BuffObj.Model.TickTime = 0;
		Res.BuffObj.ResetTicked();
		SubSystem->SetRevivedChanceHasUse_CurBattle(RevivedChanceHasUse+1);
	}
	return  Res;
}

FBuffRunResult URogueBuff::HealOnRoomEnd(FBuffObj BuffObj, int RoomLevel, ERogueRoomType RoomType,
                                         TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	Res.TimelineNode = nullptr;

	int HealValue = Params.Num()>0?FCString::Atof(*Params[0]):0;

	FOffenseInfo Offense = FOffenseInfo();
	Offense.AttackInfo = FAttackInfo();
	Offense.AttackInfo.DamagePower = HealValue/100.f* BuffObj.Stack*BuffObj.Carrier->CharacterObj.CurProperty.HP;
	Offense.AttackInfo.IsHeal = true;
	Offense.AttackInfo.DamageSourceType = EAttackSource::Buff;
	Offense.AttackInfo.DamageType = EDamageType::ExtraDamage;
	Offense.SourceId = BuffObj.Model.Id;
	Offense.CanHitTimes = 0;
	Offense.Index = BuffObj.Duration;
	UOffenseManager::DoBuffOffense(Offense, BuffObj.Carrier,BuffObj.Carrier);

	return  Res;
}

FBuffRunResult URogueBuff::RecoverApPercentOnRoomEnd(FBuffObj BuffObj, int RoomLevel, ERogueRoomType RoomType,
	TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	Res.TimelineNode = nullptr;

	int RecoverPercent = Params.Num()>0?FCString::Atof(*Params[0]):0;

	BuffObj.Carrier->CharacterObj.CurrentRes.AP +=RecoverPercent/100.f* BuffObj.Stack*BuffObj.Carrier->CharacterObj.CurProperty.AP; ;
	BuffObj.Carrier->AttrRecheck();
	return  Res;
}

FBuffRunResult URogueBuff::GetCoinOnRoomEnd(FBuffObj BuffObj, int RoomLevel, ERogueRoomType RoomType,
                                            TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	Res.TimelineNode = nullptr;

	UAwRogueDataSystem* SubSystem =  GWorld->GetGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	if (!SubSystem)
	{
		return Res;
	}
	const int Num = BuffObj.Stack;

	SubSystem->AddCurrency_Coin(Num);

	return  Res;
}

FBuffRunResult URogueBuff::CastRelicNumToAttackPercent(FBuffObj BuffObj, int32 Ticked, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	Res.TimelineNode = nullptr;
	UAwRelicSubSystem* RelicSystem =  UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRelicSubSystem>();
	if (!RelicSystem)
	{
		return  Res;
	}
	
	TArray<int> Nums;
	RelicSystem->RelicHasGotInFormalPool.GenerateValueArray(Nums);
	int Total = Algo::Accumulate(Nums,0,TPlus<>());
	while (	Res.BuffObj.Model.CharacterPropertyModify.Num()<2)
	{
		Res.BuffObj.Model.CharacterPropertyModify.Add(FChaProp());
	}
	float Percent = Params.Num()>0?FCString::Atof(*Params[0]):0;
	float Limit = Params.Num()>1?FCString::Atof(*Params[1]):0;
	float  AddNum = Total*Percent;

	AddNum = FMath::Clamp(AddNum,0,Limit/BuffObj.Stack);
	
	Res.BuffObj.Model.CharacterPropertyModify[1].PAttack =AddNum;
	Res.BuffObj.Model.CharacterPropertyModify[1].MAttack =AddNum;
	return  Res;
}

FBuffRunResult URogueBuff::CastRelicGodTagToAttackPercent(FBuffObj BuffObj, int32 Ticked, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	Res.TimelineNode = nullptr;
	UAwRelicSubSystem* RelicSystem =  UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRelicSubSystem>();
	if (!RelicSystem)
	{
		return  Res;
	}
	TArray<FString> Tags;
	Tags = RelicSystem->GetRelicTagsHasGot();
	int num = Tags.Num();
	float Percent = Params.Num()>0?FCString::Atof(*Params[0]):0;
	float  AddNum = num*Percent;
	while (	Res.BuffObj.Model.CharacterPropertyModify.Num()<2)
	{
		Res.BuffObj.Model.CharacterPropertyModify.Add(FChaProp());
	}
	Res.BuffObj.Model.CharacterPropertyModify[1].PAttack =AddNum;
	Res.BuffObj.Model.CharacterPropertyModify[1].MAttack =AddNum;
	
	return  Res;
}

FBuffRunResult URogueBuff::CastRelicNumToCriticalChance(FBuffObj BuffObj, int32 Ticked, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	Res.TimelineNode = nullptr;
	UAwRelicSubSystem* RelicSystem =  UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRelicSubSystem>();
	if (!RelicSystem)
	{
		return  Res;
	}
	
	TArray<int> Nums;
	RelicSystem->RelicHasGotInFormalPool.GenerateValueArray(Nums);
	int Total = Algo::Accumulate(Nums,0,TPlus<>());
	while (	Res.BuffObj.Model.CharacterPropertyModify.Num()<2)
	{
		Res.BuffObj.Model.CharacterPropertyModify.Add(FChaProp());
	}
	float Percent = Params.Num()>0?FCString::Atof(*Params[0]):0;
	float Limit = Params.Num()>1?FCString::Atof(*Params[1]):0;
	float  AddNum = Total*Percent;

	AddNum = FMath::Clamp(AddNum,0,Limit/BuffObj.Stack);
	
	Res.BuffObj.Model.CharacterPropertyModify[0].CriticalChance =AddNum;

	return  Res;
}

FBuffRunResult URogueBuff::CastRelicGodTagToCriticalChance(FBuffObj BuffObj, int32 Ticked, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	Res.TimelineNode = nullptr;
	UAwRelicSubSystem* RelicSystem =  UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRelicSubSystem>();
	if (!RelicSystem)
	{
		return  Res;
	}
	TArray<FString> Tags;
	Tags = RelicSystem->GetRelicTagsHasGot();
	int num = Tags.Num();
	float Percent = Params.Num()>0?FCString::Atof(*Params[0]):0;
	float  AddNum = num*Percent;
	while (	Res.BuffObj.Model.CharacterPropertyModify.Num()<2)
	{
		Res.BuffObj.Model.CharacterPropertyModify.Add(FChaProp());
	}
	Res.BuffObj.Model.CharacterPropertyModify[0].CriticalChance =AddNum;
	
	return  Res;
}

FBuffRunResult URogueBuff::CastRelicNumToCriticalRate(FBuffObj BuffObj, int32 Ticked, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	Res.TimelineNode = nullptr;
	UAwRelicSubSystem* RelicSystem =  UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRelicSubSystem>();
	if (!RelicSystem)
	{
		return  Res;
	}
	
	TArray<int> Nums;
	RelicSystem->RelicHasGotInFormalPool.GenerateValueArray(Nums);
	int Total = Algo::Accumulate(Nums,0,TPlus<>());
	while (	Res.BuffObj.Model.CharacterPropertyModify.Num()<2)
	{
		Res.BuffObj.Model.CharacterPropertyModify.Add(FChaProp());
	}
	float Percent = Params.Num()>0?FCString::Atof(*Params[0]):0;
	float Limit = Params.Num()>1?FCString::Atof(*Params[1]):0;
	float  AddNum = Total*Percent;

	AddNum = FMath::Clamp(AddNum,0,Limit/BuffObj.Stack);
	
	Res.BuffObj.Model.CharacterPropertyModify[0].CriticalRate =AddNum;

	return  Res;
}

FBuffRunResult URogueBuff::CastRelicGodTagToCriticalRate(FBuffObj BuffObj, int32 Ticked, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	Res.TimelineNode = nullptr;
	UAwRelicSubSystem* RelicSystem =  UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRelicSubSystem>();
	if (!RelicSystem)
	{
		return  Res;
	}
	TArray<FString> Tags;
	Tags = RelicSystem->GetRelicTagsHasGot();
	int num = Tags.Num();
	float Percent = Params.Num()>0?FCString::Atof(*Params[0]):0;
	float  AddNum = num*Percent;
	while (	Res.BuffObj.Model.CharacterPropertyModify.Num()<2)
	{
		Res.BuffObj.Model.CharacterPropertyModify.Add(FChaProp());
	}
	Res.BuffObj.Model.CharacterPropertyModify[0].CriticalRate =AddNum;
	
	return  Res;
}

FBuffRunResult URogueBuff::CastRelicNumToMaxHp(FBuffObj BuffObj, int32 Ticked, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	Res.TimelineNode = nullptr;
	UAwRelicSubSystem* RelicSystem =  UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRelicSubSystem>();
	if (!RelicSystem)
	{
		return  Res;
	}
	
	TArray<int> Nums;
	RelicSystem->RelicHasGotInFormalPool.GenerateValueArray(Nums);
	int Total = Algo::Accumulate(Nums,0,TPlus<>());
	while (	Res.BuffObj.Model.CharacterPropertyModify.Num()<2)
	{
		Res.BuffObj.Model.CharacterPropertyModify.Add(FChaProp());
	}
	float Percent = Params.Num()>0?FCString::Atof(*Params[0]):0;
	float Limit = Params.Num()>1?FCString::Atof(*Params[1]):0;
	float  AddNum = Total*Percent;

	AddNum = FMath::Clamp(AddNum,0,Limit);
	
	Res.BuffObj.Model.CharacterPropertyModify[0].HP =AddNum;

	return  Res;
}

FBuffRunResult URogueBuff::CastRelicGodTagToMaxHp(FBuffObj BuffObj, int32 Ticked, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	Res.TimelineNode = nullptr;
	UAwRelicSubSystem* RelicSystem =  UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRelicSubSystem>();
	if (!RelicSystem)
	{
		return  Res;
	}
	TArray<FString> Tags;
	Tags = RelicSystem->GetRelicTagsHasGot();
	int num = Tags.Num();
	float Percent = Params.Num()>0?FCString::Atof(*Params[0]):0;
	float  AddNum = num*Percent;
	while (	Res.BuffObj.Model.CharacterPropertyModify.Num()<2)
	{
		Res.BuffObj.Model.CharacterPropertyModify.Add(FChaProp());
	}
	Res.BuffObj.Model.CharacterPropertyModify[0].HP =AddNum;
	
	return  Res;
}

FBuffRunResult URogueBuff::CriticalChanceUpWhenRogueItemInCoolDown(FBuffObj BuffObj, int32 Ticked,
                                                                   TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	Res.TimelineNode = nullptr;
	UAwRogueItemSubSystem* ItemSystem =  UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueItemSubSystem>();
	if (!ItemSystem)
	{
		return  Res;
	}
	/*
	if (!ItemSystem->CurItem)
	{
		return  Res;
	}
	TArray<FString> Tags;

	float Percent = Params.Num()>0?FCString::Atof(*Params[0]):0;

	if (ItemSystem->CurItem->CurEnergy>=ItemSystem->CurItem->MaxEnergy)
	{
		return  Res;
	}
	
	while (Res.BuffObj.Model.CharacterPropertyModify.Num()<2)
	{
		Res.BuffObj.Model.CharacterPropertyModify.Add(FChaProp());
	}
	Res.BuffObj.Model.CharacterPropertyModify[0].CriticalChance =Percent;
	*/
	return  Res;
}

FBuffRunResult URogueBuff::CriticalRateUpWhenRogueItemInCoolDown(FBuffObj BuffObj, int32 Ticked,
	TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	Res.TimelineNode = nullptr;
	UAwRogueItemSubSystem* ItemSystem =  UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueItemSubSystem>();
	/*
	if (!ItemSystem)
	{
		return  Res;
	}
	if (!ItemSystem->CurItem)
	{
		return  Res;
	}
	TArray<FString> Tags;

	float Percent = Params.Num()>0?FCString::Atof(*Params[0]):0;

	if (ItemSystem->CurItem->CurEnergy>=ItemSystem->CurItem->MaxEnergy)
	{
		return  Res;
	}
	
	while (Res.BuffObj.Model.CharacterPropertyModify.Num()<2)
	{
		Res.BuffObj.Model.CharacterPropertyModify.Add(FChaProp());
	}
	Res.BuffObj.Model.CharacterPropertyModify[0].CriticalRate =Percent;
	*/
	return  Res;
}

FBuffRunResult URogueBuff::CastCoinNumToAttackPercent(FBuffObj BuffObj, int32 Ticked, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	Res.TimelineNode = nullptr;

	UAwRogueDataSystem* SubSystem =  GWorld->GetGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	if (!SubSystem)
	{
		return Res;
	}
	
	int Total =  SubSystem->GetCurrency_Coin();
	while (	Res.BuffObj.Model.CharacterPropertyModify.Num()<2)
	{
		Res.BuffObj.Model.CharacterPropertyModify.Add(FChaProp());
	}
	float Percent = Params.Num()>0?FCString::Atof(*Params[0]):0;

	float  AddNum = Total*Percent;
	
	Res.BuffObj.Model.CharacterPropertyModify[1].PAttack =AddNum;
	Res.BuffObj.Model.CharacterPropertyModify[1].MAttack =AddNum;
	return  Res;

	
}

FBuffRunResult URogueBuff::CreateBulletOnCarrierOnTick(FBuffObj BuffObj, int32 WasStack, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult(BuffObj, nullptr);
	if (!BuffObj.Carrier)
	{
		return  Res;
	}
	FString BulletId = Params.Num()>0?Params[0]:"";
	float Duration =  Params.Num()>1?FCString::Atof(*Params[1]):0;
	FString SocketName = Params.Num()>2?Params[2]:"";
	FJsonFuncData Tween = UDataFuncLib::SplitFuncNameAndParams(Params.Num()>3?Params[3]:"");

	FBulletModel  BulletModel = UGameplayFuncLib::GetDataManager()->GetBulletModelById(BulletId);
	
	FVector Pos = FVector::ZeroVector; ;
	FRotator Rot = FRotator::ZeroRotator;
	FVector Direction = FVector::ZeroVector;
	FRotator NewRot = FRotator::ZeroRotator;

	bool bUseNewRot = false;
	//使用骨骼空间
	if (BuffObj.Carrier->GetMesh()&&BuffObj.Carrier->GetMesh()->DoesSocketExist(FName(SocketName)))
	{
		Pos = BuffObj.Carrier->GetMesh()->GetSocketLocation(FName(SocketName));
		Rot = BuffObj.Carrier->GetMesh()->GetSocketRotation(FName(SocketName));
		FVector Forward = BuffObj.Carrier->GetActorForwardVector();
		FVector Up =  UKismetMathLibrary::GetUpVector(Rot);
		FVector Right =  UKismetMathLibrary::GetRightVector(Rot);
		NewRot = UKismetMathLibrary::MakeRotationFromAxes(Forward,Right,Up);
		Direction = UKismetMathLibrary::GetForwardVector(NewRot);
		bUseNewRot= true;
	}
	else
	{
		//基础空间信息
		Pos = BuffObj.Carrier->GetActorLocation();
		Direction =BuffObj.Carrier->GetActorForwardVector();
	}
	Direction.Normalize();
	
	FBulletLauncher Launcher = FBulletLauncher();
	Launcher.Model = BulletModel;
	Launcher.Position = Pos;
	Launcher.Direction = Direction;
	Launcher.TweenFunc = Tween;
	Launcher.Caster = BuffObj.Carrier;
	Launcher.Duration = Duration;
	
	AAwBullet*  Bullet = UGameplayFuncLib::CreateBullet(Launcher);

	if (bUseNewRot)
	{
		Bullet->SetActorRotation(NewRot);
	}
	
	return  Res;
}

FBuffDamageResult URogueBuff::CheckNewbieActionHit(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
	TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;
	FActionInfo* CurAction = BuffObj.Carrier->CurrentAction();
	FString CheckActionId = "";
	if(Params.Num() > 0)
		CheckActionId = Params[0];
	float CheckInterval = 0;
	if(Params.Num() > 1)
		CheckInterval = FCString::Atof(*Params[1]);
	if(CurAction != nullptr)
	{
		float LastTime = 0;
		if(BuffObj.Param.Contains(CheckActionId))
			LastTime = FCString::Atof(*BuffObj.Param[CheckActionId]);
		if(CurAction->Id == CheckActionId && DamInfo.DamageSourceType == EAttackSource::AttackAction)
		{
			if(LastTime == 0 || BuffObj.Time - LastTime >= CheckInterval)
			{
				Res.BuffObj.Param.Add(CheckActionId, FString::SanitizeFloat(BuffObj.Time));
				Res.BuffObj.Stack += 1;
			}
		}
	}
	return Res;
}

FBuffRunResult URogueBuff::CheckNewbieCurActionId(FBuffObj BuffObj, int32 Ticked, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult(BuffObj, nullptr);
	FString CheckActionId = "";
	if(Params.Num() > 0)
		CheckActionId = Params[0];
	float CheckInterval = 0;
	if(Params.Num() > 1)
		CheckInterval = FCString::Atof(*Params[1]);
	FActionInfo* CurAction = BuffObj.Carrier->CurrentAction();
	if(CurAction != nullptr)
	{
		float LastTime = 0;
		if(BuffObj.Param.Contains(CheckActionId))
			LastTime = FCString::Atof(*BuffObj.Param[CheckActionId]);
		if(CurAction->Id == CheckActionId)
		{
			if(LastTime == 0 || BuffObj.Time - LastTime >= CheckInterval)
			{
				Res.BuffObj.Param.Add(CheckActionId, FString::SanitizeFloat(BuffObj.Time));
				Res.BuffObj.Stack += 1;
			}
		}
	}
	return Res;
}


