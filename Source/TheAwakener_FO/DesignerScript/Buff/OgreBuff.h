// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "TheAwakener_FO/GamePlay/Buff/Buff.h"
#include "OgreBuff.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UOgreBuff : public UObject
{
	GENERATED_BODY()
public:
	//【OnOccur】删除PillarAOE
	UFUNCTION()
	static FBuffRunResult DeletePillarSceneItem(FBuffObj BuffObj, int32 WasStack, TArray<FString> Params);

	//【OnRemoved】删除绑在巨魔身上的PillarAOE
	UFUNCTION()
	static FBuffRunResult DeleteAttachedPillarAOE(FBuffObj BuffObj, int32 WasStack, TArray<FString> Params);

	//【OnTick】 在血量在15%以下时，始终保持FightingWill.Level = 2
	UFUNCTION()
	static FBuffRunResult SetExcitedWhenHPLess(FBuffObj BuffObj, int32 Ticked, TArray<FString> Params);
	
};
