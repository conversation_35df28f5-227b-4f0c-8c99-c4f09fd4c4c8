// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/GamePlay/Buff/Buff.h"
#include "UObject/Object.h"
#include "TitanGolemBuff.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UTitanGolemBuff : public UObject
{
	GENERATED_BODY()

public:

	//显示泰坦魔俑手上的石头
	UFUNCTION()
	static FBuffRunResult ShowStone(FBuffObj BuffObj,int WasStack , TArray<FString> Params);

	//隐藏泰坦魔俑手上的石头
	UFUNCTION()
	static FBuffRunResult HideStone(FBuffObj BuffObj,int WasStack , TArray<FString> Params);
};
