// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/GamePlay/Buff/Buff.h"
#include "UObject/Object.h"
#include "DialogBuffs.generated.h"

/**
 * 对话相关的buff
 */
UCLASS()
class THEAWAKENER_FO_API UDialogBuffs : public UObject
{
	GENERATED_BODY()

public:
	/**
	 * 在不爽的时候说话
	 * 这里的问题在于是挨打前的声望，所以我们要预判先给他加一点的，其他没啥
	 * 参数：[是友好度(0)还是敌对度(1)，百分比(0-1)，说话人名(string)，说话内容(string)，对话显示多久(float)]
	 */ 
	UFUNCTION()
	static FBuffDamageResult SpeakOnDissatisfy(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);
};
