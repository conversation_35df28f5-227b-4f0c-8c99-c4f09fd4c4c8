// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "TheAwakener_FO/GameFramework/Timeline/TimelineNode.h"
#include "TheAwakener_FO/GameFramework/Base/AwGameModeBase.h"
#include "MapScript.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UMapScript : public UObject
{
	GENERATED_BODY()
public:
	//【OnCreate.Condition】Test
	UFUNCTION(BlueprintCallable)
		static bool TestMapCondition(FString LevelName, FTransform LevelTrans, TArray<FString> Params);
	//【OnCreate.Action】Test
	UFUNCTION(BlueprintCallable)
		static UTimelineNode* TestMapOnCreate(FString LevelName, FTransform LevelTrans, TArray<FString> Params);
	
	
	//【OnCreate.Action】矿洞地下城中创建堵住哥布林地区的石头
	UFUNCTION(BlueprintCallable)
		static UTimelineNode* SpawnBlockGoblinRock(FString LevelName, FTransform LevelTrans, TArray<FString> Params);

	//【OnCreate.Action】矿洞地下城中创建堵住巨魔房间的石头
	UFUNCTION(BlueprintCallable)
		static UTimelineNode* SpawnBlockOgreRock(FString LevelName, FTransform LevelTrans, TArray<FString> Params);

	//【OnCreate.Action】矿洞地下城中创建堵住冰恶魔房间的石头
	UFUNCTION(BlueprintCallable)
		static UTimelineNode* SpawnBlockIceDevilRock(FString LevelName, FTransform LevelTrans, TArray<FString> Params);

	//【OnRemoved.Condition】Test
	UFUNCTION(BlueprintCallable)
		static bool TestMapOnRemoveCondition(FString LevelName, TArray<FString> Params){return true;}
	//【OnRemoved.Action】Test
	UFUNCTION(BlueprintCallable)
		static UTimelineNode* TestMapOnRemove(FString LevelName, TArray<FString> Params){return nullptr;}

	//RandomMapCondition
	UFUNCTION(BlueprintCallable)
	static bool CheckMineStoryStep(FAwDungeonSave DungeonRecord, TArray<FString> EventList, TArray<FString> Params);

	
	//RandomDungeonLevelCondition
	//检测MineStoryStep是否为Params[0],如果是的话Result值为Params[1]
	UFUNCTION(BlueprintCallable)
	static int CheckMineStoryStepAndSetValue(FAwDungeonPresetTile TileInfo, TArray<FString> TriggeEvents, TArray<FString> Params);
};
