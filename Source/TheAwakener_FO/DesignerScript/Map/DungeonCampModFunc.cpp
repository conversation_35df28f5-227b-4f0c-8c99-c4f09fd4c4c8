// Fill out your copyright notice in the Description page of Project Settings.


#include "DungeonCampModFunc.h"

#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"

void UDungeonCampModFunc::CheckForSetMineStoryStep(FString DungeonId,	FString CampId,
                                                   FAwDungeonCampEvent EventInfo, int CurTriggerTime,int CurProgress, TArray<FString> Params)
{
	if (!UGameplayFuncLib::GetAwGameInstance() )
	{
		return ;
	}
	
	const bool OgreKilled = UGameplayFuncLib::GetAwGameInstance()->RoleInfo.GetSwitch("Ogre_Killed") > 0;
	const int RatMan = UGameplayFuncLib::GetAwGameInstance()->RoleInfo.GetSwitch("RatManMin", 1000);
	const int Goblin = UGameplayFuncLib::GetAwGameInstance()->RoleInfo.GetSwitch("GoblinMin", 1000);

	int ToValue = UGameplayFuncLib::GetAwGameInstance()->RoleInfo.GetSwitch("MineStoryStep");
	if (OgreKilled)
	{
		const int Human = 2000 - RatMan - Goblin;
		if (Human >= 1700)
		{
			ToValue = FMath::Max(ToValue, 7);
		}else if (Human >= 1200)
		{
			ToValue = FMath::Max(ToValue, 6);
		}else if (Human >= 900)
		{
			ToValue = FMath::Max(ToValue, 5);
		}else if (RatMan <= 500)
		{
			ToValue = FMath::Max(ToValue, 4);
		} else if (RatMan <= 800)
		{
			ToValue = FMath::Max(ToValue, 3);
		}
	}else
	{
		if (RatMan <= 800)
		{
			ToValue = FMath::Max(ToValue, 2);
		}
	}

	UGameplayFuncLib::GetAwGameInstance()->RoleInfo.SetSwitchValue("MineStoryStep", ToValue);
	
}

void UDungeonCampModFunc::CheckForSetRodianStory03Step(FString DungeonId, FString CampId, FAwDungeonCampEvent EventInfo,
	int CurTriggerTime, int CurProgress, TArray<FString> Params)
{
	if (!UGameplayFuncLib::GetAwGameInstance() )
	{
		return;
	}
	if(DungeonId == "TestDungeon" && CampId == "RatMan" && CurProgress <= 300)
	{
		const int RodianStory03Step = UGameplayFuncLib::GetAwGameInstance()->RoleInfo.GetSwitch("RodianStory03", 0);
		if(RodianStory03Step < 3)
			UGameplayFuncLib::GetAwGameInstance()->RoleInfo.SetSwitchValue("RodianStory03", 3);
	}
}
