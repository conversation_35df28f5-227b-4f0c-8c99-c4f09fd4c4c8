// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "TheAwakener_FO/GamePlay/Map/GameDestSign.h"
#include "UObject/Object.h"
#include "PlayerTargetFunc.generated.h"

/**
 * 玩家引导器的回调
 */
UCLASS()
class THEAWAKENER_FO_API UPlayerTargetFunc : public UObject
{
	GENERATED_BODY()
public:
	//【OnEnter】始终会被删除，就是测试用用，别多想了
	UFUNCTION(BlueprintCallable)
	static bool Always(AGameDestSign* Sign, AAwCharacter* EnterCha, TArray<FString> Params);

	//【OnEnter】显示一个Toast，[0]Toast的TextKey, [1]是否返回true（关闭这个点）默认是的
	UFUNCTION(BlueprintCallable)
	static bool Toast(AGameDestSign* Sign, AAwCharacter* EnterCha, TArray<FString> Params);

	//【OnEnter】显示一个新手引导框 [0]要显示的Key，请查阅AwGameInstance.cpp中NewbieManager
	UFUNCTION(BlueprintCallable)
	static bool NewbieHint(AGameDestSign* Sign, AAwCharacter* EnterCha, TArray<FString> Params);

	//【OnEnter】设置GameState的Switch，[0]Key, [1]Value
	UFUNCTION(BlueprintCallable)
	static bool SetSwitchInGameState(AGameDestSign* Sign, AAwCharacter* EnterCha, TArray<FString> Params);

	//【OnEnter】设置RoleSwitch，[0]Key, [1]Value
	UFUNCTION(BlueprintCallable)
	static bool SetRoleSwitch(AGameDestSign* Sign, AAwCharacter* EnterCha, TArray<FString> Params);

	//【OnEnter】检测GameState的Switch，[0]Key, [1]Value
	UFUNCTION(BlueprintCallable)
	static bool CheckSwitchInGameState(AGameDestSign* Sign, AAwCharacter* EnterCha, TArray<FString> Params);

	//【OnEnter】检测RoleSwitch，[0]Key, [1]Value
	UFUNCTION(BlueprintCallable)
	static bool CheckRoleSwitch(AGameDestSign* Sign, AAwCharacter* EnterCha, TArray<FString> Params);

	//【OnEnter】检测RoleSwitch，[0]Key, [1]Value
	UFUNCTION(BlueprintCallable)
	static bool CheckRoleSwitchGreater(AGameDestSign* Sign, AAwCharacter* EnterCha, TArray<FString> Params);

	//【OnEnter】检测RoleSwitch，[0]Key, [1]Value
	UFUNCTION(BlueprintCallable)
	static bool CheckRoleSwitchLess(AGameDestSign* Sign, AAwCharacter* EnterCha, TArray<FString> Params);

	//【LocationPicker】选择回电梯的入口，仅适合目前版本Dungeon01
	UFUNCTION(BlueprintCallable)
	static FTransform BackToElevator(TArray<FString> Params);
};
