// Fill out your copyright notice in the Description page of Project Settings.


#include "MapScript.h"

#include "Kismet/KismetMathLibrary.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GamePlay/GameMode/AwGameMode_RandomDungeon.h"

bool UMapScript::TestMapCondition(FString LevelName, FTransform LevelTrans, TArray<FString> Params)
{
	return true;
}

UTimelineNode* UMapScript::TestMapOnCreate(FString LevelName, FTransform LevelTrans, TArray<FString> Params)
{
	UAwGameInstance* GameInstance = UGameplayFuncLib::GetAwGameInstance();
	int MobLevel = 1;
	if (Params.Num())
		MobLevel = FCString::Atoi(*Params[0]);
	FTransform MobTrans = FTransform();
	MobTrans.SetLocation(FVector(100, 500, 200));
	UGameplayFuncLib::CreateCharacterByMobInfo(MobTrans,"Ogre", MobLevel);
	return nullptr;
}

UTimelineNode* UMapScript::SpawnBlockGoblinRock(FString LevelName, FTransform LevelTrans, TArray<FString> Params)
{
	if(Params.Num())
	{
		AAwGameMode_RandomDungeon* DungeonGameMode = Cast<AAwGameMode_RandomDungeon>(UGameplayStatics::GetGameMode(GWorld));
		if (DungeonGameMode)
		{
			for (FDungeonTile CurRoom : DungeonGameMode->RoomList)
			{
				if (LevelName == CurRoom.LevelPath)
				{
					if (CurRoom.LevelPath == "/Game/Maps/MainMapWhiteBox/LA_Dungeon_07" || CurRoom.LevelPath == "/Game/Maps/MainMapWhiteBox/LA_Dungeon_01")
					{
						for (FDungeonDoorInfo OpenedDoor : CurRoom.OpenedDoors)
						{
							if (OpenedDoor.LinkedStep == 7)
							{
								FTransform SpawnTrans = FTransform();
								SpawnTrans.SetLocation(OpenedDoor.Location);
								SpawnTrans.SetRotation(UKismetMathLibrary::MakeRotFromX(FVector(OpenedDoor.Direction * -1, 0)).Quaternion());
								SpawnTrans.SetScale3D(FVector(1, 1, 1));
								//可被破坏的门
								FSceneItemModel Model = UGameplayFuncLib::GetAwDataManager()->GetSceneItemModelById(Params[0]);
								UGameplayFuncLib::CreateSceneItem(Model, 2, SpawnTrans);
								//不可被破坏的岩石门架子
								if (Params.Num() > 1)
								{
									FSceneItemModel RockModel = UGameplayFuncLib::GetAwDataManager()->GetSceneItemModelById(Params[1]);
									UGameplayFuncLib::CreateSceneItem(RockModel, 0, SpawnTrans);
								}
								return nullptr;
							}
						}
					}
					break;
				}
			}
		}
	}
	return nullptr;
}

UTimelineNode* UMapScript::SpawnBlockOgreRock(FString LevelName, FTransform LevelTrans, TArray<FString> Params)
{
	if(Params.Num())
	{
		AAwGameMode_RandomDungeon* DungeonGameMode = Cast<AAwGameMode_RandomDungeon>(UGameplayStatics::GetGameMode(GWorld));
		if (DungeonGameMode)
		{
			for (FDungeonTile CurRoom : DungeonGameMode->RoomList)
			{
				if (LevelName == CurRoom.LevelPath)
				{
					if (CurRoom.LevelPath == "/Game/Maps/MainMapWhiteBox/LA_Dungeon_05")
					{
						for (FDungeonDoorInfo OpenedDoor : CurRoom.OpenedDoors)
						{
							if (OpenedDoor.LinkedStep == 2)
							{
								FTransform SpawnTrans = FTransform();
								SpawnTrans.SetLocation(OpenedDoor.Location);
								SpawnTrans.SetRotation(UKismetMathLibrary::MakeRotFromX(FVector(OpenedDoor.Direction * -1, 0)).Quaternion());
								SpawnTrans.SetScale3D(FVector(1, 1, 1));
								//可被破坏的门
								FSceneItemModel Model = UGameplayFuncLib::GetAwDataManager()->GetSceneItemModelById(Params[0]);
								UGameplayFuncLib::CreateSceneItem(Model, 0, SpawnTrans);
								return nullptr;
							}
						}
					}
					break;
				}
			}
		}
	}
	return nullptr;
}

UTimelineNode* UMapScript::SpawnBlockIceDevilRock(FString LevelName, FTransform LevelTrans, TArray<FString> Params)
{
	if(Params.Num())
	{
		AAwGameMode_RandomDungeon* DungeonGameMode = Cast<AAwGameMode_RandomDungeon>(UGameplayStatics::GetGameMode(GWorld));
		if (DungeonGameMode)
		{
			for (FDungeonTile CurRoom : DungeonGameMode->RoomList)
			{
				if (LevelName == CurRoom.LevelPath)
				{
					if (CurRoom.LevelPath == "/Game/Maps/MainMapWhiteBox/LA_Dungeon_05")
					{
						for (FDungeonDoorInfo OpenedDoor : CurRoom.OpenedDoors)
						{
							if (OpenedDoor.LinkedStep == 4 || OpenedDoor.LinkedStep == 5)
							{
								FTransform SpawnTrans = FTransform();
								SpawnTrans.SetLocation(OpenedDoor.Location);
								SpawnTrans.SetRotation(UKismetMathLibrary::MakeRotFromX(FVector(OpenedDoor.Direction * -1, 0)).Quaternion());
								SpawnTrans.SetScale3D(FVector(1, 1, 1));
								//可被破坏的门
								FSceneItemModel Model = UGameplayFuncLib::GetAwDataManager()->GetSceneItemModelById(Params[0]);
								UGameplayFuncLib::CreateSceneItem(Model, 0, SpawnTrans);
							}
						}
						return nullptr;
					}
					break;
				}
			}
		}
	}
	return nullptr;
}

bool UMapScript::CheckMineStoryStep(FAwDungeonSave DungeonRecord, TArray<FString> EventList, TArray<FString> Params)
{
	if(UGameplayFuncLib::GetAwGameInstance()->RoleInfo.SwitchJustModified.Contains("MineStoryStep"))
	{
		return UGameplayFuncLib::GetAwGameInstance()->RoleInfo.SwitchJustModified["MineStoryStep"];
	}
	return false;
}

int UMapScript::CheckMineStoryStepAndSetValue(FAwDungeonPresetTile TileInfo, TArray<FString> TriggeEvents, TArray<FString> Params)
{
	if(Params.Num() >1)
	{
		bool HasFindKey = false;
		int SwitchKey = UGameplayFuncLib::GetRoleSwitchValue("MineStoryStep",HasFindKey);
		if(SwitchKey == FCString::Atoi(*Params[0]))
		{
			return FCString::Atoi(*Params[1]);
		}
	}
	return 10;
}


