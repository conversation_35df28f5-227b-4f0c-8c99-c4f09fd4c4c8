// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/GamePlay/Map/AwDungeonInfo.h"
#include "UObject/Object.h"
#include "DungeonCampModFunc.generated.h"

/**
 * 地城势力信息变化触发器
 */
UCLASS()
class THEAWAKENER_FO_API UDungeonCampModFunc : public UObject
{
	GENERATED_BODY()
public:
	//【Action】 自动检查增加矿洞故事的进度用，MineStoryStep变化检测
	UFUNCTION(BlueprintCallable)
	static void CheckForSetMineStoryStep(FString DungeonId,	FString CampId,
		FAwDungeonCampEvent EventInfo, int CurTriggerTime,int CurProgress, TArray<FString> Params);

	//【Action】 自动检查鼠人势力值，RodianStory03=3的变化检测
	UFUNCTION(BlueprintCallable)
	static void CheckForSetRodianStory03Step(FString DungeonId,	FString CampId,
		FAwDungeonCampEvent EventInfo, int CurTriggerTime,int CurProgress, TArray<FString> Params);
};
