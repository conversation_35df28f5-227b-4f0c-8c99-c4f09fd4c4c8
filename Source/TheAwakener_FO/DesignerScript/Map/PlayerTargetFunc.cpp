// Fill out your copyright notice in the Description page of Project Settings.


#include "PlayerTargetFunc.h"

#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"


bool UPlayerTargetFunc::Always(AGameDestSign* Sign, AAwCharacter* EnterCha, TArray<FString> Params)
{
	return true;
}

bool UPlayerTargetFunc::Toast(AGameDestSign* Sign, AAwCharacter* EnterCha, TArray<FString> Params)
{
	const FString Key = Params.Num() > 0 ? Params[0] : "";
	const bool Res = Params.Num() > 1 ? Params[1].ToBool() : true;

	if (Key.IsEmpty() == false)
	{
		UGameplayFuncLib::GetAwGameInstance()->UIManager->ShowToast(Key);
	}

	return Res;
}

bool UPlayerTargetFunc::NewbieHint(AGameDestSign* Sign, AAwCharacter* EnterCha, TArray<FString> Params)
{
	if (Params.Num() <= 0) return true;

	UGameplayFuncLib::GetAwGameInstance()->NewbieManager->ShowNewbie(Params);
	
	return true;
}

bool UPlayerTargetFunc::SetSwitchInGameState(AGameDestSign* Sign, AAwCharacter* EnterCha, TArray<FString> Params)
{
	if (Params.Num() < 2) return true;
	const FString SKey = Params[0];
	const int SVal = FCString::Atoi(*Params[1]);
	AAwGameState* GameState = UGameplayFuncLib::GetAwGameState();
	if(GameState)
	{
		GameState->SwitchList.Add(SKey, SVal);
	}
	
	return true;
}

bool UPlayerTargetFunc::SetRoleSwitch(AGameDestSign* Sign, AAwCharacter* EnterCha, TArray<FString> Params)
{
	if (Params.Num() < 2) return true;
	const FString SKey = Params[0];
	const int SVal = FCString::Atoi(*Params[1]);
	UGameplayFuncLib::GetAwGameInstance()->RoleInfo.SetSwitchValue(SKey, SVal);
	
	return true;
}

bool UPlayerTargetFunc::CheckSwitchInGameState(AGameDestSign* Sign, AAwCharacter* EnterCha, TArray<FString> Params)
{
	if(!Params.Num()) return false;
	const FString SwitchKey = Params[0];
	AAwGameState* GameState = UGameplayFuncLib::GetAwGameState();
	if(GameState)
	{
		if(Params.Num() > 1)
		{
			const int CheckValue = FCString::Atoi(*Params[1]);
			if(GameState->SwitchList.Contains(SwitchKey))
			{
				return (CheckValue == GameState->SwitchList[SwitchKey]);
			}
		}
		else
		{
			if(GameState->SwitchList.Contains(SwitchKey))
			{
				if(GameState->SwitchList[SwitchKey] == 0)
					return false;
				return true;
			}
		}
	}
	return false;
}

bool UPlayerTargetFunc::CheckRoleSwitch(AGameDestSign* Sign, AAwCharacter* EnterCha, TArray<FString> Params)
{
	const FString SKey = Params[0];
	int SVal = 0;
	if (Params.Num() > 1)
		SVal = FCString::Atoi(*Params[1]);
	const int CurVal = UGameplayFuncLib::GetAwGameInstance()->RoleInfo.GetSwitch(SKey);
	return SVal == CurVal;
	return false;
}

bool UPlayerTargetFunc::CheckRoleSwitchGreater(AGameDestSign* Sign, AAwCharacter* EnterCha, TArray<FString> Params)
{
	const FString SKey = Params[0];
	int SVal = 0;
	if (Params.Num() > 1)
		SVal = FCString::Atoi(*Params[1]);
	const int CurVal = UGameplayFuncLib::GetAwGameInstance()->RoleInfo.GetSwitch(SKey);
	return SVal < CurVal;
	return false;
}

bool UPlayerTargetFunc::CheckRoleSwitchLess(AGameDestSign* Sign, AAwCharacter* EnterCha, TArray<FString> Params)
{
	const FString SKey = Params[0];
	int SVal = 0;
	if (Params.Num() > 1)
		SVal = FCString::Atoi(*Params[1]);
	const int CurVal = UGameplayFuncLib::GetAwGameInstance()->RoleInfo.GetSwitch(SKey);
	return SVal > CurVal;
	return false;
}

FTransform UPlayerTargetFunc::BackToElevator(TArray<FString> Params)
{
	FTransform Res = FTransform::Identity;
	for (const FDungeonTile RoomInfo : UGameplayFuncLib::GetAwGameState()->DungeonRoomsInfo)
	{
		if (RoomInfo.StepValue == 1)
		{
			//这就是我自己了
			for (const FDungeonDoorInfo Door : RoomInfo.OpenedDoors)
			{
				if (Door.LinkedStep == 0)
				{
					Res.SetLocation(Door.Location);
					return Res;
				}
			} 
		}
	}
	return Res;
}
