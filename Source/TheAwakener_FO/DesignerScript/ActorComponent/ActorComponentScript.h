// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "ActorComponentScript.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UActorComponentScript : public UObject
{
	GENERATED_BODY()
public:
	UFUNCTION()
		static bool CheckSwitchInGameState(UActorComponent* InfoBubbleComponent, TArray<FString> Params);
	UFUNCTION()
		static bool CheckLevelSequencePlaying(UActorComponent* InfoBubbleComponent, TArray<FString> Params);
	UFUNCTION()
		static bool CheckGameControlState(UActorComponent* InfoBubbleComponent, TArray<FString> Params);

};
