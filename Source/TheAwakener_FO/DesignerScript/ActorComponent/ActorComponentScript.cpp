// Fill out your copyright notice in the Description page of Project Settings.


#include "ActorComponentScript.h"
#include "TheAwakener_FO/DesignerScript/Trigger/TriggerScript.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"

bool UActorComponentScript::CheckSwitchInGameState(UActorComponent* InfoBubbleComponent, TArray<FString> Params)
{
	return UTriggerScript::CheckSwitchInGameState(Params);
}

bool UActorComponentScript::CheckLevelSequencePlaying(UActorComponent* InfoBubbleComponent, TArray<FString> Params)
{
	if (!UGameplayFuncLib::GetAwGameInstance())
	{
		return false;
	}
	UAwSequenceManager* Manager = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwSequenceManager>();
	const bool CheckPlay = Params.Num() > 0 ? Params[0] == "Play" : true;
	const bool IsGlobal = Params.Num() > 1 ? Params[1] == "Global" : true;
	if (Manager)
	{
		if (IsGlobal)
		{
			return Manager->IsGlobalSequencePlaying() == CheckPlay;
		}
		else
		{
			return Manager->IsAnyLocalSequencePlaying() == CheckPlay;
		}
	}
	return false;
}

bool UActorComponentScript::CheckGameControlState(UActorComponent* InfoBubbleComponent, TArray<FString> Params)
{
	if (Params.Num()<1)
	{
		return false;
	}
	AAwPlayerController* MyController = UGameplayFuncLib::GetWorkingAwPlayerController();
	if (!MyController) return false;

	bool result = false;
	for (auto StateParam :Params)
	{
		EGameControlState TargetState =UDataFuncLib::FStringToEnum<EGameControlState>(StateParam);
		if (TargetState == MyController->GameControlState)
		{
			result = true;
		}
	}

	return result;

}
