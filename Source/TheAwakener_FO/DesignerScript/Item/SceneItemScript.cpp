// Fill out your copyright notice in the Description page of Project Settings.


#include "SceneItemScript.h"

#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "TheAwakener_FO/GamePlay/Move/TrackMoveComponent.h"

bool USceneItemScript::PushOnTrackMove(FDamageInfo DamageInfo, AAwSceneItem* Defender, TArray<FString> Params)
{
	if(Defender->bDestroyed) return true;
	UTrackMoveComponent* TrackMove = Cast<UTrackMoveComponent>(Defender->GetComponentByClass(UTrackMoveComponent::StaticClass()));
	if(!TrackMove) return true;
	const int PushPower = FMath::Clamp(DamageInfo.FinalDamage() * 20 , 200 , 2000);
	if(!DamageInfo.Attacker)
		return false;
	FVector AttackDir = Defender->GetActorLocation() - DamageInfo.Attacker->GetActorLocation();
	AttackDir.Normalize();
	
	const FVector PushVelocity = AttackDir * PushPower;
	TrackMove->Push(PushVelocity);
	return true;
}

bool USceneItemScript::DestroyBlockGoblinRock(FDamageInfo DamageInfo, AAwSceneItem* Defender, TArray<FString> Params)
{
	if(Defender->bDestroyed) return true;
	if(DamageInfo.HitBox->GetOwner())
	{
		if(DamageInfo.HitBox->GetOwner()->Tags.Contains("MineCar"))
		{
			Defender->Kill();
		}
	}
	return true;
}

FVector USceneItemScript::GetBeamEndPointByProtectSceneItemBuff(AAwCharacter* Character, FVector OffSet, TArray<FString> Params)
{
	TArray<FBuffObj*> BuffObjs = Character->GetBuff("ProtectSceneItem");
	if (BuffObjs.Num() > 0)
	{
		FBuffObj* BuffObj = BuffObjs[0];
		const FString SceneItemId = "SceneItemId";
		if (BuffObj->Param.Contains(SceneItemId) && UGameplayFuncLib::GetAwGameState()->SceneItemList.Contains(BuffObj->Param[SceneItemId]))
		{
			const AAwSceneItem* SceneItem = UGameplayFuncLib::GetAwGameState()->SceneItemList[BuffObj->Param[SceneItemId]];
			if (SceneItem)
				return SceneItem->GetActorLocation() + SceneItem->GetActorScale() * OffSet;
		}
	}
	
	return FVector::ZeroVector;
}
