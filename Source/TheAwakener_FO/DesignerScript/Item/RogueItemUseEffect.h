// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "TheAwakener_FO/GamePlay/Roguelike/Item/AwRogueItemSubSystem.h"
#include "UObject/Object.h"
#include "RogueItemUseEffect.generated.h"

UCLASS()
class THEAWAKENER_FO_API URogueItemUseEffect : public UObject
{
	GENERATED_BODY()
public:
	//发射子弹
	UFUNCTION(BlueprintCallable)
	static FItemUseResult FireBullet(FAwRogueItemInfo Item, AAwCharacter* User,TMap<FString,FString>SpecialParams, TArray<FString> Params);

	UFUNCTION(BlueprintCallable)
	static FItemUseResult CreateAoeOnRoot(FAwRogueItemInfo Item, AAwCharacter* User,TMap<FString,FString>SpecialParams, TArray<FString> Params);

	UFUNCTION(BlueprintCallable)
	static FItemUseResult CreateAoeAttach(FAwRogueItemInfo Item, AAwCharacter* User,TMap<FString,FString>SpecialParams, TArray<FString> Params);
	
	//在使用者处生成对象
	UFUNCTION(BlueprintCallable)
	static FItemUseResult CreateTargetOnUserPoint(FAwRogueItemInfo Item, AAwCharacter* User,TMap<FString,FString>SpecialParams, TArray<FString> Params);

	//Rogue 喝血瓶
	UFUNCTION(BlueprintCallable)
	static FItemUseResult UseRogueHealingPotion(FAwRogueItemInfo Item, AAwCharacter* User,TMap<FString,FString>SpecialParams, TArray<FString> Params);

	//在使用者处生成 特定控制器（包括召唤器,生成器等, 这些东西是纯逻辑是 具体逻辑的控制器 ,所有具体逻辑由内部决定）
	UFUNCTION(BlueprintCallable)
	static FItemUseResult CreateControllersOnUserPoint(FAwRogueItemInfo Item, AAwCharacter* User,TMap<FString,FString>SpecialParams, TArray<FString> Params);

	//在使用者处生成并Attach 特定控制器（包括召唤器,生成器等, 这些东西是纯逻辑是 具体逻辑的控制器 ,所有具体逻辑由内部决定）
	UFUNCTION(BlueprintCallable)
	static FItemUseResult CreateControllersAttach(FAwRogueItemInfo Item, AAwCharacter* User,TMap<FString,FString>SpecialParams, TArray<FString> Params);
	
	//增加Buff
	UFUNCTION(BlueprintCallable)
	static FItemUseResult AddSelfItemBuff(FAwRogueItemInfo Item, AAwCharacter* User,TMap<FString,FString>SpecialParams, TArray<FString> Params);

	//增加Buff 如果已经存在 则只刷新时间
	UFUNCTION(BlueprintCallable)
	static FItemUseResult AddSelfItemBuffNoStack(FAwRogueItemInfo Item, AAwCharacter* User,TMap<FString,FString>SpecialParams, TArray<FString> Params);
	
	//移除Buff
	UFUNCTION(BlueprintCallable)
	static FItemUseResult RemoveSelfItemBuff(FAwRogueItemInfo Item, AAwCharacter* User,TMap<FString,FString>SpecialParams, TArray<FString> Params);

	//增加位移修改
	UFUNCTION(BlueprintCallable)
	static FItemUseResult ModifyRootMotionRate(FAwRogueItemInfo Item, AAwCharacter* User,TMap<FString,FString>SpecialParams, TArray<FString> Params);

	//移除位移修改
	UFUNCTION(BlueprintCallable)
	static FItemUseResult RemoveRootMotionRate(FAwRogueItemInfo Item, AAwCharacter* User,TMap<FString,FString>SpecialParams, TArray<FString> Params);
};
