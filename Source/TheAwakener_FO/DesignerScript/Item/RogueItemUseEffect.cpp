// Fill out your copyright notice in the Description page of Project Settings.


#include "RogueItemUseEffect.h"

//#include "K2Node_SpawnActorFromClass.h"
#include "Kismet/KismetMathLibrary.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GamePlay/Battle/OffenseManager.h"
#include "TheAwakener_FO/GamePlay/Buff/BuffManager.h"
#include "TheAwakener_FO/GamePlay/Roguelike/Item/RogueInterface.h"


FItemUseResult URogueItemUseEffect::FireBullet(FAwRogueItemInfo Item, AAwCharacter* User,
	TMap<FString,FString>SpecialParams, TArray<FString> Params)
{
	FItemUseResult Res = FItemUseResult(true, 1, true);
	//
	FString BulletId =Params.Num() > 0 ? Params[0] : "";
	FString FireAttackHitBoxName = Params.Num() > 1 ? Params[1] : "";
	float Speed =   Params.Num() > 2 ? FCString::Atof(*Params[2]) : 2;
	float LifeTime =   Params.Num() > 3 ? FCString::Atof(*Params[3]) : 3;
	bool UseFireBoxRotation =  Params.Num() > 4 ? Params[4].Equals("true",ESearchCase::IgnoreCase):false ;
	FVector TargetOffset =  FVector(1500,0,0);
	
	const USceneComponent* AttBox = User->GetAttackHitBoxByName(FireAttackHitBoxName);
	if (!AttBox) return Res;
	
	FVector TargetLoc = User->GetActorRotation().RotateVector(TargetOffset) + AttBox->GetComponentLocation();
	
	FVector StartLoc = AttBox->GetComponentLocation();
	
	FVector BulletDir = TargetLoc - StartLoc;
	BulletDir.Normalize();

	

	FBulletModel Model = UGameplayFuncLib::GetAwDataManager()->GetBulletModelById(BulletId);
	if (BulletId.IsEmpty() == false && Model.Id == BulletId)
	{
		FString TweenFunc =  "BulletScript.GoStraightAhead("+FString::SanitizeFloat(Speed)+")";
		FBulletLauncher Launcher = FBulletLauncher(User, Model,
			StartLoc, BulletDir, LifeTime,
			TweenFunc, TargetLoc);
		
			Res.Creaters.Add( UGameplayFuncLib::CreateBullet(Launcher));

		if (UseFireBoxRotation)
		{
			FJsonFuncData SetRotFunc = UDataFuncLib::SplitFuncNameAndParams("BulletScript.SetBulletRotation()");
			SetRotFunc.Params.Add(FString::SanitizeFloat(AttBox->GetComponentRotation().Roll));
			SetRotFunc.Params.Add(FString::SanitizeFloat(AttBox->GetComponentRotation().Pitch));
			SetRotFunc.Params.Add(FString::SanitizeFloat(AttBox->GetComponentRotation().Yaw));
			TArray<FJsonFuncData> ModelCreateList = Launcher.Model.OnCreate;
			Launcher.Model.OnCreate.Insert(SetRotFunc, 0);
		}
	}
	return Res;
}

FItemUseResult URogueItemUseEffect::CreateAoeOnRoot(FAwRogueItemInfo Item, AAwCharacter* User,
	TMap<FString,FString>SpecialParams, TArray<FString> Params)
{
	FItemUseResult Res = FItemUseResult(true, 1, true);
	const FVector Pos = User->GetActorLocation() - FVector(0, 0, User->GetCapsuleComponent()->GetScaledCapsuleHalfHeight());
	const FString AoeID = Params.Num() > 0 ? Params[0] : "";
	const float LifeTime = Params.Num() > 1 ? FCString::Atof(*Params[1]) : 1;
	
	if (AoeID.IsEmpty() == false)
	{
		Res.Creaters.Add(UGameplayFuncLib::CreateAOE(User, AoeID, Pos, FVector::ForwardVector, LifeTime, ""));
	}
	
	return Res;
}

FItemUseResult URogueItemUseEffect::CreateAoeAttach(FAwRogueItemInfo Item, AAwCharacter* User,
	TMap<FString,FString>SpecialParams, TArray<FString> Params)
{
	FItemUseResult Res = FItemUseResult(true, 1, true);
	const FVector Pos = User->GetActorLocation() - FVector(0, 0, User->GetCapsuleComponent()->GetScaledCapsuleHalfHeight());
	const FString AoeID = Params.Num() > 0 ? Params[0] : "";
	float LifeTime = Params.Num() > 1 ? FCString::Atof(*Params[1]) : 0;
	const FString AttachSocketId = Params.Num() > 2 ? Params[2] : "";
	bool UseAttachTransform =  Params.Num() > 3 ? Params[3].Equals("true",ESearchCase::IgnoreCase):false ;
	
	if (AoeID.IsEmpty() == false)
	{
		AAWAoe* AOE = UGameplayFuncLib::CreateAOE(User, AoeID, Pos, FVector::ForwardVector, LifeTime, "");
		if(AOE)
		{
			if (UseAttachTransform)
			{
				AOE->AttachToActor(User,FAttachmentTransformRules::KeepRelativeTransform,FName(AttachSocketId));
			}
			else
			{
				AOE->AttachToActor(User,FAttachmentTransformRules::SnapToTargetNotIncludingScale,FName(AttachSocketId));
			}
			Res.Creaters.Add(AOE);
		}
		return Res;
	}
	
	return FItemUseResult(true, 1, true);
}

FItemUseResult URogueItemUseEffect::CreateTargetOnUserPoint(FAwRogueItemInfo Item, AAwCharacter* User,
	TMap<FString,FString>SpecialParams, TArray<FString> Params)
{
	FItemUseResult Res = FItemUseResult(true, 1, true);
	FString BpClassPath = Params.Num() > 0 ?Params[0] : "";
	FTransform BulletTransform;
	FVector Offset = FVector::ZeroVector;
	FVector ParamsOffset = FVector::ZeroVector;
	ParamsOffset.X = Params.Num() > 1 ?FCString::Atof(*Params[1]) : 0.f;
	ParamsOffset.Y = Params.Num() > 2 ?FCString::Atof(*Params[2]) : 0.f;
	ParamsOffset.Z = Params.Num() > 3 ?FCString::Atof(*Params[3]) : 0.f;
	
	if (User->GetCapsuleComponent())
	{
		Offset = FVector(0,0,-1*User->GetCapsuleComponent()->GetScaledCapsuleHalfHeight());
	}
	BulletTransform.SetLocation(User->GetActorLocation()+Offset);
	BulletTransform.SetRotation(User->GetActorRotation().Quaternion());

	FTransform BulletOffsetTransform = BulletTransform;

	BulletOffsetTransform.SetLocation( UKismetMathLibrary::TransformLocation(BulletTransform,ParamsOffset));
	
	FString BpPath = UResourceFuncLib::GetBpAssetPath(BpClassPath);
	UClass* BpClass = LoadClass<AActor>(nullptr, *BpPath);
	if (BpClass)
	{
		AActor* Target =GWorld->SpawnActor<AActor>(BpClass, BulletOffsetTransform);
		if (IsValid(Target))
		{
			Target->SetOwner(User);
		}
		Res.Creaters.Add(Target);
	}

	return Res;
}

FItemUseResult URogueItemUseEffect::UseRogueHealingPotion(FAwRogueItemInfo Item, AAwCharacter* User,
	TMap<FString,FString>SpecialParams, TArray<FString> Params)
{
	UAwRogueItemSubSystem* SubSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueItemSubSystem>();
	if (!User)
	{
		return  FItemUseResult();
	}
    float  CurValue =	SubSystem->GetRogueItemAfterGlobalModify(SubSystem->CurHealingPotion).CurEffectValue;
	const int RestVal =FMath::RoundToInt(CurValue+SubSystem->CurHealingPotion.GetSelfAfterModify().CurEffectLevel/100.f*User->CharacterObj.CurProperty.HP);

	FOffenseInfo Offense = FOffenseInfo();
	Offense.AttackInfo = FAttackInfo();
	Offense.AttackInfo.DamagePower = FDamageValue(RestVal);
	Offense.AttackInfo.IsHeal = true;
	Offense.AttackInfo.DamageSourceType = EAttackSource::Item ;
	Offense.AttackInfo.DamageType = EDamageType::ExtraDamage;
	Offense.AttackInfo.Elemental = User->GetElemental(EChaElemental::Physical, EElementalPickMethod::Set, ESlotInWeaponObj::MainWeapon);
	Offense.SourceId = Item.UID;
	Offense.CanHitTimes = 1;
	//User->BeOffended(Offense, User, nullptr);
	UOffenseManager::DoBuffOffense(Offense, User, User);
	
	return FItemUseResult(true, 1, true);
	
}

FItemUseResult URogueItemUseEffect::CreateControllersOnUserPoint(FAwRogueItemInfo Item, AAwCharacter* User,
	TMap<FString,FString>SpecialParams, TArray<FString> Params)
{
	FItemUseResult Res = FItemUseResult(true, 1, true);
	
	FString TargetClassPath = Params.Num() > 0 ?Params[0]:"";
	const FString AttachSocketId = Params.Num() > 1 ? Params[1] : "";

	FString BpPath = UResourceFuncLib::GetBpAssetPath(TargetClassPath);
	UClass* BpClass = LoadClass<AActor>(nullptr, *BpPath);
	if (BpClass)
	{
		AActor* Controller  = GWorld->SpawnActor<AActor>(BpClass, User->GetActorTransform());
		if (IsValid(Controller)&&Controller->Implements<URogueCreaterControllerInterface>())
		{
			TMap<FString,FString>InterfaceParams = SpecialParams;
			IRogueCreaterControllerInterface::Execute_SetControllerInfo(Controller,User,InterfaceParams);
		}
		if (IsValid(Controller))
		{
			Controller->AttachToActor(User,FAttachmentTransformRules::SnapToTargetNotIncludingScale,FName(AttachSocketId));
		}
		Res.Creaters.Add(Controller);
	}
	return Res;

	
}

FItemUseResult URogueItemUseEffect::CreateControllersAttach(FAwRogueItemInfo Item, AAwCharacter* User,
	TMap<FString, FString> SpecialParams, TArray<FString> Params)
{
	FItemUseResult Res = FItemUseResult(true, 1, true);
	
	FString TargetClassPath = Params.Num() > 0 ?Params[0]:"";


	FString BpPath = UResourceFuncLib::GetBpAssetPath(TargetClassPath);
	UClass* BpClass = LoadClass<AActor>(nullptr, *BpPath);
	if (BpClass)
	{
		AActor* Controller  = GWorld->SpawnActor<AActor>(BpClass, User->GetActorTransform());
		if (IsValid(Controller)&&Controller->Implements<URogueCreaterControllerInterface>())
		{
			TMap<FString,FString>InterfaceParams = SpecialParams;
			IRogueCreaterControllerInterface::Execute_SetControllerInfo(Controller,User,InterfaceParams);
		}
		Res.Creaters.Add(Controller);
	}
	
	return Res;
}

FItemUseResult URogueItemUseEffect::AddSelfItemBuff(FAwRogueItemInfo Item, AAwCharacter* User, TMap<FString, FString> SpecialParams,
                                                    TArray<FString> Params)
{
	FItemUseResult Res = FItemUseResult(true, 1, true);
	if (!User) return Res;
	UAwDataManager* DataManager = UGameplayFuncLib::GetAwDataManager();
	if (!DataManager)
	{
		return  Res;
	}
	FString TargetMob = Params.Num() > 0 ?Params[0]:"";
	int Stack = Params.Num() > 1 ?FCString::Atoi(*Params[1]) : 0;
	float Time = Params.Num() > 2 ?FCString::Atof(*Params[2]) : 0;
	bool SetToDuration = Params.Num() > 3 ? Params[3].Equals("true",ESearchCase::IgnoreCase):true ;
	bool Infinity =  Params.Num() > 4? Params[4].Equals("true",ESearchCase::IgnoreCase):false ;
	
	FAddBuffInfo AddBuffInfo = FAddBuffInfo();
	AddBuffInfo.Model = DataManager->GetBuffModelById(TargetMob);
	AddBuffInfo.AddStack = Stack;
	AddBuffInfo.SetToDuration = SetToDuration;
	AddBuffInfo.Infinity = Infinity;
	AddBuffInfo.Duration = Time;
	AddBuffInfo.Caster = User;
	AddBuffInfo.Target = User;
	User->AddBuff(AddBuffInfo);
	
	return Res;
}

FItemUseResult URogueItemUseEffect::AddSelfItemBuffNoStack(FAwRogueItemInfo Item, AAwCharacter* User,
	TMap<FString, FString> SpecialParams, TArray<FString> Params)
{
	FItemUseResult Res = FItemUseResult(true, 1, true);
	if (!User) return Res;
	UAwDataManager* DataManager = UGameplayFuncLib::GetAwDataManager();
	if (!DataManager)
	{
		return  Res;
	}
	FString TargetMob = Params.Num() > 0 ?Params[0]:"";
	int Stack = Params.Num() > 1 ?FCString::Atoi(*Params[1]) : 0;
	float Time = Params.Num() > 2 ?FCString::Atof(*Params[2]) : 0;
	bool SetToDuration = Params.Num() > 3 ? Params[3].Equals("true",ESearchCase::IgnoreCase):false ;
	bool Infinity =  Params.Num() > 4? Params[4].Equals("true",ESearchCase::IgnoreCase):false ;
	
	FAddBuffInfo AddBuffInfo = FAddBuffInfo();
	AddBuffInfo.Model = DataManager->GetBuffModelById(TargetMob);
	AddBuffInfo.AddStack = Stack;
	AddBuffInfo.SetToDuration = SetToDuration;
	AddBuffInfo.Infinity = Infinity;
	AddBuffInfo.Duration = Time;
	AddBuffInfo.Caster = User;
	AddBuffInfo.Target = User;
	
	TArray<AAwCharacter*> Caster;
	Caster.Add(User);
	TArray<FBuffObj*> BuffObjs = User ->GetBuff(AddBuffInfo.Model.Id,Caster);
	
	if (BuffObjs.IsEmpty())
	{
		FBuffObj*Buff = User->AddBuff(AddBuffInfo);
		Buff->Param = SpecialParams;
	}
	else
	{
		for(auto Buff:BuffObjs)
		{
			UBuffManager::RefreshBuff(Buff,Time,SetToDuration);
			if (Buff->Stack<AddBuffInfo.AddStack )
			{
				UBuffManager::ModifyBuffStack(Buff,AddBuffInfo.AddStack,true);
			}
			Buff->Param = SpecialParams;
		}
	}
	return  Res;
}

FItemUseResult URogueItemUseEffect::RemoveSelfItemBuff(FAwRogueItemInfo Item, AAwCharacter* User,
                                                       TMap<FString, FString> SpecialParams, TArray<FString> Params)
{
	FItemUseResult Res = FItemUseResult(true, 1, true);
	if (!User) return Res;

	UAwDataManager* DataManager = UGameplayFuncLib::GetAwDataManager();
	if (!DataManager)
	{
		return  Res;
	}
	FString TargetMob = Params.Num() > 0 ?Params[0]:"";
	int Stack = Params.Num() > 1 ?FCString::Atoi(*Params[1]) : 0;
	
	FAddBuffInfo AddBuffInfo = FAddBuffInfo();
	AddBuffInfo.Model = DataManager->GetBuffModelById(TargetMob);
	AddBuffInfo.AddStack = Stack*-1;
	AddBuffInfo.Caster = User;
	AddBuffInfo.Target = User;
	
	User->AddBuff(AddBuffInfo);
	return Res;
}

FItemUseResult URogueItemUseEffect::ModifyRootMotionRate(FAwRogueItemInfo Item, AAwCharacter* User,
	TMap<FString, FString> SpecialParams, TArray<FString> Params)
{
	FItemUseResult Res = FItemUseResult(true, 1, true);
	if (!User) return Res;
	
	float Rate = Params.Num() > 0 ?FCString::Atof(*Params[0]) : 0;
	
	User->RootMotionRate.Add(Item.UID, Rate);
	return Res;
}

FItemUseResult URogueItemUseEffect::RemoveRootMotionRate(FAwRogueItemInfo Item, AAwCharacter* User,
	TMap<FString, FString> SpecialParams, TArray<FString> Params)
{
	FItemUseResult Res = FItemUseResult(true, 1, true);
	if (!User) return Res;
	
	User->RootMotionRate.Remove(Item.UID);
	return Res;
}


