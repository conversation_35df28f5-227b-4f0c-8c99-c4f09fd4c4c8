// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/GamePlay/Trigger/AwSceneItem.h"
#include "UObject/NoExportTypes.h"
#include "SceneItemScript.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API USceneItemScript : public UObject
{
	GENERATED_BODY()
public:
	//OnHit
	UFUNCTION()
	static bool PushOnTrackMove(FDamageInfo DamageInfo,AAwSceneItem* Defender,TArray<FString> Params);

	//OnHit
	UFUNCTION()
	static bool DestroyBlockGoblinRock(FDamageInfo DamageInfo,AAwSceneItem* Defender,TArray<FString> Params);

	UFUNCTION()
	static FVector GetBeamEndPointByProtectSceneItemBuff(AAwCharacter* Character, FVector OffSet, TArray<FString> Params);
};
