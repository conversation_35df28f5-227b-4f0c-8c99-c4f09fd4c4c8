// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "TheAwakener_FO/GamePlay/Item/ItemObj.h"
#include "UObject/Object.h"
#include "ItemUseEffect.generated.h"

/**
 * 道具使用、投掷、附魔效果
 */
UCLASS()
class THEAWAKENER_FO_API UItemUseEffect : public UObject
{
	GENERATED_BODY()
public:
	//恢复百分比生命
	UFUNCTION(BlueprintCallable)
	static FItemUseResult RestoreHealth(FItemObj Item, AAwCharacter* User, TArray<FString> DesignerParams);

	//恢复固定生命
	UFUNCTION(BlueprintCallable)
	static FItemUseResult RestoreFixedHealth(FItemObj Item, AAwCharacter* User, TArray<FString> DesignerParams);

	//发射子弹
	UFUNCTION(BlueprintCallable)
	static FItemUseResult FireBullet(FItemObj Item, AAwCharacter* User, TArray<FString> DParams);

	UFUNCTION(BlueprintCallable)
	static FItemUseResult CreateAoeOnRoot(FItemObj Item, AAwCharacter* User, TArray<FString> DParams);

	UFUNCTION(BlueprintCallable)
	static FItemUseResult CreateAoeAttach(FItemObj Item, AAwCharacter* User, TArray<FString> DParams);
	
	//【特殊：奶酪】丢出奶酪 [0]水平飞行速度（厘米/秒）[1]水平速度递减（厘米/秒）  [2]上抛速度（厘米/秒） [3]下落速度 （厘米/秒）
	UFUNCTION(BlueprintCallable)
	static FItemUseResult ThrowCheese(FItemObj Item, AAwCharacter* User, TArray<FString> Params);

	//在使用者处生成对象
	UFUNCTION(BlueprintCallable)
	static FItemUseResult CreateTargetOnUserPoint(FItemObj Item, AAwCharacter* User, TArray<FString> Params);

	//Rogue 喝血瓶
	UFUNCTION(BlueprintCallable)
	static FItemUseResult UseRogueHealingPotion(FItemObj Item, AAwCharacter* User, TArray<FString> Params);
};
