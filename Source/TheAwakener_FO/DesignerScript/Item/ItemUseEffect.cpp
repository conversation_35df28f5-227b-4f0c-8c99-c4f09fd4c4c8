// Fill out your copyright notice in the Description page of Project Settings.


#include "ItemUseEffect.h"

//#include "K2Node_SpawnActorFromClass.h"
#include "Kismet/KismetMathLibrary.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GamePlay/Battle/OffenseManager.h"
#include "TheAwakener_FO/GamePlay/Roguelike/Item/RogueInterface.h"

FItemUseResult UItemUseEffect::RestoreHealth(FItemObj Item, AAwCharacter* User, TArray<FString> DesignerParams)
{
	const float RestPer = DesignerParams.Num() > 0 ? FCString::Atof(*DesignerParams[0]) : 0;
	const int RestVal = FMath::CeilToInt(User->CharacterObj.CurProperty.HP * RestPer);
	//TODO: 用的效果还没写
	UKismetSystemLibrary::PrintString(GWorld, FString("Go to restore ").Append(FString::FromInt(RestVal)).Append(" HP"));

	//UGameplayFuncLib::PlaySFXAttached()

	FOffenseInfo Offense = FOffenseInfo();
	Offense.AttackInfo = FAttackInfo();
	Offense.AttackInfo.DamagePower = FDamageValue(RestVal);
	Offense.AttackInfo.IsHeal = true;
	Offense.AttackInfo.DamageSourceType = EAttackSource::Bullet;
	Offense.AttackInfo.Elemental = User->GetElemental(EChaElemental::Physical, EElementalPickMethod::Set, ESlotInWeaponObj::MainWeapon);
	Offense.SourceId = Item.Model.Id;
	Offense.CanHitTimes = 1;
	//User->BeOffended(Offense, User, nullptr);
	UOffenseManager::DoBuffOffense(Offense, User, User);
	
	return FItemUseResult(true, 1, true);
}

FItemUseResult UItemUseEffect::RestoreFixedHealth(FItemObj Item, AAwCharacter* User, TArray<FString> DesignerParams)
{
	const int HPValue = DesignerParams.Num() > 0 ? FCString::Atoi(*DesignerParams[0]) : 0;
	//TODO: 用的效果还没写
	UKismetSystemLibrary::PrintString(GWorld, FString("Go to restore ").Append(FString::FromInt(HPValue)).Append(" HP"));

	//UGameplayFuncLib::PlaySFXAttached()

	FOffenseInfo Offense = FOffenseInfo();
	Offense.AttackInfo = FAttackInfo();
	Offense.AttackInfo.DamagePower = FDamageValue(HPValue);
	Offense.AttackInfo.IsHeal = true;
	Offense.AttackInfo.DamageSourceType = EAttackSource::Bullet;
	Offense.AttackInfo.Elemental = User->GetElemental(EChaElemental::Physical, EElementalPickMethod::Set, ESlotInWeaponObj::MainWeapon);
	Offense.SourceId = Item.Model.Id;
	Offense.CanHitTimes = 1;
	//User->BeOffended(Offense, User, nullptr);
	UOffenseManager::DoBuffOffense(Offense, User, User);
	
	return FItemUseResult(true, 1, true);
}

FItemUseResult UItemUseEffect::FireBullet(FItemObj Item, AAwCharacter* User, TArray<FString> DParams)
{
	FString BulletId = DParams.Num() > 0 ? DParams[0] : "";
	FString Speed = DParams.Num() > 2 ? DParams[2] : "400";
	FBulletModel Model = UGameplayFuncLib::GetAwDataManager()->GetBulletModelById(BulletId);
	if (BulletId.IsEmpty() == false && Model.Id == BulletId)
	{
		float AttackPower = DParams.Num() > 1 ? FCString::Atof(*DParams[1]) : 0;
		FOffenseInfo OInfo = FOffenseInfo();
		OInfo.AttackInfo = FAttackInfo();
		OInfo.AttackInfo.Active = true;
		OInfo.AttackInfo.IsHeal = false;
		OInfo.AttackInfo.DamagePower = FDamageValue(AttackPower);
		OInfo.AttackInfo.DamageSourceType = EAttackSource::Bullet;
		OInfo.AttackInfo.Elemental = EChaElemental::Fire;
		OInfo.SourceId = Item.Model.Id;
		OInfo.CanHitTimes = 1;

		FTransform Trans = User->GetTransformByBindPointId(Item.Model.AppearanceParts.Num() > 0 ? Item.Model.AppearanceParts[0].BindPointIds[static_cast<int32>(User->GetArmState())] : "");
		
		FBulletLauncher Launcher = FBulletLauncher(
			User,
			Model,
			Trans.GetLocation(),
			User->GetActorForwardVector(),
			10,
			"BulletScript.GoStraightAhead("+Speed+")",
			User->GetActorLocation()
		);
		UGameplayFuncLib::CreateBullet(Launcher);
	}
	return FItemUseResult(true, 1, true);
}

FItemUseResult UItemUseEffect::CreateAoeOnRoot(FItemObj Item, AAwCharacter* User, TArray<FString> DParams)
{
	FItemUseResult Res = FItemUseResult(true, 1, true);
	const FVector Pos = User->GetActorLocation() - FVector(0, 0, User->GetCapsuleComponent()->GetScaledCapsuleHalfHeight());
	const FString AoeID = DParams.Num() > 0 ? DParams[0] : "";
	const float LifeTime = DParams.Num() > 1 ? FCString::Atof(*DParams[1]) : 1;
	
	if (AoeID.IsEmpty() == false)
	{
		Res.Creaters.Add(UGameplayFuncLib::CreateAOE(User, AoeID, Pos, FVector::ForwardVector, LifeTime, ""));
	}
	
	return Res;
}

FItemUseResult UItemUseEffect::CreateAoeAttach(FItemObj Item, AAwCharacter* User, TArray<FString> DParams)
{
	FItemUseResult Res = FItemUseResult(true, 1, true);
	const FVector Pos = User->GetActorLocation() - FVector(0, 0, User->GetCapsuleComponent()->GetScaledCapsuleHalfHeight());
	const FString AoeID = DParams.Num() > 0 ? DParams[0] : "";
	const FString AttachSocketId = DParams.Num() > 1 ? DParams[1] : "";
	
	if (AoeID.IsEmpty() == false)
	{
		AAWAoe* AOE = UGameplayFuncLib::CreateAOE(User, AoeID, Pos, FVector::ForwardVector, 0, "");
		if(AOE)
		{
			AOE->AttachToActor(User,FAttachmentTransformRules::SnapToTargetNotIncludingScale,FName(AttachSocketId));
			Res.Creaters.Add(AOE);
		}
		return Res;
	}
	
	return FItemUseResult(true, 1, true);
}

FItemUseResult UItemUseEffect::ThrowCheese(FItemObj Item, AAwCharacter* User, TArray<FString> Params)
{
	FRotator AimRot = User->SetAimDirection();
	AimRot.Roll = User->GetActorRotation().Roll;
	AimRot.Yaw = User->GetActorRotation().Yaw;
	FVector AimDir = AimRot.Vector();
	
	FBulletModel BModel = UGameplayFuncLib::GetAwDataManager()->GetBulletModelById("ThrownCheese");
	FTransform Trans = User->GetTransformByBindPointId(Item.Model.AppearanceParts.Num() > 0 ? Item.Model.AppearanceParts[0].BindPointIds[static_cast<int32>(User->GetArmState())] : "");
	FString Effect = FString("BulletScript.ParabolicCurve(")
		.Append(Params.Num() > 0 ? Params[0] : "500").Append(",")
		.Append(Params.Num() > 1 ? Params[1] : "100").Append(",")
		.Append(Params.Num() > 2 ? Params[2] : "0.5").Append(",")
		.Append(FString::SanitizeFloat(AimRot.Pitch)).Append(")");
	FBulletLauncher BLauncher = FBulletLauncher(
		User, BModel, Trans.GetLocation(), User->GetActorRotation().Vector(), 99999, Effect, User->GetActorLocation()
	);
	
	AAwBullet* Bullet = UGameplayFuncLib::CreateBullet(BLauncher);
	
	return FItemUseResult(true, 1, true);
}

FItemUseResult UItemUseEffect::CreateTargetOnUserPoint(FItemObj Item, AAwCharacter* User, TArray<FString> Params)
{
	FItemUseResult Res = FItemUseResult(true, 1, true);
	FTransform BulletTransform;
	FVector Offset = FVector::ZeroVector;
	FVector ParamsOffset = FVector::ZeroVector;
	ParamsOffset.X = Params.Num() > 0 ?FCString::Atof(*Params[0]) : 0.f;
	ParamsOffset.Y = Params.Num() > 1 ?FCString::Atof(*Params[1]) : 0.f;
	ParamsOffset.Z = Params.Num() > 2 ?FCString::Atof(*Params[2]) : 0.f;
	
	if (User->GetCapsuleComponent())
	{
		Offset = FVector(0,0,-1*User->GetCapsuleComponent()->GetScaledCapsuleHalfHeight());
	}
	BulletTransform.SetLocation(User->GetActorLocation()+Offset);
	BulletTransform.SetRotation(User->GetActorRotation().Quaternion());

	FTransform BulletOffsetTransform = BulletTransform;

	BulletOffsetTransform.SetLocation( UKismetMathLibrary::TransformLocation(BulletTransform,ParamsOffset));
	
	for (auto Apperance: Item.Model.AppearanceParts)
	{
		FString BpPath = UResourceFuncLib::GetBpAssetPath(Apperance.BluePrintPath);
		UClass* BpClass = LoadClass<AActor>(nullptr, *BpPath);
		if (BpClass)
		{
			Res.Creaters.Add(GWorld->SpawnActor<AActor>(BpClass, BulletOffsetTransform));
		}
	}
	return Res;
}

FItemUseResult UItemUseEffect::UseRogueHealingPotion(FItemObj Item, AAwCharacter* User, TArray<FString> Params)
{
	UAwRogueItemSubSystem* SubSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueItemSubSystem>();
	if (!User)
	{
		return  FItemUseResult();
	}
	const int RestVal =FMath::RoundToInt(SubSystem->CurHealingPotion.GetSelfAfterModify().CurEffectValue+SubSystem->CurHealingPotion.GetSelfAfterModify().CurEffectLevel/100.f*User->CharacterObj.CurProperty.HP);

	FOffenseInfo Offense = FOffenseInfo();
	Offense.AttackInfo = FAttackInfo();
	Offense.AttackInfo.DamagePower = FDamageValue(RestVal);
	Offense.AttackInfo.IsHeal = true;
	Offense.AttackInfo.DamageSourceType = EAttackSource::Item ;
	Offense.AttackInfo.DamageType = EDamageType::ExtraDamage;
	Offense.AttackInfo.Elemental = User->GetElemental(EChaElemental::Physical, EElementalPickMethod::Set, ESlotInWeaponObj::MainWeapon);
	Offense.SourceId = Item.Model.Id;
	Offense.CanHitTimes = 1;
	//User->BeOffended(Offense, User, nullptr);
	UOffenseManager::DoBuffOffense(Offense, User, User);
	
	return FItemUseResult(true, 1, true);
	
}




