// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/Object.h"
#include "MoveStartFunc.generated.h"

/**
 * 移动相关的函数，这里写起步的Tween，这个版本凑效果的，下版本改进移动之后就没了
 */
UCLASS()
class THEAWAKENER_FO_API UMoveStartFunc : public UObject
{
	GENERATED_BODY()
public:
	//平方式启动
	UFUNCTION()
	static float Square(float TimeElapsed, float StartSpeedTimes, float EndSpeedTimes, float Duration, TArray<FString> Params);
};
