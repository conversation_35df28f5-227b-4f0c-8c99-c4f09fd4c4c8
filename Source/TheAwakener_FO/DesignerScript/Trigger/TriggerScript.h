// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "TheAwakener_FO/GamePlay/Trigger/TriggerManager.h"
#include "TriggerScript.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UTriggerScript : public UObject
{
	GENERATED_BODY()
public:	
	//Condition
	//Always
	UFUNCTION(BlueprintCallable)
	static bool TestAlwaysCondition(TArray<FString> Params);

	//玩家触碰到刷怪点 Params[0]: TriggerCatcherId
	UFUNCTION(BlueprintCallable)
	static bool PlayerTouchCatcher(TArray<FString> Params);

	//【MapOnCreate.Condition】 Params[0]:CampId Params[1]:CampProgressMin Params[2]:CampProgressMax
	UFUNCTION(BlueprintCallable)
	static bool CheckCurDungeonCampProgress(TArray<FString> Params);

	//【MapOnCreate.Condition】 Params[0]:DungeonId Params[1]:CampId Params[2]:CampProgressMin Params[3]:CampProgressMax
	UFUNCTION(BlueprintCallable)
	static bool CheckDungeonCampProgress(TArray<FString> Params);

	// 【MapOnCreate.Condition】检查某个NPC是否完成了初次对话，[0]NpcId
	UFUNCTION(BlueprintCallable)
	static bool HasNpcDoneFirstCommunication(TArray<FString> Params);

	//检测指定Tag标记的Character的数量小于输入值的 Params[0]: TriggerCatcherId Params[1]: CheckCharacterNum
	UFUNCTION(BlueprintCallable)
	static bool CheckCharacterNumByTag(TArray<FString> Params);

	//检测在GameState上的SwitchList Params[0]: SwitchKey Params[1]: SwitchValue
	UFUNCTION(BlueprintCallable)
	static bool CheckSwitchInGameState(TArray<FString> Params);

	//检测在Role上的SwitchList Params[0]: SwitchKey Params[1]: SwitchValue
	UFUNCTION(BlueprintCallable)
	static bool CheckSwitchInRole(TArray<FString> Params);

	//检测在Role上的SwitchList.Value是否大于 Params[0]: SwitchKey Params[1]: SwitchValue
	UFUNCTION(BlueprintCallable)
	static bool CheckSwitchInRoleGreater(TArray<FString> Params);

	//检测在Role上的SwitchList.Value是否小于 Params[0]: SwitchKey Params[1]: SwitchValue
	UFUNCTION(BlueprintCallable)
	static bool CheckSwitchInRoleLess(TArray<FString> Params);

	//检测在GameState上的SwitchList没有 Params[0]: SwitchKey
	UFUNCTION(BlueprintCallable)
	static bool CheckNoSwitchInGameState(TArray<FString> Params);

	//检测在Role上的SwitchList没有 Params[0]: SwitchKey
	UFUNCTION(BlueprintCallable)
	static bool CheckNoSwitchInRole(TArray<FString> Params);

	//检测在Role上的SwitchList Params[0]: SwitchKey >= Params[1]: SwitchValue
	UFUNCTION(BlueprintCallable)
	static bool CheckSwitchInRoleLargerOrEqualThan(TArray<FString> Params);

	//检测在GameState的AllCharacter.Value == Params[0]的结果数量小于Params[1]
	UFUNCTION(BlueprintCallable)
	static bool CheckCharacterWithTagLess(TArray<FString> Params);

	//检测在GameState的AllCharacter.Value == Params[0]的结果数量大于Params[1]
	UFUNCTION(BlueprintCallable)
	static bool CheckCharacterWithTagGreater(TArray<FString> Params);
	
	//Action
	//Tick:				TArray<FString> Params
	//OverlapBegin:		TArray<FString> Params
	//OverlapEnd:		TArray<FString> Params
	//Timer:			TArray<FString> Params
	//CharacterDead:	TArray<FString> Params
	//SceneItemDestroy: TArray<FString> Params
	//DialogEnd:		TArray<FString> Params
	//MapOnCreate:		FString LevelName, FTransform LevelTrans, TArray<FString> Params
	//MapOnRemoved:		FString LevelName, TArray<FString> Params

	//
	UFUNCTION(BlueprintCallable)
	static UTimelineNode* TestTriggerAction(TArray<FString> Params);

	//【OverLapBegin.Action】 根据TriggerCatcher的SpawnPointList里的数据刷怪
	UFUNCTION(BlueprintCallable)
	static UTimelineNode* SpawnNPCBySpawnInfo(TArray<FString> Params);

	//【MapOnCreate.Action】 根据TriggerCatcher的SpawnPointList里的数据刷怪
	UFUNCTION(BlueprintCallable)
	static UTimelineNode* SpawnNPCBySpawnInfoOnMapCreate(FString LevelName, FTransform LevelTrans, TArray<FString> Params);

	//【Action】 在GameState里添加Switch的值
	UFUNCTION(BlueprintCallable)
	static UTimelineNode* SetSwitchValueInGameState(TArray<FString> Params);

	//【Action】 在Role里添加Switch的值
	UFUNCTION(BlueprintCallable)
	static UTimelineNode* SetSwitchValueInRole(TArray<FString> Params);

	//【Action】 自动检查增加矿洞故事的进度用，MineStoryStep变化检测
	UFUNCTION(BlueprintCallable)
	static UTimelineNode* CheckForSetMineStoryStep(TArray<FString> Params);

	UFUNCTION(BlueprintCallable)
	static UTimelineNode* ElevatorFixed(TArray<FString> Params);

	UFUNCTION(BlueprintCallable)
	static UTimelineNode* GetElevatorStick(TArray<FString> Params);

	UFUNCTION(BlueprintCallable)
	static UTimelineNode* GetVendorGoods(TArray<FString> Params);

	UFUNCTION(BlueprintCallable)
	static UTimelineNode* GetBlacksmithTool(TArray<FString> Params);

	//【Action】播放某个BGM，[0]=BGM的Key，如"Intro"等
	UFUNCTION(BlueprintCallable)
	static UTimelineNode* PlayBGM(TArray<FString> Params);

	//【Action】根据进入VillageMain的Switch不同，播放不同的BGM
	UFUNCTION(BlueprintCallable)
	static UTimelineNode* SetBGMOnEnterVillageMain(TArray<FString> Params);

	//【Action】回到城里
	UFUNCTION(BlueprintCallable)
	static UTimelineNode* BackToVillage(TArray<FString> Params);

	//【Action】回城播放动画片
	UFUNCTION(BlueprintCallable)
	static UTimelineNode* PlayAnimWhileBackToVillage(TArray<FString> Params);

	//【Action】物品进玩家背包
	//Params里是这个宝箱一定会出的物品Id
	UFUNCTION(BlueprintCallable)
	static UTimelineNode* PlayerGetItemByPackageIds(TArray<FString> Params);

	//【Action】打碎场景物件给玩家金币
	//Params[0]:获得金币的概率
	//Params[1]:金币基础值
	//Params[2]:金币浮动值
	UFUNCTION(BlueprintCallable)
	static UTimelineNode* PlayerGetGoldCoinOnSceneItemDestroy(AAwSceneItem* SceneItem, TArray<FString> Params);

	//是否有正在播放全局的LevelSequence
	UFUNCTION(BlueprintCallable)
		static bool CheckLevelSequencePlaying(TArray<FString> Params);
	
};
