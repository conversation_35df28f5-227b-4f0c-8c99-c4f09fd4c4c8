// Fill out your copyright notice in the Description page of Project Settings.


#include "TriggerScript.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GameFramework/Manager/AwRogueDataSystem.h"
#include "TheAwakener_FO/GamePlay/Quest/AwQuestComponent.h"
#include "TheAwakener_FO/GamePlay/GameMode/AwGameMode_RandomDungeon.h"
#include "TheAwakener_FO/UI/MediaPlayerUI.h"

bool UTriggerScript::TestAlwaysCondition(TArray<FString> Params)
{
	return true;
}

bool UTriggerScript::PlayerTouchCatcher(TArray<FString> Params)
{
	if(!Params.Num()) return false;
	const FString CatcherId = Params[0];
	AAwGameState* GameState = UGameplayFuncLib::GetAwGameState();
	if(GameState->TriggerCatcherList.Contains(CatcherId))
	{
		const ATriggerCatcher* Catcher = GameState->TriggerCatcherList[CatcherId];
		if(Catcher)
		{
			if(Catcher->EnterActor.Contains(GameState->GetMyCharacter()))
				return true;
		}
	}
	
	return false;
}

bool UTriggerScript::CheckCurDungeonCampProgress(TArray<FString> Params)
{
	if(Params.Num() < 3) return false;
	const FString CampId = Params[0];
	const int CampProgressMin = FCString::Atoi(*Params[1]);
	const int CampProgressMax = FCString::Atoi(*Params[2]);
	if(CampProgressMax < CampProgressMin) return false;
	if(UGameplayFuncLib::GetAwGameMode())
	{
		const AAwGameMode_RandomDungeon* DungeonGameMode = Cast<AAwGameMode_RandomDungeon>(UGameplayFuncLib::GetAwGameMode());
		if(DungeonGameMode)
		{
			FAwDungeonSave DungeonSave = UGameplayFuncLib::GetAwGameInstance()->RoleInfo.GetDungeonRecordByDungeonId(DungeonGameMode->CurDungeonInfo.Id);
			for(auto CampRecord : DungeonSave.Camps)
			{
				if(CampRecord.CampId == CampId)
				{
					if(CampRecord.CampProgress <= CampProgressMax && CampRecord.CampProgress >= CampProgressMin)
						return true;
					break;
				}
			}
		}
	}
	return false;
}

bool UTriggerScript::CheckDungeonCampProgress(TArray<FString> Params)
{
	if(Params.Num() < 4) return false;
	const FString DungeonId = Params[0];
	const FString CampId = Params[1];
	const int CampProgressMin = FCString::Atoi(*Params[2]);
	const int CampProgressMax = FCString::Atoi(*Params[3]);
	if(CampProgressMax < CampProgressMin) return false;
	if(UGameplayFuncLib::GetAwGameMode())
	{
		bool bFindCampId = false;
		FAwDungeonSave DungeonSave = UGameplayFuncLib::GetAwGameInstance()->RoleInfo.GetDungeonRecordByDungeonId(DungeonId);
		for(auto CampRecord : DungeonSave.Camps)
		{
			if(CampRecord.CampId == CampId)
			{
				if(CampRecord.CampProgress <= CampProgressMax && CampRecord.CampProgress >= CampProgressMin)
					return true;
				bFindCampId = true;
				break;
			}
		}
		if(!bFindCampId)
		{
			const int CurCampProgress = 0;
			if(CurCampProgress <= CampProgressMax && CurCampProgress >= CampProgressMin)
				return true;
		}
	}
	return false;
}

bool UTriggerScript::HasNpcDoneFirstCommunication(TArray<FString> Params)
{
	if (Params.Num() < 1 || !UAwGameInstance::Instance) return false;
	const FString NpcId = Params[0];
	const FString SwitchKey = NpcId + "_CommunicationTimes";
	return UAwGameInstance::Instance->RoleInfo.GetSwitch(SwitchKey) < 1;
}


bool UTriggerScript::CheckCharacterNumByTag(TArray<FString> Params)
{
	if(Params.Num() < 2) return false;
	const FString CatcherId = Params[0];
	const int CheckNum = FCString::Atoi(*Params[1]);
	AAwGameState* GameState = UGameplayFuncLib::GetAwGameState();
	if(GameState)
	{
		int CurCharacterNum = 0;
		for(auto Character : GameState->AllCharacters)
		{
			if(Character.Value == CatcherId)
				CurCharacterNum++;
		}
		if(CurCharacterNum <= CheckNum)
			return true;
	}
	return false;
}

bool UTriggerScript::CheckSwitchInGameState(TArray<FString> Params)
{
	if(!Params.Num()) return false;
	const FString SwitchKey = Params[0];
	AAwGameState* GameState = UGameplayFuncLib::GetAwGameState();
	if(GameState)
	{
		if(Params.Num() > 1)
		{
			const int CheckValue = FCString::Atoi(*Params[1]);
			if(GameState->SwitchList.Contains(SwitchKey))
			{
				return (CheckValue == GameState->SwitchList[SwitchKey]);
			}
			else
			{
				return CheckValue == 0;
			}
		}
		else
		{
			if(GameState->SwitchList.Contains(SwitchKey))
			{
				if(GameState->SwitchList[SwitchKey] == 0)
					return false;
				return true;
			}
			else
				return true;
		}
	}
	return false;
}

bool UTriggerScript::CheckSwitchInRole(TArray<FString> Params)
{
	if(!Params.Num()) return false;
	const FString SwitchKey = Params[0];
	UAwGameInstance* GameInstance = UGameplayFuncLib::GetAwGameInstance();
	if(GameInstance)
	{
		const int CheckValue = Params.Num() > 1 ? FCString::Atoi(*Params[1]) : 0;
		const int MyValue = GameInstance->RoleInfo.GetSwitch(SwitchKey);
		return CheckValue == MyValue;
	}
	return false;
}

bool UTriggerScript::CheckSwitchInRoleGreater(TArray<FString> Params)
{
	if(Params.Num() < 2) return false;
	const FString SwitchKey = Params[0];
	const int CheckValue = FCString::Atoi(*Params[1]);
	UAwGameInstance* GameInstance = UGameplayFuncLib::GetAwGameInstance();
	if(GameInstance)
	{
			return (GameInstance->RoleInfo.GetSwitch(SwitchKey) > CheckValue);
	}
	return false;
}

bool UTriggerScript::CheckSwitchInRoleLess(TArray<FString> Params)
{
	if(Params.Num() < 2) return false;
	const FString SwitchKey = Params[0];
	const int CheckValue = FCString::Atoi(*Params[1]);
	UAwGameInstance* GameInstance = UGameplayFuncLib::GetAwGameInstance();
	if(GameInstance)
	{
			return (GameInstance->RoleInfo.GetSwitch(SwitchKey) < CheckValue);
	}
	return false;
}

bool UTriggerScript::CheckNoSwitchInGameState(TArray<FString> Params)
{
	if(!Params.Num()) return false;
	const FString SwitchKey = Params[0];
	AAwGameState* GameState = UGameplayFuncLib::GetAwGameState();
	if(GameState)
	{
		if(GameState->SwitchList.Contains(SwitchKey))
		{
			if(GameState->SwitchList[SwitchKey] == 0)
				return true;
			return false;
		}
	}
	return true;
}

bool UTriggerScript::CheckNoSwitchInRole(TArray<FString> Params)
{
	if(!Params.Num()) return false;
	const FString SwitchKey = Params[0];
	UAwGameInstance* GameInstance = UGameplayFuncLib::GetAwGameInstance();
	if(GameInstance)
	{
		return (GameInstance->RoleInfo.GetSwitch(SwitchKey) == 0);
	}
	return true;
}

bool UTriggerScript::CheckSwitchInRoleLargerOrEqualThan(TArray<FString> Params)
{
	if(!Params.Num()) return false;
	const FString SwitchKey = Params[0];
	UAwGameInstance* GameInstance = UGameplayFuncLib::GetAwGameInstance();
	if(GameInstance)
	{
		const int CheckValue = Params.Num() > 1 ? FCString::Atoi(*Params[1]) : 0;
		const int MyValue = GameInstance->RoleInfo.GetSwitch(SwitchKey);
		return CheckValue <= MyValue;
	}
	return false;
}

bool UTriggerScript::CheckCharacterWithTagLess(TArray<FString> Params)
{
	AAwGameState* GameState = UGameplayFuncLib::GetAwGameState();
	if(GameState)
	{
		if(Params.Num() > 1)
		{
			const int CheckNum = FCString::Atoi(*Params[1]);
			if(GameState->GetCharactersByTag(Params[0]).Num() < CheckNum)
				return true;
		}
		
	}
	return false;
}

bool UTriggerScript::CheckCharacterWithTagGreater(TArray<FString> Params)
{
	AAwGameState* GameState = UGameplayFuncLib::GetAwGameState();
	if(GameState)
	{
		if(Params.Num() > 1)
		{
			const int CheckNum = FCString::Atoi(*Params[1]);
			if(GameState->GetCharactersByTag(Params[0]).Num() > CheckNum)
				return true;
		}
		
	}
	return false;
}

UTimelineNode* UTriggerScript::TestTriggerAction(TArray<FString> Params)
{
	UTimelineNode* NewTimelineNode = NewObject<UTimelineNode>();
	NewTimelineNode->FuncClassPath = "TimelineNodeScript";
	NewTimelineNode->FuncName = "TestTriggerTimeline";

	return NewTimelineNode;
}

UTimelineNode* UTriggerScript::SpawnNPCBySpawnInfo(TArray<FString> Params)
{
	if(!Params.Num()) return nullptr;
	const FString UniqueId = Params[0];
	if(UGameplayFuncLib::GetAwGameState()->NPCSpawnInfoList.Contains(UniqueId))
	{
		const FNPCSpawnInfo NPCSpawnInfo = UGameplayFuncLib::GetAwGameState()->NPCSpawnInfoList[UniqueId];
		if (NPCSpawnInfo.NpcInfo.MobModelId == "") return nullptr;
		FString SpawnTag = "";
		if(Params.Num() > 1)
			SpawnTag = Params[1];
		AAwCharacter* NewNPC = UGameplayFuncLib::CreateCharacterBySpawnInfo(NPCSpawnInfo, SpawnTag);
		if(!NewNPC)
		{
			return nullptr;
		}
		UAwQuestComponent* QuestComponent = Cast<UAwQuestComponent>(NewNPC->AddComponentByClass(UAwQuestComponent::StaticClass(),false,FTransform::Identity,true));
		QuestComponent->MyQuestData = NPCSpawnInfo.QuestData;
		NewNPC->GetReady();
	}
	return nullptr;
}

UTimelineNode* UTriggerScript::SpawnNPCBySpawnInfoOnMapCreate(FString LevelName, FTransform LevelTrans, TArray<FString> Params)
{
	if(!Params.Num()) return nullptr;
	const FString UniqueId = Params[0];
	if(UGameplayFuncLib::GetAwGameState()->NPCSpawnInfoList.Contains(UniqueId))
	{
		const FNPCSpawnInfo NPCSpawnInfo = UGameplayFuncLib::GetAwGameState()->NPCSpawnInfoList[UniqueId];
		if (NPCSpawnInfo.NpcInfo.MobModelId == "") return nullptr;
		FString SpawnTag = "";
		if(Params.Num() > 1)
			SpawnTag = Params[1];
		AAwCharacter* NewNPC = UGameplayFuncLib::CreateCharacterBySpawnInfo(NPCSpawnInfo, SpawnTag);
		
		UAwQuestComponent* QuestComponent = Cast<UAwQuestComponent>(NewNPC->AddComponentByClass(UAwQuestComponent::StaticClass(),false,FTransform::Identity,true));
		QuestComponent->MyQuestData = NPCSpawnInfo.QuestData;
		if(NewNPC)
			NewNPC->GetReady();
	}
	return nullptr;
}

UTimelineNode* UTriggerScript::SetSwitchValueInGameState(TArray<FString> Params)
{
	if(!Params.Num()) return nullptr;
	const FString SwitchKey = Params[0];
	int SwitchValue = 1;
	if(Params.Num() > 1)
	{
		SwitchValue = FCString::Atoi(*Params[1]);
	}
	AAwGameState* GameState = UGameplayFuncLib::GetAwGameState();
	if(GameState)
	{
		GameState->SwitchList.Add(SwitchKey, SwitchValue);
	}
	return nullptr;
}

UTimelineNode* UTriggerScript::SetSwitchValueInRole(TArray<FString> Params)
{
	if(!Params.Num()) return nullptr;
	const FString SwitchKey = Params[0];
	int SwitchValue = 1;
	if(Params.Num() > 1)
	{
		SwitchValue = FCString::Atoi(*Params[1]);
	}
	UAwGameInstance* GameInstance = UGameplayFuncLib::GetAwGameInstance();
	if(GameInstance)
	{
		GameInstance->RoleInfo.SetSwitchValue(SwitchKey, SwitchValue);
	}
	return nullptr;
}

UTimelineNode* UTriggerScript::CheckForSetMineStoryStep(TArray<FString> Params)
{
	if (!UGameplayFuncLib::GetAwGameInstance() )
	{
		return nullptr;
	}
	
	const bool OgreKilled = UGameplayFuncLib::GetAwGameInstance()->RoleInfo.GetSwitch("Ogre_Killed") > 0;
	const int RatMan = UGameplayFuncLib::GetAwGameInstance()->RoleInfo.GetSwitch("RatManMin", 1000) ;
	const int Goblin = UGameplayFuncLib::GetAwGameInstance()->RoleInfo.GetSwitch("GoblinMin", 1000) ;

	int ToValue = UGameplayFuncLib::GetAwGameInstance()->RoleInfo.GetSwitch("MineStoryStep");
	if (OgreKilled)
	{
		const int Human = 2000 - RatMan - Goblin;
		if (Human >= 1700)
		{
			ToValue = FMath::Max(ToValue, 7);
		}else if (Human >= 1200)
		{
			ToValue = FMath::Max(ToValue, 6);
		}else if (Human >= 900)
		{
			ToValue = FMath::Max(ToValue, 5);
		}else if (RatMan <= 500)
		{
			ToValue = FMath::Max(ToValue, 4);
		} else if (RatMan <= 800)
		{
			ToValue = FMath::Max(ToValue, 3);
		}
	}else
	{
		if (RatMan <= 800)
		{
			ToValue = FMath::Max(ToValue, 2);
		}
	}

	UGameplayFuncLib::GetAwGameInstance()->RoleInfo.SetSwitchValue("MineStoryStep", ToValue);
	
	return nullptr;
}

UTimelineNode* UTriggerScript::ElevatorFixed(TArray<FString> Params)
{
	//UGameplayFuncLib::GetUiManager()->ShowToast("Elevator_Fixed");
	
	AActor* SeatAndStick = UResourceFuncLib::SpawnActorByBP("Core/Item/Elevator/ElevatorSeatAndStick", FTransform::Identity);
	SeatAndStick->SetActorLocation(FVector(148,224,1700));
	
	UGameplayFuncLib::SaveGame();

	// UTimelineNode* Node = NewObject<UTimelineNode>();
	// Node->FuncClassPath = "UIList";
	// Node->FuncName = "ShowUI";
	// Node->Params.Add("BackToVillage");
	// Node->Params.Add("2");
	// UGameplayFuncLib::GetTimelineManager()->AddNode(Node);
	
	UGameplayFuncLib::GetUiManager()->Show("BackToVillage");
	
	return nullptr;
}

UTimelineNode* UTriggerScript::GetElevatorStick(TArray<FString> Params)
{
	UGameplayFuncLib::GetUiManager()->ShowToast("Get_Elevator_Stick");
	return nullptr;
}

UTimelineNode* UTriggerScript::GetVendorGoods(TArray<FString> Params)
{
	UGameplayFuncLib::GetUiManager()->ShowToast("Get_Vendor_Goods");
	return nullptr;
}

UTimelineNode* UTriggerScript::GetBlacksmithTool(TArray<FString> Params)
{
	UGameplayFuncLib::GetUiManager()->ShowToast("Get_Blacksmith_Tool");
	return nullptr;
}

UTimelineNode* UTriggerScript::PlayBGM(TArray<FString> Params)
{
	if (Params.Num() < 1) return nullptr;
	if (UGameplayFuncLib::GetBGMManager())
		UGameplayFuncLib::PlayBgmByKey(Params[0]);
	
	return nullptr;
}

UTimelineNode* UTriggerScript::SetBGMOnEnterVillageMain(TArray<FString> Params)
{
	const bool FirstEnterVillage = UGameplayFuncLib::GetAwGameInstance()->RoleInfo.GetSwitch("MineStoryStep") <= 0;
	const FString Key = FirstEnterVillage ? "Envir_WakeUp" : "Envir_Village";
	
	if (UGameplayFuncLib::GetBGMManager())
		UGameplayFuncLib::PlayBgmByKey(Key);
	
	return nullptr;
}

UTimelineNode* UTriggerScript::BackToVillage(TArray<FString> Params)
{
	UGameplayFuncLib::GetAwGameInstance()->UIManager->Show("BackToVillage");
	//UGameplayFuncLib::GetAwGameInstance()->UIManager->ShowLoading();
	//UGameplayFuncLib::GetAwGameInstance()->BackToVillage();
	return nullptr;
}

UTimelineNode* UTriggerScript::PlayAnimWhileBackToVillage(TArray<FString> Params)
{
	FString ToPlay;
	TArray<FString> ToSetSwitchKey;
	//UKismetSystemLibrary::PrintString(UGameplayFuncLib::GetAwGameInstance(), FString("PlayAnimWhileBackToVillage"), true, true, FLinearColor::Red, 30);
	for (int i = 1; i <= 7; i++)
	{
		const FString ThisSwitchKey = FString("Anim_MineStoryStep").Append(FString::FromInt(i));
		if (
			UGameplayFuncLib::GetAwGameInstance()->RoleInfo.GetSwitch(FString("MineStoryStep")) == i &&
			UGameplayFuncLib::GetAwGameInstance()->RoleInfo.GetSwitch(ThisSwitchKey) <= 0
			)
		{
			ToPlay = FString("./MoviesInGame/MineStoryStep_").Append(FString::FromInt(i)).Append(".mp4");
			ToSetSwitchKey.Add(ThisSwitchKey);
		}
	}
	
	for (int i = 1; i <= 3; i++)
	{
		const FString ThisSwitchKey = FString("Anim_VagabondageVendor_Step").Append(FString::FromInt(i));
		if (
			UGameplayFuncLib::GetAwGameInstance()->RoleInfo.GetSwitch(FString("VagabondageVendor_Step")) == i &&
			UGameplayFuncLib::GetAwGameInstance()->RoleInfo.GetSwitch(ThisSwitchKey) <= 0
		)
		{
			if (ToPlay.IsEmpty()) ToPlay = FString("./MoviesInGame/VagabondageVendorStep_").Append(FString::FromInt(i)).Append(".mp4");
			ToSetSwitchKey.Add(ThisSwitchKey);
			break;
		}
	}
	
	if (ToPlay.IsEmpty()) return nullptr;

	UKismetSystemLibrary::PrintString(UGameplayFuncLib::GetAwGameInstance(), FString("ToPlay :: ").Append(ToPlay), true, true, FLinearColor::Yellow, 30);
	
	UMediaPlayerUI* MediaUI = Cast<UMediaPlayerUI>(UGameplayFuncLib::GetUiManager()->Show("MediaPlayer", 1));
	if(MediaUI && MediaUI->Playing() == false)
	{
		UFileMediaSource* MediaSource = NewObject<UFileMediaSource>(GWorld, UFileMediaSource::StaticClass(), "VillageUpgrade");
		MediaSource->SetFilePath(ToPlay);
		MediaUI->StartPlayMedia(MediaSource,false);
		for (const FString SwitchKey : ToSetSwitchKey) 
			UGameplayFuncLib::GetAwGameInstance()->RoleInfo.SetSwitchValue(SwitchKey, 1);
	}

	return nullptr;
}

UTimelineNode* UTriggerScript::PlayerGetItemByPackageIds(TArray<FString> Params)
{
	if(Params.Num() <= 0) return nullptr;
	
	TArray<FString> LootPackageIds = Params;

	FVector Loc = FVector::ZeroVector;

	if(UGameplayFuncLib::GetAwGameState()->GetMyCharacter())
		Loc = UGameplayFuncLib::GetAwGameState()->GetMyCharacter()->GetActorLocation();

	UGameplayFuncLib::GetLootManager()->DropLootPackageByLootPackageIds(LootPackageIds,Loc);
	
	return nullptr;
}

UTimelineNode* UTriggerScript::PlayerGetGoldCoinOnSceneItemDestroy(AAwSceneItem* SceneItem, TArray<FString> Params)
{
	if(UGameplayFuncLib::GetAwGameState()->GetMyCharacter())
	{
		if(FVector::Dist(UGameplayFuncLib::GetAwGameState()->GetMyCharacter()->GetActorLocation(), SceneItem->GetActorLocation()) > 750)
			return nullptr;
	}
	
	if(Params.Num() <= 1) return nullptr;
	const float GetCoinPercent = FCString::Atof(*Params[0]);
	const int RandIndex = FMath::RandRange(0,100);
	if(RandIndex > GetCoinPercent) return nullptr;
	
	FThingObj GoldCoinObj;
	GoldCoinObj.Id = "Gold";
	GoldCoinObj.Type = EThingType::Currency;
	int GoldCoinCount = FCString::Atoi(*Params[1]);
	if(Params.Num() > 2)
		GoldCoinCount += FMath::RandRange(FCString::Atoi(*Params[2]) * -1, FCString::Atoi(*Params[2]));
	GoldCoinObj.Count = GoldCoinCount;
	
	UGameMain* MainUI = nullptr;

	if (UGameplayFuncLib::GetUiManager()->OpenedWidgets.Contains("GameMain"))
		MainUI = Cast<UGameMain>(UGameplayFuncLib::GetUiManager()->OpenedWidgets["GameMain"]);

	UKismetSystemLibrary::PrintString(GWorld, "------------- Pick Up Loots!");
	
	UGameplayFuncLib::GetAwGameInstance()->RoleInfo.GiveThing(GoldCoinObj);
	if (GoldCoinObj.Type != EThingType::Equipment &&
		GoldCoinObj.Type != EThingType::WeaponModel &&
		GoldCoinObj.Type != EThingType::WeaponObj)
	{
		if (MainUI)
			MainUI->NewThingHint(GoldCoinObj);
		UKismetSystemLibrary::PrintString(GWorld,FString("ID:").Append(GoldCoinObj.Id)
			.Append("   Count:").Append(FString::FromInt(GoldCoinObj.Count)));
	}
	
	return nullptr;
}

bool UTriggerScript::CheckLevelSequencePlaying(TArray<FString> Params)
{
	if (!UGameplayFuncLib::GetAwGameInstance())
	{
		return false;
	}
	UAwSequenceManager* Manager = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwSequenceManager>();
	const bool CheckPlay = Params.Num() > 0 ? Params[0] == "Play" : true;
	const bool IsGlobal = Params.Num()>1 ?Params[1]=="Global": true;
	if (Manager)
	{
		if (IsGlobal)
		{
			return Manager->IsGlobalSequencePlaying()== CheckPlay;
		}
		else
		{
			return Manager->IsAnyLocalSequencePlaying() == CheckPlay;
		}
	}
	return false;
}
