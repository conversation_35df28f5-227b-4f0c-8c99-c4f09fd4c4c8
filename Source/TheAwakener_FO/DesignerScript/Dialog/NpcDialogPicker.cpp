// Fill out your copyright notice in the Description page of Project Settings.


#include "NpcDialogPicker.h"

#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GameFramework/Manager/AwRogueDataSystem.h"
#include "TheAwakener_FO/FunctionLibrary/CallFuncLib.h"

FString UNpcDialogPicker::VagabondageVendorDialogPicker(FNpcInfo NpcInfo, TArray<FString> Params)
{
	FString Res = NpcInfo.Personality.DialogModelId;
	const FString ComKey = NpcInfo.Id + "_CommunicationTimes";

	const bool HasNotCommunicated =
		UGameplayFuncLib::GetAwGameInstance()->RoleInfo.GetSwitch(ComKey) <= 0;

	if (HasNotCommunicated) return "VagabondageVendor_FirstDialog";

	if(UGameplayFuncLib::GetAwGameMode())
	{
		int HumanPower = 0;
		FAwDungeonSave DungeonSave = UGameplayFuncLib::GetAwGameInstance()->RoleInfo.GetDungeonRecordByDungeonId("TestDungeon");
		for(auto CampRecord : DungeonSave.Camps)
		{
			if(CampRecord.CampId == "RatMan" || CampRecord.CampId == "Goblin")
			{
				HumanPower += 1000 - CampRecord.CampProgress;
			}
		}

		if (HumanPower >= 2000)
		{
			return "VagabondageVendor_SpeakOnMine200";
		}else if (HumanPower >= 1200)
		{
			return "VagabondageVendor_SpeakOnMine120";
		}else if (HumanPower >= 600)
		{
			return "VagabondageVendor_SpeakOnMine60";	
		}else
		{
			return "VagabondageVendor_SpeakOnMine0";
		}
	}

	return Res;
}

FString UNpcDialogPicker::BlacksmithDialogPicker(FNpcInfo NpcInfo, TArray<FString> Params)
{
	const int RodianStory02Step = UGameplayFuncLib::GetAwGameInstance()->RoleInfo.GetSwitch("RodianStory03") ;
	if(RodianStory02Step < 1)
		return "";
	const int SmithToolStep = UGameplayFuncLib::GetAwGameInstance()->RoleInfo.GetSwitch("RodianBlackSmithMission01") ;

	if (SmithToolStep == 0)
	{
		return "Blacksmith_SideQuestA";
	}
	else if (SmithToolStep == 2)
	{
		return "Blacksmith_SideQuestD";
	}
	else if (SmithToolStep == 3)
	{
		return "Blacksmith_SideQuestD2";
	}
	return "";
}

FString UNpcDialogPicker::RodianCaptainDialogPicker(FNpcInfo NpcInfo, TArray<FString> Params)
{
	const int RodianStory05Step = UGameplayFuncLib::GetAwGameInstance()->RoleInfo.GetSwitch("RodianStory05");
	if(RodianStory05Step == 1)
		return "CaptainSmith_MainQuest5a";
	else if(RodianStory05Step == 3)
		return "CaptainSmith_MainQuest5b";

	const int RodianStory04Step = UGameplayFuncLib::GetAwGameInstance()->RoleInfo.GetSwitch("RodianStory04");
	if(RodianStory04Step == 1)
		return "CaptainSmith_MainQuest4a";
	else if(RodianStory04Step == 2)
		return "CaptainSmith_MainQuest4c";
	else if(RodianStory04Step == 4)
		return "CaptainSmith_MainQuest4d";

	const int RodianStory03Step = UGameplayFuncLib::GetAwGameInstance()->RoleInfo.GetSwitch("RodianStory03");
	if(RodianStory03Step == 1)
		return "CaptainSmith_MainQuest3a";
	else if(RodianStory03Step == 2)
		return "CaptainSmith_MainQuest3b";
	else if(RodianStory03Step == 3)
		return "CaptainSmith_MainQuest3c";

	const int RodianStory02Step = UGameplayFuncLib::GetAwGameInstance()->RoleInfo.GetSwitch("RodianStory02");
	if(RodianStory02Step == 3)
		return "CaptainSmith_MainQuest2b";
	else if(RodianStory02Step == 4)
        return "CaptainSmith_MainQuest2c";
	
	const int RodianStory01Step = UGameplayFuncLib::GetAwGameInstance()->RoleInfo.GetSwitch("RodianStory01");
	if(RodianStory01Step == 2)
		return "CaptainSmith_MainQuest1b";
	
	return "";
}

FString UNpcDialogPicker::RodianRogarosDialogPicker(FNpcInfo NpcInfo, TArray<FString> Params)
{
	const int RodianStory02Step = UGameplayFuncLib::GetAwGameInstance()->RoleInfo.GetSwitch("RodianStory02");
	if(RodianStory02Step == 3)
		return "GuardRogaros_MianQuest2b";
	else if(RodianStory02Step == 4)
		return "GuardRogaros_MianQuest2c";
	else if(RodianStory02Step == 5)
		return "GuardRogaros_MianQuest2cNormal";
	return NpcInfo.Personality.DialogModelId;
}

FString UNpcDialogPicker::NormalDialogPicker(FNpcInfo NpcInfo, TArray<FString> Params)
{
	FString Res = NpcInfo.Personality.DialogModelId;
	const FString ComKey = NpcInfo.Id + "_CommunicationTimes";

	const bool HasNotCommunicated =
		UGameplayFuncLib::GetAwGameInstance()->RoleInfo.GetSwitch(ComKey) <= 0;

	const bool GoblinNotOpen =
		UGameplayFuncLib::GetAwGameInstance()->RoleInfo.GetSwitch("UnlockMineGoblin") <= 0;

 	if (HasNotCommunicated) return NpcInfo.Id + "_FirstDialog";

	if(UGameplayFuncLib::GetAwGameMode())
	{
		int HumanPower = 0;
		int RatPower = 0;
		int GoblinPower = 0;
		FAwDungeonSave DungeonSave = UGameplayFuncLib::GetAwGameInstance()->RoleInfo.GetDungeonRecordByDungeonId("TestDungeon");
		for(auto CampRecord : DungeonSave.Camps)
		{
			if (CampRecord.CampId == "RatMan")
			{
				HumanPower += 1000 - CampRecord.CampProgress;
				RatPower += CampRecord.CampProgress;
			}else if (CampRecord.CampId == "Goblin")
			{
				HumanPower += 1000 - CampRecord.CampProgress;
				GoblinPower += CampRecord.CampProgress;
			}
		}

		TArray<FString> MaySay;

		if (HumanPower >= 2000)
		{
			MaySay.Add(NpcInfo.Id + "_SpeakOnMine200");
		}else if (HumanPower >= 1200)
		{
			MaySay.Add(NpcInfo.Id + "_SpeakOnMine120");
		}else if (HumanPower >= 600)
		{
			MaySay.Add(NpcInfo.Id + "_SpeakOnMine60");	
		}else
		{
			MaySay.Add(NpcInfo.Id + "_SpeakOnMine0");
		}
		if (RatPower >= 1000)
		{
			MaySay.Add(NpcInfo.Id + "_WereRat100");
		}else if (RatPower >= 600)
		{
			MaySay.Add(NpcInfo.Id + "_WereRat60");
		}else if (RatPower >= 300)
		{
			MaySay.Add(NpcInfo.Id + "_WereRat30");
		}else
		{
			MaySay.Add(NpcInfo.Id + "_WereRat0");
		}
		if (GoblinPower >= 1000 && GoblinNotOpen == false)
		{
			MaySay.Add(NpcInfo.Id + "_Goblin100");
		}else if (GoblinPower >= 600 && GoblinNotOpen == false)
		{
			MaySay.Add(NpcInfo.Id + "_Goblin60");
		}else if (GoblinPower >= 300 && GoblinNotOpen == false)
		{
			MaySay.Add(NpcInfo.Id + "_Goblin30");
		}else if ( GoblinNotOpen == false)
		{
			MaySay.Add(NpcInfo.Id + "_Goblin0");
		}

		if (MaySay.Num() == 1)
		{
			return MaySay[0];
		}else if (MaySay.Num() > 1)
		{
			const int RIndex = FMath::RandRange(0, MaySay.Num() - 1);
			return MaySay[RIndex];
		}
	}

	return Res;
}

FString UNpcDialogPicker::RogueGerassoFirstDialogPicker(FNpcInfo NpcInfo, TArray<FString> Params)
{
	UAwRogueDataSystem* SubSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	if (SubSystem)
	{
		if(SubSystem->GetIsClearedGame())
		{
			if(SubSystem->GetSwitch("Gerasso_WeeklyRound_VeryFirst_Dialog") == 0)
			{
				return "Rogue_Gerasso_WeeklyRoundFirstDialog";
			}
			else
			{
				return "Rogue_Gerasso_DefaultStartDialog";
			}
		}
		else
		{
			return "Rogue_Gerasso_DefaultStartDialog";
		}
	}
	return NpcInfo.Personality.DialogModelId;
}

FString UNpcDialogPicker::RogueGerassoStoryDialogPicker(FDialogScriptObj Dialog, TArray<FString> Params)
{
	UAwRogueDataSystem* SubSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	if (SubSystem)
	{
		FString CurClassId = SubSystem->GetCurPawnClassId(0);
		FRogueClassRecord CurClassRecord = SubSystem->GetBattleRecord().ClassRecords[CurClassId];
		int DeadTimes = CurClassRecord.TotalGameTimes - CurClassRecord.ClearedGameTimes;
		//初次对话
		if(SubSystem->GetSwitch("Gerasso_First_StoryDialog") == 0)
		{
			return "StoryFirstDialog";
		}
		//死亡后对话
		if(DeadTimes >= 1 && SubSystem->GetSwitch("Gerasso_AfterDeath_StoryDialog") == 0)
		{
			return "AfterDeathDialog";
		}
		//到过沙漠图
		if(SubSystem->GetSwitch("ArriveDesertLevel") >= 1 && SubSystem->GetSwitch("Gerasso_ArriveDesertLevel_StoryDialog") == 0)
		{
			return "ArriveDesertLevelDialog";
		}
		//通关判定
		if(SubSystem->GetIsClearedGame())
		{
			//圣杯相关
			if(SubSystem->GetSwitch("GrailLevel") >= 1)
			{
				//获得1个圣杯碎片的对话
				if(SubSystem->GetSwitch("Gerasso_GrailLevel01_Dialog") == 0)
				{
					//return "FirstGrailDialog";
				}
				//与管理人对话得知“圣杯誓言石”后
				if(SubSystem->GetSwitch("GetGrailInfo") >= 1 && SubSystem->GetSwitch("Gerasso_GetGrailInfo_Dialog") == 0)
				{
					return "GetGrailInfoDialog";
				}
				//获得2个圣杯碎片的对话
				if(SubSystem->GetSwitch("GrailLevel") >= 2 && SubSystem->GetSwitch("Gerasso_GrailLevel02_Dialog") == 0)
				{
					return "SecondGrailDialog";
				}
				// //获得3个圣杯碎片的对话
				// if(SubSystem->GetSwitch("GrailLevel") >= 3 && SubSystem->GetSwitch("Gerasso_GrailLevel03_Dialog") == 0)
				// {
				// 	return "ThirdGrailDialog";
				// }
				// //获得4个圣杯碎片的对话
				// if(SubSystem->GetSwitch("GrailLevel") >= 4 && SubSystem->GetSwitch("Gerasso_GrailLevel04_Dialog") == 0)
				// {
				// 	return "ForthGrailDialog";
				// }
				// //获得5个圣杯碎片的对话
				// if(SubSystem->GetSwitch("GrailLevel") >= 5 && SubSystem->GetSwitch("Gerasso_GrailLevel05_Dialog") == 0)
				// {
				// 	return "FifthGrailDialog";
				// }
				//解除圣杯封印的对话
				if(SubSystem->GetSwitch("GrailLevel") >= 3 && SubSystem->GetSwitch("Gerasso_GrailLevel06_Dialog") == 0)
				{
					return "UnlockGrailDialog";
				}
				//击败最终BOSS的对话
				if(SubSystem->GetSwitch("KillFinalBoss") >= 1 && SubSystem->GetSwitch("Gerasso_KillFinalBoss_Dialog") == 0)
				{
					return "KillFinalBossDialog";
				}

				//击败最终BOSS的重复对话
				if(SubSystem->GetSwitch("KillFinalBoss") >= 1 && SubSystem->GetSwitch("Gerasso_KillFinalBoss_Dialog") >= 1)
				{
					return "KillFinalBossDialogRepeat";
				}
				//解除圣杯封印的重复对话
				if(SubSystem->GetSwitch("GrailLevel") >= 3 && SubSystem->GetSwitch("Gerasso_GrailLevel06_Dialog") >= 1)
				{
					return "UnlockGrailDialogRepeat";
				}
				// //获得5个圣杯碎片的重复对话
				// if(SubSystem->GetSwitch("GrailLevel") >= 5 && SubSystem->GetSwitch("Gerasso_GrailLevel05_Dialog") >= 1)
				// {
				// 	return "FifthGrailDialogRepeat";
				// }
				// //获得4个圣杯碎片的重复对话
				// if(SubSystem->GetSwitch("GrailLevel") >= 4 && SubSystem->GetSwitch("Gerasso_GrailLevel04_Dialog") >= 1)
				// {
				// 	return "ForthGrailDialogRepeat";
				// }
				// //获得3个圣杯碎片的重复对话
				// if(SubSystem->GetSwitch("GrailLevel") >= 3 && SubSystem->GetSwitch("Gerasso_GrailLevel03_Dialog") >= 1)
				// {
				// 	return "ThirdGrailDialogRepeat";
				// }
				//获得2个圣杯碎片的重复对话
				if(SubSystem->GetSwitch("GrailLevel") >= 2 && SubSystem->GetSwitch("Gerasso_GrailLevel02_Dialog") >= 1)
				{
					return "SecondGrailDialogRepeat";
				}
				//Switch GetGrailInfo >= 1后的重复对话
				if(SubSystem->GetSwitch("GetGrailInfo") >= 1 && SubSystem->GetSwitch("Gerasso_GetGrailInfo_Dialog") >= 1)
				{
					return "GetGrailInfoDialogRepeat";
				}
				//获得1个圣杯碎片的重复对话
				if(SubSystem->GetSwitch("GrailLevel") >= 1 && SubSystem->GetSwitch("Gerasso_GrailLevel01_Dialog") >= 1)
				{
					return "FirstGrailDialogRepeat";
				}
			}
			//通关后重复对话
			return "WeeklyRoundDialogRepeat";
		}
		
		//到过沙漠图重复对话
		if(SubSystem->GetSwitch("ArriveDesertLevel") >= 1 && SubSystem->GetSwitch("Gerasso_ArriveDesertLevel_StoryDialog") >= 1)
			return "ArriveDesertLevelDialogRepeat";
		//死亡后重复对话
		if(DeadTimes >= 1 && SubSystem->GetSwitch("Gerasso_AfterDeath_StoryDialog") >= 0)
			return "AfterDeathDialogRepeat";
		//保底重复对话
		return "StoryFirstDialogRepeat";
	}
	return "DefaultStoryDialog";
}

FString UNpcDialogPicker::RogueHenrikFirstDialogPicker(FNpcInfo NpcInfo, TArray<FString> Params)
{
	UAwRogueDataSystem* SubSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	if (SubSystem)
	{
		if(SubSystem->GetIsClearedGame())
		{
			if(SubSystem->GetSwitch("Henrik_WeeklyRound_VeryFirst_Dialog") == 0)
			{
				return "Rogue_Henrik_WeeklyRoundFirstDialog";
			}
			else
			{
				return "Rogue_Henrik_DefaultStartDialog";
			}
		}
		else
		{
			return "Rogue_Henrik_DefaultStartDialog";
		}
	}
	return NpcInfo.Personality.DialogModelId;
}

FString UNpcDialogPicker::RogueHenrikStoryDialogPicker(FDialogScriptObj Dialog, TArray<FString> Params)
{
	UAwRogueDataSystem* SubSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	if (SubSystem)
	{
		FString CurClassId = SubSystem->GetCurPawnClassId(0);
		FRogueClassRecord CurClassRecord = SubSystem->GetBattleRecord().ClassRecords[CurClassId];
		int DeadTimes = CurClassRecord.TotalGameTimes - CurClassRecord.ClearedGameTimes;
		//初次对话
		// if(SubSystem->GetSwitch("Henrik_First_StoryDialog") == 0)
		// {
		// 	return "StoryFirstDialog";
		// }
		//死亡后对话
		if(DeadTimes >= 1 && SubSystem->GetSwitch("Henrik_AfterDeath_StoryDialog") == 0)
		{
			return "AfterDeathDialog";
		}
		//已见过兽人碎骨者
		if(SubSystem->GetSwitch("MeetOrcBoneBreaker") >= 1 && SubSystem->GetSwitch("Henrik_ArriveLevel30_StoryDialog") == 0)
		{
			return "ArriveLevel30Dialog";
		}
		//通关判定
		if(SubSystem->GetIsClearedGame())
		{
			//圣杯相关
			if(SubSystem->GetSwitch("GrailLevel") >= 1)
			{
				//获得1个圣杯碎片的对话
				if(SubSystem->GetSwitch("Henrik_GrailLevel01_Dialog") == 0)
				{
					//return "FirstGrailDialog";
				}
				//与管理人对话得知“圣杯誓言石”后
				if(SubSystem->GetSwitch("GetGrailInfo") >= 1 && SubSystem->GetSwitch("Henrik_GetGrailInfo_Dialog") == 0)
				{
					return "GetGrailInfoDialog";
				}
				//获得2个圣杯碎片的对话
				if(SubSystem->GetSwitch("GrailLevel") >= 2 && SubSystem->GetSwitch("Henrik_GrailLevel02_Dialog") == 0)
				{
					return "SecondGrailDialog";
				}
				// //获得3个圣杯碎片的对话
				// if(SubSystem->GetSwitch("GrailLevel") >= 3 && SubSystem->GetSwitch("Henrik_GrailLevel03_Dialog") == 0)
				// {
				// 	return "ThirdGrailDialog";
				// }
				// //获得4个圣杯碎片的对话
				// if(SubSystem->GetSwitch("GrailLevel") >= 4 && SubSystem->GetSwitch("Henrik_GrailLevel04_Dialog") == 0)
				// {
				// 	return "ForthGrailDialog";
				// }
				// //获得5个圣杯碎片的对话
				// if(SubSystem->GetSwitch("GrailLevel") >= 5 && SubSystem->GetSwitch("Henrik_GrailLevel05_Dialog") == 0)
				// {
				// 	return "FifthGrailDialog";
				// }
				//解除圣杯封印的对话
				if(SubSystem->GetSwitch("GrailLevel") >= 6 && SubSystem->GetSwitch("Henrik_GrailLevel06_Dialog") == 0)
				{
					return "UnlockGrailDialog";
				}
				//击败最终BOSS的对话
				if(SubSystem->GetSwitch("KillFinalBoss") >= 1 && SubSystem->GetSwitch("Henrik_KillFinalBoss_Dialog") == 0)
				{
					return "KillFinalBossDialog";
				}

				//击败最终BOSS的重复对话
				if(SubSystem->GetSwitch("KillFinalBoss") >= 1 && SubSystem->GetSwitch("Henrik_KillFinalBoss_Dialog") >= 1)
				{
					return "KillFinalBossDialogRepeat";
				}
				//解除圣杯封印的重复对话
				if(SubSystem->GetSwitch("GrailLevel") >= 3 && SubSystem->GetSwitch("Henrik_GrailLevel06_Dialog") >= 1)
				{
					return "UnlockGrailDialogRepeat";
				}
				// //获得5个圣杯碎片的重复对话
				// if(SubSystem->GetSwitch("GrailLevel") >= 5 && SubSystem->GetSwitch("Henrik_GrailLevel05_Dialog") >= 1)
				// {
				// 	return "FifthGrailDialogRepeat";
				// }
				// //获得4个圣杯碎片的重复对话
				// if(SubSystem->GetSwitch("GrailLevel") >= 4 && SubSystem->GetSwitch("Henrik_GrailLevel04_Dialog") >= 1)
				// {
				// 	return "ForthGrailDialogRepeat";
				// }
				// //获得3个圣杯碎片的重复对话
				// if(SubSystem->GetSwitch("GrailLevel") >= 3 && SubSystem->GetSwitch("Henrik_GrailLevel03_Dialog") >= 1)
				// {
				// 	return "ThirdGrailDialogRepeat";
				// }
				//获得2个圣杯碎片的重复对话
				if(SubSystem->GetSwitch("GrailLevel") >= 2 && SubSystem->GetSwitch("Henrik_GrailLevel02_Dialog") >= 1)
				{
					return "SecondGrailDialogRepeat";
				}
				//Switch GetGrailInfo >= 1后的重复对话
				if(SubSystem->GetSwitch("GetGrailInfo") >= 1 && SubSystem->GetSwitch("Henrik_GetGrailInfo_Dialog") >= 1)
				{
					return "GetGrailInfoDialogRepeat";
				}
				//获得1个圣杯碎片的重复对话
				if(SubSystem->GetSwitch("GrailLevel") >= 1 && SubSystem->GetSwitch("Henrik_GrailLevel01_Dialog") >= 1)
				{
					return "FirstGrailDialogRepeat";
				}
			}
			//通关后重复对话
			return "WeeklyRoundDialogRepeat";
		}
		
		//见过兽人碎骨者重复对话
		if(SubSystem->GetSwitch("MeetOrcBoneBreaker") >= 1 && SubSystem->GetSwitch("Henrik_ArriveLevel30_StoryDialog") >= 1)
			return "ArriveLevel30DialogRepeat";
		//死亡后重复对话
		if(DeadTimes >= 1 && SubSystem->GetSwitch("Henrik_AfterDeath_StoryDialog") >= 0)
			return "AfterDeathDialogRepeat";
		//保底重复对话
		return "StoryFirstDialogRepeat";
	}
	return "DefaultStoryDialog";
}

FString UNpcDialogPicker::RogueSolaFirstDialogPicker(FNpcInfo NpcInfo, TArray<FString> Params)
{
	UAwRogueDataSystem* SubSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	if (SubSystem)
	{
		if(SubSystem->GetIsClearedGame())
		{
			if(SubSystem->GetSwitch("Sola_WeeklyRound_VeryFirst_Dialog") == 0)
			{
				return "Rogue_Sola_WeeklyRoundFirstDialog";
			}
			else
			{
				return "Rogue_Sola_DefaultStartDialog";
			}
		}
		else
		{
			return "Rogue_Sola_DefaultStartDialog";
		}
	}
	return NpcInfo.Personality.DialogModelId;
}

FString UNpcDialogPicker::RogueSolaStoryDialogPicker(FDialogScriptObj Dialog, TArray<FString> Params)
{
	UAwRogueDataSystem* SubSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	if (SubSystem)
	{
		FString CurClassId = SubSystem->GetCurPawnClassId(0);
		FRogueClassRecord CurClassRecord = SubSystem->GetBattleRecord().ClassRecords[CurClassId];
		int DeadTimes = CurClassRecord.TotalGameTimes - CurClassRecord.ClearedGameTimes;
		//初次对话
		// if(SubSystem->GetSwitch("Sola_First_StoryDialog") == 0)
		// {
		// 	return "StoryFirstDialog";
		// }
		//死亡后对话
		if(DeadTimes >= 1 && SubSystem->GetSwitch("Sola_AfterDeath_StoryDialog") == 0)
		{
			return "AfterDeathDialog";
		}
		//已见过熔岩魔俑
		if(SubSystem->GetSwitch("MeetLavaGolem") >= 1 && SubSystem->GetSwitch("Sola_ArriveLevel20_StoryDialog") == 0)
		{
			return "ArriveLevel20Dialog";
		}
		//通关判定
		if(SubSystem->GetIsClearedGame())
		{
			//圣杯相关
			if(SubSystem->GetSwitch("GrailLevel") >= 1)
			{
				//获得1个圣杯碎片的对话
				if(SubSystem->GetSwitch("Sola_GrailLevel01_Dialog") == 0)
				{
					//return "FirstGrailDialog";
				}
				//与管理人对话得知“圣杯誓言石”后
				if(SubSystem->GetSwitch("GetGrailInfo") >= 1 && SubSystem->GetSwitch("Sola_GetGrailInfo_Dialog") == 0)
				{
					return "GetGrailInfoDialog";
				}
				//获得2个圣杯碎片的对话
				if(SubSystem->GetSwitch("GrailLevel") >= 2 && SubSystem->GetSwitch("Sola_GrailLevel02_Dialog") == 0)
				{
					return "SecondGrailDialog";
				}
				// //获得3个圣杯碎片的对话
				// if(SubSystem->GetSwitch("GrailLevel") >= 3 && SubSystem->GetSwitch("Sola_GrailLevel03_Dialog") == 0)
				// {
				// 	return "ThirdGrailDialog";
				// }
				// //获得4个圣杯碎片的对话
				// if(SubSystem->GetSwitch("GrailLevel") >= 4 && SubSystem->GetSwitch("Sola_GrailLevel04_Dialog") == 0)
				// {
				// 	return "ForthGrailDialog";
				// }
				// //获得5个圣杯碎片的对话
				// if(SubSystem->GetSwitch("GrailLevel") >= 5 && SubSystem->GetSwitch("Sola_GrailLevel05_Dialog") == 0)
				// {
				// 	return "FifthGrailDialog";
				// }
				//解除圣杯封印的对话
				if(SubSystem->GetSwitch("GrailLevel") >= 3 && SubSystem->GetSwitch("Sola_GrailLevel06_Dialog") == 0)
				{
					return "UnlockGrailDialog";
				}
				//击败最终BOSS的对话
				if(SubSystem->GetSwitch("KillFinalBoss") >= 1 && SubSystem->GetSwitch("Sola_KillFinalBoss_Dialog") == 0)
				{
					return "KillFinalBossDialog";
				}

				//击败最终BOSS的重复对话
				if(SubSystem->GetSwitch("KillFinalBoss") >= 1 && SubSystem->GetSwitch("Sola_KillFinalBoss_Dialog") >= 1)
				{
					return "KillFinalBossDialogRepeat";
				}
				//解除圣杯封印的重复对话
				if(SubSystem->GetSwitch("GrailLevel") >= 3 && SubSystem->GetSwitch("Sola_GrailLevel06_Dialog") >= 1)
				{
					return "UnlockGrailDialogRepeat";
				}
				// //获得5个圣杯碎片的重复对话
				// if(SubSystem->GetSwitch("GrailLevel") >= 5 && SubSystem->GetSwitch("Sola_GrailLevel05_Dialog") >= 1)
				// {
				// 	return "FifthGrailDialogRepeat";
				// }
				// //获得4个圣杯碎片的重复对话
				// if(SubSystem->GetSwitch("GrailLevel") >= 4 && SubSystem->GetSwitch("Sola_GrailLevel04_Dialog") >= 1)
				// {
				// 	return "ForthGrailDialogRepeat";
				// }
				// //获得3个圣杯碎片的重复对话
				// if(SubSystem->GetSwitch("GrailLevel") >= 3 && SubSystem->GetSwitch("Sola_GrailLevel03_Dialog") >= 1)
				// {
				// 	return "ThirdGrailDialogRepeat";
				// }
				//获得2个圣杯碎片的重复对话
				if(SubSystem->GetSwitch("GrailLevel") >= 2 && SubSystem->GetSwitch("Sola_GrailLevel02_Dialog") >= 1)
				{
					return "SecondGrailDialogRepeat";
				}
				//Switch GetGrailInfo >= 1后的重复对话
				if(SubSystem->GetSwitch("GetGrailInfo") >= 1 && SubSystem->GetSwitch("Sola_GetGrailInfo_Dialog") >= 1)
				{
					return "GetGrailInfoDialogRepeat";
				}
				//获得1个圣杯碎片的重复对话
				if(SubSystem->GetSwitch("GrailLevel") >= 1 && SubSystem->GetSwitch("Sola_GrailLevel01_Dialog") >= 1)
				{
					return "FirstGrailDialogRepeat";
				}
			}
			//通关后重复对话
			return "WeeklyRoundDialogRepeat";
		}
		
		//见过熔岩魔俑重复对话
		if(SubSystem->GetSwitch("MeetLavaGolem") >= 1 && SubSystem->GetSwitch("Sola_ArriveLevel20_StoryDialog") >= 1)
			return "ArriveLevel20DialogRepeat";
		//死亡后重复对话
		if(DeadTimes >= 1 && SubSystem->GetSwitch("Sola_AfterDeath_StoryDialog") >= 0)
			return "AfterDeathDialogRepeat";
		//保底重复对话
		return "StoryFirstDialogRepeat";
	}
	return "DefaultStoryDialog";
}

FString UNpcDialogPicker::RogueTierdagonDialogPicker(FNpcInfo NpcInfo, TArray<FString> Params)
{
	UAwRogueDataSystem* SubSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	if (SubSystem)
	{
		if (SubSystem->GetIsClearedGame())
		{
			if (SubSystem->GetSwitch("Tierdagon_WeeklyRound_VeryFirst_Dialog") == 0)
			{
				return "Rogue_Tierdagon_WeeklyRoundFirstDialog";
			}
			else
			{
				return "Rogue_Tierdagon_DefaultStartDialog";
			}
		}
		else
		{
			return "Rogue_Tierdagon_DefaultStartDialog";
		}
	}
	return NpcInfo.Personality.DialogModelId;
}


FString UNpcDialogPicker::RogueTierdagonStoryDialogPicker(FDialogScriptObj Dialog, TArray<FString> Params)
{
	UAwRogueDataSystem* SubSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	if (SubSystem)
	{
		FString CurClassId = SubSystem->GetCurPawnClassId(0);
		FRogueClassRecord CurClassRecord = SubSystem->GetBattleRecord().ClassRecords[CurClassId];
		int DeadTimes = CurClassRecord.TotalGameTimes - CurClassRecord.ClearedGameTimes;
		//初次对话
		// if(SubSystem->GetSwitch("Tierdagon_First_StoryDialog") == 0)
		// {
		// 	return "StoryFirstDialog";
		// }
		//死亡后对话
		if (DeadTimes >= 1 && SubSystem->GetSwitch("Tierdagon_AfterDeath_StoryDialog") == 0)
		{
			return "AfterDeathDialog";
		}
		//已见过熔岩魔俑
		if (SubSystem->GetSwitch("MeetLavaGolem") >= 1 && SubSystem->GetSwitch("Tierdagon_ArriveLevel20_StoryDialog") == 0)
		{
			return "ArriveLevel20Dialog";
		}
		//通关判定
		if (SubSystem->GetIsClearedGame())
		{
			//圣杯相关
			if (SubSystem->GetSwitch("GrailLevel") >= 1)
			{
				//获得1个圣杯碎片的对话
				if (SubSystem->GetSwitch("Tierdagon_GrailLevel01_Dialog") == 0)
				{
					//return "FirstGrailDialog";
				}
				//与管理人对话得知“圣杯誓言石”后
				if (SubSystem->GetSwitch("GetGrailInfo") >= 1 && SubSystem->GetSwitch("Tierdagon_GetGrailInfo_Dialog") == 0)
				{
					return "GetGrailInfoDialog";
				}
				//获得2个圣杯碎片的对话
				if (SubSystem->GetSwitch("GrailLevel") >= 2 && SubSystem->GetSwitch("Tierdagon_GrailLevel02_Dialog") == 0)
				{
					return "SecondGrailDialog";
				}
				// //获得3个圣杯碎片的对话
				// if(SubSystem->GetSwitch("GrailLevel") >= 3 && SubSystem->GetSwitch("Tierdagon_GrailLevel03_Dialog") == 0)
				// {
				// 	return "ThirdGrailDialog";
				// }
				// //获得4个圣杯碎片的对话
				// if(SubSystem->GetSwitch("GrailLevel") >= 4 && SubSystem->GetSwitch("Tierdagon_GrailLevel04_Dialog") == 0)
				// {
				// 	return "ForthGrailDialog";
				// }
				// //获得5个圣杯碎片的对话
				// if(SubSystem->GetSwitch("GrailLevel") >= 5 && SubSystem->GetSwitch("Tierdagon_GrailLevel05_Dialog") == 0)
				// {
				// 	return "FifthGrailDialog";
				// }
				//解除圣杯封印的对话
				if (SubSystem->GetSwitch("GrailLevel") >= 3 && SubSystem->GetSwitch("Tierdagon_GrailLevel06_Dialog") == 0)
				{
					return "UnlockGrailDialog";
				}
				//击败最终BOSS的对话
				if (SubSystem->GetSwitch("KillFinalBoss") >= 1 && SubSystem->GetSwitch("Tierdagon_KillFinalBoss_Dialog") == 0)
				{
					return "KillFinalBossDialog";
				}

				//击败最终BOSS的重复对话
				if (SubSystem->GetSwitch("KillFinalBoss") >= 1 && SubSystem->GetSwitch("Tierdagon_KillFinalBoss_Dialog") >= 1)
				{
					return "KillFinalBossDialogRepeat";
				}
				//解除圣杯封印的重复对话
				if (SubSystem->GetSwitch("GrailLevel") >= 3 && SubSystem->GetSwitch("Tierdagon_GrailLevel06_Dialog") >= 1)
				{
					return "UnlockGrailDialogRepeat";
				}
				// //获得5个圣杯碎片的重复对话
				// if(SubSystem->GetSwitch("GrailLevel") >= 5 && SubSystem->GetSwitch("Tierdagon_GrailLevel05_Dialog") >= 1)
				// {
				// 	return "FifthGrailDialogRepeat";
				// }
				// //获得4个圣杯碎片的重复对话
				// if(SubSystem->GetSwitch("GrailLevel") >= 4 && SubSystem->GetSwitch("Tierdagon_GrailLevel04_Dialog") >= 1)
				// {
				// 	return "ForthGrailDialogRepeat";
				// }
				// //获得3个圣杯碎片的重复对话
				// if(SubSystem->GetSwitch("GrailLevel") >= 3 && SubSystem->GetSwitch("Tierdagon_GrailLevel03_Dialog") >= 1)
				// {
				// 	return "ThirdGrailDialogRepeat";
				// }
				//获得2个圣杯碎片的重复对话
				if (SubSystem->GetSwitch("GrailLevel") >= 2 && SubSystem->GetSwitch("Tierdagon_GrailLevel02_Dialog") >= 1)
				{
					return "SecondGrailDialogRepeat";
				}
				//Switch GetGrailInfo >= 1后的重复对话
				if (SubSystem->GetSwitch("GetGrailInfo") >= 1 && SubSystem->GetSwitch("Tierdagon_GetGrailInfo_Dialog") >= 1)
				{
					return "GetGrailInfoDialogRepeat";
				}
				//获得1个圣杯碎片的重复对话
				if (SubSystem->GetSwitch("GrailLevel") >= 1 && SubSystem->GetSwitch("Tierdagon_GrailLevel01_Dialog") >= 1)
				{
					return "FirstGrailDialogRepeat";
				}
			}
			//通关后重复对话
			return "WeeklyRoundDialogRepeat";
		}

		//见过熔岩魔俑重复对话
		if (SubSystem->GetSwitch("MeetLavaGolem") >= 1 && SubSystem->GetSwitch("Tierdagon_ArriveLevel20_StoryDialog") >= 1)
			return "ArriveLevel20DialogRepeat";
		//死亡后重复对话
		if (DeadTimes >= 1 && SubSystem->GetSwitch("Tierdagon_AfterDeath_StoryDialog") >= 0)
			return "AfterDeathDialogRepeat";
		//保底重复对话
		return "StoryFirstDialogRepeat";
	}
	return "DefaultStoryDialog";
}
FString UNpcDialogPicker::RogueCaelynnDialogPicker(FNpcInfo NpcInfo, TArray<FString> Params)
{
	UAwRogueDataSystem* SubSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	if (SubSystem)
	{
		if (SubSystem->GetIsClearedGame())
		{
			if (SubSystem->GetSwitch("Caelynn_WeeklyRound_VeryFirst_Dialog") == 0)
			{
				return "Rogue_Caelynn_WeeklyRoundFirstDialog";
			}
			else
			{
				return "Rogue_Caelynn_DefaultStartDialog";
			}
		}
		else
		{
			return "Rogue_Caelynn_DefaultStartDialog";
		}
	}
	return NpcInfo.Personality.DialogModelId;
}

FString UNpcDialogPicker::RogueCaelynnStoryDialogPicker(FDialogScriptObj Dialog, TArray<FString> Params)
{
	UAwRogueDataSystem* SubSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	if (SubSystem)
	{
		FString CurClassId = SubSystem->GetCurPawnClassId(0);
		FRogueClassRecord CurClassRecord = SubSystem->GetBattleRecord().ClassRecords[CurClassId];
		int DeadTimes = CurClassRecord.TotalGameTimes - CurClassRecord.ClearedGameTimes;
		//初次对话
		// if(SubSystem->GetSwitch("Caelynn_First_StoryDialog") == 0)
		// {
		// 	return "StoryFirstDialog";
		// }
		//死亡后对话
		if (DeadTimes >= 1 && SubSystem->GetSwitch("Caelynn_AfterDeath_StoryDialog") == 0)
		{
			return "AfterDeathDialog";
		}
		//已见过熔岩魔俑
		if (SubSystem->GetSwitch("MeetLavaGolem") >= 1 && SubSystem->GetSwitch("Caelynn_ArriveLevel20_StoryDialog") == 0)
		{
			return "ArriveLevel20Dialog";
		}
		//通关判定
		if (SubSystem->GetIsClearedGame())
		{
			//圣杯相关
			if (SubSystem->GetSwitch("GrailLevel") >= 1)
			{
				//获得1个圣杯碎片的对话
				if (SubSystem->GetSwitch("Caelynn_GrailLevel01_Dialog") == 0)
				{
					//return "FirstGrailDialog";
				}
				//与管理人对话得知“圣杯誓言石”后
				if (SubSystem->GetSwitch("GetGrailInfo") >= 1 && SubSystem->GetSwitch("Caelynn_GetGrailInfo_Dialog") == 0)
				{
					return "GetGrailInfoDialog";
				}
				//获得2个圣杯碎片的对话
				if (SubSystem->GetSwitch("GrailLevel") >= 2 && SubSystem->GetSwitch("Caelynn_GrailLevel02_Dialog") == 0)
				{
					return "SecondGrailDialog";
				}
				// //获得3个圣杯碎片的对话
				// if(SubSystem->GetSwitch("GrailLevel") >= 3 && SubSystem->GetSwitch("Caelynn_GrailLevel03_Dialog") == 0)
				// {
				// 	return "ThirdGrailDialog";
				// }
				// //获得4个圣杯碎片的对话
				// if(SubSystem->GetSwitch("GrailLevel") >= 4 && SubSystem->GetSwitch("Caelynn_GrailLevel04_Dialog") == 0)
				// {
				// 	return "ForthGrailDialog";
				// }
				// //获得5个圣杯碎片的对话
				// if(SubSystem->GetSwitch("GrailLevel") >= 5 && SubSystem->GetSwitch("Caelynn_GrailLevel05_Dialog") == 0)
				// {
				// 	return "FifthGrailDialog";
				// }
				//解除圣杯封印的对话
				if (SubSystem->GetSwitch("GrailLevel") >= 3 && SubSystem->GetSwitch("Caelynn_GrailLevel06_Dialog") == 0)
				{
					return "UnlockGrailDialog";
				}
				//击败最终BOSS的对话
				if (SubSystem->GetSwitch("KillFinalBoss") >= 1 && SubSystem->GetSwitch("Caelynn_KillFinalBoss_Dialog") == 0)
				{
					return "KillFinalBossDialog";
				}

				//击败最终BOSS的重复对话
				if (SubSystem->GetSwitch("KillFinalBoss") >= 1 && SubSystem->GetSwitch("Caelynn_KillFinalBoss_Dialog") >= 1)
				{
					return "KillFinalBossDialogRepeat";
				}
				//解除圣杯封印的重复对话
				if (SubSystem->GetSwitch("GrailLevel") >= 3 && SubSystem->GetSwitch("Caelynn_GrailLevel06_Dialog") >= 1)
				{
					return "UnlockGrailDialogRepeat";
				}
				// //获得5个圣杯碎片的重复对话
				// if(SubSystem->GetSwitch("GrailLevel") >= 5 && SubSystem->GetSwitch("Caelynn_GrailLevel05_Dialog") >= 1)
				// {
				// 	return "FifthGrailDialogRepeat";
				// }
				// //获得4个圣杯碎片的重复对话
				// if(SubSystem->GetSwitch("GrailLevel") >= 4 && SubSystem->GetSwitch("Caelynn_GrailLevel04_Dialog") >= 1)
				// {
				// 	return "ForthGrailDialogRepeat";
				// }
				// //获得3个圣杯碎片的重复对话
				// if(SubSystem->GetSwitch("GrailLevel") >= 3 && SubSystem->GetSwitch("Caelynn_GrailLevel03_Dialog") >= 1)
				// {
				// 	return "ThirdGrailDialogRepeat";
				// }
				//获得2个圣杯碎片的重复对话
				if (SubSystem->GetSwitch("GrailLevel") >= 2 && SubSystem->GetSwitch("Caelynn_GrailLevel02_Dialog") >= 1)
				{
					return "SecondGrailDialogRepeat";
				}
				//Switch GetGrailInfo >= 1后的重复对话
				if (SubSystem->GetSwitch("GetGrailInfo") >= 1 && SubSystem->GetSwitch("Caelynn_GetGrailInfo_Dialog") >= 1)
				{
					return "GetGrailInfoDialogRepeat";
				}
				//获得1个圣杯碎片的重复对话
				if (SubSystem->GetSwitch("GrailLevel") >= 1 && SubSystem->GetSwitch("Caelynn_GrailLevel01_Dialog") >= 1)
				{
					return "FirstGrailDialogRepeat";
				}
			}
			//通关后重复对话
			return "WeeklyRoundDialogRepeat";
		}

		//见过熔岩魔俑重复对话
		if (SubSystem->GetSwitch("MeetLavaGolem") >= 1 && SubSystem->GetSwitch("Caelynn_ArriveLevel20_StoryDialog") >= 1)
			return "ArriveLevel20DialogRepeat";
		//死亡后重复对话
		if (DeadTimes >= 1 && SubSystem->GetSwitch("Caelynn_AfterDeath_StoryDialog") >= 0)
			return "AfterDeathDialogRepeat";
		//保底重复对话
		return "StoryFirstDialogRepeat";
	}
	return "DefaultStoryDialog";
}

FString UNpcDialogPicker::RogueHephaesmoseFirstDialogPicker(FNpcInfo NpcInfo, TArray<FString> Params)
{
	UAwRogueDataSystem* SubSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	if (SubSystem)
	{
		{
			if(SubSystem->GetSwitch("FirstInHall") == 0)
			{
				return "Rogue_Hephaesmose_FirstInHallDialog";
			}
			//新周目的初次对话
			if(SubSystem->GetSwitch("GrailLevel") >= 1 && SubSystem->GetSwitch("Hephaesmose_WeeklyRound_Dialog") == 0)
			{
				return "Rogue_Hephaesmose_WeeklyRoundDialog";
			}
			//新周目的第二次对话
			// if(SubSystem->GetSwitch("GrailLevel") >= 1 && SubSystem->GetSwitch("Hephaesmose_WeeklyRoundAgain_Dialog") == 0)
			// {
			// 	return "Rogue_Hephaesmose_WeeklyRoundAgainDialog";
			// }
			//获得3个圣杯碎片的对话
			if(SubSystem->GetSwitch("GrailLevel") >= 3 && SubSystem->GetSwitch("Hephaesmose_GrailLevel05_Dialog") == 0)
			{
				return "Rogue_Hephaesmose_FifthGrailDialog";
			}
			//击败最终BOSS的对话
			if(SubSystem->GetSwitch("KillFinalBoss") >= 1 && SubSystem->GetSwitch("Hephaesmose_KillFinalBoss_Dialog") == 0)
			{
				return "Rogue_Hephaesmose_KillFinalBossDialog";
			}
			// if(SubSystem->GetSwitch("Hephaesmose_VeryFirst_Dialog") == 0)
			// {
			// 	return "Rogue_Hephaesmose_FirstDialog";
			// }
			return "Rogue_Hephaesmose_DefaultStartDialog";
		}
	}
	return NpcInfo.Personality.DialogModelId;
}

FString UNpcDialogPicker::RogueHephaesmoseStoryDialogPicker(FDialogScriptObj Dialog, TArray<FString> Params)
{
	UAwRogueDataSystem* SubSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	if (SubSystem)
	{
		FString CurClassId = SubSystem->GetCurPawnClassId(0);
		FRogueClassRecord CurClassRecord = SubSystem->GetBattleRecord().ClassRecords[CurClassId];
		int DeadTimes = CurClassRecord.TotalGameTimes - CurClassRecord.ClearedGameTimes;
		//初次对话
		// if(SubSystem->GetSwitch("Hephaesmose_First_StoryDialog") == 0)
		// {
		// 	return "StoryFirstDialog";
		// }
		//死亡后对话
		if(DeadTimes >= 1 && SubSystem->GetSwitch("Hephaesmose_AfterDeath_StoryDialog") == 0)
		{
			return "AfterDeathDialog";
		}
		//已见过熔岩图
		if(SubSystem->GetSwitch("ArriveLavaLevel") >= 1 && SubSystem->GetSwitch("Hephaesmose_ArriveLavaLevel_StoryDialog") == 0)
		{
			return "ArriveLavaLevelDialog";
		}
		//已见过沙漠图
		if(SubSystem->GetSwitch("ArriveDesertLevel") >= 1 && SubSystem->GetSwitch("Hephaesmose_ArriveDesertLevel_StoryDialog") == 0)
		{
			return "ArriveDesertLevelDialog";
		}
		//通关判定
		if(SubSystem->GetIsClearedGame())
		{
			//圣杯相关
			if(SubSystem->GetSwitch("GrailLevel") >= 1)
			{
				//新周目的初次对话
				// if(SubSystem->GetSwitch("Hephaesmose_WeeklyRound_Dialog") == 0)
				// {
				// 	return "WeeklyRoundDialog";
				// }
				//新周目的第二次对话
				// if(SubSystem->GetSwitch("Hephaesmose_WeeklyRoundAgain_Dialog") == 0)
				// {
				// 	return "WeeklyRoundAgainDialog";
				// }
				//获得2个圣杯碎片的对话
				if(SubSystem->GetSwitch("GrailLevel") >= 2 && SubSystem->GetSwitch("Hephaesmose_GrailLevel02_Dialog") == 0)
				{
					return "SecondGrailDialog";
				}
				// //获得3个圣杯碎片的对话
				// if(SubSystem->GetSwitch("GrailLevel") >= 3 && SubSystem->GetSwitch("Hephaesmose_GrailLevel03_Dialog") == 0)
				// {
				// 	return "ThirdGrailDialog";
				// }
				// //获得4个圣杯碎片的对话
				// if(SubSystem->GetSwitch("GrailLevel") >= 4 && SubSystem->GetSwitch("Hephaesmose_GrailLevel04_Dialog") == 0)
				// {
				// 	return "ForthGrailDialog";
				// }
				//获得5个圣杯碎片的对话
				// if(SubSystem->GetSwitch("GrailLevel") >= 5 && SubSystem->GetSwitch("Hephaesmose_GrailLevel05_Dialog") == 0)
				// {
				// 	return "FifthGrailDialog";
				// }
				//击败最终BOSS的对话
				// if(SubSystem->GetSwitch("KillFinalBoss") >= 1 && SubSystem->GetSwitch("Hephaesmose_KillFinalBoss_Dialog") == 0)
				// {
				// 	return "KillFinalBossDialog";
				// }
				
				//获得5个圣杯碎片的重复对话
				if(SubSystem->GetSwitch("GrailLevel") >= 5 && SubSystem->GetSwitch("Hephaesmose_GrailLevel05_Dialog") >= 1)
				{
					return "FifthGrailDialogRepeat";
				}
			}
			//通关后重复对话
			return "WeeklyRoundDialogRepeat";
		}
		//保底重复对话
		return "DefaultStoryDialog";
	}
	return "DefaultStoryDialog";
}

FString UNpcDialogPicker::RogueBakuraFirstDialogPicker(FNpcInfo NpcInfo, TArray<FString> Params)
{
	UAwRogueDataSystem* SubSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	if (SubSystem)
	{
		{
			if(SubSystem->GetSwitch("Bakura_VeryFirst_Dialog") == 0)
			{
				return "Rogue_Bakura_FirstDialog";
			}
			else
			{
				if(SubSystem->GetSwitch("Fandral_Third_StoryDialog") >= 1)
					return "Rogue_Bakura_RealNameDefaultStartDialog";
				return "Rogue_Bakura_DefaultStartDialog";
			}
		}
	}
	return NpcInfo.Personality.DialogModelId;
}

FString UNpcDialogPicker::RogueBakuraStoryDialogPicker(FDialogScriptObj Dialog, TArray<FString> Params)
{
	UAwRogueDataSystem* SubSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	if (SubSystem)
	{
		FString CurClassId = SubSystem->GetCurPawnClassId(0);
		FRogueClassRecord CurClassRecord = SubSystem->GetBattleRecord().ClassRecords[CurClassId];
		int DeadTimes = CurClassRecord.TotalGameTimes - CurClassRecord.ClearedGameTimes;
		//初次对话
		if(SubSystem->GetSwitch("Bakura_First_StoryDialog") == 0)
		{
			return "StoryFirstDialog";
		}
		//死亡3次或见到死骸骑士后对话
		if((DeadTimes >= 3 || SubSystem->GetSwitch("MeetDeathLord") >= 1) && SubSystem->GetSwitch("Bakura_AfterDeath_StoryDialog") == 0)
		{
			return "AfterDeathDialog";
		}
		//出现了NPC卫士长后的对话
		if(SubSystem->GetSwitch("FandralAppear") >= 1 && SubSystem->GetSwitch("Bakura_FandralAppear_StoryDialog") == 0)
		{
			return "FandralAppearDialog";
		}
		//和NPC卫士长第一次对话完后的对话
		if(SubSystem->GetSwitch("Fandral_Second_StoryDialog") >= 1 && SubSystem->GetSwitch("Bakura_AfterFirstTalkFandral_StoryDialog") == 0)
		{
			return "AfterFirstTalkFandralDialog";
		}
		//和NPC卫士长第二次对话完后的对话
		if(SubSystem->GetSwitch("Fandral_Third_StoryDialog") >= 1 && SubSystem->GetSwitch("Bakura_AfterSecondTalkFandral_StoryDialog") == 0)
		{
			return "AfterSecondTalkFandralDialog";
		}
		//打完最终BOSS后的对话
		if(SubSystem->GetSwitch("Bakura_AfterSecondTalkFandral_StoryDialog") >= 1 && SubSystem->GetSwitch("KillFinalBoss") >= 1 && SubSystem->GetSwitch("Bakura_KillFinalBoss_Dialog") == 0)
		{
			return "KillFinalBossDialog";
		}

		//打完最终BOSS后的重复对话
		if(SubSystem->GetSwitch("Bakura_AfterSecondTalkFandral_StoryDialog") >= 1 && SubSystem->GetSwitch("KillFinalBoss") >= 1 && SubSystem->GetSwitch("Bakura_KillFinalBoss_Dialog") >= 1)
		{
			return "KillFinalBossDialogRepeat";
		}
		
		//和NPC卫士长第一次对话完后的重复对话
		if(SubSystem->GetSwitch("Fandral_Second_StoryDialog") >= 1 && SubSystem->GetSwitch("Bakura_AfterFirstTalkFandral_StoryDialog") >= 1)
		{
			return "AfterFirstTalkFandralDialogRepeat";
		}

		//出现了NPC卫士长后的重复对话
		if(SubSystem->GetSwitch("FandralAppear") >= 1 && SubSystem->GetSwitch("Bakura_FandralAppear_StoryDialog") >= 1)
		{
			return "FandralAppearDialogRepeat";
		}
		
		//死亡3次或见到死骸骑士后 没有送魂之残响 重复对话
		if((DeadTimes >= 3 || SubSystem->GetSwitch("MeetDeathLord") >= 1) &&
			SubSystem->GetSwitch("Bakura_AfterDeath_StoryDialog") >= 1 &&
			SubSystem->GetSwitch("Bakura_GiveEcho") == 0)
		{
			return "AfterDeathDialogRepeat1";
		}

		//死亡3次或见到死骸骑士后 没有送魂之残响 重复对话
		if((DeadTimes >= 3 || SubSystem->GetSwitch("MeetDeathLord") >= 1) &&
			SubSystem->GetSwitch("Bakura_AfterDeath_StoryDialog") >= 1 &&
			SubSystem->GetSwitch("Bakura_GiveEcho") >= 1)
		{
			return "AfterDeathDialogRepeat2";
		}
		
		//保底重复对话
		return "StoryFirstDialogRepeat";
	}
	return "StoryFirstDialogRepeat";
}

FString UNpcDialogPicker::RogueFandralFirstDialogPicker(FNpcInfo NpcInfo, TArray<FString> Params)
{
	UAwRogueDataSystem* SubSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	if (SubSystem)
	{
		{
			if(SubSystem->GetSwitch("Fandral_VeryFirst_Dialog") == 0)
			{
				return "Rogue_Fandral_FirstDialog";
			}
			else
			{
				return "Rogue_Fandral_DefaultStartDialog";
			}
		}
	}
	return NpcInfo.Personality.DialogModelId;
}

FString UNpcDialogPicker::RogueFandralStoryDialogPicker(FDialogScriptObj Dialog, TArray<FString> Params)
{
	UAwRogueDataSystem* SubSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	if (SubSystem)
	{
		//初次对话
		if(SubSystem->GetSwitch("Fandral_First_StoryDialog") == 0)
		{
			return "StoryFirstDialog";
		}
		//第二次对话
		if(SubSystem->GetSwitch("Bakura_FandralAppear_StoryDialog") >= 1 && SubSystem->GetSwitch("Fandral_Second_StoryDialog") == 0)
		{
			return "StorySecondDialog";
		}
		//第三次对话
		if(SubSystem->GetSwitch("Bakura_AfterFirstTalkFandral_StoryDialog") >= 1 && SubSystem->GetSwitch("Fandral_Third_StoryDialog") == 0)
		{
			return "StoryThirdDialog";
		}
		//二周目初次对话
		if(SubSystem->GetSwitch("GrailLevel") >= 1 && SubSystem->GetSwitch("Fandral_WeeklyRound_StoryDialog") == 0 &&
			SubSystem->GetSwitch("Fandral_Third_StoryDialog") >= 1)
		{
			return "WeeklyRoundDialog";
		}
		//打完最终BOSS后的对话
		if(SubSystem->GetSwitch("Fandral_Third_StoryDialog") >= 1 && SubSystem->GetSwitch("KillFinalBoss") >= 1 &&
			SubSystem->GetSwitch("Fandral_KillFinalBoss_Dialog") == 0)
		{
			return "KillFinalBossDialog";
		}

		//打完最终BOSS后的重复对话
		if(SubSystem->GetSwitch("Fandral_Third_StoryDialog") >= 1 && SubSystem->GetSwitch("KillFinalBoss") >= 1 &&
			SubSystem->GetSwitch("Fandral_KillFinalBoss_Dialog") >= 1)
		{
			return "KillFinalBossDialogRepeat";
		}

		//二周目重复对话
		if(SubSystem->GetSwitch("GrailLevel") >= 1 && SubSystem->GetSwitch("Fandral_WeeklyRound_StoryDialog") >= 1)
		{
			return "WeeklyRoundDialogRepeat";
		}
		
		//第三次对话 重复对话
		if(SubSystem->GetSwitch("Bakura_FandralAppear_StoryDialog") >= 1 && SubSystem->GetSwitch("Fandral_Third_StoryDialog") >= 1)
		{
			return "StoryThirdDialogRepeat";
		}

		//第二次对话 重复对话
		if(SubSystem->GetSwitch("Bakura_FandralAppear_StoryDialog") >= 1 && SubSystem->GetSwitch("Fandral_Second_StoryDialog") >= 1)
		{
			return "StorySecondDialogRepeat";
		}
		
		//保底重复对话
		return "StoryFirstDialogRepeat";
	}
	return "StoryFirstDialogRepeat";
}

FString UNpcDialogPicker::RoguePostiveAdventurerFirstDialogPicker(FNpcInfo NpcInfo, TArray<FString> Params)
{
	UAwRogueDataSystem* SubSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	if (SubSystem)
	{
		{
			if(SubSystem->GetSwitch("PostiveAdventurer_VeryFirst_Dialog") == 0)
			{
				return "Rogue_PostiveAdventurer_FirstDialog";
			}
			else
			{
				return "Rogue_PostiveAdventurer_DefaultStartDialog";
			}
		}
	}
	return NpcInfo.Personality.DialogModelId;
}

FString UNpcDialogPicker::RoguePostiveAdventurerStoryDialogPicker(FDialogScriptObj Dialog, TArray<FString> Params)
{
	UAwRogueDataSystem* SubSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	if (SubSystem)
	{
		//初次对话
		if(SubSystem->GetSwitch("PostiveAdventurer_First_StoryDialog") == 0)
		{
			return "StoryFirstDialog";
		}
		//大厅初次对话
		if(SubSystem->GetSwitch("PostiveAdventurer_AppearInHall") >= 1 && SubSystem->GetSwitch("PostiveAdventurer_Second_StoryDialog") == 0)
		{
			return "StorySecondDialog";
		}
		
		//二周目初次对话
		if(SubSystem->GetSwitch("PostiveAdventurer_AppearInHall") >= 1 && SubSystem->GetSwitch("GrailLevel") >= 1 &&
			SubSystem->GetSwitch("PostiveAdventurer_WeeklyRound_StoryDialog") == 0)
		{
			return "WeeklyRoundDialog";
		}
		//打完最终BOSS后的对话
		if(SubSystem->GetSwitch("PostiveAdventurer_AppearInHall") >= 1 && SubSystem->GetSwitch("KillFinalBoss") >= 1 &&
			SubSystem->GetSwitch("PostiveAdventurer_KillFinalBoss_Dialog") == 0)
		{
			return "KillFinalBossDialog";
		}

		//打完最终BOSS后的重复对话
		if(SubSystem->GetSwitch("PostiveAdventurer_AppearInHall") >= 1 && SubSystem->GetSwitch("KillFinalBoss") >= 1 &&
			SubSystem->GetSwitch("PostiveAdventurer_KillFinalBoss_Dialog") >= 1)
		{
			return "KillFinalBossDialogRepeat";
		}

		//二周目重复对话
		if(SubSystem->GetSwitch("PostiveAdventurer_AppearInHall") >= 1 && SubSystem->GetSwitch("GrailLevel") >= 1 &&
			SubSystem->GetSwitch("PostiveAdventurer_WeeklyRound_StoryDialog") >= 1)
		{
			return "WeeklyRoundDialogRepeat";
		}

		//大厅 重复对话
		if(SubSystem->GetSwitch("PostiveAdventurer_AppearInHall") >= 1 && SubSystem->GetSwitch("PostiveAdventurer_Second_StoryDialog") >= 1)
		{
			return "StorySecondDialogRepeat";
		}
		
		//保底重复对话
		return "StoryFirstDialogRepeat";
	}
	return "StoryFirstDialogRepeat";
}

FString UNpcDialogPicker::RogueGenericNPCDialogPicker(FNpcInfo NpcInfo, TArray<FString> Params)
{
    TArray<FString> DialogModelIds;

	for (const auto& DialogModel : UGameplayFuncLib::GetAwDataManager()->DialogModels)
	{
		if (!DialogModel.Value.Id.StartsWith("Rogue_"+NpcInfo.MobModelId))
		{
			continue;
		}			
		DialogModelIds.Add(DialogModel.Value.Id);
		bool condition_match = true;
		for (const FString Cond : DialogModel.Value.ConditionFunc)
		{
			FJsonFuncData JsonFunc = UCallFuncLib::StringToJsonFuncData(Cond);
			UFunction* Func = UCallFuncLib::JsonFuncToUFunc(JsonFunc);
			if (Func)
			{
				struct
				{
					FDialogScriptObj DSObj;
					TArray<FString> Params;
					bool Result;
				}FuncParam;
				//FuncParam.DSObj = Dialog;
				FuncParam.Params = JsonFunc.Params;
				UGameplayFuncLib::GetAwDataManager()->ProcessEvent(Func, &FuncParam);
				if (FuncParam.Result == false)
				{
					// UE_LOG(LogTemp, Warning, TEXT("DialogModel Id(Condition Match): %s"), *DialogModel.Value.Id);
					condition_match = false;
					break;
				}
				// condition_match = condition_match && FuncParam.Result;
			}
		}
		if (condition_match)
			return DialogModel.Value.Id;
	}
	return "Rogue_"+NpcInfo.MobModelId+"_DefaultStartDialog";

    // return NpcInfo.Personality.DialogModelId;
}


FString UNpcDialogPicker::RogueGenericNPCStoryDialogPicker(FDialogScriptObj Dialog, TArray<FString> Params)
{
	if (!Dialog.ModelId.IsEmpty())
	{
		FDialogScriptModel dialog =  UGameplayFuncLib::GetAwDataManager()->DialogModels[Dialog.ModelId];
		for (const auto& clip : UGameplayFuncLib::GetAwDataManager()->DialogModels[Dialog.ModelId].Scripts)
		{
			if (clip.Value.Id == dialog.FirstClipId)
				continue;
			bool condition_match = true;
			for (const FString Cond : clip.Value.ConditionFunc)
			{
				FJsonFuncData JsonFunc = UCallFuncLib::StringToJsonFuncData(Cond);
				UFunction* Func = UCallFuncLib::JsonFuncToUFunc(JsonFunc);
				if (Func)
				{
					struct
					{
						FDialogScriptObj DSObj;
						TArray<FString> Params;
						bool Result;
					}FuncParam;
					//FuncParam.DSObj = Dialog;
					FuncParam.Params = JsonFunc.Params;
					UGameplayFuncLib::GetAwDataManager()->ProcessEvent(Func, &FuncParam);
					if (FuncParam.Result == false)
					{
						// UE_LOG(LogTemp, Warning, TEXT("DialogModel Id(Condition Match): %s"), *DialogModel.Value.Id);
						condition_match = false;
						break;
					}
				}
			}
			if (condition_match)
				return clip.Value.Id;
		}
	}
	
	return "DefaultStoryDialog";
}
