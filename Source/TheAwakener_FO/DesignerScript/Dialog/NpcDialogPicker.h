// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/GamePlay/Characters/Creation/NpcInfo.h"
#include "TheAwakener_FO/GamePlay/Dialog/DialogStructs.h"
#include "UObject/Object.h"
#include "NpcDialogPicker.generated.h"

/**
 * 为Npc选择对话的脚本
 */
UCLASS()
class THEAWAKENER_FO_API UNpcDialogPicker : public UObject
{
	GENERATED_BODY()
public:
	//流浪商人对话选择
	UFUNCTION()
	static FString VagabondageVendorDialogPicker(FNpcInfo NpcInfo, TArray<FString> Params);

	//铁匠对话选择
	UFUNCTION()
	static FString BlacksmithDialogPicker(FNpcInfo NpcInfo, TArray<FString> Params);

	//小队长对话选择
	UFUNCTION()
	static FString RodianCaptainDialogPicker(FNpcInfo NpcInfo, TArray<FString> Params);

	//罗加洛斯对话选择
	UFUNCTION()
	static FString RodianRogarosDialogPicker(FNpcInfo NpcInfo, TArray<FString> Params);

	/**
	 *普通对话选择，符合普通的条件：
	 *1，有人类2000 1200 600 0，鼠人、哥布林1000 600 300 0的对话
	 *2，NpcId就是对话Json里面的Key_对话的。
	 */
	UFUNCTION()
	static FString NormalDialogPicker(FNpcInfo NpcInfo, TArray<FString> Params);

	//******************************Roguelike******************************************************************//

	//战士杰拉索第一句对话选择
	UFUNCTION(BlueprintCallable)
	static FString RogueGerassoFirstDialogPicker(FNpcInfo NpcInfo, TArray<FString> Params);
	
	//战士杰拉索故事对话选择(返回的是DefaultDialog的Clip的Id)
	UFUNCTION(BlueprintCallable)
	static FString RogueGerassoStoryDialogPicker(FDialogScriptObj Dialog, TArray<FString> Params);

	//剑舞者亨里克第一句对话选择
	UFUNCTION(BlueprintCallable)
	static FString RogueHenrikFirstDialogPicker(FNpcInfo NpcInfo, TArray<FString> Params);

	//剑舞者亨里克故事对话选择(返回的是DefaultDialog的Clip的Id)
	UFUNCTION(BlueprintCallable)
	static FString RogueHenrikStoryDialogPicker(FDialogScriptObj Dialog, TArray<FString> Params);

	//先锋索拉第一句对话选择
	UFUNCTION(BlueprintCallable)
	static FString RogueSolaFirstDialogPicker(FNpcInfo NpcInfo, TArray<FString> Params);

	//先锋索拉故事对话选择(返回的是DefaultDialog的Clip的Id)
	UFUNCTION(BlueprintCallable)
	static FString RogueSolaStoryDialogPicker(FDialogScriptObj Dialog, TArray<FString> Params);

	//勇士提尔达冈对话选择
	UFUNCTION(BlueprintCallable)
	static FString RogueTierdagonDialogPicker(FNpcInfo NpcInfo, TArray<FString> Params);
	//勇士提尔达冈故事对话选择(返回的是DefaultDialog的Clip的Id)
	UFUNCTION(BlueprintCallable)
	static FString RogueTierdagonStoryDialogPicker(FDialogScriptObj Dialog, TArray<FString> Params);
	

	//勇士施琳对话选择
	UFUNCTION(BlueprintCallable)
	static FString RogueCaelynnDialogPicker(FNpcInfo NpcInfo, TArray<FString> Params);
	//勇士施琳故事对话选择(返回的是DefaultDialog的Clip的Id)
	UFUNCTION(BlueprintCallable)
	static FString RogueCaelynnStoryDialogPicker(FDialogScriptObj Dialog, TArray<FString> Params);

	//管理人第一句对话选择
	UFUNCTION(BlueprintCallable)
	static FString RogueHephaesmoseFirstDialogPicker(FNpcInfo NpcInfo, TArray<FString> Params);

	//管理人故事对话选择(返回的是DefaultDialog的Clip的Id)
	UFUNCTION(BlueprintCallable)
	static FString RogueHephaesmoseStoryDialogPicker(FDialogScriptObj Dialog, TArray<FString> Params);

	//贼王巴库拉第一句对话选择
	UFUNCTION(BlueprintCallable)
	static FString RogueBakuraFirstDialogPicker(FNpcInfo NpcInfo, TArray<FString> Params);

	//贼王巴库拉故事对话选择(返回的是DefaultDialog的Clip的Id)
	UFUNCTION(BlueprintCallable)
	static FString RogueBakuraStoryDialogPicker(FDialogScriptObj Dialog, TArray<FString> Params);

	//范达尔（赫曼城卫士长）第一句对话选择
	UFUNCTION(BlueprintCallable)
	static FString RogueFandralFirstDialogPicker(FNpcInfo NpcInfo, TArray<FString> Params);

	//范达尔（赫曼城卫士长）故事对话选择(返回的是DefaultDialog的Clip的Id)
	UFUNCTION(BlueprintCallable)
	static FString RogueFandralStoryDialogPicker(FDialogScriptObj Dialog, TArray<FString> Params);

	//乐观的冒险者第一句对话选择
	UFUNCTION(BlueprintCallable)
	static FString RoguePostiveAdventurerFirstDialogPicker(FNpcInfo NpcInfo, TArray<FString> Params);

	//乐观的冒险者故事对话选择(返回的是DefaultDialog的Clip的Id)
	UFUNCTION(BlueprintCallable)
	static FString RoguePostiveAdventurerStoryDialogPicker(FDialogScriptObj Dialog, TArray<FString> Params);

	//通用对话选择
	UFUNCTION(BlueprintCallable)
	static FString RogueGenericNPCDialogPicker(FNpcInfo NpcInfo, TArray<FString> Params);
	UFUNCTION(BlueprintCallable)
	static FString RogueGenericNPCStoryDialogPicker(FDialogScriptObj Dialog, TArray<FString> Params);

};
