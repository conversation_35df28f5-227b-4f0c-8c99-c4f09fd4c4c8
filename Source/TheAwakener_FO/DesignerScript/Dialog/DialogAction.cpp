// Fill out your copyright notice in the Description page of Project Settings.


#include "DialogAction.h"

#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GameFramework/Manager/AwRogueDataSystem.h"
#include "TheAwakener_FO/Platform/PlatformCustomNodes.h"
#include "TheAwakener_FO/UI/DefeatedUI.h"
#include "TheAwakener_FO/UI/MediaPlayerUI.h"
#include "TheAwakener_FO/UI/Roguelike/RogueCareer/RogueCareer_Unlock.h"
#include "TheAwakener_FO/UI/Roguelike/RogueGetCurrency/RogueGetCurrency.h"
#include "TheAwakener_FO/UI/Roguelike/RogueGiveCurrencyToNPC/RogueGiveCurrencyToNPC.h"

FString UDialogAction::RandomFirstEventId(FDialogScriptObj Dialog, TArray<FString> Params)
{
	if(Params.Num() > 0)
	{
		const int RandomIndex = FMath::RandRange(0, Params.Num() - 1);
		return Params[RandomIndex];
	}
	return "";
}

FString UDialogAction::DirectGoTo(FDialogScriptObj Dialog, TArray<FString> Params)
{
	if (Params.Num() <= 0) return "";
	return Params[0];
}

FString UDialogAction::ChangeTargetDialog(FDialogScriptObj Dialog, TArray<FString> Params)
{
	if (Params.Num() <= 0 || !Dialog.DialogTarget) return "";
	Dialog.DialogTarget->NpcInfo.Personality.DialogModelId = Params[0];
	return "";
}

FString UDialogAction::ShowToast(FDialogScriptObj Dialog, TArray<FString> Params)
{
	if (Params.Num() <= 0) return "";

	UGameplayFuncLib::GetUiManager()->ShowToast(Params[0]);
	
	return "";
}

FString UDialogAction::SetRoleSwitchTo(FDialogScriptObj Dialog, TArray<FString> Params)
{
	if (!UGameplayFuncLib::GetAwGameInstance() || Params.Num() <= 0) return "";
	const int ToValue = Params.Num() >= 1 ? FCString::Atoi(*Params[1]) : 0;
	const int WasValue = UGameplayFuncLib::GetAwGameInstance()->RoleInfo.GetSwitch(Params[0], 0);
	if(ToValue > WasValue)
		UGameplayFuncLib::GetAwGameInstance()->RoleInfo.SetSwitchValue(Params[0], ToValue);
	
	return "";
}

FString UDialogAction::RandomChangeTargetDialog(FDialogScriptObj Dialog, TArray<FString> Params)
{
	if (Params.Num() <= 0 || !Dialog.DialogTarget) return "";
	if (Params.Num() == 1)
		Dialog.DialogTarget->NpcInfo.Personality.DialogModelId = Params[0];
	else
	{
		const int RIndex = FMath::RandRange(0, Params.Num() - 1);
		Dialog.DialogTarget->NpcInfo.Personality.DialogModelId = Params[RIndex];
	}
	return "";
}

FString UDialogAction::GivePlayerWeapon(FDialogScriptObj Dialog, TArray<FString> Params)
{
	if(Params.Num() > 1)
	{
		const FString ItemId = Params[0];
		const int Count = FCString::Atoi(*Params[1]);
		FThingObj ItemObj;
		ItemObj.Id = ItemId;
		ItemObj.Type = EThingType::WeaponModel;
		ItemObj.Count = Count;
		UGameplayFuncLib::GetAwGameInstance()->RoleInfo.GiveThing(ItemObj);
	}
	return "";
}

FString UDialogAction::GivePlayerEquipment(FDialogScriptObj Dialog, TArray<FString> Params)
{
	if(Params.Num() > 1)
	{
		const FString ItemId = Params[0];
		const int Count = FCString::Atoi(*Params[1]);
		FThingObj ItemObj;
		ItemObj.Id = ItemId;
		ItemObj.Type = EThingType::Equipment;
		ItemObj.Count = Count;
		UGameplayFuncLib::GetAwGameInstance()->RoleInfo.GiveThing(ItemObj);
	}
	return "";
}

FString UDialogAction::GivePlayerItem(FDialogScriptObj Dialog, TArray<FString> Params)
{
	if(Params.Num() > 1)
	{
		const FString ItemId = Params[0];
		const int Count = FCString::Atoi(*Params[1]);
		FThingObj ItemObj;
		ItemObj.Id = ItemId;
		ItemObj.Type = EThingType::Item;
		ItemObj.Count = Count;
		UGameplayFuncLib::GetAwGameInstance()->RoleInfo.GiveThing(ItemObj);
	}
	return "";
}

FString UDialogAction::GivePlayerPackage(FDialogScriptObj Dialog, TArray<FString> Params)
{
	if(Params.Num())
	{
		FVector Loc = FVector::ZeroVector;
		if(UGameplayFuncLib::GetAwGameState()->GetMyCharacter())
			Loc = UGameplayFuncLib::GetAwGameState()->GetMyCharacter()->GetActorLocation();
	
		UGameplayFuncLib::GetLootManager()->DropLootPackageByLootPackageIds(Params,Loc);
	}
	return "";
}

FString UDialogAction::PlayMediaByFilePath(FDialogScriptObj Dialog, TArray<FString> Params)
{
	if(Params.Num())
	{
		UMediaPlayerUI* MediaUI = Cast<UMediaPlayerUI>(UGameplayFuncLib::GetUiManager()->Show("MediaPlayer"));
		if(MediaUI)
		{
			UFileMediaSource* MediaSource = NewObject<UFileMediaSource>(GWorld, UFileMediaSource::StaticClass(), "NewMedia");
			MediaSource->SetFilePath(Params[0]);
			MediaUI->StartPlayMedia(MediaSource,false);
		}
	}
	return "";
}

FString UDialogAction::GoBackToVillage(FDialogScriptObj Dialog, TArray<FString> Params)
{
	UGameplayFuncLib::GetAwGameInstance()->BackToVillage();
	return "";
}

FString UDialogAction::ChangeLevel(FDialogScriptObj Dialog, TArray<FString> Params)
{
	if(Params.Num())
	{
		UGameplayFuncLib::GetUiManager()->ShowLoading();
		UGameplayFuncLib::GetAwGameInstance()->ChangeLevelByLevelName(Params[0],true,"");
	}
	return "";
}

FString UDialogAction::GiveBackBlacksmithTool(FDialogScriptObj Dialog, TArray<FString> Params)
{
	TArray<AActor*> House = UGameplayFuncLib::GetAwGameState()->GetImportantActorByName("BP_Smithy_01_Step02");
	if (House.Num() > 0)
	{
		House[0]->SetActorHiddenInGame(false);
		House[0]->SetActorEnableCollision(true);
	}
	House = UGameplayFuncLib::GetAwGameState()->GetImportantActorByName("BP_Smithy_01_Step01");
	if (House.Num() > 0)
	{
		House[0]->SetActorHiddenInGame(true);
		House[0]->SetActorEnableCollision(false);
	}
	
	//UGameplayFuncLib::GetAwGameInstance()->RoleInfo.SetSwitchValue( "Anim_MineTeamBlacksmith_Tool", 2);

	UMediaPlayerUI* MediaUI = Cast<UMediaPlayerUI>(UGameplayFuncLib::GetUiManager()->Show("MediaPlayer"));
	if(MediaUI)
	{
		UFileMediaSource* MediaSource = NewObject<UFileMediaSource>(GWorld, UFileMediaSource::StaticClass(), "VillageUpgrade");
		MediaSource->SetFilePath(TEXT("./MoviesInGame/MineTeamBlacksmith_Tool_1.mp4"));
		MediaUI->StartPlayMedia(MediaSource,false);
	}

	TArray<AActor*> Pos = UGameplayFuncLib::GetAwGameState()->GetImportantActorByName("PosAfterGiveBackTool");
	if (Pos.Num() > 0 && UGameplayFuncLib::GetAwGameState()->GetMyCharacter())
	{
		UGameplayFuncLib::GetAwGameState()->GetMyCharacter()->SetActorTransform(Pos[0]->GetTransform());
	}
	
	return "";
}

FString UDialogAction::RogueChangePawn(FDialogScriptObj Dialog, TArray<FString> Params)
{
	if(Params.Num())
	{
		if (Dialog.DialogTrigger)
			UGameplayFuncLib::ChangeRoguePawn(Params[0],Dialog.DialogTrigger->OwnerPlayerController->GetLocalPCIndex());
		else
			UE_LOG(LogTemp, Error, TEXT("PlayerController Not Found in %s"), *Dialog.ModelId);
	}
	return "";
}

FString UDialogAction::SetRogueSwitch(FDialogScriptObj Dialog, TArray<FString> Params)
{
	UAwRogueDataSystem* SubSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	if (SubSystem)
	{
		if(SubSystem->HasCurRogueData() && Params.Num() >= 2)
		{
			SubSystem->SetSwitch(Params[0],FCString::Atoi(*Params[1]));
			SubSystem->SaveData();
		}
	}
	return "";
}

FString UDialogAction::SetRogueSwitchOn(FDialogScriptObj Dialog, TArray<FString> Params)
{
	UAwRogueDataSystem* SubSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	if (SubSystem)
	{
		if(SubSystem->HasCurRogueData() && Params.Num() >= 1)
		{
			SubSystem->SetSwitch(Params[0], 1);
			SubSystem->SaveData();
		}
	}
	return "";
}

FString UDialogAction::GiveRogueCurrency(FDialogScriptObj Dialog, TArray<FString> Params)
{
	UAwRogueDataSystem* SubSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	if (SubSystem)
	{
		if(Params.Num() > 1)
		{
			FString CurrencyId = Params[0];
			int AddNum = FCString::Atoi(*Params[1]);
			int CurNum = SubSystem->GetCurrencyCount(CurrencyId,false);
			SubSystem->SetCurrencyCount(AddNum + CurNum, CurrencyId, false);
			UUserWidget* Widget = UGameplayFuncLib::GetUiManager()->Show("RogueGetCurrency");
			URogueGetCurrency* GetCurrencyWidget = Cast<URogueGetCurrency>(Widget);
			if(GetCurrencyWidget)
			{
				GetCurrencyWidget->InitClass(CurrencyId, AddNum);
			}
		}
	}
	return "";
}

FString UDialogAction::ShowUnlockCareerUI(FDialogScriptObj Dialog, TArray<FString> Params)
{
	if(Params.Num() > 1)
	{
		FString ClassName = Params[0];
		int Cost = FCString::Atoi(*Params[1]);
		UUserWidget* Widget = UGameplayFuncLib::GetUiManager()->Show("RogueUnlockCareer");
		URogueCareer_Unlock* UnlockWidget = Cast<URogueCareer_Unlock>(Widget);
		if(UnlockWidget)
		{
			UnlockWidget->InitClass(ClassName, Cost);
		}
	}
	return "";
}

FString UDialogAction::ShowPurchaseCareerUI(FDialogScriptObj Dialog, TArray<FString> Params)
{
	if(Params.Num() > 0)
	{
		FString ClassName = Params[0];
		UUserWidget* Widget = UGameplayFuncLib::GetUiManager()->Show("RogueUnlockCareer");
		URogueCareer_Unlock* UnlockWidget = Cast<URogueCareer_Unlock>(Widget);
		if(UnlockWidget)
		{
			UnlockWidget->InitClass(ClassName, 0,true);
		}
	}
	return "";
}

FString UDialogAction::ShowCurrencyShopUI(FDialogScriptObj Dialog, TArray<FString> Params)
{
	UGameplayFuncLib::GetUiManager()->Show("Rogue_CurrencyExchange");
	return "";
}

FString UDialogAction::ShowChangeWeaponUI(FDialogScriptObj Dialog, TArray<FString> Params)
{
	UGameplayFuncLib::GetUiManager()->Show("Rogue_ChangeWeapon");
	return "";
}

FString UDialogAction::ShowChangeSkinUI(FDialogScriptObj Dialog, TArray<FString> Params)
{
	UGameplayFuncLib::GetUiManager()->Show("Rogue_ChangeSkin");
	return "";
}

FString UDialogAction::ShowGiveCurrencyToNPCUI(FDialogScriptObj Dialog, TArray<FString> Params)
{
	if(Params.Num() > 1)
	{
		FString CurrencyName = Params[0];
		int CurrencyNum = FCString::Atoi(*Params[1]);
		FString GiveSuccessEventId = "";
		FString GiveFailureEventId = "";
		if(Params.Num() > 2)
			GiveSuccessEventId = Params[2];
		if(Params.Num() > 3)
			GiveFailureEventId = Params[3];
		UUserWidget* Widget = UGameplayFuncLib::GetUiManager()->Show("RogueGiveCurrencyToNPC");
		URogueGiveCurrencyToNPC* GiveCurrencyWidget = Cast<URogueGiveCurrencyToNPC>(Widget);
		if(GiveCurrencyWidget)
		{
			GiveCurrencyWidget->InitClass(CurrencyName, CurrencyNum, GiveSuccessEventId, GiveFailureEventId);
		}
	}
	return "";
}

void UDialogAction::SurvivorBattleStart(FDialogScriptObj Dialog, TArray<FString> Params)
{
	auto gm = UGameplayFuncLib::GetAwGameMode();
 
	// In this example, we want to call the event "CE_Trigger" that is defined in the actor's blueprint
	if (UFunction* TriggerFunction = gm->FindFunction(TEXT("StartTimeCount")))
	{
	    // Create a buffer just in case (if we send a null buffer, the system will crash if the event has parameters).
	    // (Check the codebase to see examples sending params.)
	    uint8* ParamsBuffer = static_cast<uint8*>(FMemory_Alloca(TriggerFunction->ParmsSize));
	    FMemory::Memzero(ParamsBuffer, TriggerFunction->ParmsSize);
	    gm->ProcessEvent(TriggerFunction, ParamsBuffer);
	}
}

void UDialogAction::SurvivorBattleContinue(FDialogScriptObj Dialog, TArray<FString> Params)
{
}

FString UDialogAction::SetSurvivorSwitch(FDialogScriptObj Dialog, TArray<FString> Params)
{
	UAwRogueDataSystem* SubSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	FString Key;
	int Value = 0;
	if(Params.Num() > 1)
	{
		Key = Params[0];
		Value = FCString::Atoi(*Params[1]);
	}
	else
	{
		return "";
	}
	if (SubSystem)
	{
		if (SubSystem->SwitchRoundSvl.Contains(Key))
			SubSystem->SwitchRoundSvl[Key] = Value;
	}
	return "";
}

FString UDialogAction::SetSurvivorSwitchOn(FDialogScriptObj Dialog, TArray<FString> Params)
{

	UAwRogueDataSystem* SubSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	FString Key;
	int Value = 0;
	if(Params.Num() > 0)
	{
		Key = Params[0];
	}
	else
	{
		return "";
	}
	if (SubSystem)
	{
		if (SubSystem->SwitchRoundSvl.Contains(Key))
			SubSystem->SwitchRoundSvl[Key] = 1;
		else
		{
			SubSystem->SwitchRoundSvl.Add(Key,1);
		}
	}
	return "";
}

void UDialogAction::OpenDLCStore(FDialogScriptObj Dialog, TArray<FString> Params)
{
	int DLCId = -1;
	if (Params.Num()>0)
	{
		DLCId = FCString::Atoi(*Params[0]);
		UPlatformCustomNodes::OpenStore(DLCId);
	}
}
