// Fill out your copyright notice in the Description page of Project Settings.


#include "DialogCondition.h"

#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GameFramework/Manager/AwRogueDataSystem.h"

bool UDialogCondition::Never(FDialogScriptObj Dialog, TArray<FString> Params)
{
	return false;
}

bool UDialogCondition::RoleSwitchRange(FDialogScriptObj Dialog, TArray<FString> Params)
{
	if (!UGameplayFuncLib::GetAwGameInstance() || Params.Num() < 1) return false;
	const int MinValue = Params.Num() > 1 ? FCString::Atoi(*Params[1]) : 1;
	const int MaxValue = Params.Num() > 2 ? FCString::Atoi(*Params[2]) : 1;
	const int CurValue = UGameplayFuncLib::GetAwGameInstance()->RoleInfo.GetSwitch(Params[0]);
	return MinValue <= CurValue && CurValue <= MaxValue;
}

bool UDialogCondition::TargetInRange(AAwCharacter* CatchedPlayer, UDialogBubbleComponent* DialogBubbleComponent, TArray<FString> Params)
{
	if (Params.Num()<1|| CatchedPlayer==nullptr)
	{
		return false;
	}

	const float Distance = Params.Num() > 0 ? FCString::Atof(*Params[0]) : 100;

	return FVector::Dist(CatchedPlayer->GetActorLocation(), DialogBubbleComponent->GetOwner()->GetActorLocation())<=Distance;
}

bool UDialogCondition::TargetHasWeapon(AAwCharacter* CatchedPlayer, UDialogBubbleComponent* DialogBubbleComponent, TArray<FString> Params)
{
	if (!CatchedPlayer)
	{
		return false;
	}
	return CatchedPlayer->GetArmState()==EArmState::Armed;
}

bool UDialogCondition::SelfHasWeapon(AAwCharacter* CatchedPlayer, UDialogBubbleComponent* DialogBubbleComponent, TArray<FString> Params)
{
	AAwCharacter* OwnerCharacter = Cast<AAwCharacter>(DialogBubbleComponent->GetOwner());
	if (!OwnerCharacter)
	{
		return false;
	}

	return OwnerCharacter->GetArmState() == EArmState::Armed;
}

bool UDialogCondition::TeamSideEqual(AAwCharacter* CatchedPlayer, UDialogBubbleComponent* DialogBubbleComponent, TArray<FString> Params)
{
	if (!CatchedPlayer)
	{
		return false;
	}
	AAwCharacter* OwnerCharacter = Cast<AAwCharacter>(DialogBubbleComponent->GetOwner());
	if (!OwnerCharacter)
	{
		return false;
	}

	const int TeamSide = Params.Num() > 0 ? FCString::Atof(*Params[0]) : CatchedPlayer->Side;

	return OwnerCharacter->Side == TeamSide;
}

bool UDialogCondition::OwnerNotInWar(AAwCharacter* CatchedPlayer, UDialogBubbleComponent* DialogBubbleComponent, TArray<FString> Params)
{
	AAwCharacter* OwnerCharacter = Cast<AAwCharacter>(DialogBubbleComponent->GetOwner());
	if (!OwnerCharacter)
	{
		return false;
	}
	if (OwnerCharacter->InWar())
	{
		GEngine->AddOnScreenDebugMessage(NULL, 2, FColor::Yellow, "True");
	}
	else
	{
		GEngine->AddOnScreenDebugMessage(NULL, 2, FColor::Yellow,"False");
	}


	return !OwnerCharacter->InWar();
}

bool UDialogCondition::OwnerIsLive(AAwCharacter* CatchedPlayer, UDialogBubbleComponent* DialogBubbleComponent, TArray<FString> Params)
{
	AAwCharacter* OwnerCharacter = Cast<AAwCharacter>(DialogBubbleComponent->GetOwner());
	if (!OwnerCharacter)
	{
		return false;
	}
	return !OwnerCharacter->Dead();
}

bool UDialogCondition::CheckRogueSwitchGreater(FDialogScriptObj Dialog, TArray<FString> Params)
{
	UAwRogueDataSystem* SubSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	if (SubSystem)
	{
		if(Params.Num() > 0)
		{
			FString SwitchId = Params[0];
			int CheckValue = 1;
			if(Params.Num() > 1)
				CheckValue = FCString::Atoi(*Params[1]);
			if(SubSystem->GetSwitch(SwitchId) >= CheckValue)
				return true;
		}
	}
	return false;
}

bool UDialogCondition::CheckRogueSwitchLess(FDialogScriptObj Dialog, TArray<FString> Params)
{
	UAwRogueDataSystem* SubSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	if (SubSystem)
	{
		if(Params.Num() > 0)
		{
			FString SwitchId = Params[0];
			int CheckValue = 0;
			if(Params.Num() > 1)
			{
				CheckValue = FCString::Atoi(*Params[1]);
			}
			if(SubSystem->GetSwitch(SwitchId) <= CheckValue)
			{
				return true;
			}
		}
	}
	return false;
}

bool UDialogCondition::CheckRogueSwitchEqual(FDialogScriptObj Dialog, TArray<FString> Params)
{
	UAwRogueDataSystem* SubSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	if (SubSystem)
	{
		if(Params.Num() > 0)
		{
			FString SwitchId = Params[0];
			int CheckValue = 0;
			if(Params.Num() > 1)
			{
				CheckValue = FCString::Atoi(*Params[1]);
			}
			else
			{
				UE_LOG(LogTemp, Log, TEXT("Params not compare number"));
			}
			if(SubSystem->GetSwitch(SwitchId) == CheckValue)
			{
				return true;
			}
		}
	}
	return false;
}

bool UDialogCondition::CheckRogueSwitchOn(FDialogScriptObj Dialog, TArray<FString> Params)
{
	UAwRogueDataSystem* SubSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	if (SubSystem)
	{
		if(Params.Num() > 0)
		{
			FString SwitchId = Params[0];
			int CheckValue = 1;
			if(SubSystem->GetSwitch(SwitchId) >= CheckValue)
			{
				return true;
			}
		}
	}
	return false;
}

bool UDialogCondition::CheckRogueSwitchOff(FDialogScriptObj Dialog, TArray<FString> Params)
{
	UAwRogueDataSystem* SubSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	if (SubSystem)
	{
		if(Params.Num() > 0)
		{
			const FString SwitchId = Params[0];
			int CheckValue = 0;
			if(SubSystem->GetSwitch(SwitchId) <= CheckValue)
			{
				return true;
			}
		}
	}
	return  false;
}

bool UDialogCondition::CheckCareerUnlock(FDialogScriptObj Dialog, TArray<FString> Params)
{
	UAwRogueDataSystem* SubSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	if (SubSystem)
	{
		if(Params.Num() > 0)
		{
			FString CareerId = Params[0];
			if(SubSystem->GetHasUnlockClass(CareerId))
				return true;
		}
	}
	return false;
}

bool UDialogCondition::CheckCareerLock(FDialogScriptObj Dialog, TArray<FString> Params)
{
	UAwRogueDataSystem* SubSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	if (SubSystem)
	{
		if(Params.Num() > 0)
		{
			FString CareerId = Params[0];
			if(!SubSystem->GetHasUnlockClass(CareerId))
				return true;
		}
	}
	return false;
}

bool UDialogCondition::GetClearedGame(FDialogScriptObj Dialog, TArray<FString> Params)
{
	UAwRogueDataSystem* SubSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	return SubSystem->GetIsClearedGame();
}

bool UDialogCondition::CheckDeadTimesGreater(FDialogScriptObj Dialog, TArray<FString> Params)
{
	UAwRogueDataSystem* SubSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	if (SubSystem)
	{
		FString CurClassId = SubSystem->GetCurPawnClassId(0);
		FRogueClassRecord CurClassRecord = SubSystem->GetBattleRecord().ClassRecords[CurClassId];
		int DeadTimes = CurClassRecord.TotalGameTimes - CurClassRecord.ClearedGameTimes;
		int CheckValue = 0;
		if(Params.Num() > 0)
		{
			CheckValue = FCString::Atoi(*Params[0]);
		}
		return  DeadTimes >= CheckValue;
	}
	return false;
}

bool UDialogCondition::CheckSurvivorSwitch(FDialogScriptObj Dialog, TArray<FString> Params)
{
	UAwRogueDataSystem* SubSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	if (SubSystem)
	{
		if(Params.Num() > 1)
		{
			FString SwitchId = Params[0];
			int CheckValue = FCString::Atoi(*Params[1]);
			if(SubSystem->SwitchRoundSvl.Contains(SwitchId) && SubSystem->SwitchRoundSvl[SwitchId] >= CheckValue)
			{
				return true;
			}
			else
			{
				return CheckValue == 0;
			}
		}
	}
	return false;
}

bool UDialogCondition::CheckSurvivorSwitchOn(FDialogScriptObj Dialog, TArray<FString> Params)
{
	UAwRogueDataSystem* SubSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	if (SubSystem)
	{
		if(Params.Num() > 0)
		{
			FString SwitchId = Params[0];
			int CheckValue = 1;
			if(SubSystem->SwitchRoundSvl.Contains(SwitchId) && SubSystem->SwitchRoundSvl[SwitchId] >= CheckValue)
			{
				return true;
			}
		}
	}
	return false;
}

bool UDialogCondition::CheckSurvivorSwitchOff(FDialogScriptObj Dialog, TArray<FString> Params)
{
	UAwRogueDataSystem* SubSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	if (SubSystem)
	{
		if(Params.Num() > 0)
		{
			const FString SwitchId = Params[0];
			int CheckValue = 0;
			if(!SubSystem->SwitchRoundSvl.Contains(SwitchId) || SubSystem->SwitchRoundSvl[SwitchId] <= CheckValue)
			{
				return true;
			}
		}
	}
	return  false;
}
