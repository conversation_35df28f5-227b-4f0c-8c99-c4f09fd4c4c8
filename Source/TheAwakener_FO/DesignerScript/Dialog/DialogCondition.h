// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/GamePlay/Dialog/DialogStructs.h"
#include "TheAwakener_FO/GamePlay/Dialog/DialogBubbleComponent.h"
#include "UObject/Object.h"
#include "DialogCondition.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UDialogCondition : public UObject
{
	GENERATED_BODY()
public:
	//Dialog
	UFUNCTION()
	static bool Never(FDialogScriptObj Dialog, TArray<FString> Params);

	//检查是否某个Role的Switch值处于范围内，[0]SwitchKey, [1]>=这个值达标，[2]<=这个值达标
	UFUNCTION()
	static bool RoleSwitchRange(FDialogScriptObj Dialog, TArray<FString> Params);
	
	//DialogBubble
	UFUNCTION()
		static bool TargetInRange(AAwCharacter* CatchedPlayer, UDialogBubbleComponent* DialogBubbleComponent, TArray<FString> Params);
	UFUNCTION()
		static bool TargetHasWeapon(AAwCharacter* CatchedPlayer, UDialogBubbleComponent* DialogBubbleComponent, TArray<FString> Params);
	UFUNCTION()
		static bool SelfHasWeapon(AAwCharacter* CatchedPlayer, UDialogBubbleComponent* DialogBubbleComponent, TArray<FString> Params);
	UFUNCTION()
		static bool TeamSideEqual(AAwCharacter* CatchedPlayer, UDialogBubbleComponent* DialogBubbleComponent, TArray<FString> Params);
	UFUNCTION()
		static bool OwnerNotInWar(AAwCharacter* CatchedPlayer, UDialogBubbleComponent* DialogBubbleComponent, TArray<FString> Params);
	UFUNCTION()
		static bool OwnerIsLive(AAwCharacter* CatchedPlayer, UDialogBubbleComponent* DialogBubbleComponent, TArray<FString> Params);


	/********************************Roguelike******************************/
	//检测SwitchId为Params[0]的Value是否大于等于Params[1]
	UFUNCTION()
	static bool CheckRogueSwitchGreater(FDialogScriptObj Dialog, TArray<FString> Params);

	//检测SwitchId为Params[0]的Value是否小于等于Params[1]
	UFUNCTION()
	static bool CheckRogueSwitchLess(FDialogScriptObj Dialog, TArray<FString> Params);

	
	//检测SwitchId为Params[0]的Value是否等于Params[1]
	UFUNCTION()
	static bool CheckRogueSwitchEqual(FDialogScriptObj Dialog, TArray<FString> Params);
	
	//检测SwitchId为Params[0]的Value是否大于等于1
	UFUNCTION()
	static bool CheckRogueSwitchOn(FDialogScriptObj Dialog, TArray<FString> Params);

	
	//检测SwitchId为Params[0]的Value是否小于等于0
	UFUNCTION()
	static bool CheckRogueSwitchOff(FDialogScriptObj Dialog, TArray<FString> Params);

	//检测是否解锁Id为Params[0]的职业
	UFUNCTION()
	static bool CheckCareerUnlock(FDialogScriptObj Dialog, TArray<FString> Params);

	//检测是否没有解锁Id为Params[0]的职业
	UFUNCTION()
	static bool CheckCareerLock(FDialogScriptObj Dialog, TArray<FString> Params);

	//检测是否通关
	UFUNCTION()
	static bool GetClearedGame(FDialogScriptObj Dialog, TArray<FString> Params);

	//检测死亡次数是否大于params[0]
	UFUNCTION()
	static bool CheckDeadTimesGreater(FDialogScriptObj Dialog, TArray<FString> Params);

	//Survivor 幸存者 相关条件
	//检测SwitchId为Params[0]的Value是否大于等于参数
	UFUNCTION(BlueprintCallable,BlueprintPure)
	static bool CheckSurvivorSwitch(FDialogScriptObj Dialog, TArray<FString> Params);
	//检测SwitchId为Params[0]的Value是否大于等于1
	UFUNCTION(BlueprintCallable,BlueprintPure)
	static bool CheckSurvivorSwitchOn(FDialogScriptObj Dialog, TArray<FString> Params);
	//检测SwitchId为Params[0]的Value是否小于等于0
	UFUNCTION(BlueprintCallable,BlueprintPure)
	static bool CheckSurvivorSwitchOff(FDialogScriptObj Dialog, TArray<FString> Params);
};
