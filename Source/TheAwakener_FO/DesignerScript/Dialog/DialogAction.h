// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/GamePlay/Dialog/DialogStructs.h"
#include "UObject/Object.h"
#include "DialogAction.generated.h"

/**
 * 对话执行的事件的脚本
 */
UCLASS()
class THEAWAKENER_FO_API UDialogAction : public UObject
{
	GENERATED_BODY()
public:
	//在Params中随机一个FirstEventId返回
	UFUNCTION()
	static FString RandomFirstEventId(FDialogScriptObj Dialog, TArray<FString> Params);
	
	//直接跳转到Params[0]对应Id的Event
	UFUNCTION()
	static FString DirectGoTo(FDialogScriptObj Dialog, TArray<FString> Params);

	//改变对话目标的DialogModelId为Params[0]
	UFUNCTION()
	static FString ChangeTargetDialog(FDialogScriptObj Dialog, TArray<FString> Params);

	//显示一个Toast，文字内容为Params[0]
	UFUNCTION()
	static FString ShowToast(FDialogScriptObj Dialog, TArray<FString> Params);

	//设置角色的Switch到一个值，Params[0]是switch的key，[1]是设置的值（int）
	UFUNCTION()
	static FString SetRoleSwitchTo(FDialogScriptObj Dialog, TArray<FString> Params);

	//随机从Params中选择一个作为ChangeTargetDialog的参数，如果没有一个，就啥也不会执行
	UFUNCTION()
	static FString RandomChangeTargetDialog(FDialogScriptObj Dialog, TArray<FString> Params);

	//给玩家武器奖励，Params[0]:WeaponId, Params[1]:Count
	UFUNCTION()
	static FString GivePlayerWeapon(FDialogScriptObj Dialog, TArray<FString> Params);

	//给玩家装备奖励，Params[0]:EquipmentId, Params[1]:Count
	UFUNCTION()
	static FString GivePlayerEquipment(FDialogScriptObj Dialog, TArray<FString> Params);

	//给玩家物品奖励，Params[0]:ItemId, Params[1]:Count
	UFUNCTION()
	static FString GivePlayerItem(FDialogScriptObj Dialog, TArray<FString> Params);

	//根据LootPackageId给玩家奖励，Params:PackageIds
	UFUNCTION()
	static FString GivePlayerPackage(FDialogScriptObj Dialog, TArray<FString> Params);

	//根据Params[0]填写的视频地址播放视频过场
	UFUNCTION()
	static FString PlayMediaByFilePath(FDialogScriptObj Dialog, TArray<FString> Params);

	//完成铁匠任务后执行的事情
	UFUNCTION()
	static FString GiveBackBlacksmithTool(FDialogScriptObj Dialog, TArray<FString> Params);

	//再次回村
	UFUNCTION()
	static FString GoBackToVillage(FDialogScriptObj Dialog, TArray<FString> Params);

	//切图
	UFUNCTION()
	static FString ChangeLevel(FDialogScriptObj Dialog, TArray<FString> Params);

	//*************************Roguelike******************************************//
	//切职业
	UFUNCTION()
	static FString RogueChangePawn(FDialogScriptObj Dialog, TArray<FString> Params);
	//设置RogueData的Switch到一个值，Params[0]是switch的key，[1]是设置的值（int）
	UFUNCTION()
	static FString SetRogueSwitch(FDialogScriptObj Dialog, TArray<FString> Params);
	//设置RogueData的Switch到1，Params[0]是switch的key
	UFUNCTION()
	static FString SetRogueSwitchOn(FDialogScriptObj Dialog, TArray<FString> Params);
	//Params[0]为货币ID，Params[1]为货币数量
	UFUNCTION()
	static FString GiveRogueCurrency(FDialogScriptObj Dialog, TArray<FString> Params);
	//打开解锁职业的UI
	UFUNCTION()
	static FString ShowUnlockCareerUI(FDialogScriptObj Dialog, TArray<FString> Params);
	//打开解锁职业的UI
	UFUNCTION()
	static FString ShowPurchaseCareerUI(FDialogScriptObj Dialog, TArray<FString> Params);
	//打开货币商店的UI
	UFUNCTION()
	static FString ShowCurrencyShopUI(FDialogScriptObj Dialog, TArray<FString> Params);
	//打开切换武器的UI
	UFUNCTION()
	static FString ShowChangeWeaponUI(FDialogScriptObj Dialog, TArray<FString> Params);
	//打开切换皮肤的UI
	UFUNCTION()
	static FString ShowChangeSkinUI(FDialogScriptObj Dialog, TArray<FString> Params);
	//打开送货币给NPC的UI
	//Params[0]:CurrencyName
	//Parmas[1]:CurrencyNum
	//Params[2]:GiveSuccessEventId
	//Params[3]:GiveFailureEventId
	UFUNCTION()
	static FString ShowGiveCurrencyToNPCUI(FDialogScriptObj Dialog, TArray<FString> Params);

	//点击选项 开始幸存者战斗
	UFUNCTION(BlueprintCallable)
	static void SurvivorBattleStart(FDialogScriptObj Dialog, TArray<FString> Params);
	//点击选项 继续战斗
	UFUNCTION(BlueprintCallable)
	static void SurvivorBattleContinue(FDialogScriptObj Dialog, TArray<FString> Params);
	//设置SurvivorData的Switch到一个值，Params[0]是switch的key，[1]是设置的值（int）
	UFUNCTION(BlueprintCallable)
	static FString SetSurvivorSwitch(FDialogScriptObj Dialog, TArray<FString> Params);
	//设置SurvivorData的Switch到1，Params[0]是switch的key
	UFUNCTION(BlueprintCallable)
	static FString SetSurvivorSwitchOn(FDialogScriptObj Dialog, TArray<FString> Params);

	UFUNCTION(BlueprintCallable)
	static void OpenDLCStore(FDialogScriptObj Dialog, TArray<FString> Params);
};
