// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/Gameframework/Timeline/TimelineNode.h"
#include "TheAwakener_FO/GamePlay/Bullet/AwBullet.h"
#include "UObject/Object.h"
#include "IceDevilBulletScript.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UIceDevilBulletScript : public UObject
{
	GENERATED_BODY()
public:
	//【OnCreate】调整Bullet的OriginRotation，使冰镰刀能向下碰到地板，Params[0],向下调整多少度
	UFUNCTION()
	static UTimelineNode* SetBulletOriginRotation(AAwBullet* Bullet, TArray<FString> Params);

	//【OnHit】对Hit到的Character或者场景物件产生伤害
	UFUNCTION()
	static UTimelineNode* DealDamageOnHit(AAwBullet* Bullet, FBeCaughtActorInfo CaughtInfo, TArray<FString> Params);
	
	//【OnRemoved】检测是否碰到世界，创建一个冰镰刀的SceneItem
	//Params[0] ：自然消亡特效
	//Params[0] ：碰到地形爆炸特效
	UFUNCTION()
	static bool CreateIceScytheSceneItem(AAwBullet* Bullet, EBulletRemoveType RemoveType, TArray<FString> Params);
};
