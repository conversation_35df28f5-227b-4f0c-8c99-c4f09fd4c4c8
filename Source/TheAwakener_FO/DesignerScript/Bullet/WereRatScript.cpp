// Fill out your copyright notice in the Description page of Project Settings.


#include "WereRatScript.h"

#include "Kismet/KismetMathLibrary.h"

FVector UWereRatScript::CheeseThrownPath(float TimeElapsed, AAwBullet* BulletObj, TArray<FString> Params)
{
	if (BulletObj->IsActorBeingDestroyed())
		return FVector(0, 0, 0);

	const FTransform OriginTransform = BulletObj->GetOriginTransform();

	float StraightPower = Params.Num() > 0 ? FCString::Atof(*Params[0]) : 0; //前抛速度，厘米/秒
	float ThrowPower = Params.Num() > 1 ? FCString::Atof(*Params[1]) : 0;	//上抛的速度，厘米/秒
	float FallWeight = Params.Num() > 2 ? FCString::Atof(*Params[2]) : 0;   //下落速度，厘米/秒
	constexpr int FPS = 60;	//假设每秒60帧算
	StraightPower /= FPS;
	ThrowPower /= FPS;
	FallWeight /= FPS;
	
	const int TickElapsed = FMath::FloorToInt(TimeElapsed * FPS);

	const float UpperDegree = OriginTransform.Rotator().Pitch;
//	UKismetSystemLibrary::PrintString(GWorld, FString("Upper Degree:").Append(FString::SanitizeFloat(UpperDegree)),
	//	true, true, FLinearColor::Yellow, 30);
	StraightPower = UKismetMathLibrary::DegCos(UpperDegree) * StraightPower;
	ThrowPower = (UKismetMathLibrary::DegSin(UpperDegree) + 1) * ThrowPower;

	float CurUpper = 0;
	float Weight = 0;
	for (int i = 0; i < TickElapsed; i++)
	{
		Weight += FallWeight;
		CurUpper += ThrowPower - Weight;
	}
	const float CurForward = StraightPower * TickElapsed;
	
	return FVector(CurForward , 0, CurUpper);
}
