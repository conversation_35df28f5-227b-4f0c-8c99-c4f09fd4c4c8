// Fill out your copyright notice in the Description page of Project Settings.


#include "OgreBulletScript.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GamePlay/Battle/OffenseManager.h"

//Params[0]:VelocityXY Params[1]:VelocityZ Params[2]:InSec(期望完成轨迹时间，单位秒)
FVector UOgreBulletScript::PillarParabolaPath(float TimeElapsed, AAwBullet* BulletObj, TArray<FString> Params)
{
	if (BulletObj->IsActorBeingDestroyed())
		return FVector(0, 0, 0);
	FTransform OriginTransform = BulletObj->GetOriginTransform();
	float InSec = FCString::Atof(*Params[2]);
	float CurX = FCString::Atof(*Params[0]) * TimeElapsed;
	float VelocityZ = FCString::Atof(*Params[1]);
	float OffestZ = -1 * VelocityZ / 10 * TimeElapsed * TimeElapsed + InSec * (VelocityZ / 10) * TimeElapsed;
	return FVector(CurX, 0, OffestZ);
}

UTimelineNode* UOgreBulletScript::DealDamageOnHit(AAwBullet* Bullet, FBeCaughtActorInfo CaughtInfo, TArray<FString> Params)
{
	if (Bullet->GetLocalRole() != ENetRole::ROLE_Authority)
		return nullptr;
	AAwCharacter* Character = Cast<AAwCharacter>(CaughtInfo.BeCaughtActor);
	if(Character == Bullet->Caster) return nullptr;
	UCharacterHitBoxData* CharacterHitBox = Cast<UCharacterHitBoxData>(CaughtInfo.CaughtHitBoxData);
	if (Character && CharacterHitBox)
		
	{
		FOffenseInfo Offense = FOffenseInfo();
		Offense.AttackInfo = Bullet->AttackInfo;
		Offense.AttackInfo.DamageIncomeVector = Bullet->GetActorRotation().Vector();
		Offense.SourceId = Bullet->Model.Id;
		Offense.AttackHitBoxName.Add(CaughtInfo.AttackBoxName);
		Offense.Index = Bullet->Life;
		Offense.CanHitTimes = 1;
		UOffenseManager::DoOffense(Bullet->AttackHitManager, Offense, CaughtInfo, Bullet->Caster, false);
		if (Params.Num())
		{
			UGameplayFuncLib::CreateVFXByPathAtLocation(Params[0], CaughtInfo.BeCaughtActor->GetActorTransform(), true, true);
		}
	}
	
	return nullptr;
}

UTimelineNode* UOgreBulletScript::DestroyAllPillar(AAwBullet* Bullet, FBeCaughtActorInfo CaughtInfo, TArray<FString> Params)
{
	
	AAwSceneItem* SceneItem = Cast<AAwSceneItem>(CaughtInfo.BeCaughtActor);
	if (
		SceneItem && SceneItem->Id == "OgrePillar" 
	){
		SceneItem->Kill();
	}
	return nullptr;
}

UTimelineNode* UOgreBulletScript::CreateExplodePillarOnHit(AAwBullet* Bullet, FBeCaughtActorInfo CaughtInfo, TArray<FString> Params)
{
	AAwCharacter* Character = Cast<AAwCharacter>(CaughtInfo.BeCaughtActor);
	if (Character)
	{
		FSceneItemModel Model = UGameplayFuncLib::GetDataManager()->GetSceneItemModelById("OgreExplodePillar");
		UGameplayFuncLib::CreateSceneItem(Model, 1, Bullet->GetActorTransform());
	}
	return nullptr;
}


bool UOgreBulletScript::CreateHitSceneFX(AAwBullet* Bullet, EBulletRemoveType RemoveType, TArray<FString> Params)
{
	if (Params.Num())
		UGameplayFuncLib::CreateVFXByPathAtLocation(Params[0], Bullet->GetActorTransform(), true, true);
	return true;
}

bool UOgreBulletScript::CreateExplodePillarOnRemoved(AAwBullet* Bullet, EBulletRemoveType RemoveType, TArray<FString> Params)
{
	FSceneItemModel Model = UGameplayFuncLib::GetDataManager()->GetSceneItemModelById("OgreExplodePillar");
	UGameplayFuncLib::CreateSceneItem(Model, 1, Bullet->GetActorTransform());
	return true;
}

