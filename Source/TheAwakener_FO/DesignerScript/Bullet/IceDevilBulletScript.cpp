// Fill out your copyright notice in the Description page of Project Settings.


#include "IceDevilBulletScript.h"

#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GamePlay/Battle/OffenseManager.h"

UTimelineNode* UIceDevilBulletScript::SetBulletOriginRotation(AAwBullet* Bullet, TArray<FString> Params)
{
	FTransform Trans = Bullet->GetOriginTransform();
	float ChangeDegree = 0;
	if(Params.Num())
		ChangeDegree = FCString::Atof(*Params[0]);
	FRotator NewRot = Trans.Rotator();
	NewRot.Pitch += ChangeDegree;
	NewRot.Yaw += FMath::RandRange(-5.0f, 5.0f);
	Trans.SetRotation(NewRot.Quaternion());
	Bullet->SetOriginTransform(Trans);
	
	return nullptr;
}

UTimelineNode* UIceDevilBulletScript::DealDamageOnHit(AAwBullet* Bullet, FBeCaughtActorInfo CaughtInfo, TArray<FString> Params)
{
	if (Bullet->GetLocalRole() != ENetRole::ROLE_Authority)
		return nullptr;
	AAwCharacter* Character = Cast<AAwCharacter>(CaughtInfo.BeCaughtActor);
	UCharacterHitBoxData* CharacterHitBox = Cast<UCharacterHitBoxData>(CaughtInfo.CaughtHitBoxData);
	//打到玩家
	if (Character && CharacterHitBox)
	{
		FOffenseInfo Offense = FOffenseInfo();
		Offense.AttackInfo = Bullet->AttackInfo;
		Offense.AttackInfo.DamagePower = Bullet->Caster->CharacterObj.BaseProp.PAttack;
		if (Params.Num() > 0)
			Offense.AttackInfo.DamagePower = Offense.AttackInfo.DamagePower * FCString::Atof(*Params[0]);
		Offense.AttackInfo.DamageIncomeVector = Bullet->GetActorRotation().Vector();
		Offense.AttackHitBoxName.Add(CaughtInfo.AttackBoxName); 
		Offense.SourceId = Bullet->Model.Id;
		Offense.CanHitTimes = 1;
		Offense.Index = Bullet->Life;
		UOffenseManager::DoOffense(Bullet->AttackHitManager, Offense, CaughtInfo, Bullet->Caster, false);
	}
	else if(CaughtInfo.BeCaughtActor && CaughtInfo.CaughtHitBoxData)
	{
		AAwSceneItem* SceneItem = Cast<AAwSceneItem>(CaughtInfo.BeCaughtActor);
		if(SceneItem)
		{
			if(SceneItem->Tags.Contains("IceDevilSceneItem"))
			{
				FOffenseInfo Offense = FOffenseInfo();
				Offense.AttackInfo = Bullet->AttackInfo;
				Offense.AttackInfo.DamagePower = 999999;
				Offense.AttackInfo.DamageIncomeVector = Bullet->GetActorRotation().Vector();
				Offense.AttackHitBoxName.Add(CaughtInfo.AttackBoxName);
				Offense.SourceId = Bullet->Model.Id;
				Offense.CanHitTimes = 1;
				Offense.Index = Bullet->Life;
				UOffenseManager::DoOffense(Bullet->AttackHitManager, Offense, CaughtInfo, Bullet->Caster, false);
				//生成一个AOE
				const AAWAoe* Aoe = UGameplayFuncLib::CreateAOE(Bullet->Caster, "IceDevilFreezingAOE", SceneItem->GetActorLocation(), FVector(1, 0, 0), 0.2, "");
				if(Aoe)
				{
					Aoe->AttackHitManager->ActiveAllAttackHitBox();
				}
				Bullet->Life--;
			}
		}
	}
	return nullptr;
}

bool UIceDevilBulletScript::CreateIceScytheSceneItem(AAwBullet* Bullet, EBulletRemoveType RemoveType, TArray<FString> Params)
{
	//检测是不是自然消亡
	if(Bullet->BulletLifeSpan - Bullet->LivedTime <= 0)
	{
		if (Params.Num())
			UGameplayFuncLib::CreateVFXByPathAtLocation(Params[0], Bullet->GetActorTransform(), true, true);
	}
	//碰到地形
	else
	{
		FSceneItemModel Model = UGameplayFuncLib::GetAwDataManager()->GetSceneItemModelById("IceScythe");
		FVector SpawnLoc = Bullet->GetActorLocation();
		//射线检测地面
		FHitResult HitResult;
		TArray<TEnumAsByte<EObjectTypeQuery>> CheckType;
		CheckType.Add(ObjectTypeQuery1);
		const bool bIsHit = UKismetSystemLibrary::CapsuleTraceSingleForObjects(
													Bullet, Bullet->GetActorLocation(), Bullet->GetActorLocation() + FVector::DownVector * 1000.0f, 40, 40,
													CheckType, false, TArray<AActor*>(), EDrawDebugTrace::None, HitResult, true);
		// const bool bIsHit = UKismetSystemLibrary::CapsuleTraceSingle(
		// 										Bullet, Bullet->GetActorLocation(), Bullet->GetActorLocation() + FVector::DownVector * 1000.0f, 40, 40,
		// 										TraceTypeQuery4, false, TArray<AActor*>(), EDrawDebugTrace::None, HitResult, true);
		if(bIsHit)
		{
			FCharacterCamp CasterCamp = UGameplayFuncLib::GetAwDataManager()->GetCampInfoBySide(Bullet->Caster->Side);
			int SceneItemSide = 15;
			if(CasterCamp.CanAttackCampId.Num())
			{
				int RandIndex = FMath::RandRange(0, CasterCamp.CanAttackCampId.Num() - 1);
				if(CasterCamp.CanAttackCampId[RandIndex] != 0)
					SceneItemSide = CasterCamp.CanAttackCampId[RandIndex];
			}
			UGameplayFuncLib::CreateSceneItem(Model, SceneItemSide,
				FTransform(FRotator(0,Bullet->GetOriginTransform().Rotator().Yaw,0),HitResult.ImpactPoint,FVector(1,1,1)));
			if (Params.Num() > 1)
				UGameplayFuncLib::CreateVFXByPathAtLocation(Params[1], Bullet->GetActorTransform(), true, true);
		}
	}
		
	return true;
}
