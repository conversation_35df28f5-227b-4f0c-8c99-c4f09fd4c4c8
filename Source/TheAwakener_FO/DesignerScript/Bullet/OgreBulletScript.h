// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "TheAwakener_FO/GameFramework/Timeline/TimelineNode.h"
#include "TheAwakener_FO/GamePlay/Bullet/AwBullet.h"
#include "OgreBulletScript.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UOgreBulletScript : public UObject
{
	GENERATED_BODY()
	
	//【Tween】抛物线飞行
	UFUNCTION()
	static FVector PillarParabolaPath(float TimeElapsed, AAwBullet* BulletObj, TArray<FString> Params);

	//【OnHit】对Hit到的Character产生伤害
	UFUNCTION()
	static UTimelineNode* DealDamageOnHit(AAwBullet* Bullet, FBeCaughtActorInfo CaughtInfo, TArray<FString> Params);

	//【OnHit】摧毁所有的石柱SceneItem
	UFUNCTION()
	static UTimelineNode* DestroyAllPillar(AAwBullet* Bullet, FBeCaughtActorInfo CaughtInfo, TArray<FString> Params);

	//【OnHit】生成石柱破碎的SceneItem
	UFUNCTION()
	static UTimelineNode* CreateExplodePillarOnHit(AAwBullet* Bullet, FBeCaughtActorInfo CaughtInfo, TArray<FString> Params);

	//【OnRemoved】播放爆炸特效
	UFUNCTION()
	static bool CreateHitSceneFX(AAwBullet* Bullet, EBulletRemoveType RemoveType, TArray<FString> Params);

	//【OnRemoved】播放爆炸特效
	UFUNCTION()
	static bool CreateExplodePillarOnRemoved(AAwBullet* Bullet, EBulletRemoveType RemoveType, TArray<FString> Params);
};
