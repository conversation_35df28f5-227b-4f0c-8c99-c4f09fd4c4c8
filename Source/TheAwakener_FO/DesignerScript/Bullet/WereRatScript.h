// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/GamePlay/Bullet/AWBullet.h"
#include "UObject/Object.h"
#include "WereRatScript.generated.h"

/**
 * 鼠人的子弹
 */
UCLASS()
class THEAWAKENER_FO_API UWereRatScript : public UObject
{
	GENERATED_BODY()
public:
	//【Tween】抛物线飞行 [0]水平飞行速度（厘米/秒） [1]上抛速度（厘米/秒） [2]下落速度 （厘米/秒）
	UFUNCTION()
	static FVector CheeseThrownPath(float TimeElapsed, AAwBullet* BulletObj, TArray<FString> Params);

};
