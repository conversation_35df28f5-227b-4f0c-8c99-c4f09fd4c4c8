// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "TheAwakener_FO/GameFramework/Timeline/TimelineNode.h"
#include "TheAwakener_FO/GamePlay/AOE/AWAoeBase.h"
#include "TheAwakener_FO/GamePlay/Bullet/AwBullet.h"
#include "BulletScript.generated.h"

// 伤害计算类型枚举
UENUM()
enum class EDamageCalculationType
{
	CasterAttack,      // 基于施法者攻击力
	AttackInfo,        // 基于子弹攻击信息
	FixedDamage        // 固定伤害
};
/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UBulletScript : public UObject
{
	GENERATED_BODY()
public:
	//【Tween】根据自己的面向前进
	// Params[0] - 速度（米/秒，float）
	// Params[1] - 重力倍数（float）
	UFUNCTION()
	static FVector SelfForward(float TimeElapsed, AAwBullet* BulletObj, TArray<FString> Params);
	
	//【Tween】直线飞行，参数1是速度（米/秒，float）
	UFUNCTION()
	static FVector GoStraightAhead(float TimeElapsed, AAwBullet* BulletObj, TArray<FString> Params);

	//【Tween】贴地面直线飞行，参数1是速度（米/秒，float）
	UFUNCTION()
	static FVector GoStraightAheadOnGround(float TimeElapsed, AAwBullet* BulletObj, TArray<FString> Params);
	
	//【Tween】直线飞行，参数1是速度（米/秒，float）
	UFUNCTION()
	static FVector GoAheadDelayBack(float TimeElapsed, AAwBullet* BulletObj, TArray<FString> Params);

	//【Tween】直线向下掉，参数1是重力倍数（float）
	UFUNCTION()
	static FVector FallingDown(float TimeElapsed, AAwBullet* BulletObj, TArray<FString> Params);

	//【Tween】直线向下掉，参数1是重力倍数（float）
	UFUNCTION()
	static FVector FallingDownDelay(float TimeElapsed, AAwBullet* BulletObj, TArray<FString> Params);

	//【Tween】平面圆心向外旋转扩散
	UFUNCTION()
	static FVector RotationalDiffusion(float TimeElapsed, AAwBullet* BulletObj, TArray<FString> Params);
	
	//【Tween】 围绕生成者旋转
	UFUNCTION()
	static FVector RotateAroundCaster(float TimeElapsed, AAwBullet* BulletObj, TArray<FString> Params);
	
	//【Tween】标准爆单抛物线[0]水平距离（米）[1]最高高度（米）[2]期望时间内完成（达到抛物线结束而非最高点，秒）
	UFUNCTION()
	static FVector ParabolicCurve(float TimeElapsed, AAwBullet* BulletObj, TArray<FString> Params);

	//【Tween】抛物线指向目的地（TargetLocation）的函数，[0]期望长度（厘米）[1]期望高度（厘米），[2]期望时间内完成（秒），[3]高度长度比（比如是0.5就代表水平的1厘米等于垂直的0.5厘米，默认为1）
	UFUNCTION()
	static FVector TargetParabolicCurveByFixedTime(float TimeElapsed, AAwBullet* BulletObj, TArray<FString> Params);

	//【Tween】抛物线指向目的地（TargetLocation）的函数，[0]期望长度（厘米）[1]期望高度（厘米），[2]期望的水平速度（厘米/秒），[3]高度长度比（比如是0.5就代表水平的1厘米等于垂直的0.5厘米，默认为1）
	UFUNCTION()
	static FVector TargetParabolicCurveByFixedSpeed(float TimeElapsed, AAwBullet* BulletObj, TArray<FString> Params);


	//【Tween】抛物线指向目的地 (二次函数抛物线解析)（TargetLocation）的函数，[0]期望长度（厘米）[1]期望高度（厘米），[2]期望时间内完成（秒），[3]高度长度比（比如是0.5就代表水平的1厘米等于垂直的0.5厘米，默认为1）
	UFUNCTION()
		static FVector TargetQuadraticParabolaByFixedTime(float TimeElapsed, AAwBullet* BulletObj, TArray<FString> Params);

	//【Tween】抛物线指向目的地 (二次函数抛物线解析)（TargetLocation）的函数，[0]期望长度（厘米）[1]期望高度（厘米），[2]期望的水平速度（厘米/秒），[3]高度长度比（比如是0.5就代表水平的1厘米等于垂直的0.5厘米，默认为1）
	UFUNCTION()
		static FVector TargetQuadraticParabolaByFixedSpeed(float TimeElapsed, AAwBullet* BulletObj, TArray<FString> Params);


	//【OnHit】尝试抓到目标身上，参数1是抓点的Name（FString）
	UFUNCTION()
	static UTimelineNode* TryCatchOnTarget(AAwBullet* Bullet, FBeCaughtActorInfo CaughtInfo, TArray<FString> Params);

	//【OnHit】对Hit到的Character产生一个根据子弹生成者的攻击力的伤害
	UFUNCTION(BlueprintCallable)
	static UTimelineNode* DealDamageOnHit(AAwBullet* Bullet, FBeCaughtActorInfo CaughtInfo, TArray<FString> Params);


	//【OnHit】对Hit到的Character产生一个根据子弹生成者的攻击力的伤害
	UFUNCTION(BlueprintCallable)
	static UTimelineNode* DealDamageOnHitByAttackInfo(AAwBullet* Bullet, FBeCaughtActorInfo CaughtInfo, TArray<FString> Params);

	//【OnHit】对Hit到的Character产生一个固定伤害
	UFUNCTION()
	static UTimelineNode* DealFixedDamageOnHit(AAwBullet* Bullet, FBeCaughtActorInfo CaughtInfo, TArray<FString> Params);
	
	static FOffenseInfo GetOffenseInfo(AAwBullet* Bullet, const FBeCaughtActorInfo& CaughtInfo, EDamageCalculationType DamageType,TArray<FString> Params);

private:

	// 统一的伤害处理内部函数
	static UTimelineNode* DealDamageOnHitInternal(AAwBullet* Bullet, FBeCaughtActorInfo CaughtInfo, TArray<FString> Params, EDamageCalculationType DamageType);
	
	//【OnHit】对Hit到的Character添加一个buff
	//Params[0] : BuffId
	//Params[1] : BuffStack
	//Params[2] : BuffDuration
    UFUNCTION(BlueprintCallable)
    static UTimelineNode* AddBuffOnHit(AAwBullet* Bullet, FBeCaughtActorInfo CaughtInfo, TArray<FString> Params);

	//【OnHit】在OnHit时，在Bullet的位置生成一个粒子特效
	UFUNCTION()
	static UTimelineNode* CreateVFXOnHit(AAwBullet* Bullet, FBeCaughtActorInfo CaughtInfo, TArray<FString> Params);

	//【OnHit】在OnHit时，在Bullet的位置生成一个音效
	UFUNCTION()
	static UTimelineNode* CreateSFXOnHit(AAwBullet* Bullet, FBeCaughtActorInfo CaughtInfo, TArray<FString> Params);

	//【OnHit】在OnHit时，在Bullet的位置生成一个粒子特效
	UFUNCTION()
	static UTimelineNode* CreateAOEOnHit(AAwBullet* Bullet, FBeCaughtActorInfo CaughtInfo, TArray<FString> Params);

	//【OnRemoved】创建3个自己（测试用）
	UFUNCTION()
	static bool CreateClones(AAwBullet* Bullet, EBulletRemoveType RemoveType, TArray<FString> Params);

	//【OnRemoved】创建一个AoE [0]AoeId(string) [1]持续时间(float) [2]是否始终向上(bool) [3]Aoe移动的Tween(string)
	UFUNCTION()
	static bool CreateAoEOnRemoved(AAwBullet* Bullet, EBulletRemoveType RemoveType, TArray<FString> Params);

	//【OnRemoved】创建一个AoE [0]AoeId(string) [1]持续时间(float) [2]是否始终向上(bool) [3]Aoe移动的Tween(string)
	UFUNCTION()
	static bool CreateAoEOnTimeOut(AAwBullet* Bullet, EBulletRemoveType RemoveType, TArray<FString> Params);

	//【OnRemoved】创建一个AoE [0]AoeId(string) [1]持续时间(float) [2]是否始终向上(bool) [3]Aoe移动的Tween(string)
	UFUNCTION()
	static bool CreateAoEOnHitTerrain(AAwBullet* Bullet, EBulletRemoveType RemoveType, TArray<FString> Params);

	//【OnRemoved】创建一个VFX
	//Params[0] : VF引用路径
	//Params[1] : VFX缩放，只需填一个值因为是整比缩放
	UFUNCTION()
	static bool CreateVFXOnRemoved(AAwBullet* Bullet, EBulletRemoveType RemoveType, TArray<FString> Params);

	//【OnRemoved】创建一个SFX
	UFUNCTION()
	static bool CreateSFXOnRemoved(AAwBullet* Bullet, EBulletRemoveType RemoveType, TArray<FString> Params);
	
	//【OnCreate】设置Bullet的Rotation
	UFUNCTION()
	static UTimelineNode* SetBulletRotation(AAwBullet* Bullet, TArray<FString> Params);

	//【OnCreate】设置Bullet的Rotation
	UFUNCTION()
	static UTimelineNode* SetBulletRotationByBulletDir(AAwBullet* Bullet, TArray<FString> Params);

	//【OnCreate】Add Bullet的Rotation
	UFUNCTION()
	static UTimelineNode* AddBulletRotation(AAwBullet* Bullet, TArray<FString> Params);

	//【OnCreate】Add Bullet的OriginRotation
	UFUNCTION()
	static UTimelineNode* AddBulletOriginRotation(AAwBullet* Bullet, TArray<FString> Params);

	//【OnHit】火球命中
	UFUNCTION()
	static UTimelineNode* FireBallHit(AAwBullet* Bullet, FBeCaughtActorInfo CaughtInfo, TArray<FString> Params);

	//【OnRemoved】火球碰壁
	UFUNCTION()
	static bool FireBallRemoved(AAwBullet* Bullet, EBulletRemoveType RemoveType, TArray<FString> Params);

	//OnOccur
	UFUNCTION()
	static FBuffRunResult PrintBuffStack(FBuffObj BuffObj, int WasStack, TArray<FString> Params);
};
