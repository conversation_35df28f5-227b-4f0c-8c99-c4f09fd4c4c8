// Fill out your copyright notice in the Description page of Project Settings.


#include "BulletScript.h"

#include "Located.h"
#include "Kismet/KismetMathLibrary.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GamePlay/ApparatusCustom/ECSDamageLib.h"
#include "TheAwakener_FO/GamePlay/ApparatusCustom/MechanicRunner.h"
#include "TheAwakener_FO/GamePlay/Battle/OffenseManager.h"
#include "TheAwakener_FO/GamePlay/Bullet/BulletLauncher.h"

FVector UBulletScript::SelfForward(float TimeElapsed, AAwBullet* BulletObj, TArray<FString> Params)
{
	if (Params.Num() <= 0) return BulletObj->GetActorLocation();

	float Speed = Params.Num() >= 1 ? FCString::Atof(*Params[0]) : 0;
	float Gravity = Params.Num() >= 2 ? FCString::Atof(*Params[1]) : 0;
	
	return BulletObj->GetActorRotation().RotateVector(FVector(Speed * TimeElapsed, 0, 0));
}

FVector UBulletScript::GoStraightAhead(float TimeElapsed, AAwBullet* BulletObj, TArray<FString> Params)
{
	if (Params.Num() <= 0) return BulletObj->GetActorLocation();
	FRotator Rotate = FRotator(0, BulletObj->GetOriginTransform().GetRotation().Rotator().Yaw, 0);
	
	return FVector(
	FCString::Atof(*Params[0]) * TimeElapsed,
	Params.Num() > 1 ? (FCString::Atof(*Params[1]) * TimeElapsed) : 0,
	Params.Num() > 2 ? (FCString::Atof(*Params[2]) * TimeElapsed) : 0
	);
}

FVector UBulletScript::GoStraightAheadOnGround(float TimeElapsed, AAwBullet* BulletObj, TArray<FString> Params)
{
	if (Params.Num() <= 0) return BulletObj->GetActorLocation();
	float ZOffset = Params.Num()>1?FCString::Atof(*Params[1]):0.f;
	FRotator Rotate = FRotator(0, BulletObj->GetOriginTransform().GetRotation().Rotator().Yaw, 0);

	const TArray<AActor*> ActorsToIgnore;
	FHitResult HitResult;
	FVector StartPos = BulletObj->GetActorLocation()+FVector(0,0,100);
	FVector EndPos =  BulletObj->GetActorLocation()+FVector(0,0,-99999);
	TArray<TEnumAsByte<EObjectTypeQuery>> ObjectTypes;
	ObjectTypes.Add(EObjectTypeQuery::ObjectTypeQuery1);
	UKismetSystemLibrary::LineTraceSingleForObjects(BulletObj,StartPos,EndPos,ObjectTypes,false,ActorsToIgnore,EDrawDebugTrace::None,HitResult,true);

	float ZPos =0.f;
	if (HitResult.bBlockingHit)
	{
		ZOffset =HitResult.Location.Z - BulletObj->GetOriginTransform().GetLocation().Z; 
	}
	return FVector(
	FCString::Atof(*Params[0]) * TimeElapsed,
	Params.Num() > 1 ? (FCString::Atof(*Params[1]) * TimeElapsed) : 0,
	ZPos + ZOffset
	);
}

FVector UBulletScript::GoAheadDelayBack(float TimeElapsed, AAwBullet* BulletObj, TArray<FString> Params)
{
	if (Params.Num() <= 0) return BulletObj->GetActorLocation();
	if(Params.Num() > 1)
	{
		float DelayBackTime = FCString::Atof(*Params[1]);
		if(DelayBackTime <= TimeElapsed)
		{
			float BackSpeed = Params.Num() > 2 ? FCString::Atof(*Params[2]): FCString::Atof(*Params[0]);
			return FVector(FCString::Atof(*Params[0]) * DelayBackTime,0, 0) - FVector(BackSpeed * (TimeElapsed - DelayBackTime),0, 0);
		}
	}
	return FVector(FCString::Atof(*Params[0]) * TimeElapsed,0, 0);
}

FVector UBulletScript::FallingDown(float TimeElapsed, AAwBullet* BulletObj, TArray<FString> Params)
{
	int GoTimes = 0;
	float DeltaGone = TimeElapsed;
	while (DeltaGone >= 0)
	{
		GoTimes += 1;
		DeltaGone -= 0.0083;
	}
	float TotalDis = 0;
	for (int i = 1; i <= GoTimes; i++) TotalDis += i * 0.0816;
	float GravityScale = 1;
	if(Params.Num())
		GravityScale = FCString::Atof(*Params[0]);
	float ActualDis = TotalDis * 10 * GravityScale * -1;
	FVector CurLoc = FVector(0,0,ActualDis);
	return CurLoc;
}

FVector UBulletScript::FallingDownDelay(float TimeElapsed, AAwBullet* BulletObj, TArray<FString> Params)
{
	float Time = Params.Num() > 0? FCString::Atof(*Params[0]) : 0;
	float Speed =Params.Num() > 1? FCString::Atof(*Params[1]) : 0;
	float Accelerate = Params.Num() > 2? FCString::Atof(*Params[2]) : 0;

	if (TimeElapsed<Time)
	{
		return  FVector::ZeroVector;
	}
	Speed = TimeElapsed>Time?Speed:0;
	
	int GoTimes = 0;
	float DeltaGone = TimeElapsed;
	while (DeltaGone >= 0)
	{
		GoTimes += 1;
		DeltaGone -= 0.0083;
	}
	float TotalDis = 0;
	for (int i = 1; i <= GoTimes; i++) TotalDis += i * 0.0816;

	float ActualDis = TotalDis * 10 * Accelerate * -1;

	FVector CurLoc = FVector(0,0,	(ActualDis+Speed)*(TimeElapsed-Time));
	return CurLoc;
	
}

FVector UBulletScript::RotationalDiffusion(float TimeElapsed, AAwBullet* BulletObj, TArray<FString> Params)
{
	float Radius = Params.Num() > 0? FCString::Atof(*Params[0]) : 0;
	float Speed =Params.Num() > 1? FCString::Atof(*Params[1]) : 0;
	float UpSpeed =  Params.Num() > 2? FCString::Atof(*Params[2]) : 0;
	float AngleSpeed =Params.Num() > 3? FCString::Atof(*Params[3]) : 0;
	float Accelerate = Params.Num() > 4? FCString::Atof(*Params[4]) : 0;
	float AngleAccelerate = Params.Num() > 5? FCString::Atof(*Params[5]) : 0;
	float UpAccelerate = Params.Num() > 6? FCString::Atof(*Params[6]) : 0;
	FString Axis =  Params.Num() > 7? Params[7]:"";
	
	Speed += Accelerate *TimeElapsed;
	AngleSpeed += AngleAccelerate *TimeElapsed;
	UpSpeed += UpAccelerate*TimeElapsed;
	float Angle = AngleSpeed* TimeElapsed;
	
	FVector RotatorAxis  = BulletObj->GetOriginTransform().GetRotation().GetUpVector();
	if (!Axis.IsEmpty())
	{
		RotatorAxis.InitFromString(Axis);
	}

	RotatorAxis.Normalize();
	FVector Res =  FVector(Radius+Speed* TimeElapsed,0,UpSpeed*TimeElapsed);
	Res = UKismetMathLibrary::RotateAngleAxis(Res,Angle,RotatorAxis);
	return Res;
}

FVector UBulletScript::RotateAroundCaster(float TimeElapsed, AAwBullet* BulletObj, TArray<FString> Params)
{
	FVector Res  = FVector::ZeroVector;
	if (!BulletObj->Caster)
	{
		return Res;
	}
	//初始半径 也是初始距离
	float Radius =Params.Num() > 0? FCString::Atof(*Params[0]) : 0;
	FString DirectionString =  Params.Num() > 1? Params[1]:"";
	float AngleSpeed =Params.Num() > 2? FCString::Atof(*Params[2]) : 0;
	float AngleAccelerate = Params.Num() > 3? FCString::Atof(*Params[3]) : 0;

	FTransform CasterTransform = BulletObj->Caster->GetTransform();
	FVector CasterOffset = UKismetMathLibrary::InverseTransformLocation(BulletObj->GetOriginTransform(),CasterTransform.GetLocation());
	
	FVector Direction = FVector(1,0,0);
	Direction.InitFromString(DirectionString);
	Direction*=Radius;

	 FVector RotatorAxis =	UKismetMathLibrary::FindLookAtRotation(Direction,FVector::ZeroVector).Quaternion().GetUpVector();
	RotatorAxis.Normalize();
	//RotatorAxis =UKismetMathLibrary::InverseTransformLocation(FTransform::Identity,RotatorAxis);
	AngleSpeed += AngleAccelerate *TimeElapsed;
	float Angle = AngleSpeed* TimeElapsed;
	Res =  UKismetMathLibrary::RotateAngleAxis(Direction,Angle,RotatorAxis)+CasterOffset;
	
	return Res;
}

FVector UBulletScript::TargetParabolicCurveByFixedTime(float TimeElapsed, AAwBullet* BulletObj, TArray<FString> Params)
{
	if (BulletObj->IsActorBeingDestroyed())
		return FVector(0, 0, 0);

	const float WishLen = Params.Num() > 0 ? FCString::Atof(*Params[0]) : 1;   //[0]期望长度（厘米）
	const float WishHeight = Params.Num() > 1 ? FCString::Atof(*Params[1]) : 1;   //[1]期望高度（厘米），
	const float InSec = Params.Num() > 2 ? FCString::Atof(*Params[2]) : 0;	//[2]期望时间内完成（秒），
	const float LenHeight = Params.Num() > 3 ? FCString::Atof(*Params[3]) : 1;	//[3]高度长度比（比如是0.5就代表水平的1厘米等于垂直的0.5厘米，默认为1）

	if (InSec <= 0) return BulletObj->TargetLocaiton;

	const FTransform OriginTransform = BulletObj->GetOriginTransform();

	const float ToFlyLen = (BulletObj->TargetLocaiton - OriginTransform.GetLocation()).Size2D();
	const float ToMaxHeight = WishHeight * ToFlyLen * LenHeight * 1.000f / WishLen;
	const float FlyPer = TimeElapsed * 1.000f / InSec;
	const float HalfTime = InSec / 2.000f;
	const bool FirstHalf = TimeElapsed <= HalfTime;
	const float Passed = FirstHalf ? (TimeElapsed  / HalfTime) : ((TimeElapsed - HalfTime) / HalfTime);

	//UKismetSystemLibrary::PrintString(BulletObj, FString("Per ").Append(FString::SanitizeFloat(FlyPer)));
	
	FVector Res = FVector::ZeroVector;
	Res.X = ToFlyLen * FlyPer;// + OriginTransform.GetLocation().X;
	Res.Y =0;// + OriginTransform.GetLocation().Y;
	Res.Z =  FirstHalf ?
		(ToMaxHeight * (1 - FMath::Pow(1 - Passed , 3))):
		(ToMaxHeight * (1 - FMath::Pow(Passed  , 3)));
	
	return Res;

}

FVector UBulletScript::TargetParabolicCurveByFixedSpeed(float TimeElapsed, AAwBullet* BulletObj, TArray<FString> Params)
{
	if (BulletObj->IsActorBeingDestroyed())
		return FVector(0, 0, 0);

	const float WishLen = Params.Num() > 0 ? FCString::Atof(*Params[0]) : 1;   //[0]期望长度（厘米）
	const float WishHeight = Params.Num() > 1 ? FCString::Atof(*Params[1]) : 1;   //[1]期望高度（厘米），
	const float Speed = Params.Num() > 2 ? FCString::Atof(*Params[2]) : 0;	//[2]期望的水平速度（厘米/秒），
	const float LenHeight = Params.Num() > 3 ? FCString::Atof(*Params[3]) : 1;	//[3]高度长度比（比如是0.5就代表水平的1厘米等于垂直的0.5厘米，默认为1）

	if (Speed <= 0) return BulletObj->TargetLocaiton;

	const FTransform OriginTransform = BulletObj->GetOriginTransform();

	const float ToFlyLen = (BulletObj->TargetLocaiton - OriginTransform.GetLocation()).Size2D();
	const float ToMaxHeight = WishHeight * ToFlyLen * LenHeight * 1.000f / WishLen;
	const float FlyPer = TimeElapsed * 1.000f * Speed;
	const float HalfTime = ToFlyLen / Speed / 2.000f;
	const bool FirstHalf = TimeElapsed <= HalfTime;
	const float Passed = FirstHalf ? (TimeElapsed  / HalfTime) : ((TimeElapsed - HalfTime) / HalfTime);

	//UKismetSystemLibrary::PrintString(BulletObj, FString("Per ").Append(FString::SanitizeFloat(FlyPer)));
	
	FVector Res = FVector::ZeroVector;
	Res.X = FlyPer;// + OriginTransform.GetLocation().X;
	Res.Y =0;// + OriginTransform.GetLocation().Y;
	Res.Z =  FirstHalf ?
		(ToMaxHeight * (1 - FMath::Pow(1 - Passed , 3))):
		(ToMaxHeight * (1 - FMath::Pow(Passed  , 3)));
	
	return Res;
}

FVector UBulletScript::TargetQuadraticParabolaByFixedSpeed(float TimeElapsed, AAwBullet* BulletObj, TArray<FString> Params)
{
	if (BulletObj->IsActorBeingDestroyed())
		return FVector(0, 0, 0);

	const FTransform OriginTransform = BulletObj->GetOriginTransform();

	const float zOffset = BulletObj->TargetLocaiton.Z - BulletObj->GetOriginTransform().GetLocation().Z;

	const float ToFlyLen = (BulletObj->TargetLocaiton - OriginTransform.GetLocation()).Size2D();

	const float WishLen = Params.Num() > 0 ? FCString::Atof(*Params[0]) : ToFlyLen;   //[0]期望长度（厘米）
	const float WishHeight = Params.Num() > 1 ? FCString::Atof(*Params[1]) : 1;   //[1]期望高度（厘米），
	const float Speed = Params.Num() > 2 ? FCString::Atof(*Params[2]) : 0;	//[2]期望的水平平均速度（厘米/秒），
	float LenHeight = Params.Num() > 3 ? FCString::Atof(*Params[3]) : 1;	//[3]高度长度比（比如是0.5就代表水平的1厘米等于垂直的0.5厘米，默认为1）
	const float DragSpeedPer = FMath::Clamp(Params.Num() > 4 ? FCString::Atof(*Params[4]) : 1,0.f,1.f);//最大阻力速度比 即至高点 动能完全转换为重力势能时速度的保留比 范围0-1
	const float DragLenPer = FMath::Clamp(Params.Num() > 5 ? FCString::Atof(*Params[5]) : 1, 0.f, 1.f);//阻力生效区间

	if (Speed <= 0) return BulletObj->TargetLocaiton;


	if (FMath::IsNearlyEqual(WishLen,ToFlyLen))
	{
		LenHeight = 1;
	}

	const float TopMaxHeight = WishHeight  * LenHeight * ToFlyLen / WishLen ;

	float TopLen = 0;
	//逆推顶点X坐标

	if (zOffset >= TopMaxHeight)
	{
		TopLen = ToFlyLen;
	}
	else
	{
		float k1 = (-ToFlyLen + sqrt(ToFlyLen * ToFlyLen - ToFlyLen * ToFlyLen * zOffset / TopMaxHeight)) / (ToFlyLen * ToFlyLen / (-2 * TopMaxHeight));
		float k2 = (-ToFlyLen - sqrt(ToFlyLen * ToFlyLen - ToFlyLen * ToFlyLen * zOffset / TopMaxHeight)) / (ToFlyLen * ToFlyLen / (-2 * TopMaxHeight));
		float k = k1 > k2 ? k1 : k2;
		if (k1 < 0 && k2 < 0)
		{
			k = k1 < k2 ? k1 : k2;
		}
		TopLen = 2 * TopMaxHeight / k;
	}

	const float HalfTime = TopLen/Speed;


	float FlyPer = TimeElapsed  * Speed; //当前的x

	FVector2D CurveTopPoint = FVector2D(TopLen,TopMaxHeight);
	//y = ax^2 + bx Top = (-b/2a,b^2/4a) 不用pow 是因为*消耗少
	float x = CurveTopPoint.X;
	float y = CurveTopPoint.Y;
	float a =  -y/ (x * x);
	float b = 2 * y/ x;

	float CurTargetZ = a * FlyPer * FlyPer + b * FlyPer;
	float CurZ = BulletObj->GetActorLocation().Z - BulletObj->GetOriginTransform().GetLocation().Z;


	FVector2D RealTopPoint = FVector2D();


	// 一定区域内 修改抛物线斜率以平滑Z方向速度
	if (FlyPer<ToFlyLen && FlyPer> TopLen* DragLenPer&& FlyPer < TopLen * (2-DragLenPer))
	{
		float LimitZ = 0;
		if (ToFlyLen> TopLen * (2 - DragLenPer))
		{
			LimitZ = a * TopLen * DragLenPer * TopLen * DragLenPer + b * TopLen * DragLenPer;
		}
		else
		{
			LimitZ = zOffset;
		}
		CurTargetZ = FMath::Lerp(LimitZ, CurTargetZ, DragSpeedPer);
	}

	FVector Res = FVector::ZeroVector;
	Res.X =  FlyPer;
	Res.Y = 0;
	Res.Z = CurTargetZ;

	return Res;
}

FVector UBulletScript::TargetQuadraticParabolaByFixedTime(float TimeElapsed, AAwBullet* BulletObj, TArray<FString> Params)
{
	if (BulletObj->IsActorBeingDestroyed())
		return FVector(0, 0, 0);

	const FTransform OriginTransform = BulletObj->GetOriginTransform();

	const float zOffset = BulletObj->TargetLocaiton.Z - BulletObj->GetOriginTransform().GetLocation().Z;

	const float ToFlyLen = (BulletObj->TargetLocaiton - OriginTransform.GetLocation()).Size2D();

	const float WishLen = Params.Num() > 0 ? FCString::Atof(*Params[0]) : 1;   //[0]期望达到最高高度的长度（厘米）
	const float WishHeight = Params.Num() > 1 ? FCString::Atof(*Params[1]) : 1;   //[1]达到期望最高的高度（厘米），
	const float InSec = Params.Num() > 2 ? FCString::Atof(*Params[2]) : 0;	//[2]期望时间内到达目标点（秒），
	float LenHeight = Params.Num() > 3 ? FCString::Atof(*Params[3]) : 1;	//[3]高度长度比（比如是0.5就代表水平的1厘米等于垂直的0.5厘米，默认为1）
	const float DragSpeedPer = FMath::Clamp(Params.Num() > 4 ? FCString::Atof(*Params[4]) : 1, 0.f, 1.f);//最大阻力速度比 即至高点 动能完全转换为重力势能时速度的保留比 范围0-1
	const float DragLenPer = FMath::Clamp(Params.Num() > 5 ? FCString::Atof(*Params[5]) : 1, 0.f, 1.f);//阻力生效区间

	const float Speed = ToFlyLen/ InSec;
	if (InSec <= 0) return BulletObj->TargetLocaiton;

	if (FMath::IsNearlyEqual(WishLen, ToFlyLen))
	{
		LenHeight = 1;
	}

	const float TopMaxHeight = WishHeight * LenHeight * ToFlyLen / WishLen;

	float TopLen = 0;
	//逆推顶点X坐标

	if (zOffset >= TopMaxHeight)
	{
		TopLen = ToFlyLen;
	}
	else
	{
		float k1 = (-ToFlyLen + sqrt(ToFlyLen * ToFlyLen - ToFlyLen * ToFlyLen * zOffset / TopMaxHeight)) / (ToFlyLen * ToFlyLen / (-2 * TopMaxHeight));
		float k2 = (-ToFlyLen - sqrt(ToFlyLen * ToFlyLen - ToFlyLen * ToFlyLen * zOffset / TopMaxHeight)) / (ToFlyLen * ToFlyLen / (-2 * TopMaxHeight));
		float k = k1 > k2 ? k1 : k2;
		if (k1 < 0 && k2 < 0)
		{
			k = k1 < k2 ? k1 : k2;
		}
		TopLen = 2 * TopMaxHeight / k;
	}

	const float HalfTime = TopLen / Speed;


	float FlyPer = TimeElapsed * Speed; //当前的x

	FVector2D CurveTopPoint = FVector2D(TopLen, TopMaxHeight);
	//y = ax^2 + bx Top = (-b/2a,b^2/4a) 不用pow 是因为*消耗少
	float x = CurveTopPoint.X;
	float y = CurveTopPoint.Y;
	float a = -y / (x * x);
	float b = 2 * y / x;

	float CurTargetZ = a * FlyPer * FlyPer + b * FlyPer;
	float CurZ = BulletObj->GetActorLocation().Z - BulletObj->GetOriginTransform().GetLocation().Z;


	FVector2D RealTopPoint = FVector2D();


	// 一定区域内 修改抛物线斜率以平滑Z方向速度
	if (FlyPer<ToFlyLen && FlyPer> TopLen * DragLenPer && FlyPer < TopLen * (2 - DragLenPer))
	{
		float LimitZ = 0;
		if (ToFlyLen > TopLen * (2 - DragLenPer))
		{
			LimitZ = a * TopLen * DragLenPer * TopLen * DragLenPer + b * TopLen * DragLenPer;
		}
		else
		{
			LimitZ = zOffset;
		}
		CurTargetZ = FMath::Lerp(LimitZ, CurTargetZ, DragSpeedPer);
	}

	FVector Res = FVector::ZeroVector;
	Res.X = FlyPer;
	Res.Y = 0;
	Res.Z = CurTargetZ;

	return Res;
}

FVector UBulletScript::ParabolicCurve(float TimeElapsed, AAwBullet* BulletObj, TArray<FString> Params)
{
	if (BulletObj->IsActorBeingDestroyed())
		return FVector(0, 0, 0);

	const FTransform OriginTransform = BulletObj->GetOriginTransform();
	
	if (Params.Num() <= 0) return FVector::ZeroVector;
	float Horizontal = FCString::Atof(*Params[0]);		//水平移动多少
	float Vertical = Params.Num() > 1 ? FCString::Atof(*Params[1]) : 0;	//纵向高度到多少
	const float BaseTime = Params.Num() > 2 ? FCString::Atof(*Params[2]) : 1;		//多少秒内到达终点
	const float ThrowDegree = Params.Num() > 3 ? FCString::Atof(*Params[3]) :0;	//角度是多少
	if (BaseTime <= 0) return FVector(Horizontal, 0, OriginTransform.GetLocation().Z);

	Horizontal = UKismetMathLibrary::DegCos(ThrowDegree) * Horizontal;
	Vertical = UKismetMathLibrary::DegSin(ThrowDegree) * Horizontal + Vertical;
		
	constexpr int FPS = 60;	//假设每秒60帧算
	const int BaseInTick = FMath::RoundToInt(BaseTime * FPS);
	
	const float TickP = (BaseInTick + 1) * (BaseInTick / 2);
	const float EachPieceHMove = Horizontal / TickP;
	
	const int RaiseTick = BaseInTick / 2;
	const float TickPiece = (RaiseTick + 1) * (RaiseTick / 2);
	const float EachPieceVMove = Vertical / TickPiece;
	const int TickElapsed = FMath::FloorToInt(TimeElapsed * FPS);
	
	float HMoved = 0;
	float VMoved = 0;
	for (int i = 1 ; i <= TickElapsed; i++)
	{
		HMoved += FMath::Max(0.f, (BaseInTick - i) * EachPieceHMove);
		VMoved += (EachPieceVMove > 0 ? (RaiseTick - i) : FMath::Abs(RaiseTick - i)) * EachPieceVMove;
	}

	FVector Vec = FVector(0, 0, VMoved);
	Vec = Vec.RotateAngleAxis(OriginTransform.Rotator().Pitch, FVector::RightVector);
	Vec.X = HMoved;

	// UKismetSystemLibrary::PrintString(GWorld, FString("HV:")
	// 	.Append(FString::FromInt(TickElapsed)).Append(">").Append(OriginTransform.Rotator().ToString()).Append(":::")
	// 	.Append(FString::SanitizeFloat(HMoved)).Append("<").Append(FString::SanitizeFloat(VMoved)).Append("__")
	// 	.Append(OriginTransform.GetLocation().ToString()),
	// 	true, true, FLinearColor::Yellow,40);

	return Vec;
}

UTimelineNode* UBulletScript::TryCatchOnTarget(AAwBullet* Bullet, FBeCaughtActorInfo CaughtInfo, TArray<FString> Params)
{
	if (Params.Num() <= 0) return nullptr;
	//UCharacterHitBox* CharacterHitBox = Cast<UCharacterHitBox>(CaughtInfo.BeHitBox);
	UCharacterHitBoxData* CharacterHitBox = Cast<UCharacterHitBoxData>(CaughtInfo.CaughtHitBoxData);
	AAwCharacter* Character = Cast<AAwCharacter>(CaughtInfo.BeCaughtActor);
	if (Character && CharacterHitBox && CharacterHitBox->SeatPointId.IsEmpty() == false && Bullet->Caster && Character->Dead() == false && Character->InSecondWind() == false)
	{
		UAttachPoint* TargetPoint = Character->GetAttachPointByName(CharacterHitBox->SeatPointId, false);
		UAttachPoint* ClawPoint = Bullet->Caster->GetAttachPointByName(Params[0], true);
		if (TargetPoint && ClawPoint)
		{
			bool AttachSuccess = Bullet->Caster->AttachOnTarget(ClawPoint, TargetPoint);
			//TODO 如果需要效果，肯定得需要这个结果
		}
	}
	
	return nullptr;
}

UTimelineNode* UBulletScript::DealDamageOnHitInternal(AAwBullet* Bullet, FBeCaughtActorInfo CaughtInfo, TArray<FString> Params, EDamageCalculationType DamageType)
{
	if (Bullet->GetLocalRole() != ENetRole::ROLE_Authority)
		return nullptr;

	FOffenseInfo Offense = GetOffenseInfo(Bullet, CaughtInfo, DamageType,Params);

	// 执行攻击
	if (CaughtInfo.BeCaughtActor)
	{
		UOffenseManager::DoOffense(Bullet->AttackHitManager, Offense, CaughtInfo, Bullet->Caster, false);
	}
	else
	{
		std::atomic<int32> SafeHitP1{0};
		std::atomic<int32> SafeKill{1};
		std::atomic<int32> SafeHitP2{0};
		FDamageInfo DInfo;
		bool IsHit,IsDead;
		UECSDamageLib::TryHitEnemy(CaughtInfo.BeCaughtSubject,Bullet->Caster,Bullet->GetEcsAttackFlag(),Offense,DInfo,Bullet->GetActorLocation(),IsHit,IsDead);
		// 处理命中后的逻辑
		if (IsHit)
		{
			if(Bullet->Caster->Controller == UGameplayFuncLib::GetLocalAwPlayerController(0))
			{
				SafeHitP1 = 1;
			}
			else
			{
				SafeHitP2 = 1;
			}
			UECSDamageLib::ProcessECSHitLogic(Bullet->Caster, DInfo, Offense, false, SafeHitP1, SafeHitP2);
		}

		// 处理击杀后的逻辑
		if (IsDead)
		{
			UECSDamageLib::ProcessECSKillLogic(Bullet->Caster, DInfo, SafeKill);
		}
		
		// AMechanicRunner::GetInstance()->DealAllPlayerHit(SafeHitP1,SafeHitP2,SafeKill);
	}

	return nullptr;
}

UTimelineNode* UBulletScript::DealDamageOnHit(AAwBullet* Bullet, FBeCaughtActorInfo CaughtInfo, TArray<FString> Params)
{
	return DealDamageOnHitInternal(Bullet, CaughtInfo, Params, EDamageCalculationType::CasterAttack);
}

UTimelineNode* UBulletScript::DealDamageOnHitByAttackInfo(AAwBullet* Bullet, FBeCaughtActorInfo CaughtInfo,
	TArray<FString> Params)
{
	return DealDamageOnHitInternal(Bullet, CaughtInfo, Params, EDamageCalculationType::AttackInfo);
}

UTimelineNode* UBulletScript::DealFixedDamageOnHit(AAwBullet* Bullet, FBeCaughtActorInfo CaughtInfo,
                                                   TArray<FString> Params)
{
	return DealDamageOnHitInternal(Bullet, CaughtInfo, Params, EDamageCalculationType::FixedDamage);
}

FOffenseInfo UBulletScript::GetOffenseInfo(AAwBullet* Bullet, const FBeCaughtActorInfo& CaughtInfo, EDamageCalculationType DamageType,TArray<FString> Params)
{
	
	FOffenseInfo Offense = FOffenseInfo();
	Offense.AttackInfo = Bullet->AttackInfo;
	const float LevelDamageMultiplier = UGameplayFuncLib::GetDataManager()->GetAbilityElemDmgMultiplier(Bullet->ActionLevel);

	// 根据不同的伤害计算类型设置伤害值
	switch (DamageType)
	{
	case EDamageCalculationType::CasterAttack:
		{
			// 基于施法者攻击力的伤害计算
			Offense.AttackInfo.DamagePower = 1;
			if(Bullet->Caster)
				Offense.AttackInfo.DamagePower = Bullet->Caster->CharacterObj.CurProperty.PAttack;
			if(Params.Num() > 0)
				Offense.AttackInfo.DamagePower.Physical = Offense.AttackInfo.DamagePower.Physical * FCString::Atof(*Params[0]) * LevelDamageMultiplier;
			if(Params.Num() > 1)
				Offense.AttackInfo.DamagePower.Break = FCString::Atof(*Params[1]);

			Offense.AttackInfo.DamageType = Params.Num()>2?UDataFuncLib::FStringToEnum<EDamageType>(Params[2]):EDamageType::DirectDamage;
			const FString DamageTypeKey = Params.Num()>3?Params[3]:"Physical" ;
			EChaElemental ElementalType = UDataFuncLib::FStringToEnum<EChaElemental>(DamageTypeKey);
			Offense.AttackInfo.Elemental = ElementalType>EChaElemental::Physical?ElementalType:EChaElemental::Physical;
		}
		break;
	case EDamageCalculationType::AttackInfo:
		{
			// 基于子弹攻击信息的伤害计算
			if(Bullet->Caster)
			{
				Offense.AttackInfo.DamagePower.Physical = Bullet->Caster->CharacterObj.CurProperty.PAttack*Offense.AttackInfo.DamagePower.Physical * LevelDamageMultiplier;
			}
		}
		break;
	case EDamageCalculationType::FixedDamage:
		{
			// 固定伤害计算
			Offense.AttackInfo.DamagePower = 0;
			if (Params.Num() > 0)
				Offense.AttackInfo.DamagePower = FCString::Atof(*Params[0]) * LevelDamageMultiplier;
		}
		break;
	}

	// 设置公共属性
	Offense.AttackInfo.DamageIncomeVector = Bullet->GetActorRotation().Vector();
	Offense.AttackHitBoxName.Add(CaughtInfo.AttackBoxName);
	Offense.SourceId = Bullet->UID;
	Offense.CanHitTimes = Bullet->Model.HitSameTarget;
	Offense.Index = 0;
	Offense.AttackInfo.AttackerActionChange.HitStun.AutoSetActive();
	Offense.AttackInfo.DefenderActionChange.HitStun.AutoSetActive();
	Offense.AttackInfo.DamageSourceType = EAttackSource::Bullet;

	FVector TargetPos;
	if (CaughtInfo.BeCaughtActor)
	{
		TargetPos = CaughtInfo.BeCaughtActor->GetActorLocation();
	}
	else if (CaughtInfo.BeCaughtSubject.IsValid())
	{
		TargetPos = CaughtInfo.BeCaughtSubject.GetTrait<FLocated>().Location;
	}
	// 设置旋转
	if(Bullet->Caster)
		Offense.AttackInfo.RotateByDegree(Bullet->Caster->GetActorRotation().Yaw, Bullet->Caster->GetActorLocation(), TargetPos);
	else
		Offense.AttackInfo.RotateByDegree(Bullet->GetOriginTransform().Rotator().Yaw, Bullet->GetActorLocation(), TargetPos);
	return  Offense;
}

UTimelineNode* UBulletScript::AddBuffOnHit(AAwBullet* Bullet, FBeCaughtActorInfo CaughtInfo, TArray<FString> Params)
{
	if (Bullet->GetLocalRole() != ENetRole::ROLE_Authority)
		return nullptr;
	if(Params.Num() <= 0) return nullptr;
	if (!CaughtInfo.BeCaughtActor) return nullptr;
	AAwCharacter* Character = Cast<AAwCharacter>(CaughtInfo.BeCaughtActor);
	UCharacterHitBoxData* CharacterHitBox = Cast<UCharacterHitBoxData>(CaughtInfo.CaughtHitBoxData);
	if (CharacterHitBox)
	{
		//判断角色是否无敌或者闪避无敌中
		if(CharacterHitBox->AsJustDodge.Active == true)
			return nullptr;
	}
	if (Character )
	{
		if(Character->GetHitBoxInJustDefense(CaughtInfo.CaughtHitBoxComponent->GetName()))
			return nullptr;
		FAddBuffInfo BuffInfo = FAddBuffInfo();
		BuffInfo.Model = UGameplayFuncLib::GetAwDataManager()->GetBuffModelById(Params[0]);
		BuffInfo.AddStack = Params.Num() > 1 ? FCString::Atoi(*Params[1]) : 1;
		BuffInfo.Duration = Params.Num() > 2 ? FCString::Atoi(*Params[2]) : 5;
		BuffInfo.Infinity = false;
		BuffInfo.SetToDuration = true;
		BuffInfo.Target = Character;
		BuffInfo.Caster = Bullet->Caster;
		BuffInfo.PropertyOnAdd = BuffInfo.Caster ? BuffInfo.Caster->CharacterObj.CurProperty : FChaProp();
		Character->AddBuff(BuffInfo);
	}
	return nullptr;
}

UTimelineNode* UBulletScript::CreateVFXOnHit(AAwBullet* Bullet, FBeCaughtActorInfo CaughtInfo, TArray<FString> Params)
{
	if (Params.Num())
		UGameplayFuncLib::CreateVFXByPathAtLocation(Params[0], Bullet->GetActorTransform(), true, true);
	return nullptr;
}

UTimelineNode* UBulletScript::CreateSFXOnHit(AAwBullet* Bullet, FBeCaughtActorInfo CaughtInfo, TArray<FString> Params)
{
	if (Params.Num())
	{
		float VolumeMultiplier = 1.0f;
		if (Params.Num() > 1)
			VolumeMultiplier = FCString::Atof(*Params[1]);
		float PitchMultiplier = 1.0f;
		if (Params.Num() > 2)
			PitchMultiplier = FCString::Atof(*Params[2]);
		float StartTime = 1.0f;
		if (Params.Num() > 3)
			StartTime = FCString::Atof(*Params[3]);
		UGameplayFuncLib::PlaySFXByPathAtLocation(Params[0], Bullet->GetActorLocation(), Bullet->GetActorRotation(), VolumeMultiplier, PitchMultiplier, StartTime);
	}
	return nullptr;
}

UTimelineNode* UBulletScript::CreateAOEOnHit(AAwBullet* Bullet, FBeCaughtActorInfo CaughtInfo, TArray<FString> Params)
{
	if (Params.Num() <= 0) return nullptr;
	const FString AOEId = Params[0];
	const float AoeDuration = Params.Num() > 1 ? FCString::Atof(*Params[1]) : 0;
	const bool AlwaysUp = Params.Num() > 2 ? FCString::ToBool(*Params[2]) : false;	//这仅仅是说，Pitch和Roll变成0，其他的还是原来的值
	const FString TweenFunc = Params.Num() > 3 ? Params[3] : "";
	const FAOEModel AoeModel = UGameplayFuncLib::GetAwDataManager()->GetAoeModelById(AOEId);
	if (AoeModel.Id != AOEId) return nullptr;
	
	const FAOELauncher Launcher = FAOELauncher(
		Bullet->Caster,
		AoeModel,
		Bullet->GetActorLocation(),
		Bullet->GetActorRotation().Vector(),
		AoeDuration,
		TweenFunc
	);
	AAWAoe* Aoe = UGameplayFuncLib::CreateAOEByLauncher(Launcher);
	if (AlwaysUp) {
		FRotator Rot = Aoe->GetActorRotation();
		Rot.Pitch = 0;
		Rot.Roll = 0;
		Aoe->SetActorRotation(Rot);
	}

	return nullptr;
}

bool UBulletScript::CreateClones(AAwBullet* Bullet, EBulletRemoveType RemoveType, TArray<FString> Params)
{
	const int Count = Params.Num() > 0 ? FCString::Atoi(*Params[0]) : 1;
	const FBulletModel Model = Bullet->Model;
	for (int i = 0; i < Count; i++)
	{
		FRotator Rotator = FRotator(0, (i - 1)*15.f, 2.f);
		UGameplayFuncLib::CreateBullet(
			FBulletLauncher(
					Bullet->Caster,
					Model,
					Bullet->GetActorLocation(),
					Rotator.RotateVector(Bullet->GetActorRotation().Vector()), 30,
					FString("BulletScript.GoStraightAhead(500)"),
					Bullet->Caster->GetActorLocation()
			)
		);
	}
	return true;
}

UTimelineNode* UBulletScript::FireBallHit(AAwBullet* Bullet, FBeCaughtActorInfo CaughtInfo, TArray<FString> Params)
{
	//创建一个火焰爆破
	FAOELauncher Launcher = FAOELauncher(
		Bullet->Caster,
		UGameplayFuncLib::GetAwDataManager()->GetAoeModelById("Test_Fireball_Explosion"),
		Bullet->GetActorLocation(),
		Bullet->GetActorForwardVector(),
		0.1f,""
	);
	UGameplayFuncLib::CreateAOEByLauncher(Launcher);

	//返回伤害对应的动画
	return DealDamageOnHit(Bullet, CaughtInfo, Params);
}

bool UBulletScript::FireBallRemoved(AAwBullet* Bullet, EBulletRemoveType RemoveType, TArray<FString> Params)
{
	//创建一个火焰爆破
	FAOELauncher Launcher = FAOELauncher(
		Bullet->Caster,
		UGameplayFuncLib::GetAwDataManager()->GetAoeModelById("Test_Fireball_Explosion"),
		Bullet->GetActorLocation(),
		Bullet->GetActorForwardVector(),
		0.1f,""
	);
	UGameplayFuncLib::CreateAOEByLauncher(Launcher);

	return true;
}

FBuffRunResult UBulletScript::PrintBuffStack(FBuffObj BuffObj, int WasStack, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	UKismetSystemLibrary::PrintString(BuffObj.Carrier, BuffObj.Model.Id.Append(FString(" Stack: ")).Append(FString::FromInt(BuffObj.Stack)),
		true,false,FLinearColor::Red);
	return Res;
}

bool UBulletScript::CreateAoEOnRemoved(AAwBullet* Bullet, EBulletRemoveType RemoveType, TArray<FString> Params)
{
	if (Params.Num() <= 0) return true;
	const FString AOEId = Params[0];
	const float AoeDuration = Params.Num() > 1 ? FCString::Atof(*Params[1]) : 0;
	const bool AlwaysUp = Params.Num() > 2 ? FCString::ToBool(*Params[2]) : false;	//这仅仅是说，Pitch和Roll变成0，其他的还是原来的值
	const FString TweenFunc = Params.Num() > 3 ? Params[3] : "";
	const FAOEModel AoeModel = UGameplayFuncLib::GetAwDataManager()->GetAoeModelById(AOEId);
	if (AoeModel.Id != AOEId) return true;
	
	const FAOELauncher Launcher = FAOELauncher(
		Bullet->Caster,
		AoeModel,
		Bullet->GetActorLocation(),
		Bullet->GetActorRotation().Vector(),
		AoeDuration,
		TweenFunc
	);
	AAWAoe* Aoe = UGameplayFuncLib::CreateAOEByLauncher(Launcher);
	if (AlwaysUp) {
		FRotator Rot = Aoe->GetActorRotation();
		Rot.Pitch = 0;
		Rot.Roll = 0;
		Aoe->SetActorRotation(Rot);
	}

	return true;
}

bool UBulletScript::CreateAoEOnTimeOut(AAwBullet* Bullet, EBulletRemoveType RemoveType, TArray<FString> Params)
{
	if (Params.Num() <= 0) return false;
	if(RemoveType != EBulletRemoveType::TimeOut) return false;
	const FString AOEId = Params[0];
	const float AoeDuration = Params.Num() > 1 ? FCString::Atof(*Params[1]) : 0;
	const bool AlwaysUp = Params.Num() > 2 ? FCString::ToBool(*Params[2]) : false;	//这仅仅是说，Pitch和Roll变成0，其他的还是原来的值
	const FString TweenFunc = Params.Num() > 3 ? Params[3] : "";
	const FAOEModel AoeModel = UGameplayFuncLib::GetAwDataManager()->GetAoeModelById(AOEId);
	if (AoeModel.Id != AOEId) return true;
	
	const FAOELauncher Launcher = FAOELauncher(
		Bullet->Caster,
		AoeModel,
		Bullet->GetActorLocation(),
		Bullet->GetActorRotation().Vector(),
		AoeDuration,
		TweenFunc
	);
	AAWAoe* Aoe = UGameplayFuncLib::CreateAOEByLauncher(Launcher);
	if (AlwaysUp) {
		FRotator Rot = Aoe->GetActorRotation();
		Rot.Pitch = 0;
		Rot.Roll = 0;
		Aoe->SetActorRotation(Rot);
	}

	return true;
}

bool UBulletScript::CreateAoEOnHitTerrain(AAwBullet* Bullet, EBulletRemoveType RemoveType, TArray<FString> Params)
{
	if (Params.Num() <= 0) return false;
	if(RemoveType != EBulletRemoveType::HitTerrain) return false;
	const FString AOEId = Params[0];
	const float AoeDuration = Params.Num() > 1 ? FCString::Atof(*Params[1]) : 0;
	const bool AlwaysUp = Params.Num() > 2 ? FCString::ToBool(*Params[2]) : false;	//这仅仅是说，Pitch和Roll变成0，其他的还是原来的值
	const FString TweenFunc = Params.Num() > 3 ? Params[3] : "";
	const FAOEModel AoeModel = UGameplayFuncLib::GetAwDataManager()->GetAoeModelById(AOEId);
	if (AoeModel.Id != AOEId) return true;
	
	const FAOELauncher Launcher = FAOELauncher(
		Bullet->Caster,
		AoeModel,
		Bullet->GetActorLocation(),
		Bullet->GetActorRotation().Vector(),
		AoeDuration,
		TweenFunc
	);
	AAWAoe* Aoe = UGameplayFuncLib::CreateAOEByLauncher(Launcher);
	if (AlwaysUp) {
		FRotator Rot = Aoe->GetActorRotation();
		Rot.Pitch = 0;
		Rot.Roll = 0;
		Aoe->SetActorRotation(Rot);
	}

	return true;
}

bool UBulletScript::CreateVFXOnRemoved(AAwBullet* Bullet, EBulletRemoveType RemoveType, TArray<FString> Params)
{
	FTransform Temp = Bullet->GetActorTransform();
	if (Params.Num() > 1)
		Temp.SetScale3D(FVector(FCString::Atof(*Params[1])));
	else if (Params.Num() > 0)
		Temp.SetScale3D(FVector(1));

	UGameplayFuncLib::CreateVFXByPathAtLocation(Params[0], Temp, true, true);
	
	/*if (Params.Num())
		UGameplayFuncLib::CreateVFXByPathAtLocation(Params[0], Bullet->GetActorTransform(), true, true);*/
	return true;
}

bool UBulletScript::CreateSFXOnRemoved(AAwBullet* Bullet, EBulletRemoveType RemoveType, TArray<FString> Params)
{
	if (Params.Num())
	{
		float VolumeMultiplier = 1.0f;
		if (Params.Num() > 1)
			VolumeMultiplier = FCString::Atof(*Params[1]);
		float PitchMultiplier = 1.0f;
		if (Params.Num() > 2)
			PitchMultiplier = FCString::Atof(*Params[2]);
		float StartTime = 1.0f;
		if (Params.Num() > 3)
			StartTime = FCString::Atof(*Params[3]);
		UGameplayFuncLib::PlaySFXByPathAtLocation(Params[0], Bullet->GetActorLocation(), Bullet->GetActorRotation(), VolumeMultiplier, PitchMultiplier, StartTime);
	}
	return true;
}

UTimelineNode* UBulletScript::SetBulletRotation(AAwBullet* Bullet, TArray<FString> Params)
{
	if(Params.Num() > 2)
	{
		FRotator ChangeRot = Bullet->GetActorRotation();
		ChangeRot.Roll = FCString::Atof(*Params[0]);
		ChangeRot.Pitch = FCString::Atof(*Params[1]);
		ChangeRot.Yaw = FCString::Atof(*Params[2]);
		Bullet->SetActorRotation(ChangeRot);
	}
	return nullptr;
}

UTimelineNode* UBulletScript::SetBulletRotationByBulletDir(AAwBullet* Bullet, TArray<FString> Params)
{
	FRotator ChangeRot = Bullet->GetOriginTransform().Rotator();
	Bullet->SetActorRotation(ChangeRot);
	return nullptr;
}

UTimelineNode* UBulletScript::AddBulletRotation(AAwBullet* Bullet, TArray<FString> Params)
{
	if(Params.Num() > 2)
	{
		FRotator ChangeRot = Bullet->GetActorRotation();
		ChangeRot.Roll += FCString::Atof(*Params[0]);
		ChangeRot.Pitch += FCString::Atof(*Params[1]);
		ChangeRot.Yaw += FCString::Atof(*Params[2]);
		Bullet->SetActorRotation(ChangeRot);
	}
	return nullptr;
}

UTimelineNode* UBulletScript::AddBulletOriginRotation(AAwBullet* Bullet, TArray<FString> Params)
{
	FTransform Trans = Bullet->GetOriginTransform();
	FRotator NewRot = Trans.Rotator();
	NewRot.Roll += Params.Num() > 0 ? FCString::Atof(*Params[0]) : 0;
	NewRot.Pitch += Params.Num() > 1 ? FCString::Atof(*Params[1]) : 0;
	NewRot.Yaw += Params.Num() > 2 ? FCString::Atof(*Params[2]) : 0;
	Trans.SetRotation(NewRot.Quaternion());
	Bullet->SetOriginTransform(Trans);
	
	return nullptr;
}
