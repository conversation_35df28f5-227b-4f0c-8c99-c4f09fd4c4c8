// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/GamePlay/Buff/Buff.h"
#include "ScriptFunc.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UScriptFunc : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()
public:
	/**
	 *Add buff to character
	 *@param  AddBuffInfo information for add buff
	 *@return  pointer of the FBuffObj created. or nullptr if creation fails.
	 */
	UFUNCTION(BlueprintCallable, Category="AW|DesignerScript|Buff")
	static FBuffObj AddBuff(FAddBuffInfo AddBuffInfo);
};
