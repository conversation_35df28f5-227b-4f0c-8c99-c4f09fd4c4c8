// Fill out your copyright notice in the Description page of Project Settings.


#include "UIList.h"

#include "Blueprint/WidgetLayoutLibrary.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"

TArray<FVector2D> UUIList::CalculateRouletteContentPoint(int Count,float Angle,float CompensationValue ,float RouletteDiameter)
{
	//测试代码******** ↓
	TArray<FVector2D> Point;
	if(Count > 0)
	{
		if(Count == 1)
		{
			FVector2D TempPoint = FVector2D(FMath::Sin(FMath::DegreesToRadians(90.0f)) * RouletteDiameter,
								-(FMath::Cos(FMath::DegreesToRadians(90.0f))) * RouletteDiameter);
			Point.Add(TempPoint);
		}
		else
		{
			float Temp = 0.0f;
			FVector2D TempPoint = FVector2D(0,0);
			float RotateAngle = (90.0f - Count/2 * <PERSON>le);
			for(int i = 0;i < Count;i++)
			{
				if(i < Count / 2)
				{
					Temp = -(Angle / CompensationValue);
				}
				else if(i == Count / 2)
				{
					Temp = 0.0f;
				}
				else if(i > Count / 2)
				{
					Temp = Angle / CompensationValue;
				}
				
				
				TempPoint = FVector2D(FMath::Sin(FMath::DegreesToRadians(Angle * i + RotateAngle + Temp)) * RouletteDiameter,
					FMath::Cos(FMath::DegreesToRadians(Angle * i + RotateAngle + Temp)) * -RouletteDiameter);
				
				Point.Add(TempPoint);
			}
		}
		
	}
	
	return Point;
	
	//测试代码******** ↑
}

FVector2D UUIList::CalculateListPosition(const FGeometry& MyGeometry,float interval, float curveAngle, float displacementValue, float compensationValue)
{
	float TempX =  (MyGeometry.GetLocalPositionAtCoordinates(FVector2D(0,0)).Y / (MyGeometry.GetAbsoluteSize().Y * interval) * curveAngle) * UWidgetLayoutLibrary::GetViewportScale(GWorld);
	float PositionX = FMath::Sin(FMath::DegreesToRadians(TempX)) * displacementValue + compensationValue;

	
	return FVector2D(PositionX,0);
	
}

bool UUIList::ShowUI(float Time, TArray<FString> Params)
{
	const FString UIName = Params[0];
	const float DelayTime = FCString::Atof(*Params[1]);
	if (Time >= DelayTime)
	{
		UGameplayFuncLib::GetUiManager()->Show(UIName);
		return true;
	}
	
	return false;
}



