// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/UI/GameMain/MenuListEntry.h"
#include "UObject/Object.h"
#include "UIList.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UUIList : public UObject
{
	GENERATED_BODY()


public:

	UFUNCTION(BlueprintCallable)
	static TArray<FVector2D> CalculateRouletteContentPoint(int Count,float Angle,float CompensationValue,float RouletteDiameter);

	
	UFUNCTION(BlueprintCallable)
	static FVector2D CalculateListPosition(const FGeometry& MyGeometry, float interval,float curveAngle,float displacementValue,float compensationValue);
	
	UFUNCTION(BlueprintCallable)
	static bool ShowUI(float Time, TArray<FString> Params);
};
