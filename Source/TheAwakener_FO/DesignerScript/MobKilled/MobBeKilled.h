// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "UObject/Object.h"
#include "MobBeKilled.generated.h"

/**
 * 怪物被杀死的回调：(AAwCharacter* 被杀死的怪，TArray<FString>参数)=>void
 */
UCLASS()
class THEAWAKENER_FO_API UMobBeKilled : public UObject
{
	GENERATED_BODY()
private:
	//检测当前的进度
	UFUNCTION(BlueprintCallable)
	static void CheckForMineStory();
public:
	//巨魔被杀死，需要检测MineStoryStep
	UFUNCTION(BlueprintCallable)
	static void OgreBeKilled(AAwCharacter* Guy, TArray<FString> Params);

	//任何鼠人被杀死，要记录最低鼠人到过的势力值，然后判断MineStoryStep
	UFUNCTION(BlueprintCallable)
	static void RatManBeKilled(AAwCharacter* Guy, TArray<FString> Params);

	UFUNCTION(BlueprintCallable)
	static void IceDevilBeKilled(AAwCharacter* Guy, TArray<FString> Params);

	//---------Roguelike------------

	//【Action】玩家获得指定范围内随机数量的货币
	//Params[0]:货币Id
	//Params[1]:货币最小值
	//Params[2]:货币最大值
	UFUNCTION(BlueprintCallable)
	static UTimelineNode* PlayerGetRogueCoin(AAwCharacter* Guy, TArray<FString> Params);
};
