// Fill out your copyright notice in the Description page of Project Settings.


#include "MobBeKilled.h"

#include "TheAwakener_FO/DesignerScript/Trigger/TriggerScript.h"
#include "TheAwakener_FO/FunctionLibrary/DebugFuncLib.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GameFramework/Manager/AwRogueDataSystem.h"
#include "TheAwakener_FO/GamePlay/GameMode/AwGameMode_RandomDungeon.h"
#include "TheAwakener_FO/UI/MediaPlayerUI.h"


void UMobBeKilled::CheckForMineStory()
{
	if (!UGameplayFuncLib::GetAwGameInstance() )
	{
		return ;
	}
	
	const bool OgreKilled = UGameplayFuncLib::GetAwGameInstance()->RoleInfo.GetSwitch("Ogre_Killed") > 0;
	const int RatMan = UGameplayFuncLib::GetAwGameInstance()->RoleInfo.GetSwitch("RatManMin", 1000) ;
	const int Goblin = UGameplayFuncLib::GetAwGameInstance()->RoleInfo.GetSwitch("GoblinMin", 1000) ;

	int ToValue = UGameplayFuncLib::GetAwGameInstance()->RoleInfo.GetSwitch("MineStoryStep") ;
	if (OgreKilled)
	{
		const int Human = 2000 - RatMan - Goblin;
		if (Human >= 1700)
		{
			ToValue = FMath::Max(ToValue, 7);
		}else if (Human >= 1200)
		{
			ToValue = FMath::Max(ToValue, 6);
		}else if (Human >= 900)
		{
			ToValue = FMath::Max(ToValue, 5);
		}else if (RatMan <= 500)
		{
			ToValue = FMath::Max(ToValue, 4);
		} else if (RatMan <= 800)
		{
			ToValue = FMath::Max(ToValue, 3);
		}
	}else
	{
		if (RatMan <= 800)
		{
			ToValue = FMath::Max(ToValue, 2);
		}
	}

	UGameplayFuncLib::GetAwGameInstance()->RoleInfo.SetSwitchValue("MineStoryStep", ToValue);
}

void UMobBeKilled::OgreBeKilled(AAwCharacter* Guy, TArray<FString> Params)
{
	if (!UGameplayFuncLib::GetAwGameInstance())
		return;

	if(Cast<AAwGameMode_RandomDungeon>(UGameplayFuncLib::GetAwGameMode()))
	{
		const int OgreKilled = UGameplayFuncLib::GetAwGameInstance()->RoleInfo.GetSwitch("Ogre_Killed") > 0;
		UGameplayFuncLib::GetAwGameInstance()->RoleInfo.AddSwitchValue("Ogre_Killed", 1);

		RatManBeKilled(Guy, Params);

		// for (TTuple<AAwCharacter*, FString> Character : UGameplayFuncLib::GetAwGameState()->AllCharacters)
		// {
		// 	if(Character.Key)
		// 	{
		// 		if(!Character.Key->UnderPlayerControl() && !Character.Key->Dead())
		// 			Character.Key->PauseAI(true);
		// 	}
		// }
		// UMediaPlayerUI* MediaUI = Cast<UMediaPlayerUI>(UGameplayFuncLib::GetUiManager()->Show("MediaPlayer"));
		// if(MediaUI)
		// {
		// 	UFileMediaSource* MediaSource = NewObject<UFileMediaSource>(GWorld, UFileMediaSource::StaticClass(), "Dungeon05_End");
		// 	MediaSource->SetFilePath("./MoviesInGame/LA_Dungeon_05_End.mp4");
		// 	MediaUI->StartPlayMedia(MediaSource,true);
		// }
	}
	
	

	UGameplayFuncLib::GetUiManager()->Hide("BossHP");

	// UMediaPlayerUI* MediaUI = Cast<UMediaPlayerUI>(UGameplayFuncLib::GetUiManager()->Show("MediaPlayer"));
	// if(MediaUI)
	// {
	// 	UFileMediaSource* MediaSource = NewObject<UFileMediaSource>(GWorld, UFileMediaSource::StaticClass(), "Dungeon05_End");
	// 	MediaSource->SetFilePath("./MoviesInGame/LA_Dungeon_05_End.mp4");
	// 	MediaUI->StartPlayMedia(MediaSource,false);
	// }
}

void UMobBeKilled::RatManBeKilled(AAwCharacter* Guy, TArray<FString> Params)
{
	if (!UGameplayFuncLib::GetAwGameInstance())
		return;
	
	const int RatManMinValue = UGameplayFuncLib::GetAwGameInstance()->RoleInfo.GetSwitch("RatManMin", 1000) ;
	int RatManNow = 1000;
	if(UGameplayFuncLib::GetAwGameMode())
	{
		const AAwGameMode_RandomDungeon* DungeonGameMode = Cast<AAwGameMode_RandomDungeon>(UGameplayFuncLib::GetAwGameMode());
		if(DungeonGameMode)
		{
			FAwDungeonSave DungeonSave = UGameplayFuncLib::GetAwGameInstance()->RoleInfo.GetDungeonRecordByDungeonId(DungeonGameMode->CurDungeonInfo.Id);
			for(auto CampRecord : DungeonSave.Camps)
			{
				if(CampRecord.CampId == "RatMan")
				{
					RatManNow = CampRecord.CampProgress;
					break;
				}
			}
		}
	}
	if (RatManNow < RatManMinValue)
	{
		UGameplayFuncLib::GetAwGameInstance()->RoleInfo.SetSwitchValue("RatManMin", RatManNow);
	}

	CheckForMineStory();
}

void UMobBeKilled::IceDevilBeKilled(AAwCharacter* Guy, TArray<FString> Params)
{
	if (!UGameplayFuncLib::GetAwGameInstance())
		return;

	if(!Cast<AAwGameMode_RandomDungeon>(UGameplayFuncLib::GetAwGameMode()))
		return;
	
	UGameplayFuncLib::GetUiManager()->Hide("BossHP");

	UMediaPlayerUI* MediaUI = Cast<UMediaPlayerUI>(UGameplayFuncLib::GetUiManager()->Show("MediaPlayer"));
	if(MediaUI)
	{
		UFileMediaSource* MediaSource = NewObject<UFileMediaSource>(GWorld, UFileMediaSource::StaticClass(), "Dungeon06_End");
		MediaSource->SetFilePath("./MoviesInGame/LA_Dungeon_06_End.mp4");
		MediaUI->StartPlayMedia(MediaSource,true);
	}

	CheckForMineStory();
}

UTimelineNode* UMobBeKilled::PlayerGetRogueCoin(AAwCharacter* Guy, TArray<FString> Params)
{
	if(Params.Num() <= 0) return nullptr;                                                                                  
	const int AddCoinNum = FMath::Abs(FCString::Atoi(*Params[0]));                                                         
	const int CurCoin = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>()->GetCurrency_Coin();
	UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>()->AddCurrency_Coin(AddCoinNum);    
	return nullptr;						                                                                                   
}
