// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/Object.h"
#include "MessageDlgFunc.generated.h"

/**
 * 对话框确定取消等方法
 */
UCLASS()
class THEAWAKENER_FO_API UMessageDlgFunc : public UObject
{
	GENERATED_BODY()
public:
	//创建角色时确认并保存游戏
	UFUNCTION(BlueprintCallable)
	static void SaveChaAndStartGame(TArray<FString> Params);
	//创建角色的时候返回上一步骤
	UFUNCTION(BlueprintCallable)
	static void CreateChaReturnLastStep(TArray<FString> Params);
};
