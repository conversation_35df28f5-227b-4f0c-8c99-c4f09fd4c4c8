// Fill out your copyright notice in the Description page of Project Settings.


#include "MessageDlgFunc.h"

#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/UI/CreateCharacter.h"

void UMessageDlgFunc::SaveChaAndStartGame(TArray<FString> Params)
{
	
	const FString ChaName = Params.Num() > 0 ? Params[0] : "Awakener";
	const FString TypeId = Params.Num() > 1 ? Params[1] : "TypeB";
	const EAwRace Race = Params.Num() > 2 ? UDataFuncLib::FStringToEnum<EAwRace>(Params[2]) : EAwRace::Human;
	const FString Appearance = Params.Num() > 3 ? Params[3] : "";
	const FString Voice = Params.Num() > 4 ? Params[4] : "";
	const FString ClassId = Params.Num() > 5 ? Params[5] : "Swordsman";
	const EChaElemental Elemental = Params.Num() > 6 ? UDataFuncLib::FStringToEnum<EChaElemental>(Params[6]) : EChaElemental::Fire;
	
	FAwRoleInfo Role = UGameplayFuncLib::CreateNewGame(
		ChaName, TypeId, Race, Appearance, Voice, ClassId, Elemental
	);
	
	UGameplayFuncLib::SaveGame();
	if(UGameplayFuncLib::GetAwGameInstance()->UIManager->ConfirmDialog)
	{
		UGameplayFuncLib::GetAwGameInstance()->UIManager->ConfirmDialog->OpenLevel();
	}
	//UGameplayFuncLib::GetAwGameInstance()->UIManager->Hide("CreateCharacter");
	//UGameplayFuncLib::GetAwGameInstance()->UIManager->Show("SelectData");
	//TODO 接下来该干啥？等猫哥看看

	
	/*UGameplayFuncLib::PlayUIAudio("ConfirmKey_Yes");
	UGameplayFuncLib::GetUiManager()->ShowLoading();

	UKismetSystemLibrary::Delay(UGameplayFuncLib::GetAwGameInstance()->GetWorld(),0.2f,FLatentActionInfo());

	UGameplayFuncLib::GetAwGameInstance()->ChangeLevelByLevelName("Village_Main",false,"");*/
}

void UMessageDlgFunc::CreateChaReturnLastStep(TArray<FString> Params)
{
	
	if (UGameplayFuncLib::GetAwGameInstance()->UIManager->OpenedWidgets.Contains("CreateCharacter"))
	{
		UCreateCharacter* CreateCha = Cast<UCreateCharacter>(UGameplayFuncLib::GetAwGameInstance()->UIManager->OpenedWidgets["CreateCharacter"]);
		if (CreateCha)
		{
			//CreateCha->PrevMain();
			CreateCha->SetMainSelection(CreateCha->GetMainMenuIndex());
		}
	}
}