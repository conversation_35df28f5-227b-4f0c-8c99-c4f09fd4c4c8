// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "QuestScript.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UQuestScript : public UObject
{
	GENERATED_BODY()
public:	

	UFUNCTION(BlueprintCallable)
	static bool TryActiveQuest(TArray<FString> Params);

	UFUNCTION(BlueprintCallable)
	static bool TryAcceptQuest(TArray<FString> Params);

	UFUNCTION(BlueprintCallable)
	static bool TryCompleteQuest(TArray<FString> Params);

	UFUNCTION(BlueprintCallable)
	static bool TryChangeQuestTargetProgress(TArray<FString> Params);

	UFUNCTION(BlueprintCallable)
	static bool TryChangeQuestStage(TArray<FString> Params);

	UFUNCTION(BlueprintCallable)
	static bool TryAbandonQuestStage(TArray<FString> Params);

	UFUNCTION(BlueprintCallable)
	static bool TryReFreshQuest(TArray<FString> Params);

	UFUNCTION(BlueprintCallable)
	static int GetPlayerItemNums(TArray<FString> Params);
};
