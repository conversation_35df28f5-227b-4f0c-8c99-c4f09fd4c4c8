// Fill out your copyright notice in the Description page of Project Settings.


#include "QuestScript.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GamePlay/Quest/AwQuestManager.h"

bool UQuestScript::TryActiveQuest(TArray<FString> Params)
{
	if (!Params.Num()) return false;
	if (!IsValid(UGameplayFuncLib::GetAwGameInstance()))
	{
		return false;
	}

	UAwQuestManager* Manager = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwQuestManager>();

	if (!IsValid(Manager))
	{
		return false;
	}

	FString QuestId = Params.Num() > 0 ? Params[0] : "";
	bool Force = Params.Num() > 1 ? Params[1].Equals("true", ESearchCase::IgnoreCase) : false;

	return  Manager->TryActiveQuest(QuestId, Force);
}

bool UQuestScript::TryAcceptQuest(TArray<FString> Params)
{
	if (!Params.Num()) return false;
	if (!IsValid(UGameplayFuncLib::GetAwGameInstance()))
	{
		return false;
	}

	UAwQuestManager* Manager = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwQuestManager>();

	if (!IsValid(Manager))
	{
		return false;
	}

	FString QuestId = Params.Num() > 0 ? Params[0] : "";
	bool Force = Params.Num() > 1 ? Params[1].Equals("true", ESearchCase::IgnoreCase):false;
		
	return  Manager->TryAcceptQuest(QuestId,Force);
}

bool UQuestScript::TryCompleteQuest(TArray<FString> Params)
{
	if (!Params.Num()) return false;
	if (!IsValid(UGameplayFuncLib::GetAwGameInstance()))
	{
		return false;
	}

	UAwQuestManager* Manager = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwQuestManager>();

	if (!IsValid(Manager))
	{
		return false;
	}

	FString QuestId = Params.Num() > 0 ? Params[0] : "";
	bool Force = Params.Num() > 1 ? Params[1].Equals("true", ESearchCase::IgnoreCase) : false;

	return  Manager->TryCompleteQuest(QuestId, Force);
}

bool UQuestScript::TryChangeQuestTargetProgress(TArray<FString> Params)
{
	if (!Params.Num()) return false;
	if (!IsValid(UGameplayFuncLib::GetAwGameInstance()))
	{
		return false;
	}

	UAwQuestManager* Manager = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwQuestManager>();

	if (!IsValid(Manager))
	{
		return false;
	}

	FString StageFullId = Params.Num() > 0 ? Params[0] : "";
	int ChangeMount = Params.Num() > 1 ? FCString::Atoi(*Params[1]) : 0;

	return  Manager->TryChangeQuestTargetProgress(StageFullId, ChangeMount);
}

bool UQuestScript::TryChangeQuestStage(TArray<FString> Params)
{
	if (!Params.Num()) return false;
	if (!IsValid(UGameplayFuncLib::GetAwGameInstance()))
	{
		return false;
	}

	UAwQuestManager* Manager = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwQuestManager>();

	if (!IsValid(Manager))
	{
		return false;
	}

	FString FullId = Params.Num() > 0 ? Params[0] : "";


	return  Manager->TryChangeQuestStage(FullId);
}

bool UQuestScript::TryAbandonQuestStage(TArray<FString> Params)
{
	if (!Params.Num()) return false;
	if (!IsValid(UGameplayFuncLib::GetAwGameInstance()))
	{
		return false;
	}

	UAwQuestManager* Manager = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwQuestManager>();

	if (!IsValid(Manager))
	{
		return false;
	}

	FString QuestId = Params.Num() > 0 ? Params[0] : "";
	bool Force = Params.Num() > 1 ? Params[1].Equals("true", ESearchCase::IgnoreCase) : false;

	return  Manager->TryAbandonQuest(QuestId, Force);
}

bool UQuestScript::TryReFreshQuest(TArray<FString> Params)
{
	if (!Params.Num()) return false;
	if (!IsValid(UGameplayFuncLib::GetAwGameInstance()))
	{
		return false;
	}

	UAwQuestManager* Manager = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwQuestManager>();

	if (!IsValid(Manager))
	{
		return false;
	}

	FString QuestId = Params.Num() > 0 ? Params[0] : "";
	bool Force = Params.Num() > 1 ? Params[1].Equals("true", ESearchCase::IgnoreCase) : false;

	return  Manager->TryRefreshQuest(QuestId, Force);
}

int UQuestScript::GetPlayerItemNums(TArray<FString> Params)
{
	FString Id = Params.Num() > 0 ? Params[0] : "";
	
	return  UGameplayFuncLib::GetAwGameInstance()->RoleInfo.GetItemObjCount(Id);
}
