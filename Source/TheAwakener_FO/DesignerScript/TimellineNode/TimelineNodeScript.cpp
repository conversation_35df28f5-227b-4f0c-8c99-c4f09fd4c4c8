// Fill out your copyright notice in the Description page of Project Settings.


#include "TimelineNodeScript.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"

bool UTimelineNodeScript::TestTriggerTimeline(float TimeElapsed)
{
	FTransform Transform;
	float GroundLoc = FMath::RandRange(-100,100);
	Transform.SetLocation(FVector(GroundLoc, GroundLoc,50));
	FSceneItemModel Model = UGameplayFuncLib::GetAwDataManager()->GetSceneItemModelById("Test01");
	return true;
}

bool UTimelineNodeScript::TimeToSlomo(float TimeElapsed, TArray<FString> Params)
{
	float Rate = Params.Num()>0?FCString::Atof(*Params[0]):1.f;

	//确保world是GameMode
	UWorld* World = nullptr;
	AAwPlayerController* PC = UGameplayFuncLib::GetWorkingAwPlayerController();
	
	UE_LOG(LogClass,Error,TEXT("UTimelineNodeScript::TimeToSlomo not setup for multiplayer."));
	if (PC)
	{
		World = PC->GetWorld();
	}
	if(World&&World->IsGameWorld())
	{
		UGameplayStatics::SetGlobalTimeDilation(PC,Rate);
	}
	return  true;
}


