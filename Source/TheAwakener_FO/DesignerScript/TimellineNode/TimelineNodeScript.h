// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TimelineNodeScript.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UTimelineNodeScript : public UObject
{
	GENERATED_BODY()

	//返回值bool 意味着该Timenode脚本执行后是否完成并删除 若为false则会再一次tick中继续执行
public:
	UFUNCTION(BlueprintCallable)
	static bool TestTriggerTimeline(float TimeElapsed);
	
	UFUNCTION(BlueprintCallable)
	static bool TimeToSlomo(float TimeElapsed,TArray<FString>Params);
};
