// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/Object.h"
#include "BattleUpgradeScript.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UBattleUpgradeScript : public UObject
{
	GENERATED_BODY()

public:

	// 为角色添加一个Buff
	//Params[0] : BuffId
	//Params[1] : BuffStack
	//Params[2] : BuffDuration
	//Params[3] : IsInfinity
	UFUNCTION(BlueprintCallable)
	static void AddBuff(AAwCharacter* Character, TArray<FString> Params);
	
	// ---------- Script In Buff ----------
	
	//【OnOccur】
	// 立马回复10点觉醒值
	UFUNCTION()
	static FBuffRunResult AddAP_OnOccur(FBuffObj BuffObj, int WasStack, TArray<FString> Params);

	//【OnBeHurt】
	// 收到伤害时修改伤害，并移除该Buff
	// Param[0] : DamageModifyRate
	UFUNCTION()
	static FBuffDamageResult ModifyDamage_OnBeHurt(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【OnHit】
	// 造成伤害时修改伤害，并移除该Buff
	// Param[0] : DamageModifyRate
	UFUNCTION()
	static FBuffDamageResult ModifyDamage_OnHit(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);
	
	//【OnHit】
	// 瞬间攻击造成伤害时添加buff
	// Params[0] : BuffId
	// Params[1] : BuffStack
	// Params[2] : BuffDuration
	// Params[3] : IsInfinity
	UFUNCTION()
	static FBuffDamageResult AddBuff_OnHit_ByJustAttackAction(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);
	
	//【OnKill】
	// 在空中造成击杀添加一个下一次伤害翻倍的Buff
	// Params[0] : BuffId
	// Params[1] : BuffStack
	// Params[2] : BuffDuration
	// Params[3] : IsInfinity
	UFUNCTION()
	static FBuffDamageResult AddBuff_OnKill_OnAir(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);
	
	//【OnKill】
	// CounterAction击杀回复觉醒点AP，每一层回血量是1点
	UFUNCTION()
	static FBuffDamageResult AddAp_OnKill_ByCounterAction(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);
	
	//【OnKill】
	// CounterAction击杀回血，每一层回血量是伤害的1%，最低为1点
	UFUNCTION()
	static FBuffDamageResult AddHp_OnKill_ByCounterAction(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【OnKill】
	// JustAttackAction击杀回血，每一层回血量是伤害的1%，最低为1点
	UFUNCTION()
	static FBuffDamageResult AddHp_OnKill_ByJustAttackAction(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【OnKill】
	// MaxPowerAction击杀回血，每一层回血量是伤害的1%，最低为1点
	UFUNCTION()
	static FBuffDamageResult AddHp_OnKill_ByMaxPowerAction(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【OnKill】
	// 在空中击杀回血，每一层回血量是伤害的1%，最低为1点
	UFUNCTION()
	static FBuffDamageResult AddHp_OnKill_OnAir(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);
	
	// ---------- New Battle Upgrade Buff ----------
	// 2023-10-10
	//【OnHit】
    // 反击伤害增加buff，每层1%
    UFUNCTION()
    static FBuffDamageResult AddCounterAttackDamage(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	// 【OnAnimNotfiy】
	// 回复觉醒值，每层1%，OnAnimNotify
	UFUNCTION()
	static FBuffRunResult AddApOnAnimNotify(FBuffObj BuffObj, int32 WasStack, TArray<FString> Params);

	// 【OnAnimNotfiy】
	// 回复HP，每层1%，OnAnimNotify
	UFUNCTION()
	static FBuffRunResult AddHpOnAnimNotify(FBuffObj BuffObj, int32 WasStack, TArray<FString> Params);

	// 【OnHit】
	// 增加瞬时攻击伤害
	UFUNCTION()
	static FBuffDamageResult AddJustAttackDamage(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	// 【OnAnimNotfiy】
	// 把目标buff的层数设置为当前buff的层数
	UFUNCTION()
	static FBuffRunResult SetBuffStackToThisStack(FBuffObj BuffObj, int32 WasStack, TArray<FString> Params);

	//【OnBeHurt】
	// 伤害减半，每判断一次，减少一层
	UFUNCTION()
	static FBuffDamageResult DamageHalved_OnBeHurt(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	// 【OnHit】
	// 瞬时攻击造成伤害的时候后回血，每层是伤害量的1%
	UFUNCTION()
	static FBuffDamageResult AddHpOnJustAttack(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	// 【OnHit】
	// 在空中造成伤害增加，每层1%
	UFUNCTION()
	static FBuffDamageResult AddDamageOnAir(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	// 【OnHit】
	// 动作伤害增加n%，每判断1次减1层
	// Params[0]:增加倍率，0.2就是增加20%
	UFUNCTION()
	static FBuffDamageResult AddActionDamage(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	// 【OnHit】
	// 在空中造成伤害的时候回血，每层是伤害量的1%
	UFUNCTION()
	static FBuffDamageResult AddHpOnAir(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);
	
	//【OnBeHurt】
	// 一次免疫伤害的buff，判断1次减1层
	UFUNCTION()
	static FBuffDamageResult ImmuneDamage(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【OnKill】
	// 在空中造成击杀，添加一次免疫伤害的buff
	UFUNCTION()
	static FBuffDamageResult SetBuffStackToThisStack_OnKill(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);
	
	//【OnHit】
	// 蓄力攻击伤害增加，每层1%
	UFUNCTION()
	static FBuffDamageResult AddPowerDamage(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);

	//【OnAnimNotfiyBegin】
	// 蓄力攻击伤害增加，每层1%
	UFUNCTION()
	static FBuffRunResult AddPowerSpeed(FBuffObj BuffObj, int32 WasStack, TArray<FString> Params);

	//【OnAnimNotfiyEnd】
	// 蓄力速度增加，每层1%
	UFUNCTION()
	static FBuffRunResult ResetPowerSpeed(FBuffObj BuffObj, int32 WasStack, TArray<FString> Params);

	//【OnHit】
	// 满蓄力造成伤害的时候回血，每层是伤害量的1%
	UFUNCTION()
	static FBuffDamageResult AddHPOnMaxPower(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params);
};
