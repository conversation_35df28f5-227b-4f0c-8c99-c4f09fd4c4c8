// Fill out your copyright notice in the Description page of Project Settings.


#include "BattleUpgradeScript.h"

#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"

void UBattleUpgradeScript::AddBuff(AAwCharacter* Character, TArray<FString> Params)
{
	if (Character && !Character->Dead(true))
	{
		if (Params.Num() < 0) return;
		
		FAddBuffInfo BuffInfo = FAddBuffInfo();
		
		BuffInfo.Model = UGameplayFuncLib::GetAwDataManager()->GetBuffModelById(Params[0]);
		
		if (BuffInfo.Model.Id.IsEmpty()) return;
		
		BuffInfo.AddStack = Params.Num() > 1 ? FCString::Atoi(*Params[1]) : 1;
		BuffInfo.Duration = Params.Num() > 2 ? FCString::Atoi(*Params[2]) : 0;
		BuffInfo.Infinity = Params.Num() > 3 ? FCString::ToBool(*Params[3]) : true;
		BuffInfo.SetToDuration = true;
		BuffInfo.Target = Character;
		BuffInfo.Caster = Character;
		BuffInfo.PropertyOnAdd = FChaProp();
		Character->AddBuff(BuffInfo);
	}
}

FBuffRunResult UBattleUpgradeScript::AddAP_OnOccur(FBuffObj BuffObj, int WasStack, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	Res.TimelineNode = nullptr;

	const int AddAp = Params.Num() > 0 ? FCString::Atoi(*Params[0]) : 0;
	BuffObj.Carrier->CharacterObj.CurrentRes.AP =
		FMath::Clamp(BuffObj.Carrier->CharacterObj.CurrentRes.AP + AddAp,
			0, BuffObj.Carrier->CharacterObj.CurProperty.AP);
	return Res;
}

FBuffDamageResult UBattleUpgradeScript::ModifyDamage_OnBeHurt(FBuffObj BuffObj, FDamageInfo DamInfo,
	AAwCharacter* Target, TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;

	if (BuffObj.Stack <= 0)
		return Res;
	
	const float Rate = Params.Num() > 0 ? FCString::Atof(*Params[0]) : 1;
	if (!Res.DamageInfo.IsHeal) 
		Res.DamageInfo.DamagePower.Physical *= Rate;
	
	// BuffObj.Carrier->RemoveBuffById(BuffObj.Model.Id);
	
	Res.BuffObj.Stack = 0;
	Res.BuffObj.ToBeRemoved = true;
	
	return Res;
}

FBuffDamageResult UBattleUpgradeScript::ModifyDamage_OnHit(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
	TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;

	if (BuffObj.Stack <= 0)
		return Res;
	
	const float Rate = Params.Num() > 0 ? FCString::Atof(*Params[0]) : 1;
	Res.DamageInfo.DamagePower.Physical *= Rate;
	
	// BuffObj.Carrier->RemoveBuffById(BuffObj.Model.Id);
	
	Res.BuffObj.Stack = 0;
	Res.BuffObj.ToBeRemoved = true;
	
	return Res;
}

FBuffDamageResult UBattleUpgradeScript::AddBuff_OnHit_ByJustAttackAction(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
                                                   TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;

	if (DamInfo.DamageSourceType == EAttackSource::JustAttackAction &&
		BuffObj.Carrier && !BuffObj.Carrier->Dead(true))
	{
		if (Params.Num() < 0) return Res;
		
		FAddBuffInfo BuffInfo = FAddBuffInfo();
		
		BuffInfo.Model = UGameplayFuncLib::GetAwDataManager()->GetBuffModelById(Params[0]);
		
		if (BuffInfo.Model.Id.IsEmpty()) return Res;
		
		BuffInfo.AddStack = Params.Num() > 1 ? FCString::Atoi(*Params[1]) : 1;
		BuffInfo.Duration = Params.Num() > 2 ? FCString::Atoi(*Params[2]) : 0;
		BuffInfo.Infinity = Params.Num() > 3 ? FCString::ToBool(*Params[3]) : true;
		BuffInfo.SetToDuration = true;
		BuffInfo.Target = BuffObj.Carrier;
		BuffInfo.Caster = BuffObj.Carrier;
		BuffInfo.PropertyOnAdd = FChaProp();
		BuffObj.Carrier->AddBuff(BuffInfo);
	}
	
	return Res;
}

FBuffDamageResult UBattleUpgradeScript::AddBuff_OnKill_OnAir(FBuffObj BuffObj, FDamageInfo DamInfo,
	AAwCharacter* Target, TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;
	
	if (BuffObj.Carrier && !BuffObj.Carrier->OnGround())
	{
		if (Params.Num() < 0) return Res;
		
		FAddBuffInfo BuffInfo = FAddBuffInfo();
		
		BuffInfo.Model = UGameplayFuncLib::GetAwDataManager()->GetBuffModelById(Params[0]);
		
		if (BuffInfo.Model.Id.IsEmpty()) return Res;
		
		BuffInfo.AddStack = Params.Num() > 1 ? FCString::Atoi(*Params[1]) : 1;
		BuffInfo.Duration = Params.Num() > 2 ? FCString::Atoi(*Params[2]) : 0;
		BuffInfo.Infinity = Params.Num() > 3 ? FCString::ToBool(*Params[3]) : true;
		BuffInfo.SetToDuration = true;
		BuffInfo.Target = BuffObj.Carrier;
		BuffInfo.Caster = BuffObj.Carrier;
		BuffInfo.PropertyOnAdd = FChaProp();
		BuffObj.Carrier->AddBuff(BuffInfo);
	}
	
	return Res;
}

FBuffDamageResult UBattleUpgradeScript::AddAp_OnKill_ByCounterAction(FBuffObj BuffObj, FDamageInfo DamInfo,
                                                                     AAwCharacter* Target, TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;

	const int AddAp = BuffObj.Stack;
	BuffObj.Carrier->CharacterObj.CurrentRes.AP =
		FMath::Clamp(BuffObj.Carrier->CharacterObj.CurrentRes.AP + AddAp,
			0, BuffObj.Carrier->CharacterObj.CurProperty.AP);
	
	return Res;
}

FBuffDamageResult UBattleUpgradeScript::AddHp_OnKill_ByCounterAction(FBuffObj BuffObj, FDamageInfo DamInfo,
                                                                     AAwCharacter* Target, TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;

	AAwCharacter* Carrier = BuffObj.Carrier;
	if (!Carrier)
		return Res;
	
	if (DamInfo.DamageSourceType == EAttackSource::CounterAction ||
		DamInfo.DamageType == EDamageType::SpecDamage && Carrier->CheckHasAttackSourceInCurActionHitInfo(EAttackSource::CounterAction))
	{
		int HealthValue = FMath::Floor(1.0f * DamInfo.DamagePower.Physical * BuffObj.Stack / 100.0f);
		if (HealthValue <= 0)
			HealthValue = 1;
		
		FDamageInfo NewDamageInfo = FDamageInfo();

		NewDamageInfo.Attacker = Carrier;
		NewDamageInfo.DamageSourceType = EAttackSource::Buff;
		NewDamageInfo.Elemental = EChaElemental::Physical;
		NewDamageInfo.DamageType = EDamageType::DirectDamage;
		NewDamageInfo.IsHeal = true;
		NewDamageInfo.DamagePower = FDamageValue(HealthValue, 0);

		UDamageManager::AddDamage(Carrier, Carrier, NewDamageInfo);
	}
	return Res;
}

FBuffDamageResult UBattleUpgradeScript::AddHp_OnKill_ByJustAttackAction(FBuffObj BuffObj, FDamageInfo DamInfo,
	AAwCharacter* Target, TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;

	AAwCharacter* Carrier = BuffObj.Carrier;
	if (!Carrier)
		return Res;
	
	if (DamInfo.DamageSourceType == EAttackSource::JustAttackAction ||
		DamInfo.DamageType == EDamageType::SpecDamage && Carrier->CheckHasAttackSourceInCurActionHitInfo(EAttackSource::JustAttackAction))
	{
		int HealthValue = FMath::Floor(1.0f * DamInfo.DamagePower.Physical * BuffObj.Stack / 100.0f);
		if (HealthValue <= 0)
			HealthValue = 1;
	
		FDamageInfo NewDamageInfo = FDamageInfo();

		NewDamageInfo.Attacker = Carrier;
		NewDamageInfo.DamageSourceType = EAttackSource::Buff;
		NewDamageInfo.Elemental = EChaElemental::Physical;
		NewDamageInfo.DamageType = EDamageType::DirectDamage;
		NewDamageInfo.IsHeal = true;
		NewDamageInfo.DamagePower = FDamageValue(HealthValue, 0);

		UDamageManager::AddDamage(Carrier, Carrier, NewDamageInfo);
	}
	return Res;
}

FBuffDamageResult UBattleUpgradeScript::AddHp_OnKill_ByMaxPowerAction(FBuffObj BuffObj, FDamageInfo DamInfo,
	AAwCharacter* Target, TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;

	AAwCharacter* Carrier = BuffObj.Carrier;
	if (!Carrier)
		return Res;
	
	if (DamInfo.DamageSourceType == EAttackSource::MaxPowerAction ||
		DamInfo.DamageType == EDamageType::SpecDamage && Carrier->CheckHasAttackSourceInCurActionHitInfo(EAttackSource::MaxPowerAction))
	{
		int HealthValue = FMath::Floor(1.0f * DamInfo.DamagePower.Physical * BuffObj.Stack / 100.0f);
		if (HealthValue <= 0)
			HealthValue = 1;
	
		FDamageInfo NewDamageInfo = FDamageInfo();

		NewDamageInfo.Attacker = Carrier;
		NewDamageInfo.DamageSourceType = EAttackSource::Buff;
		NewDamageInfo.Elemental = EChaElemental::Physical;
		NewDamageInfo.DamageType = EDamageType::DirectDamage;
		NewDamageInfo.IsHeal = true;
		NewDamageInfo.DamagePower = FDamageValue(HealthValue, 0);

		UDamageManager::AddDamage(Carrier, Carrier, NewDamageInfo);
	}
	return Res;
}

FBuffDamageResult UBattleUpgradeScript::AddHp_OnKill_OnAir(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
	TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;
	
	if (BuffObj.Carrier && !BuffObj.Carrier->OnGround())
	{
		int HealthValue = FMath::Floor(1.0f * DamInfo.DamagePower.Physical * BuffObj.Stack / 100.0f);
		if (HealthValue <= 0)
			HealthValue = 1;
	
		AAwCharacter* Carrier = BuffObj.Carrier;
		FDamageInfo NewDamageInfo = FDamageInfo();

		NewDamageInfo.Attacker = Carrier;
		NewDamageInfo.DamageSourceType = EAttackSource::Buff;
		NewDamageInfo.Elemental = EChaElemental::Physical;
		NewDamageInfo.DamageType = EDamageType::DirectDamage;
		NewDamageInfo.IsHeal = true;
		NewDamageInfo.DamagePower = FDamageValue(HealthValue, 0);

		UDamageManager::AddDamage(Carrier, Carrier, NewDamageInfo);
	}
	return Res;
}

FBuffDamageResult UBattleUpgradeScript::AddCounterAttackDamage(FBuffObj BuffObj, FDamageInfo DamInfo,
	AAwCharacter* Target, TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;

	if (DamInfo.DamageSourceType == EAttackSource::CounterAction &&
		BuffObj.Carrier && !BuffObj.Carrier->Dead(true))
	{
		const float Rate = 1 + BuffObj.Stack * 0.01;
		Res.DamageInfo.DamagePower.Physical *= Rate;
	}

	return Res;
}

FBuffRunResult UBattleUpgradeScript::AddApOnAnimNotify(FBuffObj BuffObj, int32 WasStack, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	Res.TimelineNode = nullptr;

	const int AddAp = FMath::RoundToInt(BuffObj.Carrier->CharacterObj.CurProperty.AP * 0.01 * Res.BuffObj.Stack);
	
	BuffObj.Carrier->CharacterObj.CurrentRes.AP =
		FMath::Clamp(BuffObj.Carrier->CharacterObj.CurrentRes.AP + AddAp,
			0, BuffObj.Carrier->CharacterObj.CurProperty.AP);
	return Res;
}

FBuffRunResult UBattleUpgradeScript::AddHpOnAnimNotify(FBuffObj BuffObj, int32 WasStack, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	Res.TimelineNode = nullptr;

	const float AddHP = BuffObj.Carrier->CharacterObj.CurProperty.HP * 0.01 * Res.BuffObj.Stack;
	
	FDamageInfo NewDamageInfo = FDamageInfo();

	NewDamageInfo.Attacker = BuffObj.Carrier;
	NewDamageInfo.DamageSourceType = EAttackSource::Buff;
	NewDamageInfo.Elemental = EChaElemental::Physical;
	NewDamageInfo.DamageType = EDamageType::DirectDamage;
	NewDamageInfo.IsHeal = true;
	NewDamageInfo.DamagePower = FDamageValue(AddHP, 0);

	UDamageManager::AddDamage(BuffObj.Carrier, BuffObj.Carrier, NewDamageInfo);
	
	return Res;
}

FBuffDamageResult UBattleUpgradeScript::AddJustAttackDamage(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
	TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;

	if (DamInfo.DamageSourceType == EAttackSource::JustAttackAction &&
		BuffObj.Carrier && !BuffObj.Carrier->Dead(true))
	{
		const float Rate = 1 + BuffObj.Stack * 0.01;
		Res.DamageInfo.DamagePower.Physical *= Rate;
	}

	return Res;
}

FBuffRunResult UBattleUpgradeScript::SetBuffStackToThisStack(FBuffObj BuffObj, int32 WasStack, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	Res.TimelineNode = nullptr;

	const FString TargetBuffId = Params.Num() > 0 ? Params[0] : "";
	if (TargetBuffId.IsEmpty())
		return Res;
	
	FAddBuffInfo BuffInfo = FAddBuffInfo();
	BuffInfo.Model = UGameplayFuncLib::GetAwDataManager()->GetBuffModelById(TargetBuffId);
	if (BuffInfo.Model.Id.IsEmpty())
		return Res;
	
	AAwCharacter* Character = BuffObj.Carrier;
	
	const int StackTotal = Character->GetBuffStackTotal(TargetBuffId);
	const int AddStack = BuffObj.Stack - StackTotal;

	if (AddStack <= 0)
		return Res;
	
	BuffInfo.AddStack = AddStack;
	BuffInfo.Duration = 0;
	BuffInfo.Infinity = true;
	BuffInfo.SetToDuration = false;
	BuffInfo.Target = Character;
	BuffInfo.Caster = Character;
	BuffInfo.PropertyOnAdd = FChaProp();
	Character->AddBuff(BuffInfo);
	
	return Res;
}

FBuffDamageResult UBattleUpgradeScript::DamageHalved_OnBeHurt(FBuffObj BuffObj, FDamageInfo DamInfo,
	AAwCharacter* Target, TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;

	if (BuffObj.Stack <= 0)
		return Res;
	
	if (!Res.DamageInfo.IsHeal)
		Res.DamageInfo.DamagePower.Physical *= 0.5;
	
	Res.BuffObj.Stack -= 1;
	
	return Res;
}

FBuffDamageResult UBattleUpgradeScript::AddHpOnJustAttack(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
	TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;

	if (DamInfo.DamageSourceType == EAttackSource::JustAttackAction &&
		BuffObj.Carrier && !BuffObj.Carrier->Dead(true))
	{
		float AddHP = Res.DamageInfo.DamagePower.Physical * BuffObj.Stack * 0.01;
		AddHP = FMath::Clamp(AddHP, 1, AddHP);
		FDamageInfo NewDamageInfo = FDamageInfo();
		
		NewDamageInfo.Attacker = BuffObj.Carrier;
		NewDamageInfo.DamageSourceType = EAttackSource::Buff;
		NewDamageInfo.Elemental = EChaElemental::Physical;
		NewDamageInfo.DamageType = EDamageType::DirectDamage;
		NewDamageInfo.IsHeal = true;
		NewDamageInfo.DamagePower = FDamageValue(AddHP, 0);

		UDamageManager::AddDamage(BuffObj.Carrier, BuffObj.Carrier, NewDamageInfo);
	}
	
	return Res;
}

FBuffDamageResult UBattleUpgradeScript::AddDamageOnAir(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
	TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;

	if (DamInfo.DamageSourceType == EAttackSource::AttackAction &&
		BuffObj.Carrier && !BuffObj.Carrier->Dead(true) &&
		!BuffObj.Carrier->OnGround())
	{
		const float Rate = 1 + BuffObj.Stack * 0.01;
		Res.DamageInfo.DamagePower.Physical *= Rate;
	}

	return Res;
}

FBuffDamageResult UBattleUpgradeScript::AddActionDamage(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
	TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;
	
	if (BuffObj.Stack <= 0)
		return Res;
	
	const float AddRate = Params.Num() > 0 ? FCString::Atof(*Params[0]) : 0;
	if (AddRate == 0)
		return Res;
	
	if (DamInfo.DamageSourceType == EAttackSource::AttackAction &&
		BuffObj.Carrier && !BuffObj.Carrier->Dead(true))
	{
		Res.DamageInfo.DamagePower.Physical *= 1 + AddRate;
	}

	Res.BuffObj.Stack -= 1;
	
	return Res;
}

FBuffDamageResult UBattleUpgradeScript::AddHpOnAir(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
	TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;

	if (DamInfo.DamageSourceType == EAttackSource::AttackAction &&
		BuffObj.Carrier && !BuffObj.Carrier->Dead(true) &&
		!BuffObj.Carrier->OnGround())
	{
		float AddHP = Res.DamageInfo.DamagePower.Physical * BuffObj.Stack * 0.01;
		AddHP = FMath::Clamp(AddHP, 1, AddHP);
		FDamageInfo NewDamageInfo = FDamageInfo();
		
		NewDamageInfo.Attacker = BuffObj.Carrier;
		NewDamageInfo.DamageSourceType = EAttackSource::Buff;
		NewDamageInfo.Elemental = EChaElemental::Physical;
		NewDamageInfo.DamageType = EDamageType::DirectDamage;
		NewDamageInfo.IsHeal = true;
		NewDamageInfo.DamagePower = FDamageValue(AddHP, 0);

		UDamageManager::AddDamage(BuffObj.Carrier, BuffObj.Carrier, NewDamageInfo);
	}
	
	return Res;
}

FBuffDamageResult UBattleUpgradeScript::ImmuneDamage(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
	TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;

	if (BuffObj.Stack <= 0)
		return Res;
	
	if (!Res.DamageInfo.IsHeal)
		Res.DamageInfo.DamagePower.Physical = 0;
	
	Res.BuffObj.Stack -= 1;
	
	return Res;
}

FBuffDamageResult UBattleUpgradeScript::SetBuffStackToThisStack_OnKill(FBuffObj BuffObj, FDamageInfo DamInfo,
	AAwCharacter* Target, TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;

	const FString TargetBuffId = Params.Num() > 0 ? Params[0] : "";
	if (TargetBuffId.IsEmpty())
		return Res;
	
	FAddBuffInfo BuffInfo = FAddBuffInfo();
	BuffInfo.Model = UGameplayFuncLib::GetAwDataManager()->GetBuffModelById(TargetBuffId);
	if (BuffInfo.Model.Id.IsEmpty())
		return Res;
	
	AAwCharacter* Character = BuffObj.Carrier;
	
	const int StackTotal = Character->GetBuffStackTotal(TargetBuffId);
	const int AddStack = BuffObj.Stack - StackTotal;

	if (AddStack <= 0)
		return Res;
	
	BuffInfo.AddStack = AddStack;
	BuffInfo.Duration = 0;
	BuffInfo.Infinity = true;
	BuffInfo.SetToDuration = false;
	BuffInfo.Target = Character;
	BuffInfo.Caster = Character;
	BuffInfo.PropertyOnAdd = FChaProp();
	Character->AddBuff(BuffInfo);
	
	return Res;
}

FBuffDamageResult UBattleUpgradeScript::AddPowerDamage(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
	TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;

	if (( DamInfo.DamageSourceType == EAttackSource::PowerAction ||
		DamInfo.DamageSourceType == EAttackSource::MaxPowerAction ) &&
		BuffObj.Carrier && !BuffObj.Carrier->Dead(true))
	{
		const float Rate = 1 + BuffObj.Stack * 0.01;
		Res.DamageInfo.DamagePower.Physical *= Rate;
	}

	return Res;
}

FBuffRunResult UBattleUpgradeScript::AddPowerSpeed(FBuffObj BuffObj, int32 WasStack, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	Res.TimelineNode = nullptr;

	const float Rate = 1 + BuffObj.Stack * 0.01;
	Res.BuffObj.Carrier->GetAwAnimInstance()->AnimRateScaleModifies.Add(Res.BuffObj.Model.Id, Rate);
	return Res;
}

FBuffRunResult UBattleUpgradeScript::ResetPowerSpeed(FBuffObj BuffObj, int32 WasStack, TArray<FString> Params)
{
	FBuffRunResult Res = FBuffRunResult();
	Res.BuffObj = BuffObj;
	Res.TimelineNode = nullptr;

	if (Res.BuffObj.Carrier->GetAwAnimInstance()->AnimRateScaleModifies.Contains(Res.BuffObj.Model.Id))
		Res.BuffObj.Carrier->GetAwAnimInstance()->AnimRateScaleModifies.Remove(Res.BuffObj.Model.Id);
	
	return Res;
}

FBuffDamageResult UBattleUpgradeScript::AddHPOnMaxPower(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target,
	TArray<FString> Params)
{
	FBuffDamageResult Res = FBuffDamageResult();
	Res.BuffObj = BuffObj;
	Res.DamageInfo = DamInfo;
	Res.TimelineNode = nullptr;

	if (DamInfo.DamageSourceType == EAttackSource::MaxPowerAction &&
		BuffObj.Carrier && !BuffObj.Carrier->Dead(true))
	{
		float AddHP = Res.DamageInfo.DamagePower.Physical * BuffObj.Stack * 0.01;
		AddHP = FMath::Clamp(AddHP, 1, AddHP);
		FDamageInfo NewDamageInfo = FDamageInfo();
		
		NewDamageInfo.Attacker = BuffObj.Carrier;
		NewDamageInfo.DamageSourceType = EAttackSource::Buff;
		NewDamageInfo.Elemental = EChaElemental::Physical;
		NewDamageInfo.DamageType = EDamageType::DirectDamage;
		NewDamageInfo.IsHeal = true;
		NewDamageInfo.DamagePower = FDamageValue(AddHP, 0);

		UDamageManager::AddDamage(BuffObj.Carrier, BuffObj.Carrier, NewDamageInfo);
	}
	
	return Res;
}
