// Fill out your copyright notice in the Description page of Project Settings.


#include "OrcBoneBreakerAIScript.h"
#include "Kismet/GameplayStatics.h"
#include "Kismet/KismetMathLibrary.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"

FAICommand UOrcBoneBreakerAIScript::CreatePillarByActorTag(AAwCharacter* Character, UAwAIComponent* AIComponent,
                                                           TArray<FString> Params)
{
	if(Params.Num() > 1)
	{
		const int SpawnNum = FCString::Atoi(*Params[0]);
		const FName FindTag = FName(Params[1]);
		TArray<AActor*> SpawnPoint;
		UGameplayStatics::GetAllActorsOfClassWithTag(Character,AActor::StaticClass(),FindTag,SpawnPoint);
		if(SpawnPoint.Num() > 0)
		{
			TArray<AActor*> CanUseSpawnPoint;
			for (AActor* Point : SpawnPoint)
			{
				if(FVector::Dist2D(Character->GetActorLocation(), Point->GetActorLocation()) > 400)
					CanUseSpawnPoint.Add(Point);
			}
			for (int i = 0; i < SpawnNum; i++)
			{
				if(CanUseSpawnPoint.Num() <= 0)
					break;
				int RandIndex = FMath::RandRange(0,CanUseSpawnPoint.Num() - 1);
				FSceneItemModel SceneItemModel = UGameplayFuncLib::GetDataManager()->GetSceneItemModelById("Orc_BoneBreaker_Pillar");
				if(RandIndex < CanUseSpawnPoint.Num())
				{
					if(CanUseSpawnPoint[RandIndex])
					{
						FTransform SceneItemTrans = CanUseSpawnPoint[RandIndex]->GetActorTransform();
						FVector SceneDir = Character->GetActorLocation() - CanUseSpawnPoint[RandIndex]->GetActorLocation();
						SceneDir.Z = 0;
						SceneDir.Normalize();
						FRotator SceneItemRot = UKismetMathLibrary::MakeRotFromX(SceneDir);
						SceneItemTrans.SetRotation(SceneItemRot.Quaternion());
						AAwSceneItem* NewSceneItem = UGameplayFuncLib::CreateSceneItem(SceneItemModel, 1, SceneItemTrans);
						if(NewSceneItem)
						{
							CanUseSpawnPoint.RemoveAt(RandIndex);
						}
					}
				}
			}
		}
	}
	return FAICommand();
}
