// Fill out your copyright notice in the Description page of Project Settings.


#include "WereRatShamamAIScript.h"

#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/FunctionLibrary/MathFuncLib.h"

TArray<AAwCharacter*> UWereRatShamamAIScript::GetViewedTribesBySameSide(TArray<AAwCharacter*> AllCharacter,
                                                                        int WishSide)
{
	//声明数组的名称
	TArray<AAwCharacter*> ViewedTribe;
	//遍历
	for (AAwCharacter* Character : AllCharacter)
	{
		if (Character->Side == WishSide) //如果是同一个side
			ViewedTribe.Add(Character); //就add进数组里面
	}

	return ViewedTribe; //返回数组
}

//test function 这是一个可以显示在ue4里面的弹幕，用于解释json脚本和ue的传递关系
bool UWereRatShamamAIScript::TestConditionByMang(AAwCharacter* Character, UAwAIComponent* AIComponent,
                                                 TArray<FString> Params)
{
	UKismetSystemLibrary::PrintString(GWorld, FString("Test Condition By Mang"));
	// UE_LOG(LogTemp, Log, TEXT("Test Condition By Mang"));
	return true;
}

//==============================================condition==============================================

//视野内没有敌人+不在族人旁边
bool UWereRatShamamAIScript::CheckShamanNotNearTribe(AAwCharacter* Character, UAwAIComponent* AIComponent,
                                                     TArray<FString> Params)
{
	TArray<FAIFindChaInfo> ViewedEnemy = AIComponent->GetAllViewedCharacter(); //视野范围
	if (ViewedEnemy.Num() <= 0) //没有看见敌人
	{
		float CheckDis = 500; //创建距离500
		if (Params.Num()) //params的数量
			CheckDis = FCString::Atof(*Params[0]); //转义，ue4的语法，直接记忆
		for (TTuple<AAwCharacter*, FString> EachCharacter : UGameplayFuncLib::GetAwGameState()->AllCharacters)
		//for UGameplayFuncLib::GetAwGameState()回车
		{
			if (!EachCharacter.Key)
				continue;
			if (EachCharacter.Key != Character //所有角色排除自己
				&& EachCharacter.Key->Side == Character->Side //阵营相同
				&& !EachCharacter.Key->Dead() //角色不是死亡状态
				&& FVector::Dist(Character->GetActorLocation(), EachCharacter.Key->GetActorLocation()) <= CheckDis)
			//与族人的距离大于500，不在族人旁边
			{
				return false;
			}
		}
		return true;
	}
	return false;
}

//有目标族人
bool UWereRatShamamAIScript::CheckShamanWithAim(AAwCharacter* Character, UAwAIComponent* AIComponent,
                                                TArray<FString> Params)
{
	TArray<FAIFindChaInfo> ViewedTribe = AIComponent->GetAllViewedCharacter(true, false); //视野范围
	for (const FAIFindChaInfo ViewedCharacter : ViewedTribe) //遍历视野内的对象
	{
		if (!Character->IsEnemy(ViewedCharacter.Character)) //这些对象中有一个不是敌人
			return true;
	}
	return false;
}

//视野内没有敌人+在族人边上+有正常工作buff
bool UWereRatShamamAIScript::CheckShamanNearTribeWithWorkBuff(AAwCharacter* Character, UAwAIComponent* AIComponent,
                                                              TArray<FString> Params)
{
	TArray<FAIFindChaInfo> ViewedEnemy = AIComponent->GetAllViewedCharacter(true, true); //视野范围
	if (ViewedEnemy.Num() <= 0) //没有看见敌人
	{
		float CheckDis = 500; //创建距离500

		if (Params.Num()) //params的数量
			CheckDis = FCString::Atof(*Params[0]); //转义，ue4的语法，直接记忆//把json填进的文本转化为数字

		//TMap<AAwCharacter*, FString> Monkey;
		for (TTuple<AAwCharacter*, FString> EachCharacter : UGameplayFuncLib::GetAwGameState()->AllCharacters)
		//for UGameplayFuncLib::GetAwGameState()回车
		{
			if (!EachCharacter.Key) continue;
			if (EachCharacter.Key != Character //所有角色排除自己
				&& EachCharacter.Key->Side == Character->Side //阵营相同
				&& !EachCharacter.Key->Dead() //角色不是死亡状态
				&& FVector::Dist(Character->GetActorLocation(), EachCharacter.Key->GetActorLocation()) < CheckDis)
			//与族人的距离小于500，在族人旁边
			{
				TArray<FAIFindChaInfo> ViewedTribe = AIComponent->GetAllViewedCharacter(true, false); //视野范围
				//if (ViewedTribe.Num() > 0) //有目标族人
				{
					for (FAIFindChaInfo EachViewedCharacter : ViewedTribe)
					{
						if (EachViewedCharacter.Character->GetBuff("DoMine", TArray<AAwCharacter*>()).Num() <= 2)
						//族人采矿buff<=2，为正常工作状态
						{
							return true;
						}
					}
				}
			}
		}
	}
	return false;
}


//视野内没有敌人+在族人边上+有偷懒buff
bool UWereRatShamamAIScript::CheckShamanNearTribeWithLazyBuff(AAwCharacter* Character, UAwAIComponent* AIComponent,
                                                              TArray<FString> Params)
{
	TArray<FAIFindChaInfo> ViewedEnemy = AIComponent->GetAllViewedCharacter(true, true); //视野范围
	if (ViewedEnemy.Num() <= 0) //没有看见敌人
	{
		float CheckDis = 500; //创建距离500

		if (Params.Num()) //params的数量
			CheckDis = FCString::Atof(*Params[0]); //转义，ue4的语法，直接记忆//把json填进的文本转化为数字

		//TMap<AAwCharacter*, FString> Monkey;
		for (TTuple<AAwCharacter*, FString> EachCharacter : UGameplayFuncLib::GetAwGameState()->AllCharacters)
		//for UGameplayFuncLib::GetAwGameState()回车
		{
			if (EachCharacter.Key != Character //所有角色排除自己
				&& EachCharacter.Key->Side == Character->Side //阵营相同
				&& !EachCharacter.Key->Dead() //角色不是死亡状态
				&& FVector::Dist(Character->GetActorLocation(), EachCharacter.Key->GetActorLocation()) < CheckDis)
			//与族人的距离小于500，在族人旁边
			{
				TArray<FAIFindChaInfo> ViewedTribe = AIComponent->GetAllViewedCharacter(true, false); //视野范围
				//if (ViewedTribe.Num() > 0) //有目标族人
				{
					for (FAIFindChaInfo EachViewedCharacter : ViewedTribe)
					{
						if (EachViewedCharacter.Character->GetBuff("DoMine", TArray<AAwCharacter*>()).Num() > 2)
						//族人采矿buff>3，进入偷懒状态
						{
							return true;
						}
					}
				}
			}
		}
	}
	return false;
}

//敌人在视野范围内
bool UWereRatShamamAIScript::CheckShamanInBattle(AAwCharacter* Character, UAwAIComponent* AIComponent,
                                                 TArray<FString> Params)
{
	for (TTuple<AAwCharacter*, FString> EachCharacter : UGameplayFuncLib::GetAwGameState()->AllCharacters)
	{
		if (EachCharacter.Key != Character
			&& EachCharacter.Key->Side != Character->Side
			&& !EachCharacter.Key->Dead())
			return true;
	}
	return false;
}


//敌人在半径为10米的圆内
bool UWereRatShamamAIScript::CheckShamanInCircle(AAwCharacter* Character, UAwAIComponent* AIComponent,
                                                 TArray<FString> Params)
{
	float CheckDis = 1000;

	if (Params.Num()) //params的数量
		CheckDis = FCString::Atof(*Params[0]); //转义，ue4的语法，直接记忆

	for (TTuple<AAwCharacter*, FString> EachCharacter : UGameplayFuncLib::GetAwGameState()->AllCharacters)
	//for UGameplayFuncLib::GetAwGameState()回车
	{
		if(!EachCharacter.Key)continue;
		if (EachCharacter.Key != Character //所有角色排除自己，如果是敌人，这个条件应该不需要
			&& EachCharacter.Key->Side != Character->Side //阵营不同
			&& EachCharacter.Key->Dead() //角色不是死亡状态
			&& FVector::Dist(Character->GetActorLocation(), EachCharacter.Key->GetActorLocation()) < CheckDis)
			//与敌人的距离小于500
			return true;
	}
	return false;
}

//敌人在视野范围内+FightingWill_Level==1 + FightingWill.Value <= 2000
bool UWereRatShamamAIScript::CheckShamanPuff(AAwCharacter* Character, UAwAIComponent* AIComponent,
                                             TArray<FString> Params)
{
	float FightPercent = Character->FightingWill.ValuePercentage(); //先声明一个FightPercent存fightingwill百分比值
	if (GuyWeak(Character) == false || FightPercent > 0.2) return false;
	//如果fightingwill不是1或者fightingwill的value大于2000，直接return false
	for (FAIFindChaInfo ViewedCharacter : AIComponent->GetAllViewedCharacter()) //敌人在视野范围内
	{
		if (!ViewedCharacter.Character->Dead(true) && Character->IsEnemy(ViewedCharacter.Character))
			return true;
	}
	return false;
}


//敌人在视野内 + FightingWill_Level==0 + 积累伤害的buff>=[300]
bool UWereRatShamamAIScript::CheckShamanScared(AAwCharacter* Character, UAwAIComponent* AIComponent,
                                               TArray<FString> Params)
{
	//float FightPercent = Character->FightingWill.ValuePercentage(); //先声明一个FightPercent存fightingwill百分比值
	//if (GuyWeak(Character) == false || FightPercent > 0.2) return false;
	//如果fightingwill不是1或者fightingwill的value大于2000，直接return false
	for (FAIFindChaInfo ViewedCharacter : AIComponent->GetAllViewedCharacter()) //敌人在视野范围内
	{
		if (!ViewedCharacter.Character->Dead(true) && Character->IsEnemy(ViewedCharacter.Character))
			return true;
	}
	return false;
}

//敌人在视野内 + FightingWill_Level==0
bool UWereRatShamamAIScript::CheckShamanTired(AAwCharacter* Character, UAwAIComponent* AIComponent,
                                              TArray<FString> Params)
{
	if (GuyStare(Character) == false) return true; //如果fightingwill不是0直接return false
	for (FAIFindChaInfo ViewedCharactor : AIComponent->GetAllViewedCharacter()) //敌人在视野范围内
	{
		if (!ViewedCharactor.Character->Dead(true) && ViewedCharactor.Character)
			return true;
	}
	return false;
}

//敌人在视野内 + 敌人在【30】米范围内 + FightingWill_Level>0
bool UWereRatShamamAIScript::CheckShamanExcited(AAwCharacter* Character, UAwAIComponent* AIComponent,
                                                TArray<FString> Params)
{
	if (GuyStare(Character) == true) return true; //如果fightingwill是0直接return false
	for (FAIFindChaInfo ViewedCharactor : AIComponent->GetAllViewedCharacter())
	{
		if (!ViewedCharactor.Character->Dead(true) && ViewedCharactor.Character)
			return true;
	}
	return false;
	//因为我不想写30米范围内，所以我思考了一下有没有必要写30米范围内，然后我的结论是不用写，甚至视野范围内不写问题可能也不大？
}

//敌人在视野内 + 敌人在【30】米范围内 + FightingWill_Level>0+战场上鼠人存活率＜【30%】
//同理，当友方伤亡惨重时，萨满应该优先进行激励buff的施加，和玩家在哪没有必然联系，所以前面两条关于距离的condition都不需要
bool UWereRatShamamAIScript::CheckShamanCalling(AAwCharacter* Character, UAwAIComponent* AIComponent,
                                                TArray<FString> Params)
{
	if (GuyStare(Character) == true)return false; //fightingwill是0直接return false
	for (TTuple<AAwCharacter*, FString> EachCharacter : UGameplayFuncLib::GetAwGameState()->AllCharacters)
	//for UGameplayFuncLib::GetAwGameState()回车
	{
		if (EachCharacter.Key != Character //所有角色排除自己
			&& EachCharacter.Key->Side == Character->Side //阵营相同
			&& EachCharacter.Key->Dead() //角色不是死亡状态
			&& FVector::Dist(Character->GetActorLocation(), EachCharacter.Key->GetActorLocation()) < 3000)
		//身边1000距离以内的敌人
		{
			TArray<FAIFindChaInfo> ViewedTribe = AIComponent->GetAllViewedCharacter(); //视野范围
			if (ViewedTribe.Num() <= 10) //如果可以作为目标（活着）的族人比率不及30%，暂时没写，假设场景内有30个左右鼠人
			{
				return true;
			}
		}
	}
	return false;
}

//敌人在视野内 + 敌人在【2】米范围内 + 目标在【-60】度到【-45】度之间 + FightingWill_Level>0
bool UWereRatShamamAIScript::CheckShamanAttackLeft(AAwCharacter* Character, UAwAIComponent* AIComponent,
                                                   TArray<FString> Params)
{
	//敌人fightingwill=0直接return false
	if (GuyStare(Character) == true) return false;
	//敌人在2米范围外直接return false
	float CheckDis = 200; //2米
	if (Params.Num()) //params的数量
		CheckDis = FCString::Atof(*Params[0]); //转义，ue4的语法，直接记忆
	for (TTuple<AAwCharacter*, FString> EachCharacter : UGameplayFuncLib::GetAwGameState()->AllCharacters)
	//for UGameplayFuncLib::GetAwGameState()回车
	{
		if (!EachCharacter.Key)
			continue; //防止EachCharacter.Key为空
		//计算夹角
		float Degree =
			UMathFuncLib::GetDegreeBetweenTwoVector(Character->GetActorForwardVector(),
			                                        EachCharacter.Key->GetActorLocation() - Character->
			                                        GetActorLocation());
		if (EachCharacter.Key->Side != Character->Side //阵营不同
			&& !EachCharacter.Key->Dead() //角色不是死亡状态
			&& FVector::Dist(Character->GetActorLocation(), EachCharacter.Key->GetActorLocation()) < CheckDis)
		//与敌人的距离小于2米
		{
			for (FAIFindChaInfo ViewedCharactor : AIComponent->GetAllViewedCharacter()) //敌人在视野范围内
			{
				if (!ViewedCharactor.Character->Dead(true) && ViewedCharactor.Character)
					return true;
			}
		}
	}
	return false;
}

//敌人在视野内 + 敌人在【2】米范围内 + 目标在【45】度到【60】度之间  + FightingWill_Level>0
bool UWereRatShamamAIScript::CheckShamanAttackRight(AAwCharacter* Character, UAwAIComponent* AIComponent,
                                                    TArray<FString> Params)
{
	//敌人fightingwill=0直接return false
	if (GuyStare(Character) == true) return false;
	//敌人在2米范围外直接return false
	float CheckDis = 200; //2米
	if (Params.Num()) //params的数量
		CheckDis = FCString::Atof(*Params[0]); //转义，ue4的语法，直接记忆
	for (TTuple<AAwCharacter*, FString> EachCharacter : UGameplayFuncLib::GetAwGameState()->AllCharacters)
	//for UGameplayFuncLib::GetAwGameState()回车
	{
		if (!EachCharacter.Key)
			continue; //防止EachCharacter.Key为空
		//计算夹角
		float Degree =
			UMathFuncLib::GetDegreeBetweenTwoVector(Character->GetActorForwardVector(),
			                                        EachCharacter.Key->GetActorLocation() - Character->
			                                        GetActorLocation());
		if (EachCharacter.Key->Side != Character->Side //阵营不同
			&& !EachCharacter.Key->Dead() //角色不是死亡状态
			&& FVector::Dist(Character->GetActorLocation(), EachCharacter.Key->GetActorLocation()) < CheckDis)
		//与敌人的距离小于2米
		{
			for (FAIFindChaInfo ViewedCharactor : AIComponent->GetAllViewedCharacter()) //敌人在视野范围内
			{
				if (!ViewedCharactor.Character->Dead(true) && ViewedCharactor.Character)
					return true;
			}
		}
	}
	return false;
}

// 敌人在【-90】度到【90】度内 + 敌人在【0.5】米范围内+FightingWill_Level>0
bool UWereRatShamamAIScript::CheckShamanWatch(AAwCharacter* Character, UAwAIComponent* AIComponent,
                                              TArray<FString> Params)
{
	//敌人fightingwill=0直接return false
	if (GuyStare(Character) == true) return true;
	for (FAIFindChaInfo ViewedCharactor : AIComponent->GetAllViewedCharacter()) //敌人在视野范围内
	{
		if (!ViewedCharactor.Character->Dead(true) && ViewedCharactor.Character)
			return true;
	}
	return false;
}

//敌人在视野内 + 敌人在【1.5】米范围内 + FightingWill_Level>0
bool UWereRatShamamAIScript::CheckShamanKnock(AAwCharacter* Character, UAwAIComponent* AIComponent,
                                              TArray<FString> Params)
{
	//敌人fightingwill=0直接return false
	if (GuyStare(Character) == true) return false;
	//敌人在2米范围外直接return false
	float CheckDis = 150; //1.5米
	if (Params.Num()) //params的数量
		CheckDis = FCString::Atof(*Params[0]); //转义，ue4的语法，直接记忆
	for (TTuple<AAwCharacter*, FString> EachCharacter : UGameplayFuncLib::GetAwGameState()->AllCharacters)
	//for UGameplayFuncLib::GetAwGameState()回车
	{
		if (!EachCharacter.Key)
			continue; //防止EachCharacter.Key为空
		//计算夹角
		float Degree =
			UMathFuncLib::GetDegreeBetweenTwoVector(Character->GetActorForwardVector(),
			                                        EachCharacter.Key->GetActorLocation() - Character->
			                                        GetActorLocation());
		if (EachCharacter.Key->Side != Character->Side //阵营不同
			&& !EachCharacter.Key->Dead() //角色不是死亡状态
			&& FVector::Dist(Character->GetActorLocation(), EachCharacter.Key->GetActorLocation()) < CheckDis)
		//与敌人的距离小于1.5米
		{
			for (FAIFindChaInfo ViewedCharactor : AIComponent->GetAllViewedCharacter()) //敌人在视野范围内
			{
				if (!ViewedCharactor.Character->Dead(true) && ViewedCharactor.Character)
					return true;
			}
		}
	}
	return false;
}

//敌人在视野内 + 敌人在【2】米范围内 + FightingWill_Level>0 
bool UWereRatShamamAIScript::CheckShamanPoke(AAwCharacter* Character, UAwAIComponent* AIComponent,
                                             TArray<FString> Params)
{
	//敌人fightingwill=0直接return false
	if (GuyStare(Character) == true) return false;
	//敌人在2米范围外直接return false
	float CheckDis = 200; //2米
	if (Params.Num()) //params的数量
		CheckDis = FCString::Atof(*Params[0]); //转义，ue4的语法，直接记忆
	for (TTuple<AAwCharacter*, FString> EachCharacter : UGameplayFuncLib::GetAwGameState()->AllCharacters)
	//for UGameplayFuncLib::GetAwGameState()回车
	{
		if (!EachCharacter.Key)
			continue; //防止EachCharacter.Key为空
		if (EachCharacter.Key->Side != Character->Side //阵营不同
			&& !EachCharacter.Key->Dead() //角色不是死亡状态
			&& FVector::Dist(Character->GetActorLocation(), EachCharacter.Key->GetActorLocation()) < CheckDis)
		//与敌人的距离小于2米
		{
			for (FAIFindChaInfo ViewedCharactor : AIComponent->GetAllViewedCharacter()) //敌人在视野范围内
			{
				if (!ViewedCharactor.Character->Dead(true) && ViewedCharactor.Character)
					return true;
			}
		}
	}
	return false;
}

//敌人在视野范围内+距离敌人【10】米目以外，【30】米以内
bool UWereRatShamamAIScript::CheckShamanMove(AAwCharacter* Character, UAwAIComponent* AIComponent,
                                                   TArray<FString> Params)
{
	float CheckDis1 = 1000; //10米
	float CheckDis2 = 3000; //30米
	if (Params.Num()) //params的数量
	{
		CheckDis1 = FCString::Atof(*Params[0]); //转义，ue4的语法，直接记忆
		CheckDis2 = FCString::Atof(*Params[1]); //转义，ue4的语法，直接记忆
	}
	for (FAIFindChaInfo ViewedCharactor : AIComponent->GetAllViewedCharacter()) //敌人在视野范围内
	{
		if (!ViewedCharactor.Character->Dead(true) //敌人不在死亡状态
			&& ViewedCharactor.Character != nullptr //在视野范围内
			&& Character->IsEnemy(ViewedCharactor.Character) //是敌人
			&& ((FVector::Dist(Character->GetActorLocation(),
			                   ViewedCharactor.Character->GetActorLocation()) > CheckDis1) //在十米以外
				&& (FVector::Dist(Character->GetActorLocation(),
				                  ViewedCharactor.Character->GetActorLocation()) < CheckDis2)) //在三十米以内
		)
			return true;
	}
	return false;
}

bool UWereRatShamamAIScript::CheckNotNearAlly(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	float CheckDis = 500;
	if (Params.Num())
		CheckDis = FCString::Atof(*Params[0]);
	for (TTuple<AAwCharacter*, FString> EachCharacter : UGameplayFuncLib::GetAwGameState()->AllCharacters)
	{
		if (!EachCharacter.Key)
			continue;
		if (EachCharacter.Key != Character
			&& !Character->IsEnemy(EachCharacter.Key)
			&& !EachCharacter.Key->Dead()
			&& FVector::Dist(Character->GetActorLocation(), EachCharacter.Key->GetActorLocation()) <= CheckDis)
		{
			return false;
		}
	}
	return true;
}

bool UWereRatShamamAIScript::CheckNearAllyHasBuff(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	if (Params.Num())
	{
		float CheckDis = FCString::Atof(*Params[0]);
		for (TTuple<AAwCharacter*, FString> EachCharacter : UGameplayFuncLib::GetAwGameState()->AllCharacters)
		{
			if (!EachCharacter.Key)
				continue;
			if (EachCharacter.Key != Character
			&& !Character->IsEnemy(EachCharacter.Key)
			&& !EachCharacter.Key->Dead()
			&& FVector::Dist(Character->GetActorLocation(), EachCharacter.Key->GetActorLocation()) <= CheckDis)
			{
				if(Params.Num() > 1)
				{
					const TArray<FBuffObj*> BuffList = EachCharacter.Key->GetBuff(Params[1]);
					if(BuffList.Num())
					{
						if(Params.Num() > 3)
						{
							const int BuffMinStack = FCString::Atoi(*Params[2]);
							const int BuffMaxStack = FCString::Atoi(*Params[3]);
							for (FBuffObj* BuffObj : BuffList)
							{
								if(BuffObj->Stack <= BuffMaxStack && BuffObj->Stack >= BuffMinStack)
									return true;
							}
							continue;
						}
					}
					else
						continue;
				}
				return true;
			}
		}
	}
	return false;
}

bool UWereRatShamamAIScript::CheckHasViewedAlly(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	for (FAIFindChaInfo ViewedCharactor : AIComponent->GetAllViewedCharacter(false, false))
	{
		if(!Character->IsEnemy(ViewedCharactor.Character) && !ViewedCharactor.Character->Dead())
			return true;
	}
	return false;
}

bool UWereRatShamamAIScript::CheckWereRatSurvivalRate(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	TArray<FBuffObj*> BuffList = Character->GetBuff("RecordWereRatNum");
	for (FBuffObj* Buff : BuffList)
	{
		if(Buff->Param.Contains("MaxStack"))
		{
			const int MaxStack = FCString::Atoi(*Buff->Param["MaxStack"]);
			const int CurStack = Buff->Stack;
			float CheckPercent = 0.3;
			if(Params.Num())
				CheckPercent = FCString::Atof(*Params[0]);
			if((CurStack / MaxStack) <= CheckPercent)
				return true;
		}
	}
	return false;
}


//Action
//猫哥写的，用于说明脚本json和ue4的内在联系
FAICommand UWereRatShamamAIScript::TestActionByMang(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
                                                    
{
	UKismetSystemLibrary::PrintString(GWorld, FString("Test Action By Mang!"));

	return FAICommand();
}

FAICommand UWereRatShamamAIScript::WereRatShamanLeisure(AAwCharacter* Character, UAwAIComponent* AIComponent,
                                                        TArray<FString> Params)
{
	FAICommand NewCommand = FAICommand();
	if (Params.Num())
	{
		NewCommand = AIComponent->CreateUseActionCommand(Params[0], Character->GetActorForwardVector());
	}
	return NewCommand;
}

FAICommand UWereRatShamamAIScript::WereRatShamanSupervise(AAwCharacter* Character, UAwAIComponent* AIComponent,
                                                          TArray<FString> Params)
{
	FVector TargetLoc; //建立存放目标坐标的对象
	FAIFindChaInfo Target = AIComponent->GetClosestAlly(); //目标为距离角色最近的友方
	if (Target.Character)
	{
		TargetLoc = UGameplayFuncLib::GetPointOnGround(Target.Character);
		//Character Root点在中心，所以获得地面坐标后要加上HalfHeight
		TargetLoc.Z += Target.Character->GetCapsuleComponent()->GetScaledCapsuleHalfHeight();
	}
	TArray<FVector> LocList;
	LocList.Add(TargetLoc);
	FAICommand NewCommand = AIComponent->CreateMoveToCommand(LocList, 0);
	return NewCommand;
}

FAICommand UWereRatShamamAIScript::WereRatShamanTalk(AAwCharacter* Character, UAwAIComponent* AIComponent,
                                                     TArray<FString> Params)
{
	FAICommand NewCommand = FAICommand();
	if (Params.Num())
	{
		NewCommand = AIComponent->CreateUseActionCommand(Params[0], Character->GetActorForwardVector());
	}
	return NewCommand;
}

FAICommand UWereRatShamamAIScript::WereRatShamanPoke(AAwCharacter* Character, UAwAIComponent* AIComponent,
                                                     TArray<FString> Params)
{
	FAICommand NewCommand = FAICommand();
	if (Params.Num())
	{
		NewCommand = AIComponent->CreateUseActionCommand(Params[0], Character->GetActorForwardVector());
	}
	return NewCommand;
}

FAICommand UWereRatShamamAIScript::WereRatShamanBuff(AAwCharacter* Character, UAwAIComponent* AIComponent,
                                                     TArray<FString> Params)
{
	FAICommand NewCommand = FAICommand();
	if (Params.Num())
	{
		NewCommand = AIComponent->CreateUseActionCommand(Params[0], Character->GetActorForwardVector());
	}
	return NewCommand;
}

FAICommand UWereRatShamamAIScript::WereRatShamanBreath(AAwCharacter* Character, UAwAIComponent* AIComponent,
                                                       TArray<FString> Params)
{
	FAICommand NewCommand = FAICommand();
	if (Params.Num())
	{
		NewCommand = AIComponent->CreateUseActionCommand(Params[0], Character->GetActorForwardVector());
	}
	return NewCommand;
}

FAICommand UWereRatShamamAIScript::WereRatShamanScared(AAwCharacter* Character, UAwAIComponent* AIComponent,
                                                       TArray<FString> Params)
{
	FAICommand NewCommand = FAICommand();
	if (Params.Num())
	{
		NewCommand = AIComponent->CreateUseActionCommand(Params[0], Character->GetActorForwardVector());
	}
	return NewCommand;
}

FAICommand UWereRatShamamAIScript::WereRatShamanTired(AAwCharacter* Character, UAwAIComponent* AIComponent,
                                                      TArray<FString> Params)
{
	FAICommand NewCommand = FAICommand();
	if (Params.Num())
	{
		NewCommand = AIComponent->CreateUseActionCommand(Params[0], Character->GetActorForwardVector());
	}
	return NewCommand;
}

FAICommand UWereRatShamamAIScript::WereRatShamanCrazy(AAwCharacter* Character, UAwAIComponent* AIComponent,
                                                      TArray<FString> Params)
{
	FAICommand NewCommand = FAICommand();
	if (Params.Num())
	{
		NewCommand = AIComponent->CreateUseActionCommand(Params[0], Character->GetActorForwardVector());
	}
	return NewCommand;
}

FAICommand UWereRatShamamAIScript::WereRatShamanCalling(AAwCharacter* Character, UAwAIComponent* AIComponent,
                                                        TArray<FString> Params)
{
	FAICommand NewCommand = FAICommand();
	if (Params.Num())
	{
		NewCommand = AIComponent->CreateUseActionCommand(Params[0], Character->GetActorForwardVector());
	}
	return NewCommand;
}

FAICommand UWereRatShamamAIScript::WereRatShamanAttackLeft(AAwCharacter* Character, UAwAIComponent* AIComponent,
                                                           TArray<FString> Params)
{
	FAICommand NewCommand = FAICommand();
	if (Params.Num())
	{
		NewCommand = AIComponent->CreateUseActionCommand(Params[0], Character->GetActorForwardVector());
	}
	return NewCommand;
}

FAICommand UWereRatShamamAIScript::WereRatShamanAttackRight(AAwCharacter* Character, UAwAIComponent* AIComponent,
                                                            TArray<FString> Params)
{
	FAICommand NewCommand = FAICommand();
	if (Params.Num())
	{
		NewCommand = AIComponent->CreateUseActionCommand(Params[0], Character->GetActorForwardVector());
	}
	return NewCommand;
}

FAICommand UWereRatShamamAIScript::WereRatShamanSwear(AAwCharacter* Character, UAwAIComponent* AIComponent,
                                                      TArray<FString> Params)
{
	FAICommand NewCommand = FAICommand();
	if (Params.Num())
	{
		NewCommand = AIComponent->CreateUseActionCommand(Params[0], Character->GetActorForwardVector());
	}
	return NewCommand;
}

FAICommand UWereRatShamamAIScript::WereRatShamanAttackTapping(AAwCharacter* Character, UAwAIComponent* AIComponent,
                                                              TArray<FString> Params)
{
	FAICommand NewCommand = FAICommand();
	if (Params.Num())
	{
		NewCommand = AIComponent->CreateUseActionCommand(Params[0], Character->GetActorForwardVector());
	}
	return NewCommand;
}

FAICommand UWereRatShamamAIScript::WereRatShamanAttackPoking(AAwCharacter* Character, UAwAIComponent* AIComponent,
                                                             TArray<FString> Params)
{
	FAICommand NewCommand = FAICommand();
	if (Params.Num())
	{
		NewCommand = AIComponent->CreateUseActionCommand(Params[0], Character->GetActorForwardVector());
	}
	return NewCommand;
}

FAICommand UWereRatShamamAIScript::WereRatShamanMove(AAwCharacter* Character, UAwAIComponent* AIComponent,
                                                     TArray<FString> Params)
{
	FAICommand NewCommand = FAICommand();
	float CheckDis1 = 1000; //10米
	float CheckDis2 = 3000; //30米
	if (Params.Num()>1) //params的数量大于一
		{
		CheckDis1 = FCString::Atof(*Params[0]); //转义，ue4的语法，直接记忆
		CheckDis2 = FCString::Atof(*Params[1]); //转义，ue4的语法，直接记忆
		}
	FAIFindChaInfo ClosetTarget = FAIFindChaInfo();
	for (FAIFindChaInfo ViewedCharactor : AIComponent->GetAllViewedCharacter()) //敌人在视野范围内
		{
			if (!ViewedCharactor.Character->Dead(true) //敌人不在死亡状态
				&& ViewedCharactor.Character != nullptr //在视野范围内
				&& Character->IsEnemy(ViewedCharactor.Character) //是敌人
				&& ((FVector::Dist(Character->GetActorLocation(),
								   ViewedCharactor.Character->GetActorLocation()) > CheckDis1) //在十米以外
					&& (FVector::Dist(Character->GetActorLocation(),
									  ViewedCharactor.Character->GetActorLocation()) < CheckDis2)) //在三十米以内
			)
			{
				if(!ClosetTarget.Character)
					ClosetTarget = ViewedCharactor;
				else
				{
					if(ViewedCharactor.DistanceXY < ClosetTarget.DistanceXY)
						ClosetTarget = ViewedCharactor;
				}
			}
		}
	if(ClosetTarget.Character)
	{
		if (Params.Num()>4)
		{
			float CheckDis = FCString::Atof(*Params[2]);
			if(ClosetTarget.DistanceXY < CheckDis)
				NewCommand = AIComponent->CreateUseActionCommand(Params[3],Character->GetActorForwardVector());
			else
				NewCommand = AIComponent->CreateUseActionCommand(Params[4],Character->GetActorForwardVector());
			AIComponent->SetTargetEnemy(ClosetTarget);
			AIComponent->TargetDirFunc = UDataFuncLib::SplitFuncNameAndParams("MobAIScript.GetCurTargetEnemyDir()");
		}
	}
	return NewCommand;
}

FAICommand UWereRatShamamAIScript::MoveToClosetViewedAlly(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	FAICommand NewCommand = FAICommand();
	FAIFindChaInfo ClosetTarget = FAIFindChaInfo();
	for (FAIFindChaInfo ViewedCharactor : AIComponent->GetAllViewedCharacter(false, false))
	{
		if(!Character->IsEnemy(ViewedCharactor.Character) && !ViewedCharactor.Character->Dead())
		{
			if(!ClosetTarget.Character)
				ClosetTarget = ViewedCharactor;
			else if(ViewedCharactor.DistanceXY < ClosetTarget.DistanceXY)
				ClosetTarget = ViewedCharactor;
		}
	}
	if(ClosetTarget.Character)
	{
		FVector TargetLoc = UGameplayFuncLib::GetPointOnGround(ClosetTarget.Character);
		//Character Root点在中心，所以获得地面坐标后要加上HalfHeight
		TargetLoc.Z += ClosetTarget.Character->GetCapsuleComponent()->GetScaledCapsuleHalfHeight();
		TArray<FVector> LocList;
		LocList.Add(TargetLoc);
		return AIComponent->CreateMoveToCommand(LocList, 0);
	}
	return NewCommand;
}

FAICommand UWereRatShamamAIScript::AddAllySignalAround(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	float CheckDis = 3000;
	if(Params.Num())
		CheckDis = FCString::Atof(*Params[0]);
	for (TTuple<AAwCharacter*, FString> EachCharacter : UGameplayFuncLib::GetAwGameState()->AllCharacters)
	{
		if(!EachCharacter.Key) continue;
		if(EachCharacter.Key != Character
			&& !Character->IsEnemy(EachCharacter.Key)
			&& !EachCharacter.Key->Dead()
			&& FVector::Dist(Character->GetActorLocation(), EachCharacter.Key->GetActorLocation()) <= CheckDis)
		{
			if(AIComponent->GetTargetEnemy().Character)
				EachCharacter.Key->GetAIComponent()->OnAllyCall(AIComponent->GetTargetEnemy().Character);
		}
	}
	return FAICommand();
}

FAICommand UWereRatShamamAIScript::AddBerserkBuffAround(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	float CheckDis = 3000;
	if(Params.Num())
		CheckDis = FCString::Atof(*Params[0]);
	float BuffTime = 10;
	if(Params.Num() > 1)
		BuffTime = FCString::Atof(*Params[1]);
	
	FBuffModel BuffModel = UGameplayFuncLib::GetDataManager()->GetBuffModelById("WereRat_Berserk");
	if(BuffModel.Id != "")
	{
		for (TTuple<AAwCharacter*, FString> EachCharacter : UGameplayFuncLib::GetAwGameState()->AllCharacters)
		{
			if(!EachCharacter.Key) continue;
			if(EachCharacter.Key != Character
				&& !Character->IsEnemy(EachCharacter.Key)
				&& !EachCharacter.Key->Dead()
				&& FVector::Dist(Character->GetActorLocation(), EachCharacter.Key->GetActorLocation()) <= CheckDis)
			{
				FAddBuffInfo BuffInfo = FAddBuffInfo(Character, EachCharacter.Key, BuffModel, 1, BuffTime, true);
				EachCharacter.Key->AddBuff(BuffInfo);
			}
		}
	}
	return FAICommand();
}

FAICommand UWereRatShamamAIScript::SpawnWereRatCommandos(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	TArray<AActor*> ActorList;
	UGameplayStatics::GetAllActorsOfClass(Character,ASpawnPoint::StaticClass(),ActorList);
	TMap<AActor*,float> SpawnPointList;
	for (AActor* SpawnPoint : ActorList)
	{
		SpawnPointList.Add(SpawnPoint, FVector::Dist(Character->GetActorLocation(),SpawnPoint->GetActorLocation()));
	}
	SpawnPointList.ValueSort([](float A,float B)
	{
		return A < B;
	});
	int SpawnNum = 0;
	for (TTuple<AActor*, float> SpawnPoint : SpawnPointList)
	{
		if(SpawnNum < 2)
		{
			UGameplayFuncLib::CreateCharacterByMobInfo(SpawnPoint.Key->GetTransform(), "WereRatCommando", Character->CharacterObj.Level,Character->Side,EMobRank::Normal);
			SpawnNum++;
		}
	}
	return FAICommand();
}

FAICommand UWereRatShamamAIScript::BasicBattle(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	FAICommand NewCommand = FAICommand();
	FAIFindChaInfo ClosetTarget = FAIFindChaInfo();

	float CheckMinDisXY = 0;
	float CheckNearMaxDisXY = 0;
	float CheckMidMinDisXY = 0;
	float CheckMidMaxDisXY = 0;
	float CheckFarMinDisXY = 0;
	float CheckMaxDisXY = 0;

	if (Params.Num() > 5)
	{
		CheckMinDisXY = FCString::Atof(*Params[0]);
		CheckNearMaxDisXY = FCString::Atof(*Params[1]);
		CheckMidMinDisXY = FCString::Atof(*Params[2]);
		CheckMidMaxDisXY = FCString::Atof(*Params[3]);
		CheckFarMinDisXY = FCString::Atof(*Params[4]);
		CheckMaxDisXY = FCString::Atof(*Params[5]);
	}
	for (FAIFindChaInfo FindedTarget : AIComponent->GetAllViewedCharacter(false, true))
	{
		if (FindedTarget.Character)
		{
			const float CurDisXY = FindedTarget.DistanceXY;
			const float CurDisZ = FindedTarget.DistanceZ;

			const float CheckDisZ = AIComponent->GetSightZRadius();
			if ((CurDisXY <= CheckMaxDisXY) && (CurDisXY >= CheckMinDisXY) && (CurDisZ < CheckDisZ))
			{
				if (!ClosetTarget.Character)
					ClosetTarget = FindedTarget;
				else if (FindedTarget.DistanceXY < ClosetTarget.DistanceXY)
					ClosetTarget = FindedTarget;
			}
		}
	}
	if (ClosetTarget.Character)
	{
		TArray<FString> CanUseComboRange;
		//检测选中的敌人在哪个连招的范围
		//连招1 范围（0-450）
		if (ClosetTarget.DistanceXY <= CheckNearMaxDisXY && ClosetTarget.DistanceXY >= CheckMinDisXY)
		{
			CanUseComboRange.Add("Near");
		}
		//连招2 范围（300-900）
		if (ClosetTarget.DistanceXY <= CheckMidMaxDisXY && ClosetTarget.DistanceXY >= CheckMidMinDisXY)
		{
			CanUseComboRange.Add("Mid");
		}
		//连招3 范围（800-检测的最大范围）
		if (ClosetTarget.DistanceXY <= CheckMaxDisXY && ClosetTarget.DistanceXY >= CheckFarMinDisXY)
		{
			CanUseComboRange.Add("Far");
		}
		if (CanUseComboRange.Num())
		{
			const FString UseCombo = CanUseComboRange[FMath::RandRange(0, CanUseComboRange.Num() - 1)];
			if (UseCombo == "Near")
			{
				int UseActionIndex = FMath::RandRange(0, 10);
				switch (UseActionIndex)
				{
				case 0:
				case 1:
				case 2:
					{
						NewCommand = AIComponent->CreateUseActionCommand("Attack_S1", Character->GetActorForwardVector());
						break;
					}
				case 3:
				case 4:
				case 5:
					{
						NewCommand = AIComponent->CreateUseActionCommand("Attack_S2", Character->GetActorForwardVector());
						break;
					}
				case 6:
				case 7:
					{
						NewCommand = AIComponent->CreateUseActionCommand("LeftPace", Character->GetActorForwardVector());
						break;
					}
				case 8:
				case 9:
					{
						NewCommand = AIComponent->CreateUseActionCommand("RightPace", Character->GetActorForwardVector());
						break;
					}
				case 10:
					{
						NewCommand = AIComponent->CreateUseActionCommand("Dodge_Back", Character->GetActorForwardVector());
						break;
					}
				}
			}
			else if (UseCombo == "Mid")
			{
				int UseActionIndex = FMath::RandRange(0, 7);
				switch (UseActionIndex)
				{
				case 0:
				case 1:
				case 2:
					{
						NewCommand = AIComponent->CreateUseActionCommand("ThrowFireBall", Character->GetActorForwardVector());
						break;
					}
				case 3:
				case 4:
					{
						NewCommand = AIComponent->CreateUseActionCommand("LeftPace", Character->GetActorForwardVector());
						break;
					}
				case 5:
				case 6:
					{
						NewCommand = AIComponent->CreateUseActionCommand("RightPace", Character->GetActorForwardVector());
						break;
					}
				case 7:
					{
						NewCommand = AIComponent->CreateUseActionCommand("Dodge_Back", Character->GetActorForwardVector());
						break;
					}
				}
			}
			else if (UseCombo == "Far")
			{
				int UseActionIndex = FMath::RandRange(0, 3);
				switch (UseActionIndex)
				{
				case 0:
					{
						NewCommand = AIComponent->CreateUseActionCommand("ThrowFireBall", Character->GetActorForwardVector());
						break;
					}
				case 1:
					{
						NewCommand = AIComponent->CreateUseActionCommand("LeftPace", Character->GetActorForwardVector());
						break;
					}
				case 2:
					{
						NewCommand = AIComponent->CreateUseActionCommand("RightPace", Character->GetActorForwardVector());
						break;
					}
				case 3:
					{
						NewCommand = AIComponent->CreateUseActionCommand("Crawling_Short", Character->GetActorForwardVector());
						break;
					}
				}
			}
			AIComponent->SetTargetEnemy(ClosetTarget);
			AIComponent->TargetDirFunc = UDataFuncLib::SplitFuncNameAndParams("MobAIScript.GetCurTargetEnemyDir()");
		}
	}

	return NewCommand;
}

