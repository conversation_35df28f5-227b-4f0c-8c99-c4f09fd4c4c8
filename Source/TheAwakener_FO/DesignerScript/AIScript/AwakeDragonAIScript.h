// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "AIUtils.h"
#include "TheAwakener_FO/GamePlay/AOE/AWAoeBase.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "TheAwakener_FO/GamePlay/Characters/AI/AwAIComponent.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "Kismet/GameplayStatics.h"
#include "UObject/Object.h"
#include "AwakeDragonAIScript.generated.h"





/**
 * 觉醒之龙的AI脚本函数
 */
UCLASS()
class THEAWAKENER_FO_API UAwakeDragonAIScript : public UObject
{
	GENERATED_BODY()
public:
	//检测周围敌人数量
	UFUNCTION(BlueprintCallable)
	static bool  CheckAroundEnemyNumGreater(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	//检测锁定目标的敌人距离是否在[0]-[1]之间
	UFUNCTION(BlueprintCallable)
	static bool  CheckEnemyTargetDistance(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//检测动作是否在CD
	UFUNCTION(BlueprintCallable)
	static bool  CheckActionCoolDown(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//检测距离地面的高度
	UFUNCTION(BlueprintCallable)
	static bool  CheckGroundHeight(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//AI执行Action,Params[0]为ActionId,播放动画
	UFUNCTION(BlueprintCallable)
	static FAICommand AIDoActionWithCoolDown(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//给指定动作添加CD
	UFUNCTION(BlueprintCallable)
	static FAICommand AddActionCoolDown(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//AI执行Action,Params[0]为离地高度,Params[1]为ActionId,播放动画
	UFUNCTION(BlueprintCallable)
	static FAICommand AITurnToStimulateDoActionInSky(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	
	//基础连招机制
	UFUNCTION(BlueprintCallable)
	static FAICommand AwakeDragonBasicBattle(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
};

