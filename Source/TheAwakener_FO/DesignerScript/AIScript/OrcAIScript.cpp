// Fill out your copyright notice in the Description page of Project Settings.


#include "OrcAIScript.h"

#include "Kismet/KismetMathLibrary.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GamePlay/Buff/BuffManager.h"

bool UOrcAIScript::CheckHasStimulate(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	return ((AIComponent->GetAllViewedCharacter().Num() > 0 && AIComponent->GetClosestDistanceEnemy(true, false).Character)
		|| AIComponent->Stimuli_Voice.Num() > 0 || AIComponent->Stimuli_Offended.Num() > 0 || AIComponent->Stimuli_AllySignal.Num() > 0);
}

FAICommand UOrcAIScript::RandomNormalOrcCombo(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	FAICommand NewCommand = FAICommand();
	FAIFindChaInfo ClosetTarget = FAIFindChaInfo();

	float CheckMinDisXY = 0;
	float CheckNearMaxDisXY = 0;
	float CheckMidMinDisXY = 0;
	float CheckMidMaxDisXY = 0;
	float CheckFarMinDisXY = 0;
	float CheckMaxDisXY = 0;

	if (Params.Num() > 5)
	{
		CheckMinDisXY = FCString::Atof(*Params[0]);
		CheckNearMaxDisXY = FCString::Atof(*Params[1]);
		CheckMidMinDisXY = FCString::Atof(*Params[2]);
		CheckMidMaxDisXY = FCString::Atof(*Params[3]);
		CheckFarMinDisXY = FCString::Atof(*Params[4]);
		CheckMaxDisXY = FCString::Atof(*Params[5]);
	}
	for (FAIFindChaInfo FindedTarget : AIComponent->GetAllViewedCharacter(false, true))
	{
		if (FindedTarget.Character)
		{
			const float CurDisXY = FindedTarget.DistanceXY;
			const float CurDisZ = FindedTarget.DistanceZ;

			const float CheckDisZ = AIComponent->GetSightZRadius();
			if ((CurDisXY <= CheckMaxDisXY) && (CurDisXY >= CheckMinDisXY) && (CurDisZ < CheckDisZ))
			{
				if (!ClosetTarget.Character)
					ClosetTarget = FindedTarget;
				else if (FindedTarget.DistanceXY < ClosetTarget.DistanceXY)
					ClosetTarget = FindedTarget;
			}
		}
	}
	if (ClosetTarget.Character)
	{
		//检测怪物是否在玩家的正面(如果目标是怪物，则不管正反面)
		bool bInPlayerFront = true;
		if (ClosetTarget.Character->UnderPlayerControl())
		{
			FVector TargetDir = ClosetTarget.Character->GetActorLocation() - Character->GetActorLocation();
			TargetDir.Normalize();
			FVector PlayerCameraDir = ClosetTarget.Character->GetAwCameraComponent()->GetCameraRotate().Vector();
			if (FMath::Abs(UMathFuncLib::GetDegreeBetweenTwoVector(TargetDir, PlayerCameraDir)) < 90)
				bInPlayerFront = false;
		}

		TArray<FString> CanUseComboRange;
		//检测选中的敌人在哪个连招的范围
		//连招1 范围（0-450）
		if (ClosetTarget.DistanceXY <= CheckNearMaxDisXY && ClosetTarget.DistanceXY >= CheckMinDisXY)
		{
			CanUseComboRange.Add("NearCombo");
		}
		//连招2 范围（300-900）
		if (ClosetTarget.DistanceXY <= CheckMidMaxDisXY && ClosetTarget.DistanceXY >= CheckMidMinDisXY)
		{
			CanUseComboRange.Add("MidCombo");
		}
		//连招3 范围（800-检测的最大范围）
		if (ClosetTarget.DistanceXY <= CheckMaxDisXY && ClosetTarget.DistanceXY >= CheckFarMinDisXY)
		{
			CanUseComboRange.Add("FarCombo");
		}
		if (CanUseComboRange.Num())
		{
			const FString UseCombo = CanUseComboRange[FMath::RandRange(0, CanUseComboRange.Num() - 1)];
			FString ActionId = "";
			if (UseCombo == "NearCombo")
			{
				int UseComboIndex = FMath::RandRange(0, 15);
				if (!bInPlayerFront)
					UseComboIndex = FMath::RandRange(9, 15);
				switch (UseComboIndex)
				{
					case 0:
					case 1:
					{
						AIComponent->AddUseActionTag("OrcNearCombo0");
							ActionId = "NormalAttack_S1";
						NewCommand = AIComponent->CreateUseActionCommand("NormalAttack_S1", Character->GetActorForwardVector());
					break;
					}
					case 2:
					case 3:
					{
						AIComponent->AddUseActionTag("OrcNearCombo1");
							ActionId = "NormalAttack_S4";
						NewCommand = AIComponent->CreateUseActionCommand("NormalAttack_S4", Character->GetActorForwardVector());
						break;
					}
					case 4:
					{
						AIComponent->AddUseActionTag("OrcNearCombo2");
							ActionId = "Dodge_JumpBack";
						NewCommand = AIComponent->CreateUseActionCommand("Dodge_JumpBack", Character->GetActorForwardVector());
						break;
					}
					case 5:
					{
						AIComponent->AddUseActionTag("NormalOrcNearCombo");
							ActionId = "Roar";
						NewCommand = AIComponent->CreateUseActionCommand("Roar", Character->GetActorForwardVector());
						break;
					}
					case 6:
					{
						AIComponent->AddUseActionTag("NormalOrcNearCombo");
							ActionId = "Roar_C2";
						NewCommand = AIComponent->CreateUseActionCommand("Roar_C2", Character->GetActorForwardVector());
						break;
					}
					case 7:
					{
						AIComponent->AddUseActionTag("NormalOrcNearCombo");
							ActionId = "Roar_C3";
						NewCommand = AIComponent->CreateUseActionCommand("Roar_C3", Character->GetActorForwardVector());
						break;
					}
					case 8:
					{
						AIComponent->AddUseActionTag("OrcNearCombo4");
							ActionId = "Dodge_JumpBack";
						NewCommand = AIComponent->CreateUseActionCommand("Dodge_JumpBack", Character->GetActorForwardVector());
						break;
					}
					case 9:
					{
						AIComponent->AddUseActionTag("OrcNearCombo3");
							ActionId = "Dodge_Right";
						NewCommand = AIComponent->CreateUseActionCommand("Dodge_Right", Character->GetActorForwardVector());
						break;
					}
					case 10:
					{
						AIComponent->AddUseActionTag("OrcNearCombo5");
							ActionId = "Dodge_Back";
						NewCommand = AIComponent->CreateUseActionCommand("Dodge_Back", Character->GetActorForwardVector());
						break;
					}
					case 11:
					{
							ActionId = "Walk_Back";
						NewCommand = AIComponent->CreateUseActionCommand("Walk_Back", Character->GetActorForwardVector());
						break;
					}
					case 12:
					{
							ActionId = "Walk_Left";
						NewCommand = AIComponent->CreateUseActionCommand("Walk_Left", Character->GetActorForwardVector());
						break;
					}
					case 13:
					{
							//ActionId = "Walk_Right";
						//NewCommand = AIComponent->CreateUseActionCommand("Walk_Right", Character->GetActorForwardVector());
						//break;
					}
					case 14:
					{
						AIComponent->AddUseActionTag("OrcNearCombo0");
							ActionId = "NormalAttack_S1";
						NewCommand = AIComponent->CreateUseActionCommand("NormalAttack_S1", Character->GetActorForwardVector());
						break;
					}
					case 15:
					{
						AIComponent->AddUseActionTag("OrcNearCombo1");
							ActionId = "NormalAttack_S4";
						NewCommand = AIComponent->CreateUseActionCommand("NormalAttack_S4", Character->GetActorForwardVector());
						break;
					}
				}
			}
			else if (UseCombo == "MidCombo")
			{
				int UseComboIndex = FMath::RandRange(0, 7);
				if (!bInPlayerFront)
					UseComboIndex = FMath::RandRange(5, 7);
				switch (UseComboIndex)
				{
				case 0:
				{
					AIComponent->AddUseActionTag("OrcMidCombo0");
						ActionId = "NormalAttack_M1";
					NewCommand = AIComponent->CreateUseActionCommand("NormalAttack_M1", Character->GetActorForwardVector());
					break;
				}
				case 1:
				{
					AIComponent->AddUseActionTag("OrcMidCombo1");
						ActionId = "NormalAttack_M2";
					NewCommand = AIComponent->CreateUseActionCommand("NormalAttack_M2", Character->GetActorForwardVector());
					break;
				}
				case 2:
				{
					AIComponent->AddUseActionTag("OrcMidCombo2");
						ActionId = "NormalAttack_M5";
					NewCommand = AIComponent->CreateUseActionCommand("NormalAttack_M5", Character->GetActorForwardVector());
					break;
				}
				case 3:
				{
					AIComponent->AddUseActionTag("NormalOrcMidCombo");
						ActionId = "Roar";
					NewCommand = AIComponent->CreateUseActionCommand("Roar", Character->GetActorForwardVector());
					break;
				}
				case 4:
				{
					AIComponent->AddUseActionTag("NormalOrcMidCombo");
						ActionId = "Roar_C2";
					NewCommand = AIComponent->CreateUseActionCommand("Roar_C2", Character->GetActorForwardVector());
					break;
				}
				case 5:
				{
					AIComponent->AddUseActionTag("NormalOrcMidCombo");
						ActionId = "Roar_C3";
					NewCommand = AIComponent->CreateUseActionCommand("Roar_C3", Character->GetActorForwardVector());
					break;
				}
				case 6:
				{
						ActionId = "Walk_Right";
					NewCommand = AIComponent->CreateUseActionCommand("Walk_Right", Character->GetActorForwardVector());
					break;
				}
				case 7:
				{
						//ActionId = "Walk_Left";
					//NewCommand = AIComponent->CreateUseActionCommand("Walk_Left", Character->GetActorForwardVector());
					//break;
				}
				case 8:
				{
					AIComponent->AddUseActionTag("OrcMidCombo1");
						ActionId = "NormalAttack_M2";
					NewCommand = AIComponent->CreateUseActionCommand("NormalAttack_M2", Character->GetActorForwardVector());
					break;
				}
				case 9:
				{
					AIComponent->AddUseActionTag("OrcMidCombo3");
						ActionId = "Dodge_Front";
					NewCommand = AIComponent->CreateUseActionCommand("Dodge_Front", Character->GetActorForwardVector());
					break;
				}
				case 10:
				{
					AIComponent->AddUseActionTag("OrcMidCombo4");
						ActionId = "Dodge_Left";
					NewCommand = AIComponent->CreateUseActionCommand("Dodge_Left", Character->GetActorForwardVector());
					break;
				}
				}
			}
			else if (UseCombo == "FarCombo")
			{
				int UseComboIndex = FMath::RandRange(0, 9);
				if (!bInPlayerFront)
					UseComboIndex = FMath::RandRange(7, 9);
				switch (UseComboIndex)
				{
				case 0:
				case 1:
				{
					AIComponent->AddUseActionTag("OrcFarCombo0");
						ActionId = "NormalAttack_L1";
					NewCommand = AIComponent->CreateUseActionCommand("NormalAttack_L1", Character->GetActorForwardVector());
					break;
				}
				case 2:
				case 3:
				{
					AIComponent->AddUseActionTag("OrcFarCombo1");
						ActionId = "NormalAttack_L2";
					NewCommand = AIComponent->CreateUseActionCommand("NormalAttack_L2", Character->GetActorForwardVector());
					break;
				}
				case 4:
				{
					AIComponent->AddUseActionTag("NormalOrcFarCombo");
						ActionId = "Roar";
					NewCommand = AIComponent->CreateUseActionCommand("Roar", Character->GetActorForwardVector());
					break;
				}
				case 5:
				{
					AIComponent->AddUseActionTag("NormalOrcFarCombo");
						ActionId = "Roar_C2";
					NewCommand = AIComponent->CreateUseActionCommand("Roar_C2", Character->GetActorForwardVector());
					break;
				}
				case 6:
				{
					AIComponent->AddUseActionTag("NormalOrcFarCombo");
						ActionId = "Roar_C3";
					NewCommand = AIComponent->CreateUseActionCommand("Roar_C3", Character->GetActorForwardVector());
					break;
				}
				case 7:
				{
					AIComponent->AddUseActionTag("OrcFarCombo2");
						ActionId = "Walk_Front";
					NewCommand = AIComponent->CreateUseActionCommand("Walk_Front", Character->GetActorForwardVector());
					break;
				}
				case 8:
				{
					AIComponent->AddUseActionTag("OrcFarCombo3");
						ActionId = "Dodge_Front";
					NewCommand = AIComponent->CreateUseActionCommand("Dodge_Front", Character->GetActorForwardVector());
					break;
				}
				case 9:
				{
					AIComponent->AddUseActionTag("OrcFarCombo0");
						ActionId = "NormalAttack_L1";
					NewCommand = AIComponent->CreateUseActionCommand("NormalAttack_L1", Character->GetActorForwardVector());
					break;
				}
				}
			}
			UKismetSystemLibrary::PrintString(GWorld, FString("OrcComboStartAction:").Append(ActionId));
			AIComponent->SetTargetEnemy(ClosetTarget);
			AIComponent->TargetDirFunc = UDataFuncLib::SplitFuncNameAndParams("MobAIScript.GetCurTargetEnemyDir()");
		}
	}
	return NewCommand;
}
