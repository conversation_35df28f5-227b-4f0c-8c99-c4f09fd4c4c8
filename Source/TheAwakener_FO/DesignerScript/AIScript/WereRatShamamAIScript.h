// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "UObject/Object.h"
#include "WereRatShamamAIScript.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UWereRatShamamAIScript : public UObject
{
	GENERATED_BODY()

private:
	//声明一个可以筛选出同阵营数组的函数
	static TArray<AAwCharacter*> GetViewedTribesBySameSide(TArray<AAwCharacter*> AllCharacter, int WishSide);

	//声明一个偷懒的buff，当采矿层数>=3时，突击兵身上出现偷懒buff
	//

	//copy from orge
	//亢奋中
	static bool GuyExcited(const AAwCharacter* Guy) { return Guy->FightingWill.Level >= 3; }
	//普通中
	static bool GuyNormalFightingWill(const AAwCharacter* Guy) { return Guy->FightingWill.Level == 2; }
	//虚弱中
	static bool GuyWeak(const AAwCharacter* Guy) { return Guy->FightingWill.Level == 1; }
	//发呆中
	static bool GuyStare(const AAwCharacter* Guy) { return Guy->FightingWill.Level <= 0; }

public:
	//condition
	// 测试用condition
	UFUNCTION(BlueprintCallable)
	static bool TestConditionByMang(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//视野内没有敌人+不在族人旁边 周围Params[0]范围内没有WereRat
	UFUNCTION(BlueprintCallable)
	static bool CheckShamanNotNearTribe(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//视野内没有敌人+不在族人旁边+有目标族人
	UFUNCTION(BlueprintCallable)
	static bool CheckShamanWithAim(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//视野内没有敌人+在族人边上+有偷懒buff
	UFUNCTION(BlueprintCallable)
	static bool CheckShamanNearTribeWithWorkBuff(AAwCharacter* Character, UAwAIComponent* AIComponent,
	                                             TArray<FString> Params);

	//视野内没有敌人+在族人边上+有偷懒buff
	UFUNCTION(BlueprintCallable)
	static bool CheckShamanNearTribeWithLazyBuff(AAwCharacter* Character, UAwAIComponent* AIComponent,
	                                             TArray<FString> Params);

	//敌人在视野范围内(有空再写夹角
	UFUNCTION(BlueprintCallable)
	static bool CheckShamanInBattle(AAwCharacter* Character, UAwAIComponent* AIComponent,
	                                TArray<FString> Params);

	//敌人在半径为10米的圆内
	UFUNCTION(BlueprintCallable)
	static bool CheckShamanInCircle(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//敌人在视野范围内+FightingWill_Level==1 + FightingWill.Value <= 2000
	UFUNCTION(BlueprintCallable)
	static bool CheckShamanPuff(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//敌人在视野内 + FightingWill_Level==0 
	UFUNCTION(BlueprintCallable)
	static bool CheckShamanScared(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	
	//敌人在视野内 + FightingWill_Level==0
	UFUNCTION(BlueprintCallable)
	static bool CheckShamanTired(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//敌人在视野内 + 敌人在【30】米范围内 + FightingWill_Level>0
	UFUNCTION(BlueprintCallable)
	static bool CheckShamanExcited(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//敌人在视野内 + 敌人在【30】米范围内 + FightingWill_Level>0+战场上鼠人存活率＜【30%】
	UFUNCTION(BlueprintCallable)
	static bool CheckShamanCalling(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//敌人在视野内 + 敌人在【2】米范围内 + 目标在正偏左方60度以上 + FightingWill_Level>0
	UFUNCTION(BlueprintCallable)
	static bool CheckShamanAttackLeft(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//敌人在视野内 + 敌人在【2】米范围内 + 目标在正偏右方60度以上 + FightingWill_Level>0
	UFUNCTION(BlueprintCallable)
	static bool CheckShamanAttackRight(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//敌人在视野内 + 敌人在正前方【180】度【0.5】米范围内 + FightingWill_Level>0
	UFUNCTION(BlueprintCallable)
	static bool CheckShamanWatch(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//敌人在视野内 + 敌人在【1.5】米范围内 + FightingWill_Level>0
	UFUNCTION(BlueprintCallable)
	static bool CheckShamanKnock(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//敌人在视野内 + 敌人在【2】米范围内 + FightingWill_Level>0 
	UFUNCTION(BlueprintCallable)
	static bool CheckShamanPoke(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	
	//敌人在视野范围内+距离敌人【10】米目以外，【30】米以内
	UFUNCTION(BlueprintCallable)
	static bool CheckShamanMove(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);


	//----------ZDH ADD---------------//
	
	//身边Params[0]距离范围内没有族人
	UFUNCTION(BlueprintCallable)
	static bool CheckNotNearAlly(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//身边Params[0]距离范围内有族人,且族人有Id = Params[1]的Buff,Buff层数为Params[2]-Params[3]
	UFUNCTION(BlueprintCallable)
	static bool CheckNearAllyHasBuff(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//视野内有族人
	UFUNCTION(BlueprintCallable)
	static bool CheckHasViewedAlly(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//检测场上鼠人存活率,Params[0] = 检测要小于的比例
	UFUNCTION(BlueprintCallable)
	static bool CheckWereRatSurvivalRate(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	
	//----------ZDH ADD---------------//
	

	//action
	//打印通知
	UFUNCTION(BlueprintCallable)
	static FAICommand TestActionByMang(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	// //播放idle动画
	// UFUNCTION(BlueprintCallable)
	// static FAICommand WereRatShamanIdle(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//播放转转头/左右闻闻动画
	UFUNCTION(BlueprintCallable)
	static FAICommand WereRatShamanLeisure(AAwCharacter* Character, UAwAIComponent* AIComponent,
	                                       TArray<FString> Params);

	//向目标族人走去
	UFUNCTION(BlueprintCallable)
	static FAICommand WereRatShamanSupervise(AAwCharacter* Character, UAwAIComponent* AIComponent,
	                                         TArray<FString> Params);

	//播放对话动画
	UFUNCTION(BlueprintCallable)
	static FAICommand WereRatShamanTalk(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	
	//播放戳醒动画
	UFUNCTION(BlueprintCallable)
	static FAICommand WereRatShamanPoke(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	
	//转向敌人+播放摇法杖动画，半径【30】米内的族人均进入战斗
	UFUNCTION(BlueprintCallable)
	static FAICommand WereRatShamanBuff(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	
	//发呆：喘气 + FightingWill-=2000
	UFUNCTION(BlueprintCallable)
	static FAICommand WereRatShamanBreath(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	
	//发呆：害怕 + FightingWill_Level=1
	UFUNCTION(BlueprintCallable)
	static FAICommand WereRatShamanScared(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	
	//发呆：累了 + FightingWill_Level=1
	UFUNCTION(BlueprintCallable)
	static FAICommand WereRatShamanTired(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	
	//施放法术动画，给半径【30】米内的鼠人加上【狂热】buff，表现为鼠人身上有红光特效，眼睛变红
	UFUNCTION(BlueprintCallable)
	static FAICommand WereRatShamanCrazy(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	
	//施放复活动画，读条【10】秒，结束后复在自己身后【3】米【60】度召唤出一左一右两只突击兵鼠人
	UFUNCTION(BlueprintCallable)
	static FAICommand WereRatShamanCalling(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	
	//用法杖向左戳，戳的同时转向敌人
	UFUNCTION(BlueprintCallable)
	static FAICommand WereRatShamanAttackLeft(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	
	//用法杖向右戳，戳的同时转向敌人
	UFUNCTION(BlueprintCallable)
	static FAICommand WereRatShamanAttackRight(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	
	//向右\左踱步，转向敌人，围绕敌人念念有词
	UFUNCTION(BlueprintCallable)
	static FAICommand WereRatShamanSwear(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	
	//拿杖子敲人
	UFUNCTION(BlueprintCallable)
	static FAICommand WereRatShamanAttackTapping(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	
	//拿杖子捅人
	UFUNCTION(BlueprintCallable)
	static FAICommand WereRatShamanAttackPoking(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	
	//往敌人的位置 移动
	UFUNCTION(BlueprintCallable)
	static FAICommand WereRatShamanMove(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//----------ZDH ADD---------------//

	//往视野内最近的族人移动
	UFUNCTION(BlueprintCallable)
	static FAICommand MoveToClosetViewedAlly(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//给周围的鼠人添加同伴信号
	UFUNCTION(BlueprintCallable)
	static FAICommand AddAllySignalAround(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//给周围的鼠人添加狂热buff
	UFUNCTION(BlueprintCallable)
	static FAICommand AddBerserkBuffAround(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//召唤2只鼠人突击兵
	UFUNCTION(BlueprintCallable)
	static FAICommand SpawnWereRatCommandos(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//萨满的在有敌人在近战时的行动
	UFUNCTION(BlueprintCallable)
	static FAICommand BasicBattle(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	
	//----------ZDH ADD---------------//
	
};
