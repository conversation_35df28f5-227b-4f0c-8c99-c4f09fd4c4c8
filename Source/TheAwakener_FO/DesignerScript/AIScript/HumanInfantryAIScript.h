// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "UObject/NoExportTypes.h"
#include "HumanInfantryAIScript.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UHumanInfantryAIScript : public UObject
{
	GENERATED_BODY()
public:
	//检测是否有刺激源
	UFUNCTION(BlueprintCallable)
	static bool CheckHasStimulate(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	// 向着视野内Params[0]-Params[5]距离范围的一个最近的目标，
	// 随机一个步兵的连招
	// Params[0]-Params[1]为近距离
	// Params[2]-Params[3]为中距离
	// Params[4]-Params[5]为远距离
	UFUNCTION(BlueprintCallable)
	static FAICommand RandomNormalInfantryCombo(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
};
