// Fill out your copyright notice in the Description page of Project Settings.


#include "AIUtils.h"

#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/FunctionLibrary/MathFuncLib.h"

TArray<AAwCharacter*> UAIUtils::CharacterInRange(FVector Center, float Radius, TArray<int> Side, bool In3D, bool IncludeSecondWind, bool IncludeDead)
{
	TArray<AAwCharacter*> Res;
	for (const TTuple<AAwCharacter*, FString> AllCharacter : UGameplayFuncLib::GetAwGameState()->AllCharacters)
	{
		AAwCharacter* Guy = AllCharacter.Key;
		if (
			(Guy->InSecondWind() == false || IncludeSecondWind == true) &&
			(Guy->Dead() == false || IncludeDead == true) &&
			(Side.Num() <= 0 || Side.Contains(Guy->Side)) &&
			(
				(In3D == true && (Guy->GetActorLocation() - Center).SizeSquared() <= Radius*Radius) ||
				(In3D == false && (FVector2D(Guy->GetActorLocation().X, Guy->GetActorLocation().Y) - FVector2D(Center.X, Center.Y)).SizeSquared()<= 
				Radius*Radius)
			)
		){
			Res.Add(Guy);
		}
	}
	return Res;
}

TTuple<TArray<AAwCharacter*>, TArray<AAwCharacter*>, TArray<AAwCharacter*>> UAIUtils::CharacterSplitByCone(FVector Center, float FaceYaw, float Degree, float InteractionLength, TArray<int> Side, bool In3D, bool IncludeSecondWind, bool IncludeDead)
{
	TArray<AAwCharacter*> InRange;
	TArray<AAwCharacter*> InCone;
	TArray<AAwCharacter*> Others;
	Degree = FMath::Clamp(Degree, 0.000f, 180.000f);
	FaceYaw = UMathFuncLib::NormalizeAngle(FaceYaw);
	FaceYaw = (FMath::RoundToInt(FaceYaw * 1000) % 360000) / 1000.000f;

	FVector CurDir = FRotator(0, FaceYaw,0).Quaternion().GetForwardVector();
	UAwGameInstance* GameInstance = Cast<UAwGameInstance>(GWorld->GetGameInstance());
	for (auto CurCharacter : UGameplayFuncLib::GetAwGameState()->AllCharacters)
	{
		if (CurCharacter.Key)
		{
			if ((Side.Num() <= 0 || Side.Contains(CurCharacter.Key->Side)) && !CurCharacter.Key->Dead(true))
			{
				FVector TargetDir = (CurCharacter.Key->GetActorLocation() - Center).GetSafeNormal();
				if (FMath::Abs(FMath::Acos(FVector::DotProduct(CurDir, TargetDir))) < 45)
				{
					if (
						(In3D == true && FMath::Abs((CurCharacter.Key->GetActorLocation() - Center).Size()) <= InteractionLength) ||
						(In3D == false && FMath::Pow(CurCharacter.Key->GetActorLocation().X - Center.X, 2) + FMath::Pow(CurCharacter.Key->GetActorLocation().Y - Center.Y, 2) <= InteractionLength * InteractionLength)
						)
					{
						//完全在范围内
						InRange.Add(CurCharacter.Key);
					}
					else
					{
						//仅仅符合锥形
						InCone.Add(CurCharacter.Key);
					}
				}
				else
				{
					//不在范围内
					Others.Add(CurCharacter.Key);
				}
			}
		}
	}
	return TTuple<TArray<AAwCharacter*>, TArray<AAwCharacter*>, TArray<AAwCharacter*>>(InRange, InCone, Others);
}

TArray<int> UAIUtils::Foe(AAwCharacter* Me, int MaxSide)
{
	TArray<int> Res;
	for (int i = 0; i < FMath::Max(2, MaxSide); i++)
	{
		if (Me->Side != i)
		{
			Res.Add(i);
		}
	}
	return Res;
}