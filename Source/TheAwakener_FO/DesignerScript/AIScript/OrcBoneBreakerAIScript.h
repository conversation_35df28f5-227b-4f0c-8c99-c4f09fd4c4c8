// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "UObject/NoExportTypes.h"
#include "OrcBoneBreakerAIScript.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UOrcBoneBreakerAIScript : public UObject
{
	GENERATED_BODY()
public:
	// 查找场景上有Params[1]Tag的Actor位置，生成Params[0]个
	UFUNCTION(BlueprintCallable)
	static FAICommand CreatePillarByActorTag(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
};
