// Fill out your copyright notice in the Description page of Project Settings.


#include "NPCAIScript.h"

bool UNPCAIScript::CheckNpcArmed(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	return Character->GetArmState() == EArmState::Armed;
}

bool UNPCAIScript::CheckNpcNotArmed(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	return Character->GetArmState() == EArmState::Unarmed;
}

bool UNPCAIScript::CheckNpcInInterruption(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	if (!Character)
	{
		return false;
	}
	UInterruptComponent* InterruptComp = Character->GetInterruptComp();
	if (!InterruptComp)
	{
		return false;
	}

	if (InterruptComp->IsInInterruption())
	{
		return true;
	}
	return false;
}

bool UNPCAIScript::CheckNpcInterruptedInterestHigher(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	if (!Character)
	{
		return false;
	}
	UInterruptComponent* InterruptComp = Character->GetInterruptComp();
	EInterruptedLevel LimitLevel;
	if (!InterruptComp)
	{
		return false;
	}
	if (!Params.IsValidIndex(0))
	{
		LimitLevel = EInterruptedLevel::LowInterested;
	}
	else
	{
		LimitLevel = UDataFuncLib::FStringToEnum<EInterruptedLevel>(Params[0]);
	}
	if (InterruptComp->InterruptedLevel >= LimitLevel)
	{
		return true;
	}
	return false;
}

bool UNPCAIScript::CheckNpcInterruptedInterestLess(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	if (!Character)
	{
		return false;
	}
	UInterruptComponent* InterruptComp = Character->GetInterruptComp();
	EInterruptedLevel LimitLevel;

	if (!InterruptComp)
	{
		return false;
	}

	if (!Params.IsValidIndex(0))
	{
		LimitLevel = EInterruptedLevel::LowInterested;
	}
	else
	{
		LimitLevel = UDataFuncLib::FStringToEnum<EInterruptedLevel>(Params[0]);
	}
	if (InterruptComp->InterruptedLevel < LimitLevel)
	{
		return true;
	}
	return false;
}

bool UNPCAIScript::CheckNpcDoNoCombatIdle(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	bool Res = false;
	if(Character->GetBuff("NPCNoCombat").Num() > 0)
	{
		for (const FBuffObj* Buff : Character->GetBuff("NPCNoCombat"))
			if (Buff->Stack >= 1 && Buff->Param.Contains("IdleAction"))
			{
				if(Buff->Param["IdleAction"] != "")
					Res = true;
			}
	}
	return Res;
}

bool UNPCAIScript::CheckNpcInterruptedInterestEqual(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	if (!Character)
	{
		return false;
	}

	UInterruptComponent* InterruptComp = Character->GetInterruptComp();
	EInterruptedLevel LimitLevel;
	if (!InterruptComp)
	{
		return false;
	}
	if (!Params.IsValidIndex(0))
	{
		LimitLevel = EInterruptedLevel::LowInterested;
	}
	else
	{
		LimitLevel = UDataFuncLib::FStringToEnum<EInterruptedLevel>(Params[0]);
	}
	if (InterruptComp->InterruptedLevel == LimitLevel)
	{
		return true;
	}
	return false;
}

FAICommand UNPCAIScript::DoInterruptedReflaction(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	FAICommand NewCommand = FAICommand();

	if (!Character||Params.Num()<=0)
	{
		return NewCommand;
	}
	UInterruptComponent* InterruptComp = Character->GetInterruptComp();
	if (!InterruptComp)
	{
		return NewCommand;
	}
	if (InterruptComp->InterruptedLevel == EInterruptedLevel::NoInterruption)
	{
		return NewCommand;
	}
	FString CommandParam;
	switch (InterruptComp->InterruptedLevel)
	{
		case EInterruptedLevel::LowInterested:
			{
				CommandParam = *Params.begin();
				NewCommand = AIComponent->CreateUseActionCommand(CommandParam, Character->GetActorForwardVector());
			}
			break;
		case EInterruptedLevel::MidInterested:
			{	
				CommandParam = Params.IsValidIndex(1) ? Params[1] : Params[0];
				NewCommand = AIComponent->CreateUseActionCommand(CommandParam, Character->GetActorForwardVector());
			}
		break;
		case EInterruptedLevel::HighInterested:
			{
				CommandParam = Params.Last();
				NewCommand = AIComponent->CreateUseActionCommand(CommandParam, Character->GetActorForwardVector());
			}
		break;
	}
	return NewCommand;
}

FAICommand UNPCAIScript::DoNoCombatIdle(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	FAICommand NewCommand = FAICommand();
	FString ActionId = "";
	for (FBuffObj* Buff : Character->GetBuff("NPCNoCombat", TArray<AAwCharacter*>()))
	{
		if(Buff->Param.Contains("IdleAction"))
			ActionId = Buff->Param["IdleAction"];
	}
	NewCommand = AIComponent->CreateUseActionCommand(ActionId, Character->GetActorForwardVector());
	return NewCommand;
}

EInterruptedLevel UNPCAIScript::SelectInterruptedLevelTest(AAwCharacter* OtherCharacter, const FHitResult& SweepResult, TArray<FString> Params)
{
	GEngine->AddOnScreenDebugMessage(NULL,5.0f,FColor::Blue,TEXT("Select InterrrputLevel"));
	return EInterruptedLevel::NoInterruption;
}

void UNPCAIScript::CharacterDoPreorderAction(AAwCharacter* Character, TArray<FString> Params)
{
	if(Params.Num() > 0)
		Character->PreorderAction(Params[0]);
}

void UNPCAIScript::AddNoCombatBuff(AAwCharacter* Character, TArray<FString> Params)
{
	FAddBuffInfo NewBuffInfo;
	NewBuffInfo.Model = UGameplayFuncLib::GetAwDataManager()->GetBuffModelById("NPCNoCombat");
	NewBuffInfo.Target = Character;
	NewBuffInfo.Caster = Character;
	NewBuffInfo.AddStack = 1;
	NewBuffInfo.Duration = 9999;
	NewBuffInfo.Infinity = true;
	if (NewBuffInfo.Model.ValidBuffModel())
	{
		FBuffObj* Buff = Character->AddBuff(NewBuffInfo);
		if(Buff && Params.Num())
		{
			Buff->Param.Add("IdleAction",Params[0]);
		}
	}
}

void UNPCAIScript::SetCanNotBeSqueezed(AAwCharacter* Character, TArray<FString> Params)
{
	Character->SetSqueezeType(ESqueezeType::Character_CanNotBeSqueeze);
}
