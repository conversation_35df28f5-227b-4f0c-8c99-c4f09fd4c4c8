// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "TheAwakener_FO/GamePlay/Characters/AI/AwAIComponent.h"
#include "TheAwakener_FO/GamePlay/Characters/HitBox/InterruptComponent.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "NPCAIScript.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UNPCAIScript : public UObject
{
	GENERATED_BODY()
public:
	//Condition

	//NPC是否已拔出武器
	UFUNCTION(BlueprintCallable)
	static bool CheckNpcArmed(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//NPC是否没有拔出武器
	UFUNCTION(BlueprintCallable)
	static bool CheckNpcNotArmed(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//检测NPC 是否被打扰
	UFUNCTION(BlueprintCallable)
	static bool CheckNpcInInterruption(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//检测NPC 当前被打扰行为兴趣是否高于目标level
	UFUNCTION(BlueprintCallable)
	static bool CheckNpcInterruptedInterestHigher(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	//检测NPC 当前被打扰行为兴趣是否等于目标level
	UFUNCTION(BlueprintCallable)
	static bool CheckNpcInterruptedInterestEqual(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	//检测NPC 当前被打扰行为兴趣是否低于目标level
	UFUNCTION(BlueprintCallable)
	static bool CheckNpcInterruptedInterestLess(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//检测NPC 是否有NPCNoCambat的Buff,且Buff里存了NoCombatIdle
	UFUNCTION(BlueprintCallable)
	static bool CheckNpcDoNoCombatIdle(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//Do Action
	UFUNCTION(BlueprintCallable)
	static FAICommand DoInterruptedReflaction(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	UFUNCTION(BlueprintCallable)
	static FAICommand DoNoCombatIdle(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	//


	//Enum Select
	UFUNCTION(BlueprintCallable)
	static EInterruptedLevel SelectInterruptedLevelTest(AAwCharacter* OtherCharacter, const FHitResult& SweepResult, TArray<FString> Params);

	//NPC OnCreate 让NPC在生成后播放PreorderAction
	UFUNCTION(BlueprintCallable)
	static void CharacterDoPreorderAction(AAwCharacter* Character, TArray<FString> Params);

	//NPC OnCreate 给NPC添加NoCombat的buff，Params[0]为NoCambat时的Idle动作ActionId
	UFUNCTION(BlueprintCallable)
	static void AddNoCombatBuff(AAwCharacter* Character, TArray<FString> Params);

	UFUNCTION(BlueprintCallable)
	static void SetCanNotBeSqueezed(AAwCharacter* Character, TArray<FString> Params);
};
