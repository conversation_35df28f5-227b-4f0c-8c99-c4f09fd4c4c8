// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "UObject/Object.h"
#include "AIUtils.generated.h"

/**
 * AI一些通用的东西，偷懒专用
 */
UCLASS()
class THEAWAKENER_FO_API UAIUtils : public UObject
{
	GENERATED_BODY()
public:
	/**
	 * 获得一个范围内的角色(副本内使用，不需要检测AI“看见”或者“听见”)
	 * @param Center 检查的中心
	 * @param Radius 范围（厘米）
	 * @param Side 阵营，如果是空数组，则代表无所谓阵营，否则只会找到符合数组内阵营的角色
	 * @param In3D 是否3D检查，如果不是就做2D检查
	 * @param IncludeSecondWind 濒死角色是否算上
	 * @param IncludeDead 挂了的角色是否算上
	 * @return 所有范围内的角色
	 */
	static TArray<AAwCharacter*> CharacterInRange(FVector Center, float Radius, TArray<int> Side = TArray<int>(), bool In3D = false, bool IncludeSecondWind = true, bool IncludeDead = false);

	/**
	 * 获得一个扇形（锥形）范围内的角色(副本内使用，不需要检测AI“看见”或者“听见”)
	* @param Center 检查的中心
	* @param FaceYaw 面对的角度（世界角度，从GetActorRotation.Yaw获得）
	* @param Degree 扇形左右各多少度，最大按照180度算，总角度就是这个值x2
	 * @param InteractionLength 视野距离（厘米），这并不是一个标准的锥形，首先2D坐标位于视角内，才会判断距离是否足够
	 * @param Side 阵营，如果是空数组，则代表无所谓阵营，否则只会找到符合数组内阵营的角色
	 * @param In3D 是否3D检查，如果不是就做2D检查
	 * @param IncludeSecondWind 濒死角色是否算上
	 * @param IncludeDead 挂了的角色是否算上
	 * @return Get<0>()是所有的在范围内的角色，Get<1>()是位于角度内但没有位于视野范围内的角色，Get<2>()是所有视野外的。返回内容仅包含符合Include和Side条件的角色。
	 */
	static TTuple<TArray<AAwCharacter*>, TArray<AAwCharacter*>, TArray<AAwCharacter*>> CharacterSplitByCone(FVector Center, float FaceYaw, float Degree, float InteractionLength, TArray<int> Side = TArray<int>(), bool In3D = false, bool IncludeSecondWind = true, bool IncludeDead = false);

	/**
	 * 敌人的阵营
	 * @param Me 我是谁
	 * @param MaxSide 最大阵营数
	 * @return 所有敌人阵营
	 */
	static TArray<int> Foe(AAwCharacter* Me, int MaxSide = 3);
};
