// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "TheAwakener_FO/GamePlay/Characters/AI/AwAIComponent.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "Kismet/GameplayStatics.h"
#include "BuddyAIScript.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UBuddyAIScript : public UObject
{
	GENERATED_BODY()

	UFUNCTION(BlueprintCallable)
	static bool Always(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params){return true;}

	
};
