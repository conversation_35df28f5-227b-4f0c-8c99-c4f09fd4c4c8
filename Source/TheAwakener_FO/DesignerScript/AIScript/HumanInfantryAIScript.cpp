// Fill out your copyright notice in the Description page of Project Settings.


#include "HumanInfantryAIScript.h"

#include "TheAwakener_FO/FunctionLibrary/MathFuncLib.h"

bool UHumanInfantryAIScript::CheckHasStimulate(AAwCharacter* Character, UAwAIComponent* AIComponent,
                                               TArray<FString> Params)
{
	return ((AIComponent->GetAllViewedCharacter().Num() > 0 && AIComponent->GetClosestDistanceEnemy(true, false).Character)
		|| AIComponent->Stimuli_Voice.Num() > 0 || AIComponent->Stimuli_Offended.Num() > 0 || AIComponent->Stimuli_AllySignal.Num() > 0);
}

FAICommand UHumanInfantryAIScript::RandomNormalInfantryCombo(AAwCharacter* Character, UAwAIComponent* AIComponent,
	TArray<FString> Params)
{
	FAICommand NewCommand = FAICommand();
	FAIFindChaInfo ClosetTarget = FAIFindChaInfo();

	float CheckMinDisXY = 0;
	float CheckNearMaxDisXY = 0;
	float CheckMidMinDisXY = 0;
	float CheckMidMaxDisXY = 0;
	float CheckFarMinDisXY = 0;
	float CheckMaxDisXY = 0;

	if (Params.Num() > 5)
	{
		CheckMinDisXY = FCString::Atof(*Params[0]);
		CheckNearMaxDisXY = FCString::Atof(*Params[1]);
		CheckMidMinDisXY = FCString::Atof(*Params[2]);
		CheckMidMaxDisXY = FCString::Atof(*Params[3]);
		CheckFarMinDisXY = FCString::Atof(*Params[4]);
		CheckMaxDisXY = FCString::Atof(*Params[5]);
	}
	for (FAIFindChaInfo FindedTarget : AIComponent->GetAllViewedCharacter(false, true))
	{
		if (FindedTarget.Character)
		{
			const float CurDisXY = FindedTarget.DistanceXY;
			const float CurDisZ = FindedTarget.DistanceZ;

			const float CheckDisZ = AIComponent->GetSightZRadius();
			if ((CurDisXY <= CheckMaxDisXY) && (CurDisXY >= CheckMinDisXY) && (CurDisZ < CheckDisZ))
			{
				if (!ClosetTarget.Character)
					ClosetTarget = FindedTarget;
				else if (FindedTarget.DistanceXY < ClosetTarget.DistanceXY)
					ClosetTarget = FindedTarget;
			}
		}
	}
	if (ClosetTarget.Character)
	{
		//检测怪物是否在玩家的正面(如果目标是怪物，则不管正反面)
		bool bInPlayerFront = true;
		if (ClosetTarget.Character->UnderPlayerControl())
		{
			FVector TargetDir = ClosetTarget.Character->GetActorLocation() - Character->GetActorLocation();
			TargetDir.Normalize();
			FVector PlayerCameraDir = ClosetTarget.Character->GetAwCameraComponent()->GetCameraRotate().Vector();
			if (FMath::Abs(UMathFuncLib::GetDegreeBetweenTwoVector(TargetDir, PlayerCameraDir)) < 90)
				bInPlayerFront = false;
		}

		TArray<FString> CanUseComboRange;
		//检测选中的敌人在哪个连招的范围
		//连招1 范围（0-450）
		if (ClosetTarget.DistanceXY <= CheckNearMaxDisXY && ClosetTarget.DistanceXY >= CheckMinDisXY)
		{
			CanUseComboRange.Add("NearCombo");
		}
		//连招2 范围（300-900）
		if (ClosetTarget.DistanceXY <= CheckMidMaxDisXY && ClosetTarget.DistanceXY >= CheckMidMinDisXY)
		{
			CanUseComboRange.Add("MidCombo");
		}
		//连招3 范围（800-检测的最大范围）
		if (ClosetTarget.DistanceXY <= CheckMaxDisXY && ClosetTarget.DistanceXY >= CheckFarMinDisXY)
		{
			CanUseComboRange.Add("FarCombo");
		}
		if (CanUseComboRange.Num())
		{
			const FString UseCombo = CanUseComboRange[FMath::RandRange(0, CanUseComboRange.Num() - 1)];
			if (UseCombo == "NearCombo")
			{
				int UseComboIndex = FMath::RandRange(0, 10);
				if (!bInPlayerFront)
					UseComboIndex = FMath::RandRange(6, 10);
				switch (UseComboIndex)
				{
				case 0:
				{
					AIComponent->AddUseActionTag("InfantryNearCombo0");
					NewCommand = AIComponent->CreateUseActionCommand("NormalAttack_S1", Character->GetActorForwardVector());
					break;
				}
				case 1:
				{
					AIComponent->AddUseActionTag("InfantryNearCombo1");
					NewCommand = AIComponent->CreateUseActionCommand("NormalAttack_S5_Combo", Character->GetActorForwardVector());
					break;
				}
				case 2:
				{
					AIComponent->AddUseActionTag("InfantryNearCombo2");
					NewCommand = AIComponent->CreateUseActionCommand("Dodge_DashStep_Back", Character->GetActorForwardVector());
					break;
				}
				case 3:
				{
					AIComponent->AddUseActionTag("InfantryNearCombo3");
					NewCommand = AIComponent->CreateUseActionCommand("Dodge_DashStep_Right", Character->GetActorForwardVector());
					break;
				}
				case 4:
				{
					AIComponent->AddUseActionTag("InfantryNearCombo4");
					NewCommand = AIComponent->CreateUseActionCommand("Dodge_DashStep_Left", Character->GetActorForwardVector());
					break;
				}
				case 5:
				{
					AIComponent->AddUseActionTag("InfantryNearCombo5");
					NewCommand = AIComponent->CreateUseActionCommand("Dodge_DashStep_Back", Character->GetActorForwardVector());
					break;
				}	
				case 6:
				case 7:
				{
					AIComponent->AddUseActionTag("InfantryNearCombo6");
					NewCommand = AIComponent->CreateUseActionCommand("Dodge_DashStep_Back", Character->GetActorForwardVector());
					break;
				}
				case 8:
				{
					AIComponent->AddUseActionTag("InfantryNearCombo7");
					NewCommand = AIComponent->CreateUseActionCommand("NormalAttack_S1", Character->GetActorForwardVector());
					break;
				}
				case 9:
				case 10:
				{
					NewCommand = AIComponent->CreateUseActionCommand("Walk_Back", Character->GetActorForwardVector());
					break;
				}
				}
			}
			else if (UseCombo == "MidCombo")
			{
				int UseComboIndex = FMath::RandRange(0, 7);
				if (!bInPlayerFront)
					UseComboIndex = FMath::RandRange(5, 7);
				switch (UseComboIndex)
				{
				case 0:
				{
					AIComponent->AddUseActionTag("InfantryMidCombo0");
					NewCommand = AIComponent->CreateUseActionCommand("Dodge_DashStep_Front", Character->GetActorForwardVector());
					break;
				}
				case 1:
				{
					AIComponent->AddUseActionTag("InfantryMidCombo1");
					NewCommand = AIComponent->CreateUseActionCommand("NormalAttack_M1", Character->GetActorForwardVector());
					break;
				}
				case 2:
				{
					AIComponent->AddUseActionTag("InfantryMidCombo2");
					NewCommand = AIComponent->CreateUseActionCommand("NormalAttack_M2", Character->GetActorForwardVector());
					break;
				}
				case 3:
				{
					AIComponent->AddUseActionTag("InfantryMidCombo3");
					NewCommand = AIComponent->CreateUseActionCommand("NormalAttack_M3", Character->GetActorForwardVector());
					break;
				}
				case 4:
				{
					AIComponent->AddUseActionTag("InfantryMidCombo4");
					NewCommand = AIComponent->CreateUseActionCommand("Dodge_DashStep_Back", Character->GetActorForwardVector());
					break;
				}
				case 5:
				{
					AIComponent->AddUseActionTag("InfantryMidCombo5");
					NewCommand = AIComponent->CreateUseActionCommand("NormalAttack_M1", Character->GetActorForwardVector());
					break;
				}
				case 6:
				case 7:
				{
					NewCommand = AIComponent->CreateUseActionCommand("Walk_Back", Character->GetActorForwardVector());
					break;
				}
				}
			}
			else if (UseCombo == "FarCombo")
			{
				int UseComboIndex = FMath::RandRange(0, 6);
				if (!bInPlayerFront)
					UseComboIndex = FMath::RandRange(3, 6);
				switch (UseComboIndex)
				{
				case 0:
				{
					AIComponent->AddUseActionTag("InfantryFarCombo0");
					NewCommand = AIComponent->CreateUseActionCommand("NormalAttack_L1", Character->GetActorForwardVector());
					break;
				}
				case 1:
				{
					AIComponent->AddUseActionTag("InfantryFarCombo1");
					NewCommand = AIComponent->CreateUseActionCommand("Dodge_DashStep_Front", Character->GetActorForwardVector());
					break;
				}
				case 2:
				{
					AIComponent->AddUseActionTag("InfantryFarCombo2");
					NewCommand = AIComponent->CreateUseActionCommand("Walk_Front", Character->GetActorForwardVector());
					break;
				}
				case 3:
				case 4:
				{
					AIComponent->AddUseActionTag("InfantryFarCombo3");
					NewCommand = AIComponent->CreateUseActionCommand("Dodge_DashStep_Front", Character->GetActorForwardVector());
					break;
				}
				case 5:
				case 6:
				{
					NewCommand = AIComponent->CreateUseActionCommand("Walk_Front", Character->GetActorForwardVector());
					break;
				}
				}
			}
			AIComponent->SetTargetEnemy(ClosetTarget);
			AIComponent->TargetDirFunc = UDataFuncLib::SplitFuncNameAndParams("MobAIScript.GetCurTargetEnemyDir()");
		}
	}
	return NewCommand;
}
