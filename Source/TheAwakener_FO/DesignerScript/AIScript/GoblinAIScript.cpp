// Fill out your copyright notice in the Description page of Project Settings.


#include "GoblinAIScript.h"

#include "Kismet/KismetMathLibrary.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/FunctionLibrary/MathFuncLib.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"

bool UGoblinAIScript::CheckViewedDeathEnemy(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	const TArray<FAIFindChaInfo> FindedTargets = AIComponent->GetAllViewedCharacter(false, true, true);
	for (FAIFindChaInfo FindedTarget : FindedTargets)
	{
		if(FindedTarget.Character->Dead(true))
			return true;
	}
	return false;
}

bool UGoblinAIScript::CheckJustLeaveBattle(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	if(AIComponent->JustClearStimulus)
		return true;
	return false;
}

bool UGoblinAIScript::CheckCanTurn(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	for (FAIFindChaInfo FindedTarget : AIComponent->GetAllViewedCharacter())
	{
		if (FindedTarget.Character)
		{
			const float CurDisXY = FindedTarget.DistanceXY;
			const float CurDisZ = FindedTarget.DistanceZ;
			float CheckMaxDisXY;
			float CheckMinDisXY = 0;
			if (Params.Num() > 1)
			{
				CheckMinDisXY = FCString::Atof(*Params[0]);
				CheckMaxDisXY = FCString::Atof(*Params[1]);
			}
			else
			{
				CheckMaxDisXY = AIComponent->StopRangeWhenMoveFollow;
			}	
			const float CheckDisZ = AIComponent->StopZDisWhenMoveFollow;
			const bool TargetInDis = (CurDisXY <= CheckMaxDisXY) && (CurDisXY >= CheckMinDisXY) && (CurDisZ < CheckDisZ);
			if(TargetInDis)
			{
				if(Params.Num() > 3)
				{
					const float CheckMinDegree = FCString::Atof(*Params[2]);
					const float CheckMaxDegree = FCString::Atof(*Params[3]);
					FVector TargetDir = FindedTarget.Character->GetActorLocation() - Character->GetActorLocation();
					TargetDir.Normalize();
					const float CurDegree = UKismetMathLibrary::DegAcos(FVector::DotProduct(Character->GetActorForwardVector(), TargetDir));
					return (CurDegree < CheckMinDegree || CurDegree > CheckMaxDegree);
				}
			}
		}
	}
	return false;
}

FAICommand UGoblinAIScript::ClearGoblinRoaredBuff(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	FBuffModel BuffModel = UGameplayFuncLib::GetDataManager()->GetBuffModelById("Goblin_HasRoared");
	if(BuffModel.Id != "")
	{
		FAddBuffInfo BuffInfo = FAddBuffInfo(Character, Character, BuffModel, -1, 5, false,true);
		Character->AddBuff(BuffInfo);
	}
	return FAICommand();
}

FAICommand UGoblinAIScript::GoblinRoarWhenStartBattle(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	FAICommand NewCommand = FAICommand();
	if(Params.Num() > 2)
	{
		const float CheckDis = FCString::Atof(*Params[0]);
		FAIFindChaInfo ClosetTarget = FAIFindChaInfo();
		TArray<FAIFindChaInfo> FindedTargets = AIComponent->GetAllViewedCharacter(false, true);
		for (FAIFindChaInfo FindedTarget : FindedTargets)
		{
			if(!ClosetTarget.Character)
				ClosetTarget = FindedTarget;
			else if(FindedTarget.DistanceXY < ClosetTarget.DistanceXY)
				ClosetTarget = FindedTarget;
		}
		if(ClosetTarget.Character)
		{
			AIComponent->SetTargetEnemy(ClosetTarget);
			AIComponent->TargetDirFunc = UDataFuncLib::SplitFuncNameAndParams("MobAIScript.GetCurTargetEnemyDir()");
			AIComponent->ClearUseActionTags();
			if(ClosetTarget.DistanceXY > CheckDis)
			{
				NewCommand = AIComponent->CreateUseActionCommand(Params[1], Character->GetActorForwardVector());
			}
			else
			{
				AIComponent->AddUseActionTag("StartBattleNearRoarCombo");
				NewCommand = AIComponent->CreateUseActionCommand(Params[2], Character->GetActorForwardVector());
			}
				
			AIComponent->SetHalfSightDegree(180);
			AIComponent->SetLoseHalfSightDegree(180);
		}
	}
	return NewCommand;
}

FAICommand UGoblinAIScript::AttackNearViewedEnemy(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	FAICommand NewCommand = FAICommand();
	FAIFindChaInfo ClosetTarget = FAIFindChaInfo();
	TArray<FAIFindChaInfo> FindedTargets = AIComponent->GetAllViewedCharacter(false, true);
	for (FAIFindChaInfo FindedTarget : FindedTargets)
	{
		if (FindedTarget.Character)
		{
			const float CurDisXY = FindedTarget.DistanceXY;
			const float CurDisZ = FindedTarget.DistanceZ;
			float CheckMaxDisXY = 0;
			float CheckMinDisXY = 0;
			if(Params.Num() > 1)
			{
				CheckMaxDisXY = FCString::Atof(*Params[1]);
				CheckMinDisXY = FCString::Atof(*Params[0]);
			}
			const float CheckDisZ = AIComponent->StopZDisWhenMoveFollow;
			if((CurDisXY <= CheckMaxDisXY) && (CurDisXY >= CheckMinDisXY) && (CurDisZ < CheckDisZ))
			{
				if(Params.Num() > 3)
				{
					const float CheckMinDegree = FCString::Atof(*Params[2]);
					const float CheckMaxDegree = FCString::Atof(*Params[3]);
					FVector TargetDir = FindedTarget.Character->GetActorLocation() - Character->GetActorLocation();
					TargetDir.Normalize();
					const float CurDegree = UMathFuncLib::GetDegreeBetweenTwoVector(Character->GetActorForwardVector(), TargetDir);
					if(CurDegree >= CheckMinDegree && CurDegree <= CheckMaxDegree)
					{
						if(!ClosetTarget.Character)
							ClosetTarget = FindedTarget;
						else if(FindedTarget.DistanceXY < ClosetTarget.DistanceXY)
							ClosetTarget = FindedTarget;
					}
				}
			}
		}
	}
	if(ClosetTarget.Character)
	{
		AIComponent->SetTargetEnemy(ClosetTarget);
		AIComponent->TargetDirFunc = UDataFuncLib::SplitFuncNameAndParams("MobAIScript.GetCurTargetEnemyDir()");
		AIComponent->ClearUseActionTags();
		AIComponent->AddUseActionTag("AttackNearCombo");
		if(FMath::RandBool())
			NewCommand = AIComponent->CreateUseActionCommand(Params[4], Character->GetActorForwardVector());
		else
			NewCommand = AIComponent->CreateUseActionCommand(Params[5], Character->GetActorForwardVector());
	}
	return NewCommand;
}

FAICommand UGoblinAIScript::AttackClosetNoViewedEnemy(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	FAICommand NewCommand = FAICommand();
	FAIFindChaInfo ClosetTarget = FAIFindChaInfo();
	for (TTuple<AAwCharacter*, FString> FindCharacter : UGameplayFuncLib::GetAwGameState()->AllCharacters)
	{
		if(FindCharacter.Key == nullptr) continue;
		if(FindCharacter.Key != Character && !FindCharacter.Key->Dead(true) && Character->IsEnemy(FindCharacter.Key))
		{
			const float CurDisXY = FVector::Dist2D(FindCharacter.Key->GetActorLocation(), Character->GetActorLocation());
			const float CurDisZ = FMath::Abs(FindCharacter.Key->GetActorLocation().Z - Character->GetActorLocation().Z);
			float CheckMaxDisXY = 0;
			float CheckMinDisXY = 0;
			if(Params.Num() > 1)
			{
				CheckMaxDisXY = FCString::Atof(*Params[1]);
				CheckMinDisXY = FCString::Atof(*Params[0]);
			}
			const float CheckDisZ = AIComponent->StopZDisWhenMoveFollow;
			if((CurDisXY < CheckMaxDisXY) && (CurDisXY >= CheckMinDisXY) && (CurDisZ < CheckDisZ))
			{
				const float CurDis = FVector::Dist2D(FindCharacter.Key->GetActorLocation(), Character->GetActorLocation());
				if(!ClosetTarget.Character)
				{
					ClosetTarget.Character = FindCharacter.Key;
					ClosetTarget.DistanceXY = CurDis;
				}
				else if(ClosetTarget.DistanceXY > CurDis)
				{
					ClosetTarget.Character = FindCharacter.Key;
					ClosetTarget.DistanceXY = CurDis;
				}
			}
		}
	}
	if(ClosetTarget.Character)
	{
		AIComponent->ClearUseActionTags();
		AIComponent->SetTargetEnemy(ClosetTarget);
		AIComponent->TargetDirFunc = UDataFuncLib::SplitFuncNameAndParams("MobAIScript.GetCurTargetEnemyDir()");
		FVector TargetDir = ClosetTarget.Character->GetActorLocation() - Character->GetActorLocation();
		TargetDir.Normalize();
		const float TargetDregee = UMathFuncLib::GetDegreeBetweenTwoVector(Character->GetActorForwardVector(),TargetDir);
		if(TargetDregee > 0)
			NewCommand = AIComponent->CreateUseActionCommand(Params[2], Character->GetActorForwardVector());
		else
			NewCommand = AIComponent->CreateUseActionCommand(Params[3], Character->GetActorForwardVector());
	}
	return NewCommand;
}

FAICommand UGoblinAIScript::ComboDaze01(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	AIComponent->ClearUseActionTags();
	AIComponent->AddUseActionTag("ComboDaze");
	return FAICommand();
}
