// Fill out your copyright notice in the Description page of Project Settings.


#include "OgreAIScript.h"

#include "Kismet/KismetMathLibrary.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/FunctionLibrary/MathFuncLib.h"

//如果玩家方全败（房间内的玩家方）就返回True
bool UOgreAIScript::AllPlayerDown(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	return UOgreAIScript::PlayerTeamDown();
}

//是否在柱子范围内且没有抱着柱子
bool UOgreAIScript::CheckNearPillar(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	if (!AIComponent->Params.Contains("BestPillar")) return false;

	const FString BestPillarUID = AIComponent->Params["BestPillar"];
	for (AAWAoe* & CurAOE : UOgreAIScript::GetPillarsInArea())
	{
		if (CurAOE && FString::FromInt(CurAOE->GetUniqueID()) == BestPillarUID && 
			FVector::Dist(CurAOE->GetActorLocation(), Character->GetActorLocation()) <= 400)
		{
			return true;
		}
	}
	return false;
}

//场上是否存在柱子且没有抱着柱子
bool UOgreAIScript::CheckHasPillarInArea(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	float CheckDis = 500;
	if(Params.Num())
		CheckDis = FCString::Atof(*Params[0]);
	for (AAWAoe* & CurAOE : UOgreAIScript::GetPillarsInArea())
	{
		if (CurAOE)
		{
			if (FVector::Dist2D(Character->GetActorLocation(), CurAOE->GetActorLocation()) <= CheckDis)
			{
				return true;
			}
		}
	}
	return false;
}

bool UOgreAIScript::CheckWillingToTakePillar(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	//只关心多少距离内的柱子
	const float CheckDis = Params.Num() > 0 ? FCString::Atof(*Params[0]) : 1000;
	//玩家距离和柱子距离比多少的时候才考虑柱子
	const float PlayerDisRate = Params.Num() > 1 ? FCString::Atof(*Params[1]) : 1.2f;

	AAWAoe* BestPillar = nullptr;
	float BestDistance = CheckDis;
	for (AAWAoe* ThisPillar : UOgreAIScript::GetPillarsInArea())
	{
		if (ThisPillar)
		{
			const float ThisPillarDis = FVector::Dist2D(Character->GetActorLocation(), ThisPillar->GetActorLocation());
			if (ThisPillarDis <= BestDistance)
			{
				BestPillar = ThisPillar;
				BestDistance = ThisPillarDis;
			}
		}
	}

	if (!BestPillar) return false;	//没有柱子抱个屁

	UKismetSystemLibrary::PrintString(Character, FString("Best Pillar Found"));
	AIComponent->Params.Add("BestPillar", FString::FromInt(BestPillar->GetUniqueID()));

	//检查柱子和玩家角色的关系，也就是当玩家和我的距离>=我跟柱子距离的n倍的时候才会想抱柱子
	const AAwCharacter* PlayerCha = UGameplayFuncLib::GetAwGameState()->GetMyCharacter();
	if (!PlayerCha) return true;

	const float PlayerDis = FVector::Dist2D(Character->GetActorLocation(), PlayerCha->GetActorLocation());
	return (PlayerDis / PlayerDisRate >= BestDistance);
}

	//如果疲劳状态(FightingWill.Level==1)并且块结束了（FightingWill.Value<=300）
bool UOgreAIScript::CheckNoPillarInArea(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	const int MaxPillar = Params.Num() > 0 ? FCString::Atoi(*Params[0]) : 0;
	int PillarFound = 0;
	for (auto& Aoe : UGameplayFuncLib::GetAwGameState()->AOEList)
	{
		if (Aoe.Key)
		{
			if (Aoe.Key->Model.Id == "Pillar" && !Aoe.Key->IsActorBeingDestroyed())
			{
				PillarFound += 1;
				if (PillarFound >= MaxPillar) return false;
			}
		}
	}
	return true;
}

bool UOgreAIScript::CheckViewedEnemyHeight(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	TArray<FAIFindChaInfo> FindedCharacters = AIComponent->GetAllViewedCharacter(true);
	if(FindedCharacters.Num())
	{
		float CheckHeight = 250;
		if(Params.Num())
			CheckHeight = FCString::Atof(*Params[0]);
		for (FAIFindChaInfo FindedCharacter : FindedCharacters)
		{
			if(FindedCharacter.Character->GetActorLocation().Z - Character->GetActorLocation().Z > CheckHeight)
				return true;
		}
	}
	return false;
}


FAICommand UOgreAIScript::PullUpPillar(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	FAICommand NewCommand = FAICommand();
	if (Params.Num())
	{
		TArray<FString> Tags;
		TArray<FString> DirParams;
		const FVector2D Dir = DirectToClosetPillar(Character, Tags, DirParams);
		NewCommand = AIComponent->CreateUseActionCommand(Params[0], FVector(Dir, 0));
		AIComponent->TargetDirFunc = UDataFuncLib::SplitFuncNameAndParams("OgreAIScript.DirectToClosetPillar()");
	}
	
	return NewCommand;
}

FVector2D UOgreAIScript::DirectToClosetPillar(AAwCharacter* AICha, TArray<FString> Tags, TArray<FString> Params)
{
	int MinDis = 0;
	FVector NearestLoc = FVector();
	const FString BestPillarUID = AICha->GetAIComponent()->Params.Contains("BestPillar") ? AICha->GetAIComponent()->Params["BestPillar"] : FString();
	for (AAWAoe* CurAOE : UOgreAIScript::GetPillarsInArea())
	{
		if (BestPillarUID.IsEmpty())
		{
			if (MinDis > 0)
			{
				int CurDis = FVector::DistSquared(AICha->GetActorLocation(), CurAOE->GetActorLocation());
				if (CurDis < MinDis)
				{
					MinDis = CurDis;
					NearestLoc = CurAOE->GetActorLocation();
				}
			}
			else
			{
				MinDis = FVector::DistSquared(AICha->GetActorLocation(), CurAOE->GetActorLocation());
				NearestLoc = CurAOE->GetActorLocation();
			}
		}else
		{
			if (FString::FromInt(CurAOE->GetUniqueID()) == BestPillarUID)
			{
				NearestLoc = CurAOE->GetActorLocation();
				AICha->GetAIComponent()->Params.Add("PullingPillarObj", CurAOE->Param.Contains("PillarObj") ? CurAOE->Param["PillarObj"] : "");
				break;
			}
		}
		
	}
	FVector2D Dir = FVector2D(NearestLoc - AICha->GetActorLocation());
	Dir.Normalize();
	return Dir;
}

//走到最近的柱子
FAICommand UOgreAIScript::MoveToNearestPillar(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	TArray<FVector> LocList;
	int MinDis = 0;
	FVector NearestLoc = Character->GetActorLocation();
	const FString BestPillarUID = AIComponent->Params.Contains("BestPillar") ? AIComponent->Params["BestPillar"] : FString();
	for (AAWAoe* CurAOE : UOgreAIScript::GetPillarsInArea())
	{
		if (BestPillarUID.IsEmpty())
		{
			if (MinDis > 0)
			{
				int CurDis = FVector::DistSquared(Character->GetActorLocation(), CurAOE->GetActorLocation());
				if (CurDis < MinDis)
				{
					MinDis = CurDis;
					NearestLoc = CurAOE->GetActorLocation();
				}	
			}
			else
			{
				MinDis = FVector::DistSquared(Character->GetActorLocation(), CurAOE->GetActorLocation());
				NearestLoc = CurAOE->GetActorLocation();
			}
		}else
		{
			if (FString::FromInt(CurAOE->GetUniqueID()) == BestPillarUID)
			{
				NearestLoc = CurAOE->GetActorLocation();
				UKismetSystemLibrary::PrintString(CurAOE, FString("Has Parent ").Append(CurAOE->GetParentActor() ? CurAOE->GetParentActor()->GetName() : "no father"));
				break;
			}
		}
	}
	LocList.Add(NearestLoc);
	FAICommand NewCommand = AIComponent->CreateMoveToCommand(LocList, 0);
	return NewCommand;
}

FAICommand UOgreAIScript::AttackMiddleViewdEnemy(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	FAICommand NewCommand = FAICommand();
	FAIFindChaInfo ClosetTarget = FAIFindChaInfo();
	TArray<FAIFindChaInfo> FindedTargets = AIComponent->GetAllViewedCharacter(false, true);
	for (FAIFindChaInfo FindedTarget : FindedTargets)
	{
		if (FindedTarget.Character)
		{
			const float CurDisXY = FindedTarget.DistanceXY;
			const float CurDisZ = FindedTarget.DistanceZ;
			float CheckMaxDisXY = 0;
			float CheckMinDisXY = 0;
			if(Params.Num() > 1)
			{
				CheckMaxDisXY = FCString::Atof(*Params[1]);
				CheckMinDisXY = FCString::Atof(*Params[0]);
			}
			
			const float CheckDisZ = AIComponent->StopZDisWhenMoveFollow;
			if((CurDisXY <= CheckMaxDisXY) && (CurDisXY >= CheckMinDisXY) && (CurDisZ < CheckDisZ))
			{
				if(!ClosetTarget.Character)
					ClosetTarget = FindedTarget;
				else if(FindedTarget.DistanceXY < ClosetTarget.DistanceXY)
					ClosetTarget = FindedTarget;
			}
		}
	}
	if(ClosetTarget.Character)
	{
		AIComponent->SetTargetEnemy(ClosetTarget);
		AIComponent->TargetDirFunc = UDataFuncLib::SplitFuncNameAndParams("MobAIScript.GetCurTargetEnemyDir()");
		if(FMath::RandBool())
			NewCommand = AIComponent->CreateUseActionCommand(Params[2], Character->GetActorForwardVector());
		else
			NewCommand = AIComponent->CreateUseActionCommand(Params[3], Character->GetActorForwardVector());
	}
	return NewCommand;
}

FAICommand UOgreAIScript::AttackHigherEnemy(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	FAICommand NewCommand = FAICommand();
	float CheckHeight = 250;
	if(Params.Num())
		CheckHeight = FCString::Atof(*Params[0]);
	TArray<FAIFindChaInfo> HigherEnemyList;
	for (FAIFindChaInfo FindedCharacter : AIComponent->GetAllViewedCharacter(true))
	{
		if(FindedCharacter.Character->GetActorLocation().Z - Character->GetActorLocation().Z > CheckHeight)
			HigherEnemyList.Add(FindedCharacter);
	}
	if(HigherEnemyList.Num())
	{
		HigherEnemyList.Sort([](FAIFindChaInfo A, FAIFindChaInfo B){
		return A.DistanceXY < B.DistanceXY;
		});
		
		NewCommand = AIComponent->CreateUseActionCommand(Params[1], Character->GetActorForwardVector());
		AIComponent->SetTargetEnemy(HigherEnemyList[0]);
		AIComponent->TargetDirFunc = UDataFuncLib::SplitFuncNameAndParams("MobAIScript.GetCurTargetEnemyDir()");
	}
	
	return NewCommand;
}

FAICommand UOgreAIScript::AttackClosetNoViewedEnemy(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	FAICommand NewCommand = FAICommand();
	FAIFindChaInfo ClosetTarget = FAIFindChaInfo();
	for (TTuple<AAwCharacter*, FString> FindCharacter : UGameplayFuncLib::GetAwGameState()->AllCharacters)
	{
		if(FindCharacter.Key == nullptr) continue;
		if(FindCharacter.Key != Character && !FindCharacter.Key->Dead(true) && Character->IsEnemy(FindCharacter.Key))
		{
			const float CurDisXY = FVector::Dist2D(FindCharacter.Key->GetActorLocation(), Character->GetActorLocation());
			const float CurDisZ = FMath::Abs(FindCharacter.Key->GetActorLocation().Z - Character->GetActorLocation().Z);
			float CheckMaxDisXY = 0;
			float CheckMinDisXY = 0;
			if(Params.Num() > 1)
			{
				CheckMaxDisXY = FCString::Atof(*Params[1]);
				CheckMinDisXY = FCString::Atof(*Params[0]);
			}
			const float CheckDisZ = AIComponent->StopZDisWhenMoveFollow;
			if((CurDisXY < CheckMaxDisXY) && (CurDisXY >= CheckMinDisXY) && (CurDisZ < CheckDisZ))
			{
				const float CurDis = FVector::Dist2D(FindCharacter.Key->GetActorLocation(), Character->GetActorLocation());
				if(!ClosetTarget.Character)
				{
					ClosetTarget.Character = FindCharacter.Key;
					ClosetTarget.DistanceXY = CurDis;
				}
				else if(ClosetTarget.DistanceXY > CurDis)
				{
					ClosetTarget.Character = FindCharacter.Key;
					ClosetTarget.DistanceXY = CurDis;
				}
			}
		}
	}
	if(ClosetTarget.Character)
	{
		AIComponent->SetTargetEnemy(ClosetTarget);
		AIComponent->TargetDirFunc = UDataFuncLib::SplitFuncNameAndParams("MobAIScript.GetCurTargetEnemyDir()");
		FVector TargetDir = ClosetTarget.Character->GetActorLocation() - Character->GetActorLocation();
		TargetDir.Normalize();
		const float TargetDregee = UMathFuncLib::GetDegreeBetweenTwoVector(Character->GetActorForwardVector(),TargetDir);
		if(TargetDregee > 0)
			NewCommand = AIComponent->CreateUseActionCommand(Params[2], Character->GetActorForwardVector());
		else
			NewCommand = AIComponent->CreateUseActionCommand(Params[3], Character->GetActorForwardVector());
	}
	return NewCommand;
}

UTimelineNode* UOgreAIScript::PillarBroken(AAwCharacter* Character, FChaPart Part, bool Broken, TArray<FString> Params)
{
	UKismetSystemLibrary::PrintString(Character, FString("Pillar Broken"));
	Character->PreorderActionByMontageState(ECharacterMontageState::Hurt);
	FBuffModel BuffModel = UGameplayFuncLib::GetAwDataManager()->GetBuffModelById("OgreWithPillar");
	Character->AddBuff(FAddBuffInfo(Character, Character, BuffModel, BuffModel.MaxStack * -1, 0, true, false));
	return nullptr;
}

FAICommand UOgreAIScript::CreateQueuePillars(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	const int Index = Params.Num() > 0 ? FCString::Atoi(*Params[0]) : 0;
	FVector CurPoint = Character->GetActorLocation() + Character->GetActorForwardVector() * Index * 450;
	CurPoint.Z = Character->GetActorLocation().Z - 220;
	
	UGameplayFuncLib::CreateAOE(Character, "FallingPillar", CurPoint, FVector(1, 0, 0), 8, "");
	return FAICommand();
}

FAICommand UOgreAIScript::CreateRandomPillars(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	FVector CurPoint = FVector::ZeroVector;
	if (UGameplayFuncLib::GetAwGameState()->GetMyCharacter()) CurPoint = UGameplayFuncLib::GetAwGameState()->GetMyCharacter()->GetCharacterGroundLoc();
	UGameplayFuncLib::CreateAOE(Character, "FallingPillar", CurPoint, FVector(1, 0, 0), 8, "");
	// TArray<FVector> SpawnPoints;
	// for (FMapSetPoint CurPoint : UGameplayFuncLib::GetAwGameState()->MapPointList)
	// {
	// 	if(CurPoint.Id.Contains("PillarSpawnPoint"))
	// 		SpawnPoints.Add(CurPoint.Location);
	// }
	// if (SpawnPoints.Num())
	// {
	// 	int SpawnedAOE = 0;
	// 	for (FVector CurPoint : SpawnPoints)
	// 	{
	// 		if (FMath::RandBool())
	// 		{
	// 			UGameplayFuncLib::CreateAOE(Character, "FallingPillar", CurPoint, FVector(1, 0, 0), 8, "");
	// 			SpawnedAOE++;
	// 		}
	// 	}
	// 	if (SpawnedAOE == 0)
	// 	{
	// 		FVector SpawnPoint = SpawnPoints[FMath::RandRange(0, SpawnPoints.Num() - 1)];
	// 		UGameplayFuncLib::CreateAOE(Character, "FallingPillar", SpawnPoint, FVector(1, 0, 0), 8, "");
	// 	}
	// }
	return FAICommand();
}

FAICommand UOgreAIScript::TestShowState(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	if (Params.Num() <= 0) return FAICommand();

	UKismetSystemLibrary::PrintString(Character, FString("[Print]").Append(Params[0]));

	return FAICommand();
}

FString UOgreAIScript::ComboAttack_SummonPillar(AAwCharacter* AIGuy, TArray<FString> StateTags, TArray<FString> Params)
{
	int ActionIndex = 0;
	if (StateTags.Contains("PillarSummon1") == true)
	{
		ActionIndex = 2;
		TArray<FString> Tags;
		Tags.Add("PillarSummon2");
		Tags.Remove("PillarSummon1");
		AIGuy->SetUseActionTags(Tags);
	}else if (StateTags.Contains("PillarSummon2") == true)
	{
		
		ActionIndex = 0;
		AIGuy->SetUseActionTags(TArray<FString>());
	}else
	{
		ActionIndex = 1;
		TArray<FString> Tags;
		Tags.Add("PillarSummon1");
		AIGuy->SetUseActionTags(Tags);
	}

	TArray<FString> ToUseActionId;
	ToUseActionId.Add("CallPillar");
	ToUseActionId.Add("CallPillar1");
	ToUseActionId.Add("CallPillar2");

	return ActionIndex <= 0 ? "" : ToUseActionId[ActionIndex];
}

FAICommand UOgreAIScript::OgreBasicBattle(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	//当前基本条件
	const int FWLevel = Character->FightingWill.Level ;
	const TArray<FActionInfo*> CanDoActions = Character->GetCurrentCancellableActions();
	const float DistanceToPlayer = FVector::Dist2D(Character->GetActorLocation(), UGameplayFuncLib::GetAwGameState()->GetMyCharacter()->GetActorLocation());
	FVector TargetDir = Character->GetActorLocation() - UGameplayFuncLib::GetAwGameState()->GetMyCharacter()->GetActorLocation();
	TargetDir.Normalize();
	const bool TargetBehindMe = FMath::Abs(UMathFuncLib::GetDegreeBetweenTwoVector(Character->GetActorForwardVector(), TargetDir)) < 90;

	UKismetSystemLibrary::PrintString(Character, FString("Distance To player ").Append(FString::SanitizeFloat(DistanceToPlayer)),
		true, true, FLinearColor::Yellow, 30);
	
	//策划输入数据
	FString MobID = "Ogre";
	if(Params.Num())
		MobID = Params[0];
	FAIPickActionInfo AIActionValue = UGameplayFuncLib::GetAwDataManager()->GetAIPickActionInfo(MobID);

	float RestRate =
		AIActionValue.RestActions.Num() > 0 &&AIActionValue.RestRate.Num() > FWLevel ?
		AIActionValue.RestRate[FWLevel] : 0;

	int RestStack = 0;
	TArray<FBuffObj*> RestBuff = Character->GetBuff("OgreRested",TArray<AAwCharacter*>());
	if(RestBuff.Num())
	{
		for (FBuffObj* Buff : RestBuff)
		{
			RestStack += Buff->Stack;
			break;
		}
	}
	RestRate = RestRate / (RestStack + 1);
	
	//逻辑开始
	FAICommand Cmd = FAICommand();
	FAIFindChaInfo Found = FAIFindChaInfo();
	Found.Character = UGameplayFuncLib::GetAwGameState()->GetMyCharacter();
	AIComponent->SetTargetEnemy(Found);
	AIComponent->TargetDirFunc = UDataFuncLib::SplitFuncNameAndParams("MobAIScript.GetCurTargetEnemyDir()");

	float TotalRate = RestRate;
	TArray<FAIPickActionCandidate> NowMayActions;
	for (FAIPickActionData Act : AIActionValue.Actions) 
	{
		if (Act.ActionId == AIComponent->AILastActionId) continue;
		const float ThisRate = Act.PickRate[FWLevel] * (TargetBehindMe ? Act.EnemyInBackRate : Act.EnemyInFrontRate);
		if (
			DistanceToPlayer >= Act.MinRange[FWLevel] &&
			DistanceToPlayer <= Act.MaxRange[FWLevel] &&
			ThisRate > 0
		)
		{
			TotalRate += ThisRate;
			NowMayActions.Add(FAIPickActionCandidate(Act.ActionId, ThisRate, false));
		}
	}

	float RanRes = FMath::RandRange(0.f, TotalRate);
	int Index = 0;
	while (Index < NowMayActions.Num() && RanRes > NowMayActions[Index].Rate)
	{
		RanRes -= NowMayActions[Index].Rate;
		Index += 1;
	}
	if (NowMayActions.Num() > Index)
	{
		Cmd = AIComponent->CreateUseActionCommand(NowMayActions[Index].ActionId, Character->GetActorForwardVector());
	}else
	{
		TotalRate = 0;
		NowMayActions.Empty();
		for (FAIPickActionData Act : AIActionValue.RestActions) 
		{
			const float ThisRate = Act.PickRate[FWLevel] * (TargetBehindMe ? Act.EnemyInBackRate : Act.EnemyInFrontRate);
			if (
				DistanceToPlayer >= Act.MinRange[FWLevel] &&
				DistanceToPlayer <= Act.MaxRange[FWLevel] &&
				ThisRate > 0
			)
			{
				TotalRate += ThisRate;
				NowMayActions.Add(FAIPickActionCandidate(Act.ActionId, ThisRate, false));
			}
		}
		RanRes = FMath::RandRange(0.f, TotalRate);
		Index = 0;
		while (Index < NowMayActions.Num() && RanRes > NowMayActions[Index].Rate)
		{
			RanRes -= NowMayActions[Index].Rate;
			Index += 1;
		}
		if (NowMayActions.Num() > Index)
		{
			Cmd = AIComponent->CreateUseActionCommand(NowMayActions[Index].ActionId, Character->GetActorForwardVector());
		}else
			UKismetSystemLibrary::PrintString(Character, Character->GetName().Append(FString(" NoAction To Use ")), true, true, FLinearColor::Red, 10);
	}
	
	
	return Cmd;
}