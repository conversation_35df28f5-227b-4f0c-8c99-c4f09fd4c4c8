// Fill out your copyright notice in the Description page of Project Settings.


#include "WereRatCommandoAIScript.h"

#include "Kismet/KismetMathLibrary.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GamePlay/Buff/BuffManager.h"

bool UWereRatCommandoAIScript::CheckIsNearMinePoint(AAwCharacter* Character, UAwAIComponent* AIComponent,
                                                    TArray<FString> Params)
{
	if (Character->GetBuff("DoMine").Num() <= 0 ||
		AIComponent->PathNodeQueue.Nodes.Num() <= 0)
		return false;

	float MinDis = 50;

	if (Params.Num() > 0)
		MinDis = FCString::Atof(*Params[0]);

	if (FVector2D::DistSquared(
		FVector2D(Character->GetActorLocation()),
		FVector2D(AIComponent->PathNodeQueue.Nodes[0])
		) <= MinDis*MinDis)
		return true;
	
	return false;
}

FAICommand UWereRatCommandoAIScript::MoveToNearestMinePoint(AAwCharacter* Character, UAwAIComponent* AIComponent,
	TArray<FString> Params)
{
	if (Character->GetBuff("DoMine").Num() <= 0 ||
		AIComponent->PathNodeQueue.Nodes.Num() <= 0)
			return FAICommand();

	FVector MinePoint = AIComponent->PathNodeQueue.Nodes[0];
	
	//Character Root点在中心，所以获得地面坐标后要加上HalfHeight
	MinePoint.Z += Character->GetCapsuleComponent()->GetScaledCapsuleHalfHeight();
	
	TArray<FVector> LocList;
	LocList.Add(MinePoint);
	
	FAICommand NewCommand = AIComponent->CreateMoveToCommand(LocList, 0);
	
	return NewCommand;
}

FAICommand UWereRatCommandoAIScript::DoActionOnMinePoint(AAwCharacter* Character, UAwAIComponent* AIComponent,
	TArray<FString> Params)
{
	if (Character->GetBuff("DoMine").Num() <= 0 ||
		AIComponent->PathNodeQueue.Nodes.Num() <= 0)
			return FAICommand();

	FAICommand NewCommand;
	
	bool DoDazeAction = false;
	if(Character->GetBuff("DoMine").Num() > 0)
	{
		for (const FBuffObj* Buff : Character->GetBuff("DoMine"))
			if (Buff->Stack >= 15)
				DoDazeAction = true;
	}
	if(DoDazeAction)
	{
		// do Daze
		TArray<FString> Actions;
		Actions.Add("Daze1");
		Actions.Add("Daze2");
		Actions.Add("Daze3");
		for (FBuffObj* Buff : Character->GetBuff("DoMine"))
			UBuffManager::ModifyBuffStack(Buff, 0, true);
		
		NewCommand = AIComponent->CreateUseActionCommand(
			Actions[FMath::RandRange(0,Actions.Num()-1)], AIComponent->PathNodeQueue.Nodes[0]);
	}
	else
	{
		// do Mine Action
		TArray<FString> Actions;
		Actions.Add("Mine_Short");
		Actions.Add("Mine_Long");
		NewCommand = AIComponent->CreateUseActionCommand(
			Actions[FMath::RandRange(0,Actions.Num()-1)], AIComponent->PathNodeQueue.Nodes[0]);

		AIComponent->TargetDirFunc = UDataFuncLib::SplitFuncNameAndParams(
			"WereRatCommandoAIScript.GetFaceDirToMine()");
	}
	
	return NewCommand;
}

FAICommand UWereRatCommandoAIScript::MoveAroundViewedClosetEnemy(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	FAICommand NewCommand = FAICommand();
	FAIFindChaInfo ClosetTarget = FAIFindChaInfo();
	TArray<FAIFindChaInfo> FindedTargets = AIComponent->GetAllViewedCharacter(false, true);
	for (FAIFindChaInfo FindedTarget : FindedTargets)
	{
		if (FindedTarget.Character)
		{
			const float CurDisXY = FindedTarget.DistanceXY;
			const float CurDisZ = FindedTarget.DistanceZ;
			float CheckMaxDisXY = 0;
			float CheckMinDisXY = 0;
			if(Params.Num() > 1)
			{
				CheckMaxDisXY = FCString::Atof(*Params[1]);
				CheckMinDisXY = FCString::Atof(*Params[0]);
			}
			
			const float CheckDisZ = AIComponent->StopZDisWhenMoveFollow;
			if((CurDisXY <= CheckMaxDisXY) && (CurDisXY >= CheckMinDisXY) && (CurDisZ < CheckDisZ))
			{
				if(!ClosetTarget.Character)
					ClosetTarget = FindedTarget;
				else if(FindedTarget.DistanceXY < ClosetTarget.DistanceXY)
					ClosetTarget = FindedTarget;
			}
		}
	}
	if(ClosetTarget.Character)
	{
		AIComponent->SetTargetEnemy(ClosetTarget);
		AIComponent->TargetDirFunc = UDataFuncLib::SplitFuncNameAndParams("MobAIScript.GetCurTargetEnemyDir()");
		if(Params.Num() > 3)
		{
			if(FMath::RandBool())
				NewCommand = AIComponent->CreateUseActionCommand(Params[2], Character->GetActorForwardVector());
			else
				NewCommand = AIComponent->CreateUseActionCommand(Params[3], Character->GetActorForwardVector());
		}
		else if(Params.Num() > 2)
			NewCommand = AIComponent->CreateUseActionCommand(Params[2], Character->GetActorForwardVector());
	}
	return NewCommand;
}

FAICommand UWereRatCommandoAIScript::WereRatScratch(AAwCharacter* Character, UAwAIComponent* AIComponent,
	TArray<FString> Params)
{
	FAICommand NewCommand = FAICommand();
	FAIFindChaInfo ClosetTarget = FAIFindChaInfo();
	TArray<FAIFindChaInfo> FindedTargets = AIComponent->GetAllViewedCharacter(false, true);
	for (FAIFindChaInfo FindedTarget : FindedTargets)
	{
		if (FindedTarget.Character)
		{
			const float CurDisXY = FindedTarget.DistanceXY;
			const float CurDisZ = FindedTarget.DistanceZ;
			float CheckMaxDisXY = 0;
			float CheckMinDisXY = 0;
			if(Params.Num() > 1)
			{
				CheckMaxDisXY = FCString::Atof(*Params[1]);
				CheckMinDisXY = FCString::Atof(*Params[0]);
			}
			const float CheckDisZ = AIComponent->StopZDisWhenMoveFollow;
			if((CurDisXY <= CheckMaxDisXY) && (CurDisXY >= CheckMinDisXY) && (CurDisZ < CheckDisZ))
			{
				if(Params.Num() > 3)
				{
					const float CheckMinDegree = FCString::Atof(*Params[2]);
					const float CheckMaxDegree = FCString::Atof(*Params[3]);
					FVector TargetDir = FindedTarget.Character->GetActorLocation() - Character->GetActorLocation();
					TargetDir.Normalize();
					const float CurDegree = UKismetMathLibrary::DegAcos(FVector::DotProduct(Character->GetActorForwardVector(), TargetDir));
					if(CurDegree >= CheckMinDegree && CurDegree <= CheckMaxDegree)
					{
						if(!ClosetTarget.Character)
							ClosetTarget = FindedTarget;
						else if(FindedTarget.DistanceXY < ClosetTarget.DistanceXY)
							ClosetTarget = FindedTarget;
					}
				}
			}
		}
	}
	if(ClosetTarget.Character)
	{
		AIComponent->SetTargetEnemy(ClosetTarget);
		AIComponent->TargetDirFunc = UDataFuncLib::SplitFuncNameAndParams("MobAIScript.GetCurTargetEnemyDir()");
		NewCommand = AIComponent->CreateUseActionCommand(Params[4], Character->GetActorForwardVector());
		AIComponent->ClearUseActionTags();
		AIComponent->AddUseActionTag("TwoScratchCombo");
	}
	return NewCommand;
}

FAICommand UWereRatCommandoAIScript::WereRatRageScratch(AAwCharacter* Character, UAwAIComponent* AIComponent,
	TArray<FString> Params)
{
	FAICommand NewCommand = FAICommand();
	FAIFindChaInfo ClosetTarget = FAIFindChaInfo();
	TArray<FAIFindChaInfo> FindedTargets = AIComponent->GetAllViewedCharacter(false, true);
	for (FAIFindChaInfo FindedTarget : FindedTargets)
	{
		if (FindedTarget.Character)
		{
			const float CurDisXY = FindedTarget.DistanceXY;
			const float CurDisZ = FindedTarget.DistanceZ;
			float CheckMaxDisXY = 0;
			float CheckMinDisXY = 0;
			if(Params.Num() > 1)
			{
				CheckMaxDisXY = FCString::Atof(*Params[1]);
				CheckMinDisXY = FCString::Atof(*Params[0]);
			}
			const float CheckDisZ = AIComponent->StopZDisWhenMoveFollow;
			if((CurDisXY <= CheckMaxDisXY) && (CurDisXY >= CheckMinDisXY) && (CurDisZ < CheckDisZ))
			{
				if(Params.Num() > 3)
				{
					const float CheckMinDegree = FCString::Atof(*Params[2]);
					const float CheckMaxDegree = FCString::Atof(*Params[3]);
					FVector TargetDir = FindedTarget.Character->GetActorLocation() - Character->GetActorLocation();
					TargetDir.Normalize();
					const float CurDegree = UKismetMathLibrary::DegAcos(FVector::DotProduct(Character->GetActorForwardVector(), TargetDir));
					if(CurDegree >= CheckMinDegree && CurDegree <= CheckMaxDegree)
					{
						if(!ClosetTarget.Character)
							ClosetTarget = FindedTarget;
						else if(FindedTarget.DistanceXY < ClosetTarget.DistanceXY)
							ClosetTarget = FindedTarget;
					}
				}
			}
		}
	}
	if(ClosetTarget.Character)
	{
		AIComponent->SetTargetEnemy(ClosetTarget);
		AIComponent->TargetDirFunc = UDataFuncLib::SplitFuncNameAndParams("MobAIScript.GetCurTargetEnemyDir()");
		NewCommand = AIComponent->CreateUseActionCommand(Params[4], Character->GetActorForwardVector());
		AIComponent->ClearUseActionTags();
		AIComponent->AddUseActionTag("ThreeScratchCombo");
	}
	return NewCommand;
}

FAICommand UWereRatCommandoAIScript::RandomWereRatCommandoCombo(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	FAICommand NewCommand = FAICommand();
	FAIFindChaInfo ClosetTarget = FAIFindChaInfo();
	
	float CheckMinDisXY = 0;
	float CheckNearMaxDisXY = 0;
	float CheckMidMinDisXY = 0;
	float CheckMidMaxDisXY = 0;
	float CheckFarMinDisXY = 0;
	float CheckMaxDisXY = 0;
	
	if(Params.Num() > 5)
	{
		CheckMinDisXY = FCString::Atof(*Params[0]);
		CheckNearMaxDisXY = FCString::Atof(*Params[1]);
		CheckMidMinDisXY = FCString::Atof(*Params[2]);
		CheckMidMaxDisXY = FCString::Atof(*Params[3]);
		CheckFarMinDisXY = FCString::Atof(*Params[4]);
		CheckMaxDisXY = FCString::Atof(*Params[5]);
	}
	for (FAIFindChaInfo FindedTarget : AIComponent->GetAllViewedCharacter(false, true))
	{
		if (FindedTarget.Character)
		{
			const float CurDisXY = FindedTarget.DistanceXY;
			const float CurDisZ = FindedTarget.DistanceZ;
			
			const float CheckDisZ = AIComponent->GetSightZRadius();
			if((CurDisXY <= CheckMaxDisXY) && (CurDisXY >= CheckMinDisXY) && (CurDisZ < CheckDisZ))
			{
				if(!ClosetTarget.Character)
					ClosetTarget = FindedTarget;
				else if(FindedTarget.DistanceXY < ClosetTarget.DistanceXY)
					ClosetTarget = FindedTarget;
			}
		}
	}
	if(ClosetTarget.Character)
	{
		//检测怪物是否在玩家的正面(如果目标是怪物，则不管正反面)
		bool bInPlayerFront = true;
		if(ClosetTarget.Character->UnderPlayerControl())
		{
			FVector TargetDir = ClosetTarget.Character->GetActorLocation() - Character->GetActorLocation();
			TargetDir.Normalize();
			FVector PlayerCameraDir = ClosetTarget.Character->GetAwCameraComponent()->GetCameraRotate().Vector();
			if(FMath::Abs(UMathFuncLib::GetDegreeBetweenTwoVector(TargetDir, PlayerCameraDir)) < 90)
				bInPlayerFront = false;
		}
		
		TArray<FString> CanUseComboRange;
		//检测选中的敌人在哪个连招的范围
		//连招1 范围（0-450）
		if(ClosetTarget.DistanceXY <= CheckNearMaxDisXY && ClosetTarget.DistanceXY >= CheckMinDisXY)
		{
			CanUseComboRange.Add("NearCombo");
		}
		//连招2 范围（300-900）
		if(ClosetTarget.DistanceXY <= CheckMidMaxDisXY && ClosetTarget.DistanceXY >= CheckMidMinDisXY)
		{
			CanUseComboRange.Add("MidCombo");
		}
		//连招3 范围（800-检测的最大范围）
		if(ClosetTarget.DistanceXY <= CheckMaxDisXY && ClosetTarget.DistanceXY >= CheckFarMinDisXY)
		{
			CanUseComboRange.Add("FarCombo");
		}
		if(CanUseComboRange.Num())
		{
			const FString UseCombo = CanUseComboRange[FMath::RandRange(0,CanUseComboRange.Num() - 1)];
			if(UseCombo == "NearCombo")
			{
				int UseComboIndex = FMath::RandRange(0,14);
				if(!bInPlayerFront)
					UseComboIndex = FMath::RandRange(4,14);
				switch (UseComboIndex)
				{
				case 0:
					{
						AIComponent->AddUseActionTag("WereRatCommandoNearCombo0");
						NewCommand = AIComponent->CreateUseActionCommand("Attack_S1", Character->GetActorForwardVector());
						break;
					}
				case 1:
					{
						AIComponent->AddUseActionTag("WereRatCommandoNearCombo1");
						NewCommand = AIComponent->CreateUseActionCommand("Attack_S1_C2", Character->GetActorForwardVector());
						break;
					}
				case 2:
					{
						AIComponent->AddUseActionTag("WereRatCommandoNearCombo");
						NewCommand = AIComponent->CreateUseActionCommand("Attack_S2", Character->GetActorForwardVector());
						break;
					}
				case 3:
					{
						AIComponent->AddUseActionTag("WereRatCommandoNearCombo");
						NewCommand = AIComponent->CreateUseActionCommand("Attack_S2_C2", Character->GetActorForwardVector());
						break;
					}
				case 4:
					{
						AIComponent->AddUseActionTag("WereRatCommandoNearCombo");
						NewCommand = AIComponent->CreateUseActionCommand("Attack_S2_C3", Character->GetActorForwardVector());
						break;
					}
				case 5:
					{
						AIComponent->AddUseActionTag("WereRatCommandoNearCombo2");
						NewCommand = AIComponent->CreateUseActionCommand("Attack_S1_C3", Character->GetActorForwardVector());
						break;
					}
				case 6:
				case 7:
				case 8:
				case 9:
					{
						AIComponent->AddUseActionTag("WereRatCommandoNearCombo3");
						NewCommand = AIComponent->CreateUseActionCommand("Dodge_Back", Character->GetActorForwardVector());
						break;
					}
				case 10:
				case 11:
				case 12:
					{
						NewCommand = AIComponent->CreateUseActionCommand("LeftPace", Character->GetActorForwardVector());
						break;
					}
				case 13:
				case 14:
				case 15:
					{
						NewCommand = AIComponent->CreateUseActionCommand("RightPace", Character->GetActorForwardVector());
						break;
					}
				}
			}
			else if(UseCombo == "MidCombo")
			{
				int UseComboIndex = FMath::RandRange(0,14);
				if(!bInPlayerFront)
					UseComboIndex = FMath::RandRange(4,14);
				switch (UseComboIndex)
				{
				case 0:
					{
						AIComponent->AddUseActionTag("WereRatCommandoMidCombo0");
						NewCommand = AIComponent->CreateUseActionCommand("Attack_M1", Character->GetActorForwardVector());
						break;
					}
				case 1:
					{
						AIComponent->AddUseActionTag("WereRatCommandoMidCombo1");
						NewCommand = AIComponent->CreateUseActionCommand("Attack_M1_C2", Character->GetActorForwardVector());
						break;
					}
				case 2:
					{
						AIComponent->AddUseActionTag("WereRatCommandoMidCombo3");
						NewCommand = AIComponent->CreateUseActionCommand("Attack_M2", Character->GetActorForwardVector());
						break;
					}
				case 3:
					{
						AIComponent->AddUseActionTag("WereRatCommandoMidCombo3");
						NewCommand = AIComponent->CreateUseActionCommand("Attack_M2_C2", Character->GetActorForwardVector());
						break;
					}
				case 4:
					{
						AIComponent->AddUseActionTag("WereRatCommandoMidCombo3");
						NewCommand = AIComponent->CreateUseActionCommand("Attack_M2_C3", Character->GetActorForwardVector());
						break;
					}
				case 5:
					{
						AIComponent->AddUseActionTag("WereRatCommandoMidCombo2");
						NewCommand = AIComponent->CreateUseActionCommand("Attack_M1_C3", Character->GetActorForwardVector());
						break;
					}
				case 6:
					{
						AIComponent->AddUseActionTag("WereRatCommandoMidCombo4");
						NewCommand = AIComponent->CreateUseActionCommand("Roar", Character->GetActorForwardVector());
						break;
					}
				case 7:
					{
						AIComponent->AddUseActionTag("WereRatCommandoMidCombo4");
						NewCommand = AIComponent->CreateUseActionCommand("Roar_C1", Character->GetActorForwardVector());
						break;
					}
				case 8:
				case 9:
				case 10:
				case 11:
					{
						NewCommand = AIComponent->CreateUseActionCommand("LeftPace", Character->GetActorForwardVector());
						break;
					}
				case 12:
				case 13:
				case 14:
					{
						NewCommand = AIComponent->CreateUseActionCommand("RightPace", Character->GetActorForwardVector());
						break;
					}
				}
			}
			else if(UseCombo == "FarCombo")
			{
				int UseComboIndex = FMath::RandRange(0,14);
				if(!bInPlayerFront)
					UseComboIndex = FMath::RandRange(4,14);
				switch (UseComboIndex)
				{
				case 0:
					{
						AIComponent->AddUseActionTag("WereRatCommandoFarCombo0");
						NewCommand = AIComponent->CreateUseActionCommand("Attack_L1", Character->GetActorForwardVector());
						break;
					}
				case 1:
					{
						AIComponent->AddUseActionTag("WereRatCommandoFarCombo0");
						NewCommand = AIComponent->CreateUseActionCommand("Attack_L1_C2", Character->GetActorForwardVector());
						break;
					}
				case 2:
					{
						AIComponent->AddUseActionTag("WereRatCommandoFarCombo1");
						NewCommand = AIComponent->CreateUseActionCommand("Attack_L2", Character->GetActorForwardVector());
						break;
					}
				case 3:
					{
						AIComponent->AddUseActionTag("WereRatCommandoFarCombo1");
						NewCommand = AIComponent->CreateUseActionCommand("Attack_L2_C2", Character->GetActorForwardVector());
						break;
					}
				case 4:
					{
						AIComponent->AddUseActionTag("WereRatCommandoFarCombo1");
						NewCommand = AIComponent->CreateUseActionCommand("Attack_L2_C3", Character->GetActorForwardVector());
						break;
					}
				case 5:
				{
					AIComponent->AddUseActionTag("WereRatCommandoFarCombo0");
					NewCommand = AIComponent->CreateUseActionCommand("Attack_L1_C3", Character->GetActorForwardVector());
					break;
				}
				case 6:
					{
						AIComponent->AddUseActionTag("WereRatCommandoFarCombo2");
						NewCommand = AIComponent->CreateUseActionCommand("Roar", Character->GetActorForwardVector());
						break;
					}
				case 7:
					{
						AIComponent->AddUseActionTag("WereRatCommandoFarCombo2");
						NewCommand = AIComponent->CreateUseActionCommand("Roar_C1", Character->GetActorForwardVector());
						break;
					}
				case 8:
					{
						AIComponent->AddUseActionTag("WereRatCommandoFarCombo2");
						NewCommand = AIComponent->CreateUseActionCommand("Daze1", Character->GetActorForwardVector());
						break;
					}
				case 9:
				case 10:
				case 11:
					{
						NewCommand = AIComponent->CreateUseActionCommand("LeftPace", Character->GetActorForwardVector());
						break;
					}
				case 12:
				case 13:
				case 14:
					{
						NewCommand = AIComponent->CreateUseActionCommand("RightPace", Character->GetActorForwardVector());
						break;
					}
				}
			}
			AIComponent->SetTargetEnemy(ClosetTarget);
			AIComponent->TargetDirFunc = UDataFuncLib::SplitFuncNameAndParams("MobAIScript.GetCurTargetEnemyDir()");
		}
	}
	return NewCommand;
}

FAICommand UWereRatCommandoAIScript::RandomWereRatCommandoCrazyCombo(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	FAICommand NewCommand = FAICommand();
	FAIFindChaInfo ClosetTarget = FAIFindChaInfo();
	
	float CheckMinDisXY = 0;
	float CheckNearMaxDisXY = 0;
	float CheckMidMinDisXY = 0;
	float CheckMidMaxDisXY = 0;
	float CheckFarMinDisXY = 0;
	float CheckMaxDisXY = 0;
	
	if(Params.Num() > 5)
	{
		CheckMinDisXY = FCString::Atof(*Params[0]);
		CheckNearMaxDisXY = FCString::Atof(*Params[1]);
		CheckMidMinDisXY = FCString::Atof(*Params[2]);
		CheckMidMaxDisXY = FCString::Atof(*Params[3]);
		CheckFarMinDisXY = FCString::Atof(*Params[4]);
		CheckMaxDisXY = FCString::Atof(*Params[5]);
	}
	for (FAIFindChaInfo FindedTarget : AIComponent->GetAllViewedCharacter(false, true))
	{
		if (FindedTarget.Character)
		{
			const float CurDisXY = FindedTarget.DistanceXY;
			const float CurDisZ = FindedTarget.DistanceZ;
			
			const float CheckDisZ = AIComponent->GetSightZRadius();
			if((CurDisXY <= CheckMaxDisXY) && (CurDisXY >= CheckMinDisXY) && (CurDisZ < CheckDisZ))
			{
				if(!ClosetTarget.Character)
					ClosetTarget = FindedTarget;
				else if(FindedTarget.DistanceXY < ClosetTarget.DistanceXY)
					ClosetTarget = FindedTarget;
			}
		}
	}
	if(ClosetTarget.Character)
	{
		//检测怪物是否在玩家的正面(如果目标是怪物，则不管正反面)
		bool bInPlayerFront = true;
		if(ClosetTarget.Character->UnderPlayerControl())
		{
			FVector TargetDir = ClosetTarget.Character->GetActorLocation() - Character->GetActorLocation();
			TargetDir.Normalize();
			FVector PlayerCameraDir = ClosetTarget.Character->GetAwCameraComponent()->GetCameraRotate().Vector();
			if(FMath::Abs(UMathFuncLib::GetDegreeBetweenTwoVector(TargetDir, PlayerCameraDir)) < 90)
				bInPlayerFront = false;
		}
		
		TArray<FString> CanUseComboRange;
		//检测选中的敌人在哪个连招的范围
		//连招1 范围（0-450）
		if(ClosetTarget.DistanceXY <= CheckNearMaxDisXY && ClosetTarget.DistanceXY >= CheckMinDisXY)
		{
			CanUseComboRange.Add("NearCombo");
		}
		//连招2 范围（300-900）
		if(ClosetTarget.DistanceXY <= CheckMidMaxDisXY && ClosetTarget.DistanceXY >= CheckMidMinDisXY)
		{
			CanUseComboRange.Add("MidCombo");
		}
		//连招3 范围（800-检测的最大范围）
		if(ClosetTarget.DistanceXY <= CheckMaxDisXY && ClosetTarget.DistanceXY >= CheckFarMinDisXY)
		{
			CanUseComboRange.Add("FarCombo");
		}
		if(CanUseComboRange.Num())
		{
			const FString UseCombo = CanUseComboRange[FMath::RandRange(0,CanUseComboRange.Num() - 1)];
			if(UseCombo == "NearCombo")
			{
				int UseComboIndex = FMath::RandRange(0,7);
				if(!bInPlayerFront)
					UseComboIndex = FMath::RandRange(4,9);
				switch (UseComboIndex)
				{
				case 0:
					{
						AIComponent->AddUseActionTag("WereRatCommandoNearCombo0");
						NewCommand = AIComponent->CreateUseActionCommand("Attack_S1", Character->GetActorForwardVector());
						break;
					}
				case 1:
					{
						AIComponent->AddUseActionTag("WereRatCommandoNearCombo1");
						NewCommand = AIComponent->CreateUseActionCommand("Attack_S1_C2", Character->GetActorForwardVector());
						break;
					}
				case 2:
					{
						AIComponent->AddUseActionTag("WereRatCommandoNearCombo");
						NewCommand = AIComponent->CreateUseActionCommand("Attack_S2", Character->GetActorForwardVector());
						break;
					}
				case 3:
					{
						AIComponent->AddUseActionTag("WereRatCommandoNearCombo");
						NewCommand = AIComponent->CreateUseActionCommand("Attack_S2_C2", Character->GetActorForwardVector());
						break;
					}
				case 4:
					{
						AIComponent->AddUseActionTag("WereRatCommandoNearCombo");
						NewCommand = AIComponent->CreateUseActionCommand("Attack_S2_C3", Character->GetActorForwardVector());
						break;
					}
				case 5:
					{
						AIComponent->AddUseActionTag("WereRatCommandoNearCombo2");
						NewCommand = AIComponent->CreateUseActionCommand("Attack_S1_C3", Character->GetActorForwardVector());
						break;
					}
				case 6:
				case 7:
					{
						AIComponent->AddUseActionTag("WereRatCommandoNearCombo3");
						NewCommand = AIComponent->CreateUseActionCommand("Dodge_Back", Character->GetActorForwardVector());
						break;
					}
				case 8:
					{
						NewCommand = AIComponent->CreateUseActionCommand("LeftPace", Character->GetActorForwardVector());
						break;
					}
				case 9:
					{
						NewCommand = AIComponent->CreateUseActionCommand("RightPace", Character->GetActorForwardVector());
						break;
					}
				}
			}
			else if(UseCombo == "MidCombo")
			{
				int UseComboIndex = FMath::RandRange(0,9);
				if(!bInPlayerFront)
					UseComboIndex = FMath::RandRange(4,9);
				switch (UseComboIndex)
				{
				case 0:
					{
						AIComponent->AddUseActionTag("WereRatCommandoMidCombo0");
						NewCommand = AIComponent->CreateUseActionCommand("Attack_M1", Character->GetActorForwardVector());
						break;
					}
				case 1:
					{
						AIComponent->AddUseActionTag("WereRatCommandoMidCombo1");
						NewCommand = AIComponent->CreateUseActionCommand("Attack_M1_C2", Character->GetActorForwardVector());
						break;
					}
				case 2:
					{
						AIComponent->AddUseActionTag("WereRatCommandoMidCombo3");
						NewCommand = AIComponent->CreateUseActionCommand("Attack_M2", Character->GetActorForwardVector());
						break;
					}
				case 3:
					{
						AIComponent->AddUseActionTag("WereRatCommandoMidCombo3");
						NewCommand = AIComponent->CreateUseActionCommand("Attack_M2_C2", Character->GetActorForwardVector());
						break;
					}
				case 4:
					{
						AIComponent->AddUseActionTag("WereRatCommandoMidCombo3");
						NewCommand = AIComponent->CreateUseActionCommand("Attack_M2_C3", Character->GetActorForwardVector());
						break;
					}
				case 5:
					{
						AIComponent->AddUseActionTag("WereRatCommandoMidCombo2");
						NewCommand = AIComponent->CreateUseActionCommand("Attack_M1_C3", Character->GetActorForwardVector());
						break;
					}
				case 6:
					{
						AIComponent->AddUseActionTag("WereRatCommandoMidCombo4");
						NewCommand = AIComponent->CreateUseActionCommand("Roar", Character->GetActorForwardVector());
						break;
					}
				case 7:
					{
						AIComponent->AddUseActionTag("WereRatCommandoMidCombo4");
						NewCommand = AIComponent->CreateUseActionCommand("Roar_C1", Character->GetActorForwardVector());
						break;
					}
				case 8:
					{
						NewCommand = AIComponent->CreateUseActionCommand("LeftPace", Character->GetActorForwardVector());
						break;
					}
				case 9:
					{
						NewCommand = AIComponent->CreateUseActionCommand("RightPace", Character->GetActorForwardVector());
						break;
					}
				}
			}
			else if(UseCombo == "FarCombo")
			{
				int UseComboIndex = FMath::RandRange(0,10);
				if(!bInPlayerFront)
					UseComboIndex = FMath::RandRange(4,10);
				switch (UseComboIndex)
				{
				case 0:
					{
						AIComponent->AddUseActionTag("WereRatCommandoFarCombo0");
						NewCommand = AIComponent->CreateUseActionCommand("Attack_L1", Character->GetActorForwardVector());
						break;
					}
				case 1:
					{
						AIComponent->AddUseActionTag("WereRatCommandoFarCombo0");
						NewCommand = AIComponent->CreateUseActionCommand("Attack_L1_C2", Character->GetActorForwardVector());
						break;
					}
				case 2:
					{
						AIComponent->AddUseActionTag("WereRatCommandoFarCombo1");
						NewCommand = AIComponent->CreateUseActionCommand("Attack_L2", Character->GetActorForwardVector());
						break;
					}
				case 3:
					{
						AIComponent->AddUseActionTag("WereRatCommandoFarCombo1");
						NewCommand = AIComponent->CreateUseActionCommand("Attack_L2_C2", Character->GetActorForwardVector());
						break;
					}
				case 4:
					{
						AIComponent->AddUseActionTag("WereRatCommandoFarCombo1");
						NewCommand = AIComponent->CreateUseActionCommand("Attack_L2_C3", Character->GetActorForwardVector());
						break;
					}
				case 5:
				{
					AIComponent->AddUseActionTag("WereRatCommandoFarCombo0");
					NewCommand = AIComponent->CreateUseActionCommand("Attack_L1_C3", Character->GetActorForwardVector());
					break;
				}
				case 6:
					{
						AIComponent->AddUseActionTag("WereRatCommandoFarCombo2");
						NewCommand = AIComponent->CreateUseActionCommand("Roar", Character->GetActorForwardVector());
						break;
					}
				case 7:
					{
						AIComponent->AddUseActionTag("WereRatCommandoFarCombo2");
						NewCommand = AIComponent->CreateUseActionCommand("Roar_C1", Character->GetActorForwardVector());
						break;
					}
				case 8:
					{
						AIComponent->AddUseActionTag("WereRatCommandoFarCombo2");
						NewCommand = AIComponent->CreateUseActionCommand("Daze1", Character->GetActorForwardVector());
						break;
					}
				case 9:
					{
						NewCommand = AIComponent->CreateUseActionCommand("LeftPace", Character->GetActorForwardVector());
						break;
					}
				case 10:
					{
						NewCommand = AIComponent->CreateUseActionCommand("RightPace", Character->GetActorForwardVector());
						break;
					}
				}
			}
			AIComponent->SetTargetEnemy(ClosetTarget);
			AIComponent->TargetDirFunc = UDataFuncLib::SplitFuncNameAndParams("MobAIScript.GetCurTargetEnemyDir()");
		}
	}
	return NewCommand;
}

FVector UWereRatCommandoAIScript::GetFaceDirToMine(AAwCharacter* Character, TArray<FString> TagParams,
                                                   TArray<FString> Params)
{
	const FPathNodeQueue PathNodeQueue = Character->GetAIComponent()->PathNodeQueue;
	
	if (Character->GetBuff("DoMine").Num() <= 0 || PathNodeQueue.Nodes.Num() < 2)
		return Character->GetActorForwardVector().GetSafeNormal();

	return (PathNodeQueue.Nodes[1] - Character->GetActorLocation()).GetSafeNormal(); 
}
