// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "AIUtils.h"
#include "TheAwakener_FO/GamePlay/AOE/AWAoeBase.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "TheAwakener_FO/GamePlay/Characters/AI/AwAIComponent.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "Kismet/GameplayStatics.h"
#include "UObject/Object.h"
#include "OgreAIScript.generated.h"





/**
 * 巨魔的AI脚本函数
 */
UCLASS()
class THEAWAKENER_FO_API UOgreAIScript : public UObject
{
	GENERATED_BODY()
private:
	//---------------------------这里都是inline的，所以直接写在.h就好-------------------------------

	//房间内玩家已经全部团灭
	static bool PlayerTeamDown()
	{
		for (auto CurPlayer : UGameplayFuncLib::GetAwGameState()->AllCharacters)
		{
			if (CurPlayer.Key)
			{
				if (CurPlayer.Key->Side == 0)
				{
					if (!CurPlayer.Key->Dead(true))
						return false;
				}
			}
		}
		return true;
	}

	//房间内有柱子AOE
	static TArray<AAWAoe*> GetPillarsInArea()
	{
		TArray<AAWAoe*> PillarList;
		for (auto& CurAOE : UGameplayFuncLib::GetAwGameState()->AOEList)
		{
			if (CurAOE.Key)
			{
				if (CurAOE.Key->Tags.Contains("Pillar") && !CurAOE.Key->IsActorBeingDestroyed())
				{
					PillarList.Add(CurAOE.Key);
				}
			}
			
		}
		return PillarList;
	}

	static bool HasFallingPillarInArea()
	{
		for (auto& CurAOE : UGameplayFuncLib::GetAwGameState()->AOEList)
		{
			if (CurAOE.Key)
			{
				if (CurAOE.Key->Tags.Contains("FallingPillar") && !CurAOE.Key->IsActorBeingDestroyed())
					return true;
			}
		}
		return false;
	}

	//亢奋中
	static bool GuyExcited(const AAwCharacter* Guy){return Guy->FightingWill.Level >= 3;}
	//普通中
	static bool GuyNormalFightingWill(const AAwCharacter* Guy){return Guy->FightingWill.Level == 2;}
	//虚弱中
	static bool GuyWeak(const AAwCharacter* Guy){return Guy->FightingWill.Level == 1;}
	//发呆中
	static bool GuyStare(const AAwCharacter* Guy){return Guy->FightingWill.Level <= 0;}

	//返回<视野内有多少人，视野外的有多少
	static TTuple<int, int, int> CheckFoeInSight(const AAwCharacter* Me, const float Range)
	{
		TArray<int> PlayerSide;
		PlayerSide.Add(0);
		TTuple<TArray<AAwCharacter*>, TArray<AAwCharacter*>, TArray<AAwCharacter*>> CheckResult =
			UAIUtils::CharacterSplitByCone(Me->GetActorLocation(), Me->GetActorRotation().Yaw, 90.000f, Range, PlayerSide, false, false, false);
		
		return TTuple<int,int, int>(CheckResult.Get<0>().Num(), CheckResult.Get<1>().Num(), CheckResult.Get<2>().Num());
	}
public:
	//------------------------------------------条件----------------------------------------------------------------

	//如果玩家方全败（房间内的玩家方）就返回True
	UFUNCTION(BlueprintCallable)
	static bool AllPlayerDown(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//是否在柱子范围内
	UFUNCTION(BlueprintCallable)
	static bool CheckNearPillar(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	//场上是否存在柱子
	UFUNCTION(BlueprintCallable)
	static bool CheckHasPillarInArea(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	//判断是否有意愿去拿起柱子
	UFUNCTION(BlueprintCallable)
	static bool CheckWillingToTakePillar(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	
	//场上没有柱子 [0]柱子少于等于多少根算“没有柱子”，默认0
	UFUNCTION(BlueprintCallable)
	static bool CheckNoPillarInArea(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//是否在视线中有高于AI的敌人
	UFUNCTION(BlueprintCallable)
	static bool CheckViewedEnemyHeight(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	
	//------------------------------------------事件----------------------------------------------------------------
	//转向柱子并捡起柱子
	UFUNCTION(BlueprintCallable)
	static FAICommand PullUpPillar(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//【Direction In Action】 让角色转向最近的柱子
	UFUNCTION(BlueprintCallable)
	static FVector2D DirectToClosetPillar(AAwCharacter* AICha, TArray<FString> Tags, TArray<FString> Params);

	//走到最近的柱子
	UFUNCTION(BlueprintCallable)
	static FAICommand MoveToNearestPillar(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//攻击视野中中距离的敌人
	UFUNCTION(BlueprintCallable)
	static FAICommand AttackMiddleViewdEnemy(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//攻击高处的敌人
	UFUNCTION(BlueprintCallable)
	static FAICommand AttackHigherEnemy(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//攻击范围内最近的敌人
	//Param[0]-[1]范围内
	//Parmas[2]:敌人在AI的右侧时做的动作；Parmas[3]:敌人在AI的左侧时做的动作
	UFUNCTION(BlueprintCallable)
	static FAICommand AttackClosetNoViewedEnemy(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//巨魔吼叫后场地上随机产生柱子AOE
	UFUNCTION(BlueprintCallable)
	static FAICommand CreateRandomPillars(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//巨魔向前召唤出柱子，[0]第几根
	UFUNCTION(BlueprintCallable)
	static FAICommand CreateQueuePillars(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//测试用，打出当前的状况，[0]要打的字儿，可以当kismet用
	UFUNCTION(BlueprintCallable)
	static FAICommand TestShowState(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//【部位破坏时】巨魔柱子被打破坏
	UFUNCTION(BlueprintCallable)
	static UTimelineNode* PillarBroken(AAwCharacter* Character, FChaPart Part, bool Broken, TArray<FString> Params);

	//根据情况动态选择一个动作去做
	UFUNCTION(BlueprintCallable)
	static FAICommand OgreBasicBattle(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//-----------------------------------------------------------连招------------------------------------------------------------------------
	//砸地召唤柱子的连招
	UFUNCTION(BlueprintCallable)
	static FString ComboAttack_SummonPillar(AAwCharacter* AIGuy, TArray<FString> StateTags, TArray<FString> Params);
};
