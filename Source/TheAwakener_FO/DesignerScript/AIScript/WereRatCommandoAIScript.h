// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "UObject/Object.h"
#include "WereRatCommandoAIScript.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UWereRatCommandoAIScript : public UObject
{
	GENERATED_BODY()

	// ---------- Condition ----------
	
	// 检查是否在矿点附近
	// Params[0] 距离，默认是50
	UFUNCTION(BlueprintCallable)
	static bool CheckIsNearMinePoint(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	
	// ---------- Action ----------

	// 移动到最近的矿点上
	UFUNCTION(BlueprintCallable)
	static FAICommand MoveToNearestMinePoint(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	// 在矿点上做矿点的发呆动作
	UFUNCTION(BlueprintCallable)
	static FAICommand DoActionOnMinePoint(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	// 向着视野内Params[0]-Params[1]距离范围的一个最近的目标，
	// 进行左、右踱步（Params[2]、Params[3]）
	UFUNCTION(BlueprintCallable)
	static FAICommand MoveAroundViewedClosetEnemy(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//AI以视野范围内Params[0]-Params[1]距离，Params[2]-Params[3]角度范围内的最近敌人为目标，
	//执行Action,Params[4]为ActionId
	UFUNCTION(BlueprintCallable)
	static FAICommand WereRatScratch(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//AI以视野范围内Params[0]-Params[1]距离，Params[2]-Params[3]角度范围内的最近敌人为目标，
	//执行Action,Params[4]为ActionId
	UFUNCTION(BlueprintCallable)
	static FAICommand WereRatRageScratch(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	// 向着视野内Params[0]-Params[5]距离范围的一个最近的目标，
	// 随机一个鼠人突击兵的连招
	// Params[0]-Params[1]为近距离
	// Params[2]-Params[3]为中距离
	// Params[4]-Params[5]为远距离
	UFUNCTION(BlueprintCallable)
	static FAICommand RandomWereRatCommandoCombo(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	// 向着视野内Params[0]-Params[5]距离范围的一个最近的目标，
	// 随机一个鼠人突击兵的连招
	// Params[0]-Params[1]为近距离
	// Params[2]-Params[3]为中距离
	// Params[4]-Params[5]为远距离
	UFUNCTION(BlueprintCallable)
	static FAICommand RandomWereRatCommandoCrazyCombo(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	// ---------- Dir V3 ----------
	UFUNCTION(BlueprintCallable)
	static FVector GetFaceDirToMine(AAwCharacter* Character, TArray<FString> TagParams,TArray<FString> Params);
};
