// Fill out your copyright notice in the Description page of Project Settings.


#include "MobAIScript.h"

#include <filesystem>

#include "MathUtil.h"
#include "Engine/TargetPoint.h"
#include "Kismet/KismetArrayLibrary.h"
#include "Kismet/KismetMathLibrary.h"
#include "TheAwakener_FO/FunctionLibrary/MathFuncLib.h"
#include "TheAwakener_FO/GameFramework/Manager/AwRogueDataSystem.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"

bool UMobAIScript::Always(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	return true;
}

bool UMobAIScript::Default(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	if (Params.Num())
		return FCString::ToBool(*Params[0]);
	
	return false;
}

bool UMobAIScript::CheckStopAI(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	if(Character->GetBuff("StopAI").Num()>0)
	{
		return true;
	}
	return false;
}

bool UMobAIScript::CheckPlayerInRange(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	float CheckMinDis = FCString::Atof(*Params[0]);
	float CheckMaxDis = -1;
	if (Params.Num()==2)
	{
		CheckMaxDis = FCString::Atof(*Params[1]);
	}
	
	return UGameplayFuncLib::HavePlayerCharacterInRange(Character->GetActorLocation(),CheckMinDis,CheckMaxDis);
}

bool UMobAIScript::CheckPlayerOutRange(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	float CheckMinDis = FCString::Atof(*Params[0]);
	float CheckMaxDis = -1;
	if (Params.Num()==2)
	{
		CheckMaxDis = FCString::Atof(*Params[1]);
	}
	
	return !UGameplayFuncLib::HavePlayerCharacterInRange(Character->GetActorLocation(),CheckMinDis,CheckMaxDis);
}

bool UMobAIScript::CheckEnemyOutRange(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	AAwCharacter* MyCharacter = AIComponent->GetClosestDistanceEnemy().Character;
	if (MyCharacter)
	{
		float CheckDis = 5000;
		const float CurDis = FVector::Dist2D(Character->GetActorLocation(), MyCharacter->GetActorLocation());
		if (Params.Num() == 1)
		{
			CheckDis = FCString::Atof(*Params[0]);
			if (CurDis > CheckDis)
				return true;
		}
		else if(Params.Num() == 2)
		{
			float CheckMinDis = FCString::Atof(*Params[0]);
			CheckDis = FCString::Atof(*Params[1]);
			if(CurDis > CheckDis && CurDis < CheckMinDis)
				return true;
		}
	}
	return false;
}

bool UMobAIScript::CheckCharacterNotInWar(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	return !Character->InWar();
}

bool UMobAIScript::CheckCharacterInWar(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	return Character->InWar();
}

bool UMobAIScript::CheckMoveToProtectedSceneItemAround(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	//接近距离
	float MinRange = 0;
	if (Params.IsValidIndex(0))
	{
		MinRange = Params[0].IsNumeric() ? FCString::Atof(*Params[0]) : MinRange;
	}
	float RandomMaxDistance = 500;
	if (Params.IsValidIndex(1))
	{
		RandomMaxDistance = Params[1].IsNumeric() ? FCString::Atof(*Params[1]) : RandomMaxDistance;
	}
	float RandomMinDistance = 100;
	if (Params.IsValidIndex(2))
	{
		RandomMinDistance = Params[2].IsNumeric() ? FCString::Atof(*Params[2]) : RandomMinDistance;
	}

	//保护buff相关
	TArray<FString> ProtectBuffIDArray = { "ProtectSceneItem" };
	if (!CheckHasBuff(Character,AIComponent,ProtectBuffIDArray))
	{
		return false;
	}
	TArray<FBuffObj*> ResultBuffs;
	ResultBuffs = Character->GetBuff("ProtectSceneItem");
	if (!ResultBuffs.IsValidIndex(0))
	{
		return false;
	}

	//获取保护对象
	FString UID = *(ResultBuffs[0]->Param.Find("SceneItemId"));
	AActor* Target = *(UGameplayFuncLib::GetAwGameState()->SceneItemList.Find(UID));
	if (!Target->IsValidLowLevel())
	{
		return false;
	}
	FVector TargetLocation = Target->GetActorLocation();
	FVector Offset = FVector::ZeroVector;
	if (ResultBuffs[0]->Param.Contains("FollowOffset"))
	{
		Offset.InitFromString(*ResultBuffs[0]->Param.Find("FollowOffset"));
	}
	else
	{
		Offset = AIComponent->GetRandomTargetReachableNavPointInRadius(Target->GetActorLocation(), RandomMaxDistance, RandomMinDistance) - Target->GetActorLocation();
		ResultBuffs[0]->Param.Add("FollowOffset",Offset.ToString());
	}

	bool result = FVector::Dist( Character->GetActorLocation(), TargetLocation + Offset) > MinRange;

	return result;
}

bool UMobAIScript::CheckAIMoveToLoc(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	if(Params.Num())
	{
		FVector TargetLoc;
		TargetLoc.InitFromString(Params[0]);
		const float CurDisXY = FVector::DistXY(Character->GetActorLocation(),TargetLoc );
		const float CurDisZ = FMath::Abs(Character->GetActorLocation().Z - TargetLoc.Z);
		const float CheckDisXY = AIComponent->StopRangeWhenMoveFollow;
		const float CheckDisZ = AIComponent->StopZDisWhenMoveFollow;
		return CurDisXY > CheckDisXY || (CurDisXY <= CheckDisXY && CurDisZ > CheckDisZ);
	}
	return true;
}

bool UMobAIScript::CheckAIMoveToClosetViewedEnemy(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	FAIFindChaInfo ClosetTarget = AIComponent->GetClosestDistanceEnemy(true);
	if (ClosetTarget.Character)
	{
		const float CurDisXY = ClosetTarget.DistanceXY;
		const float CurDisZ = ClosetTarget.DistanceZ;
		const float CheckDisXY = AIComponent->StopRangeWhenMoveFollow;
		const float CheckDisZ = AIComponent->StopZDisWhenMoveFollow;
		return CurDisXY > CheckDisXY || (CurDisXY <= CheckDisXY && CurDisZ > CheckDisZ);
	}
	return false;
}

bool UMobAIScript::CheckAIMoveAroundClosetViewedEnemy(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	FAIFindChaInfo ClosetTarget = AIComponent->GetClosestDistanceEnemy(true);
	if (ClosetTarget.Character)
	{
		const float CurDis = ClosetTarget.DistanceXY;
		const float CheckMaxRadius = AIComponent->MaxDisWhenMoveAround;
		const float CheckMinRadius = AIComponent->MinDisWhenMoveAround;
		return CurDis <= CheckMaxRadius && CurDis >= CheckMinRadius;
	}
	return false;
}

bool UMobAIScript::CheckHasEnemyInSightRange(AAwCharacter* Character, UAwAIComponent* AIComponent,
	TArray<FString> Params)
{
	for (TTuple<AAwCharacter*, FString> FindCharacter : UGameplayFuncLib::GetAwGameState()->AllCharacters)
	{
		if(FindCharacter.Key == nullptr) continue;
		if(FindCharacter.Key != Character && !FindCharacter.Key->Dead(true) && Character->IsEnemy(FindCharacter.Key))
		{
			const float CurDisXY = FVector::Dist2D(FindCharacter.Key->GetActorLocation(), Character->GetActorLocation());
			const float CurDisZ = FMath::Abs(FindCharacter.Key->GetActorLocation().Z - Character->GetActorLocation().Z);
			float CheckMaxDisXY = AIComponent->GetSightRadius();
			float CheckMinDisXY = 0;
			const float CheckDisZ = AIComponent->GetSightZRadius();
			return (CurDisXY < CheckMaxDisXY) && (CurDisXY >= CheckMinDisXY) && (CurDisZ < CheckDisZ);
		}
	}
	return false;
}

bool UMobAIScript::CheckHasEnemyInRange(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	for (TTuple<AAwCharacter*, FString> FindCharacter : UGameplayFuncLib::GetAwGameState()->AllCharacters)
	{
		if(FindCharacter.Key == nullptr) continue;
		if(FindCharacter.Key != Character && !FindCharacter.Key->Dead(true) && Character->IsEnemy(FindCharacter.Key))
		{
			float CurDisXY = FVector::Dist2D(FindCharacter.Key->GetActorLocation(), Character->GetActorLocation());
			float CurDisZ = FMath::Abs(FindCharacter.Key->GetActorLocation().Z - Character->GetActorLocation().Z);
			float CheckMaxDisXY = 0;
			float CheckMinDisXY = 0;
			if (Params.Num())
			{
				if(Params.Num() > 1)
				{
					CheckMaxDisXY = FCString::Atof(*Params[1]);
					CheckMinDisXY = FCString::Atof(*Params[0]);
				}
				else
				{
					CheckMaxDisXY = FCString::Atof(*Params[0]);
				}
			}
			else
			{
				CheckMaxDisXY = AIComponent->StopRangeWhenMoveFollow;
			}	
			const float CheckDisZ = AIComponent->GetSightZRadius();
			return (CurDisXY < CheckMaxDisXY) && (CurDisXY >= CheckMinDisXY) && (CurDisZ < CheckDisZ);
		}
	}
	return false;
}

bool UMobAIScript::CheckHasViewedEnemyInRange(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	TArray<FAIFindChaInfo> FindedTargets = AIComponent->GetAllViewedCharacter(false, true);
	for (FAIFindChaInfo FindedTarget : FindedTargets)
	{
		if (FindedTarget.Character)
		{
			const float CurDisXY = FindedTarget.DistanceXY;
			const float CurDisZ = FindedTarget.DistanceZ;
			float CheckMaxDisXY;
			float CheckMinDisXY = 0;
			if (Params.Num())
			{
				if(Params.Num() > 1)
				{
					CheckMaxDisXY = FCString::Atof(*Params[1]);
					CheckMinDisXY = FCString::Atof(*Params[0]);
				}
				else
				{
					CheckMaxDisXY = FCString::Atof(*Params[0]);
				}
			}
			else
			{
				CheckMaxDisXY = AIComponent->StopRangeWhenMoveFollow;
			}	
			const float CheckDisZ = AIComponent->GetSightZRadius();
			return (CurDisXY <= CheckMaxDisXY) && (CurDisXY >= CheckMinDisXY) && (CurDisZ < CheckDisZ);
		}
	}
	return false;
}

bool UMobAIScript::CheckHasViewedEnemyInDisAndDegreeRange(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	const TArray<FAIFindChaInfo> FindedTargets = AIComponent->GetAllViewedCharacter(false, true);
	for (const FAIFindChaInfo FindedTarget : FindedTargets)
	{
		if (FindedTarget.Character)
		{
			const float CurDisXY = FindedTarget.DistanceXY;
			const float CurDisZ = FindedTarget.DistanceZ;
			float CheckMaxDisXY;
			float CheckMinDisXY = 0;
			if (Params.Num() > 1)
			{
				CheckMinDisXY = FCString::Atof(*Params[0]);
				CheckMaxDisXY = FCString::Atof(*Params[1]);
			}
			else
			{
				CheckMaxDisXY = AIComponent->StopRangeWhenMoveFollow;
			}	
			const float CheckDisZ = AIComponent->GetSightZRadius();
			const bool TargetInDis = (CurDisXY <= CheckMaxDisXY) && (CurDisXY >= CheckMinDisXY) && (CurDisZ < CheckDisZ);
			if(TargetInDis)
			{
				if(Params.Num() > 3)
				{
					const float CheckMinDegree = FCString::Atof(*Params[2]);
					const float CheckMaxDegree = FCString::Atof(*Params[3]);
					FVector TargetDir = FindedTarget.Character->GetActorLocation() - Character->GetActorLocation();
					TargetDir.Normalize();
					FVector2D CurDir2D = FVector2D(Character->GetActorForwardVector().X, Character->GetActorForwardVector().Y);
					CurDir2D.Normalize();
					FVector2D TargetDir2D = FVector2D(TargetDir.X, TargetDir.Y);
					TargetDir2D.Normalize();
					const float CurDegree = UKismetMathLibrary::DegAcos(FVector::DotProduct(FVector(CurDir2D, 0), FVector(TargetDir2D, 0)));
					return (CurDegree >= CheckMinDegree && CurDegree <= CheckMaxDegree);
				}
				else
				{
					return true;
				}
			}
		}
	}
	return false;
}

bool UMobAIScript::CheckAINumLessThanPLayerNum(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	int AINum = 1;
	int EnemyNum = 0;
	for (TTuple<AAwCharacter*, FString> FindCharacter : UGameplayFuncLib::GetAwGameState()->AllCharacters)
	{
		if(FindCharacter.Key == nullptr) continue;
		if(FindCharacter.Key != Character && !FindCharacter.Key->Dead(true))
		{
			if(Character->IsEnemy(FindCharacter.Key))
				EnemyNum++;
			else
				AINum++;
		}
	}
	EnemyNum = FMath::Clamp(EnemyNum, 0, 4);
	return AINum <= EnemyNum;
}

bool UMobAIScript::CheckAIInAOE(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	TArray<AAWAoe*> AOEList;
	FString LevelName = "";
	if (UGameplayFuncLib::GetAwGameState()->AllCharacters.Contains(Character))
		LevelName = *UGameplayFuncLib::GetAwGameState()->AllCharacters.Find(Character);
	
	for (auto& CurAOE : UGameplayFuncLib::GetAwGameState()->AOEList)
	{
		if (LevelName != "")
		{
			if (CurAOE.Value == LevelName)
				AOEList.Add(CurAOE.Key);
		}
		else
			AOEList.Add(CurAOE.Key);
	}

	TArray<AAWAoe*> FilteredAOEList;
	if (Params.Num())
	{
		for (auto& CurAOE : AOEList)
		{
			for (auto& CurTag : Params)
			{
				if (CurAOE->Tags.Contains(FName(*CurTag)) && !FilteredAOEList.Contains(CurAOE))
				{
					FilteredAOEList.Add(CurAOE);
					break;
				}
			}
		}
	}
	else
	{
		FilteredAOEList = AOEList;
	}
	for (auto& CurAOE : FilteredAOEList)
	{
		for (auto& CurCharacterInfo : CurAOE->CharacterInRange())
		{
			if (CurCharacterInfo.BeCaughtActor == Character)
				return true;
		}
	}

	return false;
}

bool UMobAIScript::CheckFightingWillLevelEquals(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	if(Params.Num())
	{
		const int CheckLevel = FCString::Atoi(*Params[0]);
		return (Character->FightingWill.Level == CheckLevel);
	}
	return false;
}

bool UMobAIScript::CheckFightingWillLevelGreater(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	if(Params.Num())
	{
		const int CheckLevel = FCString::Atoi(*Params[0]);
		return (Character->FightingWill.Level > CheckLevel);
	}
	return false;
}

bool UMobAIScript::CheckFightingWillLevelLess(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	if(Params.Num())
	{
		const int CheckLevel = FCString::Atoi(*Params[0]);
		return (Character->FightingWill.Level < CheckLevel);
	}
	return false;
}

bool UMobAIScript::CheckFightingWillValueGreater(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	if(Params.Num())
	{
		const int CheckValue = FCString::Atoi(*Params[0]);
		return (Character->FightingWill.Value > CheckValue);
	}
	return false;
}

bool UMobAIScript::CheckFightingWillValueLess(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	if(Params.Num())
	{
		const int CheckValue = FCString::Atoi(*Params[0]);
		return (Character->FightingWill.Value < CheckValue);
	}
	return false;
}

bool UMobAIScript::CheckAIJustLeaveBattle(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	return (AIComponent->JustClearStimulus && AIComponent->GetAllViewedCharacter().Num() <= 0);
}

bool UMobAIScript::CheckStimulateByView(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	return AIComponent->GetAllViewedCharacter().Num() > 0;
}

bool UMobAIScript::CheckNotStimulateByView(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	return AIComponent->GetAllViewedCharacter().Num() <= 0;
}

bool UMobAIScript::CheckStimulateByHeard(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	return AIComponent->Stimuli_Voice.Num() > 0;
}

bool UMobAIScript::CheckStimulateByOffended(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	return AIComponent->Stimuli_Offended.Num() > 0;
}

bool UMobAIScript::CheckStimulateByAllySignal(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	return AIComponent->Stimuli_AllySignal.Num() > 0;
}

bool UMobAIScript::CheckHasStimulate(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	return AIComponent->GetAllViewedCharacter().Num() > 0 || AIComponent->Stimuli_Voice.Num() > 0 || AIComponent->Stimuli_Offended.Num() > 0 || AIComponent->Stimuli_AllySignal.Num() > 0;
}

bool UMobAIScript::CheckHasBuff(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	if(Params.Num() > 0)
	{
		const FString BuffId = Params[0];

		int Stack = 1;
		if (Params.Num() > 1)
			Stack = FCString::Atoi(*Params[1]);

		bool Res = false;
		if(Character->GetBuff(BuffId).Num() > 0)
		{
			for (const FBuffObj* Buff : Character->GetBuff(BuffId))
				if (Buff->Stack >= Stack && Buff->ToBeRemoved == false)
					Res = true;
		}
		return Res;
	}
	return false;
}

bool UMobAIScript::CheckHasBuffStackLess(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	if(Params.Num() > 0)
	{
		const FString BuffId = Params[0];

		int Stack = 1;
		if (Params.Num() > 1)
			Stack = FCString::Atoi(*Params[1]);

		bool Res = true;
		if(Character->GetBuff(BuffId).Num() > 0)
		{
			for (const FBuffObj* Buff : Character->GetBuff(BuffId))
			{
				if (Buff->Stack >= Stack && Buff->ToBeRemoved == false)
					return false;
			}
		}
		return Res;
	}
	return false;
}

bool UMobAIScript::CheckNotHasBuff(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	if(Params.Num())
	{
		const FString BuffId = Params[0];
		if(Character->GetBuff(BuffId).Num() <= 0)
			return true;
	}
	return false;
}

bool UMobAIScript::CheckMoveToNextPathNode(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	FPathNodeQueue PathNodeQueue = AIComponent->PathNodeQueue;
	if (PathNodeQueue.Nodes.Num() > 0 && PathNodeQueue.NodeIndex >= 0)
	{
		if (FVector2D::DistSquared(FVector2D(Character->GetActorLocation()),
			FVector2D(PathNodeQueue.Nodes[PathNodeQueue.NodeIndex])) > 50*50)
				return true;
	}
	
	return false;
}

bool UMobAIScript::CheckMoveToNextPossiblePathNode(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	FPathNodeQueue PathNodeQueue = AIComponent->PathNodeQueue;

	if (PathNodeQueue.Nodes.Num()>0&& PathNodeQueue.NodeIndex >= 0)
	{
		FVector TargetLoc = PathNodeQueue.Nodes[PathNodeQueue.NodeIndex];
		//角色root 加上半身高
		TargetLoc.Z += Character->GetCapsuleComponent()->GetScaledCapsuleHalfHeight();
		float MinDistance = Character->GetCapsuleComponent()->GetScaledCapsuleRadius();
		if (FVector2D::DistSquared(FVector2D(Character->GetActorLocation()),
			FVector2D(AIComponent->GetTargetNavPathEndPoint(TargetLoc)))
			>  MinDistance*MinDistance)
		{
			return true;
		}
		else
		{
			AIComponent->PathNodeQueue.NextNode();
		}
	}
	return false;
}

bool UMobAIScript::CheckHPLess(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	if(Params.Num())
	{
		const float CheckPercent = FCString::Atof(*Params[0]);
		return Character->HealthPercentage() <= CheckPercent;
	}
	return false;
}

bool UMobAIScript::CheckHPGreater(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	if(Params.Num())
	{
		const float CheckPercent = FCString::Atof(*Params[0]);
		return Character->HealthPercentage() > CheckPercent;
	}
	return false;
}

bool UMobAIScript::CheckSelfOnGround(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	return Character->OnGround();
}

bool UMobAIScript::CheckSelfInSky(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{

	return  !Character->OnGround();
}

bool UMobAIScript::CheckArmed(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	return Character->GetArmState() == EArmState::Armed;
}

bool UMobAIScript::CheckUnarmed(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	return Character->GetArmState() == EArmState::Unarmed;
}

bool UMobAIScript::CheckRogueHasActionCanUse(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	//策划输入数据
	FString MobId = Character->MobClassId;
	return GetMobActionWeight(Character, AIComponent, MobId).Num() > 0;
}

bool UMobAIScript::CheckHasActionCanUseSvl(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	FString MobId = Character->MobClassId;
	AAwCharacter* Target = GetClosestTauntingPlayer(AIComponent,0);
	if(!Target) return false;
	if(Target && Target->Dead(true)) return false;
	return GetMobActionWeightSvl(Character, AIComponent, MobId, Target).Num() > 0;
}

bool UMobAIScript::CheckHasActionCanUse_NPC(AAwCharacter* Character, UAwAIComponent* AIComponent,
	TArray<FString> Params)
{
	FString MobId = Character->MobClassId;
	AAwCharacter* Target = AIComponent->GetClosestDistanceEnemy().Character;
	if(!Target) return false;
	if(Target && Target->Dead(true)) return false;
	return GetMobActionWeightSvl(Character, AIComponent, MobId, Target).Num() > 0;
}

bool UMobAIScript::CheckRogueMobHpLessThanBuffStack(AAwCharacter* Character, UAwAIComponent* AIComponent,
                                                    TArray<FString> Params)
{
	if(Params.Num() > 0)
	{
		float BuffStack = Character->GetBuffStackTotal(Params[0]);
		float CheckHPPercent = BuffStack / 100;
		return Character->HealthPercentage() < CheckHPPercent;
	}
	return false;
}

void UMobAIScript::TestAIOnReady(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	//UE_LOG(LogTemp, Log, TEXT("TestAIOnReady"));
}

FAICommand UMobAIScript::DoNothing(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	return FAICommand();
}

FAICommand UMobAIScript::RemoveBuffObj(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	if (Params.Num() <= 0) return FAICommand();
	const FString BuffId = Params[0];
	Character->RemoveBuffById(BuffId);
	return FAICommand();
}

FAICommand UMobAIScript::ModifyFightingWillLevel(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	if (Params.Num() > 0)
		Character->FightingWill.Level = FCString::Atoi(*Params[0]);
	return FAICommand();
}

FAICommand UMobAIScript::AIMoveToLocation(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	FVector TargetLoc;
	if(Params.Num())
	{
		TargetLoc.InitFromString(Params[0]);
	}
	TArray<FVector> LocList;
	LocList.Add(TargetLoc);
	FAICommand NewCommand = AIComponent->CreateMoveToCommand(LocList, 0);
	return NewCommand;
}

FAICommand UMobAIScript::AIMoveToPlayer(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	FVector TargetLoc = UGameplayFuncLib::GetAwGameState()->GetMyCharacter()->GetActorLocation();
	TArray<FVector> LocList;
	LocList.Add(TargetLoc);
	FAICommand NewCommand = AIComponent->CreateMoveToCommand(LocList, 0);
	return NewCommand;
}

bool UMobAIScript::CheckEnemyInRange(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	AAwCharacter* Target = AIComponent->GetClosestDistanceEnemy().Character;
	return  CheckEnemyInRangeBase(Character,Target,AIComponent,Params);
}

bool UMobAIScript::CheckEnemyInRangeBase(AAwCharacter* MyCharacter,AAwCharacter* Target, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	if (Target)
	{
		float CheckDis = 5000;
		const float CurDis = FVector::Dist2D(MyCharacter->GetActorLocation(), Target->GetActorLocation());
		if (Params.Num() == 1)
		{
			CheckDis = GetMoreDistanceWhileHaveTarget(FCString::Atof(*Params[0]),AIComponent);;
			if (CurDis <= CheckDis)
				return true;
		}
		else if(Params.Num() == 2)
		{
			float CheckMinDis = FCString::Atof(*Params[0]);
			CheckDis = FCString::Atof(*Params[1]);
			if(CurDis <= CheckDis && CurDis >= CheckMinDis)
				return true;
		}
	}
	if (AIComponent->GetTargetEnemy().Character)
		AIComponent->SetTargetEnemy(FAIFindChaInfo());
	return false;
}

bool UMobAIScript::CheckPlayerInRangeSvl(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	AAwCharacter* Target = AIComponent->GetClosestDistancePlayer_Svl();
	return CheckEnemyInRangeBase(Character,Target, AIComponent, Params);
}

FAICommand UMobAIScript::AIMoveToAthena(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	auto gs = UGameplayFuncLib::GetAwGameStateSurvivor();
	if (!gs)return FAICommand();
	FVector TargetLoc;
	AIComponent->SetTargetEnemy(FAIFindChaInfo());
	if (gs->AthenaTower)
		TargetLoc = gs->AthenaTower->GetActorLocation();
	TArray<FVector> LocList;
	LocList.Add(TargetLoc);
	FAICommand NewCommand = AIComponent->CreateMoveToCommand(LocList, 0);
	return NewCommand;
}

FAICommand UMobAIScript::AIMoveToAthenaAndSearchEnemy(AAwCharacter* Character, UAwAIComponent* AIComponent,
	TArray<FString> Params)
{
	// auto gs = UGameplayFuncLib::GetAwGameStateSurvivor();
	// if (!gs)return FAICommand();
	float distance = GetMoreDistanceWhileHaveTarget(Params.Num()>0?FCString::Atof(*Params[0]):0,AIComponent);
	AAwCharacter* Target = GetClosestTauntingPlayer(AIComponent,distance);
	if (!Target)return FAICommand();
	FVector TargetLoc = Target->GetActorLocation();
	AIComponent->SetTargetEnemy(FAIFindChaInfo());
	TArray<FVector> LocList;
	LocList.Add(TargetLoc);
	FAICommand NewCommand = AIComponent->CreateMoveToCommand(LocList, 0);
	return NewCommand;
}

bool UMobAIScript::AIIsOverTaunt(AAwCharacter* TargetCharacter)
{
	return  TargetCharacter->GetFollowingEnemyCount() > 10;
}

FAICommand UMobAIScript::AIMoveToClosetEnemy(AAwCharacter* Character, UAwAIComponent* AIComponent,
                                             TArray<FString> Params)
{
	AAwCharacter* ClosetEnemy = AIComponent->GetClosestDistanceEnemy().Character;
	if(ClosetEnemy)
	{
		FVector TargetLoc = UGameplayFuncLib::GetPointOnGround(ClosetEnemy);
		//Character Root点在中心，所以获得地面坐标后要加上HalfHeight
		TargetLoc.Z += ClosetEnemy->GetCapsuleComponent()->GetScaledCapsuleHalfHeight();
		TArray<FVector> LocList;
		LocList.Add(TargetLoc);
		FAICommand NewCommand = AIComponent->CreateMoveToCommand(LocList, 0);
		return NewCommand;
	}
	return FAICommand();
}


FAICommand UMobAIScript::AIMoveToClosetViewedEnemy(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	FVector TargetLoc;
	FAIFindChaInfo Target = AIComponent->GetClosestDistanceEnemy(true);
	if (Target.Character)
	{
		TargetLoc = UGameplayFuncLib::GetPointOnGround(Target.Character);
		//Character Root点在中心，所以获得地面坐标后要加上HalfHeight
		TargetLoc.Z += Target.Character->GetCapsuleComponent()->GetScaledCapsuleHalfHeight();
		TArray<FVector> LocList;
		LocList.Add(TargetLoc);
		FAICommand NewCommand = AIComponent->CreateMoveToCommand(LocList, 0);
		return NewCommand;
	}
	return FAICommand();
}

FAICommand UMobAIScript::AIMoveAroundClosetViewedEnemy(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	float MinRadius = 0;
	float MaxRadius = 0;
	bool bNegativeDegree = false;
	MaxRadius = AIComponent->MaxDisWhenMoveAround;
	MinRadius = AIComponent->MinDisWhenMoveAround;
	bNegativeDegree = AIComponent->bNegativeDegreeWhenMoveAround;
	FVector TargetLoc;
	FAIFindChaInfo Target = AIComponent->GetClosestDistanceEnemy(true);
	if (Target.Character)
	{
		TargetLoc = UGameplayFuncLib::GetPointOnGround(Target.Character);
		//Character Root点在中心，所以获得地面坐标后要加上HalfHeight
		TargetLoc.Z += Target.Character->GetCapsuleComponent()->GetScaledCapsuleHalfHeight();
	}
	else
	{
		TargetLoc = Character->GetActorLocation() + Character->GetActorForwardVector() * (MinRadius + MaxRadius) * 0.5f;
	}
	FAICommand NewCommand = AIComponent->CreateMoveAroundCommand(TargetLoc, MinRadius, MaxRadius, bNegativeDegree);
	return NewCommand;
}

FAICommand UMobAIScript::MoveToNextPathNode(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	FPathNodeQueue PathNodeQueue = AIComponent->PathNodeQueue;
	
	if (PathNodeQueue.Nodes.Num() == 0)
		return FAICommand();

	FVector TargetLoc = PathNodeQueue.Nodes[PathNodeQueue.NodeIndex];

	if (FVector2D::DistSquared(FVector2D(Character->GetActorLocation()),FVector2D(TargetLoc)) <= 100*100)
	{
		AIComponent->PathNodeQueue.NextNode();
		if(Params.Num())
		{
			FAICommand NewCommand = AIComponent->CreateUseActionCommand(Params[FMath::RandRange(0,Params.Num()-1)],
				Character->GetActorForwardVector());
			return NewCommand;
		}
	}
	
	//Character Root点在中心，所以获得地面坐标后要加上HalfHeight
	TargetLoc.Z += Character->GetCapsuleComponent()->GetScaledCapsuleHalfHeight();
	
	TArray<FVector> LocList;
	LocList.Add(TargetLoc);
	
	FAICommand NewCommand = AIComponent->CreateMoveToCommand(LocList, 0);
	
	return NewCommand;
}

FAICommand UMobAIScript::MoveToNextPossiblePathNode(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	FPathNodeQueue PathNodeQueue = AIComponent->PathNodeQueue;

	if (PathNodeQueue.Nodes.Num() == 0)
		return FAICommand();
	
	int SpeedLevel = 2;
	if (Params.Num() > 0)
		SpeedLevel = FCString::Atoi(*Params[0]);
	
	FVector TargetLoc = PathNodeQueue.Nodes[PathNodeQueue.NodeIndex];
	float MinDistance = Character->GetCapsuleComponent()->GetScaledCapsuleRadius()*2;
	TargetLoc.Z += Character->GetCapsuleComponent()->GetScaledCapsuleHalfHeight();
	
	if(FVector2D::DistSquared(FVector2D(Character->GetActorLocation()),
		FVector2D(AIComponent->GetTargetNavPathEndPoint(TargetLoc))) <= MinDistance * MinDistance)
	{
		AIComponent->PathNodeQueue.NextNode();
		if (Params.Num())
		{
			FAICommand NewCommand = AIComponent->CreateUseActionCommand(Params[FMath::RandRange(1, Params.Num() - 1)],FVector::ZeroVector);
			return NewCommand;
		}
	}
	
	TArray<FVector> LocList;
	LocList.Add(TargetLoc);

	FAICommand NewCommand = AIComponent->CreateMoveToCommand(LocList, 0, SpeedLevel);

	return NewCommand;
}

FAICommand UMobAIScript::MoveToProtectedSceneItemAround(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{

	FAICommand NewCommand = FAICommand();

	TArray<FString> ProtectBuffIDArray = { "ProtectSceneItem" };
	if (!CheckHasBuff(Character, AIComponent, ProtectBuffIDArray))
	{
		return NewCommand;
	}
	TArray<FBuffObj*> ResultBuffs;
	ResultBuffs = Character->GetBuff("ProtectSceneItem");
	if (!ResultBuffs.IsValidIndex(0))
	{
		return NewCommand;
	}
	

	FString UID = *(ResultBuffs[0]->Param.Find("SceneItemId"));
	AActor* Target = *(UGameplayFuncLib::GetAwGameState()->SceneItemList.Find(UID));

	if (!Target->IsValidLowLevel())
	{
		return NewCommand;
	}

	//速度调节相关
	int SpeedLevel = 2;
	float SpeedLimitRange = 1000;
	int LowSpeedLevel = 1;
	int HighSpeedLevel = 2;
	if (Params.IsValidIndex(0))
		SpeedLimitRange = FCString::Atof(*Params[0]);
	if (Params.IsValidIndex(1))
		LowSpeedLevel = FCString::Atoi(*Params[1]);
	if (Params.IsValidIndex(2))
		HighSpeedLevel = FCString::Atoi(*Params[2]);

	FVector Offset;
	Offset.InitFromString(*ResultBuffs[0]->Param.Find("FollowOffset"));
	FVector TargetLocation = Target->GetActorLocation()+Offset;


	TArray<FVector> LocList;
	LocList.Add(TargetLocation);

	SpeedLevel = FVector::Distance(Character->GetActorLocation(),TargetLocation)>SpeedLimitRange?HighSpeedLevel:LowSpeedLevel;
	NewCommand = AIComponent->CreateMoveToCommand(LocList, 0, SpeedLevel);

	return NewCommand;
}

FAICommand UMobAIScript::AIDoAction(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	FAICommand NewCommand = FAICommand();
	if (Params.Num())
	{
		NewCommand = AIComponent->CreateUseActionCommand(Params[0], Character->GetActorForwardVector());
	}
	return NewCommand;
}

FAICommand UMobAIScript::AIDoRandomAction(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	FAICommand NewCommand = FAICommand();
	if (Params.Num())
	{
		const int RandomIndex = FMath::RandRange(0, Params.Num() - 1);
		NewCommand = AIComponent->CreateUseActionCommand(Params[RandomIndex], Character->GetActorForwardVector());
	}
	return NewCommand;
}

FAICommand UMobAIScript::AITurnToClosetEnemyInRangeDoAction(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	FAICommand NewCommand = FAICommand();
	FAIFindChaInfo ClosetTarget = FAIFindChaInfo();
	for (TTuple<AAwCharacter*, FString> FindCharacter : UGameplayFuncLib::GetAwGameState()->AllCharacters)
	{
		if(FindCharacter.Key == nullptr) continue;
		if(FindCharacter.Key != Character && !FindCharacter.Key->Dead(true) && Character->IsEnemy(FindCharacter.Key))
		{
			const float CurDisXY = FVector::Dist2D(FindCharacter.Key->GetActorLocation(), Character->GetActorLocation());
			const float CurDisZ = FMath::Abs(FindCharacter.Key->GetActorLocation().Z - Character->GetActorLocation().Z);
			float CheckMaxDisXY = 0;
			float CheckMinDisXY = 0;
			if(Params.Num() > 1)
			{
				CheckMaxDisXY = FCString::Atof(*Params[1]);
				CheckMinDisXY = FCString::Atof(*Params[0]);
			}
			const float CheckDisZ = AIComponent->GetSightZRadius();
			if((CurDisXY < CheckMaxDisXY) && (CurDisXY >= CheckMinDisXY) && (CurDisZ < CheckDisZ))
			{
				const float CurDis = FVector::Dist2D(FindCharacter.Key->GetActorLocation(), Character->GetActorLocation());
				if(!ClosetTarget.Character)
				{
					ClosetTarget.Character = FindCharacter.Key;
					ClosetTarget.DistanceXY = CurDis;
				}
				else if(ClosetTarget.DistanceXY > CurDis)
				{
					ClosetTarget.Character = FindCharacter.Key;
					ClosetTarget.DistanceXY = CurDis;
				}
			}
		}
	}
	if(ClosetTarget.Character)
	{
		AIComponent->SetTargetEnemy(ClosetTarget);
		AIComponent->TargetDirFunc = UDataFuncLib::SplitFuncNameAndParams("MobAIScript.GetCurTargetEnemyDir()");
		NewCommand = AIComponent->CreateUseActionCommand(Params[2], Character->GetActorForwardVector());
	}
	return NewCommand;
}

FAICommand UMobAIScript::AITurnToClosetEnemyInRangeDoRandomAction(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	FAICommand NewCommand = FAICommand();
	FAIFindChaInfo ClosetTarget = FAIFindChaInfo();
	for (TTuple<AAwCharacter*, FString> FindCharacter : UGameplayFuncLib::GetAwGameState()->AllCharacters)
	{
		if(FindCharacter.Key == nullptr) continue;
		if(FindCharacter.Key != Character && !FindCharacter.Key->Dead(true) && Character->IsEnemy(FindCharacter.Key))
		{
			const float CurDisXY = FVector::Dist2D(FindCharacter.Key->GetActorLocation(), Character->GetActorLocation());
			const float CurDisZ = FMath::Abs(FindCharacter.Key->GetActorLocation().Z - Character->GetActorLocation().Z);
			float CheckMaxDisXY = 0;
			float CheckMinDisXY = 0;
			if(Params.Num() > 1)
			{
				CheckMaxDisXY = FCString::Atof(*Params[1]);
				CheckMinDisXY = FCString::Atof(*Params[0]);
			}
			const float CheckDisZ = AIComponent->GetSightZRadius();
			if((CurDisXY < CheckMaxDisXY) && (CurDisXY >= CheckMinDisXY) && (CurDisZ < CheckDisZ))
			{
				const float CurDis = FVector::Dist2D(FindCharacter.Key->GetActorLocation(), Character->GetActorLocation());
				if(!ClosetTarget.Character)
				{
					ClosetTarget.Character = FindCharacter.Key;
					ClosetTarget.DistanceXY = CurDis;
				}
				else if(ClosetTarget.DistanceXY > CurDis)
				{
					ClosetTarget.Character = FindCharacter.Key;
					ClosetTarget.DistanceXY = CurDis;
				}
			}
		}
	}
	if(ClosetTarget.Character)
	{
		AIComponent->SetTargetEnemy(ClosetTarget);
		AIComponent->TargetDirFunc = UDataFuncLib::SplitFuncNameAndParams("MobAIScript.GetCurTargetEnemyDir()");
		const int RandomIndex = FMath::RandRange(2, Params.Num() - 1);
		NewCommand = AIComponent->CreateUseActionCommand(Params[RandomIndex], Character->GetActorForwardVector());
	}
	return NewCommand;
}

FAICommand UMobAIScript::AITurnToClosetViewedEnemyDoAction(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	FAICommand NewCommand = FAICommand();
	FAIFindChaInfo ClosetTarget = FAIFindChaInfo();
	TArray<FAIFindChaInfo> FindedTargets = AIComponent->GetAllViewedCharacter(false, true);
	for (FAIFindChaInfo FindedTarget : FindedTargets)
	{
		if(!ClosetTarget.Character)
			ClosetTarget = FindedTarget;
		else if(FindedTarget.DistanceXY < ClosetTarget.DistanceXY)
			ClosetTarget = FindedTarget;
	}
	if(ClosetTarget.Character)
	{
		AIComponent->SetTargetEnemy(ClosetTarget);
		AIComponent->TargetDirFunc = UDataFuncLib::SplitFuncNameAndParams("MobAIScript.GetCurTargetEnemyDir()");
		NewCommand = AIComponent->CreateUseActionCommand(Params[0], Character->GetActorForwardVector());
	}
	return NewCommand;
}

FAICommand UMobAIScript::AITurnToPlayerThenDoAction(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	FAICommand NewCommand = FAICommand();
	AAwCharacter* PlayerCha = UGameplayFuncLib::GetAwGameState()->GetMyCharacter();
	if(PlayerCha)
	{
		FAIFindChaInfo MyTarget = FAIFindChaInfo();
		MyTarget.Character = PlayerCha;
		AIComponent->SetTargetEnemy(MyTarget);
		AIComponent->TargetDirFunc = UDataFuncLib::SplitFuncNameAndParams("MobAIScript.GetCurTargetEnemyDir()");
		NewCommand = AIComponent->CreateUseActionCommand(Params[0], Character->GetActorForwardVector());
	}
	return NewCommand;
}

FAICommand UMobAIScript::AITurnToClosetViewedEnemyInRangeDoAction(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	FAICommand NewCommand = FAICommand();
	FAIFindChaInfo ClosetTarget = FAIFindChaInfo();
	TArray<FAIFindChaInfo> FindedTargets = AIComponent->GetAllViewedCharacter(false, true);
	for (FAIFindChaInfo FindedTarget : FindedTargets)
	{
		if (FindedTarget.Character)
		{
			const float CurDisXY = FindedTarget.DistanceXY;
			const float CurDisZ = FindedTarget.DistanceZ;
			float CheckMaxDisXY = 0;
			float CheckMinDisXY = 0;
			if(Params.Num() > 1)
			{
				CheckMaxDisXY = FCString::Atof(*Params[1]);
				CheckMinDisXY = FCString::Atof(*Params[0]);
			}
			
			const float CheckDisZ = AIComponent->GetSightZRadius();
			if((CurDisXY <= CheckMaxDisXY) && (CurDisXY >= CheckMinDisXY) && (CurDisZ < CheckDisZ))
			{
				if(!ClosetTarget.Character)
					ClosetTarget = FindedTarget;
				else if(FindedTarget.DistanceXY < ClosetTarget.DistanceXY)
					ClosetTarget = FindedTarget;
			}
		}
	}
	if(ClosetTarget.Character)
	{
		AIComponent->SetTargetEnemy(ClosetTarget);
		AIComponent->TargetDirFunc = UDataFuncLib::SplitFuncNameAndParams("MobAIScript.GetCurTargetEnemyDir()");
		NewCommand = AIComponent->CreateUseActionCommand(Params[2], Character->GetActorForwardVector());
	}
	return NewCommand;
}

FAICommand UMobAIScript::AITurnToClosetViewedEnemyInDisAndDegreeRangeDoAction(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	FAICommand NewCommand = FAICommand();
	FAIFindChaInfo ClosetTarget = FAIFindChaInfo();
	TArray<FAIFindChaInfo> FindedTargets = AIComponent->GetAllViewedCharacter(false, true);
	for (FAIFindChaInfo FindedTarget : FindedTargets)
	{
		if (FindedTarget.Character)
		{
			const float CurDisXY = FindedTarget.DistanceXY;
			const float CurDisZ = FindedTarget.DistanceZ;
			float CheckMaxDisXY = 0;
			float CheckMinDisXY = 0;
			if(Params.Num() > 1)
			{
				CheckMaxDisXY = FCString::Atof(*Params[1]);
				CheckMinDisXY = FCString::Atof(*Params[0]);
			}
			const float CheckDisZ = AIComponent->GetSightZRadius();
			if((CurDisXY <= CheckMaxDisXY) && (CurDisXY >= CheckMinDisXY) && (CurDisZ < CheckDisZ))
			{
				if(Params.Num() > 3)
				{
					const float CheckMinDegree = FCString::Atof(*Params[2]);
					const float CheckMaxDegree = FCString::Atof(*Params[3]);
					FVector TargetDir = FindedTarget.Character->GetActorLocation() - Character->GetActorLocation();
					TargetDir.Normalize();
					FVector2D CurDir2D = FVector2D(Character->GetActorForwardVector().X, Character->GetActorForwardVector().Y);
					CurDir2D.Normalize();
					FVector2D TargetDir2D = FVector2D(TargetDir.X, TargetDir.Y);
					TargetDir2D.Normalize();
					const float CurDegree = UKismetMathLibrary::DegAcos(FVector::DotProduct(FVector(CurDir2D, 0), FVector(TargetDir2D, 0)));
					if(CurDegree >= CheckMinDegree && CurDegree <= CheckMaxDegree)
					{
						if(!ClosetTarget.Character)
							ClosetTarget = FindedTarget;
						else if(FindedTarget.DistanceXY < ClosetTarget.DistanceXY)
							ClosetTarget = FindedTarget;
					}
				}
			}
		}
	}
	if(ClosetTarget.Character)
	{
		AIComponent->SetTargetEnemy(ClosetTarget);
		AIComponent->TargetDirFunc = UDataFuncLib::SplitFuncNameAndParams("MobAIScript.GetCurTargetEnemyDir()");
		NewCommand = AIComponent->CreateUseActionCommand(Params[4], Character->GetActorForwardVector());
	}
	return NewCommand;
}

FAICommand UMobAIScript::AITurnToStimulateDoAction(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	FAICommand NewCommand = FAICommand();
	FVector TargetLoc = Character->GetActorForwardVector() + Character->GetActorLocation();
	if(Character->GetAIComponent()->GetMostThreatTarget())
	{
		TargetLoc = Character->GetAIComponent()->GetMostThreatTarget()->GetActorLocation();
	}
	else if(Character->GetAIComponent()->GetAllViewedCharacter().Num())
	{
		FAIFindChaInfo TargetInfo = Character->GetAIComponent()->GetClosestDistanceEnemy(true,false);
		if(TargetInfo.Character)
			TargetLoc = TargetInfo.Character->GetActorLocation();
	}
	else if(Character->GetAIComponent()->Stimuli_AllySignal.Num())
	{
		TargetLoc = Character->GetAIComponent()->Stimuli_AllySignal.Last().SignalOrigin.GetLocation();
	}
	else if(Character->GetAIComponent()->Stimuli_Voice.Num())
	{
		TargetLoc = Character->GetAIComponent()->Stimuli_Voice.Last().Position;
	}
	TArray<FVector> LocList;
	LocList.Add(TargetLoc);
	NewCommand = AIComponent->CreateMoveToCommand(LocList,0);
	// if (Params.Num())
	// {
	// 	FVector CmdPos = AIComponent->GetTargetDirection();
	// 	NewCommand = AIComponent->CreateUseActionCommand(Params[0], CmdPos);
	// 	AIComponent->TargetDirFunc = UDataFuncLib::SplitFuncNameAndParams("MobAIScript.GetStimulateDir()");
	// }
	return NewCommand;
}

FAICommand UMobAIScript::DeleteMostPrioityStimulate(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	if(Character->GetAIComponent()->Stimuli_Offended.Num())
	{
		Character->GetAIComponent()->Stimuli_Offended.RemoveAt(Character->GetAIComponent()->Stimuli_Offended.Num() - 1);
		return FAICommand();
	}
	if(Character->GetAIComponent()->Stimuli_AllySignal.Num())
	{
		Character->GetAIComponent()->Stimuli_AllySignal.RemoveAt(Character->GetAIComponent()->Stimuli_AllySignal.Num() - 1);
		return FAICommand();
	}
	if(Character->GetAIComponent()->Stimuli_Voice.Num())
	{
		Character->GetAIComponent()->Stimuli_Voice.RemoveAt(Character->GetAIComponent()->Stimuli_Voice.Num() - 1);
		return FAICommand();
	}
	return FAICommand();
}

FAICommand UMobAIScript::SetComboTag(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	if(Params.Num())
	{
		AIComponent->ClearUseActionTags();
		AIComponent->AddUseActionTag(Params[0]);
	}
	return FAICommand();
}

TArray<FAIPickActionCandidate> UMobAIScript::GetMobActionWeightBase(AAwCharacter* Target, AAwCharacter* Character,
	UAwAIComponent* AIComponent, FString MobId)
{
	TArray<FAIPickActionCandidate> NowMayActions;
		//策划输入数据
	FRogueAIPickActionInfo AIActionValue = UGameplayFuncLib::GetAwDataManager()->GetRogueAIPickActionInfo(MobId);
	if(AIActionValue.Actions.Num())
	{
		//计算所有动作的权重
		for (FRogueAIPickActionData Action : AIActionValue.Actions)
		{
			//查看是否在CD
			if(AIComponent->ActionCDList.Contains(Action.ActionId))
				continue;
			float CurWeight = Action.BaseWeight;
			//计算距离权重
			const float CurDis = FVector::Dist2D(Character->GetActorLocation(), Target->GetActorLocation());
			if(Action.DistanceWeightList.Num() > 0)
			{
				for (FRogueAIRangeWeightInfo DistanceWeightInfo : Action.DistanceWeightList)
				{
					if(CurDis >= DistanceWeightInfo.MinRange && CurDis <= DistanceWeightInfo.MaxRange)
						CurWeight += DistanceWeightInfo.Weight;
				}
			}

			//计算血量权重
			if(Action.HPWeightList.Num() > 0)
			{
				const float CurHP = Character->CharacterObj.CurrentRes.HP;
				const float MaxHP = Character->CharacterObj.CurProperty.HP;
				const float CurHPPercent = CurHP / MaxHP;
				for (FRogueAIRangeWeightInfo HPWeightInfo : Action.HPWeightList)
				{
					if(CurHPPercent >= HPWeightInfo.MinRange && CurHPPercent <= HPWeightInfo.MaxRange)
						CurWeight += HPWeightInfo.Weight;
				}
			}

			//计算敌人位置角度的权重
			FVector TargetDir = Target->GetActorLocation() - Character->GetActorLocation();
			TargetDir.Normalize();
			const float TargetToSelfDegree = UMathFuncLib::GetDegreeBetweenTwoVector(Character->GetActorForwardVector(), TargetDir);
			//UKismetSystemLibrary::PrintString(Character, FString("Degree : ").Append(FString::SanitizeFloat(TargetToSelfDegree)));
			for (FRogueAIRangeWeightInfo DegreeWeight : Action.TargetInDegreeWeight)
			{
				if(TargetToSelfDegree >= DegreeWeight.MinRange && TargetToSelfDegree <= DegreeWeight.MaxRange)
					CurWeight += DegreeWeight.Weight;
			}

			//检测指定Buff是否到达指定层数
			if(Action.BuffWeightList.Num())
			{
				for (FRogueAICheckBuffInfo BuffInfo : Action.BuffWeightList)
				{
					TArray<FBuffObj*> GotBuff = Character->GetBuff(BuffInfo.BuffId);
					int TotalStack = 0;
					for (FBuffObj* Buff : GotBuff)
					{
						if(Buff->ToBeRemoved == false)
							TotalStack += Buff->Stack;
					}
					if(TotalStack >= BuffInfo.BuffStack)
						CurWeight += BuffInfo.Weight;
				}
			}

			//检测指定Switch的Value区间的权重
			if(Action.SwitchWeight.Num())
			{
				for (FRogueAISwitchWeightInfo SwitchInfo : Action.SwitchWeight)
				{
					UAwRogueDataSystem* SubSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>();
					if (SubSystem)
					{
						int SwitchValue = SubSystem->GetSwitch(SwitchInfo.SwitchId);
						if(SwitchValue >= SwitchInfo.MinValue && SwitchValue <= SwitchInfo.MaxValue)
							CurWeight += SwitchInfo.Weight;
					}
				}
			}

			//检测地面空中
			if(Target->OnGround())
			{
				CurWeight += Action.TargetOnGroundWeight;
			}
			else
			{
				CurWeight += Action.TargetInAirWeight;
			}

			//检测翻滚
			if(Target->GetActionComponent()->IsDodgeAction())
			{
				CurWeight += Action.TargetInDodgeWeight;
			}

			//检测是否正在受击
			if(Target->IsHurtAction(*Target->GetActionComponent()->CurrAction()))
			{
				CurWeight += Action.TargetInHurtWeight;
			}

			//检测是否通关（当前世界难度>0）
			if(UGameplayFuncLib::GetAwGameInstance()->Rogue_ChallengeList.Num() > 0)
			{
				CurWeight += Action.bClearedGameWeight;
			}

			//检测是否在摄像机外
			FVector PlayerCameraDir = UGameplayFuncLib::GetAwGameState()->GetMyCharacter()->GetAwCameraComponent()->GetCameraRotate().Vector();
			if(FMath::Abs(UMathFuncLib::GetDegreeBetweenTwoVector(TargetDir, PlayerCameraDir)) < 120 &&
				CurDis > 250)
			{
				CurWeight += Action.bOutofCameraWeight;
			}
			// if(Character->WasRecentlyRendered() == false)
			// {
			// 	CurWeight += Action.bOutofCameraWeight;
			// }
			
			//当权重>0，就视为有效
			if(CurWeight > 0)
			{
				NowMayActions.Add(FAIPickActionCandidate(Action.ActionId, CurWeight, Action.bWaitAction));
			}
		}
	}
	return NowMayActions;
}

TArray<FAIPickActionCandidate> UMobAIScript::GetMobActionWeightSvl(AAwCharacter* Character, UAwAIComponent* AIComponent,
	FString MobId, AAwCharacter* Target)
{
	TArray<FAIPickActionCandidate> NowMayActions;
	if(!Target) return NowMayActions;
	if(Target && Target->Dead(true)) return NowMayActions;

	return GetMobActionWeightBase(Target,Character,AIComponent,MobId);
}

TArray<FAIPickActionCandidate> UMobAIScript::GetNPCActionWeightSvl(AAwCharacter* Character, UAwAIComponent* AIComponent,
	FString MobId, AAwCharacter* Target)
{
	TArray<FAIPickActionCandidate> NowMayActions;
	if(!Target) return NowMayActions;
	if(Target && Target->Dead(true)) return NowMayActions;

	return GetMobActionWeightBase(Target,Character,AIComponent,MobId);
}

AAwCharacter* UMobAIScript::GetClosestTauntingPlayer(UAwAIComponent* AIComponent,float distance)
{
	auto gs = UGameplayFuncLib::GetAwGameStateSurvivor();
	if (!gs)return UGameplayFuncLib::GetLocalAwPlayerCharacter(0);
	AAwCharacter* Target = Target = gs->AthenaTower;
	
	float clostestDistance = FMathf::MaxReal;
	if (Target)
	{
		clostestDistance= FVector::DistSquared2D(Target->GetActorLocation(), AIComponent->GetOwner()->GetActorLocation());
	}
	for ( auto tempTarget :gs->GetPlayerCharacters()) 
	{
		if (tempTarget == gs->AthenaTower) //除了塔
			// || AIIsOverTaunt(tempTarget)) //没OT的人
			continue;
		float charaDist = FVector::DistSquared2D(tempTarget->GetActorLocation(), AIComponent->GetOwner()->GetActorLocation());
		if ((distance<=0 || charaDist<distance*distance) && charaDist<clostestDistance )
		{
			Target = tempTarget;
		}
	}
	return Target;
}

TArray<FAIPickActionCandidate> UMobAIScript::GetMobActionWeight(AAwCharacter* Character, UAwAIComponent* AIComponent,
                                                                FString MobId)
{
	TArray<FAIPickActionCandidate> NowMayActions;
	// AAwCharacter* Target = UGameplayFuncLib::GetAwGameState()->GetMyCharacter();
	for (auto PC:UGameplayFuncLib::GetAllLocalAwPlayerControllers())
	{
		if(!PC->CurCharacter) return NowMayActions;
		if(PC->CurCharacter && PC->CurCharacter->Dead(true)) return NowMayActions;

		NowMayActions.Append(GetMobActionWeightBase(PC->CurCharacter,Character,AIComponent,MobId));
	}
	return NowMayActions;
	// return GetMobActionWeightBase(Target,Character,AIComponent,MobId);
}

FAICommand UMobAIScript::RogueMobBasicBattle(AAwCharacter* Character, UAwAIComponent* AIComponent,
	TArray<FString> Params)
{
	FAICommand Cmd = FAICommand();
	AAwCharacter* Target = nullptr;
	bool AllDead = true;
	for (auto PC:UGameplayFuncLib::GetAllLocalAwPlayerControllers())
	{
		Target = PC->CurCharacter;
		if(!Target) continue;
		if(Target && Target->Dead(true))continue;
		
		AllDead = false;
		break;
	}
	if (AllDead)return Cmd;
	FString MobId = Character->MobClassId;
	TArray<FAIPickActionCandidate> NowMayActions = GetMobActionWeight(Character, AIComponent, MobId);
	return  MobBasicBattle(Target,NowMayActions,Character,AIComponent);
}

FAICommand UMobAIScript::SurvivorMobBasicBattle(AAwCharacter* Character, UAwAIComponent* AIComponent,
	TArray<FString> Params)
{
	FAICommand Cmd = FAICommand();
	// auto gs = UGameplayFuncLib::GetAwGameStateSurvivor();
	// if (!gs)return FAICommand();
	float distance = Params.Num()>0?FCString::Atof(*Params[0]):0;
	AAwCharacter* Target = GetClosestTauntingPlayer(AIComponent,distance);
	if(!Target) return Cmd;
	if(Target && Target->Dead(true)) return Cmd;
	
	FString MobId = Character->MobClassId;
	TArray<FAIPickActionCandidate> NowMayActions = GetMobActionWeightSvl(Character, AIComponent, MobId, Target);
	return  MobBasicBattle(Target,NowMayActions,Character,AIComponent);
}

FAICommand UMobAIScript::SurvivorNpcBasicBattle(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	FAICommand Cmd = FAICommand();
	auto gs = UGameplayFuncLib::GetAwGameStateSurvivor();
	if (!gs)return FAICommand();
	float distance = Params.Num()>0?FCString::Atof(*Params[0]):0;
	AAwCharacter* Target = AIComponent->GetClosestDistanceEnemy().Character;
	if(!Target) return Cmd;
	if(Target && Target->Dead(true)) return Cmd;
	
	FString MobId = Character->MobClassId;
	TArray<FAIPickActionCandidate> NowMayActions = GetNPCActionWeightSvl(Character, AIComponent, MobId, Target);
	return  MobBasicBattle(Target,NowMayActions,Character,AIComponent);
}

FAICommand UMobAIScript::MobBasicBattle(AAwCharacter* Target,TArray<FAIPickActionCandidate> NowMayActions, AAwCharacter* Character, UAwAIComponent* AIComponent)
{
	FAICommand Cmd = FAICommand();
	//策划输入数据
	FString MobId = Character->MobClassId;
	
	float TotalWeight = 0;
	for (FAIPickActionCandidate ActionWeight : NowMayActions)
	{
		TotalWeight += ActionWeight.Rate;
	}
	
	if(TotalWeight > 0)
	{
		float RanRes = FMath::RandRange(0.f, TotalWeight);
		int Index = 0;
		while (Index < NowMayActions.Num() && RanRes > NowMayActions[Index].Rate)
		{
			RanRes -= NowMayActions[Index].Rate;
			Index += 1;
		}
		if(NowMayActions.Num() > Index)
		{
			FAIFindChaInfo Found = FAIFindChaInfo();
			Found.Character = Target;
			AIComponent->SetTargetEnemy(Found);
			AIComponent->TargetDirFunc = UDataFuncLib::SplitFuncNameAndParams("MobAIScript.GetCurTargetEnemyDir()");
			//获取动作CD
			float ActionCD = 0;
			FRogueAIPickActionInfo AIActionValue = UGameplayFuncLib::GetAwDataManager()->GetRogueAIPickActionInfo(MobId);
			for (FRogueAIPickActionData Action : AIActionValue.Actions)
			{
				if(Action.ActionId == NowMayActions[Index].ActionId)
				{
					ActionCD = FMath::RandRange(Action.MinActionCD, Action.MaxActionCD);
					break;
				}
			}
			AIComponent->ActionCDList.Add(NowMayActions[Index].ActionId, ActionCD);
			int MobNum = 0;
			for (TTuple<AAwCharacter*, FString> CharacterInfo : UGameplayFuncLib::GetAwGameState()->AllCharacters)
			{
				if(CharacterInfo.Key)
				{
					if(!CharacterInfo.Key->UnderPlayerControl())
						MobNum++;
				}
			}
			if(NowMayActions[Index].bWaitAction && MobNum > 2)
			{
				int WaitTime = FMath::RandRange(0,2);
				AIComponent->AddWaitForAction(NowMayActions[Index].ActionId, WaitTime);
			}
			else
				Cmd = AIComponent->CreateUseActionCommand(NowMayActions[Index].ActionId, Character->GetActorForwardVector());
		}
	}
	
	return Cmd;
}

FAICommand UMobAIScript::CreateAOEAtTargetLocation(AAwCharacter* Character, UAwAIComponent* AIComponent,
                                                   TArray<FString> Params)
{
	if(Params.Num() > 1)
	{
		UGameplayFuncLib::CreateAOE(Character,Params[0],AIComponent->GetTargetLocation(),
		Character->GetActorForwardVector(),FCString::Atof(*Params[1]),"");
	}
	return FAICommand();
}

FAICommand UMobAIScript::TeleportToTargetBack(AAwCharacter* Character, UAwAIComponent* AIComponent,
	TArray<FString> Params)
{
	FVector BackVector = AIComponent->GetTargetEnemy().Character->GetActorForwardVector() * -1.0f;
	for (int i = 0; i <= 11 ; i++)
	{
		double DivideValue = 0;
		int DivideResult = UKismetMathLibrary::FMod(i,2,DivideValue);
		int CurDegree = 0;
		if(DivideValue == 0)
			CurDegree = DivideResult * 30;
		else
			CurDegree = DivideResult * 30 * -1;
		FVector LineStartLoc =  UKismetMathLibrary::RotateAngleAxis(BackVector, CurDegree, FVector::UpVector) * 200
		+ AIComponent->GetTargetEnemy().Character->GetCharacterGroundLoc() + FVector(0,0,150);
		FVector LineEndLoc = LineStartLoc + FVector(0,0,-150);
		TArray<TEnumAsByte<EObjectTypeQuery>> ObjectTypes;
		ObjectTypes.Add(EObjectTypeQuery::ObjectTypeQuery1);
		FHitResult HitResult = FHitResult();
		UKismetSystemLibrary::CapsuleTraceSingleForObjects(Character, LineStartLoc, LineEndLoc, 50.0f, 100.0f, ObjectTypes,
			false, TArray<AActor*>(), EDrawDebugTrace::None, HitResult, true);
			//,FLinearColor::Red,FLinearColor::Green,15);
		if(HitResult.bBlockingHit)
		{
			FFindFloorResult FloorResult = FFindFloorResult();
			Character->GetCharacterMovement()->FindFloor(HitResult.Location, FloorResult, true);
			if(FloorResult.bWalkableFloor)
			{
				Character->SetActorLocation(HitResult.Location + FVector(0,0,20), false);
				break;
			}
		}
	}
	return FAICommand();
}

FAICommand UMobAIScript::SummonMob(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	if(Params.Num() > 3)
	{
		const int MobLevel = FCString::Atoi(*Params[1]);
		const int SpawnNum = FCString::Atoi(*Params[2]);
		if(SpawnNum <= 0) return FAICommand();
		const FName SpawnPointTag = FName(Params[3]);
		float CheckMinDis = 0;
		float CheckMaxDis = 99999;
		if(Params.Num() > 4)
		{
			CheckMinDis = FCString::Atof(*Params[4]);
			CheckMaxDis = FCString::Atof(*Params[5]);
		}
		FString SpawnFXPath = "";
		if(Params.Num() > 6)
		{
			SpawnFXPath = Params[6];
		}
		TArray<AActor*> SpawnPointList;
		UGameplayStatics::GetAllActorsOfClassWithTag(Character,AActor::StaticClass(),SpawnPointTag,SpawnPointList);
		if(SpawnPointList.Num() >= SpawnNum)
		{
			TArray<AActor*> CanUsePoints;
			for (AActor* Point : SpawnPointList)
			{
				const float PointDis = FVector::Dist2D(Character->GetActorLocation(), Point->GetActorLocation());
				if(PointDis >= CheckMinDis && PointDis <= CheckMaxDis)
				{
					CanUsePoints.Add(Point);
				}
			}
			for (AActor* Point : CanUsePoints)
			{
				SpawnPointList.Remove(Point);
			}
			
			if(CanUsePoints.Num() >= SpawnNum)
			{
				for (int i = 0; i < SpawnNum; i++)
				{
					int RandIndex = FMath::RandRange(0,CanUsePoints.Num() - 1);
					AAwCharacter* Mob = UGameplayFuncLib::CreateCharacterByMobInfo(CanUsePoints[RandIndex]->GetActorTransform(),Params[0], MobLevel);
					if(Mob)
					{
						Mob->PlaySpawnFX();
						if(SpawnFXPath != "")
							UGameplayFuncLib::CreateVFXByPathAtLocation(SpawnFXPath, CanUsePoints[RandIndex]->GetActorTransform());
						CanUsePoints.RemoveAt(RandIndex);
						
						FBuffModel SummonBuffModel = UGameplayFuncLib::GetDataManager()->GetBuffModelById("SummonMobTag");
						FAddBuffInfo AddSummonBuffInfo = FAddBuffInfo(Character, Mob, SummonBuffModel, 1, 0, false, true);
						Mob->AddBuff(AddSummonBuffInfo);
						FBuffModel SummonerBuffModel = UGameplayFuncLib::GetDataManager()->GetBuffModelById("SummonNum");
						FAddBuffInfo AddSummonerBuffInfo = FAddBuffInfo(Character, Character, SummonerBuffModel, 1, 0, false, true);
						Mob->AddBuff(AddSummonerBuffInfo);
					}
				}
			}
			else
			{
				for (int i = 0; i < CanUsePoints.Num(); i++)
				{
					AAwCharacter* Mob = UGameplayFuncLib::CreateCharacterByMobInfo(CanUsePoints[i]->GetActorTransform(),Params[0], MobLevel);
					if(Mob)
					{
						Mob->PlaySpawnFX();
						if(SpawnFXPath != "")
							UGameplayFuncLib::CreateVFXByPathAtLocation(SpawnFXPath, CanUsePoints[i]->GetActorTransform());
						
						FBuffModel SummonBuffModel = UGameplayFuncLib::GetDataManager()->GetBuffModelById("SummonMobTag");
						FAddBuffInfo AddSummonBuffInfo = FAddBuffInfo(Character, Mob, SummonBuffModel, 1, 0, false, true);
						Mob->AddBuff(AddSummonBuffInfo);
						FBuffModel SummonerBuffModel = UGameplayFuncLib::GetDataManager()->GetBuffModelById("SummonNum");
						FAddBuffInfo AddSummonerBuffInfo = FAddBuffInfo(Character, Character, SummonerBuffModel, 1, 0, false, true);
						Mob->AddBuff(AddSummonerBuffInfo);
					}
				}
				int RemainingNum = SpawnNum - CanUsePoints.Num();
				for (int i = 0; i < RemainingNum; i++)
				{
					int RandIndex = FMath::RandRange(0,SpawnPointList.Num() - 1);
					AAwCharacter* Mob = UGameplayFuncLib::CreateCharacterByMobInfo(SpawnPointList[RandIndex]->GetActorTransform(),Params[0], MobLevel);
					if(Mob)
					{
						Mob->PlaySpawnFX();
						if(SpawnFXPath != "")
							UGameplayFuncLib::CreateVFXByPathAtLocation(SpawnFXPath, SpawnPointList[RandIndex]->GetActorTransform());
						SpawnPointList.RemoveAt(RandIndex);

						FBuffModel SummonBuffModel = UGameplayFuncLib::GetDataManager()->GetBuffModelById("SummonMobTag");
						FAddBuffInfo AddSummonBuffInfo = FAddBuffInfo(Character, Mob, SummonBuffModel, 1, 0, false, true);
						Mob->AddBuff(AddSummonBuffInfo);
						FBuffModel SummonerBuffModel = UGameplayFuncLib::GetDataManager()->GetBuffModelById("SummonNum");
						FAddBuffInfo AddSummonerBuffInfo = FAddBuffInfo(Character, Character, SummonerBuffModel, 1, 0, false, true);
						Mob->AddBuff(AddSummonerBuffInfo);
					}
				}
			}
		}
	}
	return FAICommand();
}

FAICommand UMobAIScript::SummonSceneItem(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	if(Params.Num() > 3)
	{
		const int SpawnNum = FCString::Atoi(*Params[1]);
		if(SpawnNum <= 0) return FAICommand();
		const FName SpawnPointTag = FName(Params[2]);
		float CheckMinDis = 0;
		float CheckMaxDis = 99999;
		if(Params.Num() > 4)
		{
			CheckMinDis = FCString::Atof(*Params[3]);
			CheckMaxDis = FCString::Atof(*Params[4]);
		}
		FString SpawnFXPath = "";
		if(Params.Num() > 6)
		{
			SpawnFXPath = Params[6];
		}
		TArray<AActor*> SpawnPointList;
		UGameplayStatics::GetAllActorsOfClassWithTag(Character,AActor::StaticClass(),SpawnPointTag,SpawnPointList);
		if(SpawnPointList.Num() >= SpawnNum)
		{
			TArray<AActor*> CanUsePoints;
			for (AActor* Point : SpawnPointList)
			{
				const float PointDis = FVector::Dist2D(Character->GetActorLocation(), Point->GetActorLocation());
				if(PointDis >= CheckMinDis && PointDis <= CheckMaxDis)
				{
					CanUsePoints.Add(Point);
				}
			}
			for (AActor* Point : CanUsePoints)
			{
				SpawnPointList.Remove(Point);
			}
			
			if(CanUsePoints.Num() >= SpawnNum)
			{
				for (int i = 0; i < SpawnNum; i++)
				{
					int RandIndex = FMath::RandRange(0,CanUsePoints.Num() - 1);
					FSceneItemModel Model = UGameplayFuncLib::GetAwDataManager()->GetSceneItemModelById(Params[0]);
					AAwSceneItem* SceneItem = UGameplayFuncLib::CreateSceneItem(Model,Character->Side,CanUsePoints[RandIndex]->GetActorTransform());
					if(SceneItem)
					{
						SceneItem->SetOwner(Character);
						if(SpawnFXPath != "")
							UGameplayFuncLib::CreateVFXByPathAtLocation(SpawnFXPath, CanUsePoints[RandIndex]->GetActorTransform());
						CanUsePoints.RemoveAt(RandIndex);
					}
				}
			}
			else
			{
				for (int i = 0; i < CanUsePoints.Num(); i++)
				{
					FSceneItemModel Model = UGameplayFuncLib::GetAwDataManager()->GetSceneItemModelById(Params[0]);
					AAwSceneItem* SceneItem = UGameplayFuncLib::CreateSceneItem(Model,Character->Side,CanUsePoints[i]->GetActorTransform());
					if(SceneItem)
					{
						SceneItem->SetOwner(Character);
						if(SpawnFXPath != "")
							UGameplayFuncLib::CreateVFXByPathAtLocation(SpawnFXPath, CanUsePoints[i]->GetActorTransform());
					}
				}
				int RemainingNum = SpawnNum - CanUsePoints.Num();
				for (int i = 0; i < RemainingNum; i++)
				{
					int RandIndex = FMath::RandRange(0,SpawnPointList.Num() - 1);
					FSceneItemModel Model = UGameplayFuncLib::GetAwDataManager()->GetSceneItemModelById(Params[0]);
					AAwSceneItem* SceneItem = UGameplayFuncLib::CreateSceneItem(Model,Character->Side,SpawnPointList[RandIndex]->GetActorTransform());
					if(SceneItem)
					{
						SceneItem->SetOwner(Character);
						if(SpawnFXPath != "")
							UGameplayFuncLib::CreateVFXByPathAtLocation(SpawnFXPath, SpawnPointList[RandIndex]->GetActorTransform());
						SpawnPointList.RemoveAt(RandIndex);
					}
				}
			}
		}
	}
	return FAICommand();
}

FAICommand UMobAIScript::AddBuffToCharacterByTag(AAwCharacter* Character, UAwAIComponent* AIComponent,
                                                 TArray<FString> Params)
{
	const FString BuffId = Params[0];
	const int BuffStack = FCString::Atoi(*Params[1]);
	const int BuffTime = FCString::Atof(*Params[2]);
	const FName CharacterTag = FName(Params[3]);
	TArray<AActor*> CharacterList;
	UGameplayStatics::GetAllActorsOfClassWithTag(Character,AAwCharacter::StaticClass(),CharacterTag,CharacterList);
	for (AActor* FindActor : CharacterList)
	{
		AAwCharacter* FindCharacter = Cast<AAwCharacter>(FindActor);
		if(FindCharacter && !FindCharacter->Dead(true))
		{
			FBuffModel BuffModel = UGameplayFuncLib::GetDataManager()->GetBuffModelById(BuffId);
			FAddBuffInfo AddBuffInfo = FAddBuffInfo(Character, FindCharacter, BuffModel, BuffStack, BuffTime, true, false);
			FindCharacter->AddBuff(AddBuffInfo);
		}
	}
	return FAICommand();
}

FAICommand UMobAIScript::AddBuffToCharacterByBuff(AAwCharacter* Character, UAwAIComponent* AIComponent,
	TArray<FString> Params)
{
	const FString BuffId = Params[0];
	const int BuffStack = FCString::Atoi(*Params[1]);
	const int BuffTime = FCString::Atof(*Params[2]);
	const FString FindBuffId = Params[3];
	const int FindBuffStack = FCString::Atoi(*Params[4]);
	TArray<AActor*> CharacterList;
	UGameplayStatics::GetAllActorsOfClass(Character,AAwCharacter::StaticClass(),CharacterList);
	for (AActor* FindActor : CharacterList)
	{
		AAwCharacter* FindCharacter = Cast<AAwCharacter>(FindActor);
		if(FindCharacter && !FindCharacter->Dead(true))
		{
			TArray<FBuffObj*> GetBuffList = FindCharacter->GetBuff(FindBuffId);
			int GetBuffStack = 0;
			for (FBuffObj* Buff : GetBuffList)
			{
				GetBuffStack += Buff->Stack;
			}
			if(GetBuffStack >= FindBuffStack)
			{
				FBuffModel BuffModel = UGameplayFuncLib::GetDataManager()->GetBuffModelById(BuffId);
				FAddBuffInfo AddBuffInfo = FAddBuffInfo(Character, FindCharacter, BuffModel, BuffStack, BuffTime, true, false);
				FindCharacter->AddBuff(AddBuffInfo);
			}
			if(Params.Num() > 5)
				UGameplayFuncLib::CreateVFXByPathAtLocation(Params[5], FindCharacter->GetActorTransform());
		}
	}
	return FAICommand();
}

FVector UMobAIScript::GetCurTargetEnemyDir(AAwCharacter* Character, TArray<FString> TagParams, TArray<FString> Params)
{
	FAIFindChaInfo TargetEnemy = Character->GetAIComponent()->GetTargetEnemy();
	if(TargetEnemy.Character)
	{
		FVector RotDir = TargetEnemy.Character->GetActorLocation() - Character->GetActorLocation();
		RotDir.Normalize();
		return RotDir;
	}
	return Character->GetActorForwardVector();
}

FVector UMobAIScript::GetClosetDistanceEnemyDir(AAwCharacter* Character, TArray<FString> TagParams, TArray<FString> Params)
{
	FAIFindChaInfo ClosetEnemy = Character->GetAIComponent()->GetClosestDistanceEnemy();
	if(ClosetEnemy.Character)
	{
		FVector RotDir = ClosetEnemy.Character->GetActorLocation() - Character->GetActorLocation();
		RotDir.Normalize();
		return RotDir;
	}
	return Character->GetActorForwardVector();
}

FVector UMobAIScript::GetClosetDegreeEnemyDir(AAwCharacter* Character, TArray<FString> TagParams, TArray<FString> Params)
{
	FAIFindChaInfo ClosetEnemy = Character->GetAIComponent()->GetClosestDegreeEnemy();
	if(ClosetEnemy.Character)
	{
		FVector RotDir = ClosetEnemy.Character->GetActorLocation() - Character->GetActorLocation();
		RotDir.Normalize();
		return RotDir;
	}
	return Character->GetActorForwardVector();
}

FVector UMobAIScript::GetStimulateDir(AAwCharacter* Character, TArray<FString> TagParams, TArray<FString> Params)
{
	FVector TargetDir = Character->GetActorForwardVector();
	if(Character->GetAIComponent()->Stimuli_Offended.Num())
	{
		const FVector TargetLoc = Character->GetAIComponent()->Stimuli_Offended.Last().HitTransform.GetLocation();
		TargetDir = TargetLoc - Character->GetActorLocation();
		TargetDir.Normalize();
		return TargetDir;
	}
	if(Character->GetAIComponent()->Stimuli_AllySignal.Num())
	{
		const FVector TargetLoc = Character->GetAIComponent()->Stimuli_AllySignal.Last().SignalOrigin.GetLocation();
		TargetDir = TargetLoc - Character->GetActorLocation();
		TargetDir.Normalize();
		return TargetDir;
	}
	if(Character->GetAIComponent()->Stimuli_Voice.Num())
	{
		const FVector TargetLoc = Character->GetAIComponent()->Stimuli_Voice.Last().Position;
		TargetDir = TargetLoc - Character->GetActorLocation();
		TargetDir.Normalize();
		return TargetDir;
	}
	return TargetDir;
}

FVector UMobAIScript::GetPlayerDir(AAwCharacter* Character, TArray<FString> TagParams, TArray<FString> Params)
{
	AAwCharacter* Player = UGameplayFuncLib::GetClosestPlayerCharacter(Character->GetActorLocation());
	if(Player)
	{
		FVector RotDir = Player->GetActorLocation() - Character->GetActorLocation();
		RotDir.Z = 0;
		RotDir.Normalize();
		return RotDir;
	}
	return Character->GetActorForwardVector();
}

FAICommand UMobAIScript::AITurnToTargetPointThenDoAction(AAwCharacter* Character, UAwAIComponent* AIComponent,
                                                         TArray<FString> Params)
{
	FAICommand NewCommand = FAICommand();
	
	if(Params.Num())
	{
		TArray<AActor*> TargetPointArray;
		UGameplayStatics::GetAllActorsOfClass(GWorld,ATargetPoint::StaticClass(),TargetPointArray);
		if(Params.Num() > 0)
		{
			if(TargetPointArray.Num() > 0)
			{
				TArray<AActor*> TempTargetPointArray;
				for (AActor* PointArray : TargetPointArray)
				{
					if(UKismetSystemLibrary::GetDisplayName(PointArray) != Params[0])
					{
						TempTargetPointArray.Add(PointArray);
					}
					
				}
				if(TempTargetPointArray.Num() > 0)
				{
					Character->GetAIComponent()->SetTargetLocation(TempTargetPointArray[
					UKismetMathLibrary::RandomIntegerInRange(0,TempTargetPointArray.Num()-1)]->GetActorLocation());
					TempTargetPointArray[UKismetMathLibrary::RandomIntegerInRange(0,TempTargetPointArray.Num()-1)];
				}
				else
				{
					FVector MaxRange = FVector::ZeroVector;
					MaxRange.X = Params.Num()>2?FCString::Atof(*Params[2]):0;
					MaxRange.Y = Params.Num()>3?FCString::Atof(*Params[3]):0;
					MaxRange.Z = Params.Num()>4?FCString::Atof(*Params[4]):0;
				
					FVector TempTargetPointLoc = FVector(
						Character->GetActorLocation().X + UKismetMathLibrary::RandomFloatInRange(-MaxRange.X,MaxRange.X),
						Character->GetActorLocation().Y + UKismetMathLibrary::RandomFloatInRange(-MaxRange.Y,MaxRange.Y),
						Character->GetActorLocation().Z + UKismetMathLibrary::RandomFloatInRange(-MaxRange.Z,MaxRange.Z));
					Character->GetAIComponent()->SetTargetLocation(TempTargetPointLoc);
				}
			}
			else
			{
				FVector MaxRange = FVector::ZeroVector;
				MaxRange.X = Params.Num()>2?FCString::Atof(*Params[2]):0;
				MaxRange.Y = Params.Num()>3?FCString::Atof(*Params[3]):0;
				MaxRange.Z = Params.Num()>4?FCString::Atof(*Params[4]):0;
				
				FVector TempTargetPointLoc = FVector(
					Character->GetActorLocation().X + UKismetMathLibrary::RandomFloatInRange(-MaxRange.X,MaxRange.X),
					Character->GetActorLocation().Y + UKismetMathLibrary::RandomFloatInRange(-MaxRange.Y,MaxRange.Y),
					Character->GetActorLocation().Z + UKismetMathLibrary::RandomFloatInRange(-MaxRange.Z,MaxRange.Z));
				Character->GetAIComponent()->SetTargetLocation(TempTargetPointLoc);
			}
		}
		if(Params.Num() > 1)
		{
			AIComponent->TargetDirFunc = UDataFuncLib::SplitFuncNameAndParams("MobAIScript.GetCurTargetPointDir()");
			NewCommand = AIComponent->CreateUseActionCommand(Params[1], Character->GetActorForwardVector());
		}
	}
	return NewCommand;
}

FVector UMobAIScript::GetCurTargetPointDir(AAwCharacter* Character,TArray<FString> TagParams, TArray<FString> Params)
{
	FVector RotDir = Character->GetAIComponent()->GetTargetLocation() - Character->GetActorLocation();
	RotDir.Normalize();
	return RotDir;
}

FAICommand UMobAIScript::CheckCenterPointThenDoAction(AAwCharacter* Character, UAwAIComponent* AIComponent,
	TArray<FString> Params)
{
	FAICommand NewCommand = FAICommand();
	
	if(Params.Num())
	{
		TArray<AActor*> TargetPointArray;
		UGameplayStatics::GetAllActorsOfClass(GWorld,ATargetPoint::StaticClass(),TargetPointArray);
		if(Params.Num() > 0)
		{
			if(TargetPointArray.Num() > 0)
			{
				
				FVector TargetLocation = FVector::Zero();
				if(Character->GetAIComponent()->GetTargetEnemy().Character)
					TargetLocation = Character->GetAIComponent()->GetTargetEnemy().Character->GetActorLocation();
				for (AActor* PointArray : TargetPointArray)
				{
					if(UKismetSystemLibrary::GetDisplayName(PointArray) == Params[0])
					{
						TargetLocation = PointArray->GetActorLocation();
					}
				}
				Character->GetAIComponent()->SetTargetLocation(TargetLocation);
			}
			else
			{
				if(Character->GetAIComponent()->GetTargetEnemy().Character)
					Character->GetAIComponent()->SetTargetLocation(Character->GetAIComponent()->GetTargetEnemy().Character->GetActorLocation());
			}
		}
		if(Params.Num() > 1)
		{
			AIComponent->TargetDirFunc = UDataFuncLib::SplitFuncNameAndParams("MobAIScript.GetCurTargetPointDir()");
			NewCommand = AIComponent->CreateUseActionCommand(Params[1], Character->GetActorForwardVector());
		}
	}
	return NewCommand;
}

