// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "UObject/Object.h"
#include "AIPickTargetScript.generated.h"

/**
 * AI在做动作时候选择目标的策略
 */
UCLASS()
class THEAWAKENER_FO_API UAIPickTargetScript : public UObject
{
	GENERATED_BODY()
public:
	//强行就选玩家角色（Warning：不建议非测试情况使用）
	UFUNCTION()
	static FVector2D DirectToPlayerCharacter(AAwCharacter* AICha, TArray<FString> Tags, TArray<FString> Params);
};
