// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "TheAwakener_FO/GamePlay/Characters/AI/AwAIComponent.h"
#include "GoblinAIScript.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UGoblinAIScript : public UObject
{
	GENERATED_BODY()
public:
	//Condition

	//在视野中死亡的敌人
	UFUNCTION(BlueprintCallable)
	static bool CheckViewedDeathEnemy(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//检测是否正好脱战
	UFUNCTION(BlueprintCallable)
	static bool CheckJustLeaveBattle(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//检测是否场上有敌人，视野范围内没敌人
	UFUNCTION(BlueprintCallable)
	static bool CheckCanTurn(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//Action
	//清除能吼叫的buff
	UFUNCTION(BlueprintCallable)
	static FAICommand ClearGoblinRoaredBuff(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//进入战斗的吼叫
	//在Params[0]距离内外用不同的连招
	//执行Params[1]的Action
	UFUNCTION(BlueprintCallable)
	static FAICommand GoblinRoarWhenStartBattle(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//AI以视野范围内Params[0]-Params[1]距离，Params[2]-Params[3]角度范围内的最近敌人为目标，
	//执行Action,Params[4]、[5]中随机一个
	UFUNCTION(BlueprintCallable)
	static FAICommand AttackNearViewedEnemy(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//攻击范围内最近的敌人
	//Param[0]-[1]范围内
	//Parmas[2]:敌人在AI的右侧时做的动作；Parmas[3]:敌人在AI的左侧时做的动作
	UFUNCTION(BlueprintCallable)
	static FAICommand AttackClosetNoViewedEnemy(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	
	//设置连招，连招名为ComboDaze
	UFUNCTION(BlueprintCallable)
	static FAICommand ComboDaze01(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	
};
