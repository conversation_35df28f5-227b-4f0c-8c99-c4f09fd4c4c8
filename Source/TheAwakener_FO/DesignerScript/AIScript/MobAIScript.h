// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "TheAwakener_FO/GamePlay/Characters/AI/AwAIComponent.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "MobAIScript.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UMobAIScript : public UObject
{
	GENERATED_BODY()

	//Condition

	//Always
	UFUNCTION(BlueprintCallable)
	static bool Always(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	
	// 默认，传入true就是true，传入false就是false
	UFUNCTION(BlueprintCallable)
	static bool Default(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	// 检测是否有StopAI的Buff
	UFUNCTION(BlueprintCallable)
	static bool CheckStopAI(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	// 检测是否在Params[0]范围内是否有玩家
	UFUNCTION(BlueprintCallable)
	static bool CheckPlayerInRange(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	// 检测是否在Params[0]范围内是否有玩家
	UFUNCTION(BlueprintCallable)
	static bool CheckPlayerOutRange(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	// 检测是否在Params[0]范围内是否有敌人
	UFUNCTION(BlueprintCallable)
	static bool CheckEnemyOutRange(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	
	//检测角色是否脱离战斗状态
	UFUNCTION(BlueprintCallable)
	static bool CheckCharacterNotInWar(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	//检测角色是否在战斗状态
	UFUNCTION(BlueprintCallable)
	static bool CheckCharacterInWar(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//检测AI与保护坐标周围随机点的距离，决定是否还需要走向他
	UFUNCTION(BlueprintCallable)
	static bool CheckMoveToProtectedSceneItemAround(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//检测AI与目标坐标的距离，决定是否走向这个坐标
	UFUNCTION(BlueprintCallable)
	static bool CheckAIMoveToLoc(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	//检测AI与最近的Target的距离，决定是否走向这个Target
	UFUNCTION(BlueprintCallable)
	static bool CheckAIMoveToClosetViewedEnemy(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	//检测AI与最近的Target的距离，决定是否绕着这个Target走路
	UFUNCTION(BlueprintCallable)
	static bool CheckAIMoveAroundClosetViewedEnemy(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	//检测AI在视线的距离范围内有没有敌人
	UFUNCTION(BlueprintCallable)
	static bool CheckHasEnemyInSightRange(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	//检测AI在Params[0] - Params[1]的距离范围内有没有敌人;如果只有Params[0],则检测 0-Params[0]的距离范围
	UFUNCTION(BlueprintCallable)
	static bool CheckHasEnemyInRange(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	//检测AI在视野范围的Params[0] - Params[1]的距离内有没有敌人
	UFUNCTION(BlueprintCallable)
	static bool CheckHasViewedEnemyInRange(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	//检测AI在视野范围的Params[0] - Params[1]的距离，且在AI的Params[2] - Params[3]的角度内，有敌人
	UFUNCTION(BlueprintCallable)
	static bool CheckHasViewedEnemyInDisAndDegreeRange(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	//该AI阵营Character的数量是否小于和该AI为敌的Character数量
	UFUNCTION(BlueprintCallable)
	static bool CheckAINumLessThanPLayerNum(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	
	//检测AI是否在AOE内
	//Params：用来区分AOE类型的Tag，如果为空则检测所有AOE
	UFUNCTION(BlueprintCallable)
	static bool CheckAIInAOE(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	
	//检测AI的FightingWill.Level是否等于Params[0]
	UFUNCTION(BlueprintCallable)
	static bool CheckFightingWillLevelEquals(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	//检测AI的FightingWill.Level是否大于Params[0]
	UFUNCTION(BlueprintCallable)
	static bool CheckFightingWillLevelGreater(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	//检测AI的FightingWill.Level是否小于Params[0]
	UFUNCTION(BlueprintCallable)
	static bool CheckFightingWillLevelLess(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	//检测AI的FightingWill.Value是否大于Params[0]
	UFUNCTION(BlueprintCallable)
	static bool CheckFightingWillValueGreater(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	//检测AI的FightingWill.Value是否小于Params[0]
	UFUNCTION(BlueprintCallable)
	static bool CheckFightingWillValueLess(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//检测AI的是否在这一帧刚刚脱战
	UFUNCTION(BlueprintCallable)
	static bool CheckAIJustLeaveBattle(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	//检测AI的是否有看见敌人
	UFUNCTION(BlueprintCallable)
	static bool CheckStimulateByView(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	//检测AI的是否没有看见敌人
	UFUNCTION(BlueprintCallable)
	static bool CheckNotStimulateByView(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	//检测AI的是否有声音刺激
	UFUNCTION(BlueprintCallable)
	static bool CheckStimulateByHeard(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	//检测AI的是否有受击刺激
	UFUNCTION(BlueprintCallable)
	static bool CheckStimulateByOffended(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	//检测AI的是否有受击刺激
	UFUNCTION(BlueprintCallable)
	static bool CheckStimulateByAllySignal(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	//检测AI的是否有任意一种刺激
	UFUNCTION(BlueprintCallable)
	static bool CheckHasStimulate(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	// 检查是否 拥有某个 Buff
	// Params[0] buffId
	// Params[1] Stack （大于等于这个层数） 默认是0层
	UFUNCTION(BlueprintCallable)
	static bool CheckHasBuff(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	// 检查是否 拥有某个 Buff
	// Params[0] buffId
	// Params[1] Stack （小于等于这个层数） 默认是0层
	//或者没有这个buff
	UFUNCTION(BlueprintCallable)
	static bool CheckHasBuffStackLess(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	// 检查是否 没有某个 Buff
	// Params[0] buffId
	UFUNCTION(BlueprintCallable)
	static bool CheckNotHasBuff(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	// 检查是否前往下一个点巡逻，如果没有PathNodeQueue找到一个最近的一个寻路点的path
	UFUNCTION(BlueprintCallable)
	static bool CheckMoveToNextPathNode(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//检查是否前往下一个点巡逻,如果下一个巡逻点不可达则抵达前往该点的最终可能点并继续巡逻
	UFUNCTION(BlueprintCallable)
	static bool CheckMoveToNextPossiblePathNode(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//AI的HP是否小于Params[0]
	UFUNCTION(BlueprintCallable)
	static bool CheckHPLess(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//AI的HP是否大于Params[0]
	UFUNCTION(BlueprintCallable)
	static bool CheckHPGreater(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//检测自身是否处于地面状态
	UFUNCTION(BlueprintCallable)
	static bool CheckSelfOnGround(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//检测自身是否处于空中状态
	UFUNCTION(BlueprintCallable)
	static bool CheckSelfInSky(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	
	//检测是否处于拔刀状态
	UFUNCTION(BlueprintCallable)
	static bool CheckArmed(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//检测是否处于收刀状态
	UFUNCTION(BlueprintCallable)
	static bool CheckUnarmed(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//Rogue 检测是否所有动作都在CD (只针对单人玩家)
	UFUNCTION(BlueprintCallable)
	static bool CheckRogueHasActionCanUse(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	
	//Rogue 检测是否所有动作都在CD (对最近的玩家)
	UFUNCTION(BlueprintCallable)
	static bool CheckHasActionCanUseSvl(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	//Rogue 检测是否所有动作都在CD (用于NPC检测怪物)
	UFUNCTION(BlueprintCallable)
	static bool CheckHasActionCanUse_NPC(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//RogueBoss
	//检测血量百分比是否小于Params[0]的Buff层数
	UFUNCTION(BlueprintCallable)
	static bool CheckRogueMobHpLessThanBuffStack(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//OnReady
	UFUNCTION(BlueprintCallable)
	static void TestAIOnReady(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	

	//Action

	// 什么都不做
	UFUNCTION(BlueprintCallable)
	static FAICommand DoNothing(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	// 删除角色身上所有这个 id 的 buff
	// BuffId = Params[0];
	UFUNCTION(BlueprintCallable)
	static FAICommand RemoveBuffObj(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	
	//【测试】强行修改FightingWill的等级
	UFUNCTION(BlueprintCallable)
	static FAICommand ModifyFightingWillLevel(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	
	//AI向目标敌人移动
	UFUNCTION(BlueprintCallable)
	static FAICommand AIMoveToLocation(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//AI向目标敌人移动
	UFUNCTION(BlueprintCallable)
	static FAICommand AIMoveToPlayer(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);


	
	// 检测是否在Params[0]范围内是否有玩家
	UFUNCTION(BlueprintCallable)
	static bool CheckEnemyInRange(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	UFUNCTION(BlueprintCallable)
	static bool CheckEnemyInRangeBase(AAwCharacter* MyCharacter,AAwCharacter* Target, UAwAIComponent* AIComponent, TArray<FString> Params);
	UFUNCTION(BlueprintCallable)
	static bool CheckPlayerInRangeSvl(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	
	//AI向塔（雅典娜）移动
	UFUNCTION(BlueprintCallable)
	static FAICommand AIMoveToAthena(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	//AI向塔（雅典娜）移动，顺路打最近的人
	UFUNCTION(BlueprintCallable)
	static FAICommand AIMoveToAthenaAndSearchEnemy(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	//AI 判断是否拉了太多怪
	UFUNCTION(BlueprintCallable)
	static bool AIIsOverTaunt(AAwCharacter* TargetCharacter);
	//AI向最近的敌人移动
	UFUNCTION(BlueprintCallable)
	static FAICommand AIMoveToClosetEnemy(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//AI向目标敌人移动
	UFUNCTION(BlueprintCallable)
	static FAICommand AIMoveToClosetViewedEnemy(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	//AI围绕着目标敌人移动
	UFUNCTION(BlueprintCallable)
	static FAICommand AIMoveAroundClosetViewedEnemy(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	// 向下一个巡逻点移动,Params里填写在到达一个巡逻点后可以随机使用的发呆动作
	UFUNCTION(BlueprintCallable)
	static FAICommand MoveToNextPathNode(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	
	//尽可能向下一个巡逻点移动,如果无法抵达则在下一个点寻路路线终点后继续巡逻
	UFUNCTION(BlueprintCallable)
	static FAICommand MoveToNextPossiblePathNode(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//前往保护物品周围
	UFUNCTION(BlueprintCallable)
	static FAICommand MoveToProtectedSceneItemAround(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//AI执行Action,Params[0]为ActionId,播放动画
	UFUNCTION(BlueprintCallable)
	static FAICommand AIDoAction(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//AI随机执行一个Action,Params为ActionId列表
	UFUNCTION(BlueprintCallable)
	static FAICommand AIDoRandomAction(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//AI以Params[0]-Params[1]范围内最近敌人为目标，
	//执行Action,Params[2]为ActionId
	UFUNCTION(BlueprintCallable)
	static FAICommand AITurnToClosetEnemyInRangeDoAction(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//AI以Params[0]-Params[1]范围内最近敌人为目标，
	//执行Action,Params[2]为ActionId
	UFUNCTION(BlueprintCallable)
	static FAICommand AITurnToClosetEnemyInRangeDoRandomAction(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//AI以视野范围内最近敌人为目标，
	//执行Action,Params[0]为ActionId
	UFUNCTION(BlueprintCallable)
	static FAICommand AITurnToClosetViewedEnemyDoAction(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//AI以视野范围内Params[0]-Params[1]距离范围内的最近敌人为目标，
	//执行Action,Params[2]为ActionId
	UFUNCTION(BlueprintCallable)
	static FAICommand AITurnToClosetViewedEnemyInRangeDoAction(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//AI以视野范围内Params[0]-Params[1]距离，Params[2]-Params[3]角度范围内的最近敌人为目标，
	//执行Action,Params[4]为ActionId
	UFUNCTION(BlueprintCallable)
	static FAICommand AITurnToClosetViewedEnemyInDisAndDegreeRangeDoAction(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//转向玩家角色，然后执行Action [0]ActionId
	UFUNCTION(BlueprintCallable)
	static FAICommand AITurnToPlayerThenDoAction(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//AI执行Action,Params[0]为ActionId,播放动画
	UFUNCTION(BlueprintCallable)
	static FAICommand AITurnToStimulateDoAction(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	
	//删除目前优先级最高的一个刺激源
	UFUNCTION(BlueprintCallable)
	static FAICommand DeleteMostPrioityStimulate(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//设置连招名
	UFUNCTION(BlueprintCallable)
	static FAICommand SetComboTag(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	
	//Rogue 获取该怪物当前可用的各个Action权重(如果在CD，则不计入)
	static TArray<FAIPickActionCandidate> GetMobActionWeightBase(AAwCharacter* Target, AAwCharacter* Character, UAwAIComponent* AIComponent, FString MobId);
	//Rogue 获取该怪物当前可用的各个Action权重(如果在CD，则不计入)(只针对单人玩家)
	static TArray<FAIPickActionCandidate> GetMobActionWeight(AAwCharacter* Character, UAwAIComponent* AIComponent, FString MobId);
	//Survivor 获取该怪物当前可用的各个Action权重(如果在CD，则不计入)
	static TArray<FAIPickActionCandidate> GetMobActionWeightSvl(AAwCharacter* Character, UAwAIComponent* AIComponent, FString MobId, AAwCharacter* Target);
	//Survivor 获取该NPC当前可用的各个Action权重(如果在CD，则不计入)
	static TArray<FAIPickActionCandidate> GetNPCActionWeightSvl(AAwCharacter* Character, UAwAIComponent* AIComponent, FString MobId, AAwCharacter* Target);

	//Survivor 获取当前可用目标（计算仇恨）
	//return TowerAthena if no player character meet condition
	static AAwCharacter* GetClosestTauntingPlayer(UAwAIComponent* AIComponent,float distance);
	
	//Rogue 怪物 战斗模组（计算动作的权重并进行随机）（只检测玩家）
	UFUNCTION(BlueprintCallable)
	static FAICommand RogueMobBasicBattle(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	
	//Survivor 怪物 战斗模组（计算动作的权重并进行随机） 检测范围内有效的敌人
	UFUNCTION(BlueprintCallable)
	static FAICommand SurvivorMobBasicBattle(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	//Survivor 怪物 战斗模组（计算动作的权重并进行随机） 检测范围内有效的敌人
	UFUNCTION(BlueprintCallable)
	static FAICommand SurvivorNpcBasicBattle(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	
	//Rogue 怪物 战斗模组（计算动作的权重并进行随机）
	static FAICommand MobBasicBattle(AAwCharacter* Target,TArray<FAIPickActionCandidate> NowMayActions, AAwCharacter* Character, UAwAIComponent* AIComponent);

	//在Save的TargetLocation生成Aoe
	//Params[0]:AoeId   Params[1]:LifeSpan
	UFUNCTION(BlueprintCallable)
	static FAICommand CreateAOEAtTargetLocation(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//AI瞬移到目标的背后
	UFUNCTION(BlueprintCallable)
	static FAICommand TeleportToTargetBack(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//AI召唤Mob
	//Params[0]:MobId
	//Params[1]:MobLevel
	//Params[2]:SummonNum
	//Params[3]:SpawnPointTag
	//Params[4]:SpawnMinDis
	//Params[5]:SpawnMaxDis
	//Params[6]:SpawnFXPath
	UFUNCTION(BlueprintCallable)
	static FAICommand SummonMob(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//AI召唤SceneItem
	//Params[0]:SceneItemId
	//Params[2]:SummonNum
	//Params[3]:SpawnPointTag
	//Params[4]:SpawnMinDis
	//Params[5]:SpawnMaxDis
	//Params[6]:SpawnFXPath
	UFUNCTION(BlueprintCallable)
	static FAICommand SummonSceneItem(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//查找带有Tag的Character,添加Buff
	//Params[0]:BuffId
	//Params[1]:BuffStack
	//Params[2]:BuffTime
	//Params[3]:CharacterTag
	UFUNCTION(BlueprintCallable)
	static FAICommand AddBuffToCharacterByTag(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//查找带有某个Buff的Character,添加Buff
	//Params[0]:BuffId
	//Params[1]:BuffStack
	//Params[2]:BuffTime
	//Params[3]:FindBuffId
	//Params[4]:FindBuffStack
	//Params[5]:SpawnVFXPath
	UFUNCTION(BlueprintCallable)
	static FAICommand AddBuffToCharacterByBuff(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	
	//【DirectionFunc In Action】 (在播放Montage时，让确定AI旋转方向的Func)
	UFUNCTION(BlueprintCallable)
	static FVector GetCurTargetEnemyDir(AAwCharacter* Character, TArray<FString> TagParams, TArray<FString> Params);

	UFUNCTION(BlueprintCallable)
	static FVector GetClosetDistanceEnemyDir(AAwCharacter* Character, TArray<FString> TagParams, TArray<FString> Params);

	UFUNCTION(BlueprintCallable)
	static FVector GetClosetDegreeEnemyDir(AAwCharacter* Character, TArray<FString> TagParams, TArray<FString> Params);
	
	UFUNCTION(BlueprintCallable)
	static FVector GetStimulateDir(AAwCharacter* Character, TArray<FString> TagParams, TArray<FString> Params);

	UFUNCTION(BlueprintCallable)
	static FVector GetPlayerDir(AAwCharacter* Character, TArray<FString> TagParams, TArray<FString> Params);


	//AI位移到目标点，查询场景中所有 TargetPoint 并随机出一个点为目标点
	//Params[0]需要忽略的点，一般为场景中心点
	//Params[1] ActionId 想要做的动作
	//Params[2] X Params[3] Y Params[4] Z 场景中没有 TargetPoint 让 AI 以自身为中心向周围随机出点位的范围值为 +-
	UFUNCTION(BlueprintCallable) 
	static FAICommand AITurnToTargetPointThenDoAction(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//AI转向至到AIComponent下的 GetTargetLocation 的方向
	UFUNCTION(BlueprintCallable)
	static FVector GetCurTargetPointDir(AAwCharacter* Character, TArray<FString> TagParams, TArray<FString> Params);

	//Params[0]需要查询的点，一般为场景中心点，没有就查询玩家位置并做动作
	//Params[1] ActionId 要做的动作
	UFUNCTION(BlueprintCallable)
	static FAICommand CheckCenterPointThenDoAction(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

private:
	static float GetMoreDistanceWhileHaveTarget(float distance,UAwAIComponent* AIComponent)
	{
		if (AIComponent->GetTargetEnemy().Character)
			distance = distance * 1.5;
		return distance;
	}
};