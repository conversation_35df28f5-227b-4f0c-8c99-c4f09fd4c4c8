// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "TheAwakener_FO/GamePlay/Characters/AI/AwAIComponent.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "WereRatAIScript.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UWereRatAIScript : public UObject
{
	GENERATED_BODY()
	//Condition

	//检测AIComponent上是否有已吼叫的的Param
	UFUNCTION(BlueprintCallable)
	static bool CheckRatCanRoar(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//检测AI上是否有生气的Buff
	UFUNCTION(BlueprintCallable)
	static bool CheckAngry(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//检测是否看见奶酪aoe
	UFUNCTION(BlueprintCallable)
	static bool CheckViewedCheeseAOE(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//检测是否可以捡起奶酪
	UFUNCTION(BlueprintCallable)
	static bool CheckCanGetCheese(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//检测是否正好脱战
	UFUNCTION(BlueprintCallable)
	static bool CheckJustLeaveBattle(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//检测是否有刺激源
	UFUNCTION(BlueprintCallable)
	static bool CheckHasStimulate(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//Action

	//向最近的奶酪AOE移动
	UFUNCTION(BlueprintCallable)
	static FAICommand AIMoveToNearestCheese(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//生成一个炸弹Attach在手上
	UFUNCTION(BlueprintCallable)
	static FAICommand SpawnBombAttachToHand(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//进入战斗的吼叫
	//在Params[0]距离内，执行Params[1]的Action
	//在Params[0]距离外，执行Params[2]的Action
	UFUNCTION(BlueprintCallable)
	static FAICommand WereRatRoarWhenStartBattle(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	// 向着视野内Params[0]-Params[1]距离范围的一个最近的目标，
	// 如果在Params[2]内，进行Params[3]；如果在Params[2]外，进行Params[3]
	UFUNCTION(BlueprintCallable)
	static FAICommand RunToViewedClosetEnemy(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//清除鼠人能吼叫的buff
	UFUNCTION(BlueprintCallable)
	static FAICommand ClearWereRatRoaredBuff(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
};
