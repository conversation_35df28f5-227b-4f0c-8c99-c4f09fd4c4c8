// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "UObject/Object.h"
#include "AIComboAction.generated.h"

/**
 * AI“连招”的回调函数，这是被AIPostCommand所使用的函数库
 * 负责的是根据角色当前的AIUseActionTags来决策下一招要放什么ActionId（例如"Action1")
 */
UCLASS()
class THEAWAKENER_FO_API UAIComboAction : public UObject
{
	GENERATED_BODY()
public:
	//【测试】就试着放参数0的Action
	UFUNCTION()
	static FString UseParam0Action(AAwCharacter* Cha, TArray<FString> UseActionTags, TArray<FString> Params);

	//检测AIComponent的UseActionTags是否包含Params[0]，如果包含，返回Params[1]
	UFUNCTION()
	static FString CheckComboTagUseAction(AAwCharacter* Cha, TArray<FString> UseActionTags, TArray<FString> Params);

	//检测AIComponent的UseActionTags是否包含Params[0]，如果包含，返回Params[1]
	UFUNCTION()
	static FString CheckComboTagUseRandomAction(AAwCharacter* Cha, TArray<FString> UseActionTags, TArray<FString> Params);

	//检测AIComponent的UseActionTags是否包含Params[0]
	//检测AIComponent的TargetEnemy是否在角色的视野范围内
	//如果TargetEnemy与当前角色的距离小于Params[1],返回Params[2]
	//如果TargetEnemy与当前角色的距离大于等于Params[1],返回Params[3]
	UFUNCTION()
	static FString CheckComboTagAndViewedTargetDis(AAwCharacter* Cha, TArray<FString> UseActionTags, TArray<FString> Params);

	//检测AIComponent的UseActionTags是否包含Params[0]
	//如果TargetEnemy与当前角色的距离小于Params[1],返回Params[2]
	//如果TargetEnemy与当前角色的距离大于等于Params[1],返回Params[3]
	UFUNCTION()
	static FString CheckComboTagAndTargetDis(AAwCharacter* Cha, TArray<FString> UseActionTags, TArray<FString> Params);
};
