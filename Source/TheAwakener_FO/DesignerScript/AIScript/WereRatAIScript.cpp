// Fill out your copyright notice in the Description page of Project Settings.


#include "WereRatAIScript.h"

#include "Kismet/KismetMathLibrary.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"

bool UWereRatAIScript::CheckRatCanRoar(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	if (Character->GetBuff("WereRat_HasRoared", TArray<AAwCharacter*>()).Num() <= 0)
		return true;
	return false;
}

bool UWereRatAIScript::CheckAngry(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	if (Character->GetBuff("WereRat_Angry", TArray<AAwCharacter*>()).Num() > 0)
		return true;
	return false;
}

bool UWereRatAIScript::CheckViewedCheeseAOE(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	if(Character->GetBuff("WereRat_EatedCheese",TArray<AAwCharacter*>()).Num() > 0)
		return false;
	for (TTuple<AAWAoe*, FString> CurAOE : UGameplayFuncLib::GetAwGameState()->AOEList)
	{
		if (CurAOE.Key)
		{
			//检测是否在视野范围内
			if(AIComponent->CheckLocationInViewRange(CurAOE.Key->GetActorLocation()))
			{
				if (CurAOE.Key->Tags.Contains("Cheese"))
					return true;
			}
		}
	}
	return false;
}

bool UWereRatAIScript::CheckCanGetCheese(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	if(Character->GetBuff("WereRat_EatedCheese",TArray<AAwCharacter*>()).Num() > 0)
		return false;
	if (Character->GetBuff("CanGetCheese", TArray<AAwCharacter*>()).Num())
		return true;
	return false;
}

bool UWereRatAIScript::CheckJustLeaveBattle(AAwCharacter* Character, UAwAIComponent* AIComponent,TArray<FString> Params)
{
	if(AIComponent->JustClearStimulus)
		return true;
	return false;
}

bool UWereRatAIScript::CheckHasStimulate(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	return ((AIComponent->GetAllViewedCharacter().Num() > 0 && AIComponent->GetClosestDistanceEnemy(true,false).Character)
			|| AIComponent->Stimuli_Voice.Num() > 0 || AIComponent->Stimuli_Offended.Num() > 0 || AIComponent->Stimuli_AllySignal.Num() > 0);
}

FAICommand UWereRatAIScript::AIMoveToNearestCheese(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	float MinDis = 0;
	AAWAoe* ClosetAOE = nullptr;
	for (auto& CurAOE : UGameplayFuncLib::GetAwGameState()->AOEList)
	{
		if(CurAOE.Key)
		{
			//检测是否在视野范围内
			if(AIComponent->CheckLocationInViewRange(CurAOE.Key->GetActorLocation()))
			{
				if (CurAOE.Key->Tags.Contains("Cheese"))
				{
					float CurDis = FVector::DistSquared(Character->GetActorLocation(), CurAOE.Key->GetActorLocation());
					if (!ClosetAOE)
					{
						ClosetAOE = CurAOE.Key;
						MinDis = CurDis;
					}
					else
					{
						if (CurDis < MinDis)
						{
							ClosetAOE = CurAOE.Key;
							MinDis = CurDis;
						}
					}
				}
			}
		}
	}
	if (ClosetAOE)
	{
		TArray<FVector> LocList;
		LocList.Add(ClosetAOE->GetActorLocation());
		FAICommand NewCommand = AIComponent->CreateMoveToCommand(LocList, 0);
		return NewCommand;
	}
	return FAICommand();
}

FAICommand UWereRatAIScript::SpawnBombAttachToHand(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	const FBulletModel BulletModel = UGameplayFuncLib::GetDataManager()->GetBulletModelById(Params[0]);
	if (BulletModel.Id.IsEmpty()) return FAICommand();

	USceneComponent* AttBox = Character->GetAttackHitBoxByName(Params[1]);
	if (!AttBox) return FAICommand();

	const FBulletLauncher Launcher = FBulletLauncher(Character, BulletModel,
		AttBox->GetComponentLocation(), AttBox->GetOwner()->GetActorRotation().Vector(), 5,"",
		AIComponent->GetTargetEnemy().Character ? AIComponent->GetTargetEnemy().Character->GetActorLocation() : Character->GetActorLocation()
	);
	AAwBullet* Bullet = UGameplayFuncLib::CreateBullet(Launcher);
	if(Bullet)
	{
		Bullet->AttachToComponent(AttBox,FAttachmentTransformRules::KeepRelativeTransform);
	}
	return FAICommand();
}

FAICommand UWereRatAIScript::WereRatRoarWhenStartBattle(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	FAICommand NewCommand = FAICommand();
	if(Params.Num() > 2)
	{
		const float CheckDis = FCString::Atof(*Params[0]);
		FAIFindChaInfo ClosetTarget = FAIFindChaInfo();
		TArray<FAIFindChaInfo> FindedTargets = AIComponent->GetAllViewedCharacter(false, true);
		for (FAIFindChaInfo FindedTarget : FindedTargets)
		{
			if(!ClosetTarget.Character)
				ClosetTarget = FindedTarget;
			else if(FindedTarget.DistanceXY < ClosetTarget.DistanceXY)
				ClosetTarget = FindedTarget;
		}
		if(ClosetTarget.Character)
		{
			AIComponent->SetTargetEnemy(ClosetTarget);
			AIComponent->TargetDirFunc = UDataFuncLib::SplitFuncNameAndParams("MobAIScript.GetCurTargetEnemyDir()");
			AIComponent->ClearUseActionTags();
			AIComponent->AddUseActionTag("StartBattleRoarCombo");
			if(ClosetTarget.DistanceXY < CheckDis)
				NewCommand = AIComponent->CreateUseActionCommand(Params[1], Character->GetActorForwardVector());
			else
				NewCommand = AIComponent->CreateUseActionCommand(Params[2], Character->GetActorForwardVector());
			AIComponent->SetHalfSightDegree(180);
			AIComponent->SetLoseHalfSightDegree(180);
		}
	}
	return NewCommand;
}

FAICommand UWereRatAIScript::RunToViewedClosetEnemy(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	FAICommand NewCommand = FAICommand();
	FAIFindChaInfo ClosetTarget = FAIFindChaInfo();
	TArray<FAIFindChaInfo> FindedTargets = AIComponent->GetAllViewedCharacter(false, true);
	for (FAIFindChaInfo FindedTarget : FindedTargets)
	{
		if (FindedTarget.Character)
		{
			const float CurDisXY = FindedTarget.DistanceXY;
			const float CurDisZ = FindedTarget.DistanceZ;
			float CheckMaxDisXY = 0;
			float CheckMinDisXY = 0;
			if(Params.Num() > 1)
			{
				CheckMaxDisXY = FCString::Atof(*Params[1]); 
				CheckMinDisXY = FCString::Atof(*Params[0]);
			}
			
			const float CheckDisZ = AIComponent->StopZDisWhenMoveFollow;
			if((CurDisXY <= CheckMaxDisXY) && (CurDisXY >= CheckMinDisXY) && (CurDisZ < CheckDisZ))
			{
				if(!ClosetTarget.Character)
					ClosetTarget = FindedTarget;
				else if(FindedTarget.DistanceXY < ClosetTarget.DistanceXY)
					ClosetTarget = FindedTarget;
			}
		}
	}
	if(ClosetTarget.Character)
	{
		AIComponent->SetTargetEnemy(ClosetTarget);
		AIComponent->TargetDirFunc = UDataFuncLib::SplitFuncNameAndParams("MobAIScript.GetCurTargetEnemyDir()");
		if(Params.Num() > 4)
		{
			const float CheckFarDis = FCString::Atof(*Params[2]);
			if(ClosetTarget.DistanceXY < CheckFarDis)
				NewCommand = AIComponent->CreateUseActionCommand(Params[3], Character->GetActorForwardVector());
			else
				NewCommand = AIComponent->CreateUseActionCommand(Params[4], Character->GetActorForwardVector());
		}
	}
	return NewCommand;
}

FAICommand UWereRatAIScript::ClearWereRatRoaredBuff(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	FBuffModel BuffModel = UGameplayFuncLib::GetDataManager()->GetBuffModelById("WereRat_HasRoared");
	if(BuffModel.Id != "")
	{
		FAddBuffInfo BuffInfo = FAddBuffInfo(Character, Character, BuffModel, -1, 5, false,true);
		Character->AddBuff(BuffInfo);
	}
	AIComponent->SetHalfSightDegree(AIComponent->MobModel.PerceptionProp.SightHalfAngleDregee);
	AIComponent->SetLoseHalfSightDegree(AIComponent->MobModel.PerceptionProp.LoseSightHalfAngleDregee);
	return FAICommand();
}

