// Fill out your copyright notice in the Description page of Project Settings.


#include "AIComboAction.h"

FString UAIComboAction::UseParam0Action(AAwCharacter* Cha, TArray<FString> UseActionTags, TArray<FString> Params)
{
	return Params.Num() > 0 ? Params[0] : "";
}

FString UAIComboAction::CheckComboTagUseAction(AAwCharacter* Cha, TArray<FString> UseActionTags, TArray<FString> Params)
{
	if(Params.Num() > 1)
	{
		if(UseActionTags.Contains(Params[0]))
		{
			return Params[1];
		}
	}
	return "";
}

FString UAIComboAction::CheckComboTagUseRandomAction(AAwCharacter* Cha, TArray<FString> UseActionTags, TArray<FString> Params)
{
	if(Params.Num() > 1)
	{
		if(UseActionTags.Contains(Params[0]))
		{
			return Params[FMath::<PERSON>Range(1,Params.Num() -1)];
		}
	}
	return "";
}

FString UAIComboAction::CheckComboTagAndViewedTargetDis(AAwCharacter* Cha, TArray<FString> UseActionTags, TArray<FString> Params)
{
	if(Params.Num() > 0)
	{
		if(UseActionTags.Contains(Params[0]))
		{
			if(Cha->GetAIComponent()->GetTargetEnemy().Character != nullptr)
			{
				//检测是否在视野范围
				if(Cha->GetAIComponent()->CheckActualOutViewRange(Cha->GetAIComponent()->GetTargetEnemy().Character) == false)
				{
					float CheckDis = 0;
					if(Params.Num() > 2)
					{
						CheckDis = FCString::Atof(*Params[1]);
						const float CurDis = FVector::Dist(Cha->GetAIComponent()->GetTargetEnemy().Character->GetActorLocation(), Cha->GetActorLocation());
						if(CurDis < CheckDis)
							return Params[2];
						else
						{
							if(Params.Num() > 3)
								return Params[3];
						}
					}
				}
				if(Params.Num() > 3)
					return Params[3];
			}
		}
	}
	return "";
}

FString UAIComboAction::CheckComboTagAndTargetDis(AAwCharacter* Cha, TArray<FString> UseActionTags, TArray<FString> Params)
{
	if(Params.Num() > 0)
	{
		if(UseActionTags.Contains(Params[0]))
		{
			if(Cha->GetAIComponent()->GetTargetEnemy().Character != nullptr)
			{
				//检测是否在视野范围
				float CheckDis = 0;
				if(Params.Num() > 2)
				{
					CheckDis = FCString::Atof(*Params[1]);
					const float CurDis = FVector::Dist(Cha->GetAIComponent()->GetTargetEnemy().Character->GetActorLocation(), Cha->GetActorLocation());
					if(CurDis < CheckDis)
						return Params[2];
					else
					{
						if(Params.Num() > 3)
							return Params[3];
					}
				}
				
				if(Params.Num() > 3)
					return Params[3];
			}
		}
	}
	return "";
}
