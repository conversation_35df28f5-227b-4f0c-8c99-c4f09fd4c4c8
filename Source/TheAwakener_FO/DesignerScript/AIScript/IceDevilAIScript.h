// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "TheAwakener_FO/GamePlay/Characters/AI/AwAIComponent.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "Kismet/GameplayStatics.h"
#include "IceDevilAIScript.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UIceDevilAIScript : public UObject
{
	GENERATED_BODY()
	//【Condition】

	//确认AI是否在亢奋状态
	UFUNCTION(BlueprintCallable)
	static bool CheckAIExcited(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//确认AI是否在疲劳状态
	UFUNCTION(BlueprintCallable)
	static bool CheckAITired(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//确认AI是否不在疲劳状态
	UFUNCTION(BlueprintCallable)
	static bool CheckAINotTired(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	
	//AI身上的憎恨buff层数是否大于等于Param[0]层
	UFUNCTION(BlueprintCallable)
	static bool CheckHateBuffStack(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//AI血量是否小于等于 Param[0] %
	UFUNCTION(BlueprintCallable)
	static bool CheckAIHpPercentLess(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//确认是否有二阶段BUFF
	UFUNCTION(BlueprintCallable)
	static bool CheckNotHasSecondStageBuff(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//确认是否有要播放进入亢奋的动作
	UFUNCTION(BlueprintCallable)
	static bool CheckDoRangeStageAction(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);
	
	//检测视野中的存在3个以上的敌人，已这些敌人的中心点为检测中心，是否有2个以上的敌人在中心的Params[0]范围内
	UFUNCTION(BlueprintCallable)
	static bool CheckCanBunchEnemies(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//检测视野中的只存在2个的敌人，且这2个敌人的距离小于Params[0]
	UFUNCTION(BlueprintCallable)
	static bool CheckViewedEnemysDistanceBetween(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//检测视野范围内只有1名敌人，并且敌人的距离小于Params[0]
	UFUNCTION(BlueprintCallable)
	static bool CheckViewedEnemyDistance(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//检测已AI自己为中心，Params[0]范围有Params[1]名以上的敌人
	UFUNCTION(BlueprintCallable)
	static bool CheckNearEnemyNum(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//检测在视野范围内，是否有位置比AI高Params[0]的敌人
	UFUNCTION(BlueprintCallable)
	static bool CheckViewedEnemyHeight(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//检测是视野内否有敌人在冰冻状态
	UFUNCTION(BlueprintCallable)
	static bool CheckHasFreezingEnemy(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//检测视野内是否有敌人冰柱或冰镰刀
	UFUNCTION(BlueprintCallable)
	static bool CheckHasFreezingSceneItemInView(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//检测场地内是否没有敌人冰柱或冰镰刀
	UFUNCTION(BlueprintCallable)
	static bool CheckNoFreezingSceneItem(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//检测视野范围有敌人
	UFUNCTION(BlueprintCallable)
	static bool CheckHasEnemyInView(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//检测视野内没有敌人，且视野外至少有1个敌人
	UFUNCTION(BlueprintCallable)
	static bool CheckNoViewedEnemy(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//场上有敌人
	UFUNCTION(BlueprintCallable)
	static bool CheckHasEnemy(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//扔冰镰刀没有进CD
	UFUNCTION(BlueprintCallable)
	static bool CheckCanThrowIceSycthe(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);


	//【Action】

	//选择视野内最近的被冰冻的敌人，
	//当与该敌人的距离小于Params[0]时，使用Action【Params[1],Params[2]】(第一、第二阶段);
	//当与敌人的距离大于等于Params[0]时，使用Action【Params[3],Params[4]】(第一、第二阶段);
	UFUNCTION(BlueprintCallable)
	static FAICommand AttackFreezingTarget(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//选择视野内的冰柱、冰镰刀、冰墙，
	//如果这些场景物件在视野中只有一个时，使用Action【Params[0],Params[1]】(第一、第二阶段);
	//如果这些场景物件在视野中大于一个时，对着最近的物件使用Action【Params[2],Params[3]】(第一、第二阶段);
	UFUNCTION(BlueprintCallable)
	static FAICommand AttackIceSceneItem(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//检测视野中的存在3个以上的敌人，已这些敌人的中心点为中心
	//使用Action【Params[0],Params[1]】(第一、第二阶段);
	UFUNCTION(BlueprintCallable)
	static FAICommand AttackViewedBunchEnemies(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//检测视野中的只存在2个的敌人，且这2个敌人的位置较近，以这2个敌人的中心点为目标
	//当与目标点距离小于Params[0]时，使用Action【Params[1],Params[2]】(第一、第二阶段);
	//当与目标点距离小于Params[3]时，使用Action【Params[4],Params[5]】(第一、第二阶段);
	//当与目标点距离大于等于Params[3]时，使用Action【Params[6],Params[7]】(第一、第二阶段);
	UFUNCTION(BlueprintCallable)
	static FAICommand AttackNearTwoEnemies(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//检测视野中的只存在2个的敌人，且这2个敌人的位置较远，最近的敌人为目标
	//当与目标点距离小于Params[0]时，使用Action【Params[1],Params[2]】(第一、第二阶段);
	//当与目标点距离大于等于Params[0]时，使用Action【Params[3],Params[4]】(第一、第二阶段);
	UFUNCTION(BlueprintCallable)
	static FAICommand AttackFarTwoEnemies(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//检测视野范围内只有1名敌人，并且与敌人的距离较近
	//使用Action【Params[0],Params[1]】(第一、第二阶段);
	UFUNCTION(BlueprintCallable)
	static FAICommand AttackNearOneViewedEnemy(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//检测视野范围内只有1名敌人，并且与敌人为中距离
	//使用Action【Params[0],Params[1]】(第一、第二阶段);
	UFUNCTION(BlueprintCallable)
	static FAICommand AttackMiddleDisOneViewedEnemy(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//检测视野范围内只有1名敌人，并且与敌人的距离较远
	//使用Action【Params[0],Params[1]】(第一、第二阶段);
	UFUNCTION(BlueprintCallable)
	static FAICommand AttackFarOneViewedEnemy(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//检测视野范围内只有1名敌人，并且与敌人的距离较远
	//使用Action【Params[0],Params[1]】(普通状态、亢奋状态);
	UFUNCTION(BlueprintCallable)
	static FAICommand MoveToOneViewedEnemy(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//AI周围近距离有多个敌人的情况下,
	//一阶段时使用Action【Params[0]】;
	//二阶段亢奋时使用Action【Params[1]】;
	//二阶段普通时使用Action【Params[2]】;
	UFUNCTION(BlueprintCallable)
	static FAICommand AttackAroundEnemies(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//视野中有处在高处的敌人,检测所有比AI高Params[0]的敌人,选取最近的那个,
	//当该角色与AI距离在Params[1]内，使用Action【Params[2]】;
	//当该角色与AI距离在Params[1]外，使用Action【Params[3]】;
	UFUNCTION(BlueprintCallable)
	static FAICommand AttackHigherEnemy(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//疲劳状态下，选择最近的一个目标，
	//如果与目标距离小于Params[0],则使用Action【Params[1]】;
	//如果与目标距离小于Params[2],则使用Action【Params[3]】;
	//如果与目标距离小于Params[4],则使用Action【Params[5]】;
	//如果与目标距离小于Params[6],则使用Action【Params[7]】;
	UFUNCTION(BlueprintCallable)
	static FAICommand DoTiredAction(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//随机选择一个场上的敌人
	//如果与目标的距离小于Params[0],则使用Action【Params[1]】;
	//如果与目标的距离小于Params[2],则使用Action【Params[3]或Params[4]】;
	//如果与目标的距离小于Params[5],则使用Action【Params[6]】;
	//如果与目标的距离大于等于Params[5],如在亢奋状态，则使用Action【Params[7]】;如在普通状态，则使用Action【Params[8]】
	UFUNCTION(BlueprintCallable)
	static FAICommand AttackRandomTarget(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//播放瞬移到场中的动画，之后连招用Ult释放全场冰刺
	UFUNCTION(BlueprintCallable)
	static FAICommand TeleportToCenterDoUlt(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//在原地生成一个冰柱
	UFUNCTION(BlueprintCallable)
	static FAICommand SpawnIcicleAtAILoc(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//在目标点生成一个冰柱，
	//Params[0]：会在AI位置和目标点位置的Params[0]处生成冰柱
	//比如Params[0] = 1,则在目标点处生成；比如Params[0] = 0.5，则在AI和目标点中间生成
	//Params[1]：让TargetDir旋转多少度
	UFUNCTION(BlueprintCallable)
	static FAICommand SpawnIcicleAtTargetLoc(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//在BOSS周围生成冰刺
	UFUNCTION(BlueprintCallable)
	static FAICommand SpawnIceSpike(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//瞬移到场地中心
	UFUNCTION(BlueprintCallable)
	static FAICommand TeleportToCenter(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params);

	//【DirectionFunc In Action】 (在播放Montage时，确定AI旋转方向的Func)

	//获取AIComponent的Params里Key为"FirstIceSceneItemTarget"或"SecondIceSceneItemTarget"对应的SceneItem的方向
	//Params[0] = 0 时查找"FirstIceSceneItemTarget";
	//Params[0] = 1 时查找"SecondIceSceneItemTarget";
	UFUNCTION(BlueprintCallable)
	static FVector GetIceSceneItemTargetDir(AAwCharacter* Character, TArray<FString> TagParams, TArray<FString> Params);

	//获取AIComponent的Params里Key为"AttackTargetLoc"的坐标，返回朝向该坐标的方向
	UFUNCTION(BlueprintCallable)
	static FVector GetTargetLocDirInParams(AAwCharacter* Character, TArray<FString> TagParams, TArray<FString> Params);


	/**
	 * AI“连招”的回调函数，这是被AIPostCommand所使用的函数库
	 * 负责的是根据角色当前的AIUseActionTags来决策下一招要放什么ActionId（例如"Action1")
	 */
	
	//检测AIComponent的UseActionTags是否包含Params[0]
	//然后检测AI的阶段;
	//如果是一阶段，返回Params[1];
	//如果是二阶段，返回Params[2]
	UFUNCTION()
	static FString CheckComboTagAndStage(AAwCharacter* Cha, TArray<FString> UseActionTags, TArray<FString> Params);

	//【部位破坏时】巨魔柱子被打破坏
	UFUNCTION(BlueprintCallable)
	static UTimelineNode* HornBroken(AAwCharacter* Character, FChaPart Part, bool Broken, TArray<FString> Params);
};
