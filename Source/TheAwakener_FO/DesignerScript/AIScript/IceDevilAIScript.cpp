// Fill out your copyright notice in the Description page of Project Settings.


#include "IceDevilAIScript.h"

#include "Components/ArrowComponent.h"
#include "Kismet/KismetMathLibrary.h"

bool UIceDevilAIScript::CheckAIExcited(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	return Character->FightingWill.Level >= 2;
}

bool UIceDevilAIScript::CheckAITired(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	return Character->FightingWill.Level == 0;
}

bool UIceDevilAIScript::CheckAINotTired(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	return Character->FightingWill.Level != 0;
}

bool UIceDevilAIScript::CheckHateBuffStack(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	float CheckStack = 100;
	if(Params.Num())
		CheckStack = FCString::Atof(*Params[0]);
	TArray<FBuffObj*> BuffList = Character->GetBuff("IceDevilHate",TArray<AAwCharacter*>());
	for (FBuffObj* CurBuff : BuffList)
	{
		if(CurBuff->Stack >= CheckStack)
			return true;
	}
	return false;
}

bool UIceDevilAIScript::CheckAIHpPercentLess(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	float CheckValue = 0.4;
	if(Params.Num())
		CheckValue = FCString::Atof(*Params[0]);
	if(Character->HealthPercentage(false) <= CheckValue)
		return true;
	return false;
}

bool UIceDevilAIScript::CheckNotHasSecondStageBuff(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	TArray<FBuffObj*> BuffList = Character->GetBuff("IceDevilSecondStage",TArray<AAwCharacter*>());
	if(BuffList.Num() > 0)
		return false;
	return true;
}

bool UIceDevilAIScript::CheckDoRangeStageAction(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	return Character->FightingWill.Level == 2 && Character->FightingWill.Value > 9000;
}

bool UIceDevilAIScript::CheckCanBunchEnemies(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	TArray<FAIFindChaInfo> FindedCharacters = AIComponent->GetAllViewedCharacter(true);
	if(FindedCharacters.Num() >= 3)
	{
		float CheckDis = 400;
		if(Params.Num())
			CheckDis = FCString::Atof(*Params[0]);
		FVector CenterLoc = FVector::ZeroVector;
		for (FAIFindChaInfo FindedCharacter : FindedCharacters)
		{
			CenterLoc = CenterLoc + FindedCharacter.Character->GetActorLocation();
		}
		CenterLoc = CenterLoc / FindedCharacters.Num();
		int CharacterInRange = 0;
		for (FAIFindChaInfo FindedCharacter : FindedCharacters)
		{
			if(FVector::Dist(CenterLoc, FindedCharacter.Character->GetActorLocation()) <= CheckDis)
				CharacterInRange++;
		}
		if(CharacterInRange >= 2)
			return true;
	}
	return false;
}

bool UIceDevilAIScript::CheckViewedEnemysDistanceBetween(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	TArray<FAIFindChaInfo> FindedCharacters = AIComponent->GetAllViewedCharacter(true);
	if(FindedCharacters.Num() == 2)
	{
		float CheckDis = 200;
		if(Params.Num())
			CheckDis = FCString::Atof(*Params[0]);
		const float CurDis = FVector::Dist(FindedCharacters[0].Character->GetActorLocation(),FindedCharacters[1].Character->GetActorLocation());
		if(CurDis <= CheckDis)
			return true;
			
	}
	return false;
}

bool UIceDevilAIScript::CheckViewedEnemyDistance(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	TArray<FAIFindChaInfo> FindedCharacters = AIComponent->GetAllViewedCharacter(true);
	if(FindedCharacters.Num() == 1)
	{
		const float CurDis = FVector::Dist(FindedCharacters[0].Character->GetActorLocation(), Character->GetActorLocation());
		float CheckDis = 200;
		if(Params.Num())
			CheckDis = FCString::Atof(*Params[0]);
		if(CurDis <= CheckDis)
			return true;
	}
	return false;
}

bool UIceDevilAIScript::CheckNearEnemyNum(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	int EnemyInRange = 0;
	float CheckDis = 300;
	if(Params.Num())
		CheckDis = FCString::Atof(*Params[0]);
	for (TTuple<AAwCharacter*, FString> FindCharacter : UGameplayFuncLib::GetAwGameState()->AllCharacters)
	{
		if(FindCharacter.Key == nullptr) continue;
		if(FindCharacter.Key != Character && !FindCharacter.Key->Dead(true) && Character->IsEnemy(FindCharacter.Key))
		{
			const float CurDis = FVector::Dist(FindCharacter.Key->GetActorLocation(), Character->GetActorLocation());
			if(CurDis <= CheckDis)
				EnemyInRange++;
		}
	}
	int CheckNum = 3;
	if(Params.Num())
		CheckNum = FCString::Atoi(*Params[1]);
	if(CheckNum <= EnemyInRange)
		return true;
	return false;
}

bool UIceDevilAIScript::CheckViewedEnemyHeight(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	TArray<FAIFindChaInfo> FindedCharacters = AIComponent->GetAllViewedCharacter(true);
	if(FindedCharacters.Num())
	{
		float CheckHeight = 250;
		if(Params.Num())
			CheckHeight = FCString::Atof(*Params[0]);
		for (FAIFindChaInfo FindedCharacter : FindedCharacters)
		{
			if(FindedCharacter.Character->GetActorLocation().Z - Character->GetActorLocation().Z > CheckHeight)
				return true;
		}
	}
	return false;
}

bool UIceDevilAIScript::CheckHasFreezingEnemy(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	TArray<FAIFindChaInfo> FindedCharacters = AIComponent->GetAllViewedCharacter(false, true);
	for (FAIFindChaInfo FindedCharacter : FindedCharacters)
	{
		TArray<FBuffObj*> BuffList = FindedCharacter.Character->GetBuff("Standard_PlayerFrozen",TArray<AAwCharacter*>());
		if(BuffList.Num() > 0)
			return true;
	}
	return false;
}

bool UIceDevilAIScript::CheckHasFreezingSceneItemInView(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	TArray<FString> RemoveList;
	int ViewedSceneItemNum = 0;
	int CheckDis = 700;
	if (Params.Num())
		CheckDis = FCString::Atoi(*Params[0]);
	for (TTuple<FString, AAwSceneItem*> SceneItem : UGameplayFuncLib::GetAwGameState()->SceneItemList)
	{
		if(!SceneItem.Value)
		{
			RemoveList.Add(SceneItem.Key);
			continue;
		}
		if(SceneItem.Value->Tags.Contains("IceDevilSceneItem"))
		{
			FVector TargetDir = SceneItem.Value->GetActorLocation() - Character->GetActorLocation();
			TargetDir.Normalize();
			const float Degree = FMath::Abs(UKismetMathLibrary::DegAcos(FVector::DotProduct(Character->GetActorForwardVector(), TargetDir)));
			const float Dis = FVector::Dist2D(SceneItem.Value->GetActorLocation(), Character->GetActorLocation());
			if(Degree <= AIComponent->GetHalfSightDegree() && Dis <= CheckDis)
				ViewedSceneItemNum++;
		}
	}
	for (FString RemoveId : RemoveList)
	{
		UGameplayFuncLib::GetAwGameState()->SceneItemList.Remove(RemoveId);
	}
	if(ViewedSceneItemNum)
		return true;
	return false;
}

bool UIceDevilAIScript::CheckNoFreezingSceneItem(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	TArray<FString> RemoveList;
	int ViewedSceneItemNum = 0;
	for (TTuple<FString, AAwSceneItem*> SceneItem : UGameplayFuncLib::GetAwGameState()->SceneItemList)
	{
		if (!SceneItem.Value)
		{
			RemoveList.Add(SceneItem.Key);
			continue;
		}
		if (SceneItem.Value->Tags.Contains("IceDevilSceneItem"))
		{
			ViewedSceneItemNum++;
		}
	}
	for (FString RemoveId : RemoveList)
	{
		UGameplayFuncLib::GetAwGameState()->SceneItemList.Remove(RemoveId);
	}
	if (ViewedSceneItemNum)
		return false;
	return true;
}

bool UIceDevilAIScript::CheckHasEnemyInView(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	TArray<FAIFindChaInfo> ViewedCharacters = AIComponent->GetAllViewedCharacter(true);
	return ViewedCharacters.Num() > 0;
}

bool UIceDevilAIScript::CheckNoViewedEnemy(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	TArray<FAIFindChaInfo> ViewedCharacters = AIComponent->GetAllViewedCharacter(true);
	if(ViewedCharacters.Num() == 0)
	{
		for (TTuple<AAwCharacter*, FString> FindCharacter : UGameplayFuncLib::GetAwGameState()->AllCharacters)
		{
			if(FindCharacter.Key == nullptr) continue;
			if(FindCharacter.Key != Character && !FindCharacter.Key->Dead(true) && Character->IsEnemy(FindCharacter.Key))
				return true;
		}
	}
	return false;
}

bool UIceDevilAIScript::CheckHasEnemy(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	for (TTuple<AAwCharacter*, FString> FindCharacter : UGameplayFuncLib::GetAwGameState()->AllCharacters)
	{
		if(FindCharacter.Key == nullptr) continue;
		if(FindCharacter.Key != Character && !FindCharacter.Key->Dead(true) && Character->IsEnemy(FindCharacter.Key))
			return true;
	}
	return false;
}

bool UIceDevilAIScript::CheckCanThrowIceSycthe(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	if(Character->GetBuff("ThrowIceSyctheCD",TArray<AAwCharacter*>()).Num() > 0)
		return false;
	return true;
}

FAICommand UIceDevilAIScript::AttackFreezingTarget(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	FAICommand NewCommand = FAICommand();
	//检测被冰冻的最近的敌人
	FAIFindChaInfo ClosetTarget = FAIFindChaInfo();
	TArray<FAIFindChaInfo> FindedCharacters = AIComponent->GetAllViewedCharacter(false, true);
	for (FAIFindChaInfo FindedCharacter : FindedCharacters)
	{
		//检测是否带有冰冻debuff
		TArray<FBuffObj*> BuffList = FindedCharacter.Character->GetBuff("Standard_PlayerFrozen",TArray<AAwCharacter*>());
		if(BuffList.Num() > 0)
		{
			//判断是否是最近的目标
			if(ClosetTarget.Character == nullptr)
				ClosetTarget = FindedCharacter;
			else
			{
				if(ClosetTarget.DistanceXY > FindedCharacter.DistanceXY)
					ClosetTarget = FindedCharacter;
			}
		}
	}
	//如果有最近的冰冻目标
	if(ClosetTarget.Character)
	{
		//清空之前的连招tag名字
		AIComponent->ClearUseActionTags();
		//获取检测距离
		float CheckDis = 500;
		if(Params.Num())
			CheckDis = FCString::Atof(*Params[0]);
		bool bInSecondStage = false;
		//判断是否是第二阶段
		TArray<FBuffObj*> BuffList = Character->GetBuff("IceDevilSecondStage",TArray<AAwCharacter*>());
		if(BuffList.Num() > 0)
			bInSecondStage = true;
		//根据距离和阶段使用不同Action
		if(ClosetTarget.DistanceXY < CheckDis)
		{
			if(Params.Num() > 2)
			{
				if(!bInSecondStage)
					NewCommand = AIComponent->CreateUseActionCommand(Params[1], Character->GetActorForwardVector());
				else
					NewCommand = AIComponent->CreateUseActionCommand(Params[2], Character->GetActorForwardVector());
			}
		}
		else
		{
			if(Params.Num() > 4)
			{
				if(!bInSecondStage)
					NewCommand = AIComponent->CreateUseActionCommand(Params[3], Character->GetActorForwardVector());
				else
					NewCommand = AIComponent->CreateUseActionCommand(Params[4], Character->GetActorForwardVector());
				AIComponent->AddUseActionTag("AttackFreezingTargetCombo");
			}
		}
		//把目标存到AIComponent中
		AIComponent->SetTargetEnemy(ClosetTarget);
		//设置Action旋转函数
		AIComponent->TargetDirFunc = UDataFuncLib::SplitFuncNameAndParams("MobAIScript.GetCurTargetEnemyDir()");
	}
	return NewCommand;
}

FAICommand UIceDevilAIScript::AttackIceSceneItem(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	FAICommand NewCommand = FAICommand();
	//清空之前的连招tag名字
	AIComponent->ClearUseActionTags();
	//查找场上的冰冻物件
	TMap<AAwSceneItem*, float> IceSceneItemList;
	TArray<FString> RemoveList;
	for (TTuple<FString, AAwSceneItem*> SceneItem : UGameplayFuncLib::GetAwGameState()->SceneItemList)
	{
		if(!SceneItem.Value)
		{
			RemoveList.Add(SceneItem.Key);
			continue;
		}
		if(SceneItem.Value->Tags.Contains("IceDevilSceneItem"))
		{
			FVector TargetDir = SceneItem.Value->GetActorLocation() - Character->GetActorLocation();
			TargetDir.Normalize();
			const float Degree = FMath::Abs(UKismetMathLibrary::DegAcos(FVector::DotProduct(Character->GetActorForwardVector(), TargetDir)));
			if(Degree <= AIComponent->GetHalfSightDegree())
			{
				IceSceneItemList.Add(SceneItem.Value, FVector::Dist(SceneItem.Value->GetActorLocation(), Character->GetActorLocation()));
			}
		}
	}
	//清空GameState的数组中已经失效的
	for (FString RemoveId : RemoveList)
	{
		UGameplayFuncLib::GetAwGameState()->SceneItemList.Remove(RemoveId);
	}
	//根据把查找到的冰冻场景物件按距离排序
	IceSceneItemList.ValueSort([](float A, float B){
		return A < B;
	});
	TArray<AAwSceneItem*> SceneItemKeyList;
	IceSceneItemList.GetKeys(SceneItemKeyList);
	//确认是否第二阶段
	bool bInSecondStage = false;
	TArray<FBuffObj*> BuffList = Character->GetBuff("IceDevilSecondStage",TArray<AAwCharacter*>());
	if(BuffList.Num() > 0)
		bInSecondStage = true;
	//视野内只有一个目标
	if(SceneItemKeyList.Num() == 1)
	{
		//在Params中储存该场景物件的Id,方便之后在调用Action旋转函数时会根据Id查找到该场景物件
		AIComponent->Params.Add("FirstIceSceneItemTarget", SceneItemKeyList[0]->Id);
		if(!bInSecondStage)
			NewCommand = AIComponent->CreateUseActionCommand(Params[0], Character->GetActorForwardVector());
		else
			NewCommand = AIComponent->CreateUseActionCommand(Params[1], Character->GetActorForwardVector());
		//设置Action旋转函数
		AIComponent->TargetDirFunc = UDataFuncLib::SplitFuncNameAndParams("IceDevilAIScript.GetIceSceneItemTargetDir(0)");
	}
	//视野内有一个以上的目标
	else if(SceneItemKeyList.Num() > 1)
	{
		//在Params中储存该场景物件的Id,方便之后在调用Action旋转函数时会根据Id查找到该场景物件
		AIComponent->Params.Add("FirstIceSceneItemTarget", SceneItemKeyList[0]->Id);
		AIComponent->Params.Add("SecondIceSceneItemTarget", SceneItemKeyList[1]->Id);
		//设置连招Tag名
		AIComponent->AddUseActionTag("AttackIceSceneItemCombo");
		if(!bInSecondStage)
			NewCommand = AIComponent->CreateUseActionCommand(Params[2], Character->GetActorForwardVector());
		else
			NewCommand = AIComponent->CreateUseActionCommand(Params[3], Character->GetActorForwardVector());
		//设置Action旋转函数
		AIComponent->TargetDirFunc = UDataFuncLib::SplitFuncNameAndParams("IceDevilAIScript.GetIceSceneItemTargetDir(0)");
	}
	return NewCommand;
}

FAICommand UIceDevilAIScript::AttackViewedBunchEnemies(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	FAICommand NewCommand = FAICommand();
	//确认是否第二阶段
	bool bInSecondStage = false;
	TArray<FBuffObj*> BuffList = Character->GetBuff("IceDevilSecondStage",TArray<AAwCharacter*>());
	if(BuffList.Num() > 0)
		bInSecondStage = true;
	if(!bInSecondStage)
		NewCommand = AIComponent->CreateUseActionCommand(Params[0], Character->GetActorForwardVector());
	else
		NewCommand = AIComponent->CreateUseActionCommand(Params[1], Character->GetActorForwardVector());
	//计算并保存敌人的中心点
	TArray<FAIFindChaInfo> FindedCharacters = AIComponent->GetAllViewedCharacter(true);
	if(FindedCharacters.Num() >= 3)
	{
		FVector CenterLoc = FVector::ZeroVector;
		for (FAIFindChaInfo FindedCharacter : FindedCharacters)
		{
			CenterLoc = CenterLoc + FindedCharacter.Character->GetActorLocation();
		}
		CenterLoc = CenterLoc / FindedCharacters.Num();
		//把中心点存在AIComponent的Params中
		AIComponent->Params.Add("AttackTargetLoc", CenterLoc.ToString());
		//设置Action旋转函数
		AIComponent->TargetDirFunc = UDataFuncLib::SplitFuncNameAndParams("IceDevilAIScript.GetTargetLocDirInParams()");
		//清空之前的连招tag名字
		AIComponent->ClearUseActionTags();
	}
	return NewCommand;
}

FAICommand UIceDevilAIScript::AttackNearTwoEnemies(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	FAICommand NewCommand = FAICommand();
	TArray<FAIFindChaInfo> FindedCharacters = AIComponent->GetAllViewedCharacter(true);
	if(FindedCharacters.Num() == 2)
	{
		//清空之前的连招tag名字
		AIComponent->ClearUseActionTags();
		const FVector CenterLoc = (FindedCharacters[0].Character->GetActorLocation() + FindedCharacters[1].Character->GetActorLocation()) / 2;
		const float CheckNearDis = FCString::Atof(*Params[0]);
		const float CheckFarDis = FCString::Atof(*Params[3]);
		const float CurDis = FVector::Dist(Character->GetActorLocation(), CenterLoc);
		//确认是否第二阶段
		bool bInSecondStage = false;
		TArray<FBuffObj*> BuffList = Character->GetBuff("IceDevilSecondStage",TArray<AAwCharacter*>());
		if(BuffList.Num() > 0)
			bInSecondStage = true;
		if(CurDis < CheckNearDis)
		{
			//设置连招Tag名
			AIComponent->AddUseActionTag("AttackTwoNearEnemyCombo01");
			if(!bInSecondStage)
				NewCommand = AIComponent->CreateUseActionCommand(Params[1], Character->GetActorForwardVector());
			else
				NewCommand = AIComponent->CreateUseActionCommand(Params[2], Character->GetActorForwardVector());
		}
		else if(CurDis < CheckFarDis)
		{
			//设置连招Tag名
			AIComponent->AddUseActionTag("AttackTwoNearEnemyCombo02");
			if(!bInSecondStage)
				NewCommand = AIComponent->CreateUseActionCommand(Params[4], Character->GetActorForwardVector());
			else
				NewCommand = AIComponent->CreateUseActionCommand(Params[5], Character->GetActorForwardVector());
		}
		else
		{
			AIComponent->AddUseActionTag("AttackTwoNearEnemyCombo03");
			if(!bInSecondStage)
				NewCommand = AIComponent->CreateUseActionCommand(Params[6], Character->GetActorForwardVector());
			else
				NewCommand = AIComponent->CreateUseActionCommand(Params[7], Character->GetActorForwardVector());
		}
		//把中心点存在AIComponent的Params中
		AIComponent->Params.Add("AttackTargetLoc", CenterLoc.ToString());
		//设置Action旋转函数
		AIComponent->TargetDirFunc = UDataFuncLib::SplitFuncNameAndParams("IceDevilAIScript.GetTargetLocDirInParams()");
	}
	return NewCommand;
}

FAICommand UIceDevilAIScript::AttackFarTwoEnemies(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	FAICommand NewCommand = FAICommand();
	TArray<FAIFindChaInfo> FindedCharacters = AIComponent->GetAllViewedCharacter(true);
	if(FindedCharacters.Num() == 2)
	{
		//选取最近的目标
		FAIFindChaInfo TargetEnemy;
		if(FindedCharacters[0].DistanceXY < FindedCharacters[1].DistanceXY)
			TargetEnemy = FindedCharacters[0];
		else
			TargetEnemy = FindedCharacters[1];
		const float CurDis = FVector::Dist(Character->GetActorLocation(), TargetEnemy.Character->GetActorLocation());
		const float CheckDis = FCString::Atof(*Params[0]);
		//确认是否第二阶段
		bool bInSecondStage = false;
		TArray<FBuffObj*> BuffList = Character->GetBuff("IceDevilSecondStage",TArray<AAwCharacter*>());
		if(BuffList.Num() > 0)
			bInSecondStage = true;
		if(CurDis < CheckDis)
		{
			//设置连招Tag名
			AIComponent->AddUseActionTag("AttackTwoFarEnemyCombo01");
			if(!bInSecondStage)
				NewCommand = AIComponent->CreateUseActionCommand(Params[1], Character->GetActorForwardVector());
			else
				NewCommand = AIComponent->CreateUseActionCommand(Params[2], Character->GetActorForwardVector());
		}
		else
		{
			if(!bInSecondStage)
				NewCommand = AIComponent->CreateUseActionCommand(Params[3], Character->GetActorForwardVector());
			else
				NewCommand = AIComponent->CreateUseActionCommand(Params[4], Character->GetActorForwardVector());
		}
		//设置AI的TargetEnemy
		AIComponent->SetTargetEnemy(TargetEnemy);
		//设置Action旋转函数
		AIComponent->TargetDirFunc = UDataFuncLib::SplitFuncNameAndParams("MobAIScript.GetCurTargetEnemyDir()");
	}
	return NewCommand;
}

FAICommand UIceDevilAIScript::AttackNearOneViewedEnemy(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	FAICommand NewCommand = FAICommand();
	TArray<FAIFindChaInfo> FindedCharacters = AIComponent->GetAllViewedCharacter(true);
	if(FindedCharacters.Num() == 1)
	{
		//确认是否第二阶段
		bool bInSecondStage = false;
		TArray<FBuffObj*> BuffList = Character->GetBuff("IceDevilSecondStage",TArray<AAwCharacter*>());
		if(BuffList.Num() > 0)
			bInSecondStage = true;
		//设置连招Tag名
		AIComponent->AddUseActionTag("AttackNearOneEnemyCombo");
		if(!bInSecondStage)
			NewCommand = AIComponent->CreateUseActionCommand(Params[0], Character->GetActorForwardVector());
		else
			NewCommand = AIComponent->CreateUseActionCommand(Params[1], Character->GetActorForwardVector());
		//设置AI的TargetEnemy
		AIComponent->SetTargetEnemy(FindedCharacters[0]);
		//设置Action旋转函数
		AIComponent->TargetDirFunc = UDataFuncLib::SplitFuncNameAndParams("MobAIScript.GetCurTargetEnemyDir()");
	}
	return NewCommand;
}

FAICommand UIceDevilAIScript::AttackMiddleDisOneViewedEnemy(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	FAICommand NewCommand = FAICommand();
	TArray<FAIFindChaInfo> FindedCharacters = AIComponent->GetAllViewedCharacter(true);
	if(FindedCharacters.Num() == 1)
	{
		//确认是否第二阶段
		bool bInSecondStage = false;
		TArray<FBuffObj*> BuffList = Character->GetBuff("IceDevilSecondStage",TArray<AAwCharacter*>());
		if(BuffList.Num() > 0)
			bInSecondStage = true;
		AIComponent->AddUseActionTag("AttackMiddleDisOneEnemyCombo");
		if(!bInSecondStage)
			NewCommand = AIComponent->CreateUseActionCommand(Params[0], Character->GetActorForwardVector());
		else
			NewCommand = AIComponent->CreateUseActionCommand(Params[1], Character->GetActorForwardVector());
		AIComponent->SetTargetEnemy(FindedCharacters[0]);
		AIComponent->TargetDirFunc = UDataFuncLib::SplitFuncNameAndParams("MobAIScript.GetCurTargetEnemyDir()");
	}
	return NewCommand;
}

FAICommand UIceDevilAIScript::AttackFarOneViewedEnemy(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	FAICommand NewCommand = FAICommand();
	TArray<FAIFindChaInfo> FindedCharacters = AIComponent->GetAllViewedCharacter(true);
	if(FindedCharacters.Num() == 1)
	{
		//确认是否第二阶段
		bool bInSecondStage = false;
		TArray<FBuffObj*> BuffList = Character->GetBuff("IceDevilSecondStage",TArray<AAwCharacter*>());
		if(BuffList.Num() > 0)
			bInSecondStage = true;
		AIComponent->AddUseActionTag("AttackFarOneEnemyCombo");
		if(!bInSecondStage)
			NewCommand = AIComponent->CreateUseActionCommand(Params[0], Character->GetActorForwardVector());
		else
			NewCommand = AIComponent->CreateUseActionCommand(Params[1], Character->GetActorForwardVector());
		AIComponent->SetTargetEnemy(FindedCharacters[0]);
		AIComponent->TargetDirFunc = UDataFuncLib::SplitFuncNameAndParams("MobAIScript.GetCurTargetEnemyDir()");
	}
	return NewCommand;
}

FAICommand UIceDevilAIScript::MoveToOneViewedEnemy(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	FAICommand NewCommand = FAICommand();
	TArray<FAIFindChaInfo> ViewedCharacters = AIComponent->GetAllViewedCharacter(true);
	int CheckDis = 1000;
	if (Params.Num())
		CheckDis = FCString::Atoi(*Params[0]);
	if(ViewedCharacters.Num())
	{
		//确认是否第二阶段
		bool bInSecondStage = false;
		TArray<FBuffObj*> BuffList = Character->GetBuff("IceDevilSecondStage",TArray<AAwCharacter*>());
		if(BuffList.Num() > 0)
			bInSecondStage = true;
		const int Index = FMath::RandRange(0, ViewedCharacters.Num() - 1);
		const float CurDis = FVector::Dist2D(Character->GetActorLocation(), ViewedCharacters[Index].Character->GetActorLocation());
		if (CurDis <= CheckDis)
		{
			NewCommand = AIComponent->CreateUseActionCommand(Params[1], Character->GetActorForwardVector());
		}
		else
		{
			if (!bInSecondStage)
				NewCommand = AIComponent->CreateUseActionCommand(Params[2], Character->GetActorForwardVector());
			else
				NewCommand = AIComponent->CreateUseActionCommand(Params[3], Character->GetActorForwardVector());
		}
		
		AIComponent->SetTargetEnemy(ViewedCharacters[Index]);
		AIComponent->TargetDirFunc = UDataFuncLib::SplitFuncNameAndParams("MobAIScript.GetCurTargetEnemyDir()");
	}
	return NewCommand;
}

FAICommand UIceDevilAIScript::AttackAroundEnemies(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	FAICommand NewCommand = FAICommand();
	//确认是否第二阶段
	bool bInSecondStage = false;
	TArray<FBuffObj*> BuffList = Character->GetBuff("IceDevilSecondStage",TArray<AAwCharacter*>());
	if(BuffList.Num() > 0)
		bInSecondStage = true;
	AIComponent->AddUseActionTag("AttackAroundEnemiesCombo");
	if(!bInSecondStage)
		NewCommand = AIComponent->CreateUseActionCommand(Params[0], Character->GetActorForwardVector());
	else
	{
		if(Character->FightingWill.Level >= 2)
			NewCommand = AIComponent->CreateUseActionCommand(Params[1], Character->GetActorForwardVector());
		else
			NewCommand = AIComponent->CreateUseActionCommand(Params[2], Character->GetActorForwardVector());
	}
		
	AIComponent->TargetDirFunc = FJsonFuncData();
	return NewCommand;
}

FAICommand UIceDevilAIScript::AttackHigherEnemy(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	FAICommand NewCommand = FAICommand();
	float CheckHeight = 250;
	if(Params.Num())
		CheckHeight = FCString::Atof(*Params[0]);
	TArray<FAIFindChaInfo> HigherEnemyList;
	for (FAIFindChaInfo FindedCharacter : AIComponent->GetAllViewedCharacter(true))
	{
		if(FindedCharacter.Character->GetActorLocation().Z - Character->GetActorLocation().Z > CheckHeight)
			HigherEnemyList.Add(FindedCharacter);
	}
	if(HigherEnemyList.Num())
	{
		HigherEnemyList.Sort([](FAIFindChaInfo A, FAIFindChaInfo B){
		return A.DistanceXY < B.DistanceXY;
		});
		float CurDis = HigherEnemyList[0].DistanceXY;
		float CheckDis = 300;
		if(Params.Num())
			CheckDis = FCString::Atof(*Params[1]);
		AIComponent->AddUseActionTag("AttackHighterEnemyCombo");
		if(CurDis < CheckDis)
			NewCommand = AIComponent->CreateUseActionCommand(Params[2], Character->GetActorForwardVector());
		else
			NewCommand = AIComponent->CreateUseActionCommand(Params[3], Character->GetActorForwardVector());
		AIComponent->SetTargetEnemy(HigherEnemyList[0]);
		AIComponent->TargetDirFunc = UDataFuncLib::SplitFuncNameAndParams("MobAIScript.GetCurTargetEnemyDir()");
	}
	
	return NewCommand;
}

FAICommand UIceDevilAIScript::DoTiredAction(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	FAICommand NewCommand = FAICommand();
	//找到最近的敌人
	FAIFindChaInfo ClosetEnemy = FAIFindChaInfo();
	for (TTuple<AAwCharacter*, FString> FindCharacter : UGameplayFuncLib::GetAwGameState()->AllCharacters)
	{
		if(FindCharacter.Key == nullptr) continue;
		if(FindCharacter.Key != Character && !FindCharacter.Key->Dead(true) && Character->IsEnemy(FindCharacter.Key))
		{
			const float CurDis = FVector::Dist2D(FindCharacter.Key->GetActorLocation(), Character->GetActorLocation());
			if(ClosetEnemy.Character == nullptr)
			{
				ClosetEnemy.Character = FindCharacter.Key;
				ClosetEnemy.DistanceXY = CurDis;
			}
			if(ClosetEnemy.DistanceXY > CurDis)
			{
				ClosetEnemy.Character = FindCharacter.Key;
				ClosetEnemy.DistanceXY = CurDis;
			}
		}
	}
	if(ClosetEnemy.Character)
	{
		float CheckDis = 100;
		if(Params.Num() > 1)
		{
			CheckDis = FCString::Atof(*Params[0]);
			if(ClosetEnemy.DistanceXY <= CheckDis)
			{
				NewCommand = AIComponent->CreateUseActionCommand(Params[1], Character->GetActorForwardVector());
				AIComponent->SetTargetEnemy(ClosetEnemy);
				AIComponent->TargetDirFunc = UDataFuncLib::SplitFuncNameAndParams("MobAIScript.GetCurTargetEnemyDir()");
				//Temp
				AIComponent->ClearUseActionTags();
				AIComponent->AddUseActionTag("DodgeCombo");
				//Temp
				return NewCommand;
			}
		}
		if(Params.Num() > 3)
		{
			CheckDis = FCString::Atof(*Params[2]);
			if(ClosetEnemy.DistanceXY <= CheckDis)
			{
				NewCommand = AIComponent->CreateUseActionCommand(Params[3], Character->GetActorForwardVector());
				AIComponent->SetTargetEnemy(ClosetEnemy);
				AIComponent->TargetDirFunc = UDataFuncLib::SplitFuncNameAndParams("MobAIScript.GetCurTargetEnemyDir()");
				return NewCommand;
			}
		}
		if(Params.Num() > 5)
		{
			CheckDis = FCString::Atof(*Params[4]);
			if(ClosetEnemy.DistanceXY <= CheckDis)
			{
				NewCommand = AIComponent->CreateUseActionCommand(Params[5], Character->GetActorForwardVector());
				AIComponent->SetTargetEnemy(ClosetEnemy);
				AIComponent->TargetDirFunc = UDataFuncLib::SplitFuncNameAndParams("MobAIScript.GetCurTargetEnemyDir()");
				return NewCommand;
			}
		}
		if(Params.Num() > 6)
		{
			NewCommand = AIComponent->CreateUseActionCommand(Params[6], Character->GetActorForwardVector());
			AIComponent->SetTargetEnemy(ClosetEnemy);
			AIComponent->TargetDirFunc = UDataFuncLib::SplitFuncNameAndParams("MobAIScript.GetCurTargetEnemyDir()");
			return NewCommand;
		}
	}
	return NewCommand;
}

FAICommand UIceDevilAIScript::AttackRandomTarget(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	FAICommand NewCommand = FAICommand();
	TArray<AAwCharacter*> EnemyList;
	for (TTuple<AAwCharacter*, FString> FindCharacter : UGameplayFuncLib::GetAwGameState()->AllCharacters)
	{
		if(FindCharacter.Key == nullptr) continue;
		if(FindCharacter.Key != Character && !FindCharacter.Key->Dead(true) && Character->IsEnemy(FindCharacter.Key))
			EnemyList.Add(FindCharacter.Key);
	}
	if(EnemyList.Num())
	{
		AAwCharacter* Target = EnemyList[FMath::RandRange(0, EnemyList.Num() -1)];
		const float CurDis = FVector::Dist(Target->GetActorLocation(), Character->GetActorLocation());
		const float CheckNearDis = FCString::Atof(*Params[0]);
		const float CheckMiddleDis = FCString::Atof(*Params[2]);
		const float CheckFarDis = FCString::Atof(*Params[5]);
		if(CurDis < CheckNearDis)
		{
			NewCommand = AIComponent->CreateUseActionCommand(Params[1], Character->GetActorForwardVector());
		}
		else if(CurDis < CheckMiddleDis)
		{
			if(FMath::RandBool())
				NewCommand = AIComponent->CreateUseActionCommand(Params[3], Character->GetActorForwardVector());
			else
				NewCommand = AIComponent->CreateUseActionCommand(Params[4], Character->GetActorForwardVector());
		}
		else if(CurDis < CheckFarDis)
		{
			NewCommand = AIComponent->CreateUseActionCommand(Params[6], Character->GetActorForwardVector());
		}
		else
		{
			//确认是否在亢奋状态
			bool bExcited = (Character->FightingWill.Level >= 2);
			if(bExcited)
				NewCommand = AIComponent->CreateUseActionCommand(Params[7], Character->GetActorForwardVector());
			else
				NewCommand = AIComponent->CreateUseActionCommand(Params[8], Character->GetActorForwardVector());
		}
		AIComponent->AddUseActionTag("AttackRandomEnemyCombo");
		FAIFindChaInfo TargetInfo = FAIFindChaInfo();
		TargetInfo.Character = Target;
		AIComponent->SetTargetEnemy(TargetInfo);
		AIComponent->TargetDirFunc = UDataFuncLib::SplitFuncNameAndParams("MobAIScript.GetCurTargetEnemyDir()");
	}
	return NewCommand;
}

FAICommand UIceDevilAIScript::TeleportToCenterDoUlt(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	FAICommand NewCommand = FAICommand();
	if (Params.Num())
	{
		NewCommand = AIComponent->CreateUseActionCommand(Params[0], Character->GetActorForwardVector());
		AIComponent->AddUseActionTag("Ult");
	}
	return NewCommand;
}

FAICommand UIceDevilAIScript::SpawnIcicleAtAILoc(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	FSceneItemModel Model = UGameplayFuncLib::GetAwDataManager()->GetSceneItemModelById("Icicle");
	FVector SpawnLoc = Character->GetActorLocation();
	//射线检测地面
	FHitResult HitResult;
	const bool bIsHit = UKismetSystemLibrary::CapsuleTraceSingle(
											Character, Character->GetActorLocation(), Character->GetActorLocation() + FVector::DownVector * 1000.0f, 40, 40,
											TraceTypeQuery4, false, TArray<AActor*>(), EDrawDebugTrace::None, HitResult, true);
	if(bIsHit)
	{
		FCharacterCamp CasterCamp = UGameplayFuncLib::GetAwDataManager()->GetCampInfoBySide(Character->Side);
		int SceneItemSide = 15;
		if(CasterCamp.CanAttackCampId.Num())
		{
			int RandIndex = FMath::RandRange(0, CasterCamp.CanAttackCampId.Num() - 1);
			if(CasterCamp.CanAttackCampId[RandIndex] != 0)
				SceneItemSide = CasterCamp.CanAttackCampId[RandIndex];
		}
		UGameplayFuncLib::CreateSceneItem(Model, SceneItemSide,
			FTransform(FRotator(0,Character->GetActorRotation().Yaw,0),HitResult.ImpactPoint,FVector(1,1,1)));
	}
	return FAICommand();
}

FAICommand UIceDevilAIScript::SpawnIcicleAtTargetLoc(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	FSceneItemModel Model = UGameplayFuncLib::GetAwDataManager()->GetSceneItemModelById("Icicle");
	if(AIComponent->Params.Contains("AttackTargetLoc"))
	{
		FVector TargetLoc;
		TargetLoc.InitFromString(AIComponent->Params["AttackTargetLoc"]);
		FVector TargetDir = TargetLoc - Character->GetActorLocation();
		const float TargetDis = TargetDir.Size();
		TargetDir.Normalize();

		float DisPercent = 1.0f;
		if(Params.Num())
			DisPercent = FCString::Atof(*Params[0]);
		
		if(Params.Num() > 1)
		{
			float ChangeDegree = FCString::Atof(*Params[1]);
			TargetDir = TargetDir.RotateAngleAxis(ChangeDegree, FVector(0,0,1));
			TargetDir.Normalize();
		}
		const FVector SpawnLoc = Character->GetActorLocation() + TargetDir * (TargetDis * DisPercent);
		//射线检测地面
		FHitResult HitResult;
		const bool bIsHit = UKismetSystemLibrary::CapsuleTraceSingle(
												Character, SpawnLoc, SpawnLoc + FVector::DownVector * 1000.0f, 40, 40,
												TraceTypeQuery4, false, TArray<AActor*>(), EDrawDebugTrace::None, HitResult, true);
		if(bIsHit)
		{
			FCharacterCamp CasterCamp = UGameplayFuncLib::GetAwDataManager()->GetCampInfoBySide(Character->Side);
			int SceneItemSide = 15;
			if(CasterCamp.CanAttackCampId.Num())
			{
				int RandIndex = FMath::RandRange(0, CasterCamp.CanAttackCampId.Num() - 1);
				if(CasterCamp.CanAttackCampId[RandIndex] != 0)
					SceneItemSide = CasterCamp.CanAttackCampId[RandIndex];
			}
			UGameplayFuncLib::CreateSceneItem(Model, SceneItemSide,
				FTransform(FRotator(0,Character->GetActorRotation().Yaw,0),HitResult.ImpactPoint,FVector(1,1,1)));
		}
	}
	return FAICommand();
}

FAICommand UIceDevilAIScript::SpawnIceSpike(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	for (UActorComponent* Component : Character->GetComponentsByTag(UArrowComponent::StaticClass(),"IceSpike"))
	{
		USceneComponent* SceneComponent = Cast<USceneComponent>(Component);
		if(SceneComponent)
		{
			FTransform SpawnTrans = SceneComponent->GetComponentTransform();
			//射线检测地面
			FHitResult HitResult;
			TArray<TEnumAsByte<EObjectTypeQuery>> CheckType;
			CheckType.Add(ObjectTypeQuery1);
			const bool bIsHit = UKismetSystemLibrary::CapsuleTraceSingleForObjects(
													Character, SpawnTrans.GetLocation(), SpawnTrans.GetLocation() + FVector::DownVector * 1000.0f, 40, 40,
													CheckType, false, TArray<AActor*>(), EDrawDebugTrace::None, HitResult, true);
			// const bool bIsHit = UKismetSystemLibrary::CapsuleTraceSingle(
			// 										Character, SpawnTrans.GetLocation(), SpawnTrans.GetLocation() + FVector::DownVector * 1000.0f, 40, 40,
			// 										TraceTypeQuery4, false, TArray<AActor*>(), EDrawDebugTrace::None, HitResult, true);
			if(bIsHit)
			{
				SpawnTrans.SetLocation(HitResult.ImpactPoint);
				FCharacterCamp CasterCamp = UGameplayFuncLib::GetAwDataManager()->GetCampInfoBySide(Character->Side);
				int SceneItemSide = 15;
				if(CasterCamp.CanAttackCampId.Num())
				{
					int RandIndex = FMath::RandRange(0, CasterCamp.CanAttackCampId.Num() - 1);
					if(CasterCamp.CanAttackCampId[RandIndex] != 0)
						SceneItemSide = CasterCamp.CanAttackCampId[RandIndex];
				}
				FSceneItemModel Model = UGameplayFuncLib::GetAwDataManager()->GetSceneItemModelById("IceSpike");
				UGameplayFuncLib::CreateSceneItem(Model, SceneItemSide, SpawnTrans);
			}
		}
	}
	return FAICommand();
}

FAICommand UIceDevilAIScript::TeleportToCenter(AAwCharacter* Character, UAwAIComponent* AIComponent, TArray<FString> Params)
{
	FVector CenterPoints = FVector::ZeroVector;
	for (FMapSetPoint CurPoint : UGameplayFuncLib::GetAwGameState()->MapPointList)
	{
		if(CurPoint.Id.Contains("CenterPoint"))
		{
			CenterPoints = CurPoint.Location;
			break;
		}
	}
	if(CenterPoints != FVector::ZeroVector)
	{
		Character->SetActorLocation(CenterPoints,false, nullptr, ETeleportType::TeleportPhysics);
	}
	return FAICommand();
}

FVector UIceDevilAIScript::GetIceSceneItemTargetDir(AAwCharacter* Character, TArray<FString> TagParams, TArray<FString> Params)
{
	if(Params.Num() > 0)
	{
		const int ParamsValue = FCString::Atoi(*Params[0]);
		FString SceneItemId = "";
		if(ParamsValue == 0)
		{
			SceneItemId = *Character->GetAIComponent()->Params.Find("FirstIceSceneItemTarget");
		}
		else if(ParamsValue == 1)
		{
			SceneItemId = *Character->GetAIComponent()->Params.Find("SecondIceSceneItemTarget");
		}
		if(UGameplayFuncLib::GetAwGameState()->SceneItemList.Contains(SceneItemId))
		{
			AAwSceneItem* TargetSceneItem = UGameplayFuncLib::GetAwGameState()->SceneItemList[SceneItemId];
			if(TargetSceneItem)
			{
				FVector Dir = TargetSceneItem->GetActorLocation() - Character->GetActorLocation();
				Dir.Normalize();
				return Dir;
			}
		}
	}
	return Character->GetActorForwardVector();
}

FVector UIceDevilAIScript::GetTargetLocDirInParams(AAwCharacter* Character, TArray<FString> TagParams, TArray<FString> Params)
{
	FVector TargetLoc = Character->GetActorLocation() + Character->GetActorForwardVector();
	if(Character->GetAIComponent()->Params.Contains("AttackTargetLoc"))
	{
		TargetLoc.InitFromString(Character->GetAIComponent()->Params["AttackTargetLoc"]);
	}
	FVector Dir = TargetLoc - Character->GetActorLocation();
	Dir.Normalize();
	return Dir;
}

FString UIceDevilAIScript::CheckComboTagAndStage(AAwCharacter* Cha, TArray<FString> UseActionTags, TArray<FString> Params)
{
	if(Params.Num() > 0)
	{
		if(UseActionTags.Contains(Params[0]))
		{
			if(Params.Num() > 2)
			{
				//确认是否第二阶段
				bool bInSecondStage = false;
				TArray<FBuffObj*> BuffList = Cha->GetBuff("IceDevilSecondStage",TArray<AAwCharacter*>());
				if(BuffList.Num() > 0)
					bInSecondStage = true;
				if(!bInSecondStage)
					return Params[1];
				else
					return Params[2];
			}
		}
	}
	return "";
}

UTimelineNode* UIceDevilAIScript::HornBroken(AAwCharacter* Character, FChaPart Part, bool Broken, TArray<FString> Params)
{
	UKismetSystemLibrary::PrintString(Character, FString("Horn Broken"),true,false,FLinearColor::Red);
	Character->PreorderActionByMontageState(ECharacterMontageState::Hurt);
	return nullptr;
}
