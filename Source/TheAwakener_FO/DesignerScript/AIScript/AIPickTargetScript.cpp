// Fill out your copyright notice in the Description page of Project Settings.


#include "AIPickTargetScript.h"

#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"

FVector2D UAIPickTargetScript::DirectToPlayerCharacter(AAwCharacter* AICha, TArray<FString> Tags, TArray<FString> Params)
{
	const AAwCharacter* PlayerCha = UGameplayFuncLib::GetAwGameState()->GetMyCharacter();
	if (!PlayerCha || PlayerCha == AICha) return FVector2D(AICha->GetActorRotation().Vector().X, AICha->GetActorRotation().Vector().Y);
	const FVector TargetTo = PlayerCha->GetActorLocation() - AICha->GetActorLocation();
	return FVector2D(TargetTo.X, TargetTo.Y);
}
