// Fill out your copyright notice in the Description page of Project Settings.


#include "AwakeDragonAIScript.h"

#include "Kismet/KismetMathLibrary.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"

bool UAwakeDragonAIScript::CheckAroundEnemyNumGreater(AAwCharacter* Character, UAwAIComponent* AIComponent,
	TArray<FString> Params)
{
	int TargetNum =  Params.Num()>0?FCString::Atoi(*Params[0]):0;
	int num =AIComponent->GetAllViewedCharacter(false,true).Num();
	
	return  num>=TargetNum;
}

bool UAwakeDragonAIScript::CheckEnemyTargetDistance(AAwCharacter* Character, UAwAIComponent* AIComponent,
	TArray<FString> Params)
{
	float MinDistance = Params.Num()>0?FCString::Atof(*Params[0]):0;
	float MaxDistance = Params.Num()>1?FCString::Atof(*Params[1]):0;

	float CurDistance = AIComponent->GetTargetEnemy().DistanceXY;
	
	return UKismetMathLibrary::InRange_FloatFloat(CurDistance,MinDistance,MaxDistance);
}

bool UAwakeDragonAIScript::CheckActionCoolDown(AAwCharacter* Character, UAwAIComponent* AIComponent,
	TArray<FString> Params)
{
	FString TargetActionId = Params.Num()>0?Params[0]:"";
	if (AIComponent->ActionCDList.Contains(TargetActionId))
	{
		return false;
	}
	return  true;
}

bool UAwakeDragonAIScript::CheckGroundHeight(AAwCharacter* Character, UAwAIComponent* AIComponent,
	TArray<FString> Params)
{
	float MinDistance = Params.Num()>0?FCString::Atof(*Params[0]):0;
	FFindFloorResult Res = FFindFloorResult();
	//Character->GetCharacterMovement()->FindFloor(Character->GetActorLocation(),Res,true);
	
	TArray<TEnumAsByte<EObjectTypeQuery>> ObjectList;
	ObjectList.Add(EObjectTypeQuery::ObjectTypeQuery1);
	FHitResult HitResult;
	bool bHit = UKismetSystemLibrary::LineTraceSingleForObjects(Character, Character->GetActorLocation(), Character->GetActorLocation() + FVector(0,0,MinDistance),
		ObjectList, false, TArray<AActor*>(), EDrawDebugTrace::None,HitResult, true
		//,FLinearColor::Red,FLinearColor::Green, 10
		);
	if(bHit)
	{
		return  true;
	}
	return  false;
}

FAICommand UAwakeDragonAIScript::AIDoActionWithCoolDown(AAwCharacter* Character, UAwAIComponent* AIComponent,
                                                        TArray<FString> Params)
{
	FAICommand NewCommand = FAICommand();
	if (Params.Num())
	{
		NewCommand = AIComponent->CreateUseActionCommand(Params[0], Character->GetActorForwardVector());
		float CoolDown = Params.Num()>1?FCString::Atof(*Params[1]):0;
		AIComponent->ActionCDList.Add(Params[0], CoolDown);
	}
	return NewCommand;
}

FAICommand UAwakeDragonAIScript::AddActionCoolDown(AAwCharacter* Character, UAwAIComponent* AIComponent,
	TArray<FString> Params)
{
	FAICommand NewCommand = FAICommand();
	if (Params.Num())
	{
		float CoolDown = Params.Num()>1?FCString::Atof(*Params[1]):0;
		AIComponent->ActionCDList.Add(Params[0], CoolDown);
	}
	return NewCommand;
}

FAICommand UAwakeDragonAIScript::AITurnToStimulateDoActionInSky(AAwCharacter* Character, UAwAIComponent* AIComponent,
	TArray<FString> Params)
{
	FAICommand NewCommand = FAICommand();
	FVector TargetLoc = Character->GetActorForwardVector() + Character->GetActorLocation();
	if(Character->GetAIComponent()->GetMostThreatTarget())
	{
		TargetLoc = Character->GetAIComponent()->GetMostThreatTarget()->GetActorLocation();
	}
	else if(Character->GetAIComponent()->GetAllViewedCharacter().Num())
	{
		FAIFindChaInfo TargetInfo = Character->GetAIComponent()->GetClosestDistanceEnemy(true,false);
		if(TargetInfo.Character)
			TargetLoc = TargetInfo.Character->GetActorLocation();
	}
	else if(Character->GetAIComponent()->Stimuli_AllySignal.Num())
	{
		TargetLoc = Character->GetAIComponent()->Stimuli_AllySignal.Last().SignalOrigin.GetLocation();
	}
	else if(Character->GetAIComponent()->Stimuli_Voice.Num())
	{
		TargetLoc = Character->GetAIComponent()->Stimuli_Voice.Last().Position;
	}
	float Height = Params.Num()>0?FCString::Atof(*Params[0]):0;
	TargetLoc.Z += Height;
	TArray<FVector> LocList;
	LocList.Add(TargetLoc);
	NewCommand = AIComponent->CreateMoveToCommand(LocList,0);
	// if (Params.Num())
	// {
	// 	FVector CmdPos = AIComponent->GetTargetDirection();
	// 	NewCommand = AIComponent->CreateUseActionCommand(Params[0], CmdPos);
	// 	AIComponent->TargetDirFunc = UDataFuncLib::SplitFuncNameAndParams("MobAIScript.GetStimulateDir()");
	// }
	return NewCommand;
}

FAICommand UAwakeDragonAIScript::AwakeDragonBasicBattle(AAwCharacter* Character, UAwAIComponent* AIComponent,
                                                        TArray<FString> Params)
{
	FAICommand NewCommand = FAICommand();
	FAIFindChaInfo ClosetTarget = FAIFindChaInfo();
	float CheckMinDisXY = 0;
	float CheckNearMaxDisXY = 0;

	float CheckMaxDisXY = 0;

	if (Params.Num() > 2)
	{
		CheckMinDisXY = FCString::Atof(*Params[0]);
		CheckNearMaxDisXY = FCString::Atof(*Params[1]);
		CheckMaxDisXY = FCString::Atof(*Params[2]);
	}
	int Targetnum =  0;
	for (FAIFindChaInfo FindedTarget : AIComponent->GetAllViewedCharacter(false, true))
	{
		if (FindedTarget.Character)
		{
			const float CurDisXY = FindedTarget.DistanceXY;
			const float CurDisZ = FindedTarget.DistanceZ;

			const float CheckDisZ = AIComponent->GetSightZRadius();
			if ((CurDisXY <= CheckMaxDisXY) && (CurDisXY >= CheckMinDisXY) && (CurDisZ < CheckDisZ))
			{
				if (!ClosetTarget.Character)
					ClosetTarget = FindedTarget;
				else if (FindedTarget.DistanceXY < ClosetTarget.DistanceXY)
					ClosetTarget = FindedTarget;
			}
		}
	}
	if (ClosetTarget.Character)
	{
		//检测怪物是否在玩家的正面(如果目标是怪物，则不管正反面)
		bool bInPlayerFront = true;
		if (ClosetTarget.Character->UnderPlayerControl())
		{
			FVector TargetDir = ClosetTarget.Character->GetActorLocation() - Character->GetActorLocation();
			TargetDir.Normalize();
			FVector PlayerCameraDir = ClosetTarget.Character->GetAwCameraComponent()->GetCameraRotate().Vector();
			if (FMath::Abs(UMathFuncLib::GetDegreeBetweenTwoVector(TargetDir, PlayerCameraDir)) < 90)
				bInPlayerFront = false;
		}
			FString ActionId = "";
			int UseComboIndex = FMath::RandRange(0, 5);
			switch (UseComboIndex)
			{
			case 0:
			case 1:
				ActionId = "NormalAttack_S1";
			case 2:
				{
					if (Targetnum>=3)
					{
						ActionId = "TailAttack";
					}
					NewCommand = AIComponent->CreateUseActionCommand(ActionId, Character->GetActorForwardVector());
					break;
				}
			case 3:
				ActionId = "NormalAttack_S2";
			case 4:
				{
					if (Targetnum>=3)
					{
						ActionId = "TailAttack";
					}
					NewCommand = AIComponent->CreateUseActionCommand(ActionId, Character->GetActorForwardVector());
					break;
				}
			case 5:
				ActionId = "TailAttack";
				NewCommand = AIComponent->CreateUseActionCommand(ActionId, Character->GetActorForwardVector());
				break;
			}
			AIComponent->SetTargetEnemy(ClosetTarget);
			AIComponent->TargetDirFunc = UDataFuncLib::SplitFuncNameAndParams("MobAIScript.GetCurTargetEnemyDir()");
		}
	
	return NewCommand;
}
