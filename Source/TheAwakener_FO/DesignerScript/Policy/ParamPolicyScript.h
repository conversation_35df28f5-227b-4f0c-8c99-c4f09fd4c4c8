// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "ParamPolicyScript.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UParamPolicyScript : public UObject
{
	GENERATED_BODY()
public:
	//Float Param Policy

	//通过随机范围获取float
	UFUNCTION(BlueprintCallable)
	static float FloatFromRange(UObject* Caller, TArray<FString> Params);

};
