// Fill out your copyright notice in the Description page of Project Settings.


#include "ParamPolicyScript.h"

float UParamPolicyScript::FloatFromRange(UObject* Caller,  TArray<FString> Params)
{
	if (Params.Num()<2)
	{
		return 0.0f;
	}
	float Min = Params[0].IsNumeric() ? FCString::Atof(*Params[0]): 0.0f;
	float Max = Params[1].IsNumeric()? FCString::Atof(*Params[1]) : 0.0f;

	return FMath::FRandRange(Min,Max);
}


