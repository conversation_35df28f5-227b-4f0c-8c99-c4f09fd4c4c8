// Fill out your copyright notice in the Description page of Project Settings.


#include "JustDodgeScript.h"

#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"


UTimelineNode* UJustDodgeScript::TimeToSlomoAfterJustDodge(AAwCharacter* Dodger, FDamageInfo DamageInfo,
	TArray<FString> Params)
{
	if (Params.Num()<1)
	{
		return nullptr;
	}
	float Duration = Params.Num()>0?FCString::Atof(*Params[0]):0.f;
	float SlomoRate = Params.Num()>1?FCString::Atof(*Params[1]):1.f;
	int Priority = Params.Num()>2?FCString::Atoi(*Params[2]):0.f;
	
	UTimelineManager* TimeManager = UGameplayFuncLib::GetTimelineManager();
	if (TimeManager)
	{
		FTimeSlomoData SlomoData = FTimeSlomoData();
		SlomoData.Duration = Duration;
		SlomoData.Rate =SlomoRate;
		SlomoData.Priority = Priority;
		TimeManager->SetGlobalTimeSlomo(SlomoData);
	}
	return  nullptr;
}
