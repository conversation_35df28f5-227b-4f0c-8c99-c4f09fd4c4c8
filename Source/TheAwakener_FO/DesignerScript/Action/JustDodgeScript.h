// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/GameFramework/Timeline/TimelineNode.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "JustDodgeScript.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UJustDodgeScript : public UObject
{
	GENERATED_BODY()
public:

	UFUNCTION(BlueprintCallable)
	static UTimelineNode* TimeToSlomoAfterJustDodge(AAwCharacter* Dodger,FDamageInfo DamageInfo, TArray<FString> Params);
};
