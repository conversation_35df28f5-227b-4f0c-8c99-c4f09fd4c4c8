// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "TheAwakener_FO/GameFramework/Timeline/TimelineNode.h"
#include "TheAwakener_FO/GamePlay/AOE/AWAoeBase.h"
#include "Ogre_AOEScript.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UOgre_AOEScript : public UObject
{
	GENERATED_BODY()
public:

	UFUNCTION(BlueprintCallable)
	static UTimelineNode* SetCanPickPillarBuffOnCharacterEnter(AAWAoe* AOEObj, TArray<FBeCaughtActorInfo> EnterCharacters, TArray<FString> Params);

	UFUNCTION(BlueprintCallable)
	static UTimelineNode* RemoveCanPickPillarBuffOnCharacterLeave(AAWAoe* AOEObj, TArray<AAwCharacter*> Leave<PERSON>haracters, TArray<FString> Params);

	//OnActorEnter 击碎碰到的石柱子
	UFUNCTION(BlueprintCallable)
	static UTimelineNode* DestroyPillar(AAWAoe* AOEObj, TArray<FBeCaughtActorInfo> EnterActors, TArray<FString> Params);

	UFUNCTION(BlueprintCallable)
	static UTimelineNode* DealDamageByFallingPillar(AAWAoe* AOEObj, TArray<FBeCaughtActorInfo> TouchCharacters, TArray<FString> Params);

	//OnCreate 绑定捕获的柱子
	UFUNCTION(BlueprintCallable)
	static UTimelineNode* ConnectToPillar(AAWAoe* AOEObj,	TArray<FString> Params);
};
