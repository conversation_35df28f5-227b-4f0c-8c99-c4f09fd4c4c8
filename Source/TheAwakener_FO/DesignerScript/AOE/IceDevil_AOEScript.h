// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/Gameframework/Timeline/TimelineNode.h"
#include "TheAwakener_FO/GamePlay/AOE/AWAoeBase.h"
#include "UObject/NoExportTypes.h"
#include "IceDevil_AOEScript.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UIceDevil_AOEScript : public UObject
{
	GENERATED_BODY()
public:
	UFUNCTION(BlueprintCallable)
	static UTimelineNode* FreezingEnemy(AAWAoe* AOEObj, TArray<FBeCaughtActorInfo> EnterCharacters, TArray<FString> Params);
};
