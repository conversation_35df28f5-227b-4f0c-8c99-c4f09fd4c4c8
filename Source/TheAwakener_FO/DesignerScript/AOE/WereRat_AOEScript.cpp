// Fill out your copyright notice in the Description page of Project Settings.


#include "WereRat_AOEScript.h"

#include "TheAwakener_FO/GamePlay/Battle/OffenseManager.h"

UTimelineNode* UWereRat_AOEScript::DealDamageAndStunWereRat(AAWAoe* AOEObj, TArray<FBeCaughtActorInfo> EnterCharacters, TArray<FString> Params)
{
	if (AOEObj)
	{
		if (AOEObj->GetLocalRole() != ENetRole::ROLE_Authority)
			return nullptr;
		for (FBeCaughtActorInfo CurCaughtActor : EnterCharacters)
		{
			AAwCharacter* Character = Cast<AAwCharacter>(CurCaughtActor.BeCaughtActor);
			if (AOEObj->Caster)
			{
				if (AOEObj->Caster->IsEnemy(Character))
				{
					FOffenseInfo Offense = FOffenseInfo();
					Offense.AttackInfo.DamageSourceType = EAttackSource::AoE;
					Offense.AttackHitBoxName = AOEObj->AttackHitManager->GetAllActiveAttackBoxNames();
					Offense.AttackInfo.Elemental = AOEObj->Caster->GetElemental(EChaElemental::Physical, EElementalPickMethod::Set, ESlotInWeaponObj::MainWeapon);
					Offense.AttackInfo.DamagePower = AOEObj->Caster->CharacterObj.BaseProp.PAttack;
					if (Params.Num() > 0)
						Offense.AttackInfo.DamagePower = Offense.AttackInfo.DamagePower * FCString::Atof(*Params[0]);
					Offense.SourceId = AOEObj->Model.Id;
					Offense.CanHitTimes = 1;
					Offense.AttackInfo.DefenderActionChange.Priority = 5;
					Offense.AttackInfo.DefenderActionChange.ChangeMethod = EActionChangeMethod::ToMontageState;
					Offense.AttackInfo.DefenderActionChange.FreezeTime = 0.1f;
					Offense.AttackInfo.DefenderActionChange.ToState = ECharacterMontageState::Hurt;
					Offense.AttackInfo.DefenderActionChange.HitStun.Active = true;
					Offense.AttackInfo.DefenderActionChange.HitStun.Velocity = FVector(100, 0, 0).RotateAngleAxis((Character->GetActorLocation() - AOEObj->GetActorLocation()).Rotation().Yaw, FVector::UpVector);
					Offense.AttackInfo.DefenderActionChange.HitStun.VelocityType = EVelocityType::RelativeDir;
					Offense.AttackInfo.DefenderActionChange.HitStun.InSec = 0.13f;
					UOffenseManager::DoOffense(AOEObj->AttackHitManager, Offense, CurCaughtActor, AOEObj->Caster, false);
				}
				else if(Character->Tags.Contains("WereRatGrenadier"))
				{
					FOffenseInfo Offense = FOffenseInfo();
					Offense.AttackInfo.DamageSourceType = EAttackSource::AoE;
					Offense.AttackHitBoxName = AOEObj->AttackHitManager->GetAllActiveAttackBoxNames();
					Offense.AttackInfo.Elemental = AOEObj->Caster->GetElemental(EChaElemental::Physical, EElementalPickMethod::Set, ESlotInWeaponObj::MainWeapon);
					Offense.AttackInfo.DamagePower = AOEObj->Caster->CharacterObj.BaseProp.PAttack;
					if (Params.Num() > 0)
						Offense.AttackInfo.DamagePower = Offense.AttackInfo.DamagePower * FCString::Atof(*Params[0]);
					Offense.SourceId = AOEObj->Model.Id;
					Offense.CanHitTimes = 1;
					Offense.AttackInfo.DefenderActionChange.Priority = 10;
					Offense.AttackInfo.DefenderActionChange.ChangeMethod = EActionChangeMethod::ChangeActionInfo;
					Offense.AttackInfo.DefenderActionChange.ChangeToActionId = "HurtByAllyBomb";
					Offense.AttackInfo.DefenderActionChange.HitStun.Active = true;
					Offense.AttackInfo.DefenderActionChange.HitStun.Velocity = FVector(100, 0, 0).RotateAngleAxis((Character->GetActorLocation() - AOEObj->GetActorLocation()).Rotation().Yaw, FVector::UpVector);
					Offense.AttackInfo.DefenderActionChange.HitStun.InSec = 0.3f;
					UOffenseManager::DoOffense(AOEObj->AttackHitManager, Offense, CurCaughtActor, AOEObj->Caster, false);
				}
			}
		}
	}
	return nullptr;
}
