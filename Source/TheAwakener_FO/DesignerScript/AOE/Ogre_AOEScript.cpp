// Fill out your copyright notice in the Description page of Project Settings.


#include "Ogre_AOEScript.h"

#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GamePlay/Battle/OffenseManager.h"

UTimelineNode* UOgre_AOEScript::SetCanPickPillarBuffOnCharacterEnter(AAWAoe* AOEObj, TArray<FBeCaughtActorInfo> EnterCharacters, TArray<FString> Params)
{
	if (AOEObj)
	{
		for (auto& CurCatchedCharacter : EnterCharacters)
		{
			AAwCharacter* Character = Cast<AAwCharacter>(CurCatchedCharacter.BeCaughtActor);
			if(Character)
			{
				if (Character->CharacterObj.Tag.Contains("Ogre"))
				{
					FBuffModel BuffModel = UGameplayFuncLib::GetAwDataManager()->GetBuffModelById("CanPullUpPillar");
					if (BuffModel.ValidBuffModel() == false) return nullptr;
					TArray<AAwCharacter*> CasterList;
					CasterList.Add(Character);
					if(Character->GetBuff("CanPullUpPillar",CasterList).Num()) return nullptr;
					Character->AddBuff(FAddBuffInfo(Character, Character, BuffModel, 1, 0, false, true));
				}
			}
		}
	}
	return nullptr;
}


UTimelineNode* UOgre_AOEScript::RemoveCanPickPillarBuffOnCharacterLeave(AAWAoe* AOEObj, TArray<AAwCharacter*> LeaveCharacters, TArray<FString> Params)
{
	if (AOEObj)
	{
		if (AOEObj->GetLocalRole() != ENetRole::ROLE_Authority)
			return nullptr;
		for (auto& CurCatchedCharacter : LeaveCharacters)
		{
			if(CurCatchedCharacter)
				CurCatchedCharacter->RemoveBuffById("CanPullUpPillar");
		}
	}
	return nullptr;
}

UTimelineNode* UOgre_AOEScript::DestroyPillar(AAWAoe* AOEObj, TArray<FBeCaughtActorInfo> EnterActors, TArray<FString> Params)
{
	for (FBeCaughtActorInfo EActor : EnterActors)
	{
		AAwSceneItem* SceneItem = Cast<AAwSceneItem>(EActor.BeCaughtActor);
		if (
			SceneItem && SceneItem->Id == "OgrePillar" 
		){
			SceneItem->Kill();
		}
	}
	return nullptr;
}

UTimelineNode* UOgre_AOEScript::DealDamageByFallingPillar(AAWAoe* AOEObj, TArray<FBeCaughtActorInfo> TouchCharacters, TArray<FString> Params)
{
	if (AOEObj)
	{
		for (auto& CurCatchedCharacter : TouchCharacters)
		{
			AAwCharacter* Character = Cast<AAwCharacter>(CurCatchedCharacter.BeCaughtActor);
			if (Character)
			{
				if (Character->CharacterObj.Tag.Contains("Ogre"))
				{
					FOffenseInfo Offense = FOffenseInfo();
					Offense.AttackInfo.DamageSourceType = EAttackSource::AoE;
					Offense.AttackInfo.Elemental = AOEObj->Caster->GetElemental(EChaElemental::Physical, EElementalPickMethod::Set, ESlotInWeaponObj::MainWeapon);
					if (Params.Num())
						Offense.AttackInfo.DamagePower = FCString::Atoi(*Params[0]);
					if(Params.Num() > 1)
						Offense.AttackInfo.DamagePower = Offense.AttackInfo.DamagePower * FCString::Atof(*Params[1]);
					Offense.SourceId = AOEObj->Model.Id;
					Offense.CanHitTimes = 1;
					Offense.AttackInfo.DefenderActionChange.ChangeMethod = EActionChangeMethod::ChangeActionInfo;
					Offense.AttackInfo.DefenderActionChange.ChangeToActionId = "HurtFromRock";
					Offense.AttackInfo.DefenderActionChange.FreezeTime = 0.1;
					Offense.AttackInfo.DefenderActionChange.Priority = 7;
					Character->BeOffended(Offense, AOEObj->AttackHitManager, nullptr, CurCatchedCharacter.CaughtHitBoxComponent);
					UKismetSystemLibrary::PrintString(AOEObj->Caster, FString("Pillar Hit Ogre!"), true, true, FLinearColor::Red, 10.0f);
				}
				else
				{
					FOffenseInfo Offense = FOffenseInfo();
					Offense.AttackInfo.DamageSourceType = EAttackSource::AoE;
					Offense.AttackInfo.Elemental = AOEObj->Caster->GetElemental(EChaElemental::Physical, EElementalPickMethod::Set, ESlotInWeaponObj::MainWeapon);
					if (Params.Num())
						Offense.AttackInfo.DamagePower = FCString::Atoi(*Params[0]);
					Offense.SourceId = AOEObj->Model.Id;
					Offense.CanHitTimes = 1;
					Offense.AttackInfo.DefenderActionChange.ChangeMethod = EActionChangeMethod::ToMontageState;
					Offense.AttackInfo.DefenderActionChange.ToState = ECharacterMontageState::Blow;
					Offense.AttackInfo.DefenderActionChange.FreezeTime = 0.1;
					Offense.AttackInfo.DefenderActionChange.HitStun.Active = true;
					Offense.AttackInfo.DefenderActionChange.HitStun.Type = EForceMoveType::KnockOut;
					Offense.AttackInfo.DefenderActionChange.HitStun.Velocity =
						FVector(450, 0, 120).RotateAngleAxis((Character->GetActorLocation() - AOEObj->GetActorLocation()).Rotation().Yaw, FVector::UpVector);
					Offense.AttackInfo.DefenderActionChange.HitStun.VelocityType = EVelocityType::RelativeDir;
					Offense.AttackInfo.DefenderActionChange.HitStun.InSec = 0.7;
					Offense.AttackInfo.DefenderActionChange.Priority = 7;
					Character->BeOffended(Offense, AOEObj->AttackHitManager, AOEObj->Caster, CurCatchedCharacter.CaughtHitBoxComponent,
						 AOEObj->AttackHitManager->GetAttackBoxByName(CurCatchedCharacter.AttackBoxName));
					UKismetSystemLibrary::PrintString(AOEObj->Caster, FString("Pillar Hit Player!"), true, true, FLinearColor::Red, 10.0f);
				}
			}
		}
	}
	return nullptr;
}

UTimelineNode* UOgre_AOEScript::ConnectToPillar(AAWAoe* AOEObj, TArray<FString> Params)
{
	for (const TTuple<FString, AAwSceneItem*> SItem : UGameplayFuncLib::GetAwGameState()->SceneItemList)
	{
		if (SItem.Value)
		{
			const float Dis = FVector::Dist2D(SItem.Value->GetActorLocation() , AOEObj->GetActorLocation());
			if (FMath::IsNearlyZero(Dis))
			{
				AOEObj->Param.Add("PillarObj", FString::FromInt(SItem.Value->GetUniqueID()));
				AOEObj->Model.OnTick.Empty();
				AOEObj->Model.TickIntervalTime = 0;
				break;
			}
			
		}
	}
	return nullptr;
}
