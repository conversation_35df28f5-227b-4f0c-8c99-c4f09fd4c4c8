// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/GameFramework/Timeline/TimelineNode.h"
#include "TheAwakener_FO/GamePlay/AOE/AWAoeBase.h"
#include "TheAwakener_FO/GamePlay/Characters/Battle/BattleStructs.h"
#include "AOEScript.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UAOEScript : public UObject
{
	GENERATED_BODY()
public:
	// 辅助函数：创建基础的AOE攻击信息模板
	static FOffenseInfo CreateBaseAOEOffenseInfo(AAWAoe* AOEObj);

	// 辅助函数：检查AOE权限
	static bool CheckAOEAuthority(AAWAoe* AOEObj);

	// 辅助函数：检查角色是否可以被攻击（敌对关系）
	static bool CanAttackCharacter(AAwCharacter* Character, AAWAoe* AOEObj);

	// 辅助函数：检查角色是否可以被治疗（友好关系）
	static bool CanHealCharacter(AAwCharacter* Character, AAWAoe* AOEObj);

	// 辅助函数：检查角色是否可以被熔岩伤害（包含免疫检查）
	static bool CanLavaDamageCharacter(AAwCharacter* Character, AAWAoe* AOEObj);

	// 辅助函数：检查角色是否可以被攻击（支持友伤模式）
	static bool CanAttackCharacterWithFriendlyFire(AAwCharacter* Character, AAWAoe* AOEObj, bool FriendlyFire);

	// 辅助函数：检查角色是否可以被攻击（支持Buff免疫检查）
	static bool CanAttackCharacterWithBuffImmunity(AAwCharacter* Character, AAWAoe* AOEObj, const FString& ImmunityBuffId = "");

	// 辅助函数：检查角色是否可以被攻击（支持闪避检查）
	static bool CanAttackCharacterWithDodgeCheck(AAwCharacter* Character, AAWAoe* AOEObj, const FBeCaughtActorInfo& CaughtInfo);

	// 辅助函数：检查角色是否可以被攻击（排除玩家角色）
	static bool CanAttackNonPlayerCharacter(AAwCharacter* Character, AAWAoe* AOEObj);

	// 辅助函数：检查角色是否可以被攻击（支持团队检查）
	static bool CanAttackCharacterWithTeamCheck(AAwCharacter* Character, AAWAoe* AOEObj, int TeamSide);

	// 包装函数：处理ECS怪物攻击循环（包含AttackHitBoxName.Add逻辑）
	static void ProcessECSAttackLoop(
		AAWAoe* AOEObj,
		const FOffenseInfo& BaseOffense,
		TFunction<void(FOffenseInfo&, const FBeCaughtActorInfo&)> OffenseModifier
	);

	// 辅助函数：设置击退效果（Actor版本）
	static void SetupKnockbackEffect(FOffenseInfo& Offense, FVector LocationOfTarget, AAWAoe* AOEObj,
		const FVector& BaseVelocity, float Duration = 0.5f, int Priority = 5, float FreezeTime = 0.1f,
		ECharacterMontageState ToState = ECharacterMontageState::Blow,
		EVelocityType VelocityType = EVelocityType::RelativeDir);

	//Params[0] = DamagePower
	UFUNCTION(BlueprintCallable)
	static UTimelineNode* DealDamageOnTick(AAWAoe* AOEObj, TArray<FString> Params);
	//Params[0] = DamagePower
	//Params[1] = BreakPower
	//Params[2] = DamageElementType
	//Params[3] = DamageType
	//Params[4] = ActionChangeMethod
	//Params[5] = ChangeToStateString
	//Params[6] = ActionChangePriority
	//Params[7] = CanHitTimes
	UFUNCTION(BlueprintCallable)
	static UTimelineNode* DealDamageByCsterPropOnTick(AAWAoe* AOEObj, TArray<FString> Params);

	//Params[0] = DamagePower
	//Params[1] = BreakPower
	//Params[2] = DamageElementType
	//Params[3] = DamageType
	//Params[4] = ActionChangeMethod
	//Params[5] = ChangeToStateString
	//Params[6] = ActionChangePriority
	//Params[7] = CanHitTimes
	UFUNCTION(BlueprintCallable)
	static UTimelineNode* DealDamageByCasterPropOnTick(AAWAoe* AOEObj, TArray<FString> Params);

	
	//Params[0] = AddDamagePower
	//Params[1] = DamagePowerRate
	//Params[2] = DamageType
	UFUNCTION(BlueprintCallable)
	static UTimelineNode* DealHealByCasterPropOnTick(AAWAoe* AOEObj, TArray<FString> Params);
	//Params[0] = DamagePower
	UFUNCTION(BlueprintCallable)
	static UTimelineNode* DealPercentDamageOnTick(AAWAoe* AOEObj, TArray<FString> Params);
	//Params[0] = 对目标的百分比伤害
	UFUNCTION(BlueprintCallable)
	static UTimelineNode* DoLavaDamageOnTick(AAWAoe* AOEObj, TArray<FString> Params);
	//Params[0] = 对目标的百分比伤害
	UFUNCTION(BlueprintCallable)
	static UTimelineNode* DoLavaDamageOnTouch(AAWAoe* AOEObj, TArray<FBeCaughtActorInfo> TouchCharacters, TArray<FString> Params);
	//【OnTick】对Touch到的敌人添加一个buff
	//Params[0] : BuffId
	//Params[1] : BuffStack
	//Params[2] : BuffDuration
	UFUNCTION(BlueprintCallable)
	static UTimelineNode* AddBuffToEnemyOnTick(AAWAoe* AOEObj, TArray<FString> Params);
	
	//Params[0] = DamagePower Params[1] = MontageChangeMethod
	//Params[5] = 根据输入的 BuffId 来免疫此次伤害
	UFUNCTION(BlueprintCallable)
	static UTimelineNode* DealDamageOnTouch(AAWAoe* AOEObj, TArray<FBeCaughtActorInfo> TouchCharacters, TArray<FString> Params);

	//Params[0] = DamagePower; Params[1] = DamageElemental; Params[2] = MaxHitTimes
	UFUNCTION(BlueprintCallable)
	static UTimelineNode* DealDotDamageOnTick(AAWAoe* AOEObj, TArray<FString> Params);
	
	UFUNCTION(BlueprintCallable)
    static UTimelineNode* DealMoveVelocityOnTouch(AAWAoe* AOEObj, TArray<FBeCaughtActorInfo> TouchCharacters, TArray<FString> Params);

	UFUNCTION(BlueprintCallable)
	static UTimelineNode* DealMoveVelocityToAoeCasterOnTouch(AAWAoe* AOEObj, TArray<FBeCaughtActorInfo> TouchCharacters, TArray<FString> Params);
	
	UFUNCTION(BlueprintCallable)
	static UTimelineNode* DealMoveVelocityToAoeCenterOnTouch(AAWAoe* AOEObj, TArray<FBeCaughtActorInfo> TouchCharacters, TArray<FString> Params);
	
	//Params[0] = DamagePowerRate
    UFUNCTION(BlueprintCallable)
    static UTimelineNode* DealDamageByCsterPropOnTouch(AAWAoe* AOEObj, TArray<FBeCaughtActorInfo> TouchCharacters, TArray<FString> Params);
	
	UFUNCTION(BlueprintCallable)
	static UTimelineNode* FireAoeByItem(AAWAoe* AOEObj, TArray<FBeCaughtActorInfo> TouchCharacters, TArray<FString> Params);

	//固定伤害
	UFUNCTION(BlueprintCallable)
	static UTimelineNode* FireAoeByTeam(AAWAoe* AOEObj, TArray<FBeCaughtActorInfo> TouchCharacters, TArray<FString> Params);

	//根据Aoe生成者的攻击力造成伤害
	//Parmas[0] : 攻击力百分比 float
	//Parmas[1] : 攻击元素类型
	//Parmas[2] : 攻击力类型 EDamageType
	//Parmas[3] : 受击者是受击还是击飞 ECharacterMontageState
	//Parmas[4] : Velocity.X
	//Parmas[5] : Velocity.Y
	//Parmas[6] : Velocity.Z
	//Parmas[7] : DefenderActionChange.Priority int
	UFUNCTION(BlueprintCallable)
	static UTimelineNode* DealDamageByCasterProp(AAWAoe* AOEObj, TArray<FBeCaughtActorInfo> TouchCharacters, TArray<FString> Params);

	//【OnHit】对Touch到的敌人添加一个buff
	//Params[0] : BuffId
	//Params[1] : BuffStack
	//Params[2] : BuffDuration
	UFUNCTION(BlueprintCallable)
	static UTimelineNode* AddBuffToEnemyOnHit(AAWAoe* AOEObj, TArray<FBeCaughtActorInfo> TouchCharacters, TArray<FString> Params);

	//【OnCharacterLeave】对Leave的敌人去除buff
	//Params[0] : BuffId
	UFUNCTION(BlueprintCallable)
	static UTimelineNode* RemoveBuffOnCharacterLeave(AAWAoe* AOEObj, TArray<AAwCharacter*> LeaveCharacters, TArray<FString> Params);

	//【OnRemoved】对Caught的敌人去除buff
	//Params[0] : BuffId
	UFUNCTION(BlueprintCallable)
	static UTimelineNode* RemoveBuffOnAoeRemoved(AAWAoe* AOEObj, TArray<FString> Params);

	
	UFUNCTION(BlueprintCallable)
	static UTimelineNode* FireAoeHitActors(AAWAoe* AOEObj, TArray<FBeCaughtActorInfo> EnterActors, TArray<FString> Params);
	
	//Params[0] = DamagePower
	//Params[1] = 伤害元素
	//Params[2] = 伤害类型
	//Params[3] = FreezeTime
	//Params[4] = Blow.Velocity.X
	//Params[5] = Blow.Velocity.Y
	//Params[6] = Blow.Velocity.Z
	//Params[7] = 击飞方向（BlowByAOE 或者 BlowByCaster）
	//Params[8] = Blow.InSec
	//Params[9] = Blow.Priority
	UFUNCTION(BlueprintCallable)
	static UTimelineNode* DealDamageAndBlowOnTouch(AAWAoe* AOEObj, TArray<FBeCaughtActorInfo> EnterCharacters, TArray<FString> Params);

	//[OnCreate] Params[0]=VFXPath
	UFUNCTION(BlueprintCallable)
	static UTimelineNode* PlayVFXOnCreate(AAWAoe* AOEObj, TArray<FString> Params);

	//Params[0] = DamagePower [1] = true代表能命中友方和自己
	UFUNCTION(BlueprintCallable)
	static UTimelineNode* DealDamageOnRemoved(AAWAoe* AOEObj, TArray<FString> Params);
	//[OnRemoved] 制造另一个Aoe在原地 [0]AoeId, [1]持续时间
	UFUNCTION(BlueprintCallable)
	static UTimelineNode* CreateAoeOnRemoved(AAWAoe* AOEObj, TArray<FString> Params);

	UFUNCTION(BlueprintCallable)
	static FVector AOETweenDown(float TimeElapsed, AAWAoe* AOEObj, FVector OriginPosition, FVector OriginDirection, TArray<FString> Params);

	UFUNCTION(BlueprintCallable)
	static FVector AOETweenSinForward(float TimeElapsed, AAWAoe* AOEObj, FVector OriginPosition, FVector OriginDirection, TArray<FString> Params);
	
	UFUNCTION(BlueprintCallable)
	static FVector AOETweenDownDelaySeconds(float TimeElapsed, AAWAoe* AOEObj, FVector OriginPosition, FVector OriginDirection, TArray<FString> Params);

	UFUNCTION(BlueprintCallable)
	static FVector AOETweenAccelerateDownDelaySeconds(float TimeElapsed, AAWAoe* AOEObj, FVector OriginPosition, FVector OriginDirection, TArray<FString> Params);
	
	UFUNCTION(BlueprintCallable)
	static UTimelineNode* SetCanGetCheeseBuffOnCharacterEnter(AAWAoe* AOEObj, TArray<FBeCaughtActorInfo> EnterCharacters, TArray<FString> Params);

	UFUNCTION(BlueprintCallable)
	static UTimelineNode* RemoveCanGetCheeseBuffOnCharacterLeave(AAWAoe* AOEObj, TArray<AAwCharacter*> LeaveCharacters, TArray<FString> Params);

	UFUNCTION(BlueprintCallable)
	static UTimelineNode* DestroySceneItem(AAWAoe* AOEObj, TArray<FBeCaughtActorInfo> EnterActors, TArray<FString> Params);

	UFUNCTION(BlueprintCallable)
	static UTimelineNode* DestroyRat(AAWAoe* AOEObj, TArray<FBeCaughtActorInfo> EnterCharacters, TArray<FString> Params);

	UFUNCTION(BlueprintCallable)
	static UTimelineNode* TestAOECreate(AAWAoe* AOEObj, TArray<FString> Params);

	UFUNCTION(BlueprintCallable)
	static FVector TestAOETween(float TimeElapsed, AAWAoe* AOEObj, FVector OriginPosition, FVector OriginDirection, TArray<FString> Params);

	/**
	 * 血球aoe
	 * 当有玩家进入的时候，我们认为他拾取了这个血球
	 * 因此会移除自己，创建另外一个更大的aoe，来捕捉到底多少角色需要被回血等
	 * @param AOEObj 这个血球，是一个AoeObj
	 * @param EnterCharacters 进入血球的角色，会判断是否符合拾取条件
	 * @param Params 策划配置在函数中的参数，[0]aoe的id，[1]aoe的半径（厘米），如果有被动技能影响的话，可以有更多，比如被动技能能增加血球恢复范围等
	 */
	UFUNCTION(BlueprintCallable)
	static UTimelineNode* PickupBloodBall(AAWAoe* AOEObj, TArray<FBeCaughtActorInfo> EnterCharacters, TArray<FString> Params);
};
