// Fill out your copyright notice in the Description page of Project Settings.


#include "AOEScript.h"

#include "Located.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GamePlay/AOE/AoeConfig.h"
#include "TheAwakener_FO/GamePlay/ApparatusCustom/ECSDamageLib.h"
#include "TheAwakener_FO/GamePlay/ApparatusCustom/MechanicRunner.h"
#include "TheAwakener_FO/GamePlay/ApparatusCustom/Traits/Health.h"
#include "TheAwakener_FO/GamePlay/Battle/OffenseManager.h"

// 创建基础的AOE攻击信息模板
FOffenseInfo UAOEScript::CreateBaseAOEOffenseInfo(AAWAoe* AOEObj)
{
	FOffenseInfo BaseOffense = FOffenseInfo();
	if (AOEObj)
	{
		BaseOffense.AttackInfo.DamageSourceType = EAttackSource::AoE;
		BaseOffense.SourceId = AOEObj->Model.Id;
		BaseOffense.CanHitTimes = 1;
		BaseOffense.Index = AOEObj->CurTickedNum;
	}
	return BaseOffense;
}

// 检查AOE权限
bool UAOEScript::CheckAOEAuthority(AAWAoe* AOEObj)
{
	return AOEObj && AOEObj->GetLocalRole() == ENetRole::ROLE_Authority;
}

// 检查角色是否可以被攻击（敌对关系）
bool UAOEScript::CanAttackCharacter(AAwCharacter* Character, AAWAoe* AOEObj)
{
	if (!IsValid(Character) || Character->Dead(true))
		return false;

	if (AOEObj->Caster && !AOEObj->Caster->IsEnemy(Character))
		return false;

	return true;
}

// 检查角色是否可以被治疗（友好关系）
bool UAOEScript::CanHealCharacter(AAwCharacter* Character, AAWAoe* AOEObj)
{
	if (!IsValid(Character) || Character->Dead(true))
		return false;

	if (AOEObj->Caster && AOEObj->Caster->IsEnemy(Character))
		return false;

	return true;
}

// 检查角色是否可以被熔岩伤害（包含免疫检查）
bool UAOEScript::CanLavaDamageCharacter(AAwCharacter* Character, AAWAoe* AOEObj)
{
	if (!CanAttackCharacter(Character, AOEObj))
		return false;

	if (Character->GetBuff("ImmuneLava").Num() > 0)
		return false;

	return true;
}

// 检查角色是否可以被攻击（支持友伤模式）
bool UAOEScript::CanAttackCharacterWithFriendlyFire(AAwCharacter* Character, AAWAoe* AOEObj, bool FriendlyFire)
{
	if (!IsValid(Character) || Character->Dead(true))
		return false;

	if (AOEObj->Caster && !FriendlyFire)
	{
		if (!AOEObj->Caster->IsEnemy(Character))
			return false;
	}

	return true;
}

// 检查角色是否可以被攻击（支持Buff免疫检查）
bool UAOEScript::CanAttackCharacterWithBuffImmunity(AAwCharacter* Character, AAWAoe* AOEObj, const FString& ImmunityBuffId)
{
	if (!CanAttackCharacter(Character, AOEObj))
		return false;

	if (!ImmunityBuffId.IsEmpty() && Character->GetBuff(ImmunityBuffId).Num() > 0)
		return false;

	return true;
}

// 检查角色是否可以被攻击（支持闪避检查）
bool UAOEScript::CanAttackCharacterWithDodgeCheck(AAwCharacter* Character, AAWAoe* AOEObj, const FBeCaughtActorInfo& CaughtInfo)
{
	if (!CanAttackCharacter(Character, AOEObj))
		return false;

	UCharacterHitBoxData* CharacterHitBox = Cast<UCharacterHitBoxData>(CaughtInfo.CaughtHitBoxData);
	if (CharacterHitBox && CharacterHitBox->AsJustDodge.Active)
		return false;

	return true;
}

// 检查角色是否可以被攻击（排除玩家角色）
bool UAOEScript::CanAttackNonPlayerCharacter(AAwCharacter* Character, AAWAoe* AOEObj)
{
	if (!IsValid(Character) || Character->Dead(true))
		return false;

	if (Character->IsPlayerCharacter())
		return false;

	return true;
}

// 检查角色是否可以被攻击（支持团队检查）
bool UAOEScript::CanAttackCharacterWithTeamCheck(AAwCharacter* Character, AAWAoe* AOEObj, int TeamSide)
{
	if (!IsValid(Character) || Character->Dead(true))
		return false;

	if (Character->Side == TeamSide)
		return false;

	return true;
}

// 设置击退效果（Actor版本）
void UAOEScript::SetupKnockbackEffect(FOffenseInfo& Offense, FVector LocationOfTarget, AAWAoe* AOEObj, const FVector& BaseVelocity, float Duration, int Priority, float FreezeTime, ECharacterMontageState ToState,EVelocityType VelocityType)
{
	Offense.AttackInfo.DefenderActionChange.ChangeMethod = EActionChangeMethod::ToMontageState;
	Offense.AttackInfo.DefenderActionChange.ToState = ToState;
	Offense.AttackInfo.DefenderActionChange.FreezeTime = FreezeTime;
	Offense.AttackInfo.DefenderActionChange.HitStun.Active = true;
	Offense.AttackInfo.DefenderActionChange.HitStun.Type = EForceMoveType::KnockOut;
	if (AOEObj)
	{
		Offense.AttackInfo.DefenderActionChange.HitStun.Velocity =
			BaseVelocity.RotateAngleAxis((LocationOfTarget - AOEObj->GetActorLocation()).Rotation().Yaw, FVector::UpVector);
	}
	Offense.AttackInfo.DefenderActionChange.HitStun.VelocityType = VelocityType;
	Offense.AttackInfo.DefenderActionChange.HitStun.InSec = Duration;
	Offense.AttackInfo.DefenderActionChange.Priority = Priority;
}
UTimelineNode* UAOEScript::DealDamageOnTick(AAWAoe* AOEObj, TArray<FString> Params)
{
	if (!CheckAOEAuthority(AOEObj))
		return nullptr;

	// 创建基础攻击信息模板
	FOffenseInfo BaseOffense = CreateBaseAOEOffenseInfo(AOEObj);
		if (AOEObj->Caster)
			BaseOffense.AttackInfo.Elemental = AOEObj->Caster->GetElemental(EChaElemental::Physical, EElementalPickMethod::Set, ESlotInWeaponObj::MainWeapon);
		if(Params.Num())
			BaseOffense.AttackInfo.DamagePower = FCString::Atoi(*Params[0]);

	for (auto& CurCatchedCharacter : AOEObj->GetCaughtCharacters())
	{
		AAwCharacter* Character = Cast<AAwCharacter>(CurCatchedCharacter.BeCaughtActor);
		if (!CanAttackCharacter(Character, AOEObj))
			continue;
		Character->BeOffended(BaseOffense, AOEObj->AttackHitManager, AOEObj->Caster, CurCatchedCharacter.CaughtHitBoxComponent);
	}

	// 使用包装函数处理ECS攻击循环
	ProcessECSAttackLoop(AOEObj, BaseOffense, [&](FOffenseInfo& Offense, const FBeCaughtActorInfo& CurCatchedCharacter) {});

	return nullptr;
}

UTimelineNode* UAOEScript::DealDamageByCsterPropOnTick(AAWAoe* AOEObj, TArray<FString> Params)
{
	if (!CheckAOEAuthority(AOEObj))
		return nullptr;
	const UAoeConfig* Config = GetDefault<UAoeConfig>();
	const float LevelDamageMultiplier = Config->DamageMultipliers[Config&&AOEObj->ActionLevel<4?AOEObj->ActionLevel:3];

	// 创建基础攻击信息模板
	FOffenseInfo BaseOffense = CreateBaseAOEOffenseInfo(AOEObj);

	// 设置与TouchCharacter无关的攻击信息
	float DamagePower = Params.Num()>0?FCString::Atof(*Params[0]):0;
	float BreakRate = Params.Num()>1?FCString::Atof(*Params[1]):0;
	const FString DamageTypeKey = Params.Num()>2?Params[2]:"Physical" ;
	BaseOffense.AttackInfo.DamageType = Params.Num()>3?UDataFuncLib::FStringToEnum<EDamageType>(Params[3]):EDamageType::DirectDamage;
	FString ChangeMethodString =Params.Num()>4?Params[4]:"";
	FString ChangeToStateString =Params.Num()>5?Params[5]:"";
	int ActionChangePriority = Params.Num()>6?FCString::Atof(*Params[6]):7;

	// 设置伤害信息
	BaseOffense.AttackInfo.DamagePower.Physical = AOEObj->Caster? DamagePower*AOEObj->Caster->CharacterObj.CurProperty.PAttack:DamagePower * LevelDamageMultiplier;
	BaseOffense.AttackInfo.DamagePower.Break = BreakRate * LevelDamageMultiplier;
	EChaElemental ElementalType = UDataFuncLib::FStringToEnum<EChaElemental>(DamageTypeKey);
	BaseOffense.AttackInfo.Elemental = ElementalType>EChaElemental::Physical?ElementalType:EChaElemental::Physical;

	// 设置动作变化
	EActionChangeMethod ChangeMethod = ChangeMethodString.IsEmpty()?EActionChangeMethod::Keep:UDataFuncLib::FStringToEnum<EActionChangeMethod>(ChangeMethodString);
	BaseOffense.AttackInfo.DefenderActionChange.ChangeMethod = ChangeMethod;
	BaseOffense.AttackInfo.DefenderActionChange.ToState = ChangeToStateString.IsEmpty()?ECharacterMontageState::Hurt:UDataFuncLib::FStringToEnum<ECharacterMontageState>(ChangeToStateString);
	BaseOffense.AttackInfo.DefenderActionChange.Priority = ActionChangePriority;
	BaseOffense.CanHitTimes = Params.Num()>7?FCString::Atof(*Params[7]):1;

	// 设置VFX和SFX（与TouchCharacter无关）
	if (AOEObj->HitVFX)
		UGameplayFuncLib::SetHitVFX(BaseOffense.AttackInfo.DefenderActionChange,AOEObj->HitVFX);
	if (AOEObj->HitSFX)
		UGameplayFuncLib::SetHitSFX(BaseOffense.AttackInfo.DefenderActionChange,AOEObj->HitSFX);

	for (auto& CurCatchedCharacter : AOEObj->GetCaughtCharacters())
	{
		AAwCharacter* Character = Cast<AAwCharacter>(CurCatchedCharacter.BeCaughtActor);
		if (!CanAttackCharacter(Character, AOEObj))
			continue;

		// 复制基础模板并修改与TouchCharacter相关的属性
		FOffenseInfo Offense = BaseOffense;
		Offense.AttackHitBoxName.Add(CurCatchedCharacter.AttackBoxName);

		// 如果是击飞状态，设置击退效果（与Character相关）
		if(Offense.AttackInfo.DefenderActionChange.ToState == ECharacterMontageState::Blow)
		{
			SetupKnockbackEffect(Offense, Character->GetActorLocation(), AOEObj, FVector(200, 0, 50), 0.3f, ActionChangePriority, 0.1f, ECharacterMontageState::Blow);
		}

		FOffendedCaughtResult Result = Character->CanBeOffended(Offense, AOEObj->AttackHitManager, AOEObj->Caster, false);
		if(Result.Hit)
			Character->BeOffended(Offense, AOEObj->AttackHitManager, AOEObj->Caster, CurCatchedCharacter.CaughtHitBoxComponent);
	}

	// 使用包装函数处理ECS攻击循环
	ProcessECSAttackLoop(AOEObj, BaseOffense, [&](FOffenseInfo& Offense, const FBeCaughtActorInfo& CurCatchedCharacter) {
		Offense.AttackHitBoxName.Add(CurCatchedCharacter.AttackBoxName);

		// ECS怪物也需要击退效果设置
		if(Offense.AttackInfo.DefenderActionChange.ToState == ECharacterMontageState::Blow && CurCatchedCharacter.BeCaughtSubject.IsValid())
		{
			// 获取ECS怪物位置并设置击退效果
			auto LocatedTrait = CurCatchedCharacter.BeCaughtSubject.GetTraitPtr<FLocated, EParadigm::Unsafe>();
			if(LocatedTrait)
			{
				SetupKnockbackEffect(Offense, LocatedTrait->Location, AOEObj, FVector(200, 0, 50), 0.3f, ActionChangePriority, 0.1f, ECharacterMontageState::Blow);
			}
		}
	});

	return nullptr;
}

UTimelineNode* UAOEScript::DealDamageByCasterPropOnTick(AAWAoe* AOEObj, TArray<FString> Params)
{
	if (!CheckAOEAuthority(AOEObj))
		return nullptr;
	const UAoeConfig* Config = GetDefault<UAoeConfig>();
	const float LevelDamageMultiplier = Config->DamageMultipliers[Config&&AOEObj->ActionLevel<4?AOEObj->ActionLevel:3];

	// 创建基础攻击信息模板
	FOffenseInfo BaseOffense = CreateBaseAOEOffenseInfo(AOEObj);

	// 设置与TouchCharacter无关的攻击信息
	float DamagePower = Params.Num()>0?FCString::Atof(*Params[0]):0;
	float BreakRate = Params.Num()>1?FCString::Atof(*Params[1]):0;
	const FString DamageTypeKey = Params.Num()>2?Params[2]:"Physical" ;
	BaseOffense.AttackInfo.DamageType = Params.Num()>3?UDataFuncLib::FStringToEnum<EDamageType>(Params[3]):EDamageType::DirectDamage;
	FString ChangeMethodString =Params.Num()>4?Params[4]:"";
	FString ChangeToStateString =Params.Num()>5?Params[5]:"";
	int ActionChangePriority = Params.Num()>6?FCString::Atof(*Params[6]):7;

	// 设置伤害信息
	BaseOffense.AttackInfo.DamagePower.Physical = AOEObj->Caster? DamagePower*AOEObj->Caster->CharacterObj.CurProperty.PAttack:DamagePower * LevelDamageMultiplier;
	BaseOffense.AttackInfo.DamagePower.Break = BreakRate * LevelDamageMultiplier;
	EChaElemental ElementalType = UDataFuncLib::FStringToEnum<EChaElemental>(DamageTypeKey);
	BaseOffense.AttackInfo.Elemental = ElementalType>EChaElemental::Physical?ElementalType:EChaElemental::Physical;

	// 设置动作变化
	EActionChangeMethod ChangeMethod = ChangeMethodString.IsEmpty()?EActionChangeMethod::Keep:UDataFuncLib::FStringToEnum<EActionChangeMethod>(ChangeMethodString);
	BaseOffense.AttackInfo.DefenderActionChange.ChangeMethod = ChangeMethod;
	BaseOffense.AttackInfo.DefenderActionChange.ToState = ChangeToStateString.IsEmpty()?ECharacterMontageState::Hurt:UDataFuncLib::FStringToEnum<ECharacterMontageState>(ChangeToStateString);
	BaseOffense.AttackInfo.DefenderActionChange.Priority = ActionChangePriority;
	BaseOffense.CanHitTimes = Params.Num()>7?FCString::Atof(*Params[7]):1;

	for (auto& CurCatchedCharacter : AOEObj->GetCaughtCharacters())
	{
		AAwCharacter* Character = Cast<AAwCharacter>(CurCatchedCharacter.BeCaughtActor);
		if (!CanAttackCharacter(Character, AOEObj))
			continue;

		// 复制基础模板并修改与TouchCharacter相关的属性
		FOffenseInfo Offense = BaseOffense;
		Offense.AttackHitBoxName.Add(CurCatchedCharacter.AttackBoxName);

		Character->BeOffended(Offense, AOEObj->AttackHitManager, AOEObj->Caster, CurCatchedCharacter.CaughtHitBoxComponent);
	}

	// 使用包装函数处理ECS攻击循环
	ProcessECSAttackLoop(AOEObj, BaseOffense, [](FOffenseInfo& Offense, const FBeCaughtActorInfo& CurCatchedCharacter) {
		Offense.AttackHitBoxName.Add(CurCatchedCharacter.AttackBoxName);
	});

	return nullptr;
}

UTimelineNode* UAOEScript::DealHealByCasterPropOnTick(AAWAoe* AOEObj, TArray<FString> Params)
{
	if (!CheckAOEAuthority(AOEObj))
		return nullptr;
	const UAoeConfig* Config = GetDefault<UAoeConfig>();
	const float LevelDamageMultiplier = Config->DamageMultipliers[Config&&AOEObj->ActionLevel<4?AOEObj->ActionLevel:3];

	// 创建基础攻击信息模板
	FOffenseInfo BaseOffense = CreateBaseAOEOffenseInfo(AOEObj);

	// 设置与TouchCharacter无关的治疗信息
	BaseOffense.AttackInfo = FAttackInfo();
	BaseOffense.AttackInfo.DamageSourceType = EAttackSource::AoE; // 重新设置，因为上面重置了AttackInfo
	float DamagePower = Params.Num()>0?FCString::Atof(*Params[0]):0;
	float Rate = Params.Num()>1?FCString::Atof(*Params[1]):0;
	BaseOffense.AttackInfo.DamageType = Params.Num()>2?UDataFuncLib::FStringToEnum<EDamageType>(Params[2]):EDamageType::DirectDamage;
	BaseOffense.AttackInfo.IsHeal = true;
	BaseOffense.AttackInfo.DamagePower = AOEObj->Caster? DamagePower+Rate*AOEObj->Caster->CharacterObj.CurProperty.HP:DamagePower * LevelDamageMultiplier;

	for (auto& CurCatchedCharacter : AOEObj->GetCaughtCharacters())
	{
		AAwCharacter* Character = Cast<AAwCharacter>(CurCatchedCharacter.BeCaughtActor);
		if (!CanHealCharacter(Character, AOEObj))
			continue;

		// 复制基础模板并修改与TouchCharacter相关的属性
		FOffenseInfo Offense = BaseOffense;
		Offense.AttackHitBoxName.Add(CurCatchedCharacter.AttackBoxName);

		Character->BeOffended(Offense, AOEObj->AttackHitManager, AOEObj->Caster, CurCatchedCharacter.CaughtHitBoxComponent);
	}

	// 使用包装函数处理ECS攻击循环（治疗）
	ProcessECSAttackLoop(AOEObj, BaseOffense, [](FOffenseInfo& Offense, const FBeCaughtActorInfo& CurCatchedCharacter) {
		Offense.AttackHitBoxName.Add(CurCatchedCharacter.AttackBoxName);
	});

	return nullptr;
}

UTimelineNode* UAOEScript::DealPercentDamageOnTick(AAWAoe* AOEObj, TArray<FString> Params)
{
	if (!CheckAOEAuthority(AOEObj))
		return nullptr;
	const UAoeConfig* Config = GetDefault<UAoeConfig>();
	const float LevelDamageMultiplier = Config->DamageMultipliers[Config&&AOEObj->ActionLevel<4?AOEObj->ActionLevel:3];

	// 创建基础攻击信息模板
	FOffenseInfo BaseOffense = CreateBaseAOEOffenseInfo(AOEObj);

	// 设置与TouchCharacter无关的攻击信息
	if(AOEObj->Caster)
		BaseOffense.AttackInfo.Elemental = AOEObj->Caster->GetElemental(EChaElemental::Physical, EElementalPickMethod::Set, ESlotInWeaponObj::MainWeapon);

	for (auto& CurCatchedCharacter : AOEObj->GetCaughtCharacters())
	{
		AAwCharacter* Character = Cast<AAwCharacter>(CurCatchedCharacter.BeCaughtActor);
		if (!CanAttackCharacter(Character, AOEObj))
			continue;

		// 复制基础模板并修改与TouchCharacter相关的属性
		FOffenseInfo Offense = BaseOffense;
		if(Params.Num())
		{
			float Damage = FCString::Atof(*Params[0]) * Character->CharacterObj.CurProperty.HP;
			Damage = Damage < 1 ? 1 : Damage;
			Offense.AttackInfo.DamagePower = Damage * LevelDamageMultiplier;
		}
		Character->BeOffended(Offense, AOEObj->AttackHitManager, AOEObj->Caster, CurCatchedCharacter.CaughtHitBoxComponent);
	}

	// 使用包装函数处理ECS攻击循环（百分比伤害）
	// 注意：ECS怪物的百分比伤害计算需要在lambda中处理
	ProcessECSAttackLoop(AOEObj, BaseOffense, [&](FOffenseInfo& Offense, const FBeCaughtActorInfo& CurCatchedCharacter) {
		if(Params.Num() && CurCatchedCharacter.BeCaughtSubject.IsValid())
		{
			// 获取ECS怪物的HP信息来计算百分比伤害
			auto HealthTrait = CurCatchedCharacter.BeCaughtSubject.GetTraitPtr<FHealth, EParadigm::Unsafe>();
			if(HealthTrait)
			{
				float Damage = FCString::Atof(*Params[0]) * HealthTrait->Maximum;
				Damage = Damage < 1 ? 1 : Damage;
				Offense.AttackInfo.DamagePower = Damage;
			}
		}
		Offense.AttackHitBoxName.Add(CurCatchedCharacter.AttackBoxName);
	});

	return nullptr;
}

UTimelineNode* UAOEScript::DoLavaDamageOnTick(AAWAoe* AOEObj, TArray<FString> Params)
{
	if (!CheckAOEAuthority(AOEObj))
		return nullptr;
	const UAoeConfig* Config = GetDefault<UAoeConfig>();
	const float LevelDamageMultiplier = Config->DamageMultipliers[Config&&AOEObj->ActionLevel<4?AOEObj->ActionLevel:3];

	// 创建基础攻击信息模板
	FOffenseInfo BaseOffense = CreateBaseAOEOffenseInfo(AOEObj);

	// 设置与TouchCharacter无关的攻击信息
	BaseOffense.AttackInfo.DamageType = EDamageType::PeriodDamage;
	BaseOffense.AttackInfo.Elemental = EChaElemental::Fire;
	if (AOEObj->HitVFX)
		UGameplayFuncLib::SetHitVFX(BaseOffense.AttackInfo.DefenderActionChange,AOEObj->HitVFX);
	if (AOEObj->HitSFX)
		UGameplayFuncLib::SetHitSFX(BaseOffense.AttackInfo.DefenderActionChange,AOEObj->HitSFX);

	for (auto& CurCatchedCharacter : AOEObj->GetCaughtCharacters())
	{
		AAwCharacter* Character = Cast<AAwCharacter>(CurCatchedCharacter.BeCaughtActor);
		if (!CanLavaDamageCharacter(Character, AOEObj))
			continue;

		// 复制基础模板并修改与TouchCharacter相关的属性
		FOffenseInfo Offense = BaseOffense;
		if(Params.Num())
		{
			float Damage = FCString::Atof(*Params[0]) * Character->CharacterObj.CurProperty.HP;
			Damage = Damage < 1 ? 1 : Damage;
			Offense.AttackInfo.DamagePower = Damage * LevelDamageMultiplier;
		}
		Character->BeOffended(Offense, AOEObj->AttackHitManager, AOEObj->Caster, CurCatchedCharacter.CaughtHitBoxComponent);
	}

	// 使用包装函数处理ECS攻击循环（熔岩伤害）
	ProcessECSAttackLoop(AOEObj, BaseOffense, [&](FOffenseInfo& Offense, const FBeCaughtActorInfo& CurCatchedCharacter) {
		if(Params.Num() && CurCatchedCharacter.BeCaughtSubject.IsValid())
		{
			// 获取ECS怪物的HP信息来计算百分比伤害
			auto HealthTrait = CurCatchedCharacter.BeCaughtSubject.GetTraitPtr<FHealth, EParadigm::Unsafe>();
			if(HealthTrait)
			{
				float Damage = FCString::Atof(*Params[0]) * HealthTrait->Maximum;
				Damage = Damage < 1 ? 1 : Damage;
				Offense.AttackInfo.DamagePower = Damage * LevelDamageMultiplier;
			}
		}
		Offense.AttackHitBoxName.Add(CurCatchedCharacter.AttackBoxName);
	});

	return nullptr;
}

UTimelineNode* UAOEScript::DoLavaDamageOnTouch(AAWAoe* AOEObj, TArray<FBeCaughtActorInfo> TouchCharacters,
	TArray<FString> Params)
{
	if (!CheckAOEAuthority(AOEObj))
		return nullptr;
	const UAoeConfig* Config = GetDefault<UAoeConfig>();
	const float LevelDamageMultiplier = Config->DamageMultipliers[Config&&AOEObj->ActionLevel<4?AOEObj->ActionLevel:3];

	// 创建基础攻击信息模板
	FOffenseInfo BaseOffense = CreateBaseAOEOffenseInfo(AOEObj);

	// 设置与TouchCharacter无关的攻击信息
	BaseOffense.AttackInfo.DamageType = EDamageType::PeriodDamage;
	BaseOffense.AttackInfo.Elemental = EChaElemental::Fire;
	BaseOffense.AttackInfo.DefenderActionChange.ChangeMethod = EActionChangeMethod::ToMontageState;
	BaseOffense.AttackInfo.DefenderActionChange.FreezeTime = 0.1;
	BaseOffense.AttackInfo.DefenderActionChange.Priority = 5;
	if (AOEObj->HitVFX)
		UGameplayFuncLib::SetHitVFX(BaseOffense.AttackInfo.DefenderActionChange,AOEObj->HitVFX);
	if (AOEObj->HitSFX)
		UGameplayFuncLib::SetHitSFX(BaseOffense.AttackInfo.DefenderActionChange,AOEObj->HitSFX);

	for (auto& CurCatchedCharacter : TouchCharacters)
	{
		AAwCharacter* Character = Cast<AAwCharacter>(CurCatchedCharacter.BeCaughtActor);
		if (!CanLavaDamageCharacter(Character, AOEObj))
			continue;

		// 复制基础模板并修改与TouchCharacter相关的属性
		FOffenseInfo Offense = BaseOffense;
		if(Params.Num())
		{
			float Damage = FCString::Atof(*Params[0]) * Character->CharacterObj.CurProperty.HP;
			Damage = Damage < 1 ? 1 : Damage;
			Offense.AttackInfo.DamagePower = Damage * LevelDamageMultiplier;
		}
		Character->BeOffended(Offense, AOEObj->AttackHitManager, AOEObj->Caster, CurCatchedCharacter.CaughtHitBoxComponent);
	}

	// 使用包装函数处理ECS攻击循环（熔岩接触伤害）
	ProcessECSAttackLoop(AOEObj, BaseOffense, [&](FOffenseInfo& Offense, const FBeCaughtActorInfo& CurCatchedCharacter) {
		if(Params.Num() && CurCatchedCharacter.BeCaughtSubject.IsValid())
		{
			// 获取ECS怪物的HP信息来计算百分比伤害
			auto HealthTrait = CurCatchedCharacter.BeCaughtSubject.GetTraitPtr<FHealth, EParadigm::Unsafe>();
			if(HealthTrait)
			{
				float Damage = FCString::Atof(*Params[0]) * HealthTrait->Maximum;
				Damage = Damage < 1 ? 1 : Damage;
				Offense.AttackInfo.DamagePower = Damage * LevelDamageMultiplier;
			}
		}
		Offense.AttackHitBoxName.Add(CurCatchedCharacter.AttackBoxName);
	});

	return nullptr;
}

UTimelineNode* UAOEScript::AddBuffToEnemyOnTick(AAWAoe* AOEObj, TArray<FString> Params)
{
	if (!CheckAOEAuthority(AOEObj))
		return nullptr;

	for (auto& CurCatchedCharacter : AOEObj->GetCaughtCharacters())
	{
		AAwCharacter* Character = Cast<AAwCharacter>(CurCatchedCharacter.BeCaughtActor);
		if (!CanAttackCharacter(Character, AOEObj))
			continue;

		FAddBuffInfo BuffInfo = FAddBuffInfo();
		BuffInfo.Model = UGameplayFuncLib::GetAwDataManager()->GetBuffModelById(Params[0]);
		BuffInfo.AddStack = Params.Num() > 1 ? FCString::Atoi(*Params[1]) : 1;
		BuffInfo.Duration = Params.Num() > 2 ? FCString::Atoi(*Params[2]) : 5;
		BuffInfo.Infinity = false;
		BuffInfo.SetToDuration = true;
		BuffInfo.Target = Character;
		BuffInfo.Caster = AOEObj->Caster;
		BuffInfo.PropertyOnAdd = BuffInfo.Caster ? BuffInfo.Caster->CharacterObj.CurProperty : FChaProp();
		Character->AddBuff(BuffInfo);
	}
	return nullptr;
}

UTimelineNode* UAOEScript::CreateAoeOnRemoved(AAWAoe* AOEObj, TArray<FString> Params)
{
	if (!AOEObj) return nullptr;
	const FString AoeId = Params.Num() > 0 ? Params[0] : "";
	if (AoeId.IsEmpty()) return nullptr;
	const FAOEModel AoeModel = UGameplayFuncLib::GetDataManager()->GetAoeModelById(AoeId);
	if (AoeModel.Id.IsEmpty() == true) return nullptr;
	const float LifeTime = Params.Num() > 1 ? FCString::Atof(*Params[1]) : 0;
	const FAOELauncher Launcher = FAOELauncher(AOEObj->Caster, AoeModel, AOEObj->GetActorLocation(), AOEObj->GetActorForwardVector(), LifeTime, "");
	UGameplayFuncLib::CreateAOEByLauncher(Launcher);
	return nullptr;
}

FVector UAOEScript::AOETweenDown(float TimeElapsed, AAWAoe* AOEObj, FVector OriginPosition, FVector OriginDirection, TArray<FString> Params)
{
	float Speed =Params.Num() > 0 ? FCString::Atof(*Params[0]) : 0;
	FVector Offset = FVector(0,0,-1)* TimeElapsed*Speed;

	return (OriginPosition + Offset);
}

FVector UAOEScript::AOETweenSinForward(float TimeElapsed, AAWAoe* AOEObj, FVector OriginPosition,
	FVector OriginDirection, TArray<FString> Params)
{
	float ForwardSpeed =Params.Num() > 0 ? FCString::Atof(*Params[0]) : 0;
	float RightSpeed =Params.Num() > 1 ? FCString::Atof(*Params[1]) : 0;
	float SinSpeed = Params.Num() > 2 ? FCString::Atof(*Params[2]) : 0;
	FVector Forward = AOEObj->OrgTransform.GetRotation().GetForwardVector();
	FVector Right = AOEObj->OrgTransform.GetRotation().GetRightVector();
	
	
	FVector ForwardOffset = Forward* TimeElapsed*ForwardSpeed; 
	FVector RightOffset =Right*FMath::Sin(TimeElapsed*SinSpeed)*RightSpeed ;
	
	return (OriginPosition + ForwardOffset+RightOffset) ;
}

FVector UAOEScript::AOETweenDownDelaySeconds(float TimeElapsed, AAWAoe* AOEObj, FVector OriginPosition,
                                             FVector OriginDirection, TArray<FString> Params)
{
	float Time = Params.Num() > 0? FCString::Atof(*Params[0]) : 0;
	float Speed =Params.Num() > 1? FCString::Atof(*Params[1]) : 0;
	Speed = TimeElapsed>Time?Speed:0;
	FVector Offset = FVector(0,0,-1)* (TimeElapsed-Time)*Speed;

	return (OriginPosition + Offset);
}

FVector UAOEScript::AOETweenAccelerateDownDelaySeconds(float TimeElapsed, AAWAoe* AOEObj, FVector OriginPosition,
	FVector OriginDirection, TArray<FString> Params)
{
	float Time = Params.Num() > 0? FCString::Atof(*Params[0]) : 0;
	float Speed =Params.Num() > 1? FCString::Atof(*Params[1]) : 0;
	float Accelerate = Params.Num() > 2? FCString::Atof(*Params[2]) : 0;
	Speed += Accelerate*TimeElapsed;
	Speed = TimeElapsed>Time?Speed:0;
	FVector Offset = FVector(0,0,-1)* (TimeElapsed-Time)*Speed;

	return (OriginPosition + Offset);
}

UTimelineNode* UAOEScript::DealDamageOnRemoved(AAWAoe* AOEObj, TArray<FString> Params)
{
	if (!CheckAOEAuthority(AOEObj))
		return nullptr;

	// 创建基础攻击信息模板
	FOffenseInfo BaseOffense = CreateBaseAOEOffenseInfo(AOEObj);

	bool FriendlyFire = Params.Num() > 1 ? Params[1].ToBool() : false;
	TArray<FBeCaughtActorInfo> CaughtActors = AOEObj->CharacterInRange(true, true);
	for (const auto& CurCaughtActor : CaughtActors)
	{
		AAwCharacter* Character = Cast<AAwCharacter>(CurCaughtActor.BeCaughtActor);
		if (!CanAttackCharacterWithFriendlyFire(Character, AOEObj, FriendlyFire))
			continue;

		// 复制基础模板并修改特定属性
		FOffenseInfo Offense = BaseOffense;
		Offense.AttackHitBoxName = AOEObj->AttackHitManager->GetAllActiveAttackBoxNames();
		if (AOEObj->Caster)
			Offense.AttackInfo.Elemental = AOEObj->Caster->GetElemental(EChaElemental::Physical, EElementalPickMethod::Set, ESlotInWeaponObj::MainWeapon);
		if (Params.Num() > 0)
			Offense.AttackInfo.DamagePower = FCString::Atoi(*Params[0]);

		// 设置击退效果（注意这里使用AttackerFace方向）
		SetupKnockbackEffect(Offense, Character->GetActorLocation(), AOEObj, FVector(100, 0, 0), 0.3f, 5, 0.2f, ECharacterMontageState::Hurt);
		Offense.AttackInfo.DefenderActionChange.HitStun.VelocityType = EVelocityType::AttackerFace;

		UOffenseManager::DoOffense(AOEObj->AttackHitManager, Offense, CurCaughtActor, AOEObj->Caster, false);
	}

	// 使用包装函数处理ECS攻击循环（移除时伤害）
	ProcessECSAttackLoop(AOEObj, BaseOffense, [AOEObj, &Params](FOffenseInfo& Offense, const FBeCaughtActorInfo& CurCatchedCharacter) {
		Offense.AttackHitBoxName = AOEObj->AttackHitManager->GetAllActiveAttackBoxNames();
		if (AOEObj->Caster)
			Offense.AttackInfo.Elemental = AOEObj->Caster->GetElemental(EChaElemental::Physical, EElementalPickMethod::Set, ESlotInWeaponObj::MainWeapon);
		if (Params.Num() > 0)
			Offense.AttackInfo.DamagePower = FCString::Atoi(*Params[0]);
		// 设置击退效果（注意这里使用AttackerFace方向）
		auto Located = CurCatchedCharacter.BeCaughtSubject.GetTraitPtr<FLocated, EParadigm::Unsafe>();
		SetupKnockbackEffect(Offense, Located->Location, AOEObj, FVector(100, 0, 0), 0.3f, 5, 0.2f, ECharacterMontageState::Hurt);
		// ECS怪物击退效果设置（使用AttackerFace方向）
	});

	return nullptr;
}

UTimelineNode* UAOEScript::PlayVFXOnCreate(AAWAoe* AOEObj, TArray<FString> Params)
{
	if (AOEObj && Params.Num() > 0)
	{
		UGameplayFuncLib::CreateVFXByPathAtLocation(Params[0], AOEObj->GetActorTransform());
	}
	return nullptr;
}

UTimelineNode* UAOEScript::DealDamageOnTouch(AAWAoe* AOEObj, TArray<FBeCaughtActorInfo> TouchCharacters, TArray<FString> Params)
{
	if (!CheckAOEAuthority(AOEObj))
		return nullptr;
	const UAoeConfig* Config = GetDefault<UAoeConfig>();
	const float LevelDamageMultiplier = Config->DamageMultipliers[Config&&AOEObj->ActionLevel<4?AOEObj->ActionLevel:3];
	// 创建基础攻击信息模板
	FOffenseInfo BaseOffense = CreateBaseAOEOffenseInfo(AOEObj);

	// 解析参数（与TouchCharacter无关，移到循环外）
	int DamagePower = Params.Num()>0?FCString::Atoi(*Params[0]):0;
	const FString DamageTypeKey = Params.Num()>1?Params[1]:"Physical" ;
	EDamageType DamageType = Params.Num()>2?UDataFuncLib::FStringToEnum<EDamageType>(Params[2]):EDamageType::DirectDamage;
	FString ChangeMethodString =Params.Num()>3?Params[3]:"";
	int ActionChangePriority = Params.Num()>4?FCString::Atof(*Params[4]):7;
	FString ImmunityBuffId = Params.Num() > 5 ? Params[5] : "";

	// 设置与TouchCharacter无关的攻击信息
	BaseOffense.AttackInfo.DamagePower = DamagePower * LevelDamageMultiplier;
	BaseOffense.AttackInfo.DamageType = DamageType;
	EChaElemental ElementalType = UDataFuncLib::FStringToEnum<EChaElemental>(DamageTypeKey);
	BaseOffense.AttackInfo.Elemental = ElementalType>EChaElemental::Physical?ElementalType:EChaElemental::Physical;
	EActionChangeMethod ChangeMethod = ChangeMethodString.IsEmpty()?EActionChangeMethod::ToMontageState:UDataFuncLib::FStringToEnum<EActionChangeMethod>(ChangeMethodString);
	BaseOffense.AttackInfo.DefenderActionChange.ChangeMethod = ChangeMethod;
	BaseOffense.AttackInfo.DefenderActionChange.ToState = ECharacterMontageState::Hurt;
	BaseOffense.AttackInfo.DefenderActionChange.Priority = ActionChangePriority;

	for (auto& CurCatchedCharacter : TouchCharacters)
	{
		AAwCharacter* Character = Cast<AAwCharacter>(CurCatchedCharacter.BeCaughtActor);
		if (!CanAttackCharacterWithBuffImmunity(Character, AOEObj, ImmunityBuffId))
			continue;

		// 复制基础模板并修改与TouchCharacter相关的属性
		FOffenseInfo Offense = BaseOffense;
		Offense.AttackHitBoxName.Add(CurCatchedCharacter.AttackBoxName);
		UOffenseManager::DoOffense(AOEObj->AttackHitManager, Offense, CurCatchedCharacter, AOEObj->Caster, false);
	}

	// 使用包装函数处理ECS攻击循环（接触伤害）
	ProcessECSAttackLoop(AOEObj, BaseOffense, [](FOffenseInfo& Offense, const FBeCaughtActorInfo& CurCatchedCharacter) {
		Offense.AttackHitBoxName.Add(CurCatchedCharacter.AttackBoxName);
	});

	return nullptr;
}

UTimelineNode* UAOEScript::DealDotDamageOnTick(AAWAoe* AOEObj, TArray<FString> Params)
{
	if (!CheckAOEAuthority(AOEObj))
		return nullptr;
	const UAoeConfig* Config = GetDefault<UAoeConfig>();
	const float LevelDamageMultiplier = Config->DamageMultipliers[Config&&AOEObj->ActionLevel<4?AOEObj->ActionLevel:3];
	
	// 创建基础攻击信息模板
	FOffenseInfo BaseOffense = CreateBaseAOEOffenseInfo(AOEObj);

	// 解析参数（与TouchCharacter无关，移到循环外）
	int DamagePower = Params.Num()>0?FCString::Atoi(*Params[0]):0;
	const FString DamageTypeKey = Params.Num() > 1 ? Params[1] : "Physical" ;
	const int MaxHitTimes = Params.Num() > 2 ? FCString::Atoi(*Params[2]) : 10 ;

	// 设置与TouchCharacter无关的攻击信息
	BaseOffense.AttackInfo.DamagePower = DamagePower * LevelDamageMultiplier;
	BaseOffense.AttackInfo.DamageType = EDamageType::PeriodDamage;
	EChaElemental ElementalType = UDataFuncLib::FStringToEnum<EChaElemental>(DamageTypeKey);
	BaseOffense.AttackInfo.Elemental = ElementalType>EChaElemental::Physical?ElementalType:EChaElemental::Physical;
	BaseOffense.CanHitTimes = MaxHitTimes;
	if (AOEObj->HitVFX)
		UGameplayFuncLib::SetHitVFX(BaseOffense.AttackInfo.DefenderActionChange,AOEObj->HitVFX);
	if (AOEObj->HitSFX)
		UGameplayFuncLib::SetHitSFX(BaseOffense.AttackInfo.DefenderActionChange,AOEObj->HitSFX);

	for (auto& CurCatchedCharacter : AOEObj->GetCaughtCharacters())
	{
		AAwCharacter* Character = Cast<AAwCharacter>(CurCatchedCharacter.BeCaughtActor);
		if (!CanAttackCharacter(Character, AOEObj))
			continue;

		// 复制基础模板并修改与TouchCharacter相关的属性
		FOffenseInfo Offense = BaseOffense;
		Offense.AttackHitBoxName.Add(CurCatchedCharacter.AttackBoxName);
		UOffenseManager::DoOffense(AOEObj->AttackHitManager, Offense, CurCatchedCharacter, AOEObj->Caster, false);
	}

	// 使用包装函数处理ECS攻击循环（持续伤害）
	ProcessECSAttackLoop(AOEObj, BaseOffense, [](FOffenseInfo& Offense, const FBeCaughtActorInfo& CurCatchedCharacter) {
		Offense.AttackHitBoxName.Add(CurCatchedCharacter.AttackBoxName);
	});

	return nullptr;
}

UTimelineNode* UAOEScript::DealMoveVelocityOnTouch(AAWAoe* AOEObj, TArray<FBeCaughtActorInfo> TouchCharacters,
                                                   TArray<FString> Params)
{
	if (!CheckAOEAuthority(AOEObj))
		return nullptr;

	// 创建基础攻击信息模板
	FOffenseInfo BaseOffense = CreateBaseAOEOffenseInfo(AOEObj);

	// 解析参数（与TouchCharacter无关，移到循环外）
	FVector BaseVeloctiy = FVector::ZeroVector;
	BaseVeloctiy.X = Params.Num()>0?FCString::Atof(*Params[0]):0;
	BaseVeloctiy.Y = Params.Num()>1?FCString::Atof(*Params[1]):0;
	BaseVeloctiy.Z = Params.Num()>2?FCString::Atof(*Params[2]):0;
	FString ChangeMethodString =Params.Num()>3?Params[3]:"";
	FString HitStunType = Params.Num()>4?Params[4]:"KnockOut";
	int ActionChangePriority = Params.Num()>5?FCString::Atof(*Params[5]):5;
	float Time = Params.Num()>6?FCString::Atof(*Params[6]):1;

	// 设置与TouchCharacter无关的攻击信息
	BaseOffense.AttackInfo.DamageType = EDamageType::OtherDamage;
	BaseOffense.SourceId = ""; // 这个函数特殊设置为空
	EActionChangeMethod ChangeMethod = ChangeMethodString.IsEmpty()?EActionChangeMethod::ToMontageState:UDataFuncLib::FStringToEnum<EActionChangeMethod>(ChangeMethodString);
	EForceMoveType ForceMoveType = HitStunType.IsEmpty()?EForceMoveType::KnockOut:UDataFuncLib::FStringToEnum<EForceMoveType>(HitStunType);
	BaseOffense.AttackInfo.DefenderActionChange.ChangeMethod = ChangeMethod;
	BaseOffense.AttackInfo.DefenderActionChange.ToState = ECharacterMontageState::Blow;
	BaseOffense.AttackInfo.DefenderActionChange.FreezeTime = 0.1;
	BaseOffense.AttackInfo.DefenderActionChange.HitStun.Active = true;
	BaseOffense.AttackInfo.DefenderActionChange.HitStun.Type = ForceMoveType;
	BaseOffense.AttackInfo.DefenderActionChange.HitStun.VelocityType = EVelocityType::RelativeDir;
	BaseOffense.AttackInfo.DefenderActionChange.HitStun.InSec = Time;
	BaseOffense.AttackInfo.DefenderActionChange.Priority = ActionChangePriority;

	for (auto& CurCatchedCharacter : TouchCharacters)
	{
		AAwCharacter* Character = Cast<AAwCharacter>(CurCatchedCharacter.BeCaughtActor);
		if (!CanAttackCharacterWithDodgeCheck(Character, AOEObj, CurCatchedCharacter))
			continue;

		// 复制基础模板并修改与TouchCharacter相关的属性
		FOffenseInfo Offense = BaseOffense;
		Offense.AttackHitBoxName.Add(CurCatchedCharacter.AttackBoxName);

		// 设置与Character位置相关的击退方向
		if(Character)
			Offense.AttackInfo.DefenderActionChange.HitStun.Velocity =
				BaseVeloctiy.RotateAngleAxis((Character->GetActorLocation() - AOEObj->GetActorLocation()).Rotation().Yaw, FVector::UpVector);

		UOffenseManager::DoOffense(AOEObj->AttackHitManager, Offense, CurCatchedCharacter, AOEObj->Caster, false);
	}

	// 使用包装函数处理ECS攻击循环（移动速度处理）
	ProcessECSAttackLoop(AOEObj, BaseOffense, [AOEObj, BaseVeloctiy](FOffenseInfo& Offense, const FBeCaughtActorInfo& CurCatchedCharacter) {
		Offense.AttackHitBoxName.Add(CurCatchedCharacter.AttackBoxName);

		// 设置与ECS怪物位置相关的击退方向
		if(CurCatchedCharacter.BeCaughtSubject.IsValid())
		{
			auto LocatedTrait = CurCatchedCharacter.BeCaughtSubject.GetTraitPtr<FLocated, EParadigm::Unsafe>();
			if(LocatedTrait)
			{
				Offense.AttackInfo.DefenderActionChange.HitStun.Velocity =
					BaseVeloctiy.RotateAngleAxis((LocatedTrait->Location - AOEObj->GetActorLocation()).Rotation().Yaw, FVector::UpVector);
			}
		}
	});

	return nullptr;
}

UTimelineNode* UAOEScript::DealMoveVelocityToAoeCasterOnTouch(AAWAoe* AOEObj,
	TArray<FBeCaughtActorInfo> TouchCharacters, TArray<FString> Params)
{
	if (!CheckAOEAuthority(AOEObj))
		return nullptr;

	// 创建基础攻击信息模板
	FOffenseInfo BaseOffense = CreateBaseAOEOffenseInfo(AOEObj);

	// 解析参数（与TouchCharacter无关，移到循环外）
	FVector BaseVeloctiy = FVector::ZeroVector;
	BaseVeloctiy.X = Params.Num()>0?FCString::Atof(*Params[0]):0;
	BaseVeloctiy.Y = Params.Num()>1?FCString::Atof(*Params[1]):0;
	BaseVeloctiy.Z = Params.Num()>2?FCString::Atof(*Params[2]):0;
	FString ChangeMethodString =Params.Num()>3?Params[3]:"";
	FString HitStunType = Params.Num()>4?Params[4]:"KnockOut";
	int ActionChangePriority = Params.Num()>5?FCString::Atof(*Params[5]):5;
	float Time = Params.Num()>6?FCString::Atof(*Params[6]):1;

	// 设置与TouchCharacter无关的攻击信息
	BaseOffense.AttackInfo.DamageType = EDamageType::OtherDamage;
	BaseOffense.SourceId = ""; // 这个函数特殊设置为空
	EActionChangeMethod ChangeMethod = ChangeMethodString.IsEmpty()?EActionChangeMethod::ToMontageState:UDataFuncLib::FStringToEnum<EActionChangeMethod>(ChangeMethodString);
	EForceMoveType ForceMoveType = HitStunType.IsEmpty()?EForceMoveType::KnockOut:UDataFuncLib::FStringToEnum<EForceMoveType>(HitStunType);
	BaseOffense.AttackInfo.DefenderActionChange.ChangeMethod = ChangeMethod;
	BaseOffense.AttackInfo.DefenderActionChange.ToState = ECharacterMontageState::Blow;
	BaseOffense.AttackInfo.DefenderActionChange.FreezeTime = 0.1;
	BaseOffense.AttackInfo.DefenderActionChange.HitStun.Active = true;
	BaseOffense.AttackInfo.DefenderActionChange.HitStun.Type = ForceMoveType;
	BaseOffense.AttackInfo.DefenderActionChange.HitStun.VelocityType = EVelocityType::RelativeDir;
	BaseOffense.AttackInfo.DefenderActionChange.HitStun.InSec = Time;
	BaseOffense.AttackInfo.DefenderActionChange.Priority = ActionChangePriority;

	for (auto& CurCatchedCharacter : TouchCharacters)
	{
		AAwCharacter* Character = Cast<AAwCharacter>(CurCatchedCharacter.BeCaughtActor);
		if (!CanAttackCharacter(Character, AOEObj))
			continue;

		// 复制基础模板并修改与TouchCharacter相关的属性
		FOffenseInfo Offense = BaseOffense;
		Offense.AttackHitBoxName.Add(CurCatchedCharacter.AttackBoxName);

		// 设置与Character和Caster位置相关的击退方向（朝向施法者方向）
		if(Character && AOEObj->Caster)
		{
			Offense.AttackInfo.DefenderActionChange.HitStun.Velocity =
				BaseVeloctiy.RotateAngleAxis((AOEObj->Caster->GetActorLocation() - Character->GetActorLocation()).Rotation().Yaw, FVector::UpVector);
		}

		UOffenseManager::DoOffense(AOEObj->AttackHitManager, Offense, CurCatchedCharacter, AOEObj->Caster, false);
	}

	// 使用包装函数处理ECS攻击循环（朝向施法者的移动）
	ProcessECSAttackLoop(AOEObj, BaseOffense, [AOEObj, BaseVeloctiy](FOffenseInfo& Offense, const FBeCaughtActorInfo& CurCatchedCharacter) {
		Offense.AttackHitBoxName.Add(CurCatchedCharacter.AttackBoxName);

		// 设置与ECS怪物和Caster位置相关的击退方向（朝向施法者方向）
		if(CurCatchedCharacter.BeCaughtSubject.IsValid() && AOEObj->Caster)
		{
			auto LocatedTrait = CurCatchedCharacter.BeCaughtSubject.GetTraitPtr<FLocated, EParadigm::Unsafe>();
			if(LocatedTrait)
			{
				Offense.AttackInfo.DefenderActionChange.HitStun.Velocity =
					BaseVeloctiy.RotateAngleAxis((AOEObj->Caster->GetActorLocation() - LocatedTrait->Location).Rotation().Yaw, FVector::UpVector);
			}
		}
	});

	return nullptr;
}

UTimelineNode* UAOEScript::DealMoveVelocityToAoeCenterOnTouch(AAWAoe* AOEObj,
                                                              TArray<FBeCaughtActorInfo> TouchCharacters, TArray<FString> Params)
{
	if (!CheckAOEAuthority(AOEObj))
		return nullptr;

	// 创建基础攻击信息模板
	FOffenseInfo BaseOffense = CreateBaseAOEOffenseInfo(AOEObj);

	// 解析参数（与TouchCharacter无关，移到循环外）
	FVector BaseVeloctiy = FVector::ZeroVector;
	BaseVeloctiy.X = Params.Num()>0?FCString::Atof(*Params[0]):0;
	BaseVeloctiy.Y = Params.Num()>1?FCString::Atof(*Params[1]):0;
	BaseVeloctiy.Z = Params.Num()>2?FCString::Atof(*Params[2]):0;
	FString ChangeMethodString =Params.Num()>3?Params[3]:"";
	FString HitStunType = Params.Num()>4?Params[4]:"KnockOut";
	int ActionChangePriority = Params.Num()>5?FCString::Atof(*Params[5]):5;
	float Time = Params.Num()>6?FCString::Atof(*Params[6]):1;

	// 设置与TouchCharacter无关的攻击信息
	BaseOffense.AttackInfo.DamageType = EDamageType::OtherDamage;
	BaseOffense.SourceId = ""; // 这个函数特殊设置为空
	EActionChangeMethod ChangeMethod = ChangeMethodString.IsEmpty()?EActionChangeMethod::ToMontageState:UDataFuncLib::FStringToEnum<EActionChangeMethod>(ChangeMethodString);
	EForceMoveType ForceMoveType = HitStunType.IsEmpty()?EForceMoveType::KnockOut:UDataFuncLib::FStringToEnum<EForceMoveType>(HitStunType);
	BaseOffense.AttackInfo.DefenderActionChange.ChangeMethod = ChangeMethod;
	BaseOffense.AttackInfo.DefenderActionChange.ToState = ECharacterMontageState::Blow;
	BaseOffense.AttackInfo.DefenderActionChange.FreezeTime = 0.1;
	BaseOffense.AttackInfo.DefenderActionChange.HitStun.Active = true;
	BaseOffense.AttackInfo.DefenderActionChange.HitStun.Type = ForceMoveType;
	BaseOffense.AttackInfo.DefenderActionChange.HitStun.VelocityType = EVelocityType::RelativeDir;
	BaseOffense.AttackInfo.DefenderActionChange.HitStun.InSec = Time;
	BaseOffense.AttackInfo.DefenderActionChange.Priority = ActionChangePriority;

	for (auto& CurCatchedCharacter : TouchCharacters)
	{
		AAwCharacter* Character = Cast<AAwCharacter>(CurCatchedCharacter.BeCaughtActor);
		if (!CanAttackCharacter(Character, AOEObj))
			continue;

		// 复制基础模板并修改与TouchCharacter相关的属性
		FOffenseInfo Offense = BaseOffense;
		Offense.AttackHitBoxName.Add(CurCatchedCharacter.AttackBoxName);

		// 设置与Character位置相关的击退方向（朝向AOE中心方向）
		if(Character)
		{
			Offense.AttackInfo.DefenderActionChange.HitStun.Velocity =
				BaseVeloctiy.RotateAngleAxis((AOEObj->GetActorLocation() - Character->GetActorLocation()).Rotation().Yaw, FVector::UpVector);
		}

		UOffenseManager::DoOffense(AOEObj->AttackHitManager, Offense, CurCatchedCharacter, AOEObj->Caster, false);
	}

	// 使用包装函数处理ECS攻击循环（朝向AOE中心的移动）
	ProcessECSAttackLoop(AOEObj, BaseOffense, [AOEObj, BaseVeloctiy](FOffenseInfo& Offense, const FBeCaughtActorInfo& CurCatchedCharacter) {
		Offense.AttackHitBoxName.Add(CurCatchedCharacter.AttackBoxName);

		// 设置与ECS怪物位置相关的击退方向（朝向AOE中心方向）
		if(CurCatchedCharacter.BeCaughtSubject.IsValid())
		{
			auto LocatedTrait = CurCatchedCharacter.BeCaughtSubject.GetTraitPtr<FLocated, EParadigm::Unsafe>();
			if(LocatedTrait)
			{
				Offense.AttackInfo.DefenderActionChange.HitStun.Velocity =
					BaseVeloctiy.RotateAngleAxis((AOEObj->GetActorLocation() - LocatedTrait->Location).Rotation().Yaw, FVector::UpVector);
			}
		}
	});

	return nullptr;
}

UTimelineNode* UAOEScript::DealDamageByCsterPropOnTouch(AAWAoe* AOEObj, TArray<FBeCaughtActorInfo> TouchCharacters,
                                                        TArray<FString> Params)
{
	if (!CheckAOEAuthority(AOEObj))
		return nullptr;
	const UAoeConfig* Config = GetDefault<UAoeConfig>();
	const float LevelDamageMultiplier = Config->DamageMultipliers[Config&&AOEObj->ActionLevel<4?AOEObj->ActionLevel:3];

	// 创建基础攻击信息模板
	FOffenseInfo BaseOffense = CreateBaseAOEOffenseInfo(AOEObj);

	// 解析参数（与TouchCharacter无关，移到循环外）
	float DamagePower = Params.Num()>0?FCString::Atof(*Params[0]):0;
	float BreakRate = Params.Num()>1?FCString::Atof(*Params[1]):0;
	const FString DamageTypeKey = Params.Num()>2?Params[2]:"" ;
	EDamageType DamageType = Params.Num()>3?UDataFuncLib::FStringToEnum<EDamageType>(Params[3]):EDamageType::DirectDamage;
	FString ChangeMethodString =Params.Num()>4?Params[4]:"";
	int ActionChangePriority = Params.Num()>5?FCString::Atof(*Params[5]):7;

	// 设置与TouchCharacter无关的攻击信息
	if (AOEObj->Caster)
		BaseOffense.AttackInfo.Elemental = AOEObj->Caster->GetElemental(EChaElemental::Physical, EElementalPickMethod::Set, ESlotInWeaponObj::MainWeapon);
	BaseOffense.AttackInfo.DamageType = DamageType;
	BaseOffense.AttackInfo.DamagePower.Physical = AOEObj->Caster? DamagePower*AOEObj->Caster->CharacterObj.CurProperty.PAttack:DamagePower;
	BaseOffense.AttackInfo.DamagePower.Physical *= LevelDamageMultiplier;
	BaseOffense.AttackInfo.DamagePower.Break = BreakRate;
	EActionChangeMethod ChangeMethod = ChangeMethodString.IsEmpty()?EActionChangeMethod::ToMontageState:UDataFuncLib::FStringToEnum<EActionChangeMethod>(ChangeMethodString);
	EChaElemental ElementalType = UDataFuncLib::FStringToEnum<EChaElemental>(DamageTypeKey);
	BaseOffense.AttackInfo.Elemental = ElementalType>EChaElemental::Physical?ElementalType:EChaElemental::Physical;
	BaseOffense.AttackInfo.DefenderActionChange.ChangeMethod = ChangeMethod;
	BaseOffense.AttackInfo.DefenderActionChange.ToState = ECharacterMontageState::Hurt;
	BaseOffense.AttackInfo.DefenderActionChange.Priority = ActionChangePriority;

	for (auto& CurCatchedCharacter : TouchCharacters)
	{
		AAwCharacter* Character = Cast<AAwCharacter>(CurCatchedCharacter.BeCaughtActor);
		if (!CanAttackCharacter(Character, AOEObj))
			continue;

		// 复制基础模板并修改与TouchCharacter相关的属性
		FOffenseInfo Offense = BaseOffense;
		Offense.AttackHitBoxName.Add(CurCatchedCharacter.AttackBoxName);

		UOffenseManager::DoOffense(AOEObj->AttackHitManager, Offense, CurCatchedCharacter, AOEObj->Caster, false);
	}
	

	
	// 使用包装函数处理ECS攻击循环
	ProcessECSAttackLoop(AOEObj,BaseOffense,[](FOffenseInfo& Offense, const FBeCaughtActorInfo& CurCatchedCharacter) {
			Offense.AttackHitBoxName.Add(CurCatchedCharacter.AttackBoxName);
		}
	);

	return nullptr;
}

UTimelineNode* UAOEScript::FireAoeByItem(AAWAoe* AOEObj, TArray<FBeCaughtActorInfo> TouchCharacters, TArray<FString> Params)
{
	// 创建基础攻击信息模板
	FOffenseInfo BaseOffense = CreateBaseAOEOffenseInfo(AOEObj);
	const UAoeConfig* Config = GetDefault<UAoeConfig>();
	const float LevelDamageMultiplier = Config->DamageMultipliers[Config&&AOEObj->ActionLevel<4?AOEObj->ActionLevel:3];

	// 设置与TouchCharacter无关的攻击信息
	if (AOEObj->Caster)
		BaseOffense.AttackInfo.Elemental = AOEObj->Caster->GetElemental(EChaElemental::Physical, EElementalPickMethod::Set, ESlotInWeaponObj::MainWeapon);
	if (Params.Num())
		BaseOffense.AttackInfo.DamagePower = FCString::Atoi(*Params[0])*LevelDamageMultiplier;
	BaseOffense.HitSameTargetDelay = 2;
	if (AOEObj->HitVFX)
		UGameplayFuncLib::SetHitVFX(BaseOffense.AttackInfo.DefenderActionChange,AOEObj->HitVFX);

	for (const FBeCaughtActorInfo TouchCharacter : TouchCharacters)
	{
		AAwCharacter* Character = Cast<AAwCharacter>(TouchCharacter.BeCaughtActor);
		if (!CanAttackNonPlayerCharacter(Character, AOEObj))
			continue;

		// 复制基础模板并修改与TouchCharacter相关的属性
		FOffenseInfo Offense = BaseOffense;
		Offense.AttackHitBoxName.Add(TouchCharacter.AttackBoxName);

		// 设置击退效果
		SetupKnockbackEffect(Offense, Character->GetActorLocation(), AOEObj, FVector(310, 0, 100), 0.5f, 5, 0.1f, ECharacterMontageState::Blow);

		const FOffendedCaughtResult Res = Character->CanBeOffended(Offense,AOEObj->AttackHitManager, AOEObj->Caster,true);
		if(Res.Hit)
			Character->BeOffended(Offense, AOEObj->AttackHitManager, AOEObj->Caster, TouchCharacter.CaughtHitBoxComponent,
				AOEObj->AttackHitManager->GetAttackBoxByName(TouchCharacter.AttackBoxName));
	}

	
	// 使用包装函数处理ECS攻击循环（熔岩接触伤害）
	ProcessECSAttackLoop(AOEObj, BaseOffense, [&](FOffenseInfo& Offense, const FBeCaughtActorInfo& CurCatchedCharacter) {
		Offense.AttackHitBoxName.Add(CurCatchedCharacter.AttackBoxName);
		// 设置击退效果
		auto LocatedTrait = CurCatchedCharacter.BeCaughtSubject.GetTraitPtr<FLocated, EParadigm::Unsafe>();
		if(LocatedTrait)
		{
			SetupKnockbackEffect(Offense, LocatedTrait->Location, AOEObj, FVector(310, 0, 100), 0.5f, 5, 0.1f, ECharacterMontageState::Blow);
		}
	});
	return nullptr;
}

UTimelineNode* UAOEScript::FireAoeByTeam(AAWAoe* AOEObj, TArray<FBeCaughtActorInfo> TouchCharacters, TArray<FString> Params)
{
	// 创建基础攻击信息模板
	FOffenseInfo BaseOffense = CreateBaseAOEOffenseInfo(AOEObj);
	const UAoeConfig* Config = GetDefault<UAoeConfig>();
	const float LevelDamageMultiplier = Config->DamageMultipliers[Config&&AOEObj->ActionLevel<4?AOEObj->ActionLevel:3];

	// 设置与TouchCharacter无关的攻击信息
	int TeamSide = Params.Num() ? FCString::Atoi(*Params[0]) : 0;
	if(AOEObj->Caster)
		BaseOffense.AttackInfo.Elemental = AOEObj->Caster->GetElemental(EChaElemental::Physical, EElementalPickMethod::Set, ESlotInWeaponObj::MainWeapon);
	BaseOffense.AttackInfo.DamagePower = 1;
	if(AOEObj->Caster)
		BaseOffense.AttackInfo.DamagePower = AOEObj->Caster->CharacterObj.CurProperty.PAttack * LevelDamageMultiplier;
	if (Params.Num() > 1)
		BaseOffense.AttackInfo.DamagePower = BaseOffense.AttackInfo.DamagePower * FCString::Atof(*Params[1]) * LevelDamageMultiplier;
	BaseOffense.HitSameTargetDelay = 2;
	if (AOEObj->HitVFX)
		UGameplayFuncLib::SetHitVFX(BaseOffense.AttackInfo.DefenderActionChange,AOEObj->HitVFX);
	if (AOEObj->HitSFX)
		UGameplayFuncLib::SetHitSFX(BaseOffense.AttackInfo.DefenderActionChange,AOEObj->HitSFX);

	for (const FBeCaughtActorInfo TouchCharacter : TouchCharacters)
	{
		AAwCharacter* Character = Cast<AAwCharacter>(TouchCharacter.BeCaughtActor);
		if (!CanAttackCharacterWithTeamCheck(Character, AOEObj, TeamSide))
			continue;

		// 复制基础模板并修改与TouchCharacter相关的属性
		FOffenseInfo Offense = BaseOffense;
		Offense.AttackHitBoxName.Add(TouchCharacter.AttackBoxName);

		// 设置击退效果
		SetupKnockbackEffect(Offense, Character->GetActorLocation(), AOEObj, FVector(310, 0, 100), 0.5f, 5, 0.1f, ECharacterMontageState::Blow);

		const FOffendedCaughtResult Res = Character->CanBeOffended(Offense,AOEObj->AttackHitManager, AOEObj->Caster,true);
		if(Res.Hit)
			Character->BeOffended(Offense, AOEObj->AttackHitManager, AOEObj->Caster, TouchCharacter.CaughtHitBoxComponent,
				AOEObj->AttackHitManager->GetAttackBoxByName(TouchCharacter.AttackBoxName));
	}
	// 使用包装函数处理ECS攻击循环（熔岩接触伤害）
	ProcessECSAttackLoop(AOEObj, BaseOffense, [&](FOffenseInfo& Offense, const FBeCaughtActorInfo& CurCatchedCharacter) {
		Offense.AttackHitBoxName.Add(CurCatchedCharacter.AttackBoxName);
		// 设置击退效果
		auto LocatedTrait = CurCatchedCharacter.BeCaughtSubject.GetTraitPtr<FLocated, EParadigm::Unsafe>();
		if(LocatedTrait)
		{
			SetupKnockbackEffect(Offense, LocatedTrait->Location, AOEObj, FVector(310, 0, 100), 0.5f, 5, 0.1f, ECharacterMontageState::Blow);
		}
	});
	return nullptr;
}

UTimelineNode* UAOEScript::DealDamageByCasterProp(AAWAoe* AOEObj, TArray<FBeCaughtActorInfo> TouchCharacters,
	TArray<FString> Params)
{
	// 创建基础攻击信息模板
	FOffenseInfo BaseOffense = CreateBaseAOEOffenseInfo(AOEObj);
	const UAoeConfig* Config = GetDefault<UAoeConfig>();
	const float LevelDamageMultiplier = Config->DamageMultipliers[Config&&AOEObj->ActionLevel<4?AOEObj->ActionLevel:3];

	// 设置与TouchCharacter无关的攻击信息
	BaseOffense.AttackInfo.DamagePower = 1;
	if(AOEObj->Caster)
		BaseOffense.AttackInfo.DamagePower = AOEObj->Caster->CharacterObj.CurProperty.PAttack * LevelDamageMultiplier;
	// DamagePower.Physical  狗屎遗留 实际 DamagePower 只需要 Damage 和 break 2个倍率
	if (Params.Num()>0)
		BaseOffense.AttackInfo.DamagePower.Physical = BaseOffense.AttackInfo.DamagePower.Physical * FCString::Atof(*Params[0]) * LevelDamageMultiplier;
	// Parmas[1] break计算
	if (Params.Num()>1)
		BaseOffense.AttackInfo.DamagePower.Break = FCString::Atof(*Params[1]) * LevelDamageMultiplier;

	// 设置元素类型
	if (AOEObj->Caster)
		BaseOffense.AttackInfo.Elemental = AOEObj->Caster->GetElemental(EChaElemental::Physical, EElementalPickMethod::Set, ESlotInWeaponObj::MainWeapon);
	if(Params.Num() >2 )
		BaseOffense.AttackInfo.Elemental = UDataFuncLib::FStringToEnum<EChaElemental>(Params[2]);

	// 设置击退效果
	FVector BlowVelocity = FVector(310, 0, 100);
	if(Params.Num() > 7)
	{
		BlowVelocity.X = FCString::Atof(*Params[5]);
		BlowVelocity.Y = FCString::Atof(*Params[6]);
		BlowVelocity.Z = FCString::Atof(*Params[7]);
	}
	int Priority = Params.Num() > 8 ? FCString::Atoi(*Params[8]) : 5;
	ECharacterMontageState ToState = Params.Num() > 4 ? UDataFuncLib::FStringToEnum<ECharacterMontageState>(Params[4]) : ECharacterMontageState::Hurt;
	
	// 设置攻击类型和其他属性
	BaseOffense.AttackInfo.DamageType = Params.Num() > 3 ? UDataFuncLib::FStringToEnum<EDamageType>(Params[3]) : EDamageType::DirectDamage;
	BaseOffense.CanHitTimes = 100;
	BaseOffense.HitSameTargetDelay = 2;
	if (AOEObj->HitVFX)
		UGameplayFuncLib::SetHitVFX(BaseOffense.AttackInfo.DefenderActionChange,AOEObj->HitVFX);

	for (const FBeCaughtActorInfo TouchCharacter : TouchCharacters)
	{
		AAwCharacter* Character = Cast<AAwCharacter>(TouchCharacter.BeCaughtActor);
		if (!CanAttackCharacter(Character, AOEObj))
			continue;

		// 复制基础模板并修改与TouchCharacter相关的属性
		FOffenseInfo Offense = BaseOffense;
		Offense.AttackHitBoxName.Add(TouchCharacter.AttackBoxName);


		SetupKnockbackEffect(Offense, Character->GetActorLocation(), AOEObj, BlowVelocity, 0.5f, Priority, 0.1f, ToState);

		const FOffendedCaughtResult Res = Character->CanBeOffended(Offense,AOEObj->AttackHitManager, AOEObj->Caster,true);
		if(Res.Hit)
			Character->BeOffended(Offense, AOEObj->AttackHitManager, AOEObj->Caster, TouchCharacter.CaughtHitBoxComponent,
				AOEObj->AttackHitManager->GetAttackBoxByName(TouchCharacter.AttackBoxName));
	}
	// 使用包装函数处理ECS攻击循环（熔岩接触伤害）
	ProcessECSAttackLoop(AOEObj, BaseOffense, [&](FOffenseInfo& Offense, const FBeCaughtActorInfo& CurCatchedCharacter) {
		Offense.AttackHitBoxName.Add(CurCatchedCharacter.AttackBoxName);
		// 设置击退效果
		auto LocatedTrait = CurCatchedCharacter.BeCaughtSubject.GetTraitPtr<FLocated, EParadigm::Unsafe>();
		if(LocatedTrait)
		{
			SetupKnockbackEffect(Offense, LocatedTrait->Location, AOEObj, BlowVelocity, 0.5f, Priority, 0.1f, ToState);
		}
	});
	return nullptr;
}

UTimelineNode* UAOEScript::AddBuffToEnemyOnHit(AAWAoe* AOEObj, TArray<FBeCaughtActorInfo> TouchCharacters,
	TArray<FString> Params)
{
	for (const FBeCaughtActorInfo TouchCharacter : TouchCharacters)
	{
		AAwCharacter* Character = Cast<AAwCharacter>(TouchCharacter.BeCaughtActor);
		if (!CanAttackCharacterWithDodgeCheck(Character, AOEObj, TouchCharacter))
			continue;

		FAddBuffInfo BuffInfo = FAddBuffInfo();
		BuffInfo.Model = UGameplayFuncLib::GetAwDataManager()->GetBuffModelById(Params[0]);
		BuffInfo.AddStack = Params.Num() > 1 ? FCString::Atoi(*Params[1]) : 1;
		BuffInfo.Duration = Params.Num() > 2 ? FCString::Atof(*Params[2]) : 5;
		BuffInfo.Infinity = false;
		BuffInfo.SetToDuration = true;
		BuffInfo.Target = Character;
		BuffInfo.Caster = AOEObj->Caster;
		BuffInfo.PropertyOnAdd = BuffInfo.Caster ? BuffInfo.Caster->CharacterObj.CurProperty : FChaProp();
		Character->AddBuff(BuffInfo);
	}
	return nullptr;
}

UTimelineNode* UAOEScript::RemoveBuffOnCharacterLeave(AAWAoe* AOEObj, TArray<AAwCharacter*> LeaveCharacters,
	TArray<FString> Params)
{
	if (!CheckAOEAuthority(AOEObj) || !Params.Num())
		return nullptr;

	for (auto& CurCatchedCharacter : LeaveCharacters)
	{
		if(CurCatchedCharacter)
			CurCatchedCharacter->RemoveBuffById(Params[0]);
	}
	return nullptr;
}

UTimelineNode* UAOEScript::RemoveBuffOnAoeRemoved(AAWAoe* AOEObj, TArray<FString> Params)
{
	if (!CheckAOEAuthority(AOEObj) || !Params.Num())
		return nullptr;

	for (auto& CurCatchedCharacter : AOEObj->GetCaughtCharacters())
	{
		AAwCharacter* Target = Cast<AAwCharacter>(CurCatchedCharacter.BeCaughtActor);
		if(Target)
			Target->RemoveBuffById(Params[0]);
	}
	return nullptr;
}

UTimelineNode* UAOEScript::FireAoeHitActors(AAWAoe* AOEObj, TArray<FBeCaughtActorInfo> EnterActors, TArray<FString> Params)
{
	// 创建基础攻击信息模板
	FOffenseInfo BaseOffense = CreateBaseAOEOffenseInfo(AOEObj);
	const UAoeConfig* Config = GetDefault<UAoeConfig>();
	const float LevelDamageMultiplier = Config->DamageMultipliers[Config&&AOEObj->ActionLevel<4?AOEObj->ActionLevel:3];

	// 设置与TouchCharacter无关的攻击信息
	if (AOEObj->Caster)
		BaseOffense.AttackInfo.Elemental = AOEObj->Caster->GetElemental(EChaElemental::Physical, EElementalPickMethod::Set, ESlotInWeaponObj::MainWeapon);
	if (Params.Num())
		BaseOffense.AttackInfo.DamagePower = FCString::Atoi(*Params[0]) * LevelDamageMultiplier;
	BaseOffense.HitSameTargetDelay = 2;
	if (AOEObj->HitVFX)
		UGameplayFuncLib::SetHitVFX(BaseOffense.AttackInfo.DefenderActionChange,AOEObj->HitVFX);

	// 设置击退效果（对场景物品）
	BaseOffense.AttackInfo.DefenderActionChange.ChangeMethod = EActionChangeMethod::ToMontageState;
	BaseOffense.AttackInfo.DefenderActionChange.ToState = ECharacterMontageState::Blow;
	BaseOffense.AttackInfo.DefenderActionChange.FreezeTime = 0.1;
	BaseOffense.AttackInfo.DefenderActionChange.HitStun.Active = true;
	BaseOffense.AttackInfo.DefenderActionChange.HitStun.Type = EForceMoveType::KnockOut;
	BaseOffense.AttackInfo.DefenderActionChange.HitStun.VelocityType = EVelocityType::RelativeDir;
	BaseOffense.AttackInfo.DefenderActionChange.HitStun.InSec = 0.5;
	BaseOffense.AttackInfo.DefenderActionChange.Priority = 5;

	for (FBeCaughtActorInfo EnterActor : EnterActors)
	{
		AAwSceneItem* BeHitSceneItem = Cast<AAwSceneItem>(EnterActor.BeCaughtActor);
		if(BeHitSceneItem)
		{
			// 复制基础模板并修改与TouchCharacter相关的属性
			FOffenseInfo Offense = BaseOffense;

			// 设置与SceneItem位置相关的击退方向
			Offense.AttackInfo.DefenderActionChange.HitStun.Velocity =
				FVector(310, 0, 100).RotateAngleAxis((BeHitSceneItem->GetActorLocation() - AOEObj->GetActorLocation()).Rotation().Yaw, FVector::UpVector);

			BeHitSceneItem->BeOffended(Offense, AOEObj->AttackHitManager, AOEObj->Caster, EnterActor.CaughtHitBoxComponent,
				 AOEObj->AttackHitManager->GetAttackBoxByName(EnterActor.AttackBoxName));
		}
	}
	return nullptr;
}

UTimelineNode* UAOEScript::DealDamageAndBlowOnTouch(AAWAoe* AOEObj, TArray<FBeCaughtActorInfo> TouchCharacters, TArray<FString> Params)
{
	if (!CheckAOEAuthority(AOEObj))
		return nullptr;
	const UAoeConfig* Config = GetDefault<UAoeConfig>();
	const float LevelDamageMultiplier = Config->DamageMultipliers[Config&&AOEObj->ActionLevel<4?AOEObj->ActionLevel:3];

	// 创建基础攻击信息模板
	FOffenseInfo BaseOffense = CreateBaseAOEOffenseInfo(AOEObj);

	// 设置与TouchCharacter无关的攻击信息
	BaseOffense.AttackInfo.DamagePower = 1;
	if(AOEObj->Caster)
		BaseOffense.AttackInfo.DamagePower = AOEObj->Caster->CharacterObj.CurProperty.PAttack * LevelDamageMultiplier;
	if (Params.Num())
		BaseOffense.AttackInfo.DamagePower = BaseOffense.AttackInfo.DamagePower * FCString::Atof(*Params[0]) * LevelDamageMultiplier;

	// 设置元素类型
	if (AOEObj->Caster)
		BaseOffense.AttackInfo.Elemental = AOEObj->Caster->GetElemental(EChaElemental::Physical, EElementalPickMethod::Set, ESlotInWeaponObj::MainWeapon);
	if(Params.Num() > 1)
		BaseOffense.AttackInfo.Elemental = UDataFuncLib::FStringToEnum<EChaElemental>(Params[1]);

	// 设置攻击类型
	BaseOffense.AttackInfo.DamageType = Params.Num() > 2 ? UDataFuncLib::FStringToEnum<EDamageType>(Params[2]) : EDamageType::DirectDamage;

	// 设置攻击者动作变化
	BaseOffense.AttackInfo.AttackerActionChange.FreezeTime = 0.1;

	// 解析击退参数
	float FreezeTime = Params.Num() > 3 ? FCString::Atof(*Params[3]) : 0.1f;
	FVector BlowVelocity = FVector(450, 0, 120);
	if (Params.Num() > 6)
	{
		BlowVelocity.X = FCString::Atof(*Params[4]);
		BlowVelocity.Y = FCString::Atof(*Params[5]);
		BlowVelocity.Z = FCString::Atof(*Params[6]);
	}
	float InSec = Params.Num() > 8 ? FCString::Atof(*Params[8]) : 0.7f;
	int Priority = Params.Num() > 9 ? FCString::Atoi(*Params[9]) : 6;

	// 设置击退效果
	BaseOffense.AttackInfo.DefenderActionChange.ChangeMethod = EActionChangeMethod::ToMontageState;
	BaseOffense.AttackInfo.DefenderActionChange.ToState = ECharacterMontageState::Blow;
	BaseOffense.AttackInfo.DefenderActionChange.FreezeTime = FreezeTime;
	BaseOffense.AttackInfo.DefenderActionChange.HitStun.Active = true;
	BaseOffense.AttackInfo.DefenderActionChange.HitStun.Type = EForceMoveType::KnockOut;
	BaseOffense.AttackInfo.DefenderActionChange.HitStun.VelocityType = EVelocityType::RelativeDir;
	BaseOffense.AttackInfo.DefenderActionChange.HitStun.InSec = InSec;
	BaseOffense.AttackInfo.DefenderActionChange.Priority = Priority;

	for (auto& CurCatchedCharacter : TouchCharacters)
	{
		AAwCharacter* Character = Cast<AAwCharacter>(CurCatchedCharacter.BeCaughtActor);
		if (!CanAttackCharacter(Character, AOEObj))
			continue;

		// 复制基础模板并修改与TouchCharacter相关的属性
		FOffenseInfo Offense = BaseOffense;
		Offense.AttackHitBoxName.Add(CurCatchedCharacter.AttackBoxName);

		// 设置击退方向
		if(Params.Num() > 7)
		{
			if(Params[7] == "BlowByCaster")
			{
				if(AOEObj->Caster)
				{
					Offense.AttackInfo.DefenderActionChange.HitStun.Velocity =
						BlowVelocity.RotateAngleAxis((Character->GetActorLocation() - AOEObj->Caster->GetActorLocation()).Rotation().Yaw, FVector::UpVector);
				}
			}
			else if(Params[7] == "BlowByAOE")
			{
				Offense.AttackInfo.DefenderActionChange.HitStun.Velocity =
					BlowVelocity.RotateAngleAxis((Character->GetActorLocation() - AOEObj->GetActorLocation()).Rotation().Yaw, FVector::UpVector);
			}
		}

		UOffenseManager::DoOffense(AOEObj->AttackHitManager, Offense, CurCatchedCharacter, AOEObj->Caster, false);
	}

	// 使用包装函数处理ECS攻击循环（伤害和击飞）
	ProcessECSAttackLoop(AOEObj, BaseOffense, [AOEObj, &Params, BlowVelocity](FOffenseInfo& Offense, const FBeCaughtActorInfo& CurCatchedCharacter) {
		Offense.AttackHitBoxName.Add(CurCatchedCharacter.AttackBoxName);

		// 设置ECS怪物的击退方向
		if(Params.Num() > 7 && CurCatchedCharacter.BeCaughtSubject.IsValid())
		{
			auto LocatedTrait = CurCatchedCharacter.BeCaughtSubject.GetTraitPtr<FLocated, EParadigm::Unsafe>();
			if(LocatedTrait)
			{
				if(Params[7] == "BlowByCaster")
				{
					if(AOEObj->Caster)
					{
						Offense.AttackInfo.DefenderActionChange.HitStun.Velocity =
							BlowVelocity.RotateAngleAxis((LocatedTrait->Location - AOEObj->Caster->GetActorLocation()).Rotation().Yaw, FVector::UpVector);
					}
				}
				else if(Params[7] == "BlowByAOE")
				{
					Offense.AttackInfo.DefenderActionChange.HitStun.Velocity =
						BlowVelocity.RotateAngleAxis((LocatedTrait->Location - AOEObj->GetActorLocation()).Rotation().Yaw, FVector::UpVector);
				}
			}
		}
	});

	return nullptr;
}

UTimelineNode* UAOEScript::SetCanGetCheeseBuffOnCharacterEnter(AAWAoe* AOEObj, TArray<FBeCaughtActorInfo> EnterCharacters, TArray<FString> Params)
{
	if (AOEObj)
	{
		if (AOEObj->GetLocalRole() != ENetRole::ROLE_Authority)
			return nullptr;
		for (auto& CurCatchedCharacter : EnterCharacters)
		{
			AAwCharacter* Character = Cast<AAwCharacter>(CurCatchedCharacter.BeCaughtActor);
			FBuffModel BuffModel = UGameplayFuncLib::GetAwDataManager()->GetBuffModelById("CanGetCheese");
			if (BuffModel.ValidBuffModel() == false) return nullptr;
			Character->AddBuff(FAddBuffInfo(Character, Character, BuffModel, 1, 5, false, true));
		}
	}
	return nullptr;
}

UTimelineNode* UAOEScript::RemoveCanGetCheeseBuffOnCharacterLeave(AAWAoe* AOEObj, TArray<AAwCharacter*> LeaveCharacters, TArray<FString> Params)
{
	if (AOEObj)
	{
		if (AOEObj->GetLocalRole() != ENetRole::ROLE_Authority)
			return nullptr;
		for (auto& CurCatchedCharacter : LeaveCharacters)
		{
			FBuffModel BuffModel = UGameplayFuncLib::GetAwDataManager()->GetBuffModelById("CanGetCheese");
			if (BuffModel.ValidBuffModel() == false) return nullptr;
			CurCatchedCharacter->AddBuff(FAddBuffInfo(CurCatchedCharacter, CurCatchedCharacter, BuffModel, -1, 5, false, true));
		}
	}
	return nullptr;
}

UTimelineNode* UAOEScript::DestroySceneItem(AAWAoe* AOEObj, TArray<FBeCaughtActorInfo> EnterActors, TArray<FString> Params)
{
	for (FBeCaughtActorInfo EnterActor : EnterActors)
	{
		if(EnterActor.BeCaughtActor)
		{
			AAwSceneItem* SceneItem = Cast<AAwSceneItem>(EnterActor.BeCaughtActor);
			if(SceneItem && !SceneItem->bIsBeProtect)
				SceneItem->Kill();
		}
	}
	return nullptr;
}

UTimelineNode* UAOEScript::DestroyRat(AAWAoe* AOEObj, TArray<FBeCaughtActorInfo> EnterCharacters, TArray<FString> Params)
{
	for (const FBeCaughtActorInfo EnterCharacter : EnterCharacters)
	{
		AAwCharacter* Character = Cast<AAwCharacter>(EnterCharacter.BeCaughtActor);
		if(Character && Character->Dead(true) == false)
		{
			if(Character->CharacterObj.Tag.Contains("WereRat"))
				Character->InstantKill();
		}
	}
	return nullptr;
}

UTimelineNode* UAOEScript::TestAOECreate(AAWAoe* AOEObj, TArray<FString> Params)
{
	FVector NewLoc;
	NewLoc.X = FCString::Atof(*Params[0]);
	NewLoc.Y = FCString::Atof(*Params[1]);
	NewLoc.Z = FCString::Atof(*Params[2]);
	AOEObj->SetActorLocation(NewLoc);
	UTimelineNode* NewTimeLine = NewObject<UTimelineNode>();
	UE_LOG(LogTemp, Log, TEXT("打印 %s 坐标 %s"), *Params[3], *NewLoc.ToString());

	return NewTimeLine;
}

FVector UAOEScript::TestAOETween(float TimeElapsed, AAWAoe* AOEObj, FVector OriginPosition, FVector OriginDirection, TArray<FString> Params)
{
	FVector Offset = OriginDirection* TimeElapsed*10;

	return (OriginPosition + Offset);
}

/**
 * 血球aoe
 * 当有玩家进入的时候，我们认为他拾取了这个血球
 * 因此会移除自己，创建另外一个更大的aoe，来捕捉到底多少角色需要被回血等
 * @param AOEObj 这个血球，是一个AoeObj
 * @param EnterCharacters 进入血球的角色，会判断是否符合拾取条件
 * @param Params 策划配置在函数中的参数，[0]aoe的id，[1]aoe的半径（厘米），如果有被动技能影响的话，可以有更多，比如被动技能能增加血球恢复范围等
 */
UTimelineNode* UAOEScript::PickupBloodBall(AAWAoe* AOEObj, TArray<FBeCaughtActorInfo> EnterCharacters, TArray<FString> Params)
{
	const FString DefaultId = "BloodBallCatcher";
	const FString AoeId = Params.Num() > 0 ? (Params[0].IsEmpty() ? DefaultId : Params[0]) : DefaultId;	//如果有别的类型的范围，就可以改这个
	const float Radius = Params.Num() > 1 ? FCString::Atoi(*Params[1]) : 100;		//默认就是1米
	for (const FBeCaughtActorInfo EnterCha : EnterCharacters)
	{
		AAwCharacter* Character = Cast<AAwCharacter>(EnterCha.BeCaughtActor);
		if (Character && Character->Dead(true) == false && Character->Side == 0)
		{
			FAOEModel Model = UGameplayFuncLib::GetDataManager()->GetAoeModelById(AoeId);
			//TODO: 创建另外一个aoe
			UGameplayFuncLib::CreateAOEByLauncher(
				FAOELauncher(
					AOEObj->Caster, Model,
					AOEObj->GetActorLocation(), AOEObj->GetActorRotation().Vector(), 10, ""
				)
			);
			return nullptr;
		}
	}
	return nullptr;
}

// 包装函数实现：处理ECS怪物攻击循环（包含AttackHitBoxName.Add逻辑）
// 使用示例：
// ProcessECSAttackLoop(AOEObj, BaseOffense, [](FOffenseInfo& Offense, const FBeCaughtActorInfo& CurCatchedCharacter) {
//     Offense.AttackHitBoxName.Add(CurCatchedCharacter.AttackBoxName);
//     // 使用优化的击退效果函数：
//     // SetupKnockbackEffectGeneric(Offense, FVector(100, 0, 0), 0.5f, 5, 0.1f, ECharacterMontageState::Hurt);
//     // 或者使用位置相关的击退：
//     // if(CurCatchedCharacter.BeCaughtSubject.IsValid()) {
//     //     auto Located = CurCatchedCharacter.BeCaughtSubject.GetTraitPtr<FLocated, EParadigm::Unsafe>();
//     //     if(Located) SetupKnockbackEffectECS(Offense, Located->Location, AOEObj, FVector(200, 0, 50));
//     // }
// });
void UAOEScript::ProcessECSAttackLoop(AAWAoe* AOEObj,const FOffenseInfo& BaseOffense,
	TFunction<void(FOffenseInfo&, const FBeCaughtActorInfo&)> OffenseModifier)
{
	if (!UAwGameInstance::Instance->isSurvivor)
		return;
	// 准备伤害计算所需的变量
	FDamageInfo DInfo;
	bool IsHit = false;
	bool IsDead = false;
	std::atomic<int32> SafeHitP1{0};
	std::atomic<int32> SafeKill{0};
	std::atomic<int32> SafeHitP2{0};
	const double WorldTimeSeconds = AOEObj->GetWorld()->GetTimeSeconds();

	for (auto& CurCatchedCharacter : AOEObj->AttackHitManager->ThisTickValidECSTarget())
	{
		// 检查ECS怪物是否有效
		if (!CurCatchedCharacter.BeCaughtSubject.IsValid())
			continue;

		auto EcsAttackbox = CurCatchedCharacter.AttackBoxSubject.GetTrait<FTrAttackBox,EParadigm::Unsafe>();

		// 复制基础模板
		FOffenseInfo Offense = BaseOffense;

		// 通过lambda表达式修改特定属性
		OffenseModifier(Offense, CurCatchedCharacter);

		// 获取目标位置
		const auto TargetLocated = CurCatchedCharacter.BeCaughtSubject.GetTraitPtr<FLocated, EParadigm::Unsafe>();
		FVector TargetLocation = TargetLocated ? TargetLocated->Location : FVector::ZeroVector;

		// 调用ECS伤害处理
		UECSDamageLib::TryHitEnemy(CurCatchedCharacter.BeCaughtSubject, EcsAttackbox, Offense, DInfo, TargetLocation, WorldTimeSeconds, IsHit, IsDead);

		// 处理命中后的逻辑
		if (IsHit)
		{
			UECSDamageLib::ProcessECSHitLogic(AOEObj->Caster, DInfo, Offense, false, SafeHitP1, SafeHitP2);
		}

		// 处理击杀后的逻辑
		if (IsDead)
		{
			UECSDamageLib::ProcessECSKillLogic(AOEObj->Caster, DInfo, SafeKill);
		}
	}
	AMechanicRunner::GetInstance()->DealAllPlayerHit(SafeHitP1,SafeHitP2,SafeKill);
}
