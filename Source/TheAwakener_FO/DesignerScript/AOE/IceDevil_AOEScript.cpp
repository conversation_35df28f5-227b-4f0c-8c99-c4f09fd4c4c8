// Fill out your copyright notice in the Description page of Project Settings.


#include "IceDevil_AOEScript.h"

#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"

UTimelineNode* UIceDevil_AOEScript::FreezingEnemy(AAWAoe* AOEObj, TArray<FBeCaughtActorInfo> EnterCharacters, TArray<FString> Params)
{
	if (AOEObj)
	{
		if (AOEObj->GetLocalRole() != ENetRole::ROLE_Authority)
			return nullptr;
		for (auto& CurCatchedCharacter : EnterCharacters)
		{
			AAwCharacter* Character = Cast<AAwCharacter>(CurCatchedCharacter.BeCaughtActor);
			if (AOEObj->Caster)
			{
				if (!AOEObj->Caster->IsEnemy(Character))
					continue;
			}
			//给角色添加冰冻Buff
			FBuffModel ForstBuffModel = UGameplayFuncLib::GetDataManager()->GetBuffModelById("ForstBuff");
			if(ForstBuffModel.Id != "")
			{
				FAddBuffInfo BuffInfo = FAddBuffInfo(AOEObj->Caster, Character, ForstBuffModel, 100, 15, true);
				Character->AddBuff(BuffInfo);
			}
		}
	}
	return nullptr;
}
