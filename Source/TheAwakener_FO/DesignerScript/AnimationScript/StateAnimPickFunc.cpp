// Fill out your copyright notice in the Description page of Project Settings.


#include "StateAnimPickFunc.h"


int UStateAnimPickFunc::StateByBuffStack(AAwCharacter* Character, FActionParam ActionParam, TArray<FString> Params)
{
	if (Params.Num() < 0) return 0;
	const FString BuffId = Params[0];
	TArray<FBuffObj*> Buffs = Character->GetBuff(BuffId, TArray<AAwCharacter*>());
	int Res = 0;
	for (const FBuffObj* Buff : Buffs)
	{
		Res += Buff->Stack;
	}
	return Res;
}