// Fill out your copyright notice in the Description page of Project Settings.


#include "AnimationScript.h"
#include "Kismet/KismetSystemLibrary.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"


FString UAnimationScript::UnAttachTarget(AAwCharacter* Cha, FActionInfo Action, UMontageSpecEvent* LoopCheck , TArray<FString> Params)
{
	if (Cha) Cha->StopAttaching();
	return FString();
}

int UAnimationScript::JumpLoop(AAwCharacter* Cha, FActionInfo Action, TArray<FString> Params)
{
	const bool StandOnGround = Cha->OnGround() == false ;
	return StandOnGround == true ? -1:0;
}

int UAnimationScript::SelfBuffCheck(AAwCharacter* Cha, FActionInfo Action, TArray<FString> Params)
{
	if (Params.Num() < 1) return 1;
	const FString BuffId = Params[0] ;
	const int BuffStack = Params.Num() <= 1 ? 1 : FCString::Atoi(*Params[1]);
	int TotalStack = 0;
	TArray<AAwCharacter*> Caster;
	Caster.Add(Cha);
	TArray<FBuffObj*> Buffs = Cha->GetBuff(BuffId, Caster);
	for (const FBuffObj* Buff : Buffs) TotalStack += Buff->Stack;
	UKismetSystemLibrary::PrintString(Cha, FString("Loop Check :").Append(BuffId).Append(FString::FromInt(TotalStack)));
	return TotalStack < BuffStack ? 0 : 1;
}

int UAnimationScript::SelfBuffStack(AAwCharacter* Cha, FActionInfo Action, TArray<FString> Params)
{
	if (Params.Num() < 1) return 1;
	const FString BuffId = Params[0] ;
	int TotalStack = 0;
	TArray<AAwCharacter*> Caster;
	Caster.Add(Cha);
	TArray<FBuffObj*> Buffs = Cha->GetBuff(BuffId, Caster);
	for (const FBuffObj* Buff : Buffs) TotalStack += Buff->Stack;
	return TotalStack;
}

int UAnimationScript::ActionCommandOccur(AAwCharacter* Cha, FActionInfo Action, TArray<FString> Params)
{
	if (Params.Num() <= 0 || !Cha) return 0;
	const bool CheckHold = Params[0].ToBool();
	const bool CheckPress = Params.Num() > 1 ? Params[1].ToBool() : false;
	TArray<TTuple<FString, EAwInputState>> CheckInput;
	for (FString Cmd : Action.Commands)
	{
		if (CheckHold == true) CheckInput.Add(TTuple<FString, EAwInputState>(Cmd, EAwInputState::Hold));
		if (CheckPress == true) CheckInput.Add(TTuple<FString, EAwInputState>(Cmd, EAwInputState::Press));
	}
	return (Cha->AnyActionOccur(CheckInput) == true) ? 0:1;
}


int UAnimationScript::OnGroundCheck(AAwCharacter* Cha, FActionInfo Action, TArray<FString> Params)
{
	return Cha->OnGround() == true ? 1: 0;
}


UTimelineNode* UAnimationScript::TestForAnimOnLoop(AAwCharacter* Cha, FActionInfo Action, int GotoIndex, TArray<FString> Params)
{
	UKismetSystemLibrary::PrintString(Cha, FString("I'm In ! ").Append(Cha->GetName()).Append(" ").Append(FString::FromInt(GotoIndex)));
	return nullptr;
}

UTimelineNode* UAnimationScript::SelfAddBuff(AAwCharacter* Cha, FActionInfo Action, int GotoIndex, TArray<FString> Params)
{
	if (Params.Num() < 1) return nullptr;
	const FString BuffId = Params[0] ;
	//[1]代表层数(默认1), [2]代表添加的时间(默认1秒), [3]代表是否永久(
	const int Stack = Params.Num() <= 1 ? 1 : FCString::Atoi(*Params[1]);
	const float Time = Params.Num() <= 2 ? 1.f : FCString::Atof(*Params[2]);
	const bool InfinityTime = Params.Num() <= 3 ? true : Params[3].ToBool();
	const FBuffModel Model = UGameplayFuncLib::GetDataManager()->GetBuffModelById(BuffId);
	Cha->AddBuff(FAddBuffInfo(Cha, Cha, Model, Stack, Time, false, InfinityTime));
	UKismetSystemLibrary::PrintString(Cha, FString("Loop AddBuff:").Append(BuffId).Append(FString::FromInt(Stack)));
	return nullptr;
}