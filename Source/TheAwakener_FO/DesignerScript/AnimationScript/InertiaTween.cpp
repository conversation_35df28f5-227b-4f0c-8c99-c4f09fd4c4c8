// Fill out your copyright notice in the Description page of Project Settings.


#include "InertiaTween.h"

FVector UInertiaTween::SlowDownToStop(FVector2D InitSpeed, float DeltaTime, TArray<FString> Params)
{
	if (Params.Num() < 1 || FMath::IsNearly<PERSON>ero(InitSpeed.Size())) return FVector(InitSpeed.X, InitSpeed.Y, 0);
	float ReducePerSec = FCString::Atof(*Params[0]);
	if (ReducePerSec < 10) ReducePerSec = 10;
	const float TickReduce = DeltaTime * ReducePerSec;
	const float Percent = (InitSpeed.Size() - TickReduce) / InitSpeed.Size();
	InitSpeed *= Percent;
	return FVector(InitSpeed.X, InitSpeed.Y, 0);
}