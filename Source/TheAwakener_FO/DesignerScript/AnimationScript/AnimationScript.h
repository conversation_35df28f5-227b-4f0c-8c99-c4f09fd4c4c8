// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
//#include "TheAwakener_FO/GameFramework/Timeline/TimelineNode.h"
#include "TheAwakener_FO/GamePlay/AnimNotify/LoopCheck.h"
#include "TheAwakener_FO/GamePlay/AnimNotifyState/MontageSpecEvent.h"
#include "UObject/Object.h"
#include "AnimationScript.generated.h"

/**
 * 动画OnLoop的脚本，动画目前也就OnLoop了
 */
UCLASS()
class THEAWAKENER_FO_API UAnimationScript : public UObject
{
	GENERATED_BODY()

public:
	//【OnLoop】
	UFUNCTION()
	static UTimelineNode* TestForAnimOnLoop(AAwCharacter* Cha, FActionInfo Action, int GotoIndex, TArray<FString> Params);

	//【OnLoop】角色自己给自己添加Buff，这个Buff必须在Json有所配置，Params[0]=BuffId(不可缺省), [1]代表层数(默认1), [2]代表添加的时间(默认1秒), [3]代表是否永久(默认true，由别的逻辑删除这个buff)
	UFUNCTION()
	static UTimelineNode* SelfAddBuff(AAwCharacter* Cha, FActionInfo Action, int GotoIndex, TArray<FString> Params);

	//【UnMount】MontageLoop中脱离Attach关系
	UFUNCTION()
	static FString UnAttachTarget(AAwCharacter* Cha, FActionInfo Action, UMontageSpecEvent* LoopCheck, TArray<FString> Params);

	//【LoopCheck】跳跃的LoopCheck，为了保证动画跳回去
	UFUNCTION()
	static int JumpLoop(AAwCharacter* Cha, FActionInfo Action, TArray<FString> Params);

	//【LoopCheck】下落贴地的LoopCheck，如果贴地就返回1，否则返回0
	UFUNCTION()
	static int OnGroundCheck(AAwCharacter* Cha, FActionInfo Action, TArray<FString> Params);

	//【LoopCheck】Buff检查，根据Buff的有无，决定去留，Params[0](FString)是BuffId，Params[1](int)是层数，如果层数不足返回值=0，否则返回1
	UFUNCTION()
	static int SelfBuffCheck(AAwCharacter* Cha, FActionInfo Action, TArray<FString> Params);

	//【LoopCheck】检查并返回指定Buff的层数，返回值等于层数，Params[0](FString)是要检查的BuffId
	UFUNCTION()
	static int SelfBuffStack(AAwCharacter* Cha, FActionInfo Action, TArray<FString> Params);

	//【LoopCheck】检查Action的按钮状态，Params[0](bool)是要检查的状态是否包含Hold，[1](bool)代表检查状态是否包含Press。
	// 有一个按键条件符合就算符合了，如果2个都是false那么就算直接符合了，返回0如果符合，1如果不符合
	UFUNCTION()
	static int ActionCommandOccur(AAwCharacter* Cha, FActionInfo Action, TArray<FString> Params);
};
