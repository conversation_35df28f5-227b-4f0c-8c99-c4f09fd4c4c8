// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "UObject/Object.h"
#include "StateAnimPickFunc.generated.h"

/**
 * 选取状态使用动画的
 */
UCLASS()
class THEAWAKENER_FO_API UStateAnimPickFunc : public UObject
{
	GENERATED_BODY()
public:
	/**
	 *返回Buff的总层数，如果没有Buff就会返回0，有几层返回几（其实超过1不要紧的，如果你就填写了0和1的话）
	 *@return Buff层数
	 */
	UFUNCTION(BlueprintCallable)
	static int StateByBuffStack(AAwCharacter* Character, FActionParam ActionParam, TArray<FString> Params);
};
