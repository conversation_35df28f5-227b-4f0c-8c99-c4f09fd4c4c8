// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "UObject/Object.h"
#include "MontageAnimPickFunc.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UMontageAnimPickFunc : public UObject
{
	GENERATED_BODY()

public:
	// 随机（RandomNumber，随机数量。（传入1，就是0或1））
	// Params[0] - 随机数的最大值
	UFUNCTION()
	static int Random(AAwCharacter* Character, FActionParam ActionParam, TArray<FString> Params);
	
	//根据移动速度返回下标，300一档，0是0，(0, 300)是1, [300-600)是2, [600, 900)是3
	UFUNCTION()
	static int GetAnimByMoveSpeed(AAwCharacter* Character, FActionParam ActionParam, TArray<FString> Params);

	//根据是否在地面返回，如果在地面返回0，否则返回1
	UFUNCTION()
	static int GetAnimByOnGround(AAwCharacter* Character, FActionParam ActionParam, TArray<FString> Params);

	/**
	 * 根据受击的力来源和自己的面向决定
	 * 地面，迎面受击，返回0
	 * 地面，背后受击，返回1
	 * 空中，迎面受击，返回2
	 * 空中，背后受击，返回3
	 */
	UFUNCTION()
	static int PowerSourceFrontBehind(AAwCharacter* Character, FActionParam ActionParam, TArray<FString> Params);

	/**
	 * 根据受击的力来源和自己的决定击飞
	 * 地面，近距离受击，返回0（原地击飞）
	 * 地面，迎面受击，返回1
	 * 地面，背后受击，返回2
	 * 空中，近距离受击，返回3（原地击飞）
	 * 空中，迎面受击，返回4
	 * 空中，背后受击，返回5
	 */
	UFUNCTION()
	static int PowerSourceForBlow(AAwCharacter* Character, FActionParam ActionParam, TArray<FString> Params);

	//根据双方的动作优先级之差算出动作index（ActionParam中我方比对方高多少，就返回值为多少）, param[0]是档位，默认是2，也就是每2挡+1反馈
	UFUNCTION()
	static int GetActionByPriorityDistance(AAwCharacter* Character, FActionParam ActionParam, TArray<FString> Params);
	
	/**
	 *返回Buff的总层数，如果没有Buff就会返回0，有几层返回几（其实超过1不要紧的，如果你就填写了0和1的话）
	 *@return Buff层数
	 */
	UFUNCTION()
	static int GetBuffStack(AAwCharacter* Character, FActionParam ActionParam, TArray<FString> Params);

	//查找角色当前的FightWill.Level是否小于Params[0],如果是返回0; 如果大于等于Params[0],返回1
	UFUNCTION()
	static int CheckFightingWillLevel(AAwCharacter* Character, FActionParam ActionParam, TArray<FString> Params);

	//查找角色当前的FightWill.Level,返回FightingWill.Level
	UFUNCTION()
	static int ReturnFightingWillLevel(AAwCharacter* Character, FActionParam ActionParam, TArray<FString> Params);

	// 根据当前职业返回需要使用哪个动画
	// Rouge 法器里面使用
	// 剑盾 Gerasso(Swordman)   - 0
	// 双刀 Henrik(BladeDancer) - 1
	// 长枪 Sola(Spearman)      - 2
	// 大剑 Tierdagon(Warrior)  - 3
	UFUNCTION()
	static int ReturnPawnClassIndex(AAwCharacter* Character, FActionParam ActionParam, TArray<FString> Params);
};
