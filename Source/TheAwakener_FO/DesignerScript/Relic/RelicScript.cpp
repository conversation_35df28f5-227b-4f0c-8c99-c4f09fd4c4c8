// Fill out your copyright notice in the Description page of Project Settings.


#include "RelicScript.h"

#include "Algo/Accumulate.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GameFramework/Manager/AwRogueDataSystem.h"


// this macro to check effect vaild and get the stack
#define  PREVIEW_EFFECT_CHANGE_CHECK  \
	for (auto Buff:TargetRelic.RelicEffectBuffs)\
	{\
		EffectStr = Buff;\
		int index = Buff.Find("(");\
		if(index>0)\
		{\
			Buff.RemoveAt (index,Buff.Len()-index);\
		}\
		if (Buff.Equals(CoreBuffId,ESearchCase::IgnoreCase))\
		{\
			bContains = true;\
			break;\
		}\
	}\
	if (!bContains)\
	{\
		return PreviewResult; \
	}\
	else\
	{\
		FString Left,Right;\
		FString StackString = UDataFuncLib::SplitParamBetweenSplitChar(EffectStr,"(",")",Left,Right);\
		ChangeStack = StackString.IsNumeric()?FCString::Atoi(*StackString):1;\
	}

#define  PREVIEW_RESULT_ADD  \
	PreviewResult.PreViewResult.Add("Flag",PreViewFlag);\
	PreviewResult.PreViewResult.Add("Cur",FString::SanitizeFloat(CurValue*CaluBasePower));\
	PreviewResult.PreViewResult.Add("After",FString::SanitizeFloat(AfterValue*CaluBasePower));\
	PreviewResult.PreViewResult.Add("PropType",UDataFuncLib::EnumToFString(PropType));\

FRelicPreviewResult URelicScript::OnRelicPreview_BuffStack( FAwRelicInfo TargetRelic,TArray<FString> Params,AAwPlayerController* Controller)
{
	FRelicPreviewResult PreviewResult = FRelicPreviewResult(TargetRelic.Id);
	AAwCharacter* PlayerCharacter = Controller->CurCharacter;
	if (!IsValid(PlayerCharacter))
	{
		return  PreviewResult;
	}
	FString PreViewFlag = Params.Num()>0?Params[0]:"";
	FString CoreBuffId = Params.Num()>1?Params[1]:"";
	FString AntiBuffId = Params.Num()>2?Params[2]:"";
	float BaseValue = Params.Num()>3?FCString::Atof(*Params[3]):1;
	float CaluBasePower = Params.Num()>4?FCString::Atof(*Params[4]):1;
	ERelicType PropType = Params.Num()>5?UDataFuncLib::FStringToEnum<ERelicType>(Params[5]):ERelicType::Attack;

	int CurStack =  PlayerCharacter->GetBuffStackTotal(CoreBuffId) - PlayerCharacter->GetBuffStackTotal(AntiBuffId);

	int ChangeStack = 0;
	bool bContains = false;
	FString EffectStr = CoreBuffId;
	
	PREVIEW_EFFECT_CHANGE_CHECK
	/*
	bool bContains = false;
	FString EffectStr = CoreBuffId;
	for (auto Buff:TargetRelic.RelicEffectBuffs)
	{
		EffectStr = Buff;
		int index = Buff.Find("(");
		Buff.RemoveAt (index,Buff.Len()-index);
		if (Buff.Equals(CoreBuffId,ESearchCase::IgnoreCase))
		{
			bContains = true;
			break;
		}
	}
	if (!bContains)
	{
		return PreviewResult; 
	}
	else
	{
		FString Left,Right;
		FString StackString = UDataFuncLib::SplitParamBetweenSplitChar(EffectStr,"(",")",Left,Right);
		ChangeStack = StackString.IsNumeric()?FCString::Atoi(*StackString):0;
	}*/
	
	PreviewResult.PreViewResult.Add("Flag",PreViewFlag);
	PreviewResult.PreViewResult.Add("Cur",FString::SanitizeFloat(CurStack*CaluBasePower+BaseValue));
	PreviewResult.PreViewResult.Add("After",FString::SanitizeFloat(CurStack*CaluBasePower+ChangeStack*CaluBasePower+BaseValue));
	PreviewResult.PreViewResult.Add("PropType",UDataFuncLib::EnumToFString(PropType));
	return  PreviewResult;
}

FRelicPreviewResult URelicScript::OnRelicPreview_BuffProp(FAwRelicInfo TargetRelic,TArray<FString> Params,AAwPlayerController* Controller)
{
	FRelicPreviewResult PreviewResult = FRelicPreviewResult(TargetRelic.Id);
	AAwCharacter* PlayerCharacter = Controller->CurCharacter;
	if (!IsValid(PlayerCharacter))
	{
		return  PreviewResult;
	}
	FString PreViewFlag = Params.Num()>0?Params[0]:"";
	FString CoreBuffId = Params.Num()>1?Params[1]:"";
	float CaluBasePower = Params.Num()>2?FCString::Atof(*Params[2]):1;
	ERelicType PropType = Params.Num()>3?UDataFuncLib::FStringToEnum<ERelicType>(Params[3]):ERelicType::Attack;
	
	int ChangeStack = 0;
	bool bContains = false;
	FString EffectStr = CoreBuffId;
	
	PREVIEW_EFFECT_CHANGE_CHECK
	
	float CurValue = 0;
	float AfterValue = 0;
	FBuffModel BuffModel = UGameplayFuncLib::GetAwDataManager()->GetBuffModelById(CoreBuffId);
	
	FChaProp Base =  PlayerCharacter->CharacterObj.BaseProp;
	FChaProp BuffPlus = PlayerCharacter->CharacterObj.BuffPlus ;
	if (BuffModel.CharacterPropertyModify.Num()>0)
	{
		BuffPlus = BuffPlus + BuffModel.CharacterPropertyModify[0] *ChangeStack;
	}
	FChaProp BuffTimes = PlayerCharacter->CharacterObj.BuffTimes ;
	if (BuffModel.CharacterPropertyModify.Num()>1)
	{
		BuffTimes =  BuffTimes +BuffModel.CharacterPropertyModify[1]*ChangeStack;
	}
	//Foolish If - else! if you want to use switch,  first  you need to  confirm  What Prop really contains ?? then create a enum
	if (PreViewFlag.Equals("Hp",ESearchCase::IgnoreCase))
	{
		CurValue = PlayerCharacter->CharacterObj.CurProperty.HP;
		AfterValue =   ((Base + BuffPlus)*BuffTimes).HP;
	}
	else if (PreViewFlag.Equals("PAtk",ESearchCase::IgnoreCase))
	{
		CurValue = PlayerCharacter->CharacterObj.CurProperty.PAttack;
		AfterValue =   ((Base + BuffPlus)*BuffTimes).PAttack;
	}
	else if (PreViewFlag.Equals("MAtk",ESearchCase::IgnoreCase))
	{
		CurValue = PlayerCharacter->CharacterObj.CurProperty.MAttack;
		AfterValue =   ((Base + BuffPlus)*BuffTimes).MAttack;
	}
	else if (PreViewFlag.Equals("CtRate",ESearchCase::IgnoreCase))
	{
		CurValue = PlayerCharacter->CharacterObj.CurProperty.CriticalChance;
		AfterValue =   ((Base + BuffPlus)*BuffTimes).CriticalChance;
	}
	else if (PreViewFlag.Equals("CtDmg",ESearchCase::IgnoreCase))
	{
		CurValue = PlayerCharacter->CharacterObj.CurProperty.CriticalRate;
		AfterValue =   ((Base + BuffPlus)*BuffTimes).CriticalRate;
	}
	else if (PreViewFlag.Equals("Spd",ESearchCase::IgnoreCase))
	{
		CurValue = PlayerCharacter->CharacterObj.CurProperty.ActionSpeed;
		AfterValue =   ((Base + BuffPlus)*BuffTimes).ActionSpeed;
	}

	PREVIEW_RESULT_ADD
	/*
	PreviewResult.PreViewResult.Add("Flag",PreViewFlag);
	PreviewResult.PreViewResult.Add("Cur",FString::SanitizeFloat(CurValue*CaluBasePower));
	PreviewResult.PreViewResult.Add("After",FString::SanitizeFloat(AfterValue*CaluBasePower));
	*/
	
	return  PreviewResult;
}

FRelicPreviewResult URelicScript::OnRelicPreview_MoneyCastAtk(FAwRelicInfo TargetRelic, TArray<FString> Params,AAwPlayerController* Controller)
{
	FRelicPreviewResult PreviewResult = FRelicPreviewResult(TargetRelic.Id);
	AAwCharacter* PlayerCharacter = Controller->CurCharacter;
	if (!IsValid(PlayerCharacter))
	{
		return  PreviewResult;
	}
	UAwRogueDataSystem* SubSystem =  GWorld->GetGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	if (!SubSystem)
	{
		return PreviewResult;
	}
	
	FString PreViewFlag = Params.Num()>0?Params[0]:"";
	FString CoreBuffId = Params.Num()>1?Params[1]:"";
	float Percent = Params.Num()>2?FCString::Atof(*Params[2]):1;
	float CaluBasePower = Params.Num()>3?FCString::Atof(*Params[3]):1;
	ERelicType PropType = Params.Num()>4?UDataFuncLib::FStringToEnum<ERelicType>(Params[4]):ERelicType::Attack;
	int ChangeStack = 0;
	bool bContains = false;
	FString EffectStr = CoreBuffId;
	
	PREVIEW_EFFECT_CHANGE_CHECK
	
	int Total =  SubSystem->GetCurrency_Coin();
	float  AddNum = Total*Percent*ChangeStack;
	
	FChaProp BuffTimes  = FChaProp();
	BuffTimes.PAttack = AddNum;
	BuffTimes.MAttack = AddNum;

	float CurValue = 0;
	float AfterValue = 0;
	
	CurValue = PlayerCharacter->CharacterObj.CurProperty.PAttack;
	AfterValue =   (PlayerCharacter->CharacterObj.CurProperty*BuffTimes).PAttack;

	PREVIEW_RESULT_ADD
	
	return  PreviewResult;
}

FRelicPreviewResult URelicScript::OnRelicPreview_KillCastHp(FAwRelicInfo TargetRelic, TArray<FString> Params,AAwPlayerController* Controller)
{
	FRelicPreviewResult PreviewResult = FRelicPreviewResult(TargetRelic.Id);
	AAwCharacter* PlayerCharacter = Controller->CurCharacter;
	if (!IsValid(PlayerCharacter))
	{
		return  PreviewResult;
	}
	UAwRogueDataSystem* SubSystem =  GWorld->GetGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	if (!SubSystem)
	{
		return PreviewResult;
	}

	FString PreViewFlag = Params.Num()>0?Params[0]:"";
	FString CoreBuffId = Params.Num()>1?Params[1]:"";
	int AddNum =Params.Num() >2? FCString::Atoi(*Params[2]) :1;
	int Max =Params.Num() >3? FCString::Atoi(*Params[3]) :1000000;
	float CaluBasePower = Params.Num()>4?FCString::Atof(*Params[4]):1;
	ERelicType PropType = Params.Num()>5?UDataFuncLib::FStringToEnum<ERelicType>(Params[5]):ERelicType::Attack;
	
	int ChangeStack = 0;
	bool bContains = false;
	FString EffectStr = CoreBuffId;

	PREVIEW_EFFECT_CHANGE_CHECK
	
	int KillNum = SubSystem->GetCurBattleData().TotalKilledBossNum+ SubSystem->GetCurBattleData().TotalKilledEliteNum;

	FChaProp BuffPlus  = FChaProp();
	BuffPlus.HP =FMath::Clamp(AddNum*KillNum*ChangeStack,0,Max);

	float CurValue = 0;
	float AfterValue = 0;
	
	CurValue = PlayerCharacter->CharacterObj.CurProperty.HP;
	AfterValue =   (PlayerCharacter->CharacterObj.CurProperty + BuffPlus).HP;

	PREVIEW_RESULT_ADD
	
	return PreviewResult;
}

FRelicPreviewResult URelicScript::OnRelicPreview_RelicNumCastHp(FAwRelicInfo TargetRelic, TArray<FString> Params,AAwPlayerController* Controller)
{
	FRelicPreviewResult PreviewResult = FRelicPreviewResult(TargetRelic.Id);
	AAwCharacter* PlayerCharacter = Controller->CurCharacter;
	if (!IsValid(PlayerCharacter))
	{
		return  PreviewResult;
	}
	UAwRelicSubSystem* RelicSystem =  UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRelicSubSystem>();
	if (!RelicSystem)
	{
		return  PreviewResult;
	}
	FString PreViewFlag = Params.Num()>0?Params[0]:"";
	FString CoreBuffId = Params.Num()>1?Params[1]:"";
	int AddNum =Params.Num() >2? FCString::Atoi(*Params[2]) :1;
	int Max =Params.Num() >3? FCString::Atoi(*Params[3]) :1000;
	float CaluBasePower = Params.Num()>4?FCString::Atof(*Params[4]):1;
	ERelicType PropType = Params.Num()>5?UDataFuncLib::FStringToEnum<ERelicType>(Params[5]):ERelicType::Attack;
	int ChangeStack = 0;
	bool bContains = false;
	FString EffectStr = CoreBuffId;
	
	PREVIEW_EFFECT_CHANGE_CHECK

	TArray<int> Nums;
	RelicSystem->RelicHasGotInFormalPool.GenerateValueArray(Nums);
	int Total = Algo::Accumulate(Nums,0,TPlus<>())+1;
	
	
	FChaProp BuffPlus  = FChaProp();
	BuffPlus.HP =FMath::Clamp(AddNum*Total*ChangeStack,0,Max);

	float CurValue = 0;
	float AfterValue = 0;
	
	CurValue = PlayerCharacter->CharacterObj.CurProperty.HP;
	AfterValue =   (PlayerCharacter->CharacterObj.CurProperty + BuffPlus).HP;

	PREVIEW_RESULT_ADD
	
	return PreviewResult;
}

FRelicPreviewResult URelicScript::OnRelicPreview_GodTagCastHp(FAwRelicInfo TargetRelic, TArray<FString> Params,AAwPlayerController* Controller)
{
	FRelicPreviewResult PreviewResult = FRelicPreviewResult(TargetRelic.Id);
	AAwCharacter* PlayerCharacter = Controller->CurCharacter;
	if (!IsValid(PlayerCharacter))
	{
		return  PreviewResult;
	}
	UAwRelicSubSystem* RelicSystem =  UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRelicSubSystem>();
	if (!RelicSystem)
	{
		return  PreviewResult;
	}
	FString PreViewFlag = Params.Num()>0?Params[0]:"";
	FString CoreBuffId = Params.Num()>1?Params[1]:"";
	int AddNum =Params.Num() >2? FCString::Atoi(*Params[2]) :1;
	float CaluBasePower = Params.Num()>3?FCString::Atof(*Params[3]):1;
	ERelicType PropType = Params.Num()>4?UDataFuncLib::FStringToEnum<ERelicType>(Params[4]):ERelicType::Attack;
	int ChangeStack = 0;
	bool bContains = false;
	FString EffectStr = CoreBuffId;
	
	PREVIEW_EFFECT_CHANGE_CHECK

	TArray<FString> Tags;
	Tags = RelicSystem->GetRelicTagsHasGot();
	if (!Tags.Contains("Group_Luana"))
	{
		Tags.Add("Group_Luana");
	}
	int Total = Tags.Num();
	
	FChaProp BuffPlus  = FChaProp();
	BuffPlus.HP =AddNum*Total*ChangeStack;

	float CurValue = 0;
	float AfterValue = 0;
	
	CurValue = PlayerCharacter->CharacterObj.CurProperty.HP;
	AfterValue =   (PlayerCharacter->CharacterObj.CurProperty + BuffPlus).HP;

	PREVIEW_RESULT_ADD
	
	return PreviewResult;
}

FRelicPreviewResult URelicScript::OnRelicPreview_RelicNumCastCtRate(FAwRelicInfo TargetRelic, TArray<FString> Params,AAwPlayerController* Controller)
{
	FRelicPreviewResult PreviewResult = FRelicPreviewResult(TargetRelic.Id);
	AAwCharacter* PlayerCharacter = Controller->CurCharacter;
	if (!IsValid(PlayerCharacter))
	{
		return  PreviewResult;
	}
	UAwRelicSubSystem* RelicSystem =  UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRelicSubSystem>();
	if (!RelicSystem)
	{
		return  PreviewResult;
	}
	FString PreViewFlag = Params.Num()>0?Params[0]:"";
	FString CoreBuffId = Params.Num()>1?Params[1]:"";
	float AddNum =Params.Num() >2? FCString::Atof(*Params[2]) :1;
	int Max =Params.Num() >3? FCString::Atoi(*Params[3]) :1000;
	float CaluBasePower = Params.Num()>4?FCString::Atof(*Params[4]):1;
	ERelicType PropType = Params.Num()>5?UDataFuncLib::FStringToEnum<ERelicType>(Params[5]):ERelicType::Attack;
	int ChangeStack = 0;
	bool bContains = false;
	FString EffectStr = CoreBuffId;
	
	PREVIEW_EFFECT_CHANGE_CHECK

	TArray<int> Nums;
	RelicSystem->RelicHasGotInFormalPool.GenerateValueArray(Nums);
	int Total = Algo::Accumulate(Nums,0,TPlus<>())+1;
	
	
	FChaProp BuffPlus  = FChaProp();
	BuffPlus.CriticalChance =FMath::Clamp(AddNum*Total*ChangeStack,0,Max);

	float CurValue = 0;
	float AfterValue = 0;
	
	CurValue = PlayerCharacter->CharacterObj.CurProperty.CriticalChance;
	AfterValue =   (PlayerCharacter->CharacterObj.CurProperty + BuffPlus).CriticalChance;

	PREVIEW_RESULT_ADD
	
	return PreviewResult;
}

FRelicPreviewResult URelicScript::OnRelicPreview_GodTagCastCtRate(FAwRelicInfo TargetRelic, TArray<FString> Params,AAwPlayerController* Controller)
{
	FRelicPreviewResult PreviewResult = FRelicPreviewResult(TargetRelic.Id);
	AAwCharacter* PlayerCharacter = Controller->CurCharacter;
	if (!IsValid(PlayerCharacter))
	{
		return  PreviewResult;
	}
	UAwRelicSubSystem* RelicSystem =  UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRelicSubSystem>();
	if (!RelicSystem)
	{
		return  PreviewResult;
	}
	FString PreViewFlag = Params.Num()>0?Params[0]:"";
	FString CoreBuffId = Params.Num()>1?Params[1]:"";
	float AddNum =Params.Num() >2? FCString::Atof(*Params[2]) :1;
	float CaluBasePower = Params.Num()>3?FCString::Atof(*Params[3]):1;
	ERelicType PropType = Params.Num()>4?UDataFuncLib::FStringToEnum<ERelicType>(Params[4]):ERelicType::Attack;
	int ChangeStack = 0;
	bool bContains = false;
	FString EffectStr = CoreBuffId;
	
	PREVIEW_EFFECT_CHANGE_CHECK

	TArray<FString> Tags;
	Tags = RelicSystem->GetRelicTagsHasGot();
	if (!Tags.Contains("Group_Ardipeng"))
	{
		Tags.Add("Group_Ardipeng");
	}
	int Total = Tags.Num();
	
	FChaProp BuffPlus  = FChaProp();
	BuffPlus.CriticalChance =AddNum*Total*ChangeStack;

	float CurValue = 0;
	float AfterValue = 0;
	
	CurValue = PlayerCharacter->CharacterObj.CurProperty.CriticalChance;
	AfterValue =   (PlayerCharacter->CharacterObj.CurProperty + BuffPlus).CriticalChance;

	PREVIEW_RESULT_ADD
	
	return PreviewResult;
}

FRelicPreviewResult URelicScript::OnRelicPreview_RelicNumCastCtDmg(FAwRelicInfo TargetRelic, TArray<FString> Params,AAwPlayerController* Controller)
{
	FRelicPreviewResult PreviewResult = FRelicPreviewResult(TargetRelic.Id);
	AAwCharacter* PlayerCharacter = Controller->CurCharacter;
	if (!IsValid(PlayerCharacter))
	{
		return  PreviewResult;
	}
	UAwRelicSubSystem* RelicSystem =  UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRelicSubSystem>();
	if (!RelicSystem)
	{
		return  PreviewResult;
	}
	FString PreViewFlag = Params.Num()>0?Params[0]:"";
	FString CoreBuffId = Params.Num()>1?Params[1]:"";
	float AddNum =Params.Num() >2? FCString::Atof(*Params[2]) :1;
	int Max =Params.Num() >3? FCString::Atoi(*Params[3]) :1000;
	float CaluBasePower = Params.Num()>4?FCString::Atof(*Params[4]):1;
	ERelicType PropType = Params.Num()>5?UDataFuncLib::FStringToEnum<ERelicType>(Params[5]):ERelicType::Attack;
	int ChangeStack = 0;
	bool bContains = false;
	FString EffectStr = CoreBuffId;
	
	PREVIEW_EFFECT_CHANGE_CHECK

	TArray<int> Nums;
	RelicSystem->RelicHasGotInFormalPool.GenerateValueArray(Nums);
	int Total = Algo::Accumulate(Nums,0,TPlus<>())+1;
	
	
	FChaProp BuffPlus  = FChaProp();
	BuffPlus.CriticalRate =FMath::Clamp(AddNum*Total*ChangeStack,0,Max);

	float CurValue = 0;
	float AfterValue = 0;
	
	CurValue = PlayerCharacter->CharacterObj.CurProperty.CriticalRate;
	AfterValue =   (PlayerCharacter->CharacterObj.CurProperty + BuffPlus).CriticalRate;

	PREVIEW_RESULT_ADD
	
	return PreviewResult;
}

FRelicPreviewResult URelicScript::OnRelicPreview_GodTagCastCtDmg(FAwRelicInfo TargetRelic, TArray<FString> Params,AAwPlayerController* Controller)
{
	FRelicPreviewResult PreviewResult = FRelicPreviewResult(TargetRelic.Id);
	AAwCharacter* PlayerCharacter = Controller->CurCharacter;
	if (!IsValid(PlayerCharacter))
	{
		return  PreviewResult;
	}
	UAwRelicSubSystem* RelicSystem =  UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRelicSubSystem>();
	if (!RelicSystem)
	{
		return  PreviewResult;
	}
	FString PreViewFlag = Params.Num()>0?Params[0]:"";
	FString CoreBuffId = Params.Num()>1?Params[1]:"";
	float AddNum =Params.Num() >2? FCString::Atof(*Params[2]) :1;
	float CaluBasePower = Params.Num()>3?FCString::Atof(*Params[3]):1;
	ERelicType PropType = Params.Num()>4?UDataFuncLib::FStringToEnum<ERelicType>(Params[4]):ERelicType::Attack;
	int ChangeStack = 0;
	bool bContains = false;
	FString EffectStr = CoreBuffId;
	
	PREVIEW_EFFECT_CHANGE_CHECK

	TArray<FString> Tags;
	Tags = RelicSystem->GetRelicTagsHasGot();
	if (!Tags.Contains("Group_Ardipeng"))
	{
		Tags.Add("Group_Ardipeng");
	}
	int Total = Tags.Num();
	
	FChaProp BuffPlus  = FChaProp();
	BuffPlus.CriticalRate =AddNum*Total*ChangeStack;

	float CurValue = 0;
	float AfterValue = 0;
	
	CurValue = PlayerCharacter->CharacterObj.CurProperty.CriticalRate;
	AfterValue =   (PlayerCharacter->CharacterObj.CurProperty + BuffPlus).CriticalRate;

	PREVIEW_RESULT_ADD
	
	return PreviewResult;
}

FRelicPreviewResult URelicScript::OnRelicPreview_ElementalBuffExtraDuration(FAwRelicInfo TargetRelic,
	TArray<FString> Params,AAwPlayerController* Controller)
{
	FRelicPreviewResult PreviewResult = FRelicPreviewResult(TargetRelic.Id);
	AAwCharacter* PlayerCharacter = Controller->CurCharacter;
	if (!IsValid(PlayerCharacter))
	{
		return  PreviewResult;
	}
	
	FString PreViewFlag = Params.Num()>0?Params[0]:"";
	FString CoreBuffId = Params.Num()>1?Params[1]:"";
	float AddNum =Params.Num() >2? FCString::Atof(*Params[2]) :1;
	float CaluBasePower = Params.Num()>3?FCString::Atof(*Params[3]):1;
	ERelicType PropType = Params.Num()>4?UDataFuncLib::FStringToEnum<ERelicType>(Params[4]):ERelicType::Attack;
	int CurStack =  PlayerCharacter->GetBuffStackTotal(CoreBuffId);

	int ChangeStack = 0;
	bool bContains = false;
	FString EffectStr = CoreBuffId;
	
	PREVIEW_EFFECT_CHANGE_CHECK

	float CurValue = CurStack*AddNum;
	float AfterValue =CurValue+ChangeStack*AddNum;
	
	PREVIEW_RESULT_ADD

	return  PreviewResult;
}
