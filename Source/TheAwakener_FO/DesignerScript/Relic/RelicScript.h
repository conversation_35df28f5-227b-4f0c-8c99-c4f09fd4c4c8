// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/GamePlay/Roguelike/Relic/AwRelicSubSystem.h"
#include "UObject/Object.h"
#include "RelicScript.generated.h"

/**
 * Relic特定脚本函数
 * by <PERSON><PERSON><PERSON>
 */
UCLASS()
class THEAWAKENER_FO_API URelicScript : public UObject
{

	GENERATED_BODY()

	//[OnRelicPreview]
	//@Param[0] TargetRelic
	//@Param[1] PreviewFlag  Explain What is the hell  that  you want to Preview
	//@Param[2] CoreBuff
	//@Param[3] AntiBuff
	//@Param[4] Base Value  like   DamageRate Base = 100%
	//@Param[5] BuffStack Power like 1 Stack =  0.01
	//@Param[6] Prop Type
	// Check Normal Direct Buff Change  by preview Relic
	UFUNCTION()
	static  FRelicPreviewResult OnRelicPreview_BuffStack( FAwRelicInfo TargetRelic ,TArray<FString>Para<PERSON>,AAwPlayerController* Controller );

	//[OnRelicPreview]
	//@Param[0] TargetRelic
	//@Param[1] PreviewFlag  Explain What is the hell  that  you want to Preview
	//@Param[2] CoreBuff
	//@Param[3] BuffStack Power like 1 Stack =  0.01
	//@Param[4] Prop Type
	// Check Prop  Buff Change  by preview Relic
	UFUNCTION()
	static  FRelicPreviewResult OnRelicPreview_BuffProp( FAwRelicInfo TargetRelic ,TArray<FString>Params,AAwPlayerController* Controller );

	//[OnRelicPreview]
	//@Param[0] TargetRelic
	//@Param[1] PreviewFlag  Explain What is the hell  that  you want to Preview
	//@Param[2] CoreBuff
	//@Param[2] Coin Cast Power Like  1 Coin =  0.0001 
	//@Param[3] BuffStack Power like 1 Stack =  0.01
	//@Param[4] Prop Type
	// Check Special Buff Change  by preview Relic
	UFUNCTION()
	static  FRelicPreviewResult OnRelicPreview_MoneyCastAtk( FAwRelicInfo TargetRelic ,TArray<FString>Params,AAwPlayerController* Controller );



	//[OnRelicPreview]
	//@Param[0] TargetRelic
	//@Param[1] PreviewFlag  Explain What is the hell  that  you want to Preview
	//@Param[2] CoreBuff
	//@Param[2]  Cast Power Like  1 =  0.0001 
	//@Param[3] Max =  1
	//@Param[3] BuffStack Power like 1 Stack =  0.01
	//@Param[4] Prop Type
	// Check Special Buff Change  by preview Relic
	UFUNCTION()
	static  FRelicPreviewResult OnRelicPreview_KillCastHp( FAwRelicInfo TargetRelic ,TArray<FString>Params,AAwPlayerController* Controller );

	//[OnRelicPreview]
	//@Param[0] TargetRelic
	//@Param[1] PreviewFlag  Explain What is the hell  that  you want to Preview
	//@Param[2] CoreBuff
	//@Param[3]  Cast Power Like  1 =  0.0001 
	//@Param[4] Max
	//@Param[5] BuffStack Power like 1 Stack =  0.01
	//@Param[6] Prop Type
	// Check Special Buff Change  by preview Relic
	UFUNCTION()
	static  FRelicPreviewResult OnRelicPreview_RelicNumCastHp( FAwRelicInfo TargetRelic ,TArray<FString>Params,AAwPlayerController* Controller );

	//[OnRelicPreview]
	//@Param[0] TargetRelic
	//@Param[1] PreviewFlag  Explain What is the hell  that  you want to Preview
	//@Param[2] CoreBuff
	//@Param[3]  Cast Power Like  1 =  0.0001 
	//@Param[4] BuffStack Power like 1 Stack =  0.01
	//@Param[5] Prop Type
	// Check Special Buff Change  by preview Relic
	UFUNCTION()
	static  FRelicPreviewResult OnRelicPreview_GodTagCastHp( FAwRelicInfo TargetRelic ,TArray<FString>Params,AAwPlayerController* Controller );

	//[OnRelicPreview]
	//@Param[0] TargetRelic
	//@Param[1] PreviewFlag  Explain What is the hell  that  you want to Preview
	//@Param[2] CoreBuff
	//@Param[3]  Cast Power Like  1 =  0.0001
	//@Param[4] Max
	//@Param[5] BuffStack Power like 1 Stack =  0.01
	//@Param[6] Prop Type
	// Check Special Buff Change  by preview Relic
	UFUNCTION()
	static  FRelicPreviewResult OnRelicPreview_RelicNumCastCtRate( FAwRelicInfo TargetRelic ,TArray<FString>Params,AAwPlayerController* Controller );

	//[OnRelicPreview]
	//@Param[0] TargetRelic
	//@Param[1] PreviewFlag  Explain What is the hell  that  you want to Preview
	//@Param[2] CoreBuff
	//@Param[3]  Cast Power Like  1 =  0.0001 
	//@Param[4] BuffStack Power like 1 Stack =  0.01
	//@Param[5] Prop Type
	// Check Special Buff Change  by preview Relic
	UFUNCTION()
	static  FRelicPreviewResult OnRelicPreview_GodTagCastCtRate( FAwRelicInfo TargetRelic ,TArray<FString>Params,AAwPlayerController* Controller );

	//[OnRelicPreview]
	//@Param[0] TargetRelic
	//@Param[1] PreviewFlag  Explain What is the hell  that  you want to Preview
	//@Param[2] CoreBuff
	//@Param[3]  Cast Power Like  1 =  0.0001 
	//@Param[4] Max
	//@Param[5] BuffStack Power like 1 Stack =  0.01
	//@Param[6] Prop Type
	// Check Special Buff Change  by preview Relic
	UFUNCTION()
	static  FRelicPreviewResult OnRelicPreview_RelicNumCastCtDmg( FAwRelicInfo TargetRelic ,TArray<FString>Params,AAwPlayerController* Controller );

	//[OnRelicPreview]
	//@Param[0] TargetRelic
	//@Param[1] PreviewFlag  Explain What is the hell  that  you want to Preview
	//@Param[2] CoreBuff
	//@Param[3]  Cast Power Like  1 =  0.0001 
	//@Param[4] BuffStack Power like 1 Stack =  0.01
	//@Param[5] Prop Type
	// Check Special Buff Change  by preview Relic
	UFUNCTION()
	static  FRelicPreviewResult OnRelicPreview_GodTagCastCtDmg( FAwRelicInfo TargetRelic ,TArray<FString>Params,AAwPlayerController* Controller );

	//[OnRelicPreview]
	//@Param[0] TargetRelic
	//@Param[1] PreviewFlag  Explain What is the hell  that  you want to Preview
	//@Param[2] CoreBuff
	//@Param[3]  Cast Power Like  1 =  0.0001 
	//@Param[4] BuffStack Power like 1 Stack =  0.01
	//@Param[5] Prop Type
	// Check Special Buff Change  by preview Relic
	UFUNCTION()
	static  FRelicPreviewResult OnRelicPreview_ElementalBuffExtraDuration( FAwRelicInfo TargetRelic ,TArray<FString>Params,AAwPlayerController* Controller );
};
