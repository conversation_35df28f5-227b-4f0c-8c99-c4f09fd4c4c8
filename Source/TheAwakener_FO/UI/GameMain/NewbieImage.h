// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "Components/HorizontalBox.h"
#include "Components/TextBlock.h"
#include "Components/WidgetSwitcher.h"
#include "TheAwakener_FO/GameFramework/Input/GameControlState.h"
#include "UObject/Object.h"
#include "NewbieImage.generated.h"

/**
 * 新手引导图片的UI
 */
UCLASS()
class THEAWAKENER_FO_API UNewbieImage : public UUserWidget
{
	GENERATED_BODY()
private:
	virtual void NativeOnInitialized() override;
	virtual void NativeConstruct() override;
	virtual void NativeTick(const FGeometry& MyGeometry, float InDeltaTime) override;
	UPROPERTY()
	EGameControlState WasState;
public:
	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	bool IsPlayAnim;

	UPROPERTY(VisibleAnywhere,BlueprintReadOnly)
	UTextBlock* ConfirmTextBlock;
	
	UPROPERTY(VisibleAnywhere,BlueprintReadOnly)
	UHorizontalBox* ConfirmHorizontalBox;

	UPROPERTY(VisibleAnywhere,BlueprintReadOnly)
	UTextBlock* CancelTextBlock;
	
	UPROPERTY(VisibleAnywhere,BlueprintReadOnly)
	UHorizontalBox* CancelHorizontalBox;

	UPROPERTY(VisibleAnywhere,BlueprintReadOnly)
	UWidgetSwitcher* WidgetSwitcherMain;

	UPROPERTY(VisibleAnywhere,BlueprintReadOnly)
	TArray<FString> OpenedKeys;
	
	UFUNCTION(BlueprintCallable)
	void Show();
	UFUNCTION(BlueprintCallable)
	void Hide();

	UFUNCTION(BlueprintCallable)
	void ChangeConfirmText();

	UFUNCTION(BlueprintImplementableEvent)
	void PlayAnim();
	
};
