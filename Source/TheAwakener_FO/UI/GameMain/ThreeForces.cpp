// Fill out your copyright notice in the Description page of Project Settings.


#include "ThreeForces.h"

#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"

void UThreeForces::NativeOnInitialized()
{
	Super::NativeOnInitialized();

	GameState				= UGameplayFuncLib::GetAwGameState();
	GameInstance			= UGameplayFuncLib::GetAwGameInstance();

	HumanForces				= Cast<UForceOfProgress>(GetWidgetFromName("WBP_HumanForces"));
	RatManForces			= Cast<UForceOfProgress>(GetWidgetFromName("WBP_RatPeopleForces"));
	GoblinForces			= Cast<UForceOfProgress>(GetWidgetFromName("WBP_GoblinForces"));
}

void UThreeForces::NativeConstruct()
{
	Super::NativeConstruct();
	
	ProgressSizeX = HumanForces->ForcesOfProgress_Bar->GetWidgetStyle().FillImage.GetImageSize().X;

	HumanForces->SetRaceIcon("Human");
	RatManForces->SetRaceIcon("RatMan");
	
	
	if(GameInstance->RoleInfo.GetSwitch("UnlockMineGoblin"))
	{
		GoblinForces->SetRaceIcon("Goblin");
	}
	else
	{
		GoblinForces->SetRaceIcon("Unknown");
	}
	
}

void UThreeForces::NativeTick(const FGeometry& MyGeometry, float InDeltaTime)
{
	Super::NativeTick(MyGeometry, InDeltaTime);
}

void UThreeForces::ChangeForcesOfProgress(FString MapId)
{
	if(GameInstance)
	{
		for (int i = 0; i < GameInstance->RoleInfo.DungeonRecords.Num() ; ++i)
		{
			if(MapId == GameInstance->RoleInfo.DungeonRecords[i].DungeonId)
			{
				for (int k = 0; k < GameInstance->RoleInfo.DungeonRecords[i].Camps.Num(); ++k)
				{
					if(GameInstance->RoleInfo.DungeonRecords[i].Camps[k].CampId == "RatMan")
					{
						LastRatManForcesValue = GameInstance->RoleInfo.DungeonRecords[i].Camps[k].LastCampProgress;
						
						RatManForces->ForcesOfProgress_Bar->SetPercent(LastRatManForcesValue / 1000.0f);
						
						RatManForces->Shade_Image->SetRenderTranslation(FVector2D(
						LastRatManForcesValue / 1000.0f * ProgressSizeX,0.0f));

						RatManForcesValue = GameInstance->RoleInfo.DungeonRecords[i].Camps[k].CampProgress;
						RatManForcesPermillage = RatManForcesValue / 1000.0f;
					}
					else if(GameInstance->RoleInfo.DungeonRecords[i].Camps[k].CampId == "Goblin")
					{
						LastGoblinForcesValue = GameInstance->RoleInfo.DungeonRecords[i].Camps[k].LastCampProgress;
						
						GoblinForces->ForcesOfProgress_Bar->SetPercent(LastGoblinForcesValue / 1000.0f);

						GoblinForces->Shade_Image->SetRenderTranslation(FVector2D(
						LastGoblinForcesValue / 1000.0f * ProgressSizeX,0.0f));
						
						GoblinForcesValue = GameInstance->RoleInfo.DungeonRecords[i].Camps[k].CampProgress;
						GoblinForcesPermillage = GoblinForcesValue / 1000.0f;
					}
				}
				if(GameInstance->RoleInfo.GetSwitch("UnlockMineGoblin"))
				{
					HumanForces->ForcesOfProgress_Bar->SetPercent((2000.0f - LastRatManForcesValue - LastGoblinForcesValue) / 2000.0f);

					HumanForces->Shade_Image->SetRenderTranslation(FVector2D(
					(2000.0f - LastRatManForcesValue - LastGoblinForcesValue) / 2000.0f * ProgressSizeX,0.0f));
					
					HumanForcesPermillage = (2000.0f - RatManForcesValue - GoblinForcesValue) /  2000.0f;
				}
				else
				{
					HumanForces->ForcesOfProgress_Bar->SetPercent((1000.0f - LastRatManForcesValue - LastGoblinForcesValue) / 1000.0f);

					HumanForces->Shade_Image->SetRenderTranslation(FVector2D(
					(1000.0f - LastRatManForcesValue - LastGoblinForcesValue) / 1000.0f * ProgressSizeX,0.0f));
					
					HumanForcesPermillage = (1000.0f - RatManForcesValue) / 1000.0f;
				}
				for (int k = 0; k < GameInstance->RoleInfo.DungeonRecords[i].Camps.Num(); ++k)
				{
					if(GameInstance->RoleInfo.DungeonRecords[i].Camps[k].CampId == "RatMan")
					{
						GameInstance->RoleInfo.DungeonRecords[i].Camps[k].LastCampProgress =
							GameInstance->RoleInfo.DungeonRecords[i].Camps[k].CampProgress;
					}
					else if(GameInstance->RoleInfo.DungeonRecords[i].Camps[k].CampId == "Goblin")
					{
						GameInstance->RoleInfo.DungeonRecords[i].Camps[k].LastCampProgress =
							GameInstance->RoleInfo.DungeonRecords[i].Camps[k].CampProgress;
					}
				}
			}
		}
		/*for (FAwDungeonSave DungeonSave : GameInstance->RoleInfo.DungeonRecords)
		{
			if(MapId == DungeonSave.DungeonId)
			{
				for (FAwDungeonCampSave DungeonCampSave : DungeonSave.Camps)
				{
					if(DungeonCampSave.CampId == "RatMan")
					{
						LastRatManForcesValue = DungeonCampSave.LastCampProgress;
						RatManForces->ForcesOfProgress_Bar->SetPercent(LastRatManForcesValue);
						
						RatManForces->Shade_Image->SetRenderTranslation(FVector2D(
						LastRatManForcesValue /1000.0f * ProgressSizeX,0.0f));
						
						RatManForcesValue = DungeonCampSave.CampProgress;
						RatManForcesPermillage = RatManForcesValue / 1000.0f;
					}
					else if(DungeonCampSave.CampId == "Goblin")
					{
						LastGoblinForcesValue = DungeonCampSave.LastCampProgress;
						
						GoblinForces->ForcesOfProgress_Bar->SetPercent(LastGoblinForcesValue);

						GoblinForces->Shade_Image->SetRenderTranslation(FVector2D(
						LastGoblinForcesValue /1000.0f * ProgressSizeX,0.0f));
						
						GoblinForcesValue = DungeonCampSave.CampProgress;
						GoblinForcesPermillage = DungeonCampSave.CampProgress / 1000.0f;
					}
				}
				
				if(GameInstance->RoleInfo.GetSwitch("UnlockMineGoblin"))
				{
					HumanForces->ForcesOfProgress_Bar->SetPercent((2000.0f - LastRatManForcesValue - LastGoblinForcesValue) / 1000.0f);

					HumanForces->Shade_Image->SetRenderTranslation(FVector2D(
					(2000.0f - LastRatManForcesValue - LastGoblinForcesValue) / 1000.0f * ProgressSizeX,0.0f));
					
					HumanForcesPermillage = (2000.0f - RatManForcesValue - GoblinForcesValue) /  1000.0f;
				}
				else
				{
					HumanForces->ForcesOfProgress_Bar->SetPercent((1000.0f - LastRatManForcesValue - LastGoblinForcesValue) / 1000.0f);

					HumanForces->Shade_Image->SetRenderTranslation(FVector2D(
					(1000.0f - LastRatManForcesValue - LastGoblinForcesValue) / 1000.0f * ProgressSizeX,0.0f));
					
					HumanForcesPermillage = (1000.0f - RatManForcesValue) / 1000.0f;
				}

				for (FAwDungeonCampSave DungeonCampSave : DungeonSave.Camps)
				{
					if(DungeonCampSave.CampId == "RatMan")
					{
						DungeonCampSave.LastCampProgress = DungeonCampSave.CampProgress;
					}
					else if(DungeonCampSave.CampId == "Goblin")
					{
						DungeonCampSave.LastCampProgress = DungeonCampSave.CampProgress;
					}
				}
			}
		}
		*/
		
	}
	else
	{
		GameInstance = UGameplayFuncLib::GetAwGameInstance();
	}
}

void UThreeForces::UpdateProgress(float InDeltaTime)
{
	HumanForces->ForcesOfProgress_Bar->SetPercent(FMath::FInterpTo(
						HumanForces->ForcesOfProgress_Bar->GetPercent(),
						HumanForcesPermillage,InDeltaTime,ChangeForcesValueSpeed));
		
	HumanForces->Shade_Image->SetRenderTranslation(FMath::Vector2DInterpTo(
		HumanForces->Shade_Image->GetRenderTransform().Translation,
		FVector2D(HumanForcesPermillage * ProgressSizeX,0.0f),InDeltaTime,ChangeForcesValueSpeed));
					
	RatManForces->ForcesOfProgress_Bar->SetPercent(FMath::FInterpTo(
		RatManForces->ForcesOfProgress_Bar->GetPercent(),
		RatManForcesPermillage,InDeltaTime,ChangeForcesValueSpeed));
					
	RatManForces->Shade_Image->SetRenderTranslation(FMath::Vector2DInterpTo(
		RatManForces->Shade_Image->GetRenderTransform().Translation,
		FVector2D(RatManForcesPermillage * ProgressSizeX,0.0f),InDeltaTime,ChangeForcesValueSpeed));
					
	GoblinForces->ForcesOfProgress_Bar->SetPercent(FMath::FInterpTo(
		GoblinForces->ForcesOfProgress_Bar->GetPercent(),
		GoblinForcesPermillage,InDeltaTime,ChangeForcesValueSpeed));
					
	GoblinForces->Shade_Image->SetRenderTranslation(FMath::Vector2DInterpTo(
		GoblinForces->Shade_Image->GetRenderTransform().Translation,
		FVector2D(GoblinForcesPermillage * ProgressSizeX,0.0f),InDeltaTime,ChangeForcesValueSpeed));
}
