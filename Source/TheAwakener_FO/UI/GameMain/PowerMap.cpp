// Fill out your copyright notice in the Description page of Project Settings.


#include "PowerMap.h"

#include "TimerManager.h"
#include "Animation/WidgetAnimationPlayCallbackProxy.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"

void UPowerMap::NativeOnInitialized()
{
	Super::NativeOnInitialized();

	GameState				= UGameplayFuncLib::GetAwGameState();
	GameInstance			= UGameplayFuncLib::GetAwGameInstance();
	
	MapList					= Cast<UMenuList>(GetWidgetFromName("WBP_MenuList"));
	ThreeForces				= Cast<UThreeForces>(GetWidgetFromName("WBP_ThreeForces"));
		
	/*HumanForces			= Cast<UForceOfProgress>(GetWidgetFromName("WBP_HumanForces"));
	RatManForces			= Cast<UForceOfProgress>(GetWidgetFromName("WBP_RatPeopleForces"));
	GoblinForces			= Cast<UForceOfProgress>(GetWidgetFromName("WBP_GoblinForces"));*/
	
	GetWorld()->GetTimerManager().SetTimer(TimerHandle,this,&UPowerMap::SetGameUIControllState,0.3f,true);
}

void UPowerMap::NativeConstruct()
{
	Super::NativeConstruct();
	/*ProgressSizeX = HumanForces->ForcesOfProgress_Bar->WidgetStyle.FillImage.GetImageSize().X;

	HumanForces->SetRaceIcon("Human");
	RatManForces->SetRaceIcon("RatMan");
	GoblinForces->SetRaceIcon("Goblin");*/

	GetWorld()->GetTimerManager().SetTimer(TimerHandle1,this,&UPowerMap::InitList,0.1f,false);
	SetIsReversePlay(false);
	Start();
}

void UPowerMap::NativeTick(const FGeometry& MyGeometry, float InDeltaTime)
{
	Super::NativeTick(MyGeometry, InDeltaTime);

	if(GameState)
	{
		AAwCharacter* Me = GameState->GetMyCharacter();
		if(UGameplayFuncLib::GetPlayerControllerByWidget(this)->GameUIControlState == EGameUIControlState::SecondaryUIState)
		{
			if(Me->GetCmdComponent()->IsActionOccur("Menu_Up") || Me->GetCmdComponent()->IsActionOccur("Menu_Up",EAwInputState::Hold))
			{
				if(MapList->MenuList->GetNumItems())
				{
					MapList->GetSelectListEntry()->EntryShadeImage->SetVisibility(ESlateVisibility::Collapsed);
					MapList->ListSelectUp();
					MapList->GetSelectListEntry()->EntryShadeImage->SetVisibility(ESlateVisibility::Visible);
				}
			}
			else if(Me->GetCmdComponent()->IsActionOccur("Menu_Down") || Me->GetCmdComponent()->IsActionOccur("Menu_Down",EAwInputState::Hold))
			{
				if(MapList->MenuList->GetNumItems())
				{
					MapList->GetSelectListEntry()->EntryShadeImage->SetVisibility(ESlateVisibility::Collapsed);
					MapList->ListSelectDown();
					MapList->GetSelectListEntry()->EntryShadeImage->SetVisibility(ESlateVisibility::Visible);
				}
			}

			if(Me->GetCmdComponent()->IsActionOccur("Menu_Confirm",EAwInputState::Press,true))
			{
				if(MapList->GetSelectData())
				{
					
				}
			}
		
			if(Me->GetCmdComponent()->IsActionOccur("Menu_Refuse",EAwInputState::Press,true))
			{
				UGameplayFuncLib::GetPlayerControllerByWidget(this)->GameUIControlState = EGameUIControlState::Changing;
				SetIsReversePlay(true);
				SetIsSelfCloss(false);
				Back();
			}
		}
		UpdateAnimPlay(InDeltaTime);
	}
	
}

void UPowerMap::SetGameUIControllState()
{
	UGameplayFuncLib::GetPlayerControllerByWidget(this)->GameUIControlState = EGameUIControlState::SecondaryUIState;
	GetWorld()->GetTimerManager().ClearTimer(TimerHandle);
}

void UPowerMap::Start()
{
	PlayWidgetAnim("ShowPowerMap",0.0f,1,EUMGSequencePlayMode::Forward,1.0f,false);
	SetIsAnimPlay(true);
}

void UPowerMap::Back()
{
	PlayWidgetAnim("ShowPowerMap",0.0f,1,EUMGSequencePlayMode::Reverse,1.0f,false);
	SetIsAnimPlay(true);
}

void UPowerMap::ChangeForcesOfProgress()
{
	if(GameInstance)
	{
		for (FAwDungeonSave DungeonSave : GameInstance->RoleInfo.DungeonRecords)
		{
			if(MapList->GetSelectData()->Id == DungeonSave.DungeonId)
			{
				ThreeForces->ChangeForcesOfProgress(DungeonSave.DungeonId);
			}
		}
	}
	else
	{
		GameInstance = UGameplayFuncLib::GetAwGameInstance();
	}
}

void UPowerMap::InitList()
{
	if(IsValid(MapList->GetSelectListEntry()))
		MapList->GetSelectListEntry()->EntryShadeImage->SetVisibility(ESlateVisibility::Visible);
	for (int i = 0; i < MapList->MenuList->GetNumItems() ; ++i)
	{
		UMenuListEntry* TempEntry = MapList->MenuList->
		GetEntryWidgetFromItem<UMenuListEntry>(MapList->MenuList->GetItemAt(i));
		if(TempEntry)
		{
			TempEntry->PlayWidgetAnim("EffectRespiration",0.0f,0,EUMGSequencePlayMode::Forward,1.0f,false);
		}
	}
	
	GetWorld()->GetTimerManager().ClearTimer(TimerHandle1);
}

void UPowerMap::UpdateAnimPlay(float InDeltaTime)
{
	if(GetIsAnimPlay())
	{
		UWidgetAnimation* TemAnim = GetNameWidgetAnimation("ShowPowerMap");
		if(TemAnim)
		{
			if(!IsAnimationPlaying(TemAnim))
			{
				SetIsAnimPlay(false);

				if(GetIsReversePlay())
				{
					if(GetIsSelfCloss())
					{
						UGameplayFuncLib::GetPlayerControllerByWidget(this)->GameControlState = EGameControlState::Game;
						//UGameplayFuncLib::GetMyAwPlayerController()->GameUIControlState = EGameUIControlState::MainUIState;
					}
					else
					{
						UGameMenu* GameMenu = Cast<UGameMenu>(UGameplayFuncLib::GetUiManager()->Show("Menu",9999));
						FListItemElementInfo TemItemElementInfo = UGameplayFuncLib::GetAwDataManager()->GetListItemsById("Menu");
						for (int i = 0;i < TemItemElementInfo.Items.Num();++i)
						{
							TemItemElementInfo.Items[i].Name = UGameplayFuncLib::GetAwDataManager()->GetTextByKey(TemItemElementInfo.Items[i].Id);
						}
						GameMenu->GetMenuList()->SetEntry(TemItemElementInfo);
						GameMenu->SetListInitScroll(GetMainIndex());
						//UGameplayFuncLib::GetMyAwPlayerController()->GameUIControlState = EGameUIControlState::MainUIState;
					}
			
					UGameplayFuncLib::GetUiManager()->Hide("PowerMap");
				}
			}
		}
	}
	else
	{
		ThreeForces->UpdateProgress(InDeltaTime);
	}
	
}



