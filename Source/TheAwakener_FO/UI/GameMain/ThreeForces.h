// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "ForceOfProgress.h"
#include "TheAwakener_FO/UI/Base/BaseUI.h"
#include "ThreeForces.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UThreeForces : public UBaseUI
{
	GENERATED_BODY()

	UPROPERTY()
	AAwGameState* GameState;

	UPROPERTY()
	UAwGameInstance* GameInstance;

	UPROPERTY()
	float ProgressSizeX;

	UPROPERTY()
	float HumanForcesPermillage;

	UPROPERTY()
	float RatManForcesPermillage;

	UPROPERTY()
	float GoblinForcesPermillage;

	UPROPERTY()
	float HumanForcesValue;

	UPROPERTY()
	float RatManForcesValue;

	UPROPERTY()
	float GoblinForcesValue;
	
	UPROPERTY()
	float LastRatManForcesValue;

	UPROPERTY()
	float LastGoblinForcesValue;
	

	virtual void NativeOnInitialized() override;
	virtual void NativeConstruct() override;
	virtual void NativeTick(const FGeometry& MyGeometry, float InDeltaTime) override;



public:

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	float ChangeForcesValueSpeed;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UForceOfProgress* HumanForces;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UForceOfProgress* RatManForces;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UForceOfProgress* GoblinForces;



	UFUNCTION(BlueprintCallable)
	void ChangeForcesOfProgress(FString MapId);


	UFUNCTION(BlueprintCallable)
	void UpdateProgress(float InDeltaTime);
};
