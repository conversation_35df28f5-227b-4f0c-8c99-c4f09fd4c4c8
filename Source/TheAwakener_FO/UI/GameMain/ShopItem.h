// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "Components/HorizontalBox.h"
#include "Components/Image.h"
#include "Components/TextBlock.h"
#include "Components/VerticalBox.h"
#include "ShopItem.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UShopItem : public UUserWidget
{
	GENERATED_BODY()



	virtual void NativeOnInitialized() override;
	virtual void NativeTick(const FGeometry& MyGeometry, float InDeltaTime) override;
	
public:
	
	
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	UImage* Props_Icon;

	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	UTextBlock* PropsCount_Text;
	
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	UTextBlock* TitleName_Text;
	
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	UTextBlock* ItemContent_Text;

	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	FString ItemId;



	UFUNCTION(BlueprintCallable)
	void SetShopItemAllAttributes(UTexture2D* PropsIcon,int PropsCount,FString ItemContent,FString Id);

	UFUNCTION(BlueprintCallable)
	void SetShopItemAttributes(int PropsCount,FString TileName,FString ItemContent,FString Id,UTexture2D* PropsIcon);
};
