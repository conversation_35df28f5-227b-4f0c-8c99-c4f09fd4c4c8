// Fill out your copyright notice in the Description page of Project Settings.


#include "RouletteItem.h"

#include "Components/CanvasPanel.h"
#include "Components/CanvasPanelSlot.h"
#include "Components/OverlaySlot.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/FunctionLibrary/ResourceFuncLib.h"

void URouletteItem::NativeOnInitialized()
{
	Super::NativeOnInitialized();

	RouletteItem_GroundBase							= Cast<UCanvasPanel>(GetWidgetFromName("RouletteItemGroundBase"));
	RouletteItemName_GroundBase						= Cast<UOverlay>(GetWidgetFromName("RouletteItemNameGroundBase"));
	RouletteItemNameBox_BackGround					= Cast<UImage>(GetWidgetFromName("RouletteItemNameBox"));
	RouletteItemName_Text							= Cast<UTextBlock>(GetWidgetFromName("RouletteItemName"));
	RouletteItem_BackGround							= Cast<UImage>(GetWidgetFromName("RouletteItemBackGround"));
	RouletteItem_Icon								= Cast<UImage>(GetWidgetFromName("RouletteItemIcon"));
	RouletteItem_Ground								= Cast<UCanvasPanel>(GetWidgetFromName("RouletteItemGround"));
	RouletteItem_IconGround							= Cast<UCanvasPanel>(GetWidgetFromName("RouletteItemIconGround"));
	RouletteItem_DecorateGround						= Cast<UCanvasPanel>(GetWidgetFromName("RouletteItemDecorateGround"));
	RouletteItem_DecorateImage						= Cast<UImage>(GetWidgetFromName("RouletteItemDecorate"));
	RouletteItem_ButtonPlane						= Cast<UImage>(GetWidgetFromName("RouletteItemButtonPlane"));
	RouletteItem_ButtonIcon							= Cast<UImage>(GetWidgetFromName("RouletteItemButtonIcon"));
	RouletteItem_IconSelectBox						= Cast<UImage>(GetWidgetFromName("RouletteItemIconSelectBox"));
	RouletteItem_SelectDecorteTop					= Cast<UImage>(GetWidgetFromName("RouletteItemSelectDecorteTop"));
	RouletteItem_SelectDecorteDown					= Cast<UImage>(GetWidgetFromName("RouletteItemSelectDecorteDown"));
	RouletteItem_SelectedSign = Cast<UImage>(GetWidgetFromName("RouletteItem_SelectSign"));


	RouletteItem_DecorateImage->SetVisibility(ESlateVisibility::Collapsed);
	RouletteItem_ButtonPlane->SetVisibility(ESlateVisibility::Collapsed);
	RouletteItem_ButtonIcon->SetVisibility(ESlateVisibility::Collapsed);
	this->SetVisibility(ESlateVisibility::Collapsed);
}

void URouletteItem::NativeConstruct()
{
	Super::NativeConstruct();
	SelectBoxRotateSpeed = 0.2f;
	RouletteItem_SelectDecorteTop->SetVisibility(ESlateVisibility::Collapsed);
	RouletteItem_SelectDecorteDown->SetVisibility(ESlateVisibility::Collapsed);
	RouletteItem_IconSelectBox->SetVisibility(ESlateVisibility::Collapsed);

	PlayWidgetAnim("Select",0.0f,0,EUMGSequencePlayMode::PingPong,1.0f,false);
}

void URouletteItem::NativeTick(const FGeometry& MyGeometry, float InDeltaTime)
{
	Super::NativeTick(MyGeometry, InDeltaTime);
	RouletteItem_IconSelectBox->SetRenderTransformAngle(RouletteItem_IconSelectBox->GetRenderTransformAngle() + SelectBoxRotateSpeed);
}

void URouletteItem::SetRouletteItemIsEquipped(bool Equipped)
{
	if(this)
	{
		IsSelected = Equipped;
		UpdateView();
	}
}

void URouletteItem::SetRouletteItemAttributes(FRouletteItemInfo RouletteItemInfo)
{
	MyRouletteItemInfo = RouletteItemInfo;
	IsSelected = MyRouletteItemInfo.IsSelected;
	IsFocused = MyRouletteItemInfo.IsFocused;
	if(RouletteItem_Icon)
		RouletteItem_Icon->SetBrushFromTexture(LoadObject<UTexture2D>(nullptr,*UResourceFuncLib::GetAssetPath(RouletteItemInfo.IconPath)));
	
	if(RouletteItemName_Text)
		RouletteItemName_Text->SetText(FText::FromString(RouletteItemInfo.Name));

	if(RouletteItem_ButtonIcon)
		RouletteItem_ButtonIcon->SetBrushFromTexture(
			LoadObject<UTexture2D>(
				nullptr,
				*UResourceFuncLib::GetAssetPath(
					UGameplayFuncLib::GetAwDataManager()->GetKeyMappingById(
						*RouletteItemInfo.ButtonIconPath	//*RouletteItemInfo.ActionSelectionUIInfo.CmdIcon
					).Gamepad_Icon
				)
			)
		);
}

void URouletteItem::SetRouletteItemNameTextDir(EItemPopupDir PopupDir)
{

	switch (PopupDir)
	{
	case EItemPopupDir::Left:
		{
			GetRouletteItemGroundSlot()->SetPosition(FVector2D(0.0f,GetRouletteItemGroundSlot()->GetPosition().Y));
			GetRouletteItemGroundSlot()->SetAlignment(FVector2D(1.0f,0.5f));
			GetRouletteItemNameGroundBaseSlot()->SetPosition(FVector2D(200.0f,GetRouletteItemNameGroundBaseSlot()->GetPosition().Y));
			RouletteItemNameBox_BackGround->SetRenderScale(FVector2D(1.0f,1.0f));
			GetRouletteItemNameTextSlot()->SetPadding(FMargin(0,0,10.0f,0));
			
			break;
		}
	case EItemPopupDir::Right:
		{
			GetRouletteItemGroundSlot()->SetPosition(FVector2D(0.0f,GetRouletteItemGroundSlot()->GetPosition().Y));
			GetRouletteItemGroundSlot()->SetAlignment(FVector2D(0.0f,0.5f));
			GetRouletteItemNameGroundBaseSlot()->SetPosition(FVector2D(-200.0f,GetRouletteItemNameGroundBaseSlot()->GetPosition().Y));
			RouletteItemNameBox_BackGround->SetRenderScale(FVector2D(-1.0f,1.0f));
			GetRouletteItemNameTextSlot()->SetPadding(FMargin(10.0f,0,0,0));
			
			break;
		}
	case EItemPopupDir::PopupNone:
		{
			break;
		}
	}
}

void URouletteItem::SetItemSelectVisibility(ESlateVisibility SlateVisibility)
{
	RouletteItem_SelectDecorteTop->SetVisibility(SlateVisibility);
	RouletteItem_SelectDecorteDown->SetVisibility(SlateVisibility);
	RouletteItem_IconSelectBox->SetVisibility(SlateVisibility);
}

void URouletteItem::UpdateView()
{
	RouletteItem_DecorateGround->SetVisibility(IsFocused && MyRouletteItemInfo.DecorateVisible ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
	RouletteItem_SelectDecorteTop->SetVisibility(IsFocused ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
	RouletteItem_SelectDecorteDown->SetVisibility(IsFocused ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
	RouletteItem_IconSelectBox->SetVisibility(IsFocused ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
	RouletteItem_ButtonPlane->SetVisibility(IsFocused ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
	RouletteItem_ButtonIcon->SetVisibility(IsFocused ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
	RouletteItem_DecorateImage->SetVisibility(MyRouletteItemInfo.DecorateVisible ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
	

	if(IsSelected)
	{
		RouletteItem_SelectedSign->SetVisibility(ESlateVisibility::SelfHitTestInvisible);
		RouletteItem_BackGround->SetColorAndOpacity(FColor::Red);
	}
	else
	{
		RouletteItem_SelectedSign->SetVisibility(ESlateVisibility::Hidden);
		RouletteItem_BackGround->SetColorAndOpacity(FColor::White);
	}
	
	// switch (ItemState)
	// {
	// case EItemState::Select:
	// 	{
	// 		RouletteItem_SelectDecorteTop->SetVisibility(ESlateVisibility::Visible);
	// 		RouletteItem_SelectDecorteDown->SetVisibility(ESlateVisibility::Visible);
	// 		RouletteItem_IconSelectBox->SetVisibility(ESlateVisibility::Visible);
	// 		RouletteItem_ButtonPlane->SetVisibility(ESlateVisibility::Visible);
	// 		RouletteItem_ButtonIcon->SetVisibility(ESlateVisibility::Visible);
	// 		if(MyRouletteItemInfo.ActionSelectionUIInfo.InAir)
	// 			RouletteItem_DecorateImage->SetVisibility(ESlateVisibility::Visible);
	// 		break;
	// 	}
	// case EItemState::NotSelect:
	// 	{
	// 		RouletteItem_SelectDecorteTop->SetVisibility(ESlateVisibility::Collapsed);
	// 		RouletteItem_SelectDecorteDown->SetVisibility(ESlateVisibility::Collapsed);
	// 		RouletteItem_IconSelectBox->SetVisibility(ESlateVisibility::Collapsed);
	// 		RouletteItem_ButtonPlane->SetVisibility(ESlateVisibility::Collapsed);
	// 		RouletteItem_ButtonIcon->SetVisibility(ESlateVisibility::Collapsed);
	// 		RouletteItem_DecorateImage->SetVisibility(ESlateVisibility::Collapsed);
	// 		
	// 		break;
	// 	}
	// }
}

// void URouletteItem::UpdateSelected()
// {
// 	if(IsSelected)
// 	{
// 		RouletteItem_SelectedSign->SetVisibility(ESlateVisibility::SelfHitTestInvisible);
// 		RouletteItem_BackGround->SetColorAndOpacity(FColor::Red);
// 	}
// 	else
// 	{
// 		RouletteItem_SelectedSign->SetVisibility(ESlateVisibility::Hidden);
// 		RouletteItem_BackGround->SetColorAndOpacity(FColor::White);
// 	}
// }


