// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "Blueprint/WidgetLayoutLibrary.h"
#include "Components/CanvasPanel.h"
#include "Components/Image.h"
#include "Components/Overlay.h"
#include "Components/TextBlock.h"
#include "TheAwakener_FO/UI/Base/BaseUI.h"
#include "RouletteItem.generated.h"

/**
 * 
 */

//详情框弹出方向  菜单UI设置向左弹,向右弹
UENUM()
enum class EItemPopupDir : uint8
{
	PopupNone,
	Left,
	Right
};

//这是一个不应该有的状态，正确状态应该是：选没选中（selected）和是否被聚焦（focused）
// UENUM()
// enum class EItemState : uint8
// {
// 	Select,
// 	NotSelect
// };


USTRUCT(BlueprintType)
struct FRouletteItemInfo
{
	GENERATED_BODY()

	//这三个不该有的东西，因为不具备通用性，这个转盘未必只服务于动作
	// UPROPERTY()
	// FActionSelection ActionSelection;
	// UPROPERTY()
	// FActionSelectionUIInfo ActionSelectionUIInfo;
	// UPROPERTY()
	// FString ActionId =  "";
	
	UPROPERTY()
	bool IsShowItemDecorateGround = true;

	UPROPERTY()
	bool IsSelected = false;

	UPROPERTY()
	bool IsFocused = false;

	/**
	 * 留一个InfoKey给外部，作为链接信号，相当于很多表的id的作用
	 * 使用这个转盘的对象，可以从选中的东西的InfoKey在自己的逻辑中去查询得出需要的信息
	 */ 
	UPROPERTY()
	FString InfoKey;
	
	UPROPERTY()
	FString IconPath = "";					//物品图标

	UPROPERTY()
	FString ButtonIconPath = "";					//物品图标

	UPROPERTY()
	FString Name;							//物品名或者是对话内容

	UPROPERTY()
	FString Event;							//按键的事件，储存在这里便于拉取

	UPROPERTY()
	bool DecorateVisible = false;	//小翅膀是否可见

	FRouletteItemInfo(){}
	FRouletteItemInfo(FString KeyOfInfo, FString PathOfIcon, FString ShowingName, FString EventFunc, FString IconOfButton = ""):
		InfoKey(KeyOfInfo), IconPath(PathOfIcon), ButtonIconPath(IconOfButton), Name(ShowingName), Event(EventFunc){}
};


UCLASS()
class THEAWAKENER_FO_API URouletteItem : public UBaseUI
{
	GENERATED_BODY()
private:	
	// UPROPERTY()
	// EItemState ItemState;

	UPROPERTY()
	FRouletteItemInfo MyRouletteItemInfo;

	float SelectBoxRotateSpeed;

	virtual void NativeOnInitialized() override;
	virtual void NativeConstruct() override;
	virtual void NativeTick(const FGeometry& MyGeometry, float InDeltaTime) override;

	//下面这些控件，都是私密的，外部要控制的话，只能提供接口和控制方法
	//举例来说，外面要求：把这个面包吃下去，那么外人不能帮我张嘴和咀嚼，最多把面包递给我，那些事儿得我自己来
	
	//选中状态的标
	UPROPERTY()
	UImage* RouletteItem_SelectedSign;
	
	UPROPERTY()
	UCanvasPanel* RouletteItem_GroundBase;

	UPROPERTY()
	UCanvasPanel* RouletteItem_Ground;

	UPROPERTY()
	UImage* RouletteItemNameBox_BackGround;

	UPROPERTY()
	UTextBlock* RouletteItemName_Text;

	UPROPERTY()
	UCanvasPanel* RouletteItem_IconGround;

	UPROPERTY()
	UImage* RouletteItem_BackGround;

	UPROPERTY()
	UImage* RouletteItem_Icon;

	UPROPERTY()
	UCanvasPanel* RouletteItem_DecorateGround;

	UPROPERTY()
	UImage* RouletteItem_DecorateImage;

	UPROPERTY()
	UImage* RouletteItem_ButtonPlane;

	UPROPERTY()
	UImage* RouletteItem_ButtonIcon;

	UPROPERTY()
	UImage* RouletteItem_IconSelectBox;
	
	UPROPERTY()
	UImage* RouletteItem_SelectDecorteTop;

	UPROPERTY()
	UImage* RouletteItem_SelectDecorteDown;

public:
	//是否被聚焦
	UPROPERTY()
	bool IsFocused = false;
	
	UPROPERTY()
	bool IsHide = false;

	//是否是选中的，不管是装备中的还是其他什么的，都算是选中的
	UPROPERTY()
	bool IsSelected = false;

	UPROPERTY()
	UOverlay* RouletteItemName_GroundBase;
	
	UFUNCTION(BlueprintCallable)
	FRouletteItemInfo GetRouletteItemInfo(){return MyRouletteItemInfo;}

	UFUNCTION()
	void SetRouletteItemIsEquipped(bool Equipped);

	UFUNCTION(BlueprintCallable)
	void SetRouletteItemAttributes(FRouletteItemInfo RouletteItemInfo);

	UFUNCTION(BlueprintCallable)
	void SetRouletteItemNameTextDir(EItemPopupDir PopupDir);
	
	UFUNCTION(BlueprintCallable)
	UCanvasPanelSlot* GetRouletteItemGroundSlot(){return UWidgetLayoutLibrary::SlotAsCanvasSlot(RouletteItem_Ground);}

	UFUNCTION(BlueprintCallable)
	UCanvasPanelSlot* GetRouletteItemNameGroundBaseSlot(){return UWidgetLayoutLibrary::SlotAsCanvasSlot(RouletteItemName_GroundBase);}

	UFUNCTION(BlueprintCallable)
	UOverlaySlot* GetRouletteItemNameTextSlot(){return UWidgetLayoutLibrary::SlotAsOverlaySlot(RouletteItemName_Text);}

	UFUNCTION(BlueprintCallable)
	void SetItemFocused(bool BeFocused){this->IsFocused = BeFocused;}
	

	UFUNCTION(BlueprintCallable)
	void SetItemSelectVisibility(ESlateVisibility SlateVisibility);
	UFUNCTION(BlueprintCallable)
	void UpdateView();

	//不必要的优化，每个状态变化的时候应该全部重绘，反正渲染脏区域几乎都覆盖的
	// UFUNCTION(BlueprintCallable)
	// void UpdateSelected();
};
