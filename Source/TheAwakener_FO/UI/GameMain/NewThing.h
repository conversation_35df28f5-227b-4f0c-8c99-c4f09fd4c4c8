// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/IUserObjectListEntry.h"
#include "Blueprint/UserWidget.h"
#include "Components/Image.h"
#include "Components/TextBlock.h"
#include "TheAwakener_FO/GamePlay/Thing/ThingObj.h"
#include "NewThing.generated.h"

/**
 * 
 */
UENUM(BlueprintType)
enum class ENewThingState : uint8
{
	Enter,
	Stay,
	Leave
};
UCLASS()
class THEAWAKENER_FO_API UNewThing : public UUserWidget, public IUserObjectListEntry
{
	GENERATED_BODY()
	virtual void NativeOnInitialized() override;

	FTimerHandle TimerHandlel;
public:
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	UImage* ThingIconComp;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	UTextBlock* ThingNameComp;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	UTextBlock* ThingCountComp;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	ENewThingState NewThingState;
	

	UFUNCTION(BlueprintCallable)
	void Set(FThingObj Thing);

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float TimeElapsed = 0;

	/*UFUNCTION(BlueprintImplementableEvent)
	void SelfDestroy();*/
};
