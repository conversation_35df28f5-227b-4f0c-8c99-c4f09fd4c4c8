// Fill out your copyright notice in the Description page of Project Settings.


#include "NewbieRetrospect.h"

#include "TimerManager.h"
#include "Kismet/BlueprintMapLibrary.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"

void UNewbieRetrospect::NativeOnInitialized()
{
	Super::NativeOnInitialized();

	GameState				= UGameplayFuncLib::GetAwGameState();
	GameInstance			= UGameplayFuncLib::GetAwGameInstance();

	NewbieCanvasPanel = Cast<UCanvasPanel>(GetWidgetFromName("NewbiePanel"));
	NewbieMenuList = Cast<UMenuList>(GetWidgetFromName("WBP_MenuList"));

	GetWorld()->GetTimerManager().SetTimer(TimerHandle,this,&UNewbieRetrospect::SetGameUIControllState,0.3f,false);
}

void UNewbieRetrospect::NativeConstruct()
{
	Super::NativeConstruct();
	this->SetRenderOpacity(0);
	GetWorld()->GetTimerManager().SetTimer(TimerHandle1,this,&UNewbieRetrospect::InitNewbieList,0.1f,false);
	SetIsReversePlay(false);
	
}

void UNewbieRetrospect::NativeTick(const FGeometry& MyGeometry, float InDeltaTime)
{
	Super::NativeTick(MyGeometry, InDeltaTime);
	OperationInput();
	UpdateAnimPlay(InDeltaTime);
}

void UNewbieRetrospect::NativeDestruct()
{
	Super::NativeDestruct();

	TArray<UNewbieImage*>TempArray;
	
	for (auto Temp = KnownNewbies.CreateConstIterator();Temp;++Temp)
	{
		Temp->Value->RemoveFromParent();
	}

	KnownNewbies.Empty();
}


void UNewbieRetrospect::InitNewbieList()
{
	if(IsValid(NewbieMenuList) && NewbieMenuList->MenuList->GetListItems().Num() > 0)
	{
		for (int i = 0; i < NewbieMenuList->MenuList->GetListItems().Num() ; ++i)
		{
			const UObject* TempObject = NewbieMenuList->MenuList->GetItemAt(i);
			if(!IsValid(TempObject))
				return;
			const UMenuListEntryData* TempData = Cast<UMenuListEntryData>(TempObject);
			if(!IsValid(TempData))
				return;
			UNewbieImage* Image = Cast<UNewbieImage>(UGameplayFuncLib::GetUiManager()->ShowNewUI(TempData->ItemId));
			
			if(!IsValid(Image))
				return;
			
			Image->IsPlayAnim = false;
			KnownNewbies.Add(TempData->ItemId,Image);
			
			UMenuListEntry* TempEntry = NewbieMenuList->MenuList->GetEntryWidgetFromItem<UMenuListEntry>(TempObject);
			if(IsValid(TempEntry))
			{
				TempEntry->PlayWidgetAnim("EffectRespiration",0.0f,0,EUMGSequencePlayMode::Forward,1.0f,false);
			}
			if(i == 0)
			{
				Image->SetVisibility(ESlateVisibility::SelfHitTestInvisible);
			}
			else
			{
				Image->SetVisibility(ESlateVisibility::Collapsed);
			}
			NewbieCanvasPanel->AddChild(Image);
			UWidgetLayoutLibrary::SlotAsCanvasSlot(Image)->SetAnchors(FAnchors(0.5f,0.5f));
			UWidgetLayoutLibrary::SlotAsCanvasSlot(Image)->SetAlignment(FVector2D(0.5f,0.5f));
		}
	}
	GetWorld()->GetTimerManager().ClearTimer(TimerHandle1);

	this->SetRenderOpacity(1);
	Start();
}

void UNewbieRetrospect::OperationInput()
{
	if(GameState)
	{
		AAwCharacter* Me = GameState->GetMyCharacter();
		if(UGameplayFuncLib::GetPlayerControllerByWidget(this)->GameUIControlState == EGameUIControlState::SecondaryUIState)
		{
			if(Me->GetCmdComponent()->IsActionOccur("Menu_Up") || Me->GetCmdComponent()->IsActionOccur("Menu_Up",EAwInputState::Hold))
			{
				if(IsValid(NewbieMenuList) && NewbieMenuList->MenuList->GetNumItems())
				{
					if(NewbieMenuList->GetSelectListEntry())
						NewbieMenuList->GetSelectListEntry()->EntryShadeImage->SetVisibility(ESlateVisibility::Collapsed);
					if(IsValid(KnownNewbies.FindRef(NewbieMenuList->GetSelectData()->ItemId)))
						KnownNewbies.FindRef(NewbieMenuList->GetSelectData()->ItemId)->SetVisibility(ESlateVisibility::Collapsed);
					NewbieMenuList->ListSelectUp();
					UMenuListEntry* TempEntry = NewbieMenuList->GetSelectListEntry();
					if(TempEntry)
						TempEntry->EntryShadeImage->SetVisibility(ESlateVisibility::Visible);
					if(IsValid(KnownNewbies.FindRef(NewbieMenuList->GetSelectData()->ItemId)))
						KnownNewbies.FindRef(NewbieMenuList->GetSelectData()->ItemId)->SetVisibility(ESlateVisibility::SelfHitTestInvisible);
				}
			}
			else if(Me->GetCmdComponent()->IsActionOccur("Menu_Down") || Me->GetCmdComponent()->IsActionOccur("Menu_Down",EAwInputState::Hold))
			{
				if(IsValid(NewbieMenuList) && NewbieMenuList->MenuList->GetNumItems())
				{
					if(NewbieMenuList->GetSelectListEntry())
						NewbieMenuList->GetSelectListEntry()->EntryShadeImage->SetVisibility(ESlateVisibility::Collapsed);
					if(IsValid(KnownNewbies.FindRef(NewbieMenuList->GetSelectData()->ItemId)))
						KnownNewbies.FindRef(NewbieMenuList->GetSelectData()->ItemId)->SetVisibility(ESlateVisibility::Collapsed);
					NewbieMenuList->ListSelectDown();
					UMenuListEntry* TempEntry = NewbieMenuList->GetSelectListEntry();
					if(TempEntry)
						TempEntry->EntryShadeImage->SetVisibility(ESlateVisibility::Visible);
					if(IsValid(KnownNewbies.FindRef(NewbieMenuList->GetSelectData()->ItemId)))
						KnownNewbies.FindRef(NewbieMenuList->GetSelectData()->ItemId)->SetVisibility(ESlateVisibility::SelfHitTestInvisible);
				}
			}

			if(Me->GetCmdComponent()->IsActionOccur("Menu_Confirm",EAwInputState::Press,true))
			{
				if(NewbieMenuList->GetSelectData())
				{
					
				}
			}
		
			if(Me->GetCmdComponent()->IsActionOccur("Menu_Refuse",EAwInputState::Press,true))
			{
				UGameplayFuncLib::PlayUIAudio("ConfirmKey_Back");
				UGameplayFuncLib::GetPlayerControllerByWidget(this)->GameUIControlState = EGameUIControlState::Changing;
				SetIsReversePlay(true);
				SetIsSelfCloss(false);
				Back();
			}
		}
	}
	
}

void UNewbieRetrospect::Start()
{
	PlayWidgetAnim("ShowNewbieRetrospect",0.0f,1,EUMGSequencePlayMode::Forward,1.0f,false);
	SetIsAnimPlay(true);
}

void UNewbieRetrospect::Back()
{
	PlayWidgetAnim("ShowNewbieRetrospect",0.0f,1,EUMGSequencePlayMode::Reverse,1.0f,false);
	SetIsAnimPlay(true);
}

void UNewbieRetrospect::SetGameUIControllState()
{
	UGameplayFuncLib::GetPlayerControllerByWidget(this)->GameUIControlState = EGameUIControlState::SecondaryUIState;;
	GetWorld()->GetTimerManager().ClearTimer(TimerHandle);
}

void UNewbieRetrospect::UpdateAnimPlay(float InDeltaTime)
{
	if(GetIsAnimPlay())
	{
		UWidgetAnimation* TemAnim = GetNameWidgetAnimation("ShowNewbieRetrospect");
		if(TemAnim)
		{
			if(!IsAnimationPlaying(TemAnim))
			{
				SetIsAnimPlay(false);

				if(GetIsReversePlay())
				{
					if(GetIsSelfCloss())
					{
						UGameplayFuncLib::GetPlayerControllerByWidget(this)->GameControlState = EGameControlState::Game;
						//UGameplayFuncLib::GetMyAwPlayerController()->GameUIControlState = EGameUIControlState::MainUIState;
					}
					else
					{
						UGameMenu* GameMenu = Cast<UGameMenu>(UGameplayFuncLib::GetUiManager()->Show("Menu",9999));
						FListItemElementInfo TemItemElementInfo = UGameplayFuncLib::GetAwDataManager()->GetListItemsById("Menu");
						for (int i = 0;i < TemItemElementInfo.Items.Num();++i)
						{
							TemItemElementInfo.Items[i].Name = UGameplayFuncLib::GetAwDataManager()->GetTextByKey(TemItemElementInfo.Items[i].Id);
						}
						GameMenu->GetMenuList()->SetEntry(TemItemElementInfo);
						GameMenu->SetListInitScroll(GetMainIndex());
						//UGameplayFuncLib::GetMyAwPlayerController()->GameUIControlState = EGameUIControlState::MainUIState;
					}
			
					UGameplayFuncLib::GetUiManager()->Hide("NewbieRetrospect");
				}
			}
		}
	}
}

