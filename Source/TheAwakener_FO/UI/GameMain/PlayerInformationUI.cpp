// Fill out your copyright notice in the Description page of Project Settings.


#include "PlayerInformationUI.h"

#include "Blueprint/WidgetLayoutLibrary.h"
#include "Engine/Texture2D.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"

void UPlayerInformationUI::NativeOnInitialized()
{
	Super::NativeOnInitialized();
	
	PlayerInfoGroundImage					=	Cast<UImage>(GetWidgetFromName("PlayerInfoGround"));
	PlayerNameTextBlock						=	Cast<UTextBlock>(GetWidgetFromName("PlayerName"));
	Exp_GroundCanvas						=	Cast<UCanvasPanel>(GetWidgetFromName("Exp_GroundBase"));
	Exp_GroundOverlay						=	Cast<UOverlay>(GetWidgetFromName("Exp_Ground"));
	Exp_SlotImage							=	Cast<UImage>(GetWidgetFromName("Exp_Slot"));
	Exp_BarProgressBar						=	Cast<UProgressBar>(GetWidgetFromName("Exp_Bar"));
	Exp_SectionImage						=	Cast<UImage>(GetWidgetFromName("Exp_Section"));
	PlayerLevelTextBlock					=	Cast<UTextBlock>(GetWidgetFromName("PlayerLevel"));
	Exp_SlotDecorationImage					=	Cast<UImage>(GetWidgetFromName("Exp_SlotDecoration"));
	ProfessionGroundOverlay					=	Cast<UOverlay>(GetWidgetFromName("ProfessionGround"));
	ProfessionPlateImage					=	Cast<UImage>(GetWidgetFromName("ProfessionPlate"));
	ProfessionIconImage						=	Cast<UImage>(GetWidgetFromName("ProfessionIcon"));


	Exp_BarProgressBar->SetPercent(0.0f);
	SectionImageYMaxValue = 115.0f;
	Test = 0.0f;
}

void UPlayerInformationUI::NativeConstruct()
{
	Super::NativeConstruct();

	UE_LOG(LogTemp,Log,TEXT("MyCharacter Cur EXP : %d        ---这段代码在 PlayerInformationUI.cpp 36行---"),
		UGameplayFuncLib::GetAwGameState()->GetMyCharacter()->CharacterObj.Exp);

	PlayerNameTextBlock->SetText(FText::FromString(UGameplayFuncLib::GetAwGameState()->
		GetMyCharacter()->CharacterObj.Name));
	PlayerLevelTextBlock->SetText(FText::FromString(FString::FromInt(UGameplayFuncLib::GetAwGameState()->
		GetMyCharacter()->CharacterObj.CharacterLevel)));
	UTexture2D* Icon = LoadObject<UTexture2D>(nullptr,
		*UResourceFuncLib::GetAssetPath(UGameplayFuncLib::GetDataManager()->
			GetClassIconById(*UGameplayFuncLib::GetAwGameState()->
				GetMyCharacter()->PlayerClassId).IconPath));
	if (Icon)
	{
		ProfessionIconImage->SetBrushFromTexture(Icon);
		ProfessionIconImage->SetDesiredSizeOverride(FVector2D(Icon->GetSizeX() / 2,Icon->GetSizeY() / 2));
	}
	

	//暂时没有当前升级的最大经验值 先用 1000.0f 为所需经验值
	
	const float Temp = UGameplayFuncLib::GetAwGameState()->GetMyCharacter()->CharacterObj.Exp * 1000.0f;
	
	//暂时没有当前升级的最大经验值 先用 1000.0f 为所需经验值
	
	Exp_BarProgressBar->SetPercent(Temp);
	UpdateExpBar();
}

void UPlayerInformationUI::NativeTick(const FGeometry& MyGeometry, float InDeltaTime)
{
	Super::NativeTick(MyGeometry, InDeltaTime);


	//测试代码,可删
	/*if(FMath::IsNearlyEqual(Test,1.0f,0.0001f))
	{
		Test = 0.0f;
	}
	else
	{
		Test += 0.001f;
	}
	Exp_BarProgressBar->SetPercent(Test);
	UpdateExpBar();*/
	
	//测试代码,可删
}

void UPlayerInformationUI::UpdateExpBar() const 
{
	UWidgetLayoutLibrary::SlotAsCanvasSlot(Exp_SectionImage)->SetPosition(FVector2D(0.0f,SectionImageYMaxValue - Exp_BarProgressBar->GetPercent() * SectionImageYMaxValue));
}
