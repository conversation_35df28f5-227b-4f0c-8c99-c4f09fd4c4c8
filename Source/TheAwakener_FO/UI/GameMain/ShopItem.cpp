// Fill out your copyright notice in the Description page of Project Settings.


#include "ShopItem.h"

#include "Engine/Texture2D.h"


void UShopItem::NativeOnInitialized()
{
	Super::NativeOnInitialized();
	
	Props_Icon = Cast<UImage>(GetWidgetFromName(TEXT("PropsIcon")));
	PropsCount_Text = Cast<UTextBlock>(GetWidgetFromName(TEXT("PropsCount")));
	TitleName_Text = Cast<UTextBlock>(GetWidgetFromName(TEXT("TitleName")));
	ItemContent_Text = Cast<UTextBlock>(GetWidgetFromName(TEXT("ItemContent")));

	PropsCount_Text->SetVisibility(ESlateVisibility::Collapsed);
}

void UShopItem::NativeTick(const FGeometry& MyGeometry, float InDeltaTime)
{
	Super::NativeTick(MyGeometry, InDeltaTime);
	
}

void UShopItem::SetShopItemAllAttributes(UTexture2D* PropsIcon,int PropsCount,FString ItemContent,FString Id)
{
	if(this)
	{
		if(Props_Icon)
			Props_Icon->SetBrushFromTexture(PropsIcon);
		if(PropsCount_Text)
		{
			const FString Temp = TEXT("X") + FString::FromInt(PropsCount);
			PropsCount_Text->SetText(FText::FromString(Temp));
			
		}
		if(ItemContent_Text)
			ItemContent_Text->SetText(FText::FromString(ItemContent));
		ItemId = Id;
			
	}
	
}

void UShopItem::SetShopItemAttributes(int PropsCount, FString TileName, FString ItemContent,FString Id,UTexture2D* PropsIcon)
{
	if(IsValid(this))
	{
		if(PropsCount_Text)
		{
			const FString Temp = TEXT("X ") + FString::FromInt(PropsCount);
			
			PropsCount_Text->SetText(FText::FromString(Temp));
			
		}
		
		if(TitleName_Text)
			TitleName_Text->SetText(FText::FromString(TileName));

		if(ItemContent_Text)
			ItemContent_Text->SetText(FText::FromString(ItemContent));
		if(Props_Icon)
		{
			if(PropsIcon)
			{
				Props_Icon->SetBrushFromTexture(PropsIcon);
				Props_Icon->SetDesiredSizeOverride(FVector2D(PropsIcon->GetSizeX(),PropsIcon->GetSizeY()));
			}
		}
		ItemId = Id;
	}
	
}
