// Fill out your copyright notice in the Description page of Project Settings.


#include "Roulette.h"
#include "RouletteItem.h"

#include "Blueprint/WidgetLayoutLibrary.h"
#include "Components/CanvasPanelSlot.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"

void URoulette::NativeOnInitialized()
{
	Super::NativeOnInitialized();
	
	Roulette_Base			= Cast<UCanvasPanel>(GetWidgetFromName("RouletteBase"));
	Roulette_Image			= Cast<UImage>(GetWidgetFromName("Roulette"));

	CurFocusIndex		= 0;
	RotateDir				= ERotateDir::None;
	RouletteState			= ERouletteState::Select;
	RouletteRotationAngle	= 0.0f;
	RotateSpeed				= 20.0f;
	RotateAcceleration		= 0.5f;
	ItemScaleSpeed			= 20.0f;
	CurSelectPointIndex		= 3;
	LastSelectPointIndex	= 0;

	TweenFunc.ClassPath		= "UIList";
	TweenFunc.FunctionName	= "CalculateRouletteContentPoint";
	
	
}

void URoulette::NativeTick(const FGeometry& MyGeometry, float InDeltaTime)
{
	Super::NativeTick(MyGeometry, InDeltaTime);
	
	RouletteOperation(RouletteRotationAngle,InDeltaTime);
	
}

void URoulette::SetRoulette(FRouletteInfo RouletteInfo, TArray<FRouletteItemInfo> RouletteItemInfo)
{
	if(RouletteInfo.RouletteImage)
	{
		Roulette_Image->SetBrushFromTexture(RouletteInfo.RouletteImage);
		Roulette_Image->SetVisibility(ESlateVisibility::Visible);
	}
	else
	{
		Roulette_Image->SetVisibility(ESlateVisibility::Collapsed);
	}
	
	RouletteItemArray.Empty();
	RouletteItemInfos.Empty();
	
	MyRouletteInfo			= RouletteInfo;
	for (auto Temp : RouletteItemInfo)
	{
		RouletteItemInfos.Add(Temp);
	}

	RouletteInfo.PointCount = FMath::Clamp(RouletteInfo.PointCount,1,7);
	
	if(RouletteItemInfos.Num() > 0)
	{
		CreateCount = RouletteItemInfos.Num();

		if(CreateCount < 5)
		{
			MyRouletteInfo.RouletteStyle = ERouletteStyle::RouletteLimited;
		}

		UFunction* Func = UCallFuncLib::GetUFunction(TweenFunc.ClassPath, TweenFunc.FunctionName);
		if (Func)
		{
			struct
			{
				int Count;
				float Angle;
				float CompensationValue;
				float RouletteDiameter;

				TArray<FVector2D> Result;
			}TweenFuncParam;
			TweenFuncParam.Count				= RouletteInfo.PointCount;
			TweenFuncParam.Angle				= RouletteInfo.Angle;
			TweenFuncParam.CompensationValue	= RouletteInfo.CompensationValue;
			TweenFuncParam.RouletteDiameter		= RouletteInfo.Diameter;
		
			this->ProcessEvent(Func, &TweenFuncParam);

			MyRouletteInfo.ItemPointArray = TweenFuncParam.Result;
		}
		if(CreateCount == 1)
		{
			URouletteItem* TempItem = Cast<URouletteItem>(UGameplayFuncLib::GetUiManager()->Show("RouletteItem"));
			
			Roulette_Base->AddChild(TempItem);
			TempItem->SetVisibility(ESlateVisibility::Visible);
			TempItem->SetRouletteItemNameTextDir(MyRouletteInfo.ItemPopupDir);
			UWidgetLayoutLibrary::SlotAsCanvasSlot(TempItem)->SetAnchors(FAnchors(0.5f));
			UWidgetLayoutLibrary::SlotAsCanvasSlot(TempItem)->SetAlignment(FVector2D(0.5f,0.5f));
			UWidgetLayoutLibrary::SlotAsCanvasSlot(TempItem)->SetPosition(MyRouletteInfo.ItemPointArray[MyRouletteInfo.ItemPointArray.Num() / 2]);
			if(IsRouletteFocus)
			{
				TempItem->SetItemFocused(true);
				TempItem->GetRouletteItemNameGroundBaseSlot()->SetPosition(FVector2D(0.0f,TempItem->GetRouletteItemNameGroundBaseSlot()->GetPosition().Y));
			}
			

			CurSelectPointIndex = 0;
			CurFocusIndex = 0;
			CanSetItemMinIndex = 0;
			CanSetItemMaxIndex = 0;
			
			TempItem->SetRouletteItemAttributes(RouletteItemInfos[CurFocusIndex]);
			
			//UE_LOG(LogTemp,Log,TEXT(" X : %f , Y : %f  代码在Roulette.cpp 116行 "),TempItem->GetRouletteItemNameGroundBaseSlot()->GetPosition().X,TempItem->GetRouletteItemNameGroundBaseSlot()->GetPosition().Y);

			//// 应该由Item自己决定是否要显示
			// if(!RouletteItemInfos[CurItemSelectIndex].IsShowItemDecorateGround)
			// 	TempItem->RouletteItem_DecorateGround->SetVisibility(ESlateVisibility::Collapsed);
			
			TempItem->UpdateView();
			//TempItem->UpdateSelected();	//被UpdateView取代
			RouletteItemArray.Add(TempItem);
		}

		else
		{
			switch (MyRouletteInfo.RouletteStyle)
			{
			case ERouletteStyle::RouletteEndless:
				
				if(CreateCount == 5)
				{
					CanSetPointMinIndex = 1;
					CurSelectPointIndex = 3;
					CanSetPointMaxIndex = 5;
					CurFocusIndex = 0;
					CanSetItemMinIndex = CreateCount - 2;
					CanSetItemMaxIndex = CurFocusIndex + 2;
				}
				else if(CreateCount == 6)
				{
					CanSetPointMinIndex = 1;
					CurSelectPointIndex = 3;
					CanSetPointMaxIndex = 6;
					CurFocusIndex = 0;
					CanSetItemMinIndex = CreateCount - 2;
					CanSetItemMaxIndex = CurFocusIndex + 3;
				}
				else if(CreateCount > 6)
				{
					CanSetPointMinIndex = 0;
					CurSelectPointIndex = 3;
					CanSetPointMaxIndex = 6;
					CurFocusIndex = 0;
					CanSetItemMinIndex = CreateCount - 3;
					CanSetItemMaxIndex = CurFocusIndex + 3;
				}
				
				
				for(int i = 0;i < MyRouletteInfo.ItemPointArray.Num();i++)
				{
					URouletteItem* TempItem = Cast<URouletteItem>(UGameplayFuncLib::GetUiManager()->Show("RouletteItem"));
					Roulette_Base->AddChild(TempItem);
					TempItem->SetVisibility(ESlateVisibility::Visible);
					TempItem->SetRouletteItemNameTextDir(MyRouletteInfo.ItemPopupDir);
					UWidgetLayoutLibrary::SlotAsCanvasSlot(TempItem)->SetAnchors(FAnchors(0.5f));
					UWidgetLayoutLibrary::SlotAsCanvasSlot(TempItem)->SetAlignment(FVector2D(0.5f,0.5f));
					UWidgetLayoutLibrary::SlotAsCanvasSlot(TempItem)->SetPosition(FVector2D(MyRouletteInfo.ItemPointArray[i]));
					RouletteItemArray.Add(TempItem);

					
					if(i < CanSetPointMinIndex || i > CanSetPointMaxIndex)
					{
						TempItem->IsHide = true;
					}
					else
					{
						if(i < CurSelectPointIndex)
						{
							// 应该由Item自己决定是否要显示
							// if(!RouletteItemInfos[CreateCount - 3 + i].IsShowItemDecorateGround)
							// 	TempItem->RouletteItem_DecorateGround->SetVisibility(ESlateVisibility::Collapsed);
							TempItem->SetRouletteItemAttributes(RouletteItemInfos[CreateCount - 3 + i]);
							TempItem->SetItemFocused(false);
						}
						else if(i == CurSelectPointIndex)
						{
							// 应该由Item自己决定是否要显示
							// if(!RouletteItemInfos[0].IsShowItemDecorateGround)
							// 	TempItem->RouletteItem_DecorateGround->SetVisibility(ESlateVisibility::Collapsed);
							TempItem->SetRouletteItemAttributes(RouletteItemInfos[0]);
							if(IsRouletteFocus)
							{
								TempItem->SetItemFocused(true);
								TempItem->GetRouletteItemNameGroundBaseSlot()->SetPosition(FVector2D(0.0f,TempItem->
								GetRouletteItemNameGroundBaseSlot()->GetPosition().Y));
							}
							else
							{
								TempItem->SetItemFocused(false);
							}
							
						}
						else
						{
							// 应该由Item自己决定是否要显示
							// if(!RouletteItemInfos[i - 3].IsShowItemDecorateGround)
							// 	TempItem->RouletteItem_DecorateGround->SetVisibility(ESlateVisibility::Collapsed);
							TempItem->SetRouletteItemAttributes(RouletteItemInfos[i - 3]);
							TempItem->SetItemFocused(false);
						}
						TempItem->UpdateView();
						//TempItem->UpdateSelected();	//被UpdateView取代
					}
				}
				
				break;
				
			case ERouletteStyle::RouletteLimited:
				
				if(CreateCount == 2)
				{
					CanSetPointMinIndex = 3;
					CurSelectPointIndex = 3;
					CanSetPointMaxIndex = 4;

					CurFocusIndex = 0;
					CanSetItemMinIndex = 0;
					CanSetItemMaxIndex = 1;
				}
				else if(CreateCount == 3)
				{
					CanSetPointMinIndex = 3;
					CurSelectPointIndex = 3;
					CanSetPointMaxIndex = 5;

					CurFocusIndex = 0;
					CanSetItemMinIndex = 0;
					CanSetItemMaxIndex = 2;
				}
				else if(CreateCount >= 4)
				{
					CanSetPointMinIndex = 3;
					CurSelectPointIndex = 3;
					CanSetPointMaxIndex = 6;

					CurFocusIndex = 0;
					CanSetItemMinIndex = 0;
					CanSetItemMaxIndex = 3;
				}

				for(int i = 0;i < MyRouletteInfo.ItemPointArray.Num();i++)
				{
					URouletteItem* TempItem = Cast<URouletteItem>(UGameplayFuncLib::GetUiManager()->Show("RouletteItem"));
					Roulette_Base->AddChild(TempItem);
					TempItem->SetRouletteItemNameTextDir(MyRouletteInfo.ItemPopupDir);
					UWidgetLayoutLibrary::SlotAsCanvasSlot(TempItem)->SetAnchors(FAnchors(0.5f));
					UWidgetLayoutLibrary::SlotAsCanvasSlot(TempItem)->SetAlignment(FVector2D(0.5f,0.5f));
					UWidgetLayoutLibrary::SlotAsCanvasSlot(TempItem)->SetPosition(FVector2D(MyRouletteInfo.ItemPointArray[i]));
					RouletteItemArray.Add(TempItem);
					
					
					if(i < CanSetPointMinIndex || i > CanSetPointMaxIndex)
					{
						TempItem->IsHide = true;
					}
					else
					{
						TempItem->SetVisibility(ESlateVisibility::Visible);
					
						if(i == CurSelectPointIndex)
						{
							// 应该由Item自己决定是否要显示
							// if(!RouletteItemInfos[0].IsShowItemDecorateGround)
							// 	TempItem->RouletteItem_DecorateGround->SetVisibility(ESlateVisibility::Collapsed);
							TempItem->SetRouletteItemAttributes(RouletteItemInfos[0]);
							if(IsRouletteFocus)
							{
								TempItem->SetItemFocused(true);
								TempItem->GetRouletteItemNameGroundBaseSlot()->SetPosition(FVector2D(0.0f,TempItem->
								GetRouletteItemNameGroundBaseSlot()->GetPosition().Y));
							}
							else
							{
								TempItem->SetItemFocused(false);
							}
						}
						else
						{
							// 应该由Item自己决定是否要显示
							// if(!RouletteItemInfos[i - 3].IsShowItemDecorateGround)
							// 	TempItem->RouletteItem_DecorateGround->SetVisibility(ESlateVisibility::Collapsed);
							TempItem->SetRouletteItemAttributes(RouletteItemInfos[i - 3]);
							TempItem->SetItemFocused(false);
						}
						TempItem->UpdateView();
						//TempItem->UpdateSelected();	//被UpdateView取代
					}
				}
				break;
			}
		}
	}
	
}

void URoulette::SetRoulettePoint(float NextAngle)
{
	URouletteItem* TempItem = nullptr;
	switch (RotateDir)
	{
	case ERotateDir::RotateDown:

		if(MyRouletteInfo.RouletteStyle == ERouletteStyle::RouletteEndless)
		{
			if(CreateCount == 5)
			{
				RouletteItemArray[RouletteItemArray.Num() - 1]->SetRouletteItemAttributes(RouletteItemInfos[CanSetItemMinIndex]);
			}
			
			TempItem = RouletteItemArray[0];
			UWidgetLayoutLibrary::SlotAsCanvasSlot(TempItem)->SetPosition(SetRouletteItemPosition(NextAngle,MyRouletteInfo.Diameter));
			TempItem->SetRenderTransformAngle(0.0f);
			RouletteItemArray.RemoveAt(0);
			RouletteItemArray.Add(TempItem);

			if(CanSetItemMinIndex >= RouletteItemInfos.Num() - 1)
			{
				CanSetItemMinIndex = 0;
			}
			else
			{
				CanSetItemMinIndex += 1;
			}

			if(CurFocusIndex >= RouletteItemInfos.Num() - 1)
			{
				CurFocusIndex = 0;
			}
			else
			{
				CurFocusIndex += 1;
			}
		
			if(CanSetItemMaxIndex >= RouletteItemInfos.Num() - 1)
			{
				CanSetItemMaxIndex = 0;
			}
			else
			{
				CanSetItemMaxIndex += 1;
			}

			if(CreateCount != 5)
			{
				if(&RouletteItemInfos[CanSetItemMaxIndex].IconPath && &RouletteItemInfos[CanSetItemMaxIndex].Name)
					TempItem->SetRouletteItemAttributes(RouletteItemInfos[CanSetItemMaxIndex]);
			}
			
		}
		
		else if(MyRouletteInfo.RouletteStyle == ERouletteStyle::RouletteLimited && CurFocusIndex < CreateCount)
		{
			
			TempItem = RouletteItemArray[0];
			UWidgetLayoutLibrary::SlotAsCanvasSlot(TempItem)->SetPosition(SetRouletteItemPosition(NextAngle,MyRouletteInfo.Diameter));
			TempItem->SetRenderTransformAngle(0.0f);
			RouletteItemArray.RemoveAt(0);
			RouletteItemArray.Add(TempItem);
			if(CanSetItemMaxIndex == RouletteItemInfos.Num() - 1)
			{
				TempItem->SetVisibility(ESlateVisibility::Collapsed);
			}
			else if(CanSetItemMaxIndex < RouletteItemInfos.Num() - 1)
			{
				TempItem->SetVisibility(ESlateVisibility::Visible);
			}

			if(CurFocusIndex > 2)
			{
				CanSetItemMinIndex += 1;
			}
			
			if(CurFocusIndex >= RouletteItemInfos.Num() - 1)
			{
				CurFocusIndex = RouletteItemInfos.Num() - 1;
			}
			else
			{
				CurFocusIndex += 1;
			}
		
			if(CanSetItemMaxIndex >= RouletteItemInfos.Num() - 1)
			{
				CanSetItemMaxIndex = RouletteItemInfos.Num() - 1;
			}
			else
			{
				CanSetItemMaxIndex += 1;
			}

			if(&RouletteItemInfos[CanSetItemMaxIndex].IconPath && &RouletteItemInfos[CanSetItemMaxIndex].Name)
				TempItem->SetRouletteItemAttributes(RouletteItemInfos[CanSetItemMaxIndex]);
			
		}
		
		break;

	case ERotateDir::RotateUp:
		
		if(MyRouletteInfo.RouletteStyle == ERouletteStyle::RouletteEndless)
		{
			if(CreateCount == 5 || CreateCount == 6)
			{
				RouletteItemArray[0]->SetRouletteItemAttributes(RouletteItemInfos[CanSetItemMaxIndex]);
			}
			
			TempItem = RouletteItemArray[RouletteItemArray.Num() - 1];
			UWidgetLayoutLibrary::SlotAsCanvasSlot(TempItem)->SetPosition(SetRouletteItemPosition(NextAngle,MyRouletteInfo.Diameter));
			TempItem->SetRenderTransformAngle(0.0f);
			RouletteItemArray.RemoveAt(RouletteItemArray.Num() - 1);
			RouletteItemArray.Insert(TempItem,0);

		
			if(CanSetItemMinIndex <= 0)
			{
				CanSetItemMinIndex = RouletteItemInfos.Num() - 1;
			}
			else
			{
				CanSetItemMinIndex -= 1;
			}

			if(CurFocusIndex <= 0)
			{
				CurFocusIndex = RouletteItemInfos.Num() - 1;
			}
			else
			{
				CurFocusIndex -= 1;
			}
		
			if(CanSetItemMaxIndex <= 0)
			{
				CanSetItemMaxIndex = RouletteItemInfos.Num() - 1;
			}
			else
			{
				CanSetItemMaxIndex -= 1;
			}

			if(CreateCount != 5 && CreateCount != 6)
			{
				if(&RouletteItemInfos[CanSetItemMaxIndex].IconPath && &RouletteItemInfos[CanSetItemMinIndex].Name)
					TempItem->SetRouletteItemAttributes(RouletteItemInfos[CanSetItemMinIndex]);
			}
			
		}
		else if(MyRouletteInfo.RouletteStyle == ERouletteStyle::RouletteLimited && CurFocusIndex >= 0)
		{
			TempItem = RouletteItemArray[RouletteItemArray.Num() - 1];
			UWidgetLayoutLibrary::SlotAsCanvasSlot(TempItem)->SetPosition(SetRouletteItemPosition(NextAngle,MyRouletteInfo.Diameter));
			TempItem->SetRenderTransformAngle(0.0f);
			RouletteItemArray.RemoveAt(RouletteItemArray.Num() - 1);
			RouletteItemArray.Insert(TempItem,0);
			
			if(CanSetItemMinIndex == 0)
			{
				TempItem->SetVisibility(ESlateVisibility::Collapsed);
			}
			else if(CanSetItemMinIndex > 0)
			{
				TempItem->SetVisibility(ESlateVisibility::Visible);
			}
				

			if(CanSetItemMinIndex <= 0)
			{
				CanSetItemMinIndex = 0;
			}
			else
			{
				CanSetItemMinIndex -= 1;
			}

			if(CurFocusIndex <= 0)
			{
				CurFocusIndex = 0;
			}
			else
			{
				CurFocusIndex -= 1;
			}
		
			if(CurFocusIndex < CreateCount - 4)
			{
				CanSetItemMaxIndex -= 1;
			}

			if(&RouletteItemInfos[CanSetItemMaxIndex].IconPath && &RouletteItemInfos[CanSetItemMinIndex].Name)
				TempItem->SetRouletteItemAttributes(RouletteItemInfos[CanSetItemMinIndex]);
		}
		
		break;
		
	default:
			
		break;
	}
}

void URoulette::SetLastPointScale(float InDeltaTime)
{
	if(MyRouletteInfo.RouletteStyle == ERouletteStyle::RouletteEndless)
	{
		RouletteItemArray[CurSelectPointIndex - LastSelectPointIndex]->SetRenderScale(FMath::Vector2DInterpTo(RouletteItemArray[CurSelectPointIndex - LastSelectPointIndex]->GetRenderTransform().Scale,
		FVector2D(1.0f,1.0f),InDeltaTime,ItemScaleSpeed));
		
		
		UWidgetLayoutLibrary::SlotAsCanvasSlot(
			RouletteItemArray[CurSelectPointIndex - LastSelectPointIndex]->RouletteItemName_GroundBase)
				->SetPosition(FMath::Vector2DInterpTo(
					UWidgetLayoutLibrary::SlotAsCanvasSlot(RouletteItemArray[CurSelectPointIndex - LastSelectPointIndex]->RouletteItemName_GroundBase)->GetPosition(),
			FVector2D(-200.0f,UWidgetLayoutLibrary::SlotAsCanvasSlot(RouletteItemArray[CurSelectPointIndex - LastSelectPointIndex]->RouletteItemName_GroundBase)->GetPosition().Y),InDeltaTime,ItemScaleSpeed));
		
		//SetRoulettePointPosition(RouletteRotationAngle,InDeltaTime);
	
		if(RouletteItemArray[CurSelectPointIndex - LastSelectPointIndex]->GetRenderTransform().Scale == FVector2D(1.0f,1.0f))
		{
			RouletteState = ERouletteState::Turning;
		}
	}
	else
	{
		if(CurFocusIndex >= 0 && CurFocusIndex < CreateCount)
		{
			RouletteItemArray[CurSelectPointIndex - LastSelectPointIndex]->SetRenderScale(FMath::Vector2DInterpTo(RouletteItemArray[CurSelectPointIndex - LastSelectPointIndex]->GetRenderTransform().Scale,
			FVector2D(1.0f,1.0f),InDeltaTime,ItemScaleSpeed));
		
			
			UWidgetLayoutLibrary::SlotAsCanvasSlot(RouletteItemArray[CurSelectPointIndex - LastSelectPointIndex]->RouletteItemName_GroundBase)->SetPosition(FMath::Vector2DInterpTo(UWidgetLayoutLibrary::SlotAsCanvasSlot(RouletteItemArray[CurSelectPointIndex - LastSelectPointIndex]->RouletteItemName_GroundBase)->GetPosition(),
				FVector2D(-200.0f,UWidgetLayoutLibrary::SlotAsCanvasSlot(RouletteItemArray[CurSelectPointIndex - LastSelectPointIndex]->RouletteItemName_GroundBase)->GetPosition().Y),InDeltaTime,ItemScaleSpeed));
			
				
			//SetRoulettePointPosition(RouletteRotationAngle,InDeltaTime);
			
			if(RouletteItemArray[CurSelectPointIndex - LastSelectPointIndex]->GetRenderTransform().Scale == FVector2D(1.0f,1.0f))
			{
				RouletteState = ERouletteState::Turning;
			}
		}
	}
	
}

void URoulette::SetCurPointScale(float InDeltaTime)
{
	RouletteItemArray[CurSelectPointIndex]->SetRenderScale(FMath::Vector2DInterpTo(RouletteItemArray[CurSelectPointIndex]->GetRenderTransform().Scale,
		FVector2D(1.5f,1.5f),InDeltaTime,ItemScaleSpeed));
	
	
	UWidgetLayoutLibrary::SlotAsCanvasSlot(RouletteItemArray[CurSelectPointIndex]->RouletteItemName_GroundBase)->SetPosition(FMath::Vector2DInterpTo(UWidgetLayoutLibrary::SlotAsCanvasSlot(RouletteItemArray[CurSelectPointIndex]->RouletteItemName_GroundBase)->GetPosition(),
	FVector2D(0.0f,UWidgetLayoutLibrary::SlotAsCanvasSlot(RouletteItemArray[CurSelectPointIndex]->RouletteItemName_GroundBase)->GetPosition().Y),InDeltaTime,ItemScaleSpeed));
	
	
	if(RouletteItemArray[CurSelectPointIndex]->GetRenderTransform().Scale == FVector2D(1.5f,1.5f))
	{
		RouletteState = ERouletteState::Select;
	}
}

void URoulette::SetRoulettePointPosition(float rotate,float InDeltaTime)
{
	switch (RotateDir)
	{
		
	case ERotateDir::RotateDown:
		{
			UWidgetLayoutLibrary::SlotAsCanvasSlot(RouletteItemArray[CurSelectPointIndex - LastSelectPointIndex])->
		SetPosition(FMath::Vector2DInterpTo(UWidgetLayoutLibrary::SlotAsCanvasSlot(RouletteItemArray[CurSelectPointIndex - LastSelectPointIndex])->GetPosition(),
		FVector2D(FMath::Sin(FMath::DegreesToRadians((90.0f - rotate - MyRouletteInfo.Angle) - MyRouletteInfo.Angle / MyRouletteInfo.CompensationValue)) * MyRouletteInfo.Diameter,
			-FMath::Cos(FMath::DegreesToRadians((90.0f - rotate - MyRouletteInfo.Angle) - MyRouletteInfo.Angle / MyRouletteInfo.CompensationValue)) * MyRouletteInfo.Diameter),InDeltaTime,RotateSpeed));
			
			UWidgetLayoutLibrary::SlotAsCanvasSlot(RouletteItemArray[CurSelectPointIndex])->
			SetPosition(FMath::Vector2DInterpTo(UWidgetLayoutLibrary::SlotAsCanvasSlot(RouletteItemArray[CurSelectPointIndex])->GetPosition(),
			FVector2D(FMath::Sin(FMath::DegreesToRadians(90.0f - rotate)) * MyRouletteInfo.Diameter,
				-FMath::Cos(FMath::DegreesToRadians(90.0f - rotate)) * MyRouletteInfo.Diameter),InDeltaTime,RotateSpeed));
		}
		break;
	case ERotateDir::RotateUp:
		{
			UWidgetLayoutLibrary::SlotAsCanvasSlot(RouletteItemArray[CurSelectPointIndex - LastSelectPointIndex])->
		SetPosition(FMath::Vector2DInterpTo(UWidgetLayoutLibrary::SlotAsCanvasSlot(RouletteItemArray[CurSelectPointIndex - LastSelectPointIndex])->GetPosition(),
		FVector2D(FMath::Sin(FMath::DegreesToRadians((90.0f + rotate - MyRouletteInfo.Angle) - MyRouletteInfo.Angle / MyRouletteInfo.CompensationValue)) * MyRouletteInfo.Diameter,
			FMath::Cos(FMath::DegreesToRadians((90.0f + rotate - MyRouletteInfo.Angle) - MyRouletteInfo.Angle / MyRouletteInfo.CompensationValue)) * MyRouletteInfo.Diameter),InDeltaTime,RotateSpeed));

			UWidgetLayoutLibrary::SlotAsCanvasSlot(RouletteItemArray[CurSelectPointIndex])->
			SetPosition(FMath::Vector2DInterpTo(UWidgetLayoutLibrary::SlotAsCanvasSlot(RouletteItemArray[CurSelectPointIndex])->GetPosition(),
			FVector2D(FMath::Sin(FMath::DegreesToRadians(90.0f + rotate)) * MyRouletteInfo.Diameter,
				FMath::Cos(FMath::DegreesToRadians(90.0f + rotate)) * MyRouletteInfo.Diameter),InDeltaTime,RotateSpeed));
		}
		break;
			
	default:
		UE_LOG(LogTemp,Warning,TEXT("RotateDir Is None"));
		break;
	}
}

void URoulette::RouletteOperation(float rotate, float InDeltaTime)
{
	if(!IsRouletteFocus && RouletteItemArray.Num() > 0 && IsValid(RouletteItemArray[CurSelectPointIndex]))
	{
		if(RouletteItemArray[CurSelectPointIndex]->GetRenderTransform().Scale != FVector2D(1.0f,1.0f))
		{
			RouletteItemArray[CurSelectPointIndex]->SetRenderScale(FMath::Vector2DInterpTo(RouletteItemArray[CurSelectPointIndex]->
						GetRenderTransform().Scale,FVector2D(1.0f,1.0f),InDeltaTime,ItemScaleSpeed));
			
			UWidgetLayoutLibrary::SlotAsCanvasSlot(RouletteItemArray[CurSelectPointIndex]->RouletteItemName_GroundBase)->
					SetPosition(FMath::Vector2DInterpTo(UWidgetLayoutLibrary::SlotAsCanvasSlot(RouletteItemArray[CurSelectPointIndex]->
						RouletteItemName_GroundBase)->GetPosition(),
					FVector2D(-200.0f,UWidgetLayoutLibrary::SlotAsCanvasSlot(RouletteItemArray[CurSelectPointIndex]->
						RouletteItemName_GroundBase)->GetPosition().Y),InDeltaTime,ItemScaleSpeed));

			RouletteItemArray[CurSelectPointIndex]->SetItemFocused(false);
			RouletteItemArray[CurSelectPointIndex]->UpdateView();
		}
	}
	else
	{
		switch (RouletteState)
	{
	case ERouletteState::Select:
		{
			if (CurSelectPointIndex >= 0 && CurSelectPointIndex < RouletteItemArray.Num())
			{
				if(RouletteItemArray[CurSelectPointIndex]->GetRenderTransform().Scale != FVector2D(1.5f,1.5f))
				{
					RouletteItemArray[CurSelectPointIndex]->SetRenderScale(FMath::Vector2DInterpTo(RouletteItemArray[CurSelectPointIndex]->
						GetRenderTransform().Scale,FVector2D(1.5f,1.5f),InDeltaTime,ItemScaleSpeed));
				
					UWidgetLayoutLibrary::SlotAsCanvasSlot(RouletteItemArray[CurSelectPointIndex]->RouletteItemName_GroundBase)->
					SetPosition(FMath::Vector2DInterpTo(UWidgetLayoutLibrary::SlotAsCanvasSlot(RouletteItemArray[CurSelectPointIndex]->
						RouletteItemName_GroundBase)->GetPosition(),
					FVector2D(0.0f,UWidgetLayoutLibrary::SlotAsCanvasSlot(RouletteItemArray[CurSelectPointIndex]->
						RouletteItemName_GroundBase)->GetPosition().Y),InDeltaTime,ItemScaleSpeed));

					RouletteItemArray[CurSelectPointIndex]->SetItemFocused(true);
					RouletteItemArray[CurSelectPointIndex]->UpdateView();
				}
			}
			
			break;
		}
	case ERouletteState::Confirm:
		{
			//SetConfirmBoxScale(InDeltaTime,ConfirmScale);
			break;
		}
	case ERouletteState::Turning:
		{
			Roulette_Base->SetRenderTransformAngle(FMath::FInterpTo(Roulette_Base->GetRenderTransformAngle(),
					rotate,InDeltaTime,RotateSpeed));
		
			for(URouletteItem* Temp : RouletteItemArray)
			{
				Temp->SetRenderTransformAngle(FMath::FInterpTo(Temp->GetRenderTransformAngle(),
				-rotate,InDeltaTime,RotateSpeed));
			}
		
			SetRoulettePointPosition(rotate,InDeltaTime);
		
			if(fabs(Roulette_Base->GetRenderTransformAngle() - rotate) < 0.5)
			{
				if(IsRotateHold)
				{
					if(MyRouletteInfo.RouletteStyle == ERouletteStyle::RouletteEndless)
					{
						RouletteState = ERouletteState::Select;
					}
					else if(MyRouletteInfo.RouletteStyle == ERouletteStyle::RouletteLimited)
					{
						if(CurFocusIndex > 0 && CurFocusIndex < CreateCount - 1)
						{
							RouletteState = ERouletteState::Select;
						}
						else
						{
							RouletteState = ERouletteState::ItemCollapsing;
						}
					}
				
				}
				else
				{
					RouletteState = ERouletteState::ItemCollapsing;
					RotateDir = ERotateDir::None;
				}
			}
		
			break;
		}
	case ERouletteState::ItemCollapsing:
		{
			RouletteItemArray[CurSelectPointIndex]->SetItemFocused(true);
			RouletteItemArray[CurSelectPointIndex]->UpdateView();
			SetCurPointScale(InDeltaTime);
			break;
		}
		
	case ERouletteState::ItemUnfolding:
		{
			RouletteItemArray[CurSelectPointIndex - LastSelectPointIndex]->SetItemFocused(false);
			RouletteItemArray[CurSelectPointIndex - LastSelectPointIndex]->UpdateView();
			SetLastPointScale(InDeltaTime);
		
			break;
		}
	default:
		
		break;
	}
	}
		
	
}

void URoulette::RouletteDownRotate()
{
	if(RouletteState == ERouletteState::Select)
	{
		
		if(MyRouletteInfo.RouletteStyle == ERouletteStyle::RouletteEndless)
		{
			RouletteRotationAngle -= MyRouletteInfo.Angle;
			if(RotateSpeed < 80.0f)
				RotateSpeed += RotateAcceleration;
			IsRotateHold = true;
			RotateDir = ERotateDir::RotateDown;
			LastSelectPointIndex = 1;
			
			SetRoulettePoint((180.0f - RouletteRotationAngle) - (90 - MyRouletteInfo.PointCount/2 * MyRouletteInfo.Angle) +
				(MyRouletteInfo.Angle / MyRouletteInfo.CompensationValue));
			RouletteState = ERouletteState::ItemUnfolding;
		}
		else if(MyRouletteInfo.RouletteStyle == ERouletteStyle::RouletteLimited && CurFocusIndex < CreateCount - 1)
		{
			UGameplayFuncLib::PlayUIAudio("SwitchKey_UpDown");
			RouletteRotationAngle -= MyRouletteInfo.Angle;
			if(RotateSpeed < 80.0f)
				RotateSpeed += RotateAcceleration;
			IsRotateHold = true;
			RotateDir = ERotateDir::RotateDown;
			LastSelectPointIndex = 1;
			SetRoulettePoint((180.0f - RouletteRotationAngle) - (90 - MyRouletteInfo.PointCount/2 * MyRouletteInfo.Angle) +
				(MyRouletteInfo.Angle / MyRouletteInfo.CompensationValue));
			RouletteState = ERouletteState::ItemUnfolding;
		}
		
	}
}

void URoulette::RouletteUpRotate()
{
	if(RouletteState == ERouletteState::Select)
	{
		
		if(MyRouletteInfo.RouletteStyle == ERouletteStyle::RouletteEndless)
		{
			RouletteRotationAngle += MyRouletteInfo.Angle;
			if(RotateSpeed < 80.0f)
				RotateSpeed += RotateAcceleration;
			IsRotateHold = true;
			RotateDir = ERotateDir::RotateUp;
			LastSelectPointIndex = -1;
			
			SetRoulettePoint((90 - MyRouletteInfo.PointCount/2 * MyRouletteInfo.Angle) -
				(MyRouletteInfo.Angle / MyRouletteInfo.CompensationValue) + (0.0f - RouletteRotationAngle));
			RouletteState = ERouletteState::ItemUnfolding;
		}
		else if(MyRouletteInfo.RouletteStyle == ERouletteStyle::RouletteLimited && CurFocusIndex > 0)
		{
			UGameplayFuncLib::PlayUIAudio("SwitchKey_UpDown");
			RouletteRotationAngle += MyRouletteInfo.Angle;
			if(RotateSpeed < 80.0f)
				RotateSpeed += RotateAcceleration;
			IsRotateHold = true;
			RotateDir = ERotateDir::RotateUp;
			LastSelectPointIndex = -1;
			
			SetRoulettePoint((90 - MyRouletteInfo.PointCount/2 * MyRouletteInfo.Angle) -
				(MyRouletteInfo.Angle / MyRouletteInfo.CompensationValue) + (0.0f - RouletteRotationAngle));
			RouletteState = ERouletteState::ItemUnfolding;
		}
	}
}

void URoulette::RouletteStopRotate()
{
	IsRotateHold = false;
	if(RouletteState != ERouletteState::Turning)
		RotateSpeed = 20.0f;
}


FVector2D URoulette::SetRouletteItemPosition(float angle,float Diameter)
{
	return FVector2D(FMath::Sin(FMath::DegreesToRadians(angle)) * Diameter,
							-(FMath::Cos(FMath::DegreesToRadians(angle))) * Diameter);
}

URouletteItem* URoulette::GetCurEquippedItem()
{
	for (URouletteItem* Temp : RouletteItemArray)
	{
		if(Temp->IsSelected && !Temp->IsHide)
		{
			return Temp;
		}
	}
	return nullptr;
}

void URoulette::RestRouletteAngleAndRouletteItemAngle()
{
	Roulette_Base->SetRenderTransformAngle(0);
	for (URouletteItem* Temp : RouletteItemArray)
	{
		Temp->SetRenderTransformAngle(0);
	}
}

FRouletteItemInfo URoulette::GetItemInfo(int Index)
{
	Index = FMath::Clamp(Index, 0, RouletteItemInfos.Num() - 1);
	return RouletteItemInfos[Index];
}


//根据两点距离位置算出夹角
/*
float URoulette::GetRouletteItemPositionToAngle(FVector2D OnePosition,FVector2D TwoPosition)
{
	FVector2D Test = OnePosition.GetSafeNormal();
	float TempAngle = FVector2D::DotProduct(Test,TwoPosition.GetSafeNormal());
	float angle = (180.0f) / PI * FMath::Acos(TempAngle);
	UE_LOG(LogTemp,Warning,TEXT("Test.X: %f  , Test.Y: %f \n TempAngle:%f  \n angle: %f \n ***********************"),Test.X,Test.Y,TempAngle,angle);
	return  angle;
}*/

TArray<int> URoulette::ItemIndex()
{
	TArray<int> Res;
	for (int i =0 ; i< this->RouletteItemInfos.Num(); i++)
	{
		if (RouletteItemInfos[i].IsSelected) Res.Add(i);
	}
	return Res;
}

int URoulette::FocusIndex()
{
	for (int i =0 ; i< this->RouletteItemInfos.Num(); i++)
	{
		if (RouletteItemInfos[i].IsFocused) return i;
	}
	return -1;
}