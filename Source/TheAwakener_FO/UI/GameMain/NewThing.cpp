// Fill out your copyright notice in the Description page of Project Settings.


#include "NewThing.h"

#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/FunctionLibrary/ResourceFuncLib.h"
#include "TheAwakener_FO/GameFramework/Manager/AwDataManager.h"
#include "TheAwakener_FO/GamePlay/Thing/ThingUIInfo.h"

void UNewThing::NativeOnInitialized()
{
	Super::NativeOnInitialized();
	ThingIconComp = Cast<UImage>(GetWidgetFromName(TEXT("ThingICon")));
	ThingNameComp = Cast<UTextBlock>(GetWidgetFromName(TEXT("ThingName")));
	ThingCountComp = Cast<UTextBlock>(GetWidgetFromName(TEXT("ThingCount")));
}

void UNewThing::Set(FThingObj Thing)
{
	const FThingUIInfo ThingUIInfo = UGameplayFuncLib::GetDataManager()->GetBaseThingUIInfo(Thing.Type, Thing.Id);
	if (ThingIconComp)
	{
		UTexture2D* IconTexture = LoadObject<UTexture2D>(nullptr,*UResourceFuncLib::GetAssetPath( ThingUIInfo.Icon));
		if (IconTexture)
		{
			ThingIconComp->SetVisibility(ESlateVisibility::HitTestInvisible);
			ThingIconComp->SetBrushFromTexture(IconTexture);
		}else
		{
			ThingIconComp->SetVisibility(ESlateVisibility::Hidden);
		}
	}
	
	if (ThingNameComp)
	{
		ThingNameComp->SetText(FText::FromString(UGameplayFuncLib::GetAwDataManager()->GetTextByKey(ThingUIInfo.Name)));
	}
	if (ThingCountComp)
	{
		ThingCountComp->SetText(FText::FromString(FString::FromInt(Thing.Count)));
	}
	TimeElapsed = 0;

	//添加获得货币的音效
	if (Thing.Type == EThingType::Currency)
	{
		UGameplayFuncLib::PlayUIAudio("GetItem_Coin");
	}
	
	/*FLatentActionInfo LatentActionInfo;
	LatentActionInfo.CallbackTarget = this;
	LatentActionInfo.ExecutionFunction = "SelfDestroy";
	LatentActionInfo.UUID = 100;
	LatentActionInfo.Linkage = 1;
	UKismetSystemLibrary::Delay(this,3.0f,LatentActionInfo);*/
}


