// Fill out your copyright notice in the Description page of Project Settings.


#include "NewbieImage.h"

#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/UI/Subtitle/Subtitle.h"

void UNewbieImage::NativeOnInitialized()
{
	Super::NativeOnInitialized();
	ConfirmTextBlock = Cast<UTextBlock>(GetWidgetFromName("Confirm"));
	ConfirmHorizontalBox = Cast<UHorizontalBox>(GetWidgetFromName("ConfirmHorizontal"));
	CancelTextBlock = Cast<UTextBlock>(GetWidgetFromName("Cancel"));
	CancelHorizontalBox = Cast<UHorizontalBox>(GetWidgetFromName("CancelHorizontal"));
	WidgetSwitcherMain = Cast<UWidgetSwitcher>(GetWidgetFromName("WidgetSwitcher_Main"));
}

void UNewbieImage::NativeConstruct()
{
	Super::NativeConstruct();
	
	if(IsValid(WidgetSwitcherMain))
	{
		WidgetSwitcherMain->SetActiveWidgetIndex(0);
		WidgetSwitcherMain->ClearChildren();
		if(WidgetSwitcherMain->GetChildrenCount() == 1)
		{
			if(IsValid(ConfirmTextBlock))
				ConfirmTextBlock->SetText(FText::FromString(UGameplayFuncLib::GetAwDataManager()->GetTextByKey("UI_Closs")));
		}
		else if(WidgetSwitcherMain->GetChildrenCount() > 1)
		{
			if(IsValid(ConfirmTextBlock))
				ConfirmTextBlock->SetText(FText::FromString(UGameplayFuncLib::GetAwDataManager()->GetTextByKey("Next")));
		}
	}
}

void UNewbieImage::NativeTick(const FGeometry& MyGeometry, float InDeltaTime)
{
	Super::NativeTick(MyGeometry, InDeltaTime);
	if(UGameplayFuncLib::GetAwGameState()->GetMyCharacter())
	{
		/*if (UGameplayFuncLib::GetAwGameState()->MyCharacter->IsActionOccur("Newbie_Left", EAwInputState::Press, true))
		{
			if(!IsValid(WidgetSwitcherMain)) return;
			if(!(WidgetSwitcherMain->GetActiveWidgetIndex() <= 0))
			{ 
				WidgetSwitcherMain->SetActiveWidgetIndex(WidgetSwitcherMain->GetActiveWidgetIndex() - 1);
			}
		}
		else if (UGameplayFuncLib::GetAwGameState()->MyCharacter->IsActionOccur("Newbie_Right", EAwInputState::Press, true))
		{
			if(!IsValid(WidgetSwitcherMain)) return;
			if(!(WidgetSwitcherMain->GetActiveWidgetIndex() >= WidgetSwitcherMain->GetChildrenCount() - 1))
			{
				WidgetSwitcherMain->SetActiveWidgetIndex(WidgetSwitcherMain->GetActiveWidgetIndex() + 1);
			}
		}*/
		if (UGameplayFuncLib::GetAwGameState()->GetMyCharacter()->IsActionOccur("Newbie_Confirm", EAwInputState::Press, true))
		{
			UGameplayFuncLib::PlayUIAudio("ConfirmKey_Yes");
			if(!IsValid(WidgetSwitcherMain)) return;
			if(WidgetSwitcherMain->GetActiveWidgetIndex() == WidgetSwitcherMain->GetChildrenCount() - 1)
				Hide();
			if(!(WidgetSwitcherMain->GetActiveWidgetIndex() >= WidgetSwitcherMain->GetChildrenCount() - 1))
			{
				WidgetSwitcherMain->SetActiveWidgetIndex(WidgetSwitcherMain->GetActiveWidgetIndex() + 1);
				if(IsValid(CancelHorizontalBox))
				{
					CancelTextBlock->SetText(FText::FromString(UGameplayFuncLib::GetAwDataManager()->GetTextByKey("Previous")));
					CancelHorizontalBox->SetVisibility(ESlateVisibility::SelfHitTestInvisible);
				}
				if(IsValid(ConfirmTextBlock))
				{
					if(WidgetSwitcherMain->GetActiveWidgetIndex() == WidgetSwitcherMain->GetChildrenCount() - 1)
					{
						ConfirmTextBlock->SetText(FText::FromString(UGameplayFuncLib::GetAwDataManager()->GetTextByKey("UI_Closs")));
					}
				}
			}
		}
		if (UGameplayFuncLib::GetAwGameState()->GetMyCharacter()->IsActionOccur("Newbie_Cancel", EAwInputState::Press, true))
		{
			UGameplayFuncLib::PlayUIAudio("ConfirmKey_Yes");
			if(!IsValid(WidgetSwitcherMain)) return;
			if(WidgetSwitcherMain->GetActiveWidgetIndex() > 0)
			{
				WidgetSwitcherMain->SetActiveWidgetIndex(WidgetSwitcherMain->GetActiveWidgetIndex() - 1);
				if(WidgetSwitcherMain->GetActiveWidgetIndex() == 0)
				{
					if(IsValid(CancelHorizontalBox))
					{
						CancelHorizontalBox->SetVisibility(ESlateVisibility::Collapsed);
					}
				}
				if(WidgetSwitcherMain->GetActiveWidgetIndex() <= WidgetSwitcherMain->GetChildrenCount() - 1)
				{
					if(IsValid(ConfirmTextBlock))
					{
						ConfirmTextBlock->SetText(FText::FromString(UGameplayFuncLib::GetAwDataManager()->GetTextByKey("Next")));
					}
				}
			}
		}
	}
}

void UNewbieImage::Show()
{
	WasState = UGameplayFuncLib::GetPlayerControllerByWidget(this)->GameControlState;
	UGameplayFuncLib::GetPlayerControllerByWidget(this)->GameControlState = EGameControlState::NewbieHint;
	UGameplayStatics::SetGamePaused(UGameplayFuncLib::GetAwGameMode(), true);
	//UGameplayFuncLib::PauseGameActors(TArray<AAwCharacter*>());
	if(UGameplayFuncLib::GetUiManager()->OpenedWidgets.Contains("Subtitle"))
	{
		USubtitle* Subtitle = Cast<USubtitle>(UGameplayFuncLib::GetUiManager()->OpenedWidgets["Subtitle"]);
		if(IsValid(Subtitle))
			Subtitle->PasueSubtitle(false);
	}
	if (!this->IsInViewport())
	{
		this->AddToViewport();
	}
}

void UNewbieImage::Hide()
{
	UGameplayFuncLib::GetPlayerControllerByWidget(this)->GameControlState = WasState;
	UGameplayStatics::SetGamePaused(this->GetOuter(), false);
	//UGameplayFuncLib::ResumeGameActors();
	if(UGameplayFuncLib::GetUiManager()->OpenedWidgets.Contains("Subtitle"))
	{
		USubtitle* Subtitle = Cast<USubtitle>(UGameplayFuncLib::GetUiManager()->OpenedWidgets["Subtitle"]);
		if(IsValid(Subtitle))
			Subtitle->ResumeSubtitle();
	}
	for (FString Key : this->OpenedKeys)
	{
		UGameplayFuncLib::GetUiManager()->Hide(Key);
	}
	if(!UGameplayFuncLib::IsRogueMode())
		UGameplayFuncLib::GetUiManager()->Hide("Newbie_Main");
	else
		UGameplayFuncLib::GetUiManager()->Hide("Rogue_Newbie_Main");
}

void UNewbieImage::ChangeConfirmText()
{
	if(WidgetSwitcherMain->GetChildrenCount() > 1)
	{
		if(IsValid(ConfirmTextBlock))
			ConfirmTextBlock->SetText(FText::FromString(UGameplayFuncLib::GetAwDataManager()->GetTextByKey("Next")));
	}
}
