// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "MenuList.h"
#include "TheAwakener_FO/UI/Base/BaseUI.h"
#include "NewbieRetrospect.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UNewbieRetrospect : public UBaseUI
{
	GENERATED_BODY()

	UPROPERTY()
	AAwGameState* GameState;

	UPROPERTY()
	UAwGameInstance* GameInstance;

	UPROPERTY()
	FTimerHandle TimerHandle;

	UPROPERTY()
	FTimerHandle TimerHandle1;

	UPROPERTY()
	TMap<FString,UNewbieImage*> KnownNewbies;


	virtual void NativeOnInitialized() override;
	virtual void NativeConstruct() override;
	virtual void NativeTick(const FGeometry& MyGeometry, float InDeltaTime) override;
	virtual void NativeDestruct() override;
	
public:


	UPROPERTY(VisibleAnywhere,BlueprintReadOnly)
	UMenuList* NewbieMenuList;

	UPROPERTY(VisibleAnywhere,BlueprintReadOnly)
	UCanvasPanel* NewbieCanvasPanel;

	UFUNCTION(BlueprintCallable)
	void InitNewbieList();
	
	UFUNCTION(BlueprintCallable)
	void OperationInput();

	UFUNCTION(BlueprintCallable)
	void Start();
	
	UFUNCTION(BlueprintCallable)
	void Back();
	
	UFUNCTION(BlueprintCallable)
	void SetGameUIControllState();

	UFUNCTION(BlueprintCallable)
	void UpdateAnimPlay(float InDeltaTime);
};
