// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
//#include "ForceOfProgress.h"
#include "ThreeForces.h"
#include "MenuList.h"
#include "TheAwakener_FO/UI/Base/BaseUI.h"
#include "PowerMap.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UPowerMap : public UBaseUI
{
	GENERATED_BODY()

	UPROPERTY()
	AAwGameState* GameState;

	UPROPERTY()
	UAwGameInstance* GameInstance;

	UPROPERTY()
	FTimerHandle TimerHandle;

	UPROPERTY()
	FTimerHandle TimerHandle1;
	
	UPROPERTY()
	float ProgressSizeX;

	UPROPERTY()
	float HumanForcesValue;

	UPROPERTY()
	float RatManForcesValue;

	UPROPERTY()
	float GoblinForcesValue;


	virtual void NativeOnInitialized() override;
	virtual void NativeConstruct() override;
	virtual void NativeTick(const FGeometry& MyGeometry, float InDeltaTime) override;
	
public:
	
	UPROPERTY()
	FString MapId;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	float ChangeForcesValueSpeed = 1.0f;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UMenuList* MapList;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UThreeForces* ThreeForces;

	/*UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UForceOfProgress* HumanForces;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UForceOfProgress* RatManForces;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UForceOfProgress* GoblinForces;*/

	
	UFUNCTION()
	void SetGameUIControllState();

	UFUNCTION()
	void UpdateAnimPlay(float InDeltaTime);
	

	UFUNCTION()
	void Start();

	UFUNCTION()
	void Back();

	UFUNCTION()
	void ChangeForcesOfProgress();

	UFUNCTION()
	void InitList();
};
