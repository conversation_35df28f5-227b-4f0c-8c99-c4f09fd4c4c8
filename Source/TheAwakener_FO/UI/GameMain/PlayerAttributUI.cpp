// Fill out your copyright notice in the Description page of Project Settings.


#include "PlayerAttributUI.h"

void UPlayerAttributUI::NativeOnInitialized()
{
	Super::NativeOnInitialized();


	AttirbutBase	= Cast<UCanvasPanel>(GetWidgetFromName("AttirbutGroundBase"));
	AttirbutGround	= Cast<UImage>(GetWidgetFromName("AttirbutGroundImage"));
	AttirbutIcon	= Cast<UImage>(GetWidgetFromName("AttirbutIconImage"));
	AttirbutValue	= Cast<UTextBlock>(GetWidgetFromName("AttirbutValueText"));
	AttirbutName	= Cast<UTextBlock>(GetWidgetFromName("AttirbutNameText"));

	
}

void UPlayerAttributUI::NativeTick(const FGeometry& MyGeometry, float InDeltaTime)
{
	Super::NativeTick(MyGeometry, InDeltaTime);

	
}

void UPlayerAttributUI::SetAllContent(UTexture2D* Attirbut_Icon, float Attirbut_Value, FString Attirbut_Name)
{
	
	if(Attirbut_Icon && AttirbutIcon)
		AttirbutIcon->SetBrushFromTexture(Attirbut_Icon);

	AttirbutValue->SetText(FText::FromString(FString::FromInt(Attirbut_Value)));
	AttirbutName->SetText(FText::FromString(Attirbut_Name));
}
