// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "Components/CanvasPanel.h"
#include "Components/Image.h"
#include "Components/TextBlock.h"
#include "TheAwakener_FO/UI/Base/BaseUI.h"
#include "PlayerAttributUI.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UPlayerAttributUI : public UBaseUI
{
	GENERATED_BODY()


	virtual void NativeOnInitialized() override;
	virtual void NativeTick(const FGeometry& MyGeometry, float InDeltaTime) override;

public:

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UCanvasPanel* AttirbutBase;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UImage* AttirbutGround;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UImage* AttirbutIcon;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UTextBlock* AttirbutValue;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UTextBlock* AttirbutName;



	UFUNCTION(BlueprintCallable)
	void SetAllContent(UTexture2D* Attirbut_Icon,float Attirbut_Value,FString Attirbut_Name);

	UFUNCTION(BlueprintCallable)
	void SetAttributValue(float Attirbut_Value){AttirbutValue->SetText(FText::FromString(FString::FromInt(Attirbut_Value)));}
};
