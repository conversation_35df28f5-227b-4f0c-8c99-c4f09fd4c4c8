// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "ShopItem.h"
#include "Blueprint/UserWidget.h"
#include "Components/Image.h"
#include "Components/TextBlock.h"
#include "ShopItemPrice.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UShopItemPrice : public UUserWidget
{
	GENERATED_BODY()


	virtual void NativeOnInitialized() override;
	virtual void NativeTick(const FGeometry& MyGeometry, float InDeltaTime) override;
	
public:

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UImage* PlayCurrency_Icon;
	
	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UTextBlock* PlayCurrencyValue_Text;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UImage* CurItemCurrency_Icon;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UTextBlock* CurItemPrice_Text;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UImage* Buy_Icon;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UTextBlock* Buy_Text;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UImage* Backpack_Icon;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UTextBlock* BackpackValue_Text;

	UPROPERTY(VisibleAnywhere,BlueprintReadOnly)
	UShopItem* ShopItem;

	UPROPERTY(VisibleAnywhere,BlueprintReadOnly)
	UTextBlock* PropsCountText;

	UPROPERTY(VisibleAnywhere,BlueprintReadOnly)
	UTextBlock* NameText;


	UFUNCTION(BlueprintCallable)
	void SetShopItemPriceAllAttributes(UTexture2D* PlayCurrencyIcon,int PlayCurrencyValueText,UTexture2D* CurItemCurrencyIcon,
		int CurItemPriceText,UTexture2D* BuyIcon,FString BuyText,UTexture2D* BackpackIcon,int BackpackValueText);

	void SetShopItemPriceAttributes(const int PlayCurrencyValueText,const int CurItemPriceText,const int BackpackValueText);
};
