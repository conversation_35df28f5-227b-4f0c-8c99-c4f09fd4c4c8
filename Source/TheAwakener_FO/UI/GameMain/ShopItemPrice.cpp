// Fill out your copyright notice in the Description page of Project Settings.


#include "ShopItemPrice.h"

#include "Components/ShapeComponent.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"

void UShopItemPrice::NativeOnInitialized()
{
	Super::NativeOnInitialized();
	
	PlayCurrency_Icon = Cast<UImage>(GetWidgetFromName(TEXT("PlayCurrencyIcon")));
	PlayCurrencyValue_Text = Cast<UTextBlock>(GetWidgetFromName(TEXT("PlayCurrencyValueText")));
	CurItemCurrency_Icon = Cast<UImage>(GetWidgetFromName(TEXT("CurItemCurrencyIcon")));
	CurItemPrice_Text = Cast<UTextBlock>(GetWidgetFromName(TEXT("CurItemPriceText")));
	Buy_Icon = Cast<UImage>(GetWidgetFromName(TEXT("BuyIcon")));
	Buy_Text = Cast<UTextBlock>(GetWidgetFromName(TEXT("BuyText")));
	Backpack_Icon = Cast<UImage>(GetWidgetFromName(TEXT("BackpackIcon")));
	BackpackValue_Text = Cast<UTextBlock>(GetWidgetFromName(TEXT("BackpackValueText")));
	ShopItem = Cast<UShopItem>(GetWidgetFromName(TEXT("WBP_ShopItem")));
	PropsCountText = Cast<UTextBlock>(GetWidgetFromName(TEXT("PropsCount")));
	NameText = Cast<UTextBlock>(GetWidgetFromName(TEXT("Name")));


	if(NameText)
		NameText->SetText(FText::FromString(UGameplayFuncLib::GetAwDataManager()->GetTextByKey("Interact_Shop")));
}

void UShopItemPrice::NativeTick(const FGeometry& MyGeometry, float InDeltaTime)
{
	Super::NativeTick(MyGeometry, InDeltaTime);
}

void UShopItemPrice::SetShopItemPriceAllAttributes(UTexture2D* PlayCurrencyIcon, int PlayCurrencyValueText,
	UTexture2D* CurItemCurrencyIcon, int CurItemPriceText, UTexture2D* BuyIcon, FString BuyText,
	UTexture2D* BackpackIcon, int BackpackValueText)
{
	if(PlayCurrency_Icon)
		PlayCurrency_Icon->SetBrushFromTexture(PlayCurrencyIcon);
	if(PlayCurrencyValue_Text)
		PlayCurrencyValue_Text->SetText(FText::FromString(FString::FromInt(PlayCurrencyValueText)));
	if(CurItemCurrency_Icon)
		CurItemCurrency_Icon->SetBrushFromTexture(CurItemCurrencyIcon);
	if(CurItemPrice_Text)
		CurItemPrice_Text->SetText(FText::FromString(FString::FromInt(CurItemPriceText)));
	if(Buy_Icon)
		Buy_Icon->SetBrushFromTexture(BuyIcon);
	if(Buy_Text)
		Buy_Text->SetText(FText::FromString(BuyText));
	if(Backpack_Icon)
		Backpack_Icon->SetBrushFromTexture(BackpackIcon);
	if(BackpackValue_Text)
	{
		const FString Temp = TEXT("x") + FString::FromInt(BackpackValueText);
		BackpackValue_Text->SetText(FText::FromString(Temp));
	}
		
}

void UShopItemPrice::SetShopItemPriceAttributes(const int PlayCurrencyValueText, const int CurItemPriceText,
	const int BackpackValueText)
{
	if(PlayCurrencyValue_Text)
		PlayCurrencyValue_Text->SetText(FText::FromString(FString::FromInt(PlayCurrencyValueText)));
	if(CurItemPrice_Text)
		CurItemPrice_Text->SetText(FText::FromString(FString::FromInt(CurItemPriceText)));
	if(BackpackValue_Text)
	{
		const FString Temp = TEXT("x") + FString::FromInt(BackpackValueText);
		BackpackValue_Text->SetText(FText::FromString(Temp));
	}	
}
