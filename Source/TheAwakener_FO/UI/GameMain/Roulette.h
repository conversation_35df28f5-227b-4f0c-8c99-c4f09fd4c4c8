// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "RouletteItem.h"
#include "Blueprint/UserWidget.h"
#include "Components/CanvasPanel.h"
#include "Components/Image.h"
#include "Components/Overlay.h"
#include "TheAwakener_FO/FunctionLibrary/DataFuncLib.h"
#include "TheAwakener_FO/UI/Base/BaseUI.h"

#include "Roulette.generated.h"

/**
 * 
 */


//轮盘旋转方向
UENUM()
enum class ERotateDir : uint8
{
	None,
	RotateUp,
	RotateDown
};

//轮盘操作状态
UENUM()
enum class ERouletteState : uint8
{
	Select,
	Confirm,
	Turning,
	ItemCollapsing,
	ItemUnfolding,
};

UENUM()
enum class ERouletteStyle : uint8
{
	RouletteEndless,
	RouletteLimited
};

USTRUCT(BlueprintType)
struct FRouletteInfo
{
	GENERATED_BODY()

	
	UPROPERTY()
	UTexture2D* RouletteImage = nullptr;

	UPROPERTY()
	float Diameter = 0;

	UPROPERTY()
	int PointCount = 0;

	UPROPERTY()
	float Angle = 0;

	UPROPERTY()
	float CompensationValue = 0;
	
	UPROPERTY()
	ERouletteStyle RouletteStyle = ERouletteStyle::RouletteEndless;

	UPROPERTY()
	EItemPopupDir ItemPopupDir = EItemPopupDir::PopupNone;
	
	UPROPERTY()
	TArray<FVector2D> ItemPointArray;
};


UCLASS()
class THEAWAKENER_FO_API URoulette : public UBaseUI
{
	GENERATED_BODY()
private:
	FJsonFuncData TweenFunc;
	
	//轮盘旋转角度
	UPROPERTY()
	float RouletteRotationAngle;

	//基础轮盘半径
	UPROPERTY()
	float BaseDiameter;

	//当前选择Index
	UPROPERTY()
	int CurSelectPointIndex;

	//上一次的Index
	UPROPERTY()
	int LastSelectPointIndex;

	//当前的焦点的Index
	UPROPERTY()
	int CurFocusIndex = 0;

	UPROPERTY()
	int CanSetItemMinIndex;

	UPROPERTY()
	int CanSetItemMaxIndex;

	UPROPERTY()
	int CanSetPointMinIndex;

	UPROPERTY()
	int CanSetPointMaxIndex;
	
	UPROPERTY()
	int CreateCount;

	//是否旋转长按
	UPROPERTY()
	bool IsRotateHold;
	
	//当前选择
	UPROPERTY()
	UOverlay* CurSelectPoint;
	
	//接收临时移除的物品点
	UPROPERTY()
	UOverlay* RemovePoint;

	//轮盘旋转方向
	ERotateDir RotateDir;
	//详情框弹出方向
	EItemPopupDir ItemPopupDir;
	//轮盘旋转风格
	ERouletteStyle RouletteStyle;

	UPROPERTY()
	bool IsRouletteFocus = false;

	
	virtual void NativeOnInitialized() override;
	virtual void NativeTick(const FGeometry& MyGeometry, float InDeltaTime) override;

public:
	//当前焦点内容的index
	UFUNCTION(BlueprintCallable)
	int FocusIndex();
	
	//当前选中的
	UFUNCTION(BlueprintCallable)
	TArray<int> ItemIndex();
	
	UPROPERTY()
	FRouletteInfo MyRouletteInfo;

	UPROPERTY()
	TArray<FRouletteItemInfo> RouletteItemInfos;
	
	//轮盘panel
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	UCanvasPanel* Roulette_Base;

	//轮盘图片
	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UImage* Roulette_Image;

	//轮盘操作状态
	ERouletteState RouletteState;

	//物品点数组
	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	TArray<FVector2D> ItemPositionArray;
	

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	TArray<URouletteItem*> RouletteItemArray;
	

	//轮盘旋转速度
	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	float RotateSpeed;

	//轮盘旋转加速度
	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	float RotateAcceleration;

	//物品缩放速度
	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	float ItemScaleSpeed;

	UFUNCTION()
	void SetRoulette(FRouletteInfo RouletteInfo,TArray<FRouletteItemInfo> RouletteItemInfo);
	

	UFUNCTION()
	void SetRoulettePoint(float NextAngle);

	//设置上一个物品点缩放
	UFUNCTION(BlueprintCallable)
	void SetLastPointScale(float InDeltaTime);

	//设置当前物品点缩放
	UFUNCTION(BlueprintCallable)
	void SetCurPointScale(float InDeltaTime);

	UFUNCTION(BlueprintCallable)
	void SetRoulettePointPosition(float rotate,float InDeltaTime);
	
	//轮盘操作
	UFUNCTION(BlueprintCallable)
	void RouletteOperation(float rotate,float InDeltaTime);

	/*设置轮盘内的选项是物品还是对话
	 *PopupDir = 弹框弹出方向  向左是物品名框弹出  向右是对话框弹出
	 *ProfessionInfo = 需要设置的属性  根据向左向右来设置是物品属性还是对话属性
	 */  
	

	UFUNCTION(BlueprintCallable)
	void SetRouletteState(ERouletteState rouletteState){RouletteState = rouletteState;}

	UFUNCTION(BlueprintCallable)
	void SetRouletteStyle(ERouletteStyle rouletteStyle){RouletteStyle = rouletteStyle;}

	UFUNCTION(BlueprintCallable)
	ERouletteState GetRouletteState(){return RouletteState;}

	UFUNCTION(BlueprintCallable)
	void ChangeRotateSpeed() { RotateSpeed = 60.0f;}

	UFUNCTION(BlueprintCallable)
	void ResetRotateSpeed() { RotateSpeed = 20.0f;}

	UFUNCTION(BlueprintCallable)
	void ResetRotateAngle()
	{
		Roulette_Base->SetRenderTransformAngle(0.0f);
		RouletteRotationAngle = 0.0f;
	}

	UFUNCTION(BlueprintCallable)
	void RouletteDownRotate();

	UFUNCTION(BlueprintCallable)
	void RouletteUpRotate();

	UFUNCTION(BlueprintCallable)
	void RouletteStopRotate();

	
	UFUNCTION(BlueprintCallable)
	FVector2D SetRouletteItemPosition(float angle,float Diameter);

	/*UFUNCTION(BlueprintCallable)
	float GetRouletteItemPositionToAngle(FVector2D OnePosition,FVector2D TwoPosition);*/

	//获得当前选择的内容数据，其实是悬停对象的数据
	UFUNCTION(BlueprintCallable)
	FRouletteItemInfo GetCurSelectItemInfo()
	{
		return RouletteItemInfos[CurFocusIndex];
	}

	//获得指定Index的内容
	UFUNCTION(BlueprintCallable)           
	FRouletteItemInfo GetItemInfo(int Index);

	UFUNCTION(BlueprintCallable)
	URouletteItem* GetCurSelectItem()
	{
		if(RouletteItemArray.Num() > 0 && IsValid(RouletteItemArray[CurSelectPointIndex]))
		{
			return RouletteItemArray[CurSelectPointIndex];
		}
		else
		{
			return nullptr;
		}
		
	}
	
	UFUNCTION(BlueprintCallable)
	URouletteItem* GetCurEquippedItem();

	UFUNCTION(BlueprintCallable)
	void SetCurSelectItemInfo(FRouletteItemInfo RouletteItemInfo)
	{
		RouletteItemInfos[CurFocusIndex] = RouletteItemInfo;
	}

	UFUNCTION(BlueprintCallable)
	int GetCurFocusIndex() const
	{
		return CurFocusIndex;
	}

	UFUNCTION()
	int GetCreateCount() const 
	{
		return CreateCount;
	}

	UFUNCTION()
	void SetIsRouletteFocus(bool IsFocus){ IsRouletteFocus = IsFocus;}

	UFUNCTION()
	bool GetIsRouletteFocus() const { return IsRouletteFocus;}


	UFUNCTION()
	void RestRouletteAngleAndRouletteItemAngle();

	bool IsRotating() const{return RouletteState == ERouletteState::Turning;}
};


