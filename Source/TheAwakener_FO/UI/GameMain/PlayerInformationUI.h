// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Components/CanvasPanel.h"
#include "Components/Image.h"
#include "Components/Overlay.h"
#include "Components/ProgressBar.h"
#include "Components/TextBlock.h"
#include "TheAwakener_FO/UI/Base/BaseUI.h"
#include "PlayerInformationUI.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UPlayerInformationUI : public UBaseUI
{
	GENERATED_BODY()
	
	UPROPERTY()
	float SectionImageYMaxValue;

	float Test;

	virtual void NativeOnInitialized() override;
	virtual void NativeConstruct() override;
	virtual void NativeTick(const FGeometry& MyGeometry, float InDeltaTime) override;

public:

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UImage* PlayerInfoGroundImage;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UTextBlock* PlayerNameTextBlock;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UCanvasPanel* Exp_GroundCanvas;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UOverlay* Exp_GroundOverlay;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UImage* Exp_SlotImage;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UProgressBar* Exp_BarProgressBar;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UImage* Exp_SectionImage;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UTextBlock* PlayerLevelTextBlock;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UImage* Exp_SlotDecorationImage;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UOverlay* ProfessionGroundOverlay;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UImage* ProfessionPlateImage;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UImage* ProfessionIconImage;


	
	UFUNCTION()
	void UpdateExpBar() const;
	
};
