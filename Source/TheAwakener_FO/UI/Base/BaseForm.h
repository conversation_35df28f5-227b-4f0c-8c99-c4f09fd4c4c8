// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "BaseUI.h"
#include "TheAwakener_FO/GameFramework/AW_HUD.h"
#include "Containers/UnrealString.h"
#include "BaseForm.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UBaseForm : public UBaseUI
{
	GENERATED_BODY()
	
public:

	UPROPERTY(BlueprintReadWrite)
	AAW_HUD* Widget_HUD;

	UPROPERTY(BlueprintReadWrite)
	bool bSingle;

public:

	//打开时执行的函数
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Base|Default")
	void Open(AAW_HUD* T_HUD, const FString& ID);
	virtual void Open_Implementation(AAW_HUD* T_HUD, const FString& ID);

	//显示
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Base|Default")
	void Show();
	virtual void Show_Implementation();

	//隐藏
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Base|Default")
	void Hide();
	virtual void Hide_Implementation();

	//页面冻结
	UFUNCTION(BlueprintNativeEvent,BlueprintCallable, Category = "Base|Default")
	void Freeze(bool NeedToHide);
	virtual void Freeze_Implementation(bool NeedToHide);

	//恢复
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Base|Default")
	void Resume();
	virtual void Resume_Implementation();

	//隐藏（从栈中去除）
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Base|Default")
	void Remove();
	virtual void Remove_Implementation();

};
