// Fill out your copyright notice in the Description page of Project Settings.


#include "BaseForm.h"
#include "Blueprint/WidgetLayoutLibrary.h"
#include "Components/CanvasPanelSlot.h"

void UBaseForm::Open_Implementation(AAW_HUD* T_HUD, const FString& ID)
{
	this->Widget_HUD = T_HUD;
}

void UBaseForm::Show_Implementation()
{
	this->SetVisibility(ESlateVisibility::SelfHitTestInvisible);
}

void UBaseForm::Hide_Implementation()
{
	this->SetVisibility(ESlateVisibility::Hidden);
}

void UBaseForm::Freeze_Implementation(bool NeedToHide)
{
	UWidgetLayoutLibrary::SlotAsCanvasSlot(this)->SetZOrder(0);
	NeedToHide ? this->SetVisibility(ESlateVisibility::Hidden) : SetVisibility(ESlateVisibility::HitTestInvisible);
}

void UBaseForm::Resume_Implementation()
{
	this->SetVisibility(ESlateVisibility::Visible);
	UWidgetLayoutLibrary::SlotAsCanvasSlot(this)->SetZOrder(1);
}

void UBaseForm::Remove_Implementation()
{
	this->Hide();
	this->Widget_HUD->RemoveFromHUD(this);
	this->RemoveFromParent();
}