// Fill out your copyright notice in the Description page of Project Settings.


#include "BaseUI.h"

#include "Blueprint/WidgetBlueprintGeneratedClass.h"
#include "Blueprint/WidgetBlueprintLibrary.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"

void UBaseUI::NativeOnInitialized()
{
	Super::NativeOnInitialized();
}

void UBaseUI::NativeConstruct()
{
	Super::NativeConstruct();

	AWGameState = UGameplayFuncLib::GetAwGameState();

	if(GetAwGameInstance())
	{
		GetAwGameInstance()->LanguageChangeDelegate.AddUniqueDynamic(this,&UBaseUI::RefreshView);
	}
		
}

void UBaseUI::NativeTick(const FGeometry& MyGeometry, float InDeltaTime)
{
	Super::NativeTick(MyGeometry, InDeltaTime);
}

void UBaseUI::NativeDestruct()
{
	Super::NativeDestruct();
	if (OnUIDestroy.IsBound())
	{
		OnUIDestroy.Broadcast(this);
	}
	if(GetAwGameInstance())
	{
		GetAwGameInstance()->LanguageChangeDelegate.RemoveDynamic(this,&UBaseUI::RefreshView);
	}
}

UAudioComponent* UBaseUI::PlayUIAudioFX(USoundCue* Cue)
{
	UAudioComponent* TempAudioComponent = nullptr;
	if(Cast<UAwGameInstance>(GetWorld()->GetGameInstance()))
		TempAudioComponent = Cast<UAwGameInstance>(GetWorld()->GetGameInstance())->SFXManager->PlaySystemAudio(Cue, 1, 1, 0, true);
	return TempAudioComponent;
}

AAwCharacter* UBaseUI::GetWidgetPawn()
{
	if (this->WidgetPawn == nullptr)
	{
		this->WidgetPawn = Cast<AAwCharacter>(UGameplayFuncLib::GetAwGameState()->GetMyCharacter());
	}
	return this->WidgetPawn;
}

AAwPlayerController* UBaseUI::GetOwningAwController()
{
	UWidgetTree* tree = Cast<UWidgetTree>(GetOuter());
	if (UUserWidget* UserWidget = tree ? Cast<UUserWidget>(tree->GetOuter()) : nullptr)
	{
		auto pc = UserWidget->GetOwningPlayer();
		if (pc)
		{
			return Cast<AAwPlayerController>(pc);
		}
	}
	return nullptr;
}


UAwGameInstance* UBaseUI::GetAwGameInstance()
{ 
	if (this->AwGameInstance == nullptr)
	{
		this->AwGameInstance = Cast<UAwGameInstance>(GetWorld()->GetGameInstance());
		return  this->AwGameInstance;
	}
	return this->AwGameInstance;

}

void UBaseUI::InitInputMode()
{
	if (this->Implements<UAwUIInterface>())
	{
		IAwUIInterface::Execute_InitSelfState(this);
	}
	for (auto PC:UGameplayFuncLib::GetAllLocalAwPlayerControllers())
		UWidgetBlueprintLibrary::SetInputMode_GameAndUIEx(PC,this,EMouseLockMode::DoNotLock,false);
}

void UBaseUI::UnBindAnimEvent()
{
	if(IsValid(MyCurAnimation))
	{
		UnbindFromAnimationFinished(MyCurAnimation,FinishEvent);
	}
}

UWidgetAnimation* UBaseUI::GetNameWidgetAnimation(const FString& InWidgetAnimName)
{
	//获取Widget蓝图生成类
	if (UWidgetBlueprintGeneratedClass* WidgetBlueprintGenerated = Cast<UWidgetBlueprintGeneratedClass>(GetClass()))
	{
		//获取类中的WidgetAnim
		TArray<UWidgetAnimation*> TArrayAnimations = WidgetBlueprintGenerated->Animations;
 
		//通过传入的动画名，找到对应的WidgetAnim
		UWidgetAnimation** MyTempAnimation = TArrayAnimations.FindByPredicate(
				[&](const UWidgetAnimation* OurAnimation)
				{
						return OurAnimation->GetFName().ToString() == (InWidgetAnimName + FString("_INST"));
				});

		if (!MyTempAnimation) return nullptr;
		return *MyTempAnimation;
	}
	return nullptr;
}

bool UBaseUI::IsKeyHolding(FString ActionCmdId, int64 FirstInterval, int64 MinInterval)
{
	for (auto PlayerController : UGameplayFuncLib::GetAllLocalAwPlayerControllers())
	{
		if (PlayerController->CurCharacter)
		{
			if (PlayerController->CurCharacter->GetCmdComponent()->IsActionHoldingOccur(ActionCmdId, FirstInterval, MinInterval))
			{
				return true;
			}
		}
	}
	return false;
}

void UBaseUI::PlayWidgetAnim(const FString& InWidgetAnimName, float StartAtTime, int NumLoopsToPlay,
                             EUMGSequencePlayMode::Type PlayModeType, float PlayBackSpeed, bool RestoreState)
{
	//判断UI动画
	if (UWidgetAnimation* TempAnimation = this->GetNameWidgetAnimation(InWidgetAnimName))
	{
		BindToAnimationFinished(TempAnimation, FinishEvent);
		//播放此WidgetAnim
		
		PlayAnimation(TempAnimation,StartAtTime,NumLoopsToPlay,PlayModeType,PlayBackSpeed,RestoreState);
		MyCurAnimation = TempAnimation;
	}
}

void UBaseUI::UpdateShowMouse()
{
	for (auto pc : UGameplayFuncLib::GetAllLocalAwPlayerControllers())
	{
		if(UAwGameInstance::Instance->bIsGamepad)
		{
			pc->SetShowMouseCursor(false);
		}
		else
		{
			pc->SetShowMouseCursor(true);
		}
	}
}

void UBaseUI::BaseBack()
{
	if(UGameplayFuncLib::GetAwPlayerController(0))
	{
		UGameplayFuncLib::GetAwPlayerController(0)->SetShowMouseCursor(false);
		UGameplayFuncLib::GetAwPlayerController(0)->GameControlState = EGameControlState::Game;
	}
}


