// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Components/ListView.h"
#include "AwListView.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UAwListView : public UListView
{
	GENERATED_BODY()

public:
	virtual void OnSelectionChangedInternal(NullableItemType FirstSelectedItem) override;

	UPROPERTY()
	FOnListItemSelectionChangedDynamic OnItemSelectionChanged;

	
	/*UFUNCTION()
	float GetEntrySpacing(){return EntrySpacing;}

	UFUNCTION()
	void SetEntrySpacing(float EntrySpace){EntrySpacing = EntrySpace;}*/

	
};
