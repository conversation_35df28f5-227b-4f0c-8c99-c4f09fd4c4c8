// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "BaseUI.h"
#include "BasePanel.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UBasePanel : public UBaseUI
{
	GENERATED_BODY()
	
public:

	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Base|Default")
	void ShowPanel();
	virtual void ShowPanel_Implementation();

	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Base|Default")
	void HidePanel();
	virtual void HidePanel_Implementation();

};
