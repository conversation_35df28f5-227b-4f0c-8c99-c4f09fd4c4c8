// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "Components/CanvasPanelSlot.h"
#include "Components/TextBlock.h"
#include "TheAwakener_FO/GamePlay/Characters/HitBox/CharacterHitBox.h"
#include "TheAwakener_FO/GameFramework/AwPlayerController.h"
#include "PopText.generated.h"

class  AAwCharacter;

/**
 * 跳数字的东西需要的数据支持
 */
USTRUCT()
struct FPopTextInfo
{
	GENERATED_BODY()
public:
	//跳的数字
	UPROPERTY()
	UTextBlock* TextBlock = nullptr;

	UPROPERTY()
	UCanvasPanelSlot* Slot = nullptr;
	
	//跳了多久了
	UPROPERTY()
	float TimeElapsed = 0;

	//起始坐标（屏幕坐标）
	UPROPERTY()
	FVector2D StartLocation = FVector2D::ZeroVector;
};

/**
 * 显示所有视野范围内跳数字的东西 
 */
UCLASS()
class THEAWAKENER_FO_API UPopText : public UUserWidget
{
	GENERATED_BODY()

	virtual void NativeConstruct() override;

	//virtual void OnLevelRemovedFromWorld(ULevel* InLevel, UWorld* InWorld) override;
	
private:
	UPROPERTY()
	UPanelWidget* CanvasPanel;

	//每一条都是一个跳数字
	UPROPERTY()
	TArray<FPopTextInfo> PopTextInfos;

	static void GetScreenLocation(AAwPlayerController* Controller,const AAwCharacter* Cha, FVector2D* ScreenLocation);
	virtual void NativeTick(const FGeometry& MyGeometry, float InDeltaTime) override;
	
	void GatherPopText(FVector AtLocation, FString Text, FLinearColor TextColor);
public:
	
	/**
	 *跳一个数字
	 *@param OnCharacter 在谁身上跳？也就是会跟着谁，这个谁没了，数字也就没了
	 *@param Text 说是数字，他其实是字符串，跳个“操你妈”也是可以的
	 *@param TextColor 跳出数字的颜色
	 */
	void PopText(AAwCharacter* OnCharacter, FString Text, FLinearColor TextColor = FLinearColor::Red);

	/**
	 *跳一个数字
	 *@param OnCharacterHitBox 在那个受击框
	 *@param Text 说是数字，他其实是字符串，跳个“操你妈”也是可以的
	 *@param TextColor 跳出数字的颜色
	 */
	void PopText(USceneComponent* OnCharacterHitBox, FString Text, FLinearColor TextColor = FLinearColor::Red);

	
};
