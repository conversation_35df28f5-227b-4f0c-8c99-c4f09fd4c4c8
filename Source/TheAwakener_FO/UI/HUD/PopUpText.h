// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "AwShowingText.h"
#include "Components/WidgetComponent.h"
#include "GameFramework/Actor.h"
#include "PopUpText.generated.h"

UCLASS()
class THEAWAKENER_FO_API APopUpText : public AActor
{
	GENERATED_BODY()

private:
	UPROPERTY()
	UAwShowingText* TextComp;

protected:
	// Called when the game starts or when spawned
	virtual void BeginPlay() override;

public:
	APopUpText();
	virtual void Tick(float DeltaTime) override;

	UPROPERTY()
	bool Working = false;
	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UWidgetComponent* WidgetComponent;
	void PopText(FVector Position, FString Text, int TextSize = 80, FLinearColor TextColor = FLinearColor::Red);
};

/**
 * 已经存在的PopText
 */
USTRUCT(BlueprintType)
struct FPopTextRecorder
{
	GENERATED_BODY()
public:
	UPROPERTY()
	int64 Timestamp = 0;	//何时创建的

	UPROPERTY()
	int Priority = 0;		//优先级

	UPROPERTY()
	APopUpText* PopText = nullptr;	//东西

	FPopTextRecorder(){}
	FPopTextRecorder(APopUpText* Text, int64 CreateTime, int AsPriority = 0):
		Timestamp(CreateTime), Priority(AsPriority), PopText(Text){};
};

//用来创建跳数字的信息
USTRUCT(BlueprintType)
struct FPopTextLauncher
{
	GENERATED_BODY()
public:
	//是属于那个的
	UPROPERTY()
	FString Id = "";
	
	//跳数字的颜色
	UPROPERTY()
	FColor Color = FColor::Green;

	//跳数字的大小
	UPROPERTY()
	int Size = 30;

	//优先级
	UPROPERTY()
	int Priority = 0;

	FPopTextLauncher(){}
	FPopTextLauncher(FString TypeId, FColor UseColor, int FontSize, int ShowPriority):
		Id(TypeId), Color(UseColor), Size(FontSize), Priority(ShowPriority){};

	static FPopTextLauncher FromJson(TSharedPtr<FJsonObject> JsonObj);
};
