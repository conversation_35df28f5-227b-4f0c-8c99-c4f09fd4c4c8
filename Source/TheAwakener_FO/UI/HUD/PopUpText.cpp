// Fill out your copyright notice in the Description page of Project Settings.


#include "PopUpText.h"

#include "Components/WidgetComponent.h"
#include "TheAwakener_FO/FunctionLibrary/DataFuncLib.h"


// Sets default values
APopUpText::APopUpText()
{
	// Set this actor to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryActorTick.bCanEverTick = true;
}

// Called when the game starts or when spawned
void APopUpText::BeginPlay()
{
	Super::BeginPlay();

	WidgetComponent = Cast<UWidgetComponent>(GetComponentByClass(UWidgetComponent::StaticClass()));
	this->TextComp = Cast<UAwShowingText>(WidgetComponent->GetWidget());
}

// Called every frame
void APopUpText::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);

	if (!TextComp)
	{
		Working = false;
		this->SetActorHiddenInGame(true);
		return;
	}

	TextComp->DoTick(DeltaTime);
	this->Working =  TextComp->Working;
	if (this->Working == false)
	{
		this->SetActorHiddenInGame(true);
	}
}

void APopUpText::PopText(FVector Position, FString Text, int TextSize, FLinearColor TextColor)
{
	this->SetActorLocation(Position);
	if (this->TextComp)
	{
		this->TextComp->PopText(Text, TextSize, TextColor);
	}
	Working = true;
	this->SetActorHiddenInGame(false);
}

FPopTextLauncher FPopTextLauncher::FromJson(TSharedPtr<FJsonObject> JsonObj)
{
	return FPopTextLauncher(
		UDataFuncLib::AwGetStringField(JsonObj, "Id", ""),
		FColor(
		UDataFuncLib::AwGetNumberField(JsonObj, "R", 0),
		UDataFuncLib::AwGetNumberField(JsonObj, "G", 0),
		UDataFuncLib::AwGetNumberField(JsonObj, "B", 0),
		255
		),
		UDataFuncLib::AwGetNumberField(JsonObj, "Size", 30),
		UDataFuncLib::AwGetNumberField(JsonObj, "Priority", 0)
	);
}