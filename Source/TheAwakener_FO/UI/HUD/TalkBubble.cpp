// Fill out your copyright notice in the Description page of Project Settings.


#include "TalkBubble.h"

#include "Components/WidgetComponent.h"


// Sets default values
ATalkBubble::ATalkBubble()
{
	// Set this actor to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryActorTick.bCanEverTick = true;
}

// Called when the game starts or when spawned
void ATalkBubble::BeginPlay()
{
	Super::BeginPlay();
	
	const UWidgetComponent* WidgetComponent = Cast<UWidgetComponent>(GetComponentByClass(UWidgetComponent::StaticClass()));
	this->TextComp = Cast<UTalkBubbleText>(WidgetComponent->GetWidget());
}

// Called every frame
void ATalkBubble::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);

	if (LifeTime > 0)
	{
		LifeTime -= DeltaTime;
		this->SetActorLocation(
			FollowCha ? 
				(FollowCha->TextBubblePoint ? FollowCha->TextBubblePoint->GetComponentLocation() : FollowCha->GetActorLocation()) :
				this->GetActorLocation()
		);
	}else
	{
		this->SetActorHiddenInGame(true);
	}
}

void ATalkBubble::ShowText(AAwCharacter* OnCha, FString GuyName, FString Text, float LiveInSec)
{
	this->LifeTime = LiveInSec;
	this->FollowCha = OnCha;
	if (this->TextComp)
	{
		TextComp->NameText->SetText(FText::FromString(GuyName));
		TextComp->DialogText->SetText(FText::FromString(Text));
	}
	this->SetActorHiddenInGame(false);
}
