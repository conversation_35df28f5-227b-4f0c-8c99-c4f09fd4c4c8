// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TalkBubbleText.h"
#include "GameFramework/Actor.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "TalkBubble.generated.h"

UCLASS()
class THEAWAKENER_FO_API ATalkBubble : public AActor
{
	GENERATED_BODY()

private:
	//还要显示多久
	UPROPERTY()
	float LifeTime = 0;
	UPROPERTY()
	UTalkBubbleText* TextComp = nullptr;
	UPROPERTY()
	AAwCharacter* FollowCha = nullptr;

protected:
	virtual void BeginPlay() override;

public:
	ATalkBubble();
	virtual void Tick(float DeltaTime) override;

	void ShowText(AAwCharacter* OnCha, FString GuyName, FString Text, float LiveInSec);
	
	bool IsFree() const {return this->LifeTime <= 0;}
	bool IsMine(AAwCharacter* Me) const {return Me!= nullptr && FollowCha!= nullptr && Me == FollowCha;}
};
