// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "Components/Button.h"
#include "Components/CanvasPanel.h"
#include "Components/TextBlock.h"
#include "TheAwakener_FO/GameFramework/Input/GameControlState.h"
#include "UObject/Object.h"
#include "MessageDialog.generated.h"

/**
 *  对话框 Msgdlg
 */
UCLASS()
class THEAWAKENER_FO_API UMessageDialog : public UUserWidget
{
	GENERATED_BODY()
private:
	//确定按钮
	UPROPERTY()
	UButton* BtnYes;
	//否定按钮
	UPROPERTY()
	UButton* BtnNo;
	//取消按钮
	UPROPERTY()
	UButton* BtnCancel;

	UPROPERTY()
	UTextBlock* TextYes;
	UPROPERTY()
	UTextBlock* TextNo;
	UPROPERTY()
	UTextBlock* TextCancel;

	//整个面板
	UCanvasPanel* Panel;

	//文字
	UPROPERTY()
	UTextBlock* TextComp;

	
	UFUNCTION()
	void YesButtonClick();
	UFUNCTION()
	void NoButtonClick();
	UFUNCTION()
	void CancelButtonClick();

	//Yes按钮的回调 (TArray<FString> Params)=>void
	UPROPERTY()
	TArray<FString> YesFunc;
	//No按钮的回调 (TArray<FString> Params)=>void
	UPROPERTY()
	TArray<FString> NoFunc;
	//Cancel按钮的回调 (TArray<FString> Params)=>void
	UPROPERTY()
	TArray<FString> CancelFunc;

	//在进入这个状态之前的状态
	UPROPERTY()
	EGameControlState WasGameState;
	
	virtual void NativeOnInitialized() override;
	virtual void NativeTick(const FGeometry& MyGeometry, float InDeltaTime) override;
public:
	/**
	 * 显示对话框，界面上必须本来就放了这些按钮才有意义
	 * @param DialogText 对话框文字的Key，最后是Chinese里面的东西
	 * @param Yes 是Yes按钮要执行的所有回调函数 (TArray<FString> Params)=>void
	 * @param No 是No按钮要执行的所有回调 (TArray<FString> Params)=>void
	 * @param Cancel 是Cancel按钮要执行的所有回调 (TArray<FString> Params)=>void
	 */
	void Show(FString DialogText, FString YesKey, FString NoKey, FString CancelKey, TArray<FString> Yes, TArray<FString> No, TArray<FString> Cancel);

	/**
	 * 关闭这个对话框
	 */
	void Hide();

	UFUNCTION(BlueprintImplementableEvent,BlueprintCallable)
	void OpenLevel();
};
