// Fill out your copyright notice in the Description page of Project Settings.


#include "InteractButton.h"

#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GameFramework/Input/ActionCmd.h"

void UInteractButton::NativeOnInitialized()
{
	Super::NativeOnInitialized();
	
	this->TextIcon = Cast<UTextBlock>(GetWidgetFromName(TEXT("TextKeyIcon")));
	this->KeyIcon = Cast<UImage>(GetWidgetFromName(TEXT("Img_KeyIcon")));
	OnChangeKeyMapSettings();
}

int32 UInteractButton::NativePaint(const FPaintArgs& Args, const FGeometry& AllottedGeometry, const FSlateRect& MyCullingRect, FSlateWindowElementList& OutDrawElements, int32 LayerId, const FWidgetStyle& InWidgetStyle, bool bParentEnabled) const
{
	// OnChangeKeyMapSettings();
	return Super::NativePaint(Args, AllottedGeometry, MyCullingRect, OutDrawElements, LayerId, InWidgetStyle, bParentEnabled);
}

void UInteractButton::NativeTick(const FGeometry& MyGeometry, float InDeltaTime)
{
	Super::NativeTick(MyGeometry, InDeltaTime);
	const bool IsFromGamePad = UAwGameInstance::Instance->bIsGamepad;

	if (this->TextIcon) TextIcon->SetVisibility(IsFromGamePad ? ESlateVisibility::Hidden : ESlateVisibility::HitTestInvisible);
	if (this->KeyIcon) KeyIcon->SetVisibility(IsFromGamePad ? ESlateVisibility::HitTestInvisible : ESlateVisibility::Hidden);
	
}


void UInteractButton::OnChangeKeyMapSettings() const
{
	if (!UGameplayFuncLib::GetDataManager())
		return;
	const FActionCmd ActionCmd = UGameplayFuncLib::GetDataManager()->GetActionCmdById("Interactive");
	if (ActionCmd.ActionKey.Num() <= 0) return;
	const FDefaultKeyMapping KeyMapping = UGameplayFuncLib::GetDataManager()->GetKeyMappingById(ActionCmd.ActionKey[0]);
	if (KeyIcon && KeyMapping.Gamepad_Icon.IsEmpty() == false)
	{
		UTexture2D* IconTexture = LoadObject<UTexture2D>(nullptr,*UResourceFuncLib::GetAssetPath( KeyMapping.Gamepad_Icon));
		if (IconTexture)
		{
			KeyIcon->SetBrushFromTexture(IconTexture);
		}
	}
	if (TextIcon && KeyMapping.Keyboard_Icon.IsEmpty() == false)
	{
		this->TextIcon->SetText(FText::FromString(KeyMapping.Keyboard_Icon));
	}
}