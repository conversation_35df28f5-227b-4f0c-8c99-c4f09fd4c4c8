// Fill out your copyright notice in the Description page of Project Settings.


#include "MessageDialog.h"

#include "TheAwakener_FO/FunctionLibrary/CallFuncLib.h"
#include "TheAwakener_FO/FunctionLibrary/DataFuncLib.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"

void UMessageDialog::NativeOnInitialized()
{
	Super::NativeOnInitialized();

	Panel = Cast<UCanvasPanel>(GetWidgetFromName(TEXT("MainPanel")));

	TextComp = Cast<UTextBlock>(GetWidgetFromName(TEXT("TextArea")));
	
	BtnYes = Cast<UButton>(GetWidgetFromName(TEXT("YesButton")));
	if (BtnYes) BtnYes->OnClicked.AddDynamic(this, &UMessageDialog::YesButtonClick);

	TextYes = Cast<UTextBlock>(GetWidgetFromName(TEXT("ButtonText_Yes")));
	
	BtnNo = Cast<UButton>(GetWidgetFromName(TEXT("NoButton")));
	if (BtnNo) BtnNo->OnClicked.AddDynamic(this, &UMessageDialog::NoButtonClick);

	TextNo = Cast<UTextBlock>(GetWidgetFromName(TEXT("ButtonText_No")));

	BtnCancel = Cast<UButton>(GetWidgetFromName(TEXT("CancelButton")));
	if (BtnCancel) BtnCancel->OnClicked.AddDynamic(this, &UMessageDialog::CancelButtonClick);

	TextCancel = Cast<UTextBlock>(GetWidgetFromName(TEXT("ButtonText_Cancel")));
}

void UMessageDialog::YesButtonClick()
{
	//优先把状态还回去
	UGameplayFuncLib::GetPlayerControllerByWidget(this)->GameControlState = WasGameState;
	for (const FString Func : YesFunc)
	{
		const FJsonFuncData FuncData = UCallFuncLib::StringToJsonFuncData(Func);
		UFunction* ToCall = UCallFuncLib::JsonFuncToUFunc(FuncData);
		if (ToCall)
		{
			struct
			{
				TArray<FString> Params;
			}FuncParam;
			FuncParam.Params = FuncData.Params;
			this->ProcessEvent(ToCall, &FuncParam);
		}
	}
	this->Hide();
}

void UMessageDialog::NoButtonClick()
{
	//优先把状态还回去
	UGameplayFuncLib::GetPlayerControllerByWidget(this)->GameControlState = WasGameState;
	for (const FString Func : NoFunc)
	{
		const FJsonFuncData FuncData = UCallFuncLib::StringToJsonFuncData(Func);
		UFunction* ToCall = UCallFuncLib::JsonFuncToUFunc(FuncData);
		if (ToCall)
		{
			struct
			{
				TArray<FString> Params;
			}FuncParam;
			FuncParam.Params = FuncData.Params;
			this->ProcessEvent(ToCall, &FuncParam);
		}
	}
	this->Hide();
}

void UMessageDialog::CancelButtonClick()
{
	//优先把状态还回去
	UGameplayFuncLib::GetPlayerControllerByWidget(this)->GameControlState = WasGameState;
	for (const FString Func : CancelFunc)
	{
		const FJsonFuncData FuncData = UCallFuncLib::StringToJsonFuncData(Func);
		UFunction* ToCall = UCallFuncLib::JsonFuncToUFunc(FuncData);
		if (ToCall)
		{
			struct
			{
				TArray<FString> Params;
			}FuncParam;
			FuncParam.Params = FuncData.Params;
			this->ProcessEvent(ToCall, &FuncParam);
		}
	}
	this->Hide();
}

void UMessageDialog::Show(FString DialogText, FString YesKey, FString NoKey, FString CancelKey,  TArray<FString> Yes, TArray<FString> No, TArray<FString> Cancel)
{
	WasGameState = UGameplayFuncLib::GetPlayerControllerByWidget(this)->GameControlState;
	UGameplayFuncLib::GetPlayerControllerByWidget(this)->GameControlState = EGameControlState::MessageDialog;
	this->YesFunc = Yes;
	this->NoFunc =No;
	this->CancelFunc = Cancel;
	if (TextComp)
	{
		TextComp->SetText(FText::FromString(UGameplayFuncLib::GetAwDataManager()->GetTextByKey(DialogText)));
	}
	if (TextYes) TextYes->SetText(FText::FromString(UGameplayFuncLib::GetAwDataManager()->GetTextByKey(YesKey)));
	if (TextNo) TextNo->SetText(FText::FromString(UGameplayFuncLib::GetAwDataManager()->GetTextByKey(NoKey)));
	if (TextCancel) TextCancel->SetText(FText::FromString(UGameplayFuncLib::GetAwDataManager()->GetTextByKey(CancelKey)));
}

void UMessageDialog::Hide()
{
	this->YesFunc.Empty();
	this->NoFunc.Empty();
	this->CancelFunc.Empty();
	this->RemoveFromParent();
}

void UMessageDialog::NativeTick(const FGeometry& MyGeometry, float InDeltaTime)
{
	Super::NativeTick(MyGeometry, InDeltaTime);

	if (UGameplayFuncLib::GetAwGameState()->GetMyCharacter()->IsActionOccur("MessageDialog_Yes", EAwInputState::Press, true))
	{
		UGameplayFuncLib::PlayUIAudio("ConfirmKey_Yes");
		YesButtonClick();
	}else if  (UGameplayFuncLib::GetAwGameState()->GetMyCharacter()->IsActionOccur("MessageDialog_Cancel", EAwInputState::Press, true))
	{
		UGameplayFuncLib::PlayUIAudio("ConfirmKey_No");
		CancelButtonClick();
	}
}
