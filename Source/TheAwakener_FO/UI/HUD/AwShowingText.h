// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "Components/CanvasPanel.h"
#include "Components/CanvasPanelSlot.h"
#include "Components/TextBlock.h"
#include "AwShowingText.generated.h"

/**
 * 专门显示一个Text的Widget，会被用于跳数字
 */
UCLASS()
class THEAWAKENER_FO_API UAwShowingText : public UUserWidget
{
	GENERATED_BODY()
private:
	virtual void NativeOnInitialized() override;

	UPROPERTY()
	float TimeElapsed = 0;

	UPROPERTY()
	bool Started = false;

	UPROPERTY()
	UCanvasPanelSlot* CanvasPanelSlot = nullptr;
public:
	UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (BindWidget))
	UCanvasPanel* SubPanel;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TMap<FString,float> TimeCostMap;
	UPROPERTY()
	UTextBlock* TextArea = nullptr;

	void InitTimeCost();
	UPROPERTY()
	bool Working = false;

	UFUNCTION()
	void PopText(FString Text, int TextSize = 80, FLinearColor TextColor = FLinearColor::Red);

	void DoTick(float InDeltaTime);
};
