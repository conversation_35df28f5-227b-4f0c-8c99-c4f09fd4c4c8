// Fill out your copyright notice in the Description page of Project Settings.


#include "AwShowingText.h"

#include "Blueprint/WidgetLayoutLibrary.h"
#include "Engine/Canvas.h"


void UAwShowingText::NativeOnInitialized()
{
	Super::NativeOnInitialized();

	this->TextArea = Cast<UTextBlock>(GetWidgetFromName(TEXT("Text")));
	if(SubPanel) CanvasPanelSlot = Cast<UCanvasPanelSlot>(SubPanel->Slot);

	InitTimeCost();
}

void UAwShowingText::DoTick(float InDeltaTime)
{
	if (Started == false || !this->TextArea) return;
	TArray<float>TimeCost;
	TimeCostMap.GenerateValueArray(TimeCost);
	
	int PIndex = 0;
	float CurTime = this->TimeElapsed;
	while (PIndex < TimeCost.Num() && CurTime >= TimeCost[PIndex])
	{
		CurTime -= TimeCost[PIndex];
		PIndex ++;
	}
    
	if (PIndex >= TimeCost.Num())
	{
		Working = false;
		Started = false;
		return;	//时间结束终止掉
	}

	const float PTime = TimeCost[PIndex];
	const float PPercent = FMath::Clamp(CurTime / PTime, 0.000f, 1.000f);
	const float ScaleShown = PIndex <= 0 ? (1.1f * PPercent) : (PIndex > 1 ? 1 : (1.1f - 0.1f * PPercent));
	const float FlyHeight =  -100.0f / UWidgetLayoutLibrary::GetViewportScale(GWorld);	//往上飞这么多厘米
	switch (PIndex)
	{
	case 0:	//开始冒出数字阶段
	case 1:	//数字回正尺寸
	case 2:	//停在那里啥也不做，就直接Break
		TextArea->SetRenderScale(FVector2D(
			ScaleShown, ScaleShown
		));
		break;
	case 3:
		//开始往上飞并且准备消失
		
		this->CanvasPanelSlot->SetPosition(
				FVector2D(
					0,
					PPercent * FlyHeight
				)	
			);
		TextArea->SetRenderOpacity(1 - PPercent * 0.8f);//最淡还得有点（？）
		break;
	default:break;
	}
    
	TimeElapsed += InDeltaTime;
}


void UAwShowingText::InitTimeCost()
{
	TimeCostMap.Empty();
	TimeCostMap.Add("ScaleTime",0.25f);	//到达scale最大值（1.1倍）的秒数
	TimeCostMap.Add("ScaleNormalTime",0.1f);	//追加这么多秒到正常Scale
	TimeCostMap.Add("StayTime",0.5f);		//停留这么多秒开始飞行
	TimeCostMap.Add("DisappearTime",0.4f);		//飞走花费的时间
}

void UAwShowingText::PopText(FString Text, int TextSize, FLinearColor TextColor)
{
	TextArea->SetText(FText::FromString(Text));
	FSlateFontInfo Temp = TextArea->GetFont();
	Temp.Size = TextSize;
	// Temp.OutlineSettings.OutlineSize = FMath::Max(2, FMath::RoundToInt(TextSize*1.f / 8));
	TextArea->SetFont(Temp);
	TextArea->SetColorAndOpacity(FSlateColor(TextColor));
	TextArea->SetRenderScale(FVector2D::ZeroVector);
	TextArea->SetRenderOpacity(1);

	CanvasPanelSlot->SetPosition(FVector2D::ZeroVector);

	InitTimeCost();
	
	Started = true;
	Working = true;
	TimeElapsed = 0;
}
