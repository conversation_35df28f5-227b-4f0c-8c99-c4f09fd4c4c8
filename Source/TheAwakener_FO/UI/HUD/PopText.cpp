// Fill out your copyright notice in the Description page of Project Settings.


#include "PopText.h"

#include "Blueprint/WidgetLayoutLibrary.h"
#include "Blueprint/WidgetTree.h"
#include "Components/CanvasPanelSlot.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"

void UPopText::NativeConstruct()
{
	Super::NativeConstruct();
}

/*
void UPopText::OnLevelRemovedFromWorld(ULevel* InLevel, UWorld* InWorld)
{
	// Super::OnLevelRemovedFromWorld(InLevel, InWorld);
}
*/
void UPopText::NativeTick(const FGeometry& MyGeometry, float InDeltaTime)
{
	Super::NativeTick(MyGeometry, InDeltaTime);
	
	

	TArray<float> TimeCost;
    TimeCost.Add(0.12f);	//到达scale最大值（1.1倍）的秒数
    TimeCost.Add(0.02f);	//追加这么多秒到正常Scale
    TimeCost.Add(0.5f);		//停留这么多秒开始飞行
    TimeCost.Add(0.4f);		//飞走花费的时间
    
    int i = 0;
    while (i < this->PopTextInfos.Num())
    {
    	int PIndex = 0;
    	float CurTime = PopTextInfos[i].TimeElapsed;
    	while (PIndex < TimeCost.Num() && CurTime >= TimeCost[PIndex])
    	{
    		CurTime -= TimeCost[PIndex];
    		PIndex ++;
    	}

    	
    	if (PIndex >= TimeCost.Num())
    	{
    		PopTextInfos[i].TextBlock->RemoveFromParent();
    		PopTextInfos[i].TextBlock = nullptr;
    		PopTextInfos[i].Slot = nullptr;
    		PopTextInfos.RemoveAt(i);
    		continue;
    	}
    
    	const float PTime = TimeCost[PIndex];
    	const float PPercent = FMath::Clamp(CurTime / PTime, 0.000f, 1.000f);
    	const float ScaleShown = PIndex <= 0 ? (1.1f * PPercent) : (PIndex > 1 ? 1 : (1.1f - 0.1f * PPercent));
    	const float FlyHeight = -100.0f / UWidgetLayoutLibrary::GetViewportScale(GWorld);	//往上飞这么多厘米
    	switch (PIndex)
    	{
    	case 0:	//开始冒出数字阶段
    	case 1:	//数字回正尺寸
    	case 2:	//停在那里啥也不做，就直接Break
    		PopTextInfos[i].TextBlock->SetRenderScale(FVector2D(
    			ScaleShown, ScaleShown
    		));
    		break;
    	case 3:
    		//开始往上飞并且准备消失
    		PopTextInfos[i].Slot->SetPosition(
    			FVector2D(
    				PopTextInfos[i].StartLocation.X,
    				PopTextInfos[i].StartLocation.Y + PPercent * FlyHeight
    			)	
    		);
    		PopTextInfos[i].TextBlock->SetRenderOpacity(1 - PPercent * 0.8f);//最淡还得有点（？）
    		break;
    	default:break;
    	}
    	
    	PopTextInfos[i].TimeElapsed += InDeltaTime;
    	i++;
    }
	
	// int i = 0;
	// while (i < this->PopTextInfos.Num())
	// {
	// 	const float DeadTime = 0.900f;
	// 	const float FlyToMaxHeight = 0.550f;
	// 	const float TotalMoveX = 140 / UWidgetLayoutLibrary::GetViewportScale(GWorld);
	// 	const float TotalMoveY = -100 / UWidgetLayoutLibrary::GetViewportScale(GWorld);
	// 	const float AlphaMin = 0.350f;
	// 	if (PopTextInfos[i].TimeElapsed >= DeadTime)
	// 	{
	// 		PopTextInfos[i].TextBlock->RemoveFromParent();
	// 		PopTextInfos[i].TextBlock = nullptr;
	// 		PopTextInfos[i].Slot = nullptr;
	// 		PopTextInfos.RemoveAt(i);
	// 	}else
	// 	{
	// 		if (PopTextInfos[i].TimeElapsed <= FlyToMaxHeight)
	// 		{
	// 			const float Percentage = FMath::Pow(PopTextInfos[i].TimeElapsed / FlyToMaxHeight, 2);
	// 			PopTextInfos[i].Slot->SetPosition(
	// 				FVector2D(
	// 				TotalMoveX * Percentage + PopTextInfos[i].StartLocation.X,
	// 				TotalMoveY * Percentage + PopTextInfos[i].StartLocation.Y
	// 				)
	// 			);
	// 		}else
	// 		{
	// 			PopTextInfos[i].Slot->SetPosition(
	// 				FVector2D(
	// 				TotalMoveX + PopTextInfos[i].StartLocation.X,
	// 				TotalMoveY + PopTextInfos[i].StartLocation.Y
	// 				)
	// 			);
	// 			const float Percentage = 1.000f - FMath::Pow((DeadTime - PopTextInfos[i].TimeElapsed) / (DeadTime - FlyToMaxHeight), 2);
	// 			PopTextInfos[i].TextBlock->RenderOpacity = (1.000f - AlphaMin) * Percentage + AlphaMin;
	// 		}
	// 		PopTextInfos[i].TimeElapsed += InDeltaTime;
	// 		i ++;
	// 	}
	// }

	
}

void UPopText::PopText(AAwCharacter* OnCharacter, FString Text, FLinearColor TextColor)
{
	if(!CanvasPanel) CanvasPanel = Cast<UPanelWidget>(GetRootWidget());
	
	if (!OnCharacter) return;
	this->GatherPopText(OnCharacter->GetActorLocation(), Text, TextColor);
}

void UPopText::PopText(USceneComponent* OnCharacterHitBox, FString Text, FLinearColor TextColor)
{
	if(!CanvasPanel) CanvasPanel = Cast<UPanelWidget>(GetRootWidget());
	
	if (!OnCharacterHitBox) return;
	this->GatherPopText(OnCharacterHitBox->GetComponentLocation(), Text, TextColor);
}

void UPopText::GatherPopText(FVector AtLocation, FString Text, FLinearColor TextColor)
{
	FPopTextInfo PInfo;
	
	PInfo.TextBlock = WidgetTree->ConstructWidget<UTextBlock>(UTextBlock::StaticClass());
	PInfo.TextBlock->SetText(FText::FromString(Text));
	FSlateFontInfo Temp = PInfo.TextBlock->GetFont();
	Temp.Size = 80;
	Temp.OutlineSettings.OutlineSize = 5;
	PInfo.TextBlock->SetFont(Temp);
	PInfo.TextBlock->SetColorAndOpacity(FSlateColor(TextColor));
	PInfo.TextBlock->SetRenderScale(FVector2D::ZeroVector);
	this->CanvasPanel->AddChild(PInfo.TextBlock);
	
	PInfo.Slot = Cast<UCanvasPanelSlot>(PInfo.TextBlock->Slot);
	PInfo.Slot->SetAutoSize(true);
	PInfo.Slot->SetAlignment(FVector2D(0.5f, 1.f));
	FVector2D Pos;
	UGameplayFuncLib::GetPlayerControllerByWidget(this)->ProjectWorldLocationToScreen(
		AtLocation , Pos);
	Pos /= UWidgetLayoutLibrary::GetViewportScale(GWorld);
	PInfo.Slot->SetPosition(Pos);
	PInfo.StartLocation = Pos;
	
	PInfo.TimeElapsed = 0;
	this->PopTextInfos.Add(PInfo);
}

void UPopText::GetScreenLocation(AAwPlayerController* Controller,const AAwCharacter* Cha, FVector2D* ScreenLocation)
{
	Controller->ProjectWorldLocationToScreen(
		Cha->GetActorLocation() , *ScreenLocation);
}