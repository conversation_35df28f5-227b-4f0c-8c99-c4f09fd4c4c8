// Fill out your copyright notice in the Description page of Project Settings.


#include "AwakeSkillMain.h"

#include "AwakeSkillOptions.h"
#include "Components/UniformGridSlot.h"
#include "Kismet/KismetArrayLibrary.h"
#include "Blueprint/WidgetBlueprintLibrary.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GameFramework/Manager/AwRogueDataSystem.h"

void UAwakeSkillMain::NativeOnInitialized()
{
	Super::NativeOnInitialized();
	// AAwPlayerState* PlayerState = UGameplayFuncLib::GetMyAwPlayerState();
	// AwakeSkillInfo_CanvasPanel = Cast<UCanvasPanel>(GetWidgetFromName(TEXT("AwakeSkillInfoCanvasPanel")));
	// AwakeSkillOptions_UGP = Cast<UUniformGridPanel>(GetWidgetFromName(TEXT("AwakeSkillOptionsUGP")));
	// AwakeSkillDesc_CanvasPanel = Cast<UCanvasPanel>(GetWidgetFromName(TEXT("AwakeSkillDescCanvasPanel")));
	// AwakeSkillDescription_Text = Cast<UTextBlock>(GetWidgetFromName(TEXT("AwakeSkillDescriptionText")));
	// ConfirmationTips_CanvasPanel = Cast<UCanvasPanel>(GetWidgetFromName(TEXT("ConfirmationTipsCanvasPanel")));
	// Confirm_Button = Cast<UButton>(GetWidgetFromName(TEXT("Confirm_BT")));
	// Cancel_Button = Cast<UButton>(GetWidgetFromName(TEXT("Cancel_BT")));
}

void UAwakeSkillMain::NativeTick(const FGeometry& MyGeometry, float InDeltaTime)
{
	Super::NativeTick(MyGeometry, InDeltaTime);
	UpdateShowMouse();
	
	return;
	
	GameState = UGameplayFuncLib::GetAwGameState();
	UpdateShowMouse();

	if (GameState)
	{
		AAwCharacter* Me = GameState->GetMyCharacter();
		if(Me->GetCmdComponent()->IsActionOccur("AwakeSkill_Left",EAwInputState::Press,true) ||
			Me->GetCmdComponent()->IsActionOccur("AwakeSkill_Left",EAwInputState::Hold,true))
		{
			AwakeSkillSelectedLeft();
		}
		else if(Me->GetCmdComponent()->IsActionOccur("AwakeSkill_Right",EAwInputState::Press,true) ||
			Me->GetCmdComponent()->IsActionOccur("AwakeSkill_Right",EAwInputState::Hold,true))
		{
			AwakeSkillSelectedRight();
		}
		else if(Me->GetCmdComponent()->IsActionOccur("AwakeSkill_Confirm",EAwInputState::Press,true))
		{
			if(GetEquippedAwakeSkillIndex() == GetCurAwakeSkillIndex())
				return;
			AwakeSkillSelectedConfirm();
			ShowOrHideConfirmationTips();
		}
		else if(Me->GetCmdComponent()->IsActionOccur("AwakeSkill_Refuse",EAwInputState::Press,true))
		{
			Back();
		}

		if(Me->GetCmdComponent()->IsActionOccur("Reminder_Confirm",EAwInputState::Press,true))
		{
			Confirm_BTOnClicked();
		}
		else if(Me->GetCmdComponent()->IsActionOccur("Reminder_Refuse",EAwInputState::Press,true))
		{
			Cancel_BTOnClicked();
		}
		
	}
}

void UAwakeSkillMain::NativeDestruct()
{
	Super::NativeDestruct();
	
	AAwPlayerController* Temp = UGameplayFuncLib::GetAwPlayerController(0);
	if(Temp)
	{
		UGameplayFuncLib::GetAwPlayerController(0)->SetInputMode(FInputModeGameOnly());
	}

	UAwRogueDataSystem* RogueDataSystem = GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	RogueDataSystem->SaveData();
}

void UAwakeSkillMain::InitializeAwakeSkill(FString ParentUI)
{
	AwakeSkillOptions_UGP->ClearChildren();
	AwakeSkillOptionArray.Empty();
	
	this->SetParentUIName(ParentUI);
	TMap<FString,FAwActionSkillInfo> AllAwakeSkillInfo;
	TArray<FString> UnLockAwakeSkillInfo;
	
	AllAwakeSkillInfo =	UGameplayFuncLib::GetAwDataManager()->GetAllAwakeSkillnfo();
	UnLockAwakeSkillInfo = UGameplayFuncLib::GetPlayerControllerByWidget(this)->GetPlayerState<AAwPlayerState>()->UnLockAwakeSkills;

	TArray<FString> Keys;
	AllAwakeSkillInfo.GetKeys(Keys);
	int Index = 0;
	bool FlipFlop = true;
	for(FString key : Keys)
	{
		UAwakeSkillOptions* TempAwakeSkillOptions =
			Cast<UAwakeSkillOptions>(UGameplayFuncLib::GetUiManager()->ShowNewUI(TEXT("AwakSkill_Options")));
		const bool IsUnLock = UnLockAwakeSkillInfo.Contains(key);
		if(IsValid(TempAwakeSkillOptions))
		{
			bool IsEquipped = false;
			if(UGameplayFuncLib::GetPlayerControllerByWidget(this)->GetPlayerState<AAwPlayerState>()->GetCurAwakeSkill())
				IsEquipped = AllAwakeSkillInfo.Find(key)->Id ==
				UGameplayFuncLib::GetPlayerControllerByWidget(this)->GetPlayerState<AAwPlayerState>()->GetCurAwakeSkill()->Id;
			
			if(IsEquipped)
				SetEquippedAwakeSkillIndex(Index);
			
			TempAwakeSkillOptions->InitializationAwakeSkill(*AllAwakeSkillInfo.Find(key),Index,IsUnLock , IsEquipped);

			AwakeSkillOptions_UGP->AddChild(TempAwakeSkillOptions);

			if(FlipFlop)
			{
				UWidgetLayoutLibrary::SlotAsUniformGridSlot(TempAwakeSkillOptions)->SetRow(0);
				UWidgetLayoutLibrary::SlotAsUniformGridSlot(TempAwakeSkillOptions)->SetColumn(Index);
				FlipFlop = false;
			}
			else
			{
				UWidgetLayoutLibrary::SlotAsUniformGridSlot(TempAwakeSkillOptions)->SetRow(1);
				UWidgetLayoutLibrary::SlotAsUniformGridSlot(TempAwakeSkillOptions)->SetColumn(Index);
				FlipFlop = true;
			}
			TempAwakeSkillOptions->AwakeSkillChangeFocus_Delegate.AddDynamic(this,&UAwakeSkillMain::AwakeSkillChangFocus);
			TempAwakeSkillOptions->AwakeSkillOnClicked_Delegate.AddDynamic(this,&UAwakeSkillMain::UAwakeSkillMain::ShowOrHideConfirmationTips);
			AwakeSkillOptionArray.Add(TempAwakeSkillOptions);
		}
		
		Index++;
	}

	if(UAwGameInstance::Instance->bIsGamepad)
	{
		AwakeSkillOptionArray[0]->AwakeSkillOnHovered();
		SetCurAwakeSKillIndex(0);
	}

	if(UGameplayFuncLib::GetAwPlayerController(0))
	{
		UGameplayFuncLib::GetAwPlayerController(0)->SetInputMode(FInputModeGameAndUI());
	}
}

void UAwakeSkillMain::Back()
{
	UGameplayFuncLib::GetPlayerControllerByWidget(this)->GameControlState = EGameControlState::Game;
	UGameplayFuncLib::GetPlayerControllerByWidget(this)->SetShowMouseCursor(false);

	UGameplayFuncLib::GetUiManager()->Hide(TEXT("AwakSkill_Main"));
	UGameplayFuncLib::GetUiManager()->Show(this->GetParentUIName());

	AAwPlayerController* Temp = UGameplayFuncLib::GetAwPlayerController(0);
	if(Temp)
	{
		UGameplayFuncLib::GetAwPlayerController(0)->SetInputMode(FInputModeGameOnly());
	}

	UAwRogueDataSystem* RogueDataSystem = GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	RogueDataSystem->SaveData();
}

UAwakeSkillOptions* UAwakeSkillMain::GetCurAwakeSkillOptions()
{
	UAwakeSkillOptions* Temp = nullptr;
	if(AwakeSkillOptionArray.IsValidIndex(CurAwakeSkillIndex) && IsValid(AwakeSkillOptionArray[CurAwakeSkillIndex]))
	{
		
		Temp =  AwakeSkillOptionArray[CurAwakeSkillIndex];
	}

	return Temp;
}

UAwakeSkillOptions* UAwakeSkillMain::GetAwakeSkillOptions(int AwakeSkillIndex)
{
	UAwakeSkillOptions* Temp = nullptr;
	if(AwakeSkillOptionArray.IsValidIndex(AwakeSkillIndex) && IsValid(AwakeSkillOptionArray[AwakeSkillIndex]))
	{
		Temp =  AwakeSkillOptionArray[AwakeSkillIndex];
	}

	return Temp;
}

void UAwakeSkillMain::AwakeSkillChangFocus(int AwakeSkillIndex)
{
	if(IsValid(GetCurAwakeSkillOptions()))
	{
		GetCurAwakeSkillOptions()->AwakeSkillOnUnHovered();
	}
	SetLastAwakeSKillIndex(GetCurAwakeSkillIndex());
	SetCurAwakeSKillIndex(AwakeSkillIndex);

	ShowOrHideAwakeSkillDesc();
}

void UAwakeSkillMain::AwakeSkillSelectedLeft()
{
	if(GetCurAwakeSkillIndex() < 0)
	{
		SetCurAwakeSKillIndex(GetLastAwakeSkillIndex());
	}
	else
	{
		if(GetCurAwakeSkillIndex() - 1 < 0)
		{
			if(!AwakeSkillOptionArray[AwakeSkillOptionArray.Num() - 1]->GetIsUnLock())
				return;
		}
		else
		{
			if(!AwakeSkillOptionArray[GetCurAwakeSkillIndex() - 1]->GetIsUnLock())
				return;
		}
		
		if(IsValid(GetCurAwakeSkillOptions()))
		{
			GetCurAwakeSkillOptions()->AwakeSkillOnUnHovered();
			if(GetCurAwakeSkillIndex() - 1 < 0)
			{
				SetCurAwakeSKillIndex(AwakeSkillOptionArray.Num() - 1);
			}
			else
			{
				SetCurAwakeSKillIndex(GetCurAwakeSkillIndex() - 1);
			}
		}
	}
	if(IsValid(GetCurAwakeSkillOptions()))
	{
		GetCurAwakeSkillOptions()->AwakeSkillOnHovered();
	}
	ShowOrHideAwakeSkillDesc();
}

void UAwakeSkillMain::AwakeSkillSelectedRight()
{
	if(GetCurAwakeSkillIndex() < 0)
	{
		SetCurAwakeSKillIndex(GetLastAwakeSkillIndex());
	}
	else
	{
		if(GetCurAwakeSkillIndex() + 1 > AwakeSkillOptionArray.Num() - 1)
		{
			if(!AwakeSkillOptionArray[0]->GetIsUnLock())
				return;
		}
		else
		{
			if(!AwakeSkillOptionArray[GetCurAwakeSkillIndex() + 1]->GetIsUnLock())
				return;
		}
		if(IsValid(GetCurAwakeSkillOptions()))
		{
			GetCurAwakeSkillOptions()->AwakeSkillOnUnHovered();
			if(GetCurAwakeSkillIndex() + 1 > AwakeSkillOptionArray.Num() - 1)
			{
				SetCurAwakeSKillIndex(0);
			}
			else
			{
				SetCurAwakeSKillIndex(GetCurAwakeSkillIndex() + 1);
			}
		}
	}
	if(IsValid(GetCurAwakeSkillOptions()))
	{
		GetCurAwakeSkillOptions()->AwakeSkillOnHovered();
	}
	ShowOrHideAwakeSkillDesc();
}

void UAwakeSkillMain::AwakeSkillSelectedConfirm()
{
	if(IsValid(GetCurAwakeSkillOptions()))
		GetCurAwakeSkillOptions()->AwakeSkillOnClicked();
}

void UAwakeSkillMain::ShowOrHideAwakeSkillDesc()
{
	if(IsValid(GetCurAwakeSkillOptions()))
	{
		GetCurAwakeSkillOptions()->AwakeSkillOnHovered();
		AwakeSkillDesc_CanvasPanel->SetVisibility(ESlateVisibility::SelfHitTestInvisible);
		AwakeSkillDescription_Text->SetText(FText::FromString(UGameplayFuncLib::GetAwDataManager()->
			GetTextByKey(GetCurAwakeSkillOptions()->GetAwakeSkillInfo().Desc)));
		
	}
	else
	{
		AwakeSkillDesc_CanvasPanel->SetVisibility(ESlateVisibility::Collapsed);
	}
}

void UAwakeSkillMain::ShowOrHideConfirmationTips()
{
	UGameplayFuncLib::GetPlayerControllerByWidget(this)->GameControlState = EGameControlState::Reminder;
	if(!UGameplayFuncLib::GetPlayerControllerByWidget(this)->GetPlayerState<AAwPlayerState>()->GetCurAwakeSkill())
	{
		SetSelectedAwakeSkillIndex(GetCurAwakeSkillIndex());
		
		AwakeSkillInfo_CanvasPanel->SetVisibility(ESlateVisibility::HitTestInvisible);
		ConfirmationTips_CanvasPanel->SetVisibility(ESlateVisibility::Visible);
	}
	else
	{
		if(IsValid(GetCurAwakeSkillOptions()) && GetCurAwakeSkillOptions()->GetAwakeSkillInfo().Id !=
		UGameplayFuncLib::K2Node_GetPlayerCurAwkeSkill(GetOwningPlayer<AAwPlayerController>()).Id)
		{
			SetSelectedAwakeSkillIndex(GetCurAwakeSkillIndex());
		
			AwakeSkillInfo_CanvasPanel->SetVisibility(ESlateVisibility::HitTestInvisible);
			ConfirmationTips_CanvasPanel->SetVisibility(ESlateVisibility::Visible);
		}
	}
	
}

void UAwakeSkillMain::Confirm_BTOnClicked()
{
	if(IsValid(GetAwakeSkillOptions(GetSelectedAwakeSkillIndex())))
	{
		if(UGameplayFuncLib::GetPlayerControllerByWidget(this)->GetPlayerState<AAwPlayerState>()->GetCurAwakeSkill())
		{
			UGameplayFuncLib::SetPlayerCurAwakeSkill(GetOwningAwController(),GetAwakeSkillOptions(GetSelectedAwakeSkillIndex())->
			GetAwakeSkillInfo().Id);
			if (GetAwakeSkillOptions(GetEquippedAwakeSkillIndex()))
			{
				GetAwakeSkillOptions(GetEquippedAwakeSkillIndex())->SetIsEquipped(false);
				GetAwakeSkillOptions(GetEquippedAwakeSkillIndex())->SetAwakeSkillButtonColor();
			}
			GetAwakeSkillOptions(GetSelectedAwakeSkillIndex())->SetIsEquipped(true);
			GetAwakeSkillOptions(GetSelectedAwakeSkillIndex())->SetAwakeSkillButtonColor();
			SetEquippedAwakeSkillIndex(GetAwakeSkillOptions(GetSelectedAwakeSkillIndex())->GetIndex());
		}
		else
		{
			UGameplayFuncLib::SetPlayerCurAwakeSkill(GetOwningAwController(),GetAwakeSkillOptions(GetSelectedAwakeSkillIndex())->
			GetAwakeSkillInfo().Id);
			GetAwakeSkillOptions(GetSelectedAwakeSkillIndex())->SetIsEquipped(true);
			GetAwakeSkillOptions(GetSelectedAwakeSkillIndex())->SetAwakeSkillButtonColor();
			SetEquippedAwakeSkillIndex(GetAwakeSkillOptions(GetSelectedAwakeSkillIndex())->GetIndex());
		}
		
	}
	AwakeSkillInfo_CanvasPanel->SetVisibility(ESlateVisibility::SelfHitTestInvisible);
	ConfirmationTips_CanvasPanel->SetVisibility(ESlateVisibility::Collapsed);
	UGameplayFuncLib::GetPlayerControllerByWidget(this)->GameControlState = EGameControlState::AwakeSkill;
}

void UAwakeSkillMain::Cancel_BTOnClicked()
{
	AwakeSkillInfo_CanvasPanel->SetVisibility(ESlateVisibility::SelfHitTestInvisible);
	ConfirmationTips_CanvasPanel->SetVisibility(ESlateVisibility::Collapsed);
	UGameplayFuncLib::GetPlayerControllerByWidget(this)->GameControlState = EGameControlState::AwakeSkill;
}


