// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/GamePlay/Characters/Skill/AwSkill.h"
#include "TheAwakener_FO/UI/Base/BaseUI.h"
#include "AwakeSkillOptions.generated.h"

/**
 * 
 */
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FAwakeSkillChangeFocus_Delegate,int,Index);
DECLARE_DYNAMIC_MULTICAST_DELEGATE(FAwakeSkillOnClicked_Delegate);
UCLASS()
class THEAWAKENER_FO_API UAwakeSkillOptions : public UBaseUI
{
	GENERATED_BODY()

	UPROPERTY()
	int Index = -1;

	UPROPERTY()
	bool IsUnLock = false;

	UPROPERTY()
	bool IsEquipped = false;

	UPROPERTY()
	FAwActionSkillInfo AwakeSkillInfo;

	virtual void NativeOnInitialized() override;
	virtual void NativeConstruct() override;
	virtual void NativeTick(const FGeometry& MyGeometry, float InDeltaTime) override;
	virtual void NativeDestruct() override;

public:

	UPROPERTY(EditAnywhere,BlueprintReadWrite,BlueprintAssignable)
	FAwakeSkillChangeFocus_Delegate AwakeSkillChangeFocus_Delegate;

	UPROPERTY(EditAnywhere,BlueprintReadWrite,BlueprintAssignable) 
	FAwakeSkillOnClicked_Delegate AwakeSkillOnClicked_Delegate;
	
	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UButton* AwakeSkill_Button;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UTextBlock* AwakeSkillName_Text;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UTextBlock* Lock_TextBlock;
	
	UFUNCTION(BlueprintCallable)
	void InitializationAwakeSkill(FAwActionSkillInfo AwActionSkillInfo , int skillindex , bool UnLock , bool Equipped);

	UFUNCTION(BlueprintCallable)
	void SetIndex(int skillindex){ Index = skillindex; }

	UFUNCTION(BlueprintCallable)
	int GetIndex() const { return Index; }

	UFUNCTION(BlueprintCallable)
	void SetIsUnLock(bool UnLock) { IsUnLock = UnLock;}

	UFUNCTION(BlueprintCallable)
	bool GetIsUnLock() const { return IsUnLock; }

	UFUNCTION(BlueprintCallable)
	void SetIsEquipped(bool Equipped) { IsEquipped = Equipped;}

	UFUNCTION(BlueprintCallable)
	bool GetIsEquipped() const { return IsEquipped; }
	
	UFUNCTION(BlueprintCallable)
	FAwActionSkillInfo GetAwakeSkillInfo() const { return AwakeSkillInfo; }

	UFUNCTION(BlueprintCallable)
	void SetAwakeSkillButtonColor();

	UFUNCTION(BlueprintImplementableEvent,BlueprintCallable)
	void AwakeSkillOnHovered();

	UFUNCTION(BlueprintImplementableEvent,BlueprintCallable)
	void AwakeSkillOnUnHovered();

	UFUNCTION(BlueprintImplementableEvent,BlueprintCallable)
	void AwakeSkillOnClicked();

	UFUNCTION(BlueprintCallable)
	void AwakeSkillChangeFocus_DelegateBroadCast(int AwakeSkillIndex);

	UFUNCTION(BlueprintCallable)
	void AwakeSkillOnClicked_DelegateBroadcast();
};


