// Fill out your copyright notice in the Description page of Project Settings.


#include "AwakeSkillOptions.h"

#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"

void UAwakeSkillOptions::NativeOnInitialized()
{
	Super::NativeOnInitialized();

	AwakeSkill_Button = Cast<UButton>(GetWidgetFromName(TEXT("AwakeSkill_BT")));
	AwakeSkillName_Text = Cast<UTextBlock>(GetWidgetFromName(TEXT("AwakeSkillName")));
	Lock_TextBlock = Cast<UTextBlock>(GetWidgetFromName(TEXT("Lock")));
}

void UAwakeSkillOptions::NativeConstruct()
{
	Super::NativeConstruct();
}

void UAwakeSkillOptions::NativeTick(const FGeometry& MyGeometry, float InDeltaTime)
{
	Super::NativeTick(MyGeometry, InDeltaTime);
}

void UAwakeSkillOptions::NativeDestruct()
{
	Super::NativeDestruct();
}

void UAwakeSkillOptions::InitializationAwakeSkill(FAwActionSkillInfo AwActionSkillInfo , int skillindex , bool UnLock , bool Equipped)
{
	Index = skillindex;
	IsUnLock = UnLock;
	AwakeSkillInfo = AwActionSkillInfo;
	IsEquipped = Equipped;
	
	if(IsUnLock)
	{
		AwakeSkill_Button->SetVisibility(ESlateVisibility::Visible);
		AwakeSkillName_Text->SetRenderOpacity(1);
		Lock_TextBlock->SetVisibility(ESlateVisibility::Collapsed);

		SetAwakeSkillButtonColor();
	}
	else
	{
		AwakeSkill_Button->SetVisibility(ESlateVisibility::HitTestInvisible);
		AwakeSkillName_Text->SetRenderOpacity(0);
		Lock_TextBlock->SetVisibility(ESlateVisibility::Visible);
	}

	if(IsValid(AwakeSkillName_Text))
		AwakeSkillName_Text->SetText(FText::FromString(UGameplayFuncLib::GetAwDataManager()->
			GetTextByKey(AwActionSkillInfo.Id)));
	
}

void UAwakeSkillOptions::SetAwakeSkillButtonColor()
{
	if(IsEquipped)
	{
		FButtonStyle TempStyle = AwakeSkill_Button->GetStyle();
		FSlateBrush NormalSlateBrush = TempStyle.Normal;
		FSlateBrush HoveredSlateBrush = TempStyle.Hovered;
		NormalSlateBrush.TintColor = FSlateColor(FLinearColor(1.0f,0.5f,0.5f,0.2f));
		HoveredSlateBrush.TintColor = FSlateColor(FLinearColor(1.0f,0.5f,0.5f,0.2f));
		TempStyle.SetNormal(NormalSlateBrush);
		TempStyle.SetHovered(HoveredSlateBrush);
		AwakeSkill_Button->SetStyle(TempStyle);
	}
	else
	{
		FButtonStyle TempStyle = AwakeSkill_Button->GetStyle();
		FSlateBrush NormalSlateBrush = TempStyle.Normal;
		FSlateBrush HoveredSlateBrush = TempStyle.Hovered;
		NormalSlateBrush.TintColor = FSlateColor(FLinearColor(0.5f,0.5f,0.5f,0.2f));
		HoveredSlateBrush.TintColor = FSlateColor(FLinearColor(0.5f,0.5f,0.5f,0.2f));
		TempStyle.SetNormal(NormalSlateBrush);
		TempStyle.SetHovered(HoveredSlateBrush);
		AwakeSkill_Button->SetStyle(TempStyle);
		
	}
}

void UAwakeSkillOptions::AwakeSkillChangeFocus_DelegateBroadCast(int AwakeSkillIndex)
{
	AwakeSkillChangeFocus_Delegate.Broadcast(AwakeSkillIndex);
}

void UAwakeSkillOptions::AwakeSkillOnClicked_DelegateBroadcast()
{
	AwakeSkillOnClicked_Delegate.Broadcast();
}
