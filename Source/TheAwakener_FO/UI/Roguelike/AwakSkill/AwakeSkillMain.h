// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "AwakeSkillOptions.h"
#include "TheAwakener_FO/UI/Base/BaseUI.h"
#include "Components/UniformGridPanel.h"
#include "AwakeSkillMain.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UAwakeSkillMain : public UBaseUI
{
	GENERATED_BODY()

	
	UPROPERTY()
	TArray<UAwakeSkillOptions*> AwakeSkillOptionArray;

	UPROPERTY()
	AAwGameState* GameState;

	UPROPERTY()
	int CurAwakeSkillIndex = -1;

	UPROPERTY()
	int LastAwakeSkillIndex = -1;

	UPROPERTY()
	int EquippedAwakeSkillIndex = -1;

	UPROPERTY()
	int SelectedAwakeSkillIndex = -1;

	virtual void NativeOnInitialized() override;
	virtual void NativeTick(const FGeometry& MyGeometry, float InDeltaTime) override;
	virtual void NativeDestruct() override;

public:

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UCanvasPanel* AwakeSkillInfo_CanvasPanel;
	
	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UUniformGridPanel* AwakeSkillOptions_UGP;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UCanvasPanel* AwakeSkillDesc_CanvasPanel;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UTextBlock* AwakeSkillDescription_Text;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UCanvasPanel* ConfirmationTips_CanvasPanel;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UButton* Confirm_Button;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UButton* Cancel_Button;

	UFUNCTION(BlueprintCallable)
	void InitializeAwakeSkill(FString ParentUI = "");

	UFUNCTION(BlueprintCallable)
	void Back();

	UFUNCTION(BlueprintCallable,BlueprintPure)
	int GetCurAwakeSkillIndex() {return CurAwakeSkillIndex;} 

	UFUNCTION(BlueprintCallable)
	void SetCurAwakeSKillIndex(int Index) { CurAwakeSkillIndex = Index;}

	UFUNCTION(BlueprintCallable,BlueprintPure)
	int GetLastAwakeSkillIndex() {return LastAwakeSkillIndex;} 

	UFUNCTION(BlueprintCallable)
	void SetLastAwakeSKillIndex(int Index) { LastAwakeSkillIndex = Index;}

	UFUNCTION(BlueprintCallable)
	void SetEquippedAwakeSkillIndex(int Index) { EquippedAwakeSkillIndex = Index;}

	UFUNCTION(BlueprintCallable)
	int GetEquippedAwakeSkillIndex() {return EquippedAwakeSkillIndex;}

	UFUNCTION(BlueprintCallable)
	void SetSelectedAwakeSkillIndex(int Index) { SelectedAwakeSkillIndex = Index;}

	UFUNCTION(BlueprintCallable)
	int GetSelectedAwakeSkillIndex() {return SelectedAwakeSkillIndex;}

	UFUNCTION(BlueprintCallable)
	UAwakeSkillOptions* GetCurAwakeSkillOptions();

	UFUNCTION(BlueprintCallable)
	UAwakeSkillOptions* GetAwakeSkillOptions(int AwakeSkillIndex);

	UFUNCTION(BlueprintCallable)
	void AwakeSkillChangFocus(int AwakeSkillIndex);

	UFUNCTION(BlueprintCallable)
	void AwakeSkillSelectedLeft();

	UFUNCTION(BlueprintCallable)
	void AwakeSkillSelectedRight();

	UFUNCTION(BlueprintCallable)
	void AwakeSkillSelectedConfirm();

	UFUNCTION(BlueprintCallable)
	void ShowOrHideAwakeSkillDesc();

	UFUNCTION(BlueprintCallable)
	void ShowOrHideConfirmationTips();

	UFUNCTION(BlueprintCallable)
	void Confirm_BTOnClicked();

	UFUNCTION(BlueprintCallable)
	void Cancel_BTOnClicked();
	
};
