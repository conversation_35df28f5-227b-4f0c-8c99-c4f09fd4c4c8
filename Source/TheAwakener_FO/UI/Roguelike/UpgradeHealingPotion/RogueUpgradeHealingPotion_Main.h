// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/UI/Base/BaseUI.h"

#include "RogueUpgradeHealingPotion_Main.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API URogueUpgradeHealingPotion_Main : public UBaseUI
{
	GENERATED_BODY()

	
	virtual void NativeOnInitialized() override;
	virtual void NativeConstruct() override;
	virtual void NativeTick(const FGeometry& MyGeometry, float InDeltaTime) override;

public:

	UFUNCTION(BlueprintImplementableEvent,BlueprintCallable)
	void Prev();

	UFUNCTION(BlueprintImplementableEvent,BlueprintCallable)
	void Next();

	UFUNCTION(BlueprintImplementableEvent,BlueprintCallable)
	void Confirm();

	UFUNCTION(BlueprintImplementableEvent,BlueprintCallable)
	void Back();

	UFUNCTION(BlueprintImplementableEvent,BlueprintCallable)
	void TipsBoxConfirm();

	UFUNCTION(BlueprintImplementableEvent,BlueprintCallable)
	void TipsBoxCancel();
};
