// Fill out your copyright notice in the Description page of Project Settings.


#include "RogueUpgradeHealingPotion_Options.h"

void URogueUpgradeHealingPotion_Options::NativeOnInitialized()
{
	Super::NativeOnInitialized();
}

void URogueUpgradeHealingPotion_Options::NativeConstruct()
{
	Super::NativeConstruct();
}

void URogueUpgradeHealingPotion_Options::NativeTick(const FGeometry& MyGeometry, float InDeltaTime)
{
	Super::NativeTick(MyGeometry, InDeltaTime);
}
