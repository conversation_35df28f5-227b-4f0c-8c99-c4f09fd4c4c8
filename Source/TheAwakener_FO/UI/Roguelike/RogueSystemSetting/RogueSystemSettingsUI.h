// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/UI/SystemSettings/SettingsUI.h"
#include "TheAwakener_FO/UI/Base/BaseUI.h"
#include "RogueSystemSettingsUI.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API URogueSystemSettingsUI : public UBaseUI
{
	GENERATED_BODY()

public:
	
	virtual void NativeConstruct() override;
	virtual void NativeTick(const FGeometry& MyGeometry, float InDeltaTime) override;

public:
	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	FString MyUIName = "RogueSystemSettings";
	
	UPROPERTY(EditAnywhere,BlueprintReadWrite, meta = (BindWidget))
	UTextBlock* TitleText;

	UPROPERTY(EditAnywhere,BlueprintReadWrite, meta = (BindWidget))
	UTextBlock* DescText;
	
	UPROPERTY(EditAnywhere,BlueprintReadWrite, meta = (BindWidget))
	UWidgetSwitcher* SettingSwitcher;

	UFUNCTION(BlueprintImplementableEvent,BlueprintCallable)
	void Prev();

	UFUNCTION(BlueprintImplementableEvent,BlueprintCallable)
	void Next();

	UFUNCTION(BlueprintImplementableEvent,BlueprintCallable)
	void Confirm();

	UFUNCTION(BlueprintImplementableEvent,BlueprintCallable)
	void Back();
	
	UFUNCTION(BlueprintCallable)
	virtual void CheckInput();
	
};

