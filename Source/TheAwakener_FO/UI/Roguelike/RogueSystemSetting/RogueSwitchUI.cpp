// Fill out your copyright notice in the Description page of Project Settings.


#include "RogueSwitchUI.h"

#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GameFramework/Manager/AwDataManager.h"

void URogueSwitchUI::InitOptions(	TArray<FString> Options)
{
	SwitchOptions = Options;
	if (!SwitchOptions.IsEmpty())
		SetSwitchByIndex(0);
}

void URogueSwitchUI::SetSwitchByIndex(int Index)
{
	if (SwitchOptions.IsEmpty())
	{
		return;
	}
	Index = Index%SwitchOptions.Num();

	if (!SwitchOptions.IsValidIndex(Index))
	{
		return;
	}
	CurIndex = Index;
	CurSwitchOption = SwitchOptions[Index];
	if(OnSwitchIndexChange.IsBound())
	{
		OnSwitchIndexChange.Broadcast(Index);
	}
	if (OnSwitchOptionChange.IsBound())
	{
		OnSwitchOptionChange.Broadcast(CurSwitchOption);
	}

	SetSwitchText();
}

void URogueSwitchUI::SetSwitchByOption(FString Option)
{
	if (SwitchOptions.IsEmpty())
	{
		return;
	}
	if (!SwitchOptions.Contains(Option))
	{
		return;
	}
	if (Option!=CurSwitchOption)
	{
		CurIndex = CurSwitchOption.Find(Option);
		CurSwitchOption =Option ;
		if(OnSwitchIndexChange.IsBound())
		{
			OnSwitchIndexChange.Broadcast(CurSwitchOption.Find(Option));
		}
		if (OnSwitchOptionChange.IsBound())
		{
			OnSwitchOptionChange.Broadcast(Option);
		}
		
		SetSwitchText();
	}
}

void URogueSwitchUI::SetSwitchPerformanceByIndex(int Index)
{
	if (SwitchOptions.IsEmpty())
	{
		return;
	}
	Index = Index%SwitchOptions.Num();

	if (!SwitchOptions.IsValidIndex(Index))
	{
		return;
	}
	CurIndex = Index;
	CurSwitchOption = SwitchOptions[Index];

	SetSwitchText();
}

void URogueSwitchUI::SetSwitchPerformanceByOption(FString Option)
{
	if (SwitchOptions.IsEmpty())
	{
		return;
	}
	if (!SwitchOptions.Contains(Option))
	{
		return;
	}
	//分辨率切换完语言内容为空，原因没找到，先将设置重复判断去掉，永远设置分辨率文本
	/*if (Option!=CurSwitchOption)
	{
		CurIndex = SwitchOptions.Find(Option);
		CurSwitchOption =Option ;
		SetSwitchText();
	}*/
	CurIndex = SwitchOptions.Find(Option);
	CurSwitchOption =Option ;
	SetSwitchText();
}

void URogueSwitchUI::SetSwitchText() const
{
	if (UAwDataManager* DataManager = UGameplayFuncLib::GetAwDataManager())
	{
		ValueTextBlock->SetVisibility(bUseRichText ? ESlateVisibility::Collapsed : ESlateVisibility::SelfHitTestInvisible);
		ValueRichText->SetVisibility(bUseRichText ? ESlateVisibility::SelfHitTestInvisible : ESlateVisibility::Collapsed);
		
		if (bUseDataText)
		{
			const FString TextString = DataManager->GetTextByKey(CurSwitchOption);
			if (bUseRichText)
				ValueRichText->SetText(FText::FromString(TextString));
			else
				ValueTextBlock->SetText(FText::FromString(TextString));
		}
		else
		{
			if (bUseRichText)
				ValueRichText->SetText(FText::FromString(CurSwitchOption));
			else
				ValueTextBlock->SetText(FText::FromString(CurSwitchOption));
		}
	}
}
