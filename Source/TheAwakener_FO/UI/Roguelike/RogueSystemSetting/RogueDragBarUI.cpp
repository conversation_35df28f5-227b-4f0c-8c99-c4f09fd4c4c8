// Fill out your copyright notice in the Description page of Project Settings.


#include "RogueDragBarUI.h"

#include "Blueprint/WidgetLayoutLibrary.h"
#include "Kismet/KismetTextLibrary.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"

void URogueDragBarUI::NativePreConstruct()
{
	Super::NativePreConstruct();
	Slider->OnValueChanged.AddUniqueDynamic(this,&URogueDragBarUI::UpdateDragBarValue);
}

void URogueDragBarUI::NativeOnInitialized()
{
	Super::NativeOnInitialized();
}

void URogueDragBarUI::NativeConstruct()
{
	Super::NativeConstruct();
	
}

void URogueDragBarUI::NativeTick(const FGeometry& MyGeometry, float InDeltaTime)
{
	Super::NativeTick(MyGeometry, InDeltaTime);
	
}


void URogueDragBarUI::SetDragBarValuePerformance_Implementation(float Value)
{
	Slider->SetValue(Value);
	FrontProgressBar->SetPercent(Value);
	BackProgressBar->SetPercent(Value);
	UpdateTextValue(Value);
}

void URogueDragBarUI::SetDragBarValue(float Value)
{
	Slider->SetValue(Value);
	Slider->OnValueChanged.Broadcast(Value);
	FrontProgressBar->SetPercent(Value);
	BackProgressBar->SetPercent(Value);
}

void URogueDragBarUI::UpdateDragBarValue_Implementation(float Value)
{
	FrontProgressBar->SetPercent(Value);
	BackProgressBar->SetPercent(Value);
	UpdateTextValue(Value);
	//FString ValueString = FString::FromInt(CurValueNum);
	//ValueTextBlock->SetText(FText::FromString(ValueString));
	if(OnPercentChange.IsBound())
	{
		OnPercentChange.Broadcast(Value);
	}
}

void URogueDragBarUI::UpdateTextValue_Implementation(float Value)
{
	float CurValueNum =FMath::Clamp(MaxValue*Value,MinValue,MaxValue);
	FText ValueText = UKismetTextLibrary::AsCurrency_Float(CurValueNum,PerformanceTextMode,false,true,1,324,MinPerformanceFracDigits,MaxPerformanceFracDigits);
	ValueTextBlock->SetText(ValueText);
}







