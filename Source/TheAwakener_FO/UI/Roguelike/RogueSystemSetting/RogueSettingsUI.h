// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/UI/Base/BaseUI.h"
#include "RogueSettingsUI.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API URogueSettingsUI : public UBaseUI
{
	GENERATED_BODY()

public:
	
	virtual void NativeConstruct() override;
	virtual void NativeTick(const FGeometry& MyGeometry, float InDeltaTime) override;

public:
	DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FSettingUIEvent,UBaseUI*,UserWidget);
	UPROPERTY(BlueprintCallable,BlueprintAssignable)
	FSettingUIEvent OnClose;

	UPROPERTY(BlueprintCallable,BlueprintAssignable)
	FSettingUIEvent OnPreToApply;

	UPROPERTY(BlueprintCallable,BlueprintAssignable)
	FSettingUIEvent OnPreToReset;

	UPROPERTY(BlueprintCallable,BlueprintAssignable)
	FSettingUIEvent OnPreToClose;
	
	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	FString MyUIName = "RogueSettings";

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	FString UpCommand = "RogueSetting_Up";

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	FString DownCommand = "RogueSetting_Down";
	
	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	FString BackCommand = "RogueSetting_Back";

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	FString ResetCommand = "RogueSetting_Reset";
	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	FString ApplyCommand = "RogueSetting_Apply";

	UFUNCTION(BlueprintCallable)
	void K2_OnClose();
	
	UFUNCTION(BlueprintImplementableEvent,BlueprintCallable)
	void Prev();

	UFUNCTION(BlueprintImplementableEvent,BlueprintCallable)
	void Next();
	
	UFUNCTION(BlueprintImplementableEvent,BlueprintCallable)
	void Reset();

	UFUNCTION(BlueprintImplementableEvent,BlueprintCallable)
	void Back();
	
	UFUNCTION(BlueprintCallable)
	virtual void CheckInput();

	UFUNCTION(BlueprintImplementableEvent,BlueprintCallable)
	void ResetToDefault();

	UFUNCTION(BlueprintImplementableEvent,BlueprintCallable)
	void ApplyToQuit();

	UFUNCTION(BlueprintImplementableEvent,BlueprintCallable)
	void  ResetToQuit();

	UFUNCTION(BlueprintImplementableEvent,BlueprintCallable)
	void  ApplySetting();
};

