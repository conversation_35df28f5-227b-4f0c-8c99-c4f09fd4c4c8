// Fill out your copyright notice in the Description page of Project Settings.


#include "RogueSystemSettingsUI.h"

#include "Blueprint/WidgetBlueprintLibrary.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"


void URogueSystemSettingsUI::NativeConstruct()
{
	Super::NativeConstruct();
	
}

void URogueSystemSettingsUI::NativeTick(const FGeometry& MyGeometry, float InDeltaTime)
{
	Super::NativeTick(MyGeometry, InDeltaTime);
	UpdateShowMouse();
	CheckInput();
}


void URogueSystemSettingsUI::CheckInput()
{
	
	if (GetAwGameState())
	{
		// AAwCharacter* Me = GetAwGameState()->GetMyCharacter();
		for (auto PC : UGameplayFuncLib::GetAllLocalAwPlayerControllers())
		{
			if (AAwCharacter* Me = PC->CurCharacter)
			{
				if(Me->GetCmdComponent()->IsActionOccur("MainRogueSetting_Up",EAwInputState::Press,true)) 
				{
					Prev();
				}
				else if(Me->GetCmdComponent()->IsActionOccur("MainRogueSetting_Down",EAwInputState::Press,true))
				{
					Next();
				}
				else if(Me->GetCmdComponent()->IsActionOccur("MainRogueSetting_Confirm",EAwInputState::Press,true))
				{
					Confirm();
				}
				else if(Me->GetCmdComponent()->IsActionOccur("MainRogueSetting_Back",EAwInputState::Press,true))
				{
					Back();
				}
			}
		}
	}
}









