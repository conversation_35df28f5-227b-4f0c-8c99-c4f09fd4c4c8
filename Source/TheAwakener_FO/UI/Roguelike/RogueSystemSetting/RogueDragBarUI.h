// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Components/Slider.h"
#include "TheAwakener_FO/UI/Base/BaseUI.h"
#include "RogueDragBarUI.generated.h"

/**
 * 
 */
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FValueEvent,float,Value);

UCLASS()
class THEAWAKENER_FO_API URogueDragBarUI : public UBaseUI
{
	GENERATED_BODY()

	virtual void NativePreConstruct()override;
	virtual void NativeOnInitialized() override;
	virtual void NativeConstruct() override;
	virtual void NativeTick(const FGeometry& MyGeometry, float InDeltaTime) override;

public:
	// Value *= SliderMaxValue   Default SliderMaxValue = 1.0f   
	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	float MaxValue = 100;
	//Value *= SliderMinValue   Default SliderMinValue = 0.f   
	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	float MinValue = 0;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	int MinPerformanceFracDigits = 0;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	int MaxPerformanceFracDigits = 1;
	
	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	TEnumAsByte<ERoundingMode> PerformanceTextMode = ERoundingMode::HalfToEven;
	
	UPROPERTY(EditAnywhere,BlueprintReadWrite, meta = (BindWidget))
	UProgressBar* FrontProgressBar;

	UPROPERTY(EditAnywhere,BlueprintReadWrite, meta = (BindWidget))
	UProgressBar* BackProgressBar;
	
	UPROPERTY(EditAnywhere,BlueprintReadWrite, meta = (BindWidget))
	USlider* Slider;


	UPROPERTY(EditAnywhere,BlueprintReadWrite, meta = (BindWidget))
	UTextBlock* ValueTextBlock;
	
	UPROPERTY(BlueprintAssignable)
	FValueEvent OnPercentChange;

	//不产生任何回调调用 外部对表现 文字+百分比填充 的初始化强制设置 
	UFUNCTION(BlueprintCallable,BlueprintNativeEvent)
	void SetDragBarValuePerformance(float Value);
	void SetDragBarValuePerformance_Implementation(float Value);

	
	//正常的设置 包括回调等正常调用
	UFUNCTION(BlueprintCallable)
	virtual  void SetDragBarValue(float Value);

	//Slider  拖动变化的 绑定回调
	UFUNCTION(BlueprintCallable,BlueprintNativeEvent)
	void UpdateDragBarValue(float Value);
	void UpdateDragBarValue_Implementation(float Value);

	//文字变化
	UFUNCTION(BlueprintCallable,BlueprintNativeEvent)
	void UpdateTextValue(float Value);
	void UpdateTextValue_Implementation(float Value);
};

