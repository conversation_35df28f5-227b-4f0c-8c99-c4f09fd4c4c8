// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Components/RichTextBlock.h"
#include "TheAwakener_FO/UI/Base/BaseUI.h"
#include "RogueSwitchUI.generated.h"

/**
 * 
 */
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FSwitchIndexEvent,int,Index);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FSwitchOptionEvent,FString,Option);

UCLASS()
class THEAWAKENER_FO_API URogueSwitchUI : public UBaseUI
{
	GENERATED_BODY()
private:
	void SetSwitchText() const;
	
public:
	UPROPERTY(EditAnywhere,BlueprintReadWrite, meta = (BindWidget))
	UTextBlock* ValueTextBlock;
	UPROPERTY(EditAnywhere,BlueprintReadWrite, meta = (BindWidget))
	URichTextBlock* ValueRichText;
	UPROPERTY(EditAnywhere,BlueprintReadWrite, meta = (BindWidget))
	UButton* LeftSwitchButton;
	UPROPERTY(EditAnywhere,BlueprintReadWrite, meta = (BindWidget))
	UButton* RightSwitchButton;
	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	TArray<FString>SwitchOptions;
	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	int CurIndex = -1;
	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	FString CurSwitchOption = "";
	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	bool bUseRichText = false;
	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	bool bUseDataText = true;
	
	UFUNCTION(BlueprintCallable)
	virtual  void InitOptions(	TArray<FString> Options);
	//正常的设置 包括回调等正常调用
	UFUNCTION(BlueprintCallable)
	virtual  void SetSwitchByIndex(int Index);

	//正常的设置 包括回调等正常调用
	UFUNCTION(BlueprintCallable)
	virtual  void SetSwitchByOption(FString Option);
	//不产生任何回调调用 外部对表现的初始化强制设置 
	UFUNCTION(BlueprintCallable)
	 void SetSwitchPerformanceByIndex(int Index);
	//不产生任何回调调用 外部对表现的初始化强制设置 
	UFUNCTION(BlueprintCallable)
	void SetSwitchPerformanceByOption(FString Option);
	
	UPROPERTY(BlueprintAssignable)
	FSwitchIndexEvent OnSwitchIndexChange;
	UPROPERTY(BlueprintAssignable)
	FSwitchOptionEvent OnSwitchOptionChange;
};



