// Fill out your copyright notice in the Description page of Project Settings.


#include "RogueSettingsUI.h"

#include "Blueprint/WidgetBlueprintLibrary.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"


void URogueSettingsUI::NativeConstruct()
{
	Super::NativeConstruct();
	
}

void URogueSettingsUI::NativeTick(const FGeometry& MyGeometry, float InDeltaTime)
{
	Super::NativeTick(MyGeometry, InDeltaTime);
	if (IsVisible())
	{ 
		CheckInput();
	}
}

void URogueSettingsUI::K2_OnClose()
{
	if (OnClose.IsBound())
	{
		OnClose.Broadcast(this);
	}
}

void URogueSettingsUI::CheckInput()  
{  
if (GetAwGameState())  
{  
	for (auto PC : UGameplayFuncLib::GetAllLocalAwPlayerControllers())  
	{  
		if (AAwCharacter* Me = PC->CurCharacter)  
		{  
			if(Me->GetCmdComponent()->IsActionOccur(UpCommand,EAwInputState::Press,true))  
			{  
				Prev();  
			}  
			else if(Me->GetCmdComponent()->IsActionOccur(DownCommand,EAwInputState::Press,true))  
			{  
				Next();  
			}  
			else if(Me->GetCmdComponent()->IsActionOccur(ResetCommand,EAwInputState::Press,true))  
			{  
				Reset();  
			}  
			else if(Me->GetCmdComponent()->IsActionOccur(BackCommand,EAwInputState::Press,true))  
			{  
				Back();  
			}  
			else if (Me->GetCmdComponent()->IsActionOccur(ApplyCommand,EAwInputState::Press,true))  
			{  
				OnPreToApply.Broadcast(this);
			}  
		}  
	}  
}  
}










