// Fill out your copyright notice in the Description page of Project Settings.


#include "ShiftDot.h"

#include "Engine/Texture2D.h"
#include "Kismet/KismetMathLibrary.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"



void UShiftDot::NativeOnInitialized()
{
	Super::NativeOnInitialized();

	ShiftDot_Img = Cast<UImage>(GetWidgetFromName(TEXT("Img_ShiftDot")));
}

void UShiftDot::NativeConstruct()
{
	Super::NativeConstruct();
}

void UShiftDot::NativeTick(const FGeometry& MyGeometry, float InDeltaTime)
{
	Super::NativeTick(MyGeometry, InDeltaTime);
}

void UShiftDot::SetShiftDotSelectedStatus(bool IsSelect)
{
	UTexture2D* ShiftDot = UGameplayFuncLib::GetItemIconTextureById("ShiftDot");
	UTexture2D* ShiftDot_Selected = UGameplayFuncLib::GetItemIconTextureById("ShiftDot-Selected");
	
	ShiftDot_Img->SetBrushFromTexture(Cast<UTexture2D>(UKismetMathLibrary::SelectObject(ShiftDot_Selected,ShiftDot,IsSelect)));
}

void UShiftDot::InitShiftDot(int DotIndex, bool IsSelected)
{
	SetIndex(DotIndex);
	SetShiftDotSelectedStatus(IsSelected);
}
