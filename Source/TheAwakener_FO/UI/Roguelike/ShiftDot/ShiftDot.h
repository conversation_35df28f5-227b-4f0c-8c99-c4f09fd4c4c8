// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "Components/Image.h"
#include "ShiftDot.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UShiftDot : public UUserWidget
{
	GENERATED_BODY()


	UPROPERTY()
	int Index = 0;

	virtual void NativeOnInitialized() override;
	virtual void NativeConstruct() override;
	virtual void NativeTick(const FGeometry& MyGeometry, float InDeltaTime) override;

public:

	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	UImage* ShiftDot_Img;

	UFUNCTION(BlueprintCallable,BlueprintPure)
	int GetIndex() const { return Index; }

	UFUNCTION(BlueprintCallable)
	void SetIndex(int DotIndex){ Index = DotIndex;}

	UFUNCTION(BlueprintCallable)
	void SetShiftDotSelectedStatus(bool IsSelect);
	
	UFUNCTION(BlueprintCallable)
	void InitShiftDot(int DotIndex,bool IsSelected);
};
