// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "ShiftDot.h"
#include "Components/HorizontalBox.h"
#include "Blueprint/UserWidget.h"
#include "ShiftDotMain.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UShiftDotMain : public UUserWidget
{
	GENERATED_BODY()

	UPROPERTY()
	int CurSelectIndex = 0;

	UPROPERTY()
	int LastSelectIndex = -1;



	virtual void NativeOnInitialized() override;
	virtual void NativeConstruct() override;
	virtual void NativeTick(const FGeometry& MyGeometry, float InDeltaTime) override;

public:

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	TArray<UShiftDot*> ShiftDotArray;
	
	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UHorizontalBox* HorizontalBox_ShiftDot; 
	
	UFUNCTION(BlueprintCallable,BlueprintPure)
	int GetCurSelectIndex() const {return CurSelectIndex;}

	UFUNCTION(BlueprintCallable)
	void SetCurSelectIndex(int _Index) {CurSelectIndex = _Index;}

	UFUNCTION(BlueprintCallable,BlueprintPure)
	int GetLastSelectIndex() const {return LastSelectIndex;}

	UFUNCTION(BlueprintCallable)
	void SetLastSelectIndex(int _Index) {LastSelectIndex = _Index;}

	UFUNCTION(BlueprintCallable,BlueprintPure)
	TArray<UShiftDot*> GetShiftDotArray() const {return ShiftDotArray;}
	
	UFUNCTION(BlueprintCallable)
	void CreateShiftDot(int dotNum);
};
