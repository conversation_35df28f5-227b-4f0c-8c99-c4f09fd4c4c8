// Fill out your copyright notice in the Description page of Project Settings.


#include "ShiftDotMain.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"

void UShiftDotMain::NativeOnInitialized()
{
	Super::NativeOnInitialized();

	HorizontalBox_ShiftDot = Cast<UHorizontalBox>(GetWidgetFromName("HorBox_ShiftDot"));
}

void UShiftDotMain::NativeConstruct()
{
	Super::NativeConstruct();
	
}

void UShiftDotMain::NativeTick(const FGeometry& MyGeometry, float InDeltaTime)
{
	Super::NativeTick(MyGeometry, InDeltaTime);
}

void UShiftDotMain::CreateShiftDot(int dotNum)
{
	ShiftDotArray.Empty();
	HorizontalBox_ShiftDot->ClearChildren();
	if(dotNum > 0)
	{
		UShiftDot* TempShiftDot = nullptr;
		for(int i = 0;i < dotNum;i++)
		{
			if(UGameplayFuncLib::GetUiManager()->ShowNewUI("ShiftDot"))
			{
				TempShiftDot = Cast<UShiftDot>(UGameplayFuncLib::GetUiManager()->ShowNewUI("ShiftDot"));
				if(TempShiftDot)
				{
					TempShiftDot->InitShiftDot(i,i==0);
					ShiftDotArray.Add(TempShiftDot);
					HorizontalBox_ShiftDot->AddChild(TempShiftDot);
				}
			}
		}
		SetCurSelectIndex(0);
	}
}
