// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/UI/Base/BaseUI.h"
#include "UObject/Object.h"
#include "RogueFightingMainUI.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API URogueFightingMainUI : public UBaseUI
{
	GENERATED_BODY()
public:
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable)
	void ShowMainUI();

	UFUNCTION(BlueprintNativeEvent, BlueprintCallable)
	void HiddeMainUI();
	UFUNCTION(BlueprintImplementableEvent, BlueprintCallable)
	void ShowHitCombo(int CurrentCombo, float ComboNextTime) const;
	UFUNCTION(BlueprintImplementableEvent, BlueprintCallable)
	void ShowKillCombo(int CurrentCombo, float ComboNextTime) const;
};
