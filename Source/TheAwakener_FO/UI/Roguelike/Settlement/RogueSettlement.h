// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/UI/Base/BaseUI.h"
#include "UObject/Object.h"
#include "RogueSettlement.generated.h"

/**
 * 
 */
UENUM(BlueprintType)
enum class ESettlementState : uint8
{
	Changing,
	First,
	Second,
	Third
};

UCLASS()
class THEAWAKENER_FO_API URogueSettlement : public UBaseUI
{
	GENERATED_BODY()

	virtual void NativeOnInitialized() override;
	virtual void NativeConstruct() override;
	virtual void NativeTick(const FGeometry& MyGeometry, float InDeltaTime) override;

public:

	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	ESettlementState CurState = ESettlementState::Changing;

};
