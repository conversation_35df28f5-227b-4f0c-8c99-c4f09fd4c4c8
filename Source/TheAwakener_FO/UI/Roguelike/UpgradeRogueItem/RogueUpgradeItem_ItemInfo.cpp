// Fill out your copyright notice in the Description page of Project Settings.


#include "RogueUpgradeItem_ItemInfo.h"

#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GameFramework/Manager/AwDataManager.h"
#include "TheAwakener_FO/GamePlay/Roguelike/Item/AwRogueItemSubSystem.h"

void URogueUpgradeItem_ItemInfo::NativeOnInitialized()
{
	Super::NativeOnInitialized();
	ItemMaxLevelText->SetVisibility(ESlateVisibility::Collapsed);

	ItemLevelNameText->SetVisibility(ESlateVisibility::Collapsed);;
}

void URogueUpgradeItem_ItemInfo::NativeConstruct()
{
	Super::NativeConstruct();

	UAwRogueItemSubSystem* SubSystem = GetGameInstance()->GetSubsystem<UAwRogueItemSubSystem>();
	if (SubSystem)
	{
		FAwRogueItemInfo CurIteminfo = SubSystem->GetCurRogueItem();
		//设置文本
		UAwDataManager* DataManager = UGameplayFuncLib::GetAwDataManager();
		if (CurIteminfo.Item.Model.Id.IsEmpty())
		{
			ItemText = DataManager->GetTextByKey("RogueItemSlot");
		}
		else
		{
			ItemText = DataManager->GetTextByKey(CurIteminfo.Item.Model.Id);
		}
		FAwRogueItemInfo CurItemInfoAfterModify = SubSystem->GetCurRogueItem(true);
		int CurItemLevel =CurItemInfoAfterModify.CurEffectLevel;
		if (bAfterUpgrade)
		{
			++CurItemLevel;
		}
		else
		{
			ItemLevelNameText->SetVisibility(ESlateVisibility::SelfHitTestInvisible);
		}
		int MaxLevel = CurItemInfoAfterModify.MaxEffectLevel;
		CurItemLevel = FMath::Clamp(CurItemLevel,1,MaxLevel);
		FString LevelText = FString::FromInt(CurItemLevel);
	//	FString LevelText = CurItemLevel<MaxLevel?FString::FromInt(CurItemLevel):"Max";
		ItemLevelText->SetText(FText::FromString(LevelText));
		if (CurItemLevel>=MaxLevel)
		{
			ItemMaxLevelText->SetVisibility(ESlateVisibility::SelfHitTestInvisible);
		}
		//ItemNameText->SetText(FText::FromString(ItemText));
		//设置文本描述
		//FString DescText = "";
		//if (!CurIteminfo.Item.Model.Id.IsEmpty())
		//{
		//	DescText = DataManager->GetTextByKey(CurIteminfo.Item.Model.Id+"_"+FString::FromInt(CurItemLevel)+"_Desc");
		//}
		//UpgradeDescText->SetText(FText::FromString(DescText));
		//设置图片
		/*
		if (!CurIteminfo.Item.Model.Id.IsEmpty())
		{
			ItemIcon->SetVisibility(ESlateVisibility::SelfHitTestInvisible);
			//道具icon
			FString ItemIconPath = DataManager->GetItemIconById(CurIteminfo.Item.Model.Id).Path;
			UTexture2D* ItemIconTex = LoadObject<UTexture2D>(nullptr,*UResourceFuncLib::GetAssetPath(ItemIconPath));
			if (!ItemIconTex)
			{
				UE_LOG(LogTemp,Error,TEXT("LoadTexture Failed Path:%s"),*ItemIconPath);
				return;
			}
			ItemIcon->SetBrushFromTexture(ItemIconTex);
		}
		else
		{
			ItemIcon->SetVisibility(ESlateVisibility::Collapsed);
		}
		*/
		//道具等级外框icon
		/*
		FString ItemLevel = "RogueItem_Level_" + FString::FromInt(CurItemLevel);
		FString ItemLevelIconPath = DataManager->GetItemIconById(ItemLevel).Path;
		UTexture2D* ItemLevelIconTex = LoadObject<UTexture2D>(nullptr,*UResourceFuncLib::GetAssetPath(ItemLevelIconPath));
		if (!ItemLevelIconTex)
		{
			UE_LOG(LogTemp,Error,TEXT("LoadTexture Failed Path:%s"),*ItemLevelIconPath);
			return;
		}
		ItemLevelIcon ->SetBrushFromTexture(ItemLevelIconTex);
		*/
	}
}

void URogueUpgradeItem_ItemInfo::NativeTick(const FGeometry& MyGeometry, float InDeltaTime)
{
	Super::NativeTick(MyGeometry, InDeltaTime);
	
}
