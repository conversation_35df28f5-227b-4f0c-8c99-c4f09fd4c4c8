// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Components/Border.h"
#include "Components/RichTextBlock.h"
#include "TheAwakener_FO/UI/Base/BaseUI.h"

#include "RogueUpgradeItem_ItemInfo.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API URogueUpgradeItem_ItemInfo : public UBaseUI
{
	GENERATED_BODY()

	
	virtual void NativeOnInitialized() override;
	virtual void NativeConstruct() override;
	virtual void NativeTick(const FGeometry& MyGeometry, float InDeltaTime) override;

public:
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool bAfterUpgrade = false;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString ItemText ="";
	UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (BindWidget))
	UUserWidget* ItemWidget;
	//UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (BindWidget))
	//UImage* ItemIcon;
	//UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (BindWidget))
	//UImage* ItemLevelIcon;
	UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (BindWidget))
	UBorder* BgMask;
	//UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (BindWidget))
	//UTextBlock* ItemNameText;
	UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (BindWidget))
	UTextBlock* ItemLevelText;
	UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (BindWidget))
	UTextBlock* ItemMaxLevelText;
	UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (BindWidget))
	UTextBlock* ItemLevelNameText;
	//UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (BindWidget))
	//URichTextBlock* UpgradeDescText;

};
