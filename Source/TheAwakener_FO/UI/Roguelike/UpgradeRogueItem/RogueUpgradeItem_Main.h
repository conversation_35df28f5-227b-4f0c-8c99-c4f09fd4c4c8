// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/UI/Base/BaseUI.h"

#include "RogueUpgradeItem_Main.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API URogueUpgradeItem_Main : public UBaseUI
{
	GENERATED_BODY()

	
	virtual void NativeOnInitialized() override;
	virtual void NativeConstruct() override;
	virtual void NativeTick(const FGeometry& MyGeometry, float InDeltaTime) override;

public:
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int CostNum = 0;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString CostSource = "Rogue_Coin";
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool MustHasItem = false;
	UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (BindWidget))
	UImage* BgImage;
	UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (BindWidget))
	UTextBlock* UpgradeText;
	UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (BindWidget))
	UTextBlock* CostText;

	
	UFUNCTION(BlueprintPure)
	bool CheckCanUpgrade(FString& NotFitReason);
	
	UFUNCTION(BlueprintCallable)
	bool CheckPlayerHasBuff(FString BuffId);
	
	UFUNCTION(BlueprintImplementableEvent,BlueprintCallable)
	void Confirm();

	UFUNCTION(BlueprintImplementableEvent,BlueprintCallable)
	void CancelConfirm();
	
	UFUNCTION(BlueprintImplementableEvent,BlueprintCallable)
	void Back();

	UFUNCTION(BlueprintImplementableEvent,BlueprintCallable)
	void TipsBoxConfirm();

	UFUNCTION(BlueprintImplementableEvent,BlueprintCallable)
	void TipsBoxCancel();

	bool InHoverConfirem = false;
};
