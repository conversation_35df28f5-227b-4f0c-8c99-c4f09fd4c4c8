// Fill out your copyright notice in the Description page of Project Settings.


#include "RogueUpgradeItem_Main.h"

#include "Blueprint/WidgetBlueprintLibrary.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/Gameframework/Base/AwGameState.h"
#include "TheAwakener_FO/GameFramework/Manager/AwRogueDataSystem.h"

void URogueUpgradeItem_Main::NativeOnInitialized()
{
	Super::NativeOnInitialized();
}

void URogueUpgradeItem_Main::NativeConstruct()
{
	Super::NativeConstruct();

	//初始化输入模式
	AAwPlayerController* MyPlayercontroller = UGameplayFuncLib::GetPlayerControllerByWidget(this);
	if (MyPlayercontroller)
	{
		MyPlayercontroller->GameControlState = EGameControlState::UpgradeActiveItem;
		UWidgetBlueprintLibrary::SetInputMode_GameAndUIEx(MyPlayercontroller,this);
	}
	//初始化消耗
	UAwRogueItemSubSystem* SubSystem = GetGameInstance()->GetSubsystem<UAwRogueItemSubSystem>();
	if (SubSystem&&SubSystem->GetCurRogueItem(true).CurEffectLevel==1)
	{
		CostNum =200;
	}
	else
	{
		CostNum =500;
	}
	
	//检测打折buff
	int DiscountNum = 0;
	AAwCharacter* MyPlayerCharacter = MyPlayercontroller->CurCharacter;
	for (auto Buff:MyPlayerCharacter->CharacterObj.Buff)
	{
		if (Buff.Model.Id == "Rogue_ShopDiscount")
		{
			DiscountNum += Buff.Stack;
		}
		else if (Buff.Model.Id == "Rogue_ShopMoreExpensive")
		{
			DiscountNum -=Buff.Stack;
		}
	}
	//初始化消耗文本
	CostNum =  FMath::RoundToInt(CostNum*FMath::Clamp((100.f-DiscountNum)/100.f,0, 100.f));

	FString CostTextStr = FString::FromInt(CostNum);
	CostText->SetText(FText::FromString(CostTextStr));
}

void URogueUpgradeItem_Main::NativeTick(const FGeometry& MyGeometry, float InDeltaTime)
{
	Super::NativeTick(MyGeometry, InDeltaTime);

	UpdateShowMouse();
	
	if (GetAwGameState())
	{
		AAwCharacter* Me = GetAwGameState()->GetMyCharacter();
		
		 if(!InHoverConfirem
		   &&(Me->GetCmdComponent()->IsActionOccur("UpgradeActiveItem_Confirm",EAwInputState::Press,false)
			||Me->GetCmdComponent()->IsActionOccur("UpgradeActiveItem_Confirm",EAwInputState::Hold,false))
		 )
		{
			Confirm();
		 	InHoverConfirem = true;
		}
		else if(Me->GetCmdComponent()->IsActionOccur("UpgradeActiveItem_Refuse",EAwInputState::Press,true))
		{
			Back();
		}
		 else if(!Me->GetCmdComponent()->IsActionOccur("UpgradeActiveItem_Confirm",EAwInputState::Press,false)
		 	&&!Me->GetCmdComponent()->IsActionOccur("UpgradeActiveItem_Confirm",EAwInputState::Hold,false)
		 	&&InHoverConfirem)
		 {      
			 CancelConfirm();
		 	InHoverConfirem = false;
		 }
	}
}

bool URogueUpgradeItem_Main::CheckCanUpgrade(FString& NotFitReason)
{
	bool Result = false;
	NotFitReason = "";
	UAwRogueItemSubSystem* SubSystem = GetGameInstance()->GetSubsystem<UAwRogueItemSubSystem>();
	UAwRogueDataSystem* RogueDataSystem = GetGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	
	if (!SubSystem||!RogueDataSystem)
	{
		NotFitReason = "Error! SubSystem is not valid";
		return  Result;
	}
	FAwRogueItemInfo CurIteminfo = SubSystem->GetCurRogueItem();
	if (MustHasItem&&CurIteminfo.Item.Model.Id.IsEmpty())
	{
		NotFitReason= "Rogue_NoNeededRogueItem";
		return  Result;
	}
	if(RogueDataSystem->GetCurrency_Coin() < CostNum)
	{
		NotFitReason= "Rogue_CoinNotEnough";
		return  Result;
	}

	if (SubSystem->GetCurRogueItem(true).CurEffectLevel>=SubSystem->GetCurRogueItem(true).MaxEffectLevel)
	{
		NotFitReason = "Rogue_RogueItemLevelMax";
		return  Result;
	}
	Result = true;
	
	return Result;
}

bool URogueUpgradeItem_Main::CheckPlayerHasBuff(FString BuffId)
{
	AAwCharacter* MyPlayerCharacter = UGameplayFuncLib::GetPlayerControllerByWidget(this)->CurCharacter;
	if (MyPlayerCharacter)
	{
			return  MyPlayerCharacter->CheckHasBuff(BuffId);
	}
	return  false;
}
