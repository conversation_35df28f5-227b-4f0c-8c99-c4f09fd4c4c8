// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/UI/Base/BaseUI.h"
#include "RogueTargetSigns.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API URogueTargetSigns : public UBaseUI
{
	GENERATED_BODY()

	virtual void NativeOnInitialized() override;
	virtual void NativeConstruct() override;
	virtual void NativeTick(const FGeometry& MyGeometry, float InDeltaTime) override;

public:
	
	UFUNCTION(BlueprintImplementableEvent,BlueprintCallable)
	void AddNewTargetAcotr (AActor* NewTarget);
	
};
