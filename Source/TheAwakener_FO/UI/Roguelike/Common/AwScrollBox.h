// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Components/ScrollBox.h"
#include "AwScrollBox.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UAwScrollBox : public UScrollBox
{
	GENERATED_BODY()

private:
	FTSTicker::FDelegateHandle MyTickHandle;

	float LastOffSet = 0;

	bool DoAlignment = false;

	int ForceIndex = 0;

public:
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category=AwScrollBox)
	float EachHeight = 50;
	
	virtual void OnWidgetRebuilt() override;
	
	bool Tick(float DeltaTime);

	void AlignmentOffSet();

	void AlignmentOffSetToIndex(int ToIndex);

	UFUNCTION(BlueprintPure, Category=AwScrollBox)
	int GetForceIndex() const;

	UFUNCTION(BlueprintPure, Category=AwScrollBox)
	float GetForceOffSet_ByEachHeight();

	UFUNCTION(BlueprintCallable, Category=AwScrollBox)
	void SetForceIndex(int Index, EDescendantScrollDestination InScrollDestination = EDescendantScrollDestination::IntoView);

	UFUNCTION(BlueprintCallable, Category=AwScrollBox)
	void ModifyForceIndex(int Index, EDescendantScrollDestination InScrollDestination = EDescendantScrollDestination::IntoView);
};
