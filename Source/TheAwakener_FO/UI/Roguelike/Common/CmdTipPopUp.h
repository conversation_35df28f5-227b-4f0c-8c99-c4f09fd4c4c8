// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "CmdTipPopUp.generated.h"

USTRUCT(BlueprintType)
struct FRogueCmdTipInfo
{
	GENERATED_BODY()

public:
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString ActionCmd;

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString DescKey;
};

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UCmdTipPopUp : public UUserWidget
{
	GENERATED_BODY()

public:
	UFUNCTION(BlueprintCallable, BlueprintNativeEvent)
	void Setup(const TArray<FRogueCmdTipInfo>& CmdTipInfo, float Duration);

	UFUNCTION(BlueprintCallable, BlueprintNativeEvent)
	void ShowUI();

	UFUNCTION(BlueprintCallable, BlueprintNativeEvent)
	void HideUI();
};
