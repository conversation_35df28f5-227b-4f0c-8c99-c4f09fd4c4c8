// Fill out your copyright notice in the Description page of Project Settings.


#include "AwScrollBox.h"

#include "Blueprint/WidgetLayoutLibrary.h"
#include "Components/CanvasPanelSlot.h"
#include "Kismet/KismetSystemLibrary.h"

void UAwScrollBox::OnWidgetRebuilt()
{
	Super::OnWidgetRebuilt();
	
	// if (!MyTickHandle.IsValid())
	// 	MyTickHandle = FTSTicker::GetCoreTicker().AddTicker(FTickerDelegate::CreateUObject(this, &UAwScrollBox::Tick), 0);
}

bool UAwScrollBox::Tick(float DeltaTime)
{
	if (!IsVisible())
		return false;
	
	// const FString Str = FString::SanitizeFloat(DeltaTime);
	// UKismetSystemLibrary::PrintString(this, Str, true, true, FLinearColor::Green, 0);

	const float NowOffSet = GetScrollOffset();
	
	if (NowOffSet != LastOffSet)
		DoAlignment = false;
	
	if (LastOffSet == NowOffSet && !DoAlignment)
	{
		AlignmentOffSet();
		DoAlignment = true;
	}

	LastOffSet = NowOffSet;
	
	return true;
}

void UAwScrollBox::AlignmentOffSet()
{
	const float CurOffSet = GetScrollOffset();
	
	// TArray<UWidget*> Children = GetAllChildren();
	// TArray<FVector2D> AllSize;
	// for (UWidget* Child : Children)
	// 	AllSize.Add(Child->GetDesiredSize());
	//
	// float TotalSizeY = 0;
	// for (const FVector2D Size : AllSize)
	// 	TotalSizeY += Size.Y;

	const float TotalSizeY = GetChildrenCount() * EachHeight;
	const float BoxSizeY = UWidgetLayoutLibrary::SlotAsCanvasSlot(this)->GetSize().Y;

	if (TotalSizeY <= BoxSizeY)
	{
		ScrollToStart();
		return;
	}

	if (CurOffSet + BoxSizeY >= TotalSizeY)
	{
		ScrollToEnd();
		return;
	}

	// float Min;
	// float Max;
	// for (int i = 0; i < AllSize.Num(); ++i)
	// {
	// 	float TargetOffSet = 0;
	// 	if (i == 0)
	// 	{
	// 		Min = - AllSize[i].Y / 2;
	// 		Max = AllSize[i].Y / 2;
	// 	}
	// 	else
	// 	{
	// 		for (int j = 0; j < i; ++j)
	// 			TargetOffSet += AllSize[j].Y; 
	// 		Min = TargetOffSet - AllSize[i-1].Y / 2;
	// 		Max = TargetOffSet + AllSize[i].Y / 2;
	// 	}
	// 	if (CurOffSet >= Min && CurOffSet <= Max)
	// 	{
	// 		SetScrollOffset(TargetOffSet);
	// 		break;
	// 	}
	// }

	for (int i = 0; i < GetChildrenCount(); ++i)
	{
		const float Min = EachHeight * i - EachHeight / 2;
		const float Max = EachHeight * i + EachHeight / 2;

		if (CurOffSet >= Min && CurOffSet <= Max)
		{
			SetScrollOffset(EachHeight * i);
			break;
		}
	}
}

void UAwScrollBox::AlignmentOffSetToIndex(int ToIndex)
{
	const float CurOffSet = GetScrollOffset();
	const float BoxSizeY = UWidgetLayoutLibrary::SlotAsCanvasSlot(this)->GetSize().Y;

	const int FirstIndex = CurOffSet / EachHeight;
	const int EndIndex = FirstIndex + BoxSizeY / EachHeight - 1;

	if (ToIndex >= FirstIndex && ToIndex <= EndIndex)
		return;

	float TargetOffSet = ToIndex * EachHeight;
	if (ToIndex < FirstIndex)
		TargetOffSet = CurOffSet + (ToIndex - FirstIndex) * EachHeight;
	if (ToIndex > EndIndex)
		TargetOffSet = CurOffSet + (ToIndex - EndIndex) * EachHeight;
	
	const float TotalSizeY = GetChildrenCount() * EachHeight;
	
	if (TargetOffSet < 0)
		ScrollToStart();
	else if (TargetOffSet + BoxSizeY >= TotalSizeY)
		ScrollToEnd();
	else
		SetScrollOffset(TargetOffSet);
}

int UAwScrollBox::GetForceIndex() const
{
	return ForceIndex;
}

float UAwScrollBox::GetForceOffSet_ByEachHeight()
{
	const FVector2D Size = UWidgetLayoutLibrary::SlotAsCanvasSlot(this)->GetSize();
	float MaxHeight = Size.Y;
	if (this->GetOrientation() == Orient_Horizontal)
		MaxHeight = Size.X;
	return FMath::Clamp(this->GetForceIndex() * EachHeight - GetScrollOffset(), 0, MaxHeight - EachHeight);
}

void UAwScrollBox::SetForceIndex(int Index, EDescendantScrollDestination InScrollDestination)
{
	ForceIndex = Index;

	UWidget* Child = this->GetChildAt(Index);
	if (Child)
		ScrollWidgetIntoView(Child, true, InScrollDestination, 0);
	
	// AlignmentOffSetToIndex(ForceIndex);
}

void UAwScrollBox::ModifyForceIndex(int Index, EDescendantScrollDestination InScrollDestination)
{
	ForceIndex = FMath::Clamp(ForceIndex + Index, 0, GetChildrenCount() - 1);

	UWidget* Child = this->GetChildAt(ForceIndex);
	if (Child)
		ScrollWidgetIntoView(Child, true, InScrollDestination, 0);
	
	AlignmentOffSetToIndex(ForceIndex);
}
