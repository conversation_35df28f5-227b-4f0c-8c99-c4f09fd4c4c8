// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/UI/Base/BaseUI.h"
#include "SelectionRoomMain.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API USelectionRoomMain : public UBaseUI
{
	GENERATED_BODY()


	UPROPERTY()
	int SelectionIndex = -1;

	UPROPERTY()
	int LastSelectionIndex = -1;
	
	UPROPERTY()
	AAwGameState* GameState;

	
	virtual void NativeOnInitialized() override;
	virtual void NativeConstruct() override;
	virtual void NativeTick(const FGeometry& MyGeometry, float InDeltaTime) override;

public:

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UHorizontalBox* SelectionRoom_HorizontalBox;

	UFUNCTION(BlueprintCallable,BlueprintPure)
	int GetSelectedIndex(){return SelectionIndex;}
	
	UFUNCTION(BlueprintCallable)
	void SetSelectedIndex(int Index) {SelectionIndex = Index;}

	UFUNCTION(BlueprintCallable,BlueprintPure)
	int GetLastSelectedIndex(){return LastSelectionIndex;}
	
	UFUNCTION(BlueprintCallable)
	void SetLastSelectedIndex(int Index) {LastSelectionIndex = Index;}

	UFUNCTION(BlueprintCallable)
	void ChangeMouseShow();

	UFUNCTION(BlueprintCallable,BlueprintImplementableEvent)
	void FUNC_SelectedLeft();

	UFUNCTION(BlueprintCallable,BlueprintImplementableEvent)
	void FUNC_SelectedRight();

	UFUNCTION(BlueprintCallable,BlueprintImplementableEvent)
	void FUNC_SelectedConfirm();
};
