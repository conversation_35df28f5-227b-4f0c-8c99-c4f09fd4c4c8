// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/UI/Base/BaseUI.h"
#include "Components/Spacer.h"
#include "SelectionRoomOptions.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API USelectionRoomOptions : public UBaseUI
{
	GENERATED_BODY()

	virtual void NativeOnInitialized() override;
	virtual void NativeConstruct() override;
	virtual void NativeTick(const FGeometry& MyGeometry, float InDeltaTime) override;


public:

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UImage* BK_Image;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UTextBlock* RoomName_TextBlock;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UImage* RoomIcon_Image;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UTextBlock* RoomDescription_TextBlock;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UButton* Selected_Button;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	USpacer* Interval_Spacer;
	
};
