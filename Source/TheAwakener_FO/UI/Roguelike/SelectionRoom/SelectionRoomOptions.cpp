// Fill out your copyright notice in the Description page of Project Settings.


#include "SelectionRoomOptions.h"

void USelectionRoomOptions::NativeOnInitialized()
{
	Super::NativeOnInitialized();

	BK_Image = Cast<UImage>(GetWidgetFromName(TEXT("BK")));

	RoomName_TextBlock = Cast<UTextBlock>(GetWidgetFromName(TEXT("RoomName")));

	RoomIcon_Image = Cast<UImage>(GetWidgetFromName(TEXT("RoomIcon")));

	RoomDescription_TextBlock = Cast<UTextBlock>(GetWidgetFromName(TEXT("RoomDescription")));

	Selected_Button = Cast<UButton>(GetWidgetFromName(TEXT("Selected_BT")));

	Interval_Spacer = Cast<USpacer>(GetWidgetFromName(TEXT("Interval"))); 
}

void USelectionRoomOptions::NativeConstruct()
{
	Super::NativeConstruct();
}

void USelectionRoomOptions::NativeTick(const FGeometry& MyGeometry, float InDeltaTime)
{
	Super::NativeTick(MyGeometry, InDeltaTime);
}
