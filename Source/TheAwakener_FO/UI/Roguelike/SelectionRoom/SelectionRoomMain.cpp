// Fill out your copyright notice in the Description page of Project Settings.


#include "SelectionRoomMain.h"

#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"

void USelectionRoomMain::NativeOnInitialized()
{
	Super::NativeOnInitialized();

	SelectionRoom_HorizontalBox = Cast<UHorizontalBox>(GetWidgetFromName(TEXT("SelectionRoomHorizontalBox")));
}

void USelectionRoomMain::NativeConstruct()
{
	Super::NativeConstruct();

	GameState = UGameplayFuncLib::GetAwGameState();
	ChangeMouseShow();
}

void USelectionRoomMain::NativeTick(const FGeometry& MyGeometry, float InDeltaTime)
{
	Super::NativeTick(MyGeometry, InDeltaTime);
	ChangeMouseShow();
	if (GameState)
	{
		AAwCharacter* Me = GameState->GetMyCharacter();
		if(Me->GetCmdComponent()->IsActionOccur("RogueRewardsAndRoom_Left",EAwInputState::Press,true) ||
			Me->GetCmdComponent()->IsActionOccur("RogueRewardsAndRoom_Left",EAwInputState::Hold,true))
		{
			FUNC_SelectedLeft();
		}
		else if(Me->GetCmdComponent()->IsActionOccur("RogueRewardsAndRoom_Right",EAwInputState::Press,true) ||
			Me->GetCmdComponent()->IsActionOccur("RogueRewardsAndRoom_Right",EAwInputState::Hold,true))
		{
			FUNC_SelectedRight();
		}
		/*else if(Me->GetCmdComponent()->IsActionOccur("RogueRewardsAndRoom_Confirm",EAwInputState::Press,true))
		{
			FUNC_SelectedConfirm();
		}*/
	}


}

void USelectionRoomMain::ChangeMouseShow()
{
	if(!UGameplayFuncLib::GetPlayerControllerByWidget(this))
		return;
	if(UAwGameInstance::Instance->bIsGamepad)
	{
		UGameplayFuncLib::GetPlayerControllerByWidget(this)->SetShowMouseCursor(false);
	}
	else
	{
		UGameplayFuncLib::GetPlayerControllerByWidget(this)->SetShowMouseCursor(true);
	}
}
