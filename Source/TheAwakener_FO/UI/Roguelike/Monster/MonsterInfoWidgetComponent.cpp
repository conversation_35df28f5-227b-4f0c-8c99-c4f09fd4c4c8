// Fill out your copyright notice in the Description page of Project Settings.


#include "MonsterInfoWidgetComponent.h"

#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/UI/Roguelike/RogueTargetSign/RogueTargetSigns.h"

UMonsterInfoWidgetComponent::UMonsterInfoWidgetComponent()
{
	SetWidgetSpace(EWidgetSpace::Screen);
	
}

void UMonsterInfoWidgetComponent::BeginPlay()
{
	Super::BeginPlay();
	OwnerCharacter= Cast<AAwCharacter>(GetOwner());
	PlayerCharacter =  UGameplayFuncLib::GetLocalAwPlayerCharacter(0);//单机部分
	if (UGameplayFuncLib::IsRogueMode() && UGameplayFuncLib::GetAwGameInstance() &&bUseEnemySign)
	{
		UAwUIManager* UIManager = UGameplayFuncLib::GetUiManager();
		if (UIManager)
		{
			if(UIManager->OpenedWidgets.Contains("TargetSigns"))
			{
				URogueTargetSigns* SignWidget = Cast<URogueTargetSigns>(UIManager->OpenedWidgets["TargetSigns"]);
				SignWidget->AddNewTargetAcotr(OwnerCharacter.Get());
			}
		}
	}
}

void UMonsterInfoWidgetComponent::TickComponent(float DeltaTime, ELevelTick TickType,
	FActorComponentTickFunction* ThisTickFunction)
{
	Super::TickComponent(DeltaTime, TickType, ThisTickFunction);

	if (IsValid(GetWidget()))
	{
		UBaseUI*  CurWidget  = Cast<UBaseUI>(GetWidget());

		if (PlayerCharacter.IsValid()&&OwnerCharacter.IsValid())
		{
			bool bOpen =  FVector::DistSquared(PlayerCharacter.Get()->GetActorLocation(),GetComponentLocation())>(ShowDistance*ShowDistance)?false:true;
			if (CurWidget->Implements<UAwUIInterface>())
			{
				if (bOpen)
				{
					IAwUIInterface::Execute_OnOpenWidget(CurWidget);
				}
				else
				{
					IAwUIInterface::Execute_OnCloseWidget(CurWidget);
				}
			}
			bool bActive = FVector::DistSquared(PlayerCharacter.Get()->GetActorLocation(),GetComponentLocation())>(DefaultIconDistance*DefaultIconDistance)?true:false;
			if (CurWidget->Implements<UAwUIInterface>())
			{
				IAwUIInterface::Execute_SetWidgetActive(CurWidget,bActive);
			}
		}
	}
}
