// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/UI/Base/BaseForm.h"
#include "UMG.h"
#include "Widgets/Input/SButton.h"
#include "Components/RetainerBox.h"
#include "MainMenu.generated.h"

class USubMenu;

UENUM(BlueprintType)
enum class EStateMenu :uint8
{
	//正在进入
	EnterState,// UMETA(DisplayName = "Enter"),

	//在进入之后处于当前Panel未作变化的状态，当前状态
	CurrentState,

	//切换panel时进入的状态
	ChangeState,

	//退出时的状态
	ExitState
};

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UMainMenu : public UBaseForm
{
	GENERATED_BODY()
	
public:
	UPROPERTY(BlueprintReadWrite, Category = "MainMenu|Property")
	USubMenu* CurWidget;

	UPROPERTY(BlueprintReadWrite, Category = "MainMenu|Property")
	USubMenu* PushWidget;

	//主菜单的状态
	UPROPERTY(BlueprintReadWrite, Category = "MainMenu|Property")
	EStateMenu MainMenuState;

	UPROPERTY(BlueprintReadWrite, Category = "MainMenu|Porperty")
	AActor* MediaSoundPlayer = nullptr;

public:

	//在进行EnterTick之后所执行的事件（要在EnterEnd之后执行）
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "SubMenu|BaseFUC")
	void MenuEnterFinished();
	virtual void MenuEnterFinished_Implementation();

	//在进行ExitTick之后所执行的事件（要在EnterEnd之后执行）
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "SubMenu|BaseFUC")
	void MenuExitFinished();
	virtual void MenuExitFinished_Implementation();

	UFUNCTION(BlueprintNativeEvent, BlueprintPure, Category = "SubMenu|BaseFUC")
	URetainerBox* GetMenuMask();
	virtual URetainerBox* GetMenuMask_Implementation();

	UMainMenu();
};
