// Fill out your copyright notice in the Description page of Project Settings.


#include "CharacterInfo.h"



void UCharacterInfo::SetCharacterRotation(AActor* Character, float Offset, float DeltaTime)
{
	float lerpValue = FMath::Lerp(Character->GetActorRotation().Yaw, Character->GetActorRotation().Yaw + Offset, DeltaTime);
	FRotator Rotator = { Character->GetActorRotation().Roll, Character->GetActorRotation().Pitch, lerpValue };
	Character->SetActorRotation(Rotator);
}