// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/UI/Base/BasePanel.h"
#include "UMG.h"
#include "Components/Image.h"
#include "SkillLine.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API USkillLine : public UBasePanel
{
	GENERATED_BODY()
	
private:
	
	//存储LineImage
	TArray<UImage*> Images;

public:

	/*
	* 设置Line图片的大小
	* @Param int SelfIndex 是处在什么位置
	* 位置分为三段（上，中，下）
	* @Param FVector2D Size 是线框的大小
	* 该线框的大小是上中下三个SkillUI框的总大小
	*/
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable)
	void SetLineSize(int SelfIndex, FVector2D Size);
	void SetLineSize_Implementation(int SelfIndex, FVector2D Size);

	/*
	* 根据Target和SelfIndex来显示Line
	* @Param SelfIndex 自身编号
	* @Target 打断的是第几个SkillUI
	*/
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable)
	void ShowLine(int SelfIndex, const TArray<int32>& Target);
	void ShowLine_Implementation(int SelfIndex, const TArray<int32>& Target);

	UFUNCTION(BlueprintCallable)
	void AddImages(UImage* Image);

};
