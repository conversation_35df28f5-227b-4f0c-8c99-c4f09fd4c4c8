// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Base/Skill_BasePanel.h"
#include "SkillUI.h"
#include "SkillLine.h"
#include "ChooseSkillPanel.h"
#include "SkillPanel.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API USkillPanel : public USkill_BasePanel
{
	GENERATED_BODY()

private:

	/*
	存放SkillUI的数组
	排列顺序：
	*****Ground*******
	
	0	3	6	9	
	1	4	7	10
	2	5	8	11

	*******air********

	12	15	18	21
	13	16	19	22
	14	17	20	23
	*/
	TArray<USkillUI*> SkillUIArray;


public:

	//存储每两个SkillUI之间的SkillLine
	UPROPERTY(BlueprintReadWrite)
	TArray<USkillLine*> SkillLineArray;

	//当前选中的UI
	UPROPERTY(BlueprintReadWrite)
	USkillUI* CurSkillUI;


public:

	//初始化
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable)
	void Initial();
	virtual void Initial_Implementation();

	//根据Index获得SkillUI
	UFUNCTION(BlueprintPure)
	USkillUI* GetSkillUIByIndex(int Index);

	//获得当前CurSkillUI的Action
	UFUNCTION(BlueprintPure)
	FActionInfo GetCurSkillUIAction();

	UFUNCTION(BlueprintCallable)
	USkillUI* CreateSkillUI();

	/*
	@Param Move 是移动的index
	0 : 上移
	1 : 下移
	2 : 左移
	3 : 右移

	之后可能要做调整现在只能在SkillUI内移动
	*/ 
	UFUNCTION(BlueprintCallable)
	USkillUI* CrossMoveKeySkillUI(int Move);

	//获取可以对应打断该SkillUI的SkillUIIndex
	//@Param index 是SkillUI数组的index，是要查询其能被Cancel的Index
	UFUNCTION(BlueprintCallable)
	TArray<int> GetCanCancelSkill(int Index = 0);

	UFUNCTION(BlueprintNativeEvent,BlueprintCallable)
	void CreateSkillLine();
	void CreateSkillLine_Implementation();

	//初始化数据
	void DoDefaultData();
	
	//查看存储的数据，有则覆盖默认
	//若没有则用默认数据
	void CheckHaveSaveData();


	//根据SkillUI找到上一列
	UFUNCTION(BlueprintPure)
	TArray<USkillUI*> GetLastGroup(USkillUI* SkillUI);

	/*
	@Param Images 指定的图片组，最多六个图片
	预设好就六张图片，每两张图片组成一个图，也就是一个主图加上一个以主图(Icon)右下角为锚点的副图(PassiveIcon）

	功能:根据上一组的图片信息指定给图片组
	*/
	UFUNCTION(BlueprintCallable)
	void SetLastGroupImg(TArray<UImage*> Images);

	//判断是否是地面技能
	UFUNCTION(BlueprintPure)
	bool IsGround();

	//是地面第一组或者是空中第一组
	bool IsFirstGroup();

	//找到SkillUI的当前编号
	UFUNCTION(BlueprintCallable)
	int GetSkillUIIndex(USkillUI* SkillUI);

	/*
	* 用输入的参数Action，看是否能被上一组打断
	* @ FActionInfo Action 输入的参数Action
	*/
	UFUNCTION(BlueprintPure)
	TArray<int> GetCurSkillCancelLastGroup(FActionInfo Action);

	//刷新所有的SkillUI
	UFUNCTION(BlueprintCallable)
	void RefreshAllSkillUI();

	//会绑定SkillUI的一个单播委托，用于确定是否点击了某个SkillUI
	UFUNCTION(BlueprintNativeEvent)
	void ToSetCurSkill(USkillUI* SkillUI);
	void ToSetCurSkill_Implementation(USkillUI* SkillUI);

	// 会绑定SkillUI的一个单播委托
	UFUNCTION(BlueprintNativeEvent)
	void ToRefreshAllSkillUI();
	void ToRefreshAllSkillUI_Implementation();
};