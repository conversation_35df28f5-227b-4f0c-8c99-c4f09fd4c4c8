// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Base/Skill_BasePanel.h"
#include "Components/Image.h"
#include "UMG.h"
#include "MediaSource.h"
#include "MediaPlayer.h"
#include "SkillUI.h"
#include "ChooseSkill.generated.h"

//关于是否按下确认键的单播委托
DECLARE_DELEGATE_OneParam(MouseCrossOK, UChooseSkill*);

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UChooseSkill : public USkill_BasePanel
{
	GENERATED_BODY()
	
public:
	
	//播放时的声音播放
	UPROPERTY(BlueprintReadOnly)
	AActor* MediaSoundPlayer = nullptr;

	//用于播放的播放器
	UPROPERTY(BlueprintReadWrite)
	UMediaPlayer* Media = nullptr;

	//关于中心点的偏移距离
	UPROPERTY(BlueprintReadWrite)
	float MoveToPostionY = 0;

	//存放当前的SkillUI的Action，便于显示是什么UI
	FActionInfo* Action;

	//创建单播委托
	MouseCrossOK D_MouseOK;

public:
	
	//初始化
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable)
	void Initial(AActor* SoundPlayer);
	void Initial_Implementation(AActor* SoundPlayer);

	//是否Action有效（不为空）
	UFUNCTION(BlueprintPure)
	bool IsHasAction();

	//获得当前Action的数值而非引用（主要用于蓝图中）
	UFUNCTION(BlueprintPure)
	FActionInfo GetChooseSkillAction();

	//获得Media资源
	UFUNCTION(BlueprintPure)
	UMediaSource* GetMediaSource();

	//获得Media资源的地址，该地址是在UIJson里通过Action->Id来获得地址信息
	UFUNCTION(BlueprintPure)
	FString GetMediaSourcePath();

	//获得Action显示名，也就是UI上该显示的名字，也是在UIJson中通过Action->Id来找到对应的UIName
	UFUNCTION(BlueprintPure)
	FText GetActionUIName();

	//获得ActionUI图标，也是在UIJson中通过Action->Id来找到对应的UIIcon
	UFUNCTION(BlueprintPure)
	UTexture2D* GetActionUIIcon();

	/*
	* 用于蓝图里按钮Click时触发的事件
	* 主要功能是当鼠标点击到ChooseSkill时，向ChooseSkillPanel发出通信
	*/
	UFUNCTION(BlueprintCallable)
	void MouseCrossOK();

	//ChooseSkill的展开
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable)
	void UnfoldChooseSkill();
	void UnfoldChooseSkill_Implementation();

	//ChooseSkill的收缩
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable)
	void ShrinkChooseSkill();
	void ShrinkChooseSkill_Implementation();

	//当前的ChooseSKill展开
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable)
	void CurUnfold();
	void CurUnfold_Implementation();

	//当前的ChooseSkill收缩
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable)
	void CurShrink();
	void CurShrink_Implementation();

	//清除ChooseSkill
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable)
	void ClearChooseSkill();
	void ClearChooseSkill_Implementation();

	//收缩到原点
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable)
	void ShrinkToZero();
	void ShrinkToZero_Implementation();

	//当ChooseSkillPanel排序时每个普通ChooseSkill走的函数
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable)
	void SortChooseSkill(float BaseSizeY,float Number, bool IsUp);
	void SortChooseSkill_Implementation(float BaseSizeY, float Number, bool IsUp);

	//若ChooseSkill为Current（Choose）则走该函数
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable)
	void CurSortChooseSkill();
	void CurSortChooseSkill_Implementation();


};
