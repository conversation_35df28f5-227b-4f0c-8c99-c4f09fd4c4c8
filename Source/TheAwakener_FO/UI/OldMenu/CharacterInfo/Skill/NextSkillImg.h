// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/UI/Base/BasePanel.h"
#include "Components/Image.h"
#include "UMG.h"
#include "NextSkillImg.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UNextSkillImg : public UBasePanel
{
	GENERATED_BODY()
	
public:

	/*
	* NextSkillImg ��ƫ����
	* ��ƫ����������RenderTranslation����������ͼ�����ڵ����ĵ��ƫ��
	*/
	UPROPERTY(BlueprintReadWrite)
	FVector2D MoveTranslation =FVector2D(0, 0);

	//�ƶ�����
	UPROPERTY(BlueprintReadWrite)
	UCurveFloat* Curve;
	
	//������Image
	UPROPERTY(BlueprintReadWrite)
	UImage* Img = nullptr;

	//��ǰ��ʱ��
	UPROPERTY(BlueprintReadWrite)
	float CurTime;

	//�Ƿ���չ���˵�
	UPROPERTY(BlueprintReadWrite)
	bool bIsUnfold = true;

public:

	//ͼƬ��ʼ��
	UFUNCTION(BlueprintCallable)
	void ImgInitial(UTexture2D* I_Img);

	
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable)
	void MoveAnim(float DeltaTime);
	void MoveAnim_Implementation(float DeltaTime);

	//������չ��
	void UnfoldCenter(float DeltaTime, float SpendTime);

	//չ��ʱ��С�仯
	void UnfoldSize(float DeltaTime, float SpendTime);

	//����������
	void ShrinkCenter(float DeltaTime, float SpendTime);

	//����ʱ��С�仯
	void ShrinkSize(float DeltaTime, float SpendTime);
};
