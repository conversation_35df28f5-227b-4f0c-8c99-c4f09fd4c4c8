// Fill out your copyright notice in the Description page of Project Settings.


#include "ChooseSkill.h"
#include "Blueprint/WidgetLayoutLibrary.h"

void UChooseSkill::Initial_Implementation(AActor* SoundPlayer)
{
	this->MediaSoundPlayer = SoundPlayer;

	//
	UWidgetLayoutLibrary::SlotAsCanvasSlot(this)->SetAnchors(FAnchors(0, 0, 1, 1));
	UWidgetLayoutLibrary::SlotAsCanvasSlot(this)->SetAlignment(FVector2D(.5, .5));
	UWidgetLayoutLibrary::SlotAsCanvasSlot(this)->SetOffsets(0);
}


bool UChooseSkill::IsHasAction()
{
	return this->Action != nullptr;
}

FString UChooseSkill::GetMediaSourcePath()
{
	FString res = "";
	
	if (this->Action == nullptr || GetBattleClass()->Id == "")return res;
	
	//看在UIJson找不找的到action的Id
	if (this->GetAwGameInstance()->DataManager->GetUISkillInfoById(GetBattleClass()->Id).UIActions.Find(this->Action->Id) == NULL)return res;

	return this->GetAwGameInstance()->DataManager->GetUISkillInfoById(GetBattleClass()->Id).UIActions.Find(this->Action->Id)->AnimPath;
}

FActionInfo UChooseSkill::GetChooseSkillAction()
{
	if (Action == nullptr)return FActionInfo();
	return *this->Action;
}

UMediaSource* UChooseSkill::GetMediaSource()
{
	UMediaSource* res = nullptr;
	if (this->Action == nullptr || GetBattleClass()->Id == "")return res;
	if (this->GetAwGameInstance()->DataManager->GetUISkillInfoById(GetBattleClass()->Id).UIActions.Find(this->Action->Id) == NULL)return res;

	FString Path = "";
	Path = this->GetAwGameInstance()->DataManager->GetUISkillInfoById(GetBattleClass()->Id).UIActions.Find(this->Action->Id)->AnimPath;
	res = LoadObject<UMediaSource>(NULL, *Path);
	return res;
}

FText UChooseSkill::GetActionUIName()
{
	if (this->Action == nullptr)
	{
		return FText::FromString(this->GetAwGameInstance()->DataManager->
			GetUISkillInfoById(GetBattleClass()->Id).UIActions.Find("NoSkill")->UIName);
	}
	return FText::FromString(this->GetAwGameInstance()->DataManager->
		GetUISkillInfoById(GetBattleClass()->Id).UIActions.Find(this->Action->Id)->UIName);
}

UTexture2D* UChooseSkill::GetActionUIIcon()
{
	UTexture2D* Res = nullptr;
	if (this->GetBattleClass() == nullptr)return Res;
	FString Path = "";
	if (this->Action == nullptr)
	{
		Path = this->GetAwGameInstance()->DataManager->
			GetUISkillInfoById(GetBattleClass()->Id).UIActions.Find("NoSkill")->ImagePath;
	}
	else
	{
		Path = this->GetAwGameInstance()->DataManager->
			GetUISkillInfoById(GetBattleClass()->Id).UIActions.Find(this->Action->Id)->ImagePath;
	}
	const FSoftObjectPath AssetPath(Path);
	Res = Cast<UTexture2D>(AssetPath.TryLoad());
	return Res;
}

void UChooseSkill::MouseCrossOK()
{
	//看委托是否有绑定
	if(D_MouseOK.IsBound())
	{
		D_MouseOK.Execute(this);
	}
}


void UChooseSkill::UnfoldChooseSkill_Implementation()
{
	UMediaSource* MediaSource = GetMediaSource();
	if (MediaSource != nullptr)this->Media->OpenSource(MediaSource);
	this->Media->Pause();
}

void UChooseSkill::ShrinkChooseSkill_Implementation()
{

}

void UChooseSkill::CurUnfold_Implementation()
{
	UMediaSource* MediaSource = GetMediaSource();
	if (MediaSource != nullptr)this->Media->OpenSource(MediaSource);
	this->Media->Pause();
}

void UChooseSkill::CurShrink_Implementation()
{

}

void UChooseSkill::ClearChooseSkill_Implementation()
{

}

void UChooseSkill::ShrinkToZero_Implementation()
{

}

void UChooseSkill::SortChooseSkill_Implementation(float BaseSizeY, float Number, bool IsUp)
{
	

}
void UChooseSkill::CurSortChooseSkill_Implementation()
{
	
}