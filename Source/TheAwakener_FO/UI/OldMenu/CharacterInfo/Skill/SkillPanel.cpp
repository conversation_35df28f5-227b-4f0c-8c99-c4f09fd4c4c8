// Fill out your copyright notice in the Description page of Project Settings.


#include "SkillPanel.h"
#include "TheAwakener_FO/GameFramework/Base/AwGameInstance.h"
#include "TheAwakener_FO/GameFramework/Manager/AwDataManager.h"


void USkillPanel::Initial_Implementation()
{
	UAwGameInstance* Instance = Cast<UAwGameInstance>(GetWorld()->GetGameInstance());
	USkillUI* CreateSkillUI = nullptr;
	for (int i = 0; i < 24; i++)
	{
		CreateSkillUI = this->CreateSkillUI();

		if(CreateSkillUI)
			UE_LOG(LogTemp, Warning, TEXT("Can't Create The i WBP_SkillUI!!!!!"));
	}
		
	this->CheckHaveSaveData();
	
}

USkillUI* USkillPanel::GetSkillUIByIndex(int Index)
{
	return this->SkillUIArray[Index];
}

USkillUI* USkillPanel::CreateSkillUI()
{
	TSubclassOf<USkillUI> USkillUIClass = LoadClass<USkillUI>(this,
		TEXT("/Game/Core/WBP/NormalWidget/Menu/CharacterInfo/Skill/WBP_SkillUI.WBP_SkillUI_C"));
	USkillUI* SkillUI;
	if (USkillUIClass != nullptr)
	{
		SkillUI = CreateWidget<USkillUI>(this, USkillUIClass);
		if (SkillUI != nullptr) 
			UE_LOG(LogTemp, Warning, TEXT("Can't Create WBP_SkillUI!!!!!"));
		this->SkillUIArray.Emplace(SkillUI);
		return SkillUI;
	}
	UE_LOG(LogTemp, Warning, TEXT("Can't find WBP_SkillUI"));
	return nullptr;
}

FActionInfo USkillPanel::GetCurSkillUIAction()
{
	if (this->CurSkillUI == nullptr)return FActionInfo();

	if (this->CurSkillUI->CurActionInfo == nullptr)return FActionInfo();

	return *this->CurSkillUI->CurActionInfo;
}

USkillUI* USkillPanel::CrossMoveKeySkillUI(int Move)
{
	if (this->CurSkillUI == nullptr)return this->SkillUIArray[0];
	int CurIndex = this->SkillUIArray.Find(this->CurSkillUI);
	switch (Move)
	{
	case 0:

		if (CurIndex % 3 == 0)
		{
			if (CurIndex / 3 < 4)
			{
				return this->CurSkillUI;
			}
			return this->GetSkillUIByIndex(CurIndex - 10);
		}
		return this->GetSkillUIByIndex(CurIndex - 1);
		break;
	case 1:

		if (CurIndex % 3 == 2)
		{
			if (CurIndex / 3 > 3)
			{
				return this->CurSkillUI;
			}
			return this->GetSkillUIByIndex(CurIndex + 10);
		}
		return this->GetSkillUIByIndex(CurIndex + 1);
		break;
	case 2:

		if (CurIndex / 3 == 0 || CurIndex / 3 == 4)
		{
			return this->CurSkillUI;
		}
		return this->GetSkillUIByIndex(CurIndex - 3);
		break;
	case 3:

		if (CurIndex / 3 == 3 || CurIndex / 3 == 7)
		{
			return this->CurSkillUI;
		}
		return this->GetSkillUIByIndex(CurIndex + 3);
		break;
	default:
		return nullptr;
	}
	return nullptr;
}

TArray<int> USkillPanel::GetCanCancelSkill(int Index)
{
	TArray<int> res;

	//防止数组越界做的保护
	if (this->SkillUIArray.Num() - 1 < Index || Index < 0)
	{
		UE_LOG(LogTemp, Warning, TEXT("SkillUIArray不存在这个Index"));
		return res;
	}

	if ( this->SkillUIArray[Index]->GetSkillUIState() == ESkillUIState::Void
		|| this->SkillUIArray[Index]->GetSkillUIProperty() == ESkillUIProperty::Lock)return res;

	if (this->SkillUIArray[Index]->CurActionInfo != nullptr)
	{
		int CheckGroup;
		CheckGroup = Index / 3 + 1;
		
		//如果是最后一列退出
		if (CheckGroup == 4 || CheckGroup == 8)return res;

		//查询下一组是否有可以Cancel的SkillUI
		for (int j = CheckGroup * 3; j < (CheckGroup + 1) * 3; j++)
		{
			//防止数组越界
			if (j < 0 || j >= this->SkillUIArray.Num())break;

			//由于存在UI存在CurAction为空的时候，所以要做跳过
			if (this->SkillUIArray[j]->CurActionInfo == nullptr)continue;
			
			for (FActionInfo* action : this->GetWidgetPawn()->GetCancellableActions({ this->SkillUIArray[Index]->CurActionInfo }))
			{
				if (this->SkillUIArray[j]->CurActionInfo == action && this->SkillUIArray[Index]->GetSkillUIProperty() != ESkillUIProperty::Error
					|| this->SkillUIArray[j]->GetSkillUIProperty() == ESkillUIProperty::Error && this->SkillUIArray[Index]->GetSkillUIProperty() == ESkillUIProperty::Error)
				{
					res.Emplace(j);
					break;
				}
			}
			
		}
	}
	return res;
}

void USkillPanel::CreateSkillLine_Implementation()
{

}

void USkillPanel::DoDefaultData()
{
	//检查是否SkillUIArray为空，若为空，程序将会奔溃，所以做一个保护
	if (this->SkillUIArray.Num() == 0)
	{
		UE_LOG(LogTemp, Warning, TEXT("SkillUIArray为空，去查看是否成功生成"));
		return;
	}
	
	if (GetBattleClass()->Id == "")return;

	//将每一个SkillUI赋值
	for (int i = 0; i < this->SkillUIArray.Num(); i++)
	{
		//有些SkillUI的默认为空，则需要做一个保护
		if (this->GetAwGameInstance()->DataManager->GetUISkillInfoById(GetBattleClass()->Id).DefaultActions[i].ActionInfo.Num() != 0)
		{
			for (int j = 0; j < this->GetAwGameInstance()->DataManager->GetUISkillInfoById(GetBattleClass()->Id).DefaultActions[i].ActionInfo.Num(); j++)
			{
				FActionInfo* AddInfo = this->GetWidgetPawn()->GetActionById(this->GetAwGameInstance()->DataManager->GetUISkillInfoById(GetBattleClass()->Id).DefaultActions[i].ActionInfo[j]);

				if (AddInfo)
					this->SkillUIArray[i]->CanChangeActionArray.Add(AddInfo);
			}
		}
		this->SkillUIArray[i]->Initial(this->GetAwGameInstance()->DataManager->GetUISkillInfoById(GetBattleClass()->Id).DefaultSkillUIState[i]);

		//绑定SkillUI上的委托
		this->SkillUIArray[i]->D_MouseOK.BindUFunction(this, FName("ToSetCurSkill"));
		this->SkillUIArray[i]->D_MouseHovered.BindUFunction(this, FName("ToRefreshAllSkillUI"));
	}
}

void USkillPanel::CheckHaveSaveData()
{
	this->DoDefaultData();
}

TArray<USkillUI*> USkillPanel::GetLastGroup(USkillUI* SkillUI)
{
	TArray<USkillUI*> lastGroup;
	//判断选定的UI不是第一组或者是空中的第一组
	if (this->SkillUIArray.Find(SkillUI) / 3 != 0 && this->SkillUIArray.Find(SkillUI) / 3 != 4)
	{
		for (int i = 0; i < 3; i++)
		{
			lastGroup.Add(this->SkillUIArray[(this->SkillUIArray.Find(SkillUI) / 3 - 1) * 3 + i]);
		}
		return lastGroup;
	}
	return lastGroup;
}

void USkillPanel::SetLastGroupImg(TArray<UImage*> Images)
{
	UTexture2D* Texture = nullptr;
	FString Path = "";
	FString PathName = "";
	if (GetLastGroup(this->CurSkillUI).Num() == 0 && this->IsFirstGroup())
	{
		for (UImage* img : Images)img->SetVisibility(ESlateVisibility::Hidden);
		return;
	}
	
	//预设好就六张图片，每两张图片组成一个图，也就是一个主图加上一个以主图(Icon)右下角为锚点的副图(PassiveIcon）
	for (int i = 0; i < 6; i++)
	{
		//i % 2 == 1是判断是否是主图还是副图
		i % 2 == 1 ? PathName = GetLastGroup(this->CurSkillUI)[i / 2]->UIIconName.Value : PathName = GetLastGroup(this->CurSkillUI)[i / 2]->UIIconName.Key;
		//若地址为空则要隐藏，并跳出进入下个循环
		if (PathName == "")
		{
			Images[i]->SetVisibility(ESlateVisibility::Hidden);
			continue;
		}
		Images[i]->SetVisibility(ESlateVisibility::Visible);
		Path = GetAwGameInstance()->DataManager->GetUISkillInfoById(GetBattleClass()->Id).UIActions.Find(PathName)->ImagePath;
		const FSoftObjectPath AssetPath(Path);
		Texture = Cast<UTexture2D>(AssetPath.TryLoad());
		Images[i]->SetBrushFromTexture(Texture);
	}
}

bool USkillPanel::IsGround()
{
	return this->SkillUIArray.Find(this->CurSkillUI) / 3 < 4;
}

bool USkillPanel::IsFirstGroup()
{
	return (this->SkillUIArray.Find(this->CurSkillUI) / 3 == 0 || this->SkillUIArray.Find(this->CurSkillUI) / 3 == 4);
}

int USkillPanel::GetSkillUIIndex(USkillUI* SkillUI)
{
	return this->SkillUIArray.Find(SkillUI);
}

TArray<int> USkillPanel::GetCurSkillCancelLastGroup(FActionInfo Action)
{
	TArray<int> res;
	int Index = 0;
	if (Action.Id == "")return res;
	for (USkillUI* LastSkill : GetLastGroup(this->CurSkillUI))
	{
		Index++;
		if (LastSkill->CurActionInfo == NULL)continue;
		if (LastSkill->CurActionInfo->CanBeCancelledBy(&Action))res.Add(Index - 1);
	}
	return res;
}

void USkillPanel::RefreshAllSkillUI()
{
	for (USkillUI* SkillUI : this->SkillUIArray)
	{
		if (SkillUI->CurActionInfo != nullptr)SkillUI->SetSkillUIState(ESkillUIState::Null);
		if (SkillUI == this->CurSkillUI)SkillUI->SetSkillUIState(ESkillUIState::Choosing);
		SkillUI->CheckSkillUIState(this->SkillUIArray);
		SkillUI->RefreshSkillUI();
	}
}

void USkillPanel::ToSetCurSkill_Implementation(USkillUI* SkillUI)
{
	this->CurSkillUI = SkillUI;
}

void USkillPanel::ToRefreshAllSkillUI_Implementation()
{
	for (USkillUI* SkillUI : this->SkillUIArray)
	{
		if (SkillUI->CurActionInfo != nullptr)SkillUI->SetSkillUIState(ESkillUIState::Null);
		SkillUI->RefreshState();

	}
}