// Fill out your copyright notice in the Description page of Project Settings.


#include "NextSkillImg.h"
#include "Blueprint/WidgetLayoutLibrary.h"



void UNextSkillImg::ImgInitial(UTexture2D* I_Img)
{
	
	UCanvasPanel* panel = Cast<UCanvasPanel>(GetRootWidget());

	this->Img = NewObject<UImage>(this);
	panel->AddChild(Img);

	this->Img->SetBrushFromTexture(I_Img);

	UWidgetLayoutLibrary::SlotAsCanvasSlot(Img)->SetSize(FVector2D(150, 150));
	UWidgetLayoutLibrary::SlotAsCanvasSlot(Img)->SetAlignment(FVector2D(0.5, 0.5));
	UWidgetLayoutLibrary::SlotAsCanvasSlot(Img)->SetAnchors(FAnchors(0.5, 0.5, 0.5, 0.5));

}

void UNextSkillImg::MoveAnim_Implementation(float DeltaTime)
{
	if(this->bIsUnfold)
	{
		this->UnfoldCenter(DeltaTime, 0.2);
		this->UnfoldSize(DeltaTime, 0.2);
	}
	else
	{
		this->ShrinkCenter(DeltaTime, 0.2);
		this->ShrinkSize(DeltaTime, 0.2);
	}
	this->CurTime += DeltaTime;
}

void UNextSkillImg::UnfoldCenter(float DeltaTime, float SpendTime)
{
	if (this->CurTime + DeltaTime > SpendTime)return;
	if (IsValid(this->Curve))
	{
		float InTime = 0;
		this->CurTime / SpendTime > 1 ? InTime = 0 : InTime = 1 - this->CurTime / SpendTime;
		this->SetRenderTranslation(this->MoveTranslation * this->Curve->GetFloatValue(InTime));
		return;
	}
	this->SetRenderTranslation(FVector2D(0, 0));
}

void UNextSkillImg::UnfoldSize(float DeltaTime, float SpendTime)
{
	if (this->CurTime + DeltaTime > SpendTime)return;
	if (IsValid(this->Curve))
	{
		float InTime = 0;
		this->CurTime / SpendTime > 1 ? InTime = 0 : InTime = this->CurTime / SpendTime;
		this->SetRenderScale(FVector2D(1, 1) * this->Curve->GetFloatValue(InTime));
		return;
	}
	this->SetRenderScale(FVector2D(0, 0));
}

void UNextSkillImg::ShrinkCenter(float DeltaTime, float SpendTime)
{
	if (this->CurTime + DeltaTime > SpendTime)return;
	if (IsValid(this->Curve))
	{
		float InTime = 0;
		this->CurTime / SpendTime > 1 ? InTime = 1 : InTime = this->CurTime / SpendTime;
		this->SetRenderTranslation(this->MoveTranslation * this->Curve->GetFloatValue(InTime));
		return;
	}
	this->SetRenderTranslation(this->MoveTranslation);
}

void UNextSkillImg::ShrinkSize(float DeltaTime, float SpendTime)
{
	if (this->CurTime + DeltaTime > SpendTime)return;
	if (IsValid(this->Curve))
	{
		float InTime = 0;
		this->CurTime / SpendTime > 1 ? InTime = 1 : InTime = 1 - this->CurTime / SpendTime;
		this->SetRenderScale(FVector2D(1, 1) * this->Curve->GetFloatValue(InTime));
		return;
	}
	this->SetRenderScale(FVector2D(0, 0));
}