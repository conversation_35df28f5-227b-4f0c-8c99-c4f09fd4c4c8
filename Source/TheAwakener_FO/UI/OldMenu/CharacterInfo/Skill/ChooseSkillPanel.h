// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Base/Skill_BasePanel.h"
#include "SkillUI.h"
#include "ChooseSkill.h"
#include "MediaSource.h"
#include "SkillUI.h"
#include "ChooseSkillPanel.generated.h"


/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UChooseSkillPanel : public USkill_BasePanel
{
	GENERATED_BODY()
	
private:


public:

	//当前选中的ChooseSkill
	UPROPERTY(BlueprintReadWrite)
	UChooseSkill* CurChooseSkill = nullptr;

	//上一组SkillUI,主要是为了获得其中的Action
	UPROPERTY(BlueprintReadWrite)
	TArray<USkillUI*> LastGroup;

	//在ChoosePanel里显示的所有ChooseSkill（ChooseSkill是Action及其资源的个体，也就是单独的SkillUI）
	UPROPERTY(BlueprintReadWrite)
	TArray<UChooseSkill*> ChooseSkillArray;

	UPROPERTY(BlueprintReadWrite)
	AActor* MediaSoundPlayer;


public:

	/*
	* 创建ChooseSkill
	* @Param CurSkillUI是在上一层级也就是Skill Panel上的选中的SkillUI
	*/
	UFUNCTION(BlueprintCallable)
	void CreateChooseSkill(USkillUI* CurSkillUI);

	//返回可以打断当前技能的技能的图片资源
	UFUNCTION(BlueprintPure)
	TArray<UTexture2D*> GetCanCancelImgs();

	//获得可以打断上一组的Action（都是学会的Action）
	TArray<FActionInfo*> GetCanCreateChooseByActionInfo();

	//获得当前Image的图片
	UFUNCTION(BlueprintPure)
	UTexture2D* GetCurActionImage();

	//后续打断技能UI的移动
	UFUNCTION(BlueprintCallable)
	void SetNextSkillImgMoveAnim(UUniformGridPanel* Panel, float DeltaTime);

	//后续打断技能UI的回到终点
	UFUNCTION(BlueprintCallable)
	void SetNextSkillImgsCenter(UUniformGridPanel* NextSkillPanel);
	
	//排序ChooseSkill
	UFUNCTION(BlueprintCallable)
	void SortChooseSkill(); 

	//刷新SkillUI
	UFUNCTION(BlueprintCallable)
	void ToRefreshSkillUI(USkillUI* CurSkillUI);

	//ChooseSkill上绑定的MouseOK事件
	UFUNCTION(BlueprintNativeEvent)
	void ChooseSkillMouseOK(UChooseSkill* ChooseSkill);
	void ChooseSkillMouseOK_Implementation(UChooseSkill* ChooseSkill);
};
