// Fill out your copyright notice in the Description page of Project Settings.


#include "SkillLine.h"
#include "Blueprint/WidgetLayoutLibrary.h"

void USkillLine::SetLineSize_Implementation(int SelfIndex, FVector2D Size)
{

}

void USkillLine::ShowLine_Implementation(int SelfIndex, const TArray<int32>& Target)
{

}

void USkillLine::AddImages(UImage* Image)
{
	if (this->Images.Find(Image) == -1) this->Images.Emplace(Image);
}