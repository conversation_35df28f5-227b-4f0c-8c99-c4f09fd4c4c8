// Fill out your copyright notice in the Description page of Project Settings.


#include "SkillUI.h"
#include "TheAwakener_FO/GameFramework/Manager/AwDataManager.h"
#include "UObject/SoftObjectPath.h"
#include "Kismet/KismetSystemLibrary.h"

void USkillUI::Initial_Implementation(UPARAM(ref)FString& UIProperty)
{
	if (this->CanChangeActionArray.Num() == 1)
	{
		this->CurActionInfo = this->CanChangeActionArray[0];
		this->UIIconName.Key = this->CanChangeActionArray[0]->Id;
		this->SetSkillUIState(ESkillUIState::Null);
	}
	else
		this->SetSkillUIState(ESkillUIState::Void);
	ESkillUIProperty Key = static_cast<ESkillUIProperty>(StaticEnum<ESkillUIProperty>()->GetIndexByName(FName(UIProperty)));
	this->SkillUIState.Key = Key;
}

ESkillUIProperty USkillUI::GetSkillUIProperty()
{
	return this->SkillUIState.Key;
}

ESkillUIState USkillUI::GetSkillUIState()
{
	return this->SkillUIState.Value;
}

void USkillUI::SetSkillUIState(ESkillUIState State)
{
	this->SkillUIState.Value = State;
}

void USkillUI::SetSkillUIProperty(ESkillUIProperty Property)
{
	if (this->SkillUIState.Key == ESkillUIProperty::Definite)return;
	this->SkillUIState.Key = Property;
}

void USkillUI::RefreshUIIcon()
{
	if (GetBattleClass()== nullptr)return;
	if (this->CurActionInfo == nullptr) return;
	this->UIIconName.Key = this->CurActionInfo->Id;
}

UTexture2D* USkillUI::GetUIIcon()
{
	UTexture2D* Texture =nullptr;
	FString Path = "";
	if (GetBattleClass() == nullptr)return Texture;
	if (this->UIIconName.Key == "" || this->SkillUIState.Value == ESkillUIState::Void 
		|| this->GetSkillUIProperty() == ESkillUIProperty::Lock || this->CurActionInfo == nullptr)
	{
		Path = GetAwGameInstance()->DataManager->GetUISkillInfoById(GetBattleClass()->Id).UIActions.Find("NoSkill")->ImagePath;
		const FSoftObjectPath AssetPath(Path);
		Texture = Cast<UTexture2D>(AssetPath.TryLoad());
		return Texture;
	}
	Path = GetAwGameInstance()->DataManager->GetUISkillInfoById(GetBattleClass()->Id).UIActions.Find(this->UIIconName.Key)->ImagePath;
	const FSoftObjectPath AssetPath(Path);
	Texture = Cast<UTexture2D>(AssetPath.TryLoad());
	return Texture;
}

UTexture2D* USkillUI::GetPassiveIcon()
{
	UTexture2D* Texture = nullptr;
	if (GetBattleClass() == nullptr)return Texture;

	FString Path = "";
	Path = GetAwGameInstance()->DataManager->GetUISkillInfoById(GetBattleClass()->Id).UIActions.Find(this->UIIconName.Value)->ImagePath;
	//const FSoftObjectPath AssetPath(Path);
	const FSoftObjectPath AssetPath(Path);
	Texture = Cast<UTexture2D>(AssetPath.TryLoad());

	return Texture;
}

bool USkillUI::IsHasCurActionInfo()
{
	return this->CurActionInfo != nullptr;
}

int USkillUI::GetCanChangeArrayNum()
{
	return this->CanChangeActionArray.Num();
}

FActionInfo USkillUI::GetCurActionInfo()
{
	FActionInfo res;
	res = this->CurActionInfo == NULL ? res : *this->CurActionInfo;
	return res;
}

void USkillUI::RefreshSkillUI_Implementation()
{
	
}

void USkillUI::RefreshState_Implementation()
{

}

void USkillUI::CheckSkillUIState(TArray<USkillUI*> SkillUIArray)
{
	if (this->CurActionInfo == nullptr)return;

	int Index = SkillUIArray.Find(this);
	int Group = Index / 3;

	if (Group - 1 < 0 || Group == 4)return;
	for (int j = (Group - 1) * 3; j < Group * 3; j++)
	{
		if (SkillUIArray[j]->GetSkillUIProperty() == ESkillUIProperty::Error)continue;
		if (SkillUIArray[j]->CurActionInfo == nullptr)continue;
		for (FActionInfo* action : this->GetWidgetPawn()->GetCancellableActions({ SkillUIArray[j]->CurActionInfo }))
		{
			if (this->CurActionInfo == action)
			{
				this->SetSkillUIProperty(ESkillUIProperty::Default);
				return;
			}
		}
	}
	this->SetSkillUIProperty(ESkillUIProperty::Error);
}

 void USkillUI::ToSetSkillPanelCurSkillUI()
{
	if (D_MouseOK.IsBound())
	{
		D_MouseOK.Execute(this);
	}
}

 void USkillUI::MouseHovered()
 {
	 if (D_MouseHovered.IsBound())
	 {
		 D_MouseHovered.Execute();
	 }
 }