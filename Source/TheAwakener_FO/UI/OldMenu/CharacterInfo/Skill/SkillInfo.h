#pragma once


#include "CoreMinimal.h"
#include "TheAwakener_FO/FunctionLibrary/DataFuncLib.h"
#include "SkillInfo.generated.h"

USTRUCT(BlueprintType)
struct FDefaultActions
{
	GENERATED_BODY()

public:

	UPROPERTY(BlueprintReadWrite)
	TArray<FString> ActionInfo;

};

USTRUCT(BlueprintType)
struct FUIActionInfo
{
	GENERATED_BODY()

public:

	//动画地址
	UPROPERTY(BlueprintReadWrite)
	FString AnimPath = "";

	//图片地址
	UPROPERTY(BlueprintReadWrite)
	FString ImagePath = "";

	//Action在UI上显示的名字
	UPROPERTY(BlueprintReadWrite)
	FString UIName = "";

	//对于该Action的细节描述
	UPROPERTY(BlueprintReadWrite)
	FString Describe = "";
};


USTRUCT(BlueprintType)
struct FSkillInfo
{
	GENERATED_BODY()

public:

	UPROPERTY(BlueprintReadWrite)
	FString Id = "";

	//UI状态，根据Index获得相应的UI状态
	TArray<FString> DefaultSkillUIState;

	//预设好的Action
	TArray<FDefaultActions> DefaultActions;

	//Actions的信息
	TMap<FString, FUIActionInfo> UIActions;
	

public:

	static FSkillInfo FromJson(TSharedPtr<FJsonObject> JsonObj)
	{
		//Id
		FSkillInfo Model;
		Model.Id = UDataFuncLib::AwGetStringField(JsonObj, "Id");

		TSharedPtr<FJsonObject> DefaultSkillUIStateObj;
		//DefaultSkillUIState的数据解析
		if (JsonObj->HasField("DefaultSkillUIProperty"))
			DefaultSkillUIStateObj = JsonObj->GetObjectField("DefaultSkillUIProperty");

		if (DefaultSkillUIStateObj != NULL)
		{
			//由于UI只排布24个所以只读到23（从0开始读）
			uint8 SkillUIIndex = 0;
			while (SkillUIIndex < 24)
			{
				DefaultSkillUIStateObj->Values.Contains(FString::FromInt(SkillUIIndex)) ?
					Model.DefaultSkillUIState.Add(DefaultSkillUIStateObj->Values.Find(FString::FromInt(SkillUIIndex))->Get()->AsString())
					: Model.DefaultSkillUIState.Add("Default");

				SkillUIIndex++;
			}
		}
		
		//DefaultAction的数据解析
		TSharedPtr<FJsonObject> DefaultActionsObj;
		if (JsonObj->HasField("DefaultActions"))
			DefaultActionsObj = JsonObj->GetObjectField("DefaultActions");
		
		if(DefaultActionsObj.IsValid())
		{
			//由于UI只排布24个所以只读到23（从0开始读）
			uint8 ActionIndex = 0;
			while (ActionIndex < 24)
			{
				if (DefaultActionsObj->Values.Contains(FString::FromInt(ActionIndex)))
				{
					FDefaultActions Result;
					TArray<TSharedPtr<FJsonValue>> JsonValues = DefaultActionsObj->Values[FString::FromInt(ActionIndex)]->AsArray();
					for (TSharedPtr<FJsonValue> JsonValue : JsonValues)
					{
						Result.ActionInfo.Add(JsonValue->AsString());
					}
					Model.DefaultActions.Add(Result);
				}
				else
				{
					FDefaultActions Result;
					Model.DefaultActions.Add(Result);
				}
				ActionIndex++;
			}

		}
		
		//ActionIcon的数据解析
		TSharedPtr<FJsonObject> UIActionObj = JsonObj->GetObjectField("UIAction");

		if (UIActionObj.IsValid())
		{
			//由于Icon的数据不定，所以根据Value的数量来循环
			uint8 IconIndex = 0;
			TArray<FString> IconKeys;
			UIActionObj->Values.GetKeys(IconKeys);
			while (IconIndex < UIActionObj->Values.Num())
			{
				if (UIActionObj->Values.Contains(IconKeys[IconIndex]))
				{
					FUIActionInfo UIAction;
					UIAction.AnimPath = UIActionObj->Values[IconKeys[IconIndex]]->AsObject()->Values["AnimPath"]->AsString();
					UIAction.ImagePath = UIActionObj->Values[IconKeys[IconIndex]]->AsObject()->Values["ImagePath"]->AsString();
					UIAction.UIName = UIActionObj->Values[IconKeys[IconIndex]]->AsObject()->Values["UIName"]->AsString();
					UIAction.Describe = UIActionObj->Values[IconKeys[IconIndex]]->AsObject()->Values["Describe"]->AsString();
					Model.UIActions.Add(IconKeys[IconIndex], UIAction);
				}
				IconIndex++;
			}
		}
		
		return Model;
	}
};