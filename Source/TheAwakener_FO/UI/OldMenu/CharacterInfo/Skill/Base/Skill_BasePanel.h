#pragma once


#include "CoreMinimal.h"
#include "TheAwakener_FO/UI/Base/BasePanel.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "Skill_BasePanel.generated.h"

UCLASS()
class THEAWAKENER_FO_API USkill_BasePanel : public UBasePanel
{
	GENERATED_BODY()

public:

	FBattleClassModel BattleClass;

public:
	AAwCharacter* GetWidgetPawn()
	{
		if (this->WidgetPawn == nullptr)
		{
			this->WidgetPawn = Cast<AAwCharacter>(UGameplayFuncLib::GetAwGameState()->GetMyCharacter());
			this->BattleClass = FBattleClassModel();
			this->BattleClass.Id = "Warrior";
			return WidgetPawn;
		}
		return WidgetPawn;
	}

	FBattleClassModel* GetBattleClass()
	{
		this->GetWidgetPawn();
		return &this->BattleClass;
	}

};
