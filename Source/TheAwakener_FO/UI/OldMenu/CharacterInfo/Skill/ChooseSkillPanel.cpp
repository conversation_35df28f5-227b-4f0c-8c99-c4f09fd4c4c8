// Fill out your copyright notice in the Description page of Project Settings.


#include "ChooseSkillPanel.h"
#include "TheAwakener_FO/GameFramework/Manager/AwDataManager.h"
#include "NextSkillImg.h"
#include "Blueprint/WidgetLayoutLibrary.h"

void UChooseSkillPanel::CreateChooseSkill(USkillUI* CurSkillUI)
{
	//获得CurSkillUI下的Action
	FActionInfo CurAction = CurSkillUI->CurActionInfo == nullptr ? FActionInfo() : *CurSkillUI->CurActionInfo;

	//获得蓝图类
	TSubclassOf<UChooseSkill> UChooseSkillClass = LoadClass<UChooseSkill>(this,
		TEXT("/Game/Core/WBP/NormalWidget/Menu/CharacterInfo/Skill/WBP_ChooseSkill.WBP_ChooseSkill_C"));
	if (UChooseSkillClass == nullptr)return;

	//获得根panel，之后创建的ChooseSkill会以它为根
	UCanvasPanel* panel = Cast<UCanvasPanel>(GetRootWidget());

	//当CurAcr为空时或者该Skill UI的状态为Error时
	if (CurAction.Id == "" || CurSkillUI->GetSkillUIProperty() == ESkillUIProperty::Error)
	{
		//创建ChooseSkill
		UChooseSkill* CurChoose = CreateWidget<UChooseSkill>(this, UChooseSkillClass);
		panel->AddChild(CurChoose);

		//为创建的CurChoose做初始化操作，并将挂在在Character上的Actor（MediaSoundPlayer）的指针传入
		CurChoose->Initial(MediaSoundPlayer);
		this->ChooseSkillArray.Add(CurChoose);
		UWidgetLayoutLibrary::SlotAsCanvasSlot(CurChoose)->SetZOrder(2);
		this->CurChooseSkill = CurChoose;
	}

	//若在Json表内已经限定过有哪些Action，就只能显示这些动作
	if (CurSkillUI->CanChangeActionArray.Num() != 0)
	{
		for (FActionInfo* action : CurSkillUI->CanChangeActionArray)
		{
			//看UI中有没有这个action
			if (!GetAwGameInstance()->DataManager->GetUISkillInfoById(GetBattleClass()->Id).UIActions.Contains(action->Id))continue;

			UChooseSkill* ChooseSkill = CreateWidget<UChooseSkill>(this, UChooseSkillClass);
			if (ChooseSkill == nullptr)return;
			ChooseSkill->Action = action;

			panel->AddChild(ChooseSkill);
			ChooseSkill->Initial(MediaSoundPlayer);

			//绑定委托
			ChooseSkill->D_MouseOK.BindUFunction(this, FName("ChooseSkillMouseOK"));

			this->ChooseSkillArray.Add(ChooseSkill);

			action->Id == CurAction.Id ? UWidgetLayoutLibrary::SlotAsCanvasSlot(ChooseSkill)->SetZOrder(2) 
				: UWidgetLayoutLibrary::SlotAsCanvasSlot(ChooseSkill)->SetZOrder(1);

			//当前的SkillUI中有CurSkill，则他的将其与之对应的ChooseSkill赋值给CurChooseSkill
			action->Id == CurAction.Id ? this->CurChooseSkill = ChooseSkill : ChooseSkill->SetVisibility(ESlateVisibility::Hidden);
		}
		return;
		
	}
	
	//根据可以打断上一列的Action来创建ChooseSkill
	for (FActionInfo* action : GetCanCreateChooseByActionInfo())
	{
		//看UI中有没有这个action
		if (!GetAwGameInstance()->DataManager->GetUISkillInfoById(GetBattleClass()->Id).UIActions.Contains(action->Id))continue;

		UChooseSkill* ChooseSkill = CreateWidget<UChooseSkill>(this, UChooseSkillClass);
		if (ChooseSkill == nullptr)return;
		ChooseSkill->Action = action;

		panel->AddChild(ChooseSkill);
		ChooseSkill->Initial(MediaSoundPlayer);

		//绑定MouseOK的委托
		ChooseSkill->D_MouseOK.BindUFunction(this, FName("ChooseSkillMouseOK"));

		this->ChooseSkillArray.Add(ChooseSkill);

		action->Id == CurAction.Id ? UWidgetLayoutLibrary::SlotAsCanvasSlot(ChooseSkill)->SetZOrder(2) 
			: UWidgetLayoutLibrary::SlotAsCanvasSlot(ChooseSkill)->SetZOrder(1);
		
		//当前的SkillUI中有CurSkill，则他的将其与之对应的ChooseSkill赋值给CurChooseSkill
		action->Id == CurAction.Id ? this->CurChooseSkill = ChooseSkill : ChooseSkill->SetVisibility(ESlateVisibility::Hidden);
		
	}

}

TArray<UTexture2D*> UChooseSkillPanel::GetCanCancelImgs()
{
	TArray<UTexture2D*> res;
	if (this->CurChooseSkill == nullptr)return res;
	if (this->CurChooseSkill->Action == nullptr)return res;
	TArray<FActionInfo*> Array;
	Array.Add(this->CurChooseSkill->Action);
	
	FString Path = "";
	for (FActionInfo* action : this->GetWidgetPawn()->GetCancellableActions(Array))
	{
		if (action == nullptr)return res;
		if (GetBattleClass()->Id == "")return res;
		if(GetAwGameInstance()->DataManager->GetUISkillInfoById(GetBattleClass()->Id).UIActions.Find(action->Id)==NULL)continue;
		Path = GetAwGameInstance()->DataManager->GetUISkillInfoById(GetBattleClass()->Id).UIActions.Find(action->Id)->ImagePath;
		const FSoftObjectPath AssetPath(Path);
		res.Add(Cast<UTexture2D>(AssetPath.TryLoad()));
	}
	return res;
}


TArray<FActionInfo*> UChooseSkillPanel::GetCanCreateChooseByActionInfo()
{
	TArray<FActionInfo*> ActionArray;
	for (USkillUI* SkillUI : this->LastGroup)
	{
		if (SkillUI->CurActionInfo == nullptr)continue;
		ActionArray.Add(SkillUI->CurActionInfo);
	}
	if (ActionArray.Num() == 0)return ActionArray;
	return ActionArray = this->GetWidgetPawn()->GetCancellableActions(ActionArray);
}

UTexture2D* UChooseSkillPanel::GetCurActionImage()
{
	UTexture2D* res = nullptr;
	FString Path = "";
	if (this->CurChooseSkill == nullptr)return res;
	
	/*
	*因为有Action为空的情况。
	*在Action为空时，显示的时无技能的Icon
	*/
	if (this->CurChooseSkill->Action == nullptr || GetBattleClass() == nullptr)
	{
		Path = this->GetAwGameInstance()->DataManager->GetUISkillInfoById("").UIActions.Find("NoSkill")->ImagePath;
		const FSoftObjectPath AssetPath(Path);
		res = Cast<UTexture2D>(AssetPath.TryLoad());
		return res;
	}
	
	/*
	* 主要是防止找不到UIJson中对应的资源做的保护
	*/
	if (this->GetAwGameInstance()->DataManager->GetUISkillInfoById(GetBattleClass()->Id).UIActions.Find(this->CurChooseSkill->Action->Id) == NULL)
	{
		Path = this->GetAwGameInstance()->DataManager->GetUISkillInfoById("").UIActions.Find("NoSkill")->ImagePath;
		const FSoftObjectPath AssetPath(Path);
		res = Cast<UTexture2D>(AssetPath.TryLoad());
		return res;
	}
	
	Path = this->GetAwGameInstance()->DataManager->GetUISkillInfoById(GetBattleClass()->Id).UIActions.Find(this->CurChooseSkill->Action->Id)->ImagePath;
	const FSoftObjectPath AssetPath(Path);
	res = Cast<UTexture2D>(AssetPath.TryLoad());
	return res;
}

void UChooseSkillPanel::SetNextSkillImgMoveAnim(UUniformGridPanel* Panel,float DeltaTime)
{
	//根据Panel获得所有Children
	for (UWidget* widget : Panel->GetAllChildren())
	{
		if (IsValid(Cast<UNextSkillImg>(widget)))continue;
		UNextSkillImg* SkillImg = nullptr;
		SkillImg = Cast<UNextSkillImg>(widget);
		SkillImg->MoveAnim_Implementation(DeltaTime);
	}
}

void UChooseSkillPanel::SetNextSkillImgsCenter(UUniformGridPanel* NextSkillPanel)
{

	if (NextSkillPanel->GetAllChildren().Num() == 0)return;

	/*
	(NextSkillPanel->GetAllChildren().Num() - 1) 是总数 X （0~n ）个
	(NextSkillPanel->GetAllChildren().Num() - 1) / 3 + 1 是行数 Y （1~n）个
	((NextSkillPanel->GetAllChildren().Num() - 1) / 3 + 1) * 2 - 1 是 总行数（每两个UI的中间也算一行）
	(((NextSkillPanel->GetAllChildren().Num() - 1) / 3 + 1) * 2 - 1) / 2 + 1中心行数
	*/
	int CenterRow = (((NextSkillPanel->GetAllChildren().Num() - 1) / 3 + 1) * 2 - 1) / 2 + 1;

	//GetAllChildren().Num() >= 0 判断是否只有一行, 若不是只有一行则都为三列
	int CenterColumn = 0;
	NextSkillPanel->GetAllChildren().Num() >= 3 ? CenterColumn = 3 : CenterColumn = ((NextSkillPanel->GetAllChildren().Num() - 1) % 3 * 2 + 1);

	for (int i = 0; i < NextSkillPanel->GetAllChildren().Num(); i++)
	{
		UNextSkillImg* NextSkill = nullptr;

		if (!Cast<UNextSkillImg>(NextSkillPanel->GetAllChildren()[i]))continue;
		NextSkill = Cast<UNextSkillImg>(NextSkillPanel->GetAllChildren()[i]);

		/*
		* i 是当前编号
		* i / 3  + 1是该编号的行数
		*/ 
		NextSkill->MoveTranslation.Y = (CenterRow - (((i / 3) + 1) * 2 - 1)) * 80;


		NextSkill->MoveTranslation.X = (CenterColumn - (i % 3 * 2 + 1)) * 80;

	}
}

void UChooseSkillPanel::SortChooseSkill()
{

	if(this->ChooseSkillArray.Num() == 0)return;

	int Index = this->ChooseSkillArray.Find(this->CurChooseSkill);
	int num = 0;

	//CurChooseSkill之前的ChooseSkill位置排序
	while(Index > 0)
	{ 
		Index--;
		num++;
		if (Index < 0)break;
		this->ChooseSkillArray[Index]->SortChooseSkill(this->CurChooseSkill->GetDesiredSize().Y, num, true);
		
	}
	Index = this->ChooseSkillArray.Find(this->CurChooseSkill);
	num = 0;

	//CurChooseSkill之后的ChooseSkill位置排序
	while (Index < this->ChooseSkillArray.Num())
	{
		Index++;
		num++; 
		if (Index > this->ChooseSkillArray.Num() - 1)break;
		this->ChooseSkillArray[Index]->SortChooseSkill(this->CurChooseSkill->GetDesiredSize().Y, num, false);
	}
	this->CurChooseSkill->CurSortChooseSkill();
}

void UChooseSkillPanel::ToRefreshSkillUI(USkillUI* CurSkillUI)
{
	if (this->CurChooseSkill->Action == nullptr)CurSkillUI->CurActionInfo = nullptr;
	CurSkillUI->CurActionInfo = this->CurChooseSkill->Action;
}

void UChooseSkillPanel::ChooseSkillMouseOK_Implementation(UChooseSkill* ChooseSkill)
{
	
}