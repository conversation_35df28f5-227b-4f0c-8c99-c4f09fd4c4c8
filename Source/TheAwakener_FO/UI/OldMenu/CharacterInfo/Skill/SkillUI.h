// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Base/Skill_BasePanel.h"
#include "UMG.h"
#include "Engine/Texture2D.h"
#include "SkillUI.generated.h"

DECLARE_DELEGATE_OneParam(MouseOK, USkillUI*);
DECLARE_DELEGATE(MouseHovered);

UENUM(BlueprintType)
enum class ESkillUIState : uint8
{
	Null,
	Choosing,
	Void
};

UENUM(BlueprintType)
enum class ESkillUIProperty : uint8
{
	Default,
	Definite,
	Lock,
	Error
};

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API USkillUI : public USkill_BasePanel
{
	GENERATED_BODY()
	

private:

	//Skill面板中每一个可选择图标的属性及状态
	TPair<ESkillUIProperty, ESkillUIState> SkillUIState;

public:

	//是否是默认固定的
	UPROPERTY(BlueprintReadWrite)
	bool bIsDefinite;

	//该SkillUI所对应的Action
	FActionInfo* CurActionInfo = nullptr;

	//该SkillUI可供选择Action的数组
	TArray<FActionInfo*> CanChangeActionArray;

	//Key是UIIcon名称，Value是PassiveIcon名称
	TPair<FString, FString> UIIconName;
	
	//单播委托
	MouseOK D_MouseOK;
	MouseHovered D_MouseHovered;

public:

	//初始化
	UFUNCTION(BlueprintNativeEvent,BlueprintCallable)
	void Initial(UPARAM(ref)FString& UIProperty);
	virtual void Initial_Implementation(UPARAM(ref)FString& UIProperty);

	//获得SkillUI的基础属性(基础属性：普通，固定，锁定)
	UFUNCTION(BlueprintPure)
	ESkillUIProperty GetSkillUIProperty();

	//获得SkillUI的状态（状态：无，选中，错误，空）
	UFUNCTION(BlueprintPure)
	ESkillUIState GetSkillUIState();

	//设置SkillUI的状态
	UFUNCTION(BlueprintCallable)
	void SetSkillUIState(ESkillUIState State);

	UFUNCTION(BlueprintCallable)
	void SetSkillUIProperty(ESkillUIProperty Property);

	UFUNCTION(BlueprintCallable)
	void RefreshUIIcon();

	//获得当前SkillUI的Icon图片（若SkillUI状态为空，则为NoSkill图片）
	UFUNCTION(BlueprintPure)
	UTexture2D* GetUIIcon();

	//获得当前SkillUI的PassiveIcon图片（若SkillUI状态为空，则为nullptr）
	UFUNCTION(BlueprintPure)
	UTexture2D* GetPassiveIcon();

	//是否有当前Actioninfo，也就是是否有选定的Skill
	UFUNCTION(BlueprintPure)
	bool IsHasCurActionInfo();

	//获得可供选择Action的数量
	UFUNCTION(BlueprintPure)
	int GetCanChangeArrayNum();

	UFUNCTION(BlueprintPure)
	FActionInfo GetCurActionInfo();

	UFUNCTION(BlueprintNativeEvent, BlueprintCallable)
	void RefreshSkillUI();
	void RefreshSkillUI_Implementation();

	UFUNCTION(BlueprintNativeEvent, BlueprintCallable)
	void RefreshState();
	void RefreshState_Implementation();

	UFUNCTION(BlueprintCallable)
	void CheckSkillUIState(TArray<USkillUI*> SkillUIArray);

	UFUNCTION(BlueprintCallable)
	void ToSetSkillPanelCurSkillUI();

	UFUNCTION(BlueprintCallable)
	void MouseHovered();
};
