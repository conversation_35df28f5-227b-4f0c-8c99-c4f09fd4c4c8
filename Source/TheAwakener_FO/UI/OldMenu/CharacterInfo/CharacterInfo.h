// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/UI/OldMenu/Base/SubMenu.h"
#include "Math/UnrealMathUtility.h"
#include "Blueprint/WidgetLayoutLibrary.h"
#include "Property/PropertyUI.h"
#include "CharacterInfo.generated.h"

UENUM(BlueprintType)
enum class EStateSubPanel : uint8
{
	PropertyPanel,
	SkillPanel,
	PrestigePanel
};

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UCharacterInfo : public USubMenu
{
	GENERATED_BODY()
	
public:

	//当前状态
	UPROPERTY(BlueprintReadWrite, Category = "SubMenu|Character|Property")
	EStateSubPanel CurState;

public:

	//
	UFUNCTION(BlueprintImplementableEvent, BlueprintCallable)
	void TickTurn(float DeltaTime);


	//按键摁下的时候角色开始旋转
	UFUNCTION(BlueprintImplementableEvent, BlueprintCallable)
	void PressCharacter();

	//按键松开时角色停止旋转
	UFUNCTION(BlueprintImplementableEvent, BlueprintCallable)
	void RepressCharacter();

	//设置角色旋转
	UFUNCTION(BlueprintCallable)
	void SetCharacterRotation(AActor* Character, float Offset, float DeltaTime);

};
