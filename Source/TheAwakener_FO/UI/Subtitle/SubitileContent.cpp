// Fill out your copyright notice in the Description page of Project Settings.


#include "SubitileContent.h"

void USubitileContent::NativeOnInitialized()
{
	Super::NativeOnInitialized();

	SpeakerTextBlock = Cast<UTextBlock>(GetWidgetFromName("Speaker"));
	ContentOfSpeechTextBlock = Cast<UTextBlock>(GetWidgetFromName("ContentOfSpeech"));
}

void USubitileContent::NativeConstruct()
{
	Super::NativeConstruct();
}

void USubitileContent::NativeTick(const FGeometry& MyGeometry, float InDeltaTime)
{
	Super::NativeTick(MyGeometry, InDeltaTime);
}
