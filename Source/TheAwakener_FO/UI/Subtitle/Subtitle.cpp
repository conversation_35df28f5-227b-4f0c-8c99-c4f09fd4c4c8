// Fill out your copyright notice in the Description page of Project Settings.


#include "Subtitle.h"

void USubtitle::NativeOnInitialized()
{
	Super::NativeOnInitialized();

	Subtitle_Panel = Cast<UCanvasPanel>(GetWidgetFromName("SubtitlePanel"));
}

void USubtitle::NativeConstruct()
{
	Super::NativeConstruct();
}

void USubtitle::NativeTick(const FGeometry& MyGeometry, float InDeltaTime)
{
	Super::NativeTick(MyGeometry, InDeltaTime);
}
