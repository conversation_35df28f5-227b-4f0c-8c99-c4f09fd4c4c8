// The copyright of this project belongs to Shanghai Minghuan Software Technology Co., Ltd

#pragma once

#include "CoreMinimal.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "ShaderCompilationBPLibrary.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UShaderCompilationBPLibrary : public UBlueprintFunctionLibrary
{
	GENERATED_UCLASS_BODY()

public:
	UFUNCTION(BlueprintCallable,Category = "Shader Compilation")
	static TArray<TSoftObjectPtr<UMaterial>> GetAllMaterials();
	
	UFUNCTION(BlueprintCallable,Category = "Shader Compilation")
	static TArray<TSoftObjectPtr<UNiagaraSystem>> GetAllNiagaraParticleSystems();

	UFUNCTION(BlueprintCallable,Category = "Shader Compilation")
	static bool AreShadersCompiling();
};
