
#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/FunctionLibrary/DataFuncLib.h"
#include "MobProp.generated.h"

/**
 * 怪物属性
 */
USTRUCT(BlueprintType)
struct FMobProp
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString Id = "";
	// 怪物名字
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString Name = "";
	// 血量
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int HP = 100;
	// 攻击力
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int Atk = 1;
	// Break值
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int Break = 100;
	// 移动速度
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int MoveSpeed = 400;
	// 经验
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int Exp = -1;
	
	static FMobProp FromJson(const TSharedPtr<FJsonObject>& JsonObj)
	{
		FMobProp Res;
	
		Res.Id = UDataFuncLib::AwGetStringField(JsonObj, "ID", "");
		Res.Name = UDataFuncLib::AwGetStringField(JsonObj, "Name", "");
		Res.HP = UDataFuncLib::AwGetNumberField(JsonObj, "HP", 100);
		Res.Atk = UDataFuncLib::AwGetNumberField(JsonObj, "Atk", 1);
		Res.Break = UDataFuncLib::AwGetNumberField(JsonObj, "Break", 100);
		Res.MoveSpeed = UDataFuncLib::AwGetNumberField(JsonObj, "MoveSpeed", 400);
		Res.Exp = UDataFuncLib::AwGetNumberField(JsonObj, "Exp", -1);

		return Res;
	}
};
