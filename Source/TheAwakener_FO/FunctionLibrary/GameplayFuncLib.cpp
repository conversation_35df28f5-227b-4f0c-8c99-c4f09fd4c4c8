// Fill out your copyright notice in the Description page of Project Settings.


#include "GameplayFuncLib.h"

#include "DataFuncLib.h"
#include "Kismet/GameplayStatics.h"
#include "TheAwakener_FO/FunctionLibrary/ResourceFuncLib.h"
#include "Kismet/KismetSystemLibrary.h"
#include "TheAwakener_FO/GameFramework/Manager/AwRogueDataSystem.h"
#include "TheAwakener_FO/GamePlay/GameSave/AwakerSaveData.h"
#include "TheAwakener_FO/GamePlay/Map/GameDestSign.h"
#include "TheAwakener_FO/GamePlay/Roguelike/BattleStyle/RogueBattleStyleSubSystem.h"
#include "TheAwakener_FO/GamePlay/Roguelike/BattleUpgrade/RogueBattleUpgradeSubSystem.h"
#include "TheAwakener_FO/GamePlay/Roguelike/Item/AwRogueItemSubSystem.h"
#include "TheAwakener_FO/GamePlay/Role/AwRoleInfo.h"
#include "TheAwakener_FO/UI/GameMain/ChangeEquipment.h"
#include "TheAwakener_FO/UI/GameMain/DialogUI.h"
#include "TheAwakener_FO/UI/GameMain/GameMenu.h"
#include "TheAwakener_FO/UI/GameMain/ItemShortcutBarUI.h"
#include "TheAwakener_FO/UI/GameMain/NewbieRetrospect.h"
#include "TheAwakener_FO/UI/GameMain/PowerMap.h"
#include "TheAwakener_FO/UI/Roguelike/FightingMain/RogueFightingMainUI.h"
#include "TheAwakener_FO/UI/Subtitle/Subtitle.h"
#include "TheAwakener_FO/UI/SystemSettings/SystemSettingsUI.h"
#include "Sound/DialogueVoice.h"
#include "Blueprint/WidgetBlueprintLibrary.h"
#include "Components/SkeletalMeshComponent.h"
#include "Components/StaticMeshComponent.h"
#include "Engine/StaticMesh.h"
#include "Engine/Texture2D.h"
#include "TheAwakener_FO/GamePlay/ApparatusCustom/Traits/Enemy.h"
#include "UObject/UnrealType.h"

UAwDataManager* UGameplayFuncLib::GetAwDataManager()
{
	return  UAwGameInstance::Instance->DataManager;
}

bool UGameplayFuncLib::CanUseDebug()
{
	if (UAwGameInstance::Instance && UAwGameInstance::Instance->DataManager)
		return UAwGameInstance::Instance->DataManager->CanUseDebug;
	return false;
}

bool UGameplayFuncLib::IsRogueMode()
{
	return  GetAwDataManager()?GetAwDataManager()->bIsRogueMode:false;
}

//TODO: 次接口目前还未完成
void UGameplayFuncLib::RemoveBuffByID(AActor* Carrier, FString BuffID)
{
	
}

AActor* UGameplayFuncLib::CreateActorByBP(FString BluePrintPath, FTransform Transform){
	AActor* Item = nullptr;
	
	const FString BpPath = UResourceFuncLib::GetBpAssetPath(BluePrintPath);
	UClass* BpClass = LoadClass<AActor>(nullptr,*BpPath);
	if (BpClass)
	{
		FActorSpawnParameters ActorSpawnParameters;
		ActorSpawnParameters.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AlwaysSpawn;
		if (UAwGameInstance::Instance)
			Item = UAwGameInstance::Instance->GetWorld()->SpawnActor<AActor>(BpClass, Transform, ActorSpawnParameters);
	}
	return Item;
}

void UGameplayFuncLib::SaveGame()
{
	if (IsRogueMode())
		return;
	
	USaveGame* SGame = UGameplayStatics::LoadGameFromSlot("GameSaveData", 0);
	if (!SGame)
		SGame = UGameplayStatics::CreateSaveGameObject(UAwakerSaveData::StaticClass());
	
	UAwakerSaveData* AwSGame = nullptr;
	AwSGame = Cast<UAwakerSaveData>(SGame);
	if (AwSGame)
	{
		AAwGameState* GameState = GetAwGameState();
		if(GameState && GetAwGameInstance() && GameState->GetMyCharacter())
			if (!GameState->GetMyCharacter()->GetName().Contains("NaN_Pawn"))
			{
				// TODO：为了在 Title 的 GameMode下读取键入 弄了一个 NaN_Pawn 的 AwCharacter
				// TODO：之后需要修改整体流程。
				GetAwGameInstance()->RoleInfo.MainCharacter = GameState->GetMyCharacter()->CharacterObj.GetCharacterInfoSaveData();
				GetAwGameInstance()->bHaveSave = true;
			}
				
		
		//TODO:目前默认用Player[0]，后面加了账号系统的判断在使用其他Index
		if (AwSGame->Players.Num())
		{
			if (GetAwGameInstance()->CurRoleIndex <= AwSGame->Players[0].Roles.Num() - 1)
			{
				AwSGame->Players[0].Roles[GetAwGameInstance()->CurRoleIndex] = GetAwGameInstance()->RoleInfo;
			}
			else
			{
				const FAwRoleInfo NewRole = GetAwGameInstance()->RoleInfo;
				GetAwGameInstance()->CurRoleIndex = AwSGame->Players[0].Roles.Add(NewRole);
			}
		}
		else
		{
			FAwPlayerInfo NewPlayer = FAwPlayerInfo();
			FAwRoleInfo NewRole = GetAwGameInstance()->RoleInfo;
			NewRole.CurLanguage = GetAwGameInstance()->Language;
			NewRole.CurGamepadButtonType = GetAwGameInstance()->GamepadButtonType;
			NewPlayer.Roles.Add(NewRole);
			AwSGame->Players.Add(NewPlayer);
		}
		UGameplayStatics::SaveGameToSlot(AwSGame, "GameSaveData", 0);
	}
}

FAwRoleInfo UGameplayFuncLib::LoadGame(bool& HasSaveData, int LoadRoleIndex)
{
	UAwakerSaveData* GameData = Cast<UAwakerSaveData>(UGameplayStatics::LoadGameFromSlot(TEXT("GameSaveData"), 0));
	if (GameData)
	{
		//TODO:目前默认用Player[0]，后面加了账号系统的判断在使用其他Index
		if (GameData->Players.Num())
		{
			if (LoadRoleIndex <= GameData->Players[0].Roles.Num() - 1)
			{
				HasSaveData = true;
				FAwRoleInfo RoleInfo = GameData->Players[0].Roles[LoadRoleIndex];
				if (GetAwGameInstance())
				{
					GetAwGameInstance()->Language = RoleInfo.CurLanguage;
					GetAwGameInstance()->GamepadButtonType = RoleInfo.CurGamepadButtonType;
				}
				return RoleInfo;
			}
		}
		//CreateCharacterBySaveData(FTransform(),GameData->Roles[0].MainCharacter);
		//TODO 还有其他的
	}
	HasSaveData = false;
	return FAwRoleInfo();
}

void UGameplayFuncLib::DeleteGame(int LoadRoleIndex)
{
	UGameplayStatics::DeleteGameInSlot(TEXT("GameSaveData"), 0);
}

AAwCharacter* UGameplayFuncLib::CreateCharacterBySaveData(FTransform Transform, FAwCharacterInfo CharacterInfo)
{
	AAwCharacter* Character = nullptr;

	// const FBattleClassModel ClassInfo = GetDataManager()->GetBattleClassModelById(CharacterInfo.ClassId);
	
	const FString Path = IsRogueMode() ?
		GetDataManager()->GetRolePawnByClassId(CharacterInfo.ClassId).BpPath:
		GetDataManager()->GetRolePawnByTypeId(CharacterInfo.TypeId).BpPath;
	const FString BpPath = UResourceFuncLib::GetBpAssetPath(Path);
	UClass* BpClass = LoadClass<APawn>(nullptr,*BpPath);
	if (BpClass)
	{
		FActorSpawnParameters ActorSpawnParameters;
		ActorSpawnParameters.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AlwaysSpawn;
		Character = GWorld->SpawnActor<AAwCharacter>(BpClass, Transform, ActorSpawnParameters);
		if (IsValid(Character))
		{
			Character->SetupByCharacterInfo(CharacterInfo);
			const FString LevelName = UAwGameInstance::Instance->CurLevelName;
			if(GetAwGameState())
				GetAwGameState()->AllCharacters.Add(Character, LevelName);
		}
		else
			UE_LOG(LogTemp, Error, TEXT("Spawn AwCharacter failed!"), nullptr);
	}
	else
		UE_LOG(LogTemp, Error, TEXT("Load bpClass failed! (path: %s)"), *BpPath);

	return Character;
}

AAwCharacter* UGameplayFuncLib::CreateCharacterByNpcInfo(FTransform Transform, FNpcInfo NpcInfo, FString LevelName)
{
	AAwCharacter* Res = CreateCharacterByMobInfo(Transform, NpcInfo.MobModelId, NpcInfo.InitLevel, NpcInfo.ToSide, NpcInfo.ThisMobRank, LevelName);
	if (Res)
	{
		Res->SetNpcInfo(NpcInfo);
	}
	return  Res;
}

AAwCharacter* UGameplayFuncLib::CreateCharacterBySpawnInfo(FNPCSpawnInfo SpawnInfo, FString LevelName)
{
	AAwCharacter* NewNPC = CreateCharacterByNpcInfo(SpawnInfo.SpawnTrans, SpawnInfo.NpcInfo, LevelName);
	if (NewNPC)
	{
		//添加PathNodeQueue
		if(UGameplayFuncLib::GetAwGameState()->AwMapPathNodeQueues.Contains(SpawnInfo.PathNodeQueueId))
		{
			if (NewNPC->GetAIComponent())
			{
				NewNPC->GetAIComponent()->PathNodeQueue = UGameplayFuncLib::GetAwGameState()->AwMapPathNodeQueues[SpawnInfo.PathNodeQueueId];
				NewNPC->GetAIComponent()->PathNodeQueue.NodeIndex = SpawnInfo.PathNodeQueueIndex;
			}
		}
		//添加AIScript
		if (NewNPC->GetAIComponent())
		{
			for (int j = 0; j < SpawnInfo.AIClips.Num(); j++)
			{
				FAIScriptPart AIScript = GetAwGameInstance()->DataManager->GetAIScriptById(SpawnInfo.AIClips[j]);
				NewNPC->GetAIComponent()->AIScriptPartList.Add(AIScript);
			}
		}
		//添加BUff
		for (int k = 0; k < SpawnInfo.Buffs.Num(); k++)
		{
			FAddBuffInfo NewBuffInfo;
			NewBuffInfo.Model = GetAwGameInstance()->DataManager->GetBuffModelById(SpawnInfo.Buffs[k].Id);
			NewBuffInfo.Target = NewNPC;
			NewBuffInfo.Caster = NewNPC;
			NewBuffInfo.AddStack = SpawnInfo.Buffs[k].AddStack;
			NewBuffInfo.Duration = 9999;
			NewBuffInfo.Infinity = true;
			if (NewBuffInfo.Model.ValidBuffModel())
				NewNPC->AddBuff(NewBuffInfo);
		}
		//调用OnCreate
		for(auto OnCreate : SpawnInfo.OnCreate)
		{
			UFunction* OnCreateFunc = UCallFuncLib::GetUFunction(OnCreate.ClassPath, OnCreate.FunctionName);
			if (OnCreateFunc)
			{
				struct
				{
					AAwCharacter* NPC;
					TArray<FString> Params;
				}OnCreateFuncParam;
				OnCreateFuncParam.NPC = NewNPC;
				OnCreateFuncParam.Params = OnCreate.Params;
				GetAwGameInstance()->ProcessEvent(OnCreateFunc, &OnCreateFuncParam);
			}
		}
	}
	return NewNPC;
}

AAwCharacter* UGameplayFuncLib::ChangeRoguePawn(FString ClassId,int playerIndex,bool IsWorkaround)
{
	if (!IsRogueMode())
		return nullptr;
	
	AAwCharacter* OldCharacter = GetLocalAwPlayerCharacter(playerIndex);
	//if(OldCharacter->PlayerClassId == ClassId)
	{
		AAwCharacter* NewCharacter = CreateCharacterByClass(OldCharacter->GetTransform(), ClassId);
		if (NewCharacter)
		{
			auto pc = GetLocalAwPlayerController(playerIndex);
			OldCharacter->SetPlayerControllable(pc, false);
			OldCharacter->Destroy();
			NewCharacter->SetPlayerControllable(pc, true);
			NewCharacter->GetReady();
			pc->InitInClient(NewCharacter);
			pc->OnChangeRoguePawn.Broadcast();
			
			UAwRogueDataSystem* RogueDataSystem = GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>();
			RogueDataSystem->SetCurPawnClassId(ClassId,playerIndex);
			RogueDataSystem->SaveData();
			//RogueDataSystem->PostLoadSaveDataInHall();
			if (!IsWorkaround)
			{
				RoguePlayerReGetRogueEffect(ClassId,playerIndex,IsWorkaround);
			}

			GetAwGameMode()->OnPlayerChangePawn.Broadcast(NewCharacter);
			
			return NewCharacter;
		}
	}
	return nullptr;
}

void UGameplayFuncLib::RoguePlayerReGetRogueEffect(FString ClassId,int playerIndex,bool IsWorkaround)
{
	// 天赋
	UAwRogueTalentSubSystem* TalentSubSystem = GetAwGameInstance()->GetSubsystem<UAwRogueTalentSubSystem>();
	if (TalentSubSystem)
	{
		TalentSubSystem->ReGetTalentEffect(GetLocalAwPlayerController(playerIndex));
	}
	// 遗物
	UAwRelicSubSystem* RelicSubSystem = GetAwGameInstance()->GetSubsystem<UAwRelicSubSystem>();
	if (RelicSubSystem)
	{
		RelicSubSystem->ReGetRelic();
	}

	// 动作强化
	
	// if (URogueBattleStyleSubSystem* BattleStyleSubSystem = GetAwGameInstance()->GetSubsystem<URogueBattleStyleSubSystem>())
	// {
	//	UAwRogueDataSystem* RogueDataSystem = GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	// 	FString StyleId = RogueDataSystem->GetBattleStyle(ClassId);
	// 	ChangeRogueBattleStyle(StyleId);
	// 	BattleStyleSubSystem->UpgradeDefault();
	// }
	
	
	if (IsWorkaround==false)
	{
		UAwRogueDataSystem* RogueDataSystem = GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>();
		// const FString CurPawnClassId = RogueDataSystem->GetCurPawnClassId(playerIndex);
		URogueBattleUpgradeSubSystem* BattleUpgradeSubSystem = GetAwGameInstance()->GetSubsystem<URogueBattleUpgradeSubSystem>();
		BattleUpgradeSubSystem->OnClearBattle();
		BattleUpgradeSubSystem->SetAbilityInfos(playerIndex,RogueDataSystem->GetDefaultAbilityLevelInfos(ClassId), false);
	}
}

void UGameplayFuncLib::ShowSurvivorLevelUpReward()
{
	UAwRogueDataSystem* RogueDataSystem = GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	RogueDataSystem->SetShopIsActive_CurBattle(false);
	GetUiManager()->Show("Survivor_LevelUp");
}

void UGameplayFuncLib::ChangeRogueBattleStyle(FString StyleId,const int PlayerIndex)
{
	const AAwCharacter* CurCharacter = GetLocalAwPlayerCharacter(0);
	if (!CurCharacter) return;

	UAwRogueDataSystem* RogueDataSystem = GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	FString LastStyleId = RogueDataSystem->GetBattleStyle(CurCharacter->CharacterObj.ClassId);
	RogueDataSystem->ClearUpgrade(LastStyleId);

	URogueBattleStyleSubSystem* BattleStyleSubSystem = GetAwGameInstance()->GetSubsystem<URogueBattleStyleSubSystem>();
	BattleStyleSubSystem->ClearTempUpgradeInfo();

	const FBattleClassModel BattleClass = GetAwDataManager()->GetBattleClassModelById(CurCharacter->CharacterObj.ClassId);
	if (CurCharacter->GetActionComponent())
		CurCharacter->GetActionComponent()->ResetActions(BattleClass, StyleId, CurCharacter->CharacterObj.TypeId);
	
	RogueDataSystem->SetBattleStyle(CurCharacter->CharacterObj.ClassId, StyleId);
	
	BattleStyleSubSystem->UpgradeDefault();
	
	RogueDataSystem->SaveData();
}

AAwCharacter* UGameplayFuncLib::CreateCharacterByMobInfo(FTransform Transform, FString MobModelId, int MobLevel, int Side, EMobRank MobRank, FString LevelName,FString AlterId)
{
	const FMobModel MobModel = GetAwDataManager()->GetMobModelById(MobModelId,AlterId);
	return CreatCreateByMobModel(MobModel, Transform, MobLevel, Side, MobRank, LevelName, AlterId);
}

AAwCharacter* UGameplayFuncLib::CreateStakePawn(FTransform Transform, FString MobModelId,
												int MobLevel, int HPStar, int AtkStar, FString LevelName)
{
	FMobModel MobModel = GetAwDataManager()->GetMobModelById(MobModelId);
	MobModel.BaseProp.HP = HPStar;
	MobModel.BaseProp.PAttack = AtkStar;
	return CreatCreateByMobModel(MobModel, Transform, MobLevel, 1, EMobRank::Normal, LevelName);
}

FMobModel UGameplayFuncLib::SetMobModel(FMobModel MobModel, int HPStar, int AtkStar)
{
	MobModel.BaseProp.HP = HPStar;
	MobModel.BaseProp.PAttack = AtkStar;
	return MobModel;
}

AAwCharacter* UGameplayFuncLib::CreatCreateByMobModel(FMobModel MobModel, FTransform Transform, int MobLevel, int Side,
                                                      EMobRank MobRank, FString LevelName, FString AlterId)
{
	AAwCharacter* Character = nullptr;
	if (MobModel.BpPath.IsEmpty())
	{
		UE_LOG(LogTemp, Error, TEXT("Spawn AwCharacter failed! No MobModel.BpPath"));
		return Character;
	}
	
	FString BpPath = UResourceFuncLib::GetBpAssetPath(MobModel.BpPath);
	UClass* BpClass = LoadClass<APawn>(nullptr,*BpPath);
	if (BpClass)
	{
		FActorSpawnParameters ActorSpawnParameters;
		ActorSpawnParameters.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AlwaysSpawn;
		Character = GWorld->SpawnActor<AAwCharacter>(BpClass, Transform, ActorSpawnParameters);
		if (IsValid(Character))
		{
			//Character->SetupAsMonster(MobModel, MobLevel, Side, MobRank);
			//Rogue:在Model里直接配置MobRank
			Character->SetupAsMonster(MobModel, MobLevel, Side, MobModel.MobRank,AlterId);
			FString CurSpawnTag = LevelName;
			if(CurSpawnTag == "")
				CurSpawnTag = UAwGameInstance::Instance->CurLevelName;
			GetAwGameState()->AllCharacters.Add(Character, CurSpawnTag);
		}
		else
			UE_LOG(LogTemp, Error, TEXT("Spawn AwCharacter failed!"));
	}
	else
		UE_LOG(LogTemp, Error, TEXT("Load bpClass failed! (path: %s)"), *BpPath);

	return Character;
}

AAwCharacter* UGameplayFuncLib::CreateCharacterByClass(FTransform Transform, FString PlayerClassId, FString TypeId)
{
	AAwCharacter* Character = nullptr;
	// FBattleClassModel ClassModel = GetAwDataManager()->GetBattleClassModelById(PlayerClassId);

	const FString Path = IsRogueMode() ?
		GetDataManager()->GetRolePawnByClassId(PlayerClassId).BpPath:
		GetDataManager()->GetRolePawnByTypeId(TypeId).BpPath;
	const FString BpPath = UResourceFuncLib::GetBpAssetPath(Path);
	UClass* BpClass = LoadClass<APawn>(nullptr,*BpPath);
	if (BpClass)
	{
		FActorSpawnParameters ActorSpawnParameters;
		ActorSpawnParameters.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AlwaysSpawn;
		Character = GWorld->SpawnActor<AAwCharacter>(BpClass, Transform, ActorSpawnParameters);
		if (IsValid(Character))
		{
			Character->SetupAsPlayerCharacter(PlayerClassId, TypeId);
			GetAwGameState()->AllCharacters.Add(Character, UAwGameInstance::Instance->CurLevelName);
		}
		else
			UE_LOG(LogTemp, Error, TEXT("Spawn AwCharacter failed!"));
	}
	else
		UE_LOG(LogTemp, Error, TEXT("Load bpClass failed! (path: %s)"), *BpPath);

	return Character;
}

FAwRoleInfo UGameplayFuncLib::CreateNewGame(FString Name, FString TypeId, EAwRace Race,
	FString Appearance, FString Voice, FString ClassId, EChaElemental Elemental)
{
	FAwRoleInfo RoleInfo = FAwRoleInfo();

	FAwCharacterInfo CharacterInfo = FAwCharacterInfo();
	CharacterInfo.Name = Name;
	CharacterInfo.TypeId = TypeId;
	CharacterInfo.Race = Race;
	// CharacterInfo.Appearance = Appearance;
	// CharacterInfo.Voice = Voice;
	CharacterInfo.ClassId = ClassId;
	CharacterInfo.Elemental = Elemental;

	RoleInfo.MainCharacter = CharacterInfo;
	
	RoleInfo.CurLanguage = GetAwGameInstance()->Language;
	RoleInfo.CurGamepadButtonType = GetAwGameInstance()->GamepadButtonType;
	
	GetAwGameInstance()->RoleInfo = RoleInfo;
	
	return GetAwGameInstance()->RoleInfo;
}

ADialogModeActor* UGameplayFuncLib::CreateDialogModeActor()
{
	if (GetAwGameState()->DialogModeActor && GetAwGameState()->DialogModeActor->GetClass()->IsChildOf(ADialogModeActor::StaticClass()))
	{
		return GetAwGameState()->DialogModeActor;
	}
	ADialogModeActor* Res = nullptr;
	const FString BpPath = UResourceFuncLib::GetBpAssetPath("Core/Camera/GameDialogMode");
	UClass* BpClass = LoadClass<AActor>(nullptr,*BpPath);
	if (BpClass)
	{
		FActorSpawnParameters ActorSpawnParameters;
		ActorSpawnParameters.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AlwaysSpawn;
		Res = GWorld->SpawnActor<ADialogModeActor>(BpClass, FTransform::Identity, ActorSpawnParameters);
		GetAwGameState()->DialogModeActor = Res;
	}
	return Res;
}

UParticleSystemComponent* UGameplayFuncLib::CreateVFXatLocation(UParticleSystem* Template, FTransform Transform, bool AutoDestroy, bool AutoActivateSystem)
{
	if (GetAwGameInstance())
		return GetAwGameInstance()->VFXManager->CreateVFXatLocation(Template, Transform, AutoDestroy, AutoActivateSystem);

	return nullptr;
}

UParticleSystemComponent* UGameplayFuncLib::CreateVFXByPathAtLocation(FString EffectPath, FTransform Transform, bool AutoDestroy, bool AutoActivateSystem)
{
	UParticleSystem* Template = LoadObject<UParticleSystem>(nullptr,*UResourceFuncLib::GetAssetPath(EffectPath));
	if (!Template) return nullptr;
	if (GetAwGameInstance())
		return GetAwGameInstance()->VFXManager->CreateVFXatLocation(Template, Transform, AutoDestroy, AutoActivateSystem);

	return nullptr;
}


UParticleSystemComponent* UGameplayFuncLib::CreateVFXAttached(UParticleSystem* Template, USceneComponent* AttachToComponent, FName AttachPointName, FTransform Transform, EAttachLocation::Type LocationType, bool AutoDestroy, bool AutoActivateSystem)
{
	if (GetAwGameInstance())
		return GetAwGameInstance()->VFXManager->CreateVFXAttached(Template, AttachToComponent, AttachPointName, Transform, LocationType, AutoDestroy, AutoActivateSystem);
	return nullptr;
}

UParticleSystemComponent* UGameplayFuncLib::CreateVFXByPathAttached(FString EffectPath, USceneComponent* AttachToComponent, FName AttachPointName, FTransform Transform, EAttachLocation::Type LocationType, bool AutoDestroy, bool AutoActivateSystem)
{
	UParticleSystem* Template = LoadObject<UParticleSystem>(nullptr,*UResourceFuncLib::GetAssetPath(EffectPath));
	if (!Template) return nullptr;
	if (GetAwGameInstance())
		return GetAwGameInstance()->VFXManager->CreateVFXAttached(Template, AttachToComponent, AttachPointName, Transform, LocationType, AutoDestroy, AutoActivateSystem);
	return nullptr;
}

UAudioComponent* UGameplayFuncLib::PlaySystemAudio(USoundBase* Sound, bool bPersistAcrossLevelTransition)
{
	if (GetAwGameInstance())
		return GetAwGameInstance()->SFXManager->PlaySystemAudio(Sound, 1, 1, 0, bPersistAcrossLevelTransition);
	return nullptr;
}

void UGameplayFuncLib::StopAllSystemAudio()
{
	if (GetAwGameInstance())
		GetAwGameInstance()->SFXManager->StopAllSystemAudio();
}

UAudioComponent* UGameplayFuncLib::PlaySFXatLocation(USoundBase* Sound, FVector Location, FRotator Rotation,
	float VolumeMultiplier, float PitchMultiplier, float StartTime, USoundAttenuation* SoundAttenuation, USoundConcurrency* SoundConcurrency)
{
	if (GetAwGameInstance())
		return GetAwGameInstance()->SFXManager->PlaySFXatLocation(Sound, Location, Rotation, VolumeMultiplier, PitchMultiplier, StartTime, SoundAttenuation, SoundConcurrency);
	return nullptr;
}

UAudioComponent* UGameplayFuncLib::PlaySFXByPathAtLocation(FString SoundPath, FVector Location, FRotator Rotation,
	float VolumeMultiplier, float PitchMultiplier, float StartTime, USoundAttenuation* SoundAttenuation, USoundConcurrency* SoundConcurrency)
{
	USoundBase* Sound = LoadObject<USoundBase>(nullptr,*UResourceFuncLib::GetAssetPath(SoundPath));
	if (!Sound) return nullptr;
	if (GetAwGameInstance())
		return GetAwGameInstance()->SFXManager->PlaySFXatLocation(Sound, Location, Rotation, VolumeMultiplier, PitchMultiplier, StartTime, SoundAttenuation, SoundConcurrency);
	return nullptr;
}

UAudioComponent* UGameplayFuncLib::PlaySFXAttached(USoundBase* Sound, USceneComponent* AttachtoComponent, FName AttachPointName,
	FVector Location, FRotator Rotation, EAttachLocation::Type LocationType, bool StopWhenAttachedtoDestroyed,
	float VolumeMultiplier, float PitchMultiplier, float StartTime, USoundAttenuation* SoundAttenuation, USoundConcurrency* SoundConcurrency)
{
	if (GetAwGameInstance())
		return GetAwGameInstance()->SFXManager->PlaySFXAttached(Sound, AttachtoComponent, AttachPointName, Location, Rotation,
			LocationType, StopWhenAttachedtoDestroyed, VolumeMultiplier, PitchMultiplier, StartTime, SoundAttenuation, SoundConcurrency);
	return nullptr;
}

UAudioComponent* UGameplayFuncLib::PlaySFXByPathAttached(FString SoundPath, USceneComponent* AttachToComponent, FName AttachPointName,
	FVector Location, FRotator Rotation, EAttachLocation::Type LocationType, bool StopWhenAttachedtoDestroyed,
	float VolumeMultiplier, float PitchMultiplier, float StartTime, USoundAttenuation* SoundAttenuation, USoundConcurrency* SoundConcurrency)
{
	USoundBase* Sound = LoadObject<USoundBase>(nullptr,*UResourceFuncLib::GetAssetPath(SoundPath));
	if (!Sound) return nullptr;
	if (GetAwGameInstance())
		return GetAwGameInstance()->SFXManager->PlaySFXAttached(Sound, AttachToComponent, AttachPointName, Location, Rotation,
			LocationType, StopWhenAttachedtoDestroyed, VolumeMultiplier, PitchMultiplier, StartTime, SoundAttenuation, SoundConcurrency);
	return nullptr;
}

UAudioComponent* UGameplayFuncLib::PlayVoiceAtLocation(USoundBase* Sound, FVector Location, FRotator Rotation,
	float VolumeMultiplier, float PitchMultiplier, float StartTime, USoundAttenuation* SoundAttenuation, USoundConcurrency* SoundConcurrency)
{
	if (GetAwGameInstance())
		return GetAwGameInstance()->SFXManager->PlayVoiceatLocation(Sound, Location, Rotation,
			VolumeMultiplier, PitchMultiplier, StartTime, SoundAttenuation, SoundConcurrency);
	return nullptr;
}

UAudioComponent* UGameplayFuncLib::PlayVoiceByPathAtLocation(FString SoundPath, FVector Location, FRotator Rotation,
	float VolumeMultiplier, float PitchMultiplier, float StartTime, USoundAttenuation* SoundAttenuation, USoundConcurrency* SoundConcurrency)
{
	USoundBase* Sound = LoadObject<USoundBase>(nullptr,*UResourceFuncLib::GetAssetPath(SoundPath));
	if (!Sound) return nullptr;
	if (GetAwGameInstance())
		return GetAwGameInstance()->SFXManager->PlayVoiceatLocation(Sound, Location, Rotation, VolumeMultiplier, PitchMultiplier, StartTime, SoundAttenuation, SoundConcurrency);
	return nullptr;
}

UAudioComponent* UGameplayFuncLib::PlayVoiceAttached(USoundBase* Sound, USceneComponent* AttachtoComponent,
	FName AttachPointName, FVector Location, FRotator Rotation, EAttachLocation::Type LocationType,
	bool StopWhenAttachedtoDestroyed, float VolumeMultiplier, float PitchMultiplier, float StartTime, USoundAttenuation* SoundAttenuation, USoundConcurrency* SoundConcurrency)
{
	if (GetAwGameInstance())
		return GetAwGameInstance()->SFXManager->PlayVoiceAttached(Sound, AttachtoComponent, AttachPointName,
			Location, Rotation, LocationType, StopWhenAttachedtoDestroyed, VolumeMultiplier, PitchMultiplier, StartTime, SoundAttenuation, SoundConcurrency);
	return nullptr;
}

UAudioComponent* UGameplayFuncLib::PlayVoiceByPathAttached(FString SoundPath, USceneComponent* AttachToComponent,
	FName AttachPointName, FVector Location, FRotator Rotation, EAttachLocation::Type LocationType,
	bool StopWhenAttachedtoDestroyed, float VolumeMultiplier, float PitchMultiplier, float StartTime, USoundAttenuation* SoundAttenuation, USoundConcurrency* SoundConcurrency)
{
	USoundBase* Sound = LoadObject<USoundBase>(nullptr,*UResourceFuncLib::GetAssetPath(SoundPath));
	if (!Sound) return nullptr;
	if (GetAwGameInstance())
		return GetAwGameInstance()->SFXManager->PlayVoiceAttached(Sound, AttachToComponent, AttachPointName,
			Location, Rotation, LocationType, StopWhenAttachedtoDestroyed, VolumeMultiplier, PitchMultiplier, StartTime, SoundAttenuation, SoundConcurrency);
	return nullptr;
}

UAudioComponent* UGameplayFuncLib::PlayVoice2D(USoundBase* Sound, float VolumeMultiplier, float PitchMultiplier,
	float StartTime)
{
	if (GetAwGameInstance())
		return GetAwGameInstance()->SFXManager->PlayVoice2D(Sound, VolumeMultiplier, PitchMultiplier, StartTime);
	return nullptr;
}

UAudioComponent* UGameplayFuncLib::PlayVoice2DByPath(FString SoundPath, float VolumeMultiplier, float PitchMultiplier,
	float StartTime)
{
	USoundBase* Sound = LoadObject<USoundBase>(nullptr,*UResourceFuncLib::GetAssetPath(SoundPath));
	if (!Sound) return nullptr;
	if (GetAwGameInstance())
		return GetAwGameInstance()->SFXManager->PlayVoice2D(Sound, VolumeMultiplier, PitchMultiplier, StartTime);
	return nullptr;
}

void UGameplayFuncLib::StopVoice(UAudioComponent* Voice, float FadeOutTime)
{
	if(Voice)
	{
		Voice->FadeOut(FadeOutTime,0.0f);
	}
}

void UGameplayFuncLib::PlayBGM(USoundBase* Sound, bool bPersistAcrossLevelTransition,
                               float LastFadeOutTime)
{
	if (GetAwGameInstance())
		GetAwGameInstance()->SFXManager->PlayBGM(Sound, 1, 1, 0, bPersistAcrossLevelTransition, LastFadeOutTime);
}

void UGameplayFuncLib::PlayBgmByKey(FString BgmKey, bool bPersistAcrossLevelTransition, float LastFadeOutTime)
{
	if (GetAwGameInstance())
		GetAwGameInstance()->SFXManager->PlayBgmByKey(BgmKey, 1, 1, 0, bPersistAcrossLevelTransition, LastFadeOutTime);
}

void UGameplayFuncLib::StopBGM(float FadeOutTime)
{
	if (GetAwGameInstance())
		GetAwGameInstance()->SFXManager->StopBGM(FadeOutTime);
}

void UGameplayFuncLib::SetMainSoundVolume(float NewVolume)
{
	if (GetAwGameInstance())
		GetAwGameInstance()->SFXManager->SetMainVolume(NewVolume);
}

void UGameplayFuncLib::SetBGMVolume(float NewVolume)
{
	if (GetAwGameInstance())
		GetAwGameInstance()->SFXManager->SetBGMVolume(NewVolume);
}

void UGameplayFuncLib::SetSystemAudioVolume(float NewVolume)
{
	if (GetAwGameInstance())
		GetAwGameInstance()->SFXManager->SetSystemAudioVolume(NewVolume);
}

void UGameplayFuncLib::SetSFXVolume(float NewVolume)
{
	if (GetAwGameInstance())
		GetAwGameInstance()->SFXManager->SetSFXVolume(NewVolume);
}

void UGameplayFuncLib::SetVoiceVolume(float NewVolume)
{
	if (GetAwGameInstance())
		GetAwGameInstance()->SFXManager->SetVoiceVolume(NewVolume);
}

float UGameplayFuncLib::GetMainSoundVolume()
{
	if (GetAwGameInstance())
		return GetAwGameInstance()->SFXManager->GetMainVolume();
	
	return 0;
	
}

float UGameplayFuncLib::GetBGMVolume()
{
	if (GetAwGameInstance())
		return GetAwGameInstance()->SFXManager->GetBGMVolume();

	return 0;
}

float UGameplayFuncLib::GetSystemAudioVolume()
{
	if (GetAwGameInstance())
		return GetAwGameInstance()->SFXManager->GetSystemAudioVolume();
	
	return 0;
}

float UGameplayFuncLib::GetSFXVolume()
{
	if (GetAwGameInstance())
		return GetAwGameInstance()->SFXManager->GetSFXVolume();
	
	return 0;
}

float UGameplayFuncLib::GetVoiceVolume()
{
	if (GetAwGameInstance())
		return GetAwGameInstance()->SFXManager->GetVoiceVolume();
	
	return 0;
}

AAWAoe* UGameplayFuncLib::CreateAOEByLauncher(FAOELauncher Launcher,int Level)
{
	if(!GWorld->GetAuthGameMode())
		return nullptr;
	FTransform AOETransform;
	AOETransform.SetLocation(Launcher.Position);
	AOETransform.SetRotation(Launcher.Direction.Rotation().Quaternion());

	FString BpPath = UResourceFuncLib::GetBpAssetPath(Launcher.Model.BpPath);
	UClass* BpClass = LoadClass<AAWAoe>(nullptr, *BpPath);
	AAWAoe* AOEObj;
	if (BpClass)
	{
		AOEObj = GWorld->SpawnActorDeferred<AAWAoe>(BpClass, AOETransform);
	}
	else
	{
		AOEObj = GWorld->SpawnActorDeferred<AAWAoe>(AAWAoe::StaticClass(), AOETransform);
	}
	if (IsValid(AOEObj))
	{
		// 在SpawnActor后立即初始化Caster
		AOEObj->Caster = Launcher.Caster;
		AOEObj->ActionLevel = Level;
		if (Launcher.Caster)
		{
			AOEObj->SetOwner(Launcher.Caster);
			if (GetAwGameState()->AllCharacters.Contains(Launcher.Caster))
			{
				FString LevelName = *GetAwGameState()->AllCharacters.Find(Launcher.Caster);
				GetAwGameState()->AOEList.Add(AOEObj, LevelName);
			}
			else
				GetAwGameState()->AOEList.Add(AOEObj, UAwGameInstance::Instance->CurLevelName);
		}
		else
			GetAwGameState()->AOEList.Add(AOEObj, UAwGameInstance::Instance->CurLevelName);
		AOEObj->SetOriginDirection(Launcher.Direction);
		AOEObj->Init(Launcher.Model, Launcher.Caster, Launcher.CasterProp);
		// AOEObj->AttackHitManager->InitAttackBox();
		AOEObj->TweenFunc = Launcher.TweenFunc;
		AOEObj->AOELifeSpan = Launcher.AOELifeSpan;
		AOEObj->AttackHitManager->AttackBoxType = EAttackBoxType::AOE;
		UGameplayStatics::FinishSpawningActor(AOEObj,AOETransform);
		return AOEObj;
	}

	return nullptr;
}

AAWAoe* UGameplayFuncLib::CreateAOE(AAwCharacter* Caster, FString AOEID, FVector AOEPosition, FVector Direction, float LifeSpan, FString TweenFuncName, int Level)
{
	if (!GWorld->GetAuthGameMode())
		return nullptr;
	FAOELauncher NewLauncher = FAOELauncher();
	if (Caster)
	{
		NewLauncher.Caster = Caster;
		NewLauncher.CasterProp = Caster->CharacterObj.CurProperty;
	}
	NewLauncher.Position = AOEPosition;
	NewLauncher.Direction = Direction;
	NewLauncher.AOELifeSpan = LifeSpan;
	NewLauncher.TweenFunc = UDataFuncLib::SplitFuncNameAndParams(TweenFuncName);
	//Create AOEModel
	NewLauncher.Model = GetDataManager()->GetAoeModelById(AOEID);
	
	return CreateAOEByLauncher(NewLauncher,Level);
}

AAwBullet* UGameplayFuncLib::CreateBullet(FBulletLauncher Launcher,int Level)
{
	if(Launcher.Caster)
	{
		if (Launcher.Caster->GetLocalRole() != ROLE_Authority)
			return nullptr;
	}
	FTransform BulletTransform;
	BulletTransform.SetLocation(Launcher.Position);
	BulletTransform.SetRotation(Launcher.Direction.Rotation().Quaternion());

	FString BpPath = UResourceFuncLib::GetBpAssetPath(Launcher.Model.BpPath);
	UClass* BpClass = LoadClass<AAwBullet>(nullptr, *BpPath);
	AAwBullet* BulletObj;
	if (BpClass)
	{
		BulletObj = GWorld->SpawnActorDeferred<AAwBullet>(BpClass, BulletTransform);
	}
	else
	{
		BulletObj = GWorld->SpawnActorDeferred<AAwBullet>(AAwBullet::StaticClass(), BulletTransform);
	}
	if (IsValid(BulletObj))
	{
		if (Launcher.Caster)
		{
			BulletObj->Caster = Launcher.Caster;
			BulletObj->ActionLevel = Level;
			if (GetAwGameState()->AllCharacters.Contains(Launcher.Caster))
			{
				FString LevelName = *GetAwGameState()->AllCharacters.Find(Launcher.Caster);
				GetAwGameState()->BulletList.Add(BulletObj, LevelName);
			}
			else
				GetAwGameState()->BulletList.Add(BulletObj, UAwGameInstance::Instance->CurLevelName);
		}
		else
			GetAwGameState()->BulletList.Add(BulletObj, UAwGameInstance::Instance->CurLevelName);
		BulletObj->SetOriginTransform(BulletTransform);
		BulletObj->Init(Launcher.Model, Launcher.Caster, Launcher.CasterProp);
		// BulletObj->AttackHitManager->InitAttackBox(); //手动初始化碰撞
		BulletObj->TargetLocaiton = Launcher.TargetLocation;
		BulletObj->TweenFunc = Launcher.TweenFunc;
		BulletObj->BulletLifeSpan = Launcher.Duration;
		BulletObj->AttackHitManager->AttackBoxType = EAttackBoxType::Bullet;
		UGameplayStatics::FinishSpawningActor(BulletObj,BulletTransform);
		return BulletObj;
	}

	return nullptr;
}

// FBulletLauncher* UGameplayFuncLib::CreateBulletLauncher(AAwCharacter* Caster, FString BulletID, FVector Position, FVector Direction, float ActionPower, float Scale, FVector DirectionOffset, int Level, FString TweenFunc)
// {
// 	FBulletLauncher* NewLauncher = NewObject<FBulletLauncher>();
// 	if (Caster)
// 	{
// 		NewLauncher->Caster = Caster;
// 		NewLauncher->CasterProp = Caster->CurProperty;
// 	}
// 	NewLauncher->Position = Position;
// 	NewLauncher->Direction = Direction;
// 	NewLauncher->ActionPower = ActionPower;
// 	NewLauncher->Scale = Scale;
// 	NewLauncher->DirectionOffset = DirectionOffset;
// 	NewLauncher->Level = Level;
// 	NewLauncher->TweenFunc = UDataFuncLib::SplitFuncNameAndParams(TweenFunc);
// 	//Create BulletModel
// 	NewLauncher->Model = NewLauncher->CreateBulletModel(BulletID);
//
// 	return NewLauncher;
// }

AAwSceneItem* UGameplayFuncLib::CreateSceneItem(FSceneItemModel Model, int Side, FTransform Transform)
{
	AAwSceneItem* SceneItem = nullptr;

	FString BpPath = UResourceFuncLib::GetBpAssetPath(Model.BpPath);
	UClass* BpClass = LoadClass<AAwSceneItem>(nullptr, *BpPath);
	if (BpClass)
	{
		FActorSpawnParameters ActorSpawnParameters;
		ActorSpawnParameters.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AlwaysSpawn;
		SceneItem = GWorld->SpawnActor<AAwSceneItem>(BpClass, Transform, ActorSpawnParameters);
		if (IsValid(SceneItem))
		{
			//Init
			SceneItem->Init(Model,Side);
		}
	}
	
	return SceneItem;
}

FVector UGameplayFuncLib::GetPointOnGround(AAwCharacter* Character, int Height)
{
	const TArray<AActor*> ActorsToIgnore;
	FHitResult HitResult;
	
	const FVector Start = Character->GetActorLocation();

	UKismetSystemLibrary::LineTraceSingleByProfile(Character, Start, Start - FVector(0, 0, Height),
												   "Terrain", false, ActorsToIgnore, EDrawDebugTrace::None, HitResult, true);
	
	return HitResult.Location;
}

void UGameplayFuncLib::ShowPopText(AAwCharacter* OnCharacter, FString Text, FLinearColor TextColor)
{
	TArray<AAwPlayerController*> PlayerControllerList;
	GetAwGameInstance()->CharacterInfoList.GetKeys(PlayerControllerList);
	for (int i = 0; i < PlayerControllerList.Num(); i++)
	{
		if(IsValid(PlayerControllerList[i]))
			PlayerControllerList[i]->ShowPopText(OnCharacter, Text, TextColor);
	}
}

void UGameplayFuncLib::ShowPopText(USceneComponent* OnHitBox, FString Text, FLinearColor TextColor)
{
	// TArray<AAwPlayerController*> PlayerControllerList;
	// GetAwGameInstance()->CharacterInfoList.GetKeys(PlayerControllerList);
	// for (int i = 0; i < PlayerControllerList.Num(); i++)
	// {
	// 	if(IsValid(PlayerControllerList[i]))
	// 		PlayerControllerList[i]->ShowPopText(OnHitBox, Text, TextColor);
	// }
}

void UGameplayFuncLib::CharacterPlaySoundOnMouth(AAwCharacter* AwCharacter, FString Path)
{
	if (IsValid(AwCharacter) && IsValid(AwCharacter->MouthPoint))
	{
		USoundBase* SoundBase = UResourceFuncLib::LoadSoundBase(Path);
		if (SoundBase) GetAwGameInstance()->SFXManager->PlaySFXAttached(SoundBase, AwCharacter->MouthPoint);
	}
	else
		UE_LOG(LogTemp, Error, TEXT("Don't find the mouse point!"));
}

void UGameplayFuncLib::CreateATestLootPackage(FTransform Transform)
{
	TArray<FThingObj> ThingObjs;
	FThingObj ThingObj;
	ThingObj.Id = "TestItem";
	ThingObj.Count = 1;
	ThingObj.Type = EThingType::Currency;
	ThingObjs.Add(ThingObj);
	GetLootManager()->CreateLootPackageActor(ThingObjs, Transform);
}

void UGameplayFuncLib::CreateVoiceStimulate(FVector Position, int VoiceRange, AAwCharacter* VoiceCaster)
{
	if(GetAwGameState())
	{
		for (const TTuple<AAwCharacter*, FString> Character : GetAwGameState()->AllCharacters)
		{
			if(Character.Key == nullptr) continue;
			if(!VoiceCaster || VoiceCaster->IsEnemy(Character.Key))
				Character.Key->GetAIComponent()->OnHeardVoice(Position, VoiceRange);
		}
	}
}

void UGameplayFuncLib::SetActorOutline(AActor* TargetActor, bool ShowOutline)
{
	TArray<USceneComponent*> Components;
	TargetActor->GetComponents(Components, true);
	for (USceneComponent* Component : Components)
	{
		UStaticMeshComponent* StaticMesh = Cast<UStaticMeshComponent>(Component);
		if (IsValid(StaticMesh))
			StaticMesh->SetRenderCustomDepth(ShowOutline);
		
		USkeletalMeshComponent* SkeletonMesh = Cast<USkeletalMeshComponent>(Component);
		if (IsValid(SkeletonMesh))
			SkeletonMesh->SetRenderCustomDepth(ShowOutline);
	}
}

bool UGameplayFuncLib::GetHitResultBetweenSceneComponent(USceneComponent* BeHitBox, USceneComponent* AttackBox, FVector& HitLocation, FVector& HitNormal)
{
	if(AttackBox && BeHitBox)
	{
		TArray<AActor*> ActorsToIgnore;
		ActorsToIgnore.Add(AttackBox->GetOwner());
		TArray<FHitResult> HitResults;
		bool bIsHit = false;
		FVector StartLoc = FVector::ZeroVector;
		FVector EndLoc = FVector::ZeroVector;
		//判断AttackBox形状
		if(IsValid(Cast<UCapsuleComponent>(AttackBox)))
		{
			const UCapsuleComponent* AttackCapsuleComponent = Cast<UCapsuleComponent>(AttackBox);
			StartLoc = AttackCapsuleComponent->GetComponentLocation() + AttackCapsuleComponent->GetUpVector() * AttackCapsuleComponent->GetScaledCapsuleHalfHeight();
			EndLoc = AttackCapsuleComponent->GetComponentLocation() + (AttackCapsuleComponent->GetUpVector() * AttackCapsuleComponent->GetScaledCapsuleHalfHeight() * -1);
			bIsHit = UKismetSystemLibrary::SphereTraceMulti(GWorld, StartLoc, EndLoc, AttackCapsuleComponent->GetScaledCapsuleRadius(),
				TraceTypeQuery6, true, ActorsToIgnore, EDrawDebugTrace::None, HitResults, true);
				//,FLinearColor::Red,FLinearColor::Green, 10.0f);
		}
		else if(IsValid(Cast<UStaticMeshComponent>(AttackBox)))
		{
			UStaticMeshComponent* AttackMeshComponent = Cast<UStaticMeshComponent>(AttackBox);
			const UStaticMesh* ShapeMesh = AttackMeshComponent->GetStaticMesh();
			const FString ShapePath = ShapeMesh->GetPathName();
			
			//球形
			if(ShapePath == "/Engine/BasicShapes/Sphere.Sphere" )
			{
				const int Scale = AttackMeshComponent->GetComponentScale().X;
				StartLoc = AttackBox->GetComponentLocation();
				EndLoc = BeHitBox->GetComponentLocation();
				bIsHit = UKismetSystemLibrary::SphereTraceMulti(GWorld,StartLoc,EndLoc,
					Scale * 50,TraceTypeQuery6,true,ActorsToIgnore,EDrawDebugTrace::None, HitResults,true);
			}
			//立方体
			else if(ShapePath == "/Engine/BasicShapes/Cube.Cube" )
			{
				const FVector CubeHalfSize = AttackMeshComponent->GetComponentScale() * 50;
				StartLoc = AttackBox->GetComponentLocation();
				EndLoc = BeHitBox->GetComponentLocation();
				bIsHit = UKismetSystemLibrary::BoxTraceMulti(GWorld,StartLoc,EndLoc,
						CubeHalfSize,AttackMeshComponent->GetComponentRotation(),TraceTypeQuery6,true,ActorsToIgnore,EDrawDebugTrace::None, HitResults,true);
			}
			//圆柱体
			else if(ShapePath == "/Engine/BasicShapes/Cylinder.Cylinder" )
			{
				const FVector CubeHalfSize = AttackMeshComponent->GetComponentScale() * 50;
				StartLoc = AttackBox->GetComponentLocation();
				EndLoc = BeHitBox->GetComponentLocation();
				bIsHit = UKismetSystemLibrary::BoxTraceMulti(GWorld,StartLoc,EndLoc,
						CubeHalfSize,AttackMeshComponent->GetComponentRotation(),TraceTypeQuery6,true,ActorsToIgnore,EDrawDebugTrace::None, HitResults,true);
			}
			//圆锥形
			else if(ShapePath == "/Engine/BasicShapes/Cone.Cone" )
			{
				//TODO
			}
			//UKismetSystemLibrary::PrintString(GWorld, ShapePath);
		}
		if(HitResults.Num())
		{
			float MinDisToBeHitBox = 0.0f;
			for(FHitResult Result : HitResults)
			{
				if(Result.GetActor() == BeHitBox->GetOwner())
				{
					const float CurDis = FVector::Dist(BeHitBox->GetComponentLocation(), Result.ImpactPoint);
					if(MinDisToBeHitBox == 0.0f || CurDis < MinDisToBeHitBox)
					{
						HitLocation = Result.ImpactPoint;
						HitNormal = Result.ImpactNormal;
						MinDisToBeHitBox = CurDis;
					}
				}
			}
			return true;
		}
	}
	return false;
}

void UGameplayFuncLib::UnifiedCameraFadeOut()
{
	for (auto pc : GetAllLocalAwPlayerControllers())
	{
		if(pc->PlayerCameraManager)
		{
			pc->PlayerCameraManager->StartCameraFade(0,1,0.2,FLinearColor::Black,false,true);
		}
	}
}

void UGameplayFuncLib::UnifiedCameraFadeIn()
{
	for (auto pc : GetAllLocalAwPlayerControllers())
	{
		if(pc->PlayerCameraManager)
		{
			pc->PlayerCameraManager->StartCameraFade(1,0,0.2,FLinearColor::Black,false,true);
		}
	}
}

AAwCharacter* UGameplayFuncLib::GetClosestPlayerCharacter(FVector Location)
{
	AAwCharacter* NearestPlayer = GetLocalAwPlayerCharacter(0);
	float DistMin = -1;
	for (auto pc : UGameplayFuncLib::GetAllLocalAwPlayerControllers())
	{
		if (!pc || !pc->CurCharacter)continue;
		float Dist = FVector::Dist(pc->CurCharacter->GetActorLocation(), Location);
		if (Dist < DistMin || Dist < 0.f)
			NearestPlayer = pc->CurCharacter;
	}
	return NearestPlayer;
}

bool UGameplayFuncLib::HavePlayerCharacterInRange(FVector Location,float RadiusMin,float RadiusMax)
{
	bool IsInRadius = false;
	for (auto pc : UGameplayFuncLib::GetAllLocalAwPlayerControllers())
	{
		if (!pc)continue;
		AAwCharacter* Chara = pc->CurCharacter;
		if (!Chara)continue;
		float Dist = FVector::Dist(Chara->GetActorLocation(), Location);
		if (RadiusMax<0)
		{
			if(Dist <= RadiusMin)
			{
				IsInRadius = true;
				break;
			}
		}
		else
		{
			if(Dist >= RadiusMin && Dist <= RadiusMax)
			{
				IsInRadius = true;
				break;
			}
		}
	}
	return IsInRadius;
}

TArray<AAwPlayerController*> UGameplayFuncLib::SetAllPlayerGameControlState(EGameControlState newState)
{
	auto pcs = GetAllLocalAwPlayerControllers();
	for ( auto pc : pcs )
		pc->GameControlState = newState;
	return pcs;
}

//void UGameplayFuncLib::SetAllPlayerWidgetInputState(AAwPlayerController Target,EGameControlState newState, bool FlashInput, bool ShowCursor)
//{
//	
//}
//DECLARE_FUNCTION(execSetAllPlayerWidgetInputState)
//{
//    P_GET_OBJECT(AAwPlayerController, Target);
//    P_GET_ENUM(EGameControlState, newState);
//    P_GET_UBOOL(FlashInput);
//    P_GET_UBOOL(ShowCursor);
//    P_FINISH;
//
//    if (!Target) return;
//	UEdGraphPin* StackPin = Stack.StepProperty();
//
//    // Replace the problematic line with the following code
//    FProperty* StepProperty = Stack.Node->FindPropertyByName(FName("StepProperty"));
//    UEdGraphPin* StackPin = StepProperty ? Cast<UEdGraphPin>(StepProperty->ContainerPtrToValuePtr<void>(Stack.Locals)) : nullptr;
//    // Assuming CustomHealth, CustomSpeed, and DisplayName are properties of ACharacter
//	if (StackPin && StackPin->PinName == "FlashInput" && StackPin->bWasEverConnected)
//	{
//		UWidgetBlueprintLibrary::SetInputMode_GameOnly(Target, FlashInput);
//	}
//	Target->SetShowMouseCursor(ShowCursor);
//	Target->GameControlState = newState;
//}

TArray<AAwPlayerController*> UGameplayFuncLib::SetAllPlayerUIControlState(EGameUIControlState newState)
{
	auto pcs = GetAllLocalAwPlayerControllers();
	for ( auto pc : pcs )
		pc->GameUIControlState = newState;
	return pcs;
}

AAwPlayerState* UGameplayFuncLib::K2GetPlayerState(EAwResultExecPin& Branches,UActorComponent* Comp)
{
	AAwPlayerState* PlayerState = GetPlayerState(Comp);
	Branches = PlayerState==nullptr? EAwResultExecPin::Failure : EAwResultExecPin::Success;
	return PlayerState;
}
AAwPlayerState* UGameplayFuncLib::GetPlayerState(const UActorComponent* Comp)
{
	ACharacter* chara = Cast<ACharacter>(Comp->GetOwner());
	if(chara == nullptr)
	{
		return nullptr;
	}
	AAwPlayerState* playerState = Cast<AAwPlayerState>(chara->GetPlayerState());
	if(playerState == nullptr)
	{
		return nullptr;
	}
	return playerState;
}

AAwPlayerController* UGameplayFuncLib::K2GetPlayerController(EAwResultExecPin& Branches, UActorComponent* Comp)
{
	AAwPlayerController* pc = GetPlayerControllerByComp(Comp);
	Branches = pc==nullptr? EAwResultExecPin::Failure : EAwResultExecPin::Success;
	return pc;
}

AAwPlayerController* UGameplayFuncLib::GetPlayerControllerByComp(const UActorComponent* Comp,bool Insure)
{
	if (Comp == nullptr)return nullptr;
	AAwCharacter* chara = Cast<AAwCharacter>(Comp->GetOwner());
	if(chara == nullptr)
	{
		if (Insure)
			return GetLocalAwPlayerController(0);
		else
			return nullptr;
	}
	AAwPlayerController* pc = chara->OwnerPlayerController;
	if(pc == nullptr && Insure)
	{
		return GetLocalAwPlayerController(0);
	}
	return pc;
}

AAwPlayerController* UGameplayFuncLib::K2GetPlayerControllerByWidget(EAwResultExecPin& Branches,
	UUserWidget* Widget)
{
	AAwPlayerController* pc = GetPlayerControllerByWidget(Widget);
	Branches = pc==nullptr? EAwResultExecPin::Failure : EAwResultExecPin::Success;
	return pc;
}

AAwPlayerController* UGameplayFuncLib::GetPlayerControllerByWidget(const UUserWidget* Widget)
{
	ACharacter* chara = Cast<ACharacter>(Widget->GetOwningPlayer());
	if(chara == nullptr)
	{
		return nullptr;
	}
	AAwPlayerController* pc = Cast<AAwPlayerController>(chara->GetController());
	if(pc == nullptr)
	{
		return nullptr;
	}
	return pc;
}

FVector UGameplayFuncLib::GetPointOnGround(FVector Location, float Radius)
{
	const TArray<AActor*> ActorsToIgnore;
	FHitResult HitResult;

	const FVector Start = Location;
	const FVector End = Start + FVector::DownVector * (10000);

	TArray<TEnumAsByte<EObjectTypeQuery>> ObjectList;
	ObjectList.Add(ObjectTypeQuery1);
	const bool bIsHit = UKismetSystemLibrary::SphereTraceSingleForObjects(
		nullptr, Start, End, Radius,
		ObjectList, false, ActorsToIgnore, EDrawDebugTrace::None, HitResult, true);

	if (bIsHit)
		return HitResult.ImpactPoint;
	return Location;
}

void UGameplayFuncLib::PauseGameActors(TArray<AAwCharacter*> ExceptFor)
{
	for (const TTuple<AAwCharacter*, FString> Character : GetAwGameState()->AllCharacters)
	{
		if (!Character.Key || ExceptFor.Contains(Character.Key)  ) continue;
		Character.Key->Pause();
	}
	for (const TTuple<AAWAoe*, FString> Aoe : GetAwGameState()->AOEList)
	{
		if (Aoe.Key)
			Aoe.Key->Pause(true);
	}
	for (const TTuple<AAwBullet*, FString> Bullet : GetAwGameState()->BulletList)
	{
		if (Bullet.Key)
			Bullet.Key->Pause();
	}
	for (const TTuple<FString, AAwSceneItem*> SceneItem : GetAwGameState()->SceneItemList)
	{
		if (SceneItem.Value)
			SceneItem.Value->Pause();
	}
}

void UGameplayFuncLib::ResumeGameActors()
{
	for (const TTuple<AAwCharacter*, FString> Character : GetAwGameState()->AllCharacters)
	{
		if (Character.Key)
			Character.Key->Resume();
	}
	for (const TTuple<AAWAoe*, FString> Aoe : GetAwGameState()->AOEList)
	{
		if (Aoe.Key)
			Aoe.Key->Resume();
	}
	for (const TTuple<AAwBullet*, FString> Bullet : GetAwGameState()->BulletList)
	{
		if (Bullet.Key)
			Bullet.Key->Resume();
	}
	for (const TTuple<FString, AAwSceneItem*> SceneItem : GetAwGameState()->SceneItemList)
	{
		if (SceneItem.Value)
			SceneItem.Value->Resume();
	}
}

void UGameplayFuncLib::PauseGameForMenu()
{
	AAwCharacter* MyGuy = GetAwGameState()->GetMyCharacter();
	PauseGameActors(TArray<AAwCharacter*>());
	
	//————测试代码（可删）↓
	
	PlayUIAudio("OptionMenu_Open");
	
	//————测试代码（可删）↑

	UGameMain* MainUI = nullptr;
	UGameMenu* GameMenu = nullptr;
	if (GetUiManager()->OpenedWidgets.Contains("GameMain"))
		MainUI = Cast<UGameMain>(GetUiManager()->OpenedWidgets["GameMain"]);
	if (MainUI) MainUI->HiddeMainUI("InHide");

	if(!GetUiManager()->OpenedWidgets.Contains("Menu"))
		GameMenu = Cast<UGameMenu>(GetUiManager()->Show("Menu",9999));

	if(GetUiManager()->OpenedWidgets.Contains("Subtitle"))
	{
		USubtitle* Subtitle = Cast<USubtitle>(GetUiManager()->OpenedWidgets["Subtitle"]);
		Subtitle->PasueSubtitle();
	}
	
	if(GameMenu)
	{
		FListItemElementInfo TemItemElementInfo = GetAwDataManager()->GetListItemsById("Menu");
		
		for (int i = 0;i < TemItemElementInfo.Items.Num();++i)
		{
			TemItemElementInfo.Items[i].Name = GetAwDataManager()->GetTextByKey(TemItemElementInfo.Items[i].Id);
		}
		
		GameMenu->GetMenuList()->SetEntry(TemItemElementInfo);
	}
	
	SetAllPlayerGameControlState(EGameControlState::PauseMenu);
}

void UGameplayFuncLib::PauseGameForDialog(AAwCharacter* TalkTo,const int PlayerIndex)
{
	if (!TalkTo || TalkTo->NpcInfo.IsNpc() == false || GetLocalAwPlayerController(PlayerIndex)->GameControlState == EGameControlState::Dialog) return;
	FString DialogModelId = TalkTo->NpcInfo.Personality.DialogModelId;
	if (TalkTo->NpcInfo.Personality.DialogAutoPicker.IsEmpty() == false)
	{
		const FJsonFuncData JsonFunc = UCallFuncLib::StringToJsonFuncData(TalkTo->NpcInfo.Personality.DialogAutoPicker);
		UFunction* Func = UCallFuncLib::JsonFuncToUFunc(JsonFunc);
		if (Func)
		{
			struct
			{
				FNpcInfo NpcInfo;
				TArray<FString> Params;
				FString Result;
			}FuncParam;
			FuncParam.NpcInfo = TalkTo->NpcInfo;
			FuncParam.Params = JsonFunc.Params;
			TalkTo->ProcessEvent(Func, &FuncParam);
			DialogModelId = TalkTo->NpcInfo.Personality.DialogModelId = FuncParam.Result;
		}
		
	}
	const FDialogScriptModel DialogModel = GetAwDataManager()->GetDialogById(DialogModelId);
	if (DialogModel.IsValidDialog() == false) return;
	if(!DialogModel.Scripts.Contains(DialogModel.FirstClipId)) return;

	FDialogScriptObj DialogObj = FDialogScriptObj();
	DialogObj.ModelId = DialogModel.Id;
	DialogObj.DialogClip = DialogModel.Scripts[DialogModel.FirstClipId];
	DialogObj.DialogTarget = TalkTo;
	DialogObj.DialogTrigger = UGameplayFuncLib::GetLocalAwPlayerCharacter(PlayerIndex);
	DialogObj.PlayingEventId = DialogObj.DialogClip.FirstEventId;

	TArray<AAwCharacter*> PerformActors;
	if (TalkTo)
	{
		PerformActors.Add(TalkTo);
		TalkTo->OnStartDialog();
	} 
	if (GetAwGameState()->GetMyCharacter()) PerformActors.Add(GetAwGameState()->GetMyCharacter());
	//TODO 玩家的小弟还没加呢
	for (TTuple<AAwCharacter*, FString> Cha : GetAwGameState()->AllCharacters)
	{
		if (!Cha.Key) continue;
		for (const TTuple<int, FString>NpcId : DialogModel.JoinNpcId)
		{
			if (Cha.Key->NpcInfo.IsNpc() && Cha.Key->NpcInfo.Id == NpcId.Value)
			{
				constexpr float DistanceForTalk = 1000;
				if ((Cha.Key->GetActorLocation() - GetAwGameState()->GetMyCharacter()->GetActorLocation()).Size() > DistanceForTalk) continue;
				DialogObj.AttendNpc.Add(NpcId.Key, Cha.Key);
				PerformActors.Add(Cha.Key);
			}
		}
		if (Cha.Key) Cha.Key->PauseAI(true);
	}
	
	PauseGameActors(PerformActors);
	ADialogModeActor* DialogActor = CreateDialogModeActor();

	UAwUIManager* UIManager = GetUiManager();
	if(!IsRogueMode())
	{
		UGameMain* MainUI =  (UIManager->OpenedWidgets.Contains("GameMain")) ? Cast<UGameMain>(UIManager->OpenedWidgets["GameMain"]) : nullptr;
		if (MainUI) MainUI->HiddeMainUI("InHide");
		DialogActor->UI = Cast<UDialogUI>(UIManager->Show("DialogUI"));
	}
	else
	{
		URogueFightingMainUI* FightingMain;
		if(UIManager->OpenedWidgets.Contains("RogueFighting_Main"))
		{
			FightingMain = Cast<URogueFightingMainUI>(UIManager->OpenedWidgets["RogueFighting_Main"]);
			if(FightingMain)
				FightingMain->HiddeMainUI();
		}
		DialogActor->UI = Cast<UDialogUI>(UIManager->Show("RogueDialogUI"));
	}
	
	DialogActor->StartDialog(DialogObj);
	// GetLocalAwPlayerController(PlayerIndex)->GameControlState = EGameControlState::Dialog;
	SetAllPlayerGameControlState(EGameControlState::Dialog);
	// GetMyAwPlayerController()->SetViewTargetWithBlend(DialogActor, 0.01f);
	// GetMyAwPlayerController()->CameraPawn->SetViewTarget(DialogActor);

	if(GetUiManager()->OpenedWidgets.Contains("Subtitle"))
	{
		USubtitle* Subtitle = Cast<USubtitle>(GetUiManager()->OpenedWidgets["Subtitle"]);
		Subtitle->PasueSubtitle();
	}
	
	
	
}

void UGameplayFuncLib::ResumeGameForDialog()
{
	ResumeGameActors();

	UDialogUI* DialogUI = nullptr;

	if(!IsRogueMode())
	{
		if(GetUiManager()->OpenedWidgets.Contains("DialogUI"))
		{
			DialogUI = Cast<UDialogUI>(GetUiManager()->OpenedWidgets["DialogUI"]);
			DialogUI->PlayHideDialogAnim();
		}
		//GetUiManager()->Hide("DialogUI");

		if(GetUiManager()->OpenedWidgets.Contains("Subtitle"))
		{
			USubtitle* Subtitle = Cast<USubtitle>(GetUiManager()->OpenedWidgets["Subtitle"]);
			Subtitle->ResumeSubtitle();
		}
		UGameMain* MainUI = nullptr;
		if(GetUiManager()->OpenedWidgets.Contains("GameMain"))
		{
			MainUI = Cast<UGameMain>(GetUiManager()->OpenedWidgets["GameMain"]);
		}
		else
		{
			MainUI = Cast<UGameMain>(GetUiManager()->Show("GameMain"));
		}
	
		if (MainUI) MainUI->ShowMainUI("InHide");
	}
	else
	{
		if(GetUiManager()->OpenedWidgets.Contains("RogueDialogUI"))
		{
			DialogUI = Cast<UDialogUI>(GetUiManager()->OpenedWidgets["RogueDialogUI"]);
			DialogUI->PlayHideDialogAnim();
		}

		if(GetUiManager()->OpenedWidgets.Contains("Subtitle"))
		{
			USubtitle* Subtitle = Cast<USubtitle>(GetUiManager()->OpenedWidgets["Subtitle"]);
			Subtitle->ResumeSubtitle();
		}
		URogueFightingMainUI* FightingMain;
		if(GetUiManager()->OpenedWidgets.Contains("RogueFighting_Main"))
		{
			FightingMain = Cast<URogueFightingMainUI>(GetUiManager()->OpenedWidgets["RogueFighting_Main"]);
			if(FightingMain)
				FightingMain->ShowMainUI();
		}
		else
		{
			FightingMain = Cast<URogueFightingMainUI>(GetUiManager()->Show("RogueFighting_Main"));
			if(FightingMain)
				FightingMain->ShowMainUI();
		}
	}
	
	auto pcs = SetAllPlayerGameControlState(EGameControlState::Game);
	for (auto pc :pcs)
	{
		if (pc && pc->CurCharacter)
		{
			pc->GiveBackCamera(0);
		}
	}
	for (const TTuple<AAwCharacter*, FString> Cha : GetAwGameState()->AllCharacters)
	{
		if (Cha.Key) Cha.Key->PauseAI(false);
	}
}

void UGameplayFuncLib::ResumeGameForMenu()
{
	const AAwCharacter* MyGuy = GetAwGameState()->GetMyCharacter();
	ResumeGameActors();
	
	PlayUIAudio("OptionMenu_Close");
	
	if(GetUiManager()->OpenedWidgets.Contains("Subtitle"))
	{
		USubtitle* Subtitle = Cast<USubtitle>(GetUiManager()->OpenedWidgets["Subtitle"]);
		Subtitle->ResumeSubtitle();
	}
	
	UGameMain* MainUI = nullptr;
	if (GetUiManager()->OpenedWidgets.Contains("GameMain"))
	{
		MainUI = Cast<UGameMain>(GetUiManager()->OpenedWidgets["GameMain"]);
		if (MainUI) MainUI->ShowMainUI("InHide");
	}
	
	if(GetUiManager()->OpenedWidgets.Contains("Menu"))
	{
		UGameMenu* GameMenu = Cast<UGameMenu>(GetUiManager()->OpenedWidgets["Menu"]);
		GameMenu->HideMenu();
	}
	
	if(GetUiManager()->OpenedWidgets.Contains("Equipment"))
	{
		UChangeEquipment* Equipment = Cast<UChangeEquipment>(GetUiManager()->OpenedWidgets["Equipment"]);
		Equipment->SetIsSelfCloss(true);
		Equipment->Back();
	}

	if(GetUiManager()->OpenedWidgets.Contains("ItemShortcutBar"))
	{
		UItemShortcutBarUI* ItemShortcutBarUI = Cast<UItemShortcutBarUI>(GetUiManager()->OpenedWidgets["ItemShortcutBar"]);
		ItemShortcutBarUI->SetIsSelfCloss(true);
		ItemShortcutBarUI->Back();
	}

	if(GetUiManager()->OpenedWidgets.Contains("PowerMap"))
	{
		UPowerMap* PowerMap = Cast<UPowerMap>(GetUiManager()->OpenedWidgets["PowerMap"]);
		PowerMap->SetIsReversePlay(true);
		PowerMap->SetIsSelfCloss(true);
		PowerMap->Back();
	}

	if(GetUiManager()->OpenedWidgets.Contains("SystemSettings"))
	{
		USystemSettingsUI* SystemSettingsUI = Cast<USystemSettingsUI>(GetUiManager()->OpenedWidgets["SystemSettings"]);
		SystemSettingsUI->SetIsReversePlay(true);
		if(SystemSettingsUI->GetParentUIName() == "Title")
			SystemSettingsUI->SetIsSelfCloss(false);
		else
			SystemSettingsUI->SetIsSelfCloss(true);
		SystemSettingsUI->Back();
	}

	if(GetUiManager()->OpenedWidgets.Contains("NewbieRetrospect"))
	{
		UNewbieRetrospect* NewbieRetrospect = Cast<UNewbieRetrospect>(GetUiManager()->OpenedWidgets["NewbieRetrospect"]);
		NewbieRetrospect->SetIsReversePlay(true);
		NewbieRetrospect->SetIsSelfCloss(true);
		NewbieRetrospect->Back();
	}

	if(GetUiManager()->OpenedWidgets.Contains("BigMap"))
	{
		UBigMap* BigMap = Cast<UBigMap>(GetUiManager()->OpenedWidgets["BigMap"]);
		BigMap->SetIsSelfCloss(true);
		BigMap->Back();
	}
	
	auto pcs = SetAllPlayerGameControlState(EGameControlState::Game);
	for (auto pc :pcs)
	{
		if (pc && pc->CurCharacter)
		{
			pc->GiveBackCamera(0);
		}
	}
}

TArray<AActor*> UGameplayFuncLib::GetTracedActorsFromScreenCenter(UCameraComponent* Cam, float LineDistance, float HalfHeight, float Radius)
{
	if (!Cam) return TArray<AActor*>();
	const FVector CameraLoc = Cam->GetOwner()->GetActorLocation();
	const FRotator CameraRot = Cam->GetOwner()->GetActorRotation();
	const FVector TraceStart = CameraLoc - CameraRot.Vector() * 20000;
	const FVector TraceEnd = CameraLoc + CameraRot.Vector() * LineDistance;
	UKismetSystemLibrary::PrintString(Cam->GetWorld(),
		FString("Owner :").Append(Cam->GetOwner()->GetActorLocation().ToString()).Append("||").Append(Cam->GetOwner()->GetActorRotation().ToString()));
	TArray<AActor*> ActorsToIgnore;
	TArray<FHitResult> HitResults;
	TArray<TEnumAsByte<EObjectTypeQuery>> TraceList;
	TraceList.Add(EObjectTypeQuery::ObjectTypeQuery10);
	TraceList.Add(EObjectTypeQuery::ObjectTypeQuery15);
	UKismetSystemLibrary::BoxTraceMultiForObjects(Cam->GetWorld(), TraceStart, TraceEnd, FVector(50.f, 5000.f, 5000.f),
		CameraRot,  TraceList, false, ActorsToIgnore, EDrawDebugTrace::None, HitResults, true, 
		FLinearColor::Red, FLinearColor::Blue, 60);
	// UKismetSystemLibrary::CapsuleTraceMultiForObjects(Cam->GetWorld(), TraceStart,TraceEnd,Radius, HalfHeight, TraceList, false,
	// 	ActorsToIgnore,EDrawDebugTrace::ForDuration,HitResults,true,
	// 	FLinearColor::Red,FLinearColor::Green, 20);
	TArray<AActor*> TracedActors;
	for(auto HitResult : HitResults)
	{
		if(HitResult.GetActor())
			TracedActors.Add(HitResult.GetActor());
	}
	return TracedActors;
}

int UGameplayFuncLib::GetRoleSwitchValue(FString SwitchKey, bool& HasKey)
{
	const int Value = GetAwGameInstance()->RoleInfo.GetSwitch(SwitchKey);
	HasKey = true;
	return Value;
}

void UGameplayFuncLib::SetRoleSwitchValue(FString SwitchKey, int SwitchValue)
{
		GetAwGameInstance()->RoleInfo.SetSwitchValue(SwitchKey, SwitchValue);
}

int UGameplayFuncLib::GetSwitchValueInGameState(FString SwitchKey, bool& HasKey)
{
	int Value = 0;
	HasKey = false;
	if(GetAwGameState()->SwitchList.Contains(SwitchKey))
	{
		HasKey = true;
		Value = GetAwGameState()->SwitchList[SwitchKey];
	}
	return Value;
}

void UGameplayFuncLib::SetSwitchValueInGameState(FString SwitchKey, int SwitchValue)
{
	GetAwGameState()->SwitchList.Add(SwitchKey,SwitchValue);
}

void UGameplayFuncLib::StartAllTargetSignFunction()
{
	TArray<AActor*> GetActors;
	UGameplayStatics::GetAllActorsOfClass(UAwGameInstance::Instance->GetWorld(),AGameDestSign::StaticClass(),GetActors);
	for (AActor* Actor : GetActors)
	{
		AGameDestSign* TargetSign = Cast<AGameDestSign>(Actor);
		if(TargetSign)
			TargetSign->StartFunction();
	}
}

FAwActionSkillInfo* UGameplayFuncLib::GetPlayerCurAwkeSkill(AAwPlayerController* pc)
{
	if (pc->GetPlayerState<AAwPlayerState>())
	{
		return  pc->GetPlayerState<AAwPlayerState>()->CurAwakeSkill;
	}
	return  nullptr;
}

FAwActionSkillInfo UGameplayFuncLib::K2Node_GetPlayerCurAwkeSkill(AAwPlayerController* pc)
{
	FAwActionSkillInfo Result = FAwActionSkillInfo();
	if (pc->GetPlayerState<AAwPlayerState>())
	{
		Result = pc->GetPlayerState<AAwPlayerState>()->K2_GetCurAwakeSkill();
		return Result;
	}
	return  Result;
}

void UGameplayFuncLib::SetPlayerCurAwakeSkill(AAwPlayerController* pc, FString NewAwakeSkillId)
{
	if (pc->GetPlayerState<AAwPlayerState>())
	{
		pc->GetPlayerState<AAwPlayerState>()->SetCurAwakeSkill(NewAwakeSkillId);
	}
}

void UGameplayFuncLib::UnlockPlayerAwakeSkill(AAwPlayerController* pc, FString AwakeSkillId)
{
	if (pc->GetPlayerState<AAwPlayerState>())
	{
		pc->GetPlayerState<AAwPlayerState>()->UnlockPlayerAwakeSkill(AwakeSkillId);
	}
}

void UGameplayFuncLib::ClearPlayerAwakeSkill(AAwPlayerController* pc)
{
	if (pc->GetPlayerState<AAwPlayerState>())
	{
		pc->GetPlayerState<AAwPlayerState>()->ClearPlayerCurAwakeSkill();
	}
}

void UGameplayFuncLib::RogueGamePaused()
{
	if(GetUiManager())
	{
		GetUiManager()->Show("RogueGamePaused");
	}
}

void UGameplayFuncLib::RogueGameResume()
{
	if(GetUiManager())
	{
		GetUiManager()->Hide("RogueGamePaused");
	}
}

UTexture2D* UGameplayFuncLib::GetTextureByPath(FString Path)
{
	UTexture2D* Temp = LoadObject<UTexture2D>(nullptr,*UResourceFuncLib::GetAssetPath(Path));
	if(IsValid(Temp))
		return Temp;
	return nullptr;
}

UTexture2D* UGameplayFuncLib::GetItemIconTextureById(FString Id)
{
	const FItemIcon ItemIcon = GetDataManager()->GetItemIconById(Id);
	return GetTextureByPath(ItemIcon.Path);
}

FRogueCardInfo_BattleStyleUpgrade UGameplayFuncLib::GetRogueCardInfo_BattleStyleUpgrade(FString UpgradeId)
{
	FRogueCardInfo_BattleStyleUpgrade Res;

	const UAwRogueDataSystem* DataSystem = GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	URogueBattleStyleSubSystem* BattleStyleSubSystem = GetAwGameInstance()->GetSubsystem<URogueBattleStyleSubSystem>();
	if (!DataSystem) return  Res;
	if (!BattleStyleSubSystem) return Res;
	
	FRogueBattleStyleUpgrade Info = GetDataManager()->GetRogueBattleStyleUpgrade(
		DataSystem->GetBattleStyle(DataSystem->GetCurPawnClassId(998)), UpgradeId);

	Res.Id = Info.Id;
	if (Info.UpgradeType == 0)
		Res.Name = GetDataManager()->GetTextByKey("ObtainAction");
	else if (Info.UpgradeType == 1)
		Res.Name = GetDataManager()->GetTextByKey("StrengthenAction");
	else
		Res.Name = GetDataManager()->GetTextByKey("MasterAction");
	
	Res.Desc = Info.Desc;

	FRogueBattleStyleAbilityInfo Ability;
	for (int i = 0; i < 5; ++i)
	{
		ERogueAbilitySlot AbilityType = ERogueAbilitySlot::NormalAttack;
		if (i == 0) AbilityType = ERogueAbilitySlot::NormalAttack;
		if (i == 1) AbilityType = ERogueAbilitySlot::Ground1;
		if (i == 2) AbilityType = ERogueAbilitySlot::Ground2;
		if (i == 3) AbilityType = ERogueAbilitySlot::Air1;
		if (i == 4) AbilityType = ERogueAbilitySlot::Air2;

		FRogueBattleStyleAbilityInfo TempAbility = BattleStyleSubSystem->GetCurAbilityInfo(AbilityType);
		if (TempAbility.Model.UpgradeIds.Contains(UpgradeId))
		{
			Ability = TempAbility;
			break;
		}
	}

	if (Ability.Model.CoreTag.Num() >= 2)
	{
		TArray<FString> UpgradeIds = BattleStyleSubSystem->GetCurrentUpgradeIds();
		UpgradeIds.Add(UpgradeId);
		int Index = FRogueBattleStyleAbility::GetShowIndex(Ability.Model.IndexIds, UpgradeIds);
		Res.CoreTag = Ability.Model.CoreTag[FMath::Min(Ability.Model.CoreTag.Num()-1, Index)];
	}
	else
		Res.CoreTag = Ability.CoreTag;

	if (UpgradeId.Contains("Unlock"))
		Res.UpgradeLevel = 0;
	else
		Res.UpgradeLevel = Ability.UpgradeCount + 1;
	
	Res.SkillCardType = Info.UpgradeType;
	
	return Res;
}

FRogueCardInfo_BattleStyleUpgrade UGameplayFuncLib::GetRogueCardInfo_BattleUpgrade(AAwPlayerController* pc,FString UpgradeId)
{
	FRogueCardInfo_BattleStyleUpgrade Res;
	const UAwRogueDataSystem* DataSystem = GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	UAwDataManager* DataManager = GetDataManager();

	const FString CurPawnClassId = pc->CurCharacter->PlayerClassId;
	FRogueBattleUpgradeInfo Info = DataManager->GetRogueBattleUpgradeInfo(CurPawnClassId, UpgradeId);
	
	Res.Id = UpgradeId;
	Res.Desc = Info.Desc;
	Res.IconPath = Info.Icon;
	Res.CoreTag = ERogueBattleTag::None;
	Res.bOnlyforCareer = Info.bOnlyforCareer;
	switch (Info.UpgradeType)
	{
	case EUpgradeType::ObtainAction:
		Res.UpgradeLevel = 0;
		Res.SkillCardType = 0;
		Res.Name = "ObtainAction";
		break;
	case EUpgradeType::StrengthenAction:
		Res.UpgradeLevel = 1;
		Res.SkillCardType = 1;
		Res.Name = "StrengthenAction";
		break;
	case EUpgradeType::MasterAction:
		Res.UpgradeLevel = 2;
		Res.SkillCardType = 2;
		Res.Name = "MasterAction";
		break;
	default: ;
	}
	
	return Res;
}

FRogueCardInfo_Relic UGameplayFuncLib::GetRogueCardInfo_Relic(FString RelicId, int Level)
{
	FRogueCardInfo_Relic Res;

	UAwDataManager* DataManger = GetDataManager();
	if(RelicId.Len() <= 0) return Res;
	FAwRelicInfo Info;
	if(DataManger->GetAllMetaRelics().Contains(RelicId))
		Info = DataManger->GetAllMetaRelics()[RelicId];
	else
	{
		return Res;
	}
	
	Res.Id = RelicId;
	Res.Name = DataManger->GetTextByKey(Info.Id);
	Res.RelicType = Info.RelicType;
	Res.RecordId = FCString::Atoi(*Info.RecordId);
	Res.Desc = DataManger->GetTextByKey(Info.Desc);;
	Res.Rarity = Info.RelicRarity;
	Res.Level = Info.RelicLevel;
	Res.ElementPathId = Info.IconPath.Num() > 0 ? Info.IconPath[0] : "";
	Res.IconPathId = Info.IconPath.Num() == 2 ? FString("Relic_").Append(Info.IconPath[1]) : "";
	if (Info.IconPath.Num() == 3)
	{
		Res.IconPathId = (Info.IconPath[2]);
	}
	

	
	UAwRelicSubSystem* RelicSubSystem = GetAwGameInstance()->GetSubsystem<UAwRelicSubSystem>();
	if (RelicSubSystem->RelicHasGot.Contains(RelicId))
		Res.GotCount = RelicSubSystem->RelicHasGot[RelicId];
	else
		Res.GotCount = 0;
	
	return Res;
}

FRogueCardInfo_MagicItem UGameplayFuncLib::GetRogueCardInfo_MagicItem(FString ItemId, int Level)
{
	FRogueCardInfo_MagicItem Res;

	UAwDataManager* DataManger = GetDataManager();
	FAwRogueItemInfo Info = DataManger->GetMetaRogueItem(ItemId);
	
	Res.Id = ItemId;
	FString NameKey = ItemId.Append("_0").Append(FString::FromInt(Level));
	Res.Name = DataManger->GetTextByKey(NameKey);
	if (Info.Tags.Contains("Fire"))
		Res.Elemental = EChaElemental::Fire;
	else if (Info.Tags.Contains("Ice"))
		Res.Elemental = EChaElemental::Ice;
	else if (Info.Tags.Contains("Wind"))
		Res.Elemental = EChaElemental::Wind;
	else if (Info.Tags.Contains("Light"))
		Res.Elemental = EChaElemental::Light;
	else if (Info.Tags.Contains("Darkness"))
		Res.Elemental = EChaElemental::Darkness;
	else if (Info.Tags.Contains("Thunder"))
		Res.Elemental = EChaElemental::Thunder;
	else
		Res.Elemental = EChaElemental::Physical;
	
	Res.IconPathId = Info.Item.Model.Id;
	
	UAwRogueItemSubSystem* RogueItemSubSystem = GetAwGameInstance()->GetSubsystem<UAwRogueItemSubSystem>();
	// if (!RogueItemSubSystem)
	// 	Res.MagicItemLevel = 1;
	// else
	// {
	// 	const FAwRogueItemInfo CurItem = RogueItemSubSystem->GetCurRogueItem(true);
	// 	const int CurItemLevel = FMath::Clamp(CurItem.CurEffectLevel, 1, CurItem.MaxEffectLevel);
	// 	Res.MagicItemLevel = CurItemLevel;
	// }
	Res.MagicItemLevel = Level;
	
	Res.Desc = DataManger->GetTextByKey(Info.Item.Model.Id + "_" +
		FString::FromInt(Res.MagicItemLevel) + "_Desc");
	
	return Res;
}

FRogueCardInfo_Action UGameplayFuncLib::GetRogueCardInfo_Action(FRougeAbilityLevelInfo ActionInfo,int playerIndex)
{
	FRogueCardInfo_Action Res;

	UAwDataManager* DataManger = GetDataManager();
	UAwRogueDataSystem* DataSystem = GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>();

	FRogueBattleAbilityInfo AbilityInfo = DataManger->GetRogueBattleAbilityInfo(DataSystem->GetCurPawnClassId(playerIndex), ActionInfo.AbilityInfoId);
	
	// if(ActionId.Len() <= 0) return Res;
	// const FAwRelicInfo Info = DataManger->GetAllMetaRelics()[ActionId];
	//
	// Res.Id = ActionId;
	// Res.Name = DataManger->GetTextByKey(Info.Id);
	// Res.RelicType = Info.RelicType;
	// Res.RecordId = FCString::Atoi(*Info.RecordId);
	// Res.Desc = DataManger->GetTextByKey(Info.Desc);;
	// Res.Rarity = Info.RelicRarity;
	// Res.ElementPathId = Info.IconPath.Num() > 0 ? Info.IconPath[0] : "";
	// Res.IconPathId = Info.IconPath.Num() > 1 ? FString("Relic_").Append(Info.IconPath[1]) : "";
	//
	// UAwRelicSubSystem* RelicSubSystem = GetAwGameInstance()->GetSubsystem<UAwRelicSubSystem>();
	// if (RelicSubSystem->RelicHasGot.Contains(ActionId))
	// 	Res.GotCount = RelicSubSystem->RelicHasGot[ActionId];
	// else
	// 	Res.GotCount = 0;
	
	Res.Id = ActionInfo.AbilityInfoId;
	Res.Name = AbilityInfo.Name;
	Res.Slot = AbilityInfo.AbilitySlot;
	Res.Desc = AbilityInfo.Desc;
	
	if (ActionInfo.Level == 1)
		Res.Desc_2 = "Ability_Level_1";
	else if (ActionInfo.Level == 2)
		Res.Desc_2 = "Ability_Level_2";
	else if (ActionInfo.Level == 3)
		Res.Desc_2 = "Ability_Level_3";
	
	Res.Level = ActionInfo.Level;
	
	switch (AbilityInfo.AbilitySlot)
	{
	default:
	case ERogueAbilitySlot::None:
	case ERogueAbilitySlot::NormalAttack:
		Res.IconPathId = "NormalAttack"; break;
	case ERogueAbilitySlot::Ground1:
		Res.IconPathId = "Ground1"; break;
	case ERogueAbilitySlot::Ground2:
		Res.IconPathId = "Ground2"; break;
	case ERogueAbilitySlot::Air1:
		Res.IconPathId = "Air1"; break;
	case ERogueAbilitySlot::Air2:
		Res.IconPathId = "Air2"; break;
	}
	
	Res.Element = ActionInfo.Element;
	Res.VideoPathId = AbilityInfo.VideoPath;
	
	return Res;
}

FRogueCardInfo_Room UGameplayFuncLib::GetRogueCardInfo_Room(FRogueRoomInfo RoomInfo)
{
	FRogueCardInfo_Room Res;

	Res.RoomInfo = RoomInfo;
	Res.RoomType = RoomInfo.RoomType;
	for (ERogueRoomReward RoomReward : RoomInfo.RoomReward)
	{
		switch (RoomReward)
		{
		case ERogueRoomReward::Relic:
			Res.Rewards.Add("Rogue_Relic");
			
			break;
		case ERogueRoomReward::Item:
			Res.Rewards.Add("Rogue_Item");
			break;
		case ERogueRoomReward::Action:
			Res.Rewards.Add("Rogue_Action");
			break;
		case ERogueRoomReward::Coin:
			Res.Rewards.Add("Rogue_Coin");
			break;
		case ERogueRoomReward::Soul:
			Res.Rewards.Add("Rogue_Soul");
			break;
		default: break;
		}
	}
	switch (RoomInfo.RoomType)
	{
	default: ;
	case ERogueRoomType::Normal:
		Res.Name = "RoomOfMediocre";
		Res.Desc = "RoomOfMediocre_Desc2";
		Res.ActionPointNumber = 1;
		break;
	case ERogueRoomType::Elite:
		Res.Name = "RoomOfTrials";
		Res.Desc = "RoomOfTrials_Desc2";
		Res.ActionPointNumber = 2;
		Res.Rewards.Add("Rogue_Coin");
		break;
	case ERogueRoomType::Boss:
		Res.Name = "RoomOfCruel";
		Res.Desc = "RoomOfCruel_Desc2";
		Res.ActionPointNumber = 2;
		break;
	case ERogueRoomType::Challenge:
		Res.Name = "RoomOfVoid";
		Res.Desc = "RoomOfVoid_Desc2";
		Res.ActionPointNumber = 2;
		break;
	case ERogueRoomType::Store:
		Res.Name = "RoomOfOpportunity";
		Res.Desc = "RoomOfOpportunity_Desc2";
		Res.ActionPointNumber = 0;
		break;
	case ERogueRoomType::Upgrade:
		Res.Name = "RoomOfSacred";
		Res.Desc = "RoomOfSacred_Desc2";
		Res.ActionPointNumber = 0;
		break;
	}
	
	return Res;
}

UObject* UGameplayFuncLib::GetObjectByPath(FString Path)
{
	UObject* Temp = LoadObject<UObject>(nullptr,*UResourceFuncLib::GetAssetPath(Path));
	if(IsValid(Temp))
		return Temp;
	return nullptr;
}

TArray<AAwCharacter*> UGameplayFuncLib::FindNearlyAngleCharacterInSector(float SectorAngle,const AAwCharacter* MyCharacter, TArray<AAwCharacter*> Characters)
{
	TArray<AAwCharacter*> InsideCharacter;
	for (AAwCharacter* Character : Characters) 
		if (UMathFuncLib::IsInsideSector(
			MyCharacter->GetActorLocation() + FVector(0,0,-100),
			FVector2D(MyCharacter->GetActorForwardVector()),
			500,
			SectorAngle,
			0,
			1500,
			Character->GetActorLocation()))
		{
			InsideCharacter.Add(Character);
		}

	return InsideCharacter;
}

AAwCharacter* UGameplayFuncLib::FindNearlyAngleCharacter(const AAwCharacter* MyCharacter,TArray<AAwCharacter*> Characters)
{
	if (Characters.Num() == 0)
		return nullptr;

	if (Characters.Num() == 1)
		return Characters[0];

	AAwCharacter* NearlyCharacter = nullptr;
	NearlyCharacter = Characters[0];
	
	float MinDegree = FMath::Abs(UMathFuncLib::GetDegreeBetweenTwoVector(MyCharacter->GetActorForwardVector(),
			Characters[0]->GetActorLocation() - MyCharacter->GetActorLocation()));

	for (int i = 1; i < Characters.Num(); i++)
	{
		const float NewMinDegree = FMath::Abs(UMathFuncLib::GetDegreeBetweenTwoVector(MyCharacter->GetActorForwardVector(),
			Characters[i]->GetActorLocation() - MyCharacter->GetActorLocation()));
		if (NewMinDegree < MinDegree)
		{
			NearlyCharacter = Characters[i];
			MinDegree = NewMinDegree;
		}
	}

	return NearlyCharacter;
}

void UGameplayFuncLib::SetSequenceState()
{
	SetAllPlayerGameControlState(EGameControlState::Sequence);
	UAwSequenceManager* SequenceManager = GetAwGameInstance()->GetSubsystem<UAwSequenceManager>();
	if(SequenceManager)
	{
		SequenceManager->OnGlobalSequencePlayEvent.Broadcast(nullptr);
	}
}

FString UGameplayFuncLib::GetMobAlterId(FSubjectHandle Handle)
{
	if (Handle.HasTrait<FMonsterAlter>())
	{
		return FStringPool::Get(Handle.GetTraitPtr<FMonsterAlter, EParadigm::Unsafe>()->AlterId);
	}
	return "";
}


TArray<FString> FStringPool::StringTable;
TMap<FString, int32> FStringPool::LookupMap;

int32 FStringPool::Register(const FString& Str)
{
	if (const int32* Existing = LookupMap.Find(Str))
	{
		return *Existing;
	}
	int32 Index = StringTable.Add(Str);
	LookupMap.Add(Str, Index);
	return Index;
}

const FString& FStringPool::Get(int32 Index)
{
	return StringTable[Index];
}

TMap<int32,FBulletModel> FBulletModelPool::LookupMap;

int32 FBulletModelPool::Register(const FBulletModel& Model)
{
	int32 StrKey = FStringPool::Register(Model.Id);
	// 检查是否已经存在相同ID的模型
	if (LookupMap.Contains(StrKey))
	{
		// 如果ID已存在，更新模型数据并返回现有索引
		LookupMap[StrKey] = Model;
		UE_LOG(LogTemp, Log, TEXT("FBulletModelPool: Updated existing model '%s' at index %i"), *Model.Id, StrKey);
		return StrKey;
	}
	
	// 添加新模型到表中
	LookupMap.Add(StrKey,Model);

	UE_LOG(LogTemp, Log, TEXT("FBulletModelPool: Registered new model '%s' at index %i"), *Model.Id, StrKey);

	return StrKey;
}

const FBulletModel& FBulletModelPool::Get(int32 Index)
{
	// 检查索引是否有效
	if (!LookupMap.Contains(Index))
	{
		UE_LOG(LogTemp, Error, TEXT("FBulletModelPool: Invalid index %d, returning default model"), Index);

		// 返回一个静态的默认模型
		static FBulletModel DefaultModel;
		return DefaultModel;
	}

	return LookupMap[Index];
}

bool FBulletModelPool::Contains(const FString& Str)
{
	return Contains(FStringPool::Register(Str));
}
bool FBulletModelPool::Contains(const int32 Key)
{
	return LookupMap.Contains(Key);
}

int32 FBulletModelPool::GetPoolSize()
{
	return LookupMap.Num();
}

void FBulletModelPool::Clear()
{
	LookupMap.Empty();
	UE_LOG(LogTemp, Log, TEXT("FBulletModelPool: Pool cleared"));
}

void UGameplayFuncLib::ProcessAwakeningAndRogueRecovery(AAwCharacter* Attacker, int DefenderHP, FDamageInfo& DamageInfo)
{
	// 觉醒 肉鸽 伤害转化觉醒值
	// 转化公式待定 先1:1
	if (Attacker && !DamageInfo.IsHeal && DamageInfo.DamageType == EDamageType::DirectDamage)
	{
		float AddPoint = FMath::Clamp(DamageInfo.FinalDamage() * 0.05f, 0.0f, DefenderHP);
		AddPoint += DamageInfo.FinalDamage() > 0 ? 5.0f : 0.0f;
		AddPoint = FMath::Clamp(AddPoint, 0.0f, 50.0f);
		Attacker->CharacterObj.CurrentRes.AP = FMath::Clamp(Attacker->CharacterObj.CurrentRes.AP+AddPoint, 0 , Attacker->CharacterObj.CurProperty.AP);

		UAwRogueItemSubSystem* ItemSubSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueItemSubSystem>();
		if (IsValid(ItemSubSystem))
		{
			ItemSubSystem->AddGlobalItemRecoverByDamageInfo(DamageInfo);
		}
	}
}
