// Fill out your copyright notice in the Description page of Project Settings.


#include "CommonFuncLib.h"

#include "Algo/Accumulate.h"
#include "Engine/GameEngine.h"


int UCommonFuncLib::AliasMethodRandom(TArray<float> WeightPool,bool ConstructRate)
{
	int result =0;
	if (WeightPool.Num()<1)
	{
		UE_LOG(LogTemp,Warning,TEXT("Random Array is Empty"));
		return  result;
	}
	if (ConstructRate)
	{
		float Sum = 0;
		Sum = Algo::Accumulate(WeightPool,0.f,TPlus<>());
		for(auto &Element:WeightPool)
		{
			Element = Element/Sum;
		}
	}
	//AliasMethod init
	TArray<float> Prob;
	TArray<float> Alias;
	TArray<float> Less;
	TArray<float> More;

	for (auto Weight:WeightPool)
	{
		Prob.Add(-1);
		Alias.Add(-1);
	}

	//大小堆
	for (int i =0;i<WeightPool.Num();i++)
	{
		WeightPool[i]*=WeightPool.Num();
		if (WeightPool[i]>=1.f)
		{
			More.Add(i);
		}
		else if (WeightPool[i]>0.f)
		{
			Less.Add(i);
		}
	}

	while (!Less.IsEmpty()&&!More.IsEmpty())
	{
		 int LessIndex = *Less.begin();
		 int MoreIndex = *More.begin();
		Less.RemoveAt(0);
		More.RemoveAt(0);
		
		Prob[LessIndex] = WeightPool[LessIndex];
		Alias[LessIndex] = MoreIndex;

		WeightPool[MoreIndex] -= 1.0 -WeightPool[LessIndex];
		 if (WeightPool[MoreIndex] <1.f)
		 {
			 Less.Push(MoreIndex);
		 }
		 else
		 {
			 More.Push(MoreIndex);
		 }
	}
	
	while (!Less.IsEmpty())
	{
		Prob[Less[0]] = 1;
		Less.RemoveAt(0);
	}

	while (!More.IsEmpty())
	{
		Prob[More[0]] = 1;
		More.RemoveAt(0);
	}
	
	//GetResult
	result = FMath::RandRange(0,Prob.Num()-1);
	auto Seed = rand()/double(RAND_MAX);
	if (Seed>Prob[result])
	{
		result = Alias[result];
	}

	return result;	
}
/*
使用示例：

// 示例1：对FString数组进行权重抽取（权重为字符串长度）
TArray<FString> StringArray = {TEXT("A"), TEXT("BB"), TEXT("CCC")};
int SelectedIndex = UCommonFuncLib::WeightedRandomSelection<FString>(StringArray,
    [](const FString& Str) -> float { return static_cast<float>(Str.Len()); });

// 示例2：对自定义结构体数组进行权重抽取
struct FWeightedItem
{
    FString Name;
    float Weight;
};
TArray<FWeightedItem> Items = {{TEXT("Item1"), 10.0f}, {TEXT("Item2"), 20.0f}, {TEXT("Item3"), 5.0f}};
int SelectedIndex = UCommonFuncLib::WeightedRandomSelection<FWeightedItem>(Items,
    [](const FWeightedItem& Item) -> float { return Item.Weight; });

// 示例3：参考GetRandomRelicByLevel的用法，对FString数组使用TMap权重
TArray<FString> RelicPool = {TEXT("Relic1"), TEXT("Relic2"), TEXT("Relic3")};
TMap<FString, float> WeightMap = {{TEXT("Relic1"), 100.0f}, {TEXT("Relic2"), 50.0f}, {TEXT("Relic3"), 25.0f}};
int SelectedIndex = UCommonFuncLib::WeightedRandomSelection<FString>(RelicPool,
    [&WeightMap](const FString& RelicId) -> float {
        return WeightMap.Contains(RelicId) ? WeightMap[RelicId] : 0.0f;
    });

// 示例4：使用WeightedRandomSelectionFromMap直接从TMap中抽取，返回Key值
TMap<FString, float> DirectWeightMap = {{TEXT("Relic1"), 100.0f}, {TEXT("Relic2"), 50.0f}, {TEXT("Relic3"), 25.0f}};
FString SelectedKey = UCommonFuncLib::WeightedRandomSelectionFromMap<float>(DirectWeightMap,
    [](const float& Weight) -> float { return Weight; });

// 示例5：对复杂结构体的TMap进行权重抽取
struct FRelicData
{
    int Level;
    float Weight;
    FString Description;
};
TMap<FString, FRelicData> RelicDataMap = {
    {TEXT("Relic1"), {1, 100.0f, TEXT("Common Relic")}},
    {TEXT("Relic2"), {2, 50.0f, TEXT("Rare Relic")}},
    {TEXT("Relic3"), {3, 25.0f, TEXT("Epic Relic")}}
};
FString SelectedRelicKey = UCommonFuncLib::WeightedRandomSelectionFromMap<FRelicData>(RelicDataMap,
    [](const FRelicData& Data) -> float { return Data.Weight; });
*/
bool UCommonFuncLib::SetWindowMode(EWindowMode::Type type)
{
	UGameEngine* gameEngine = Cast<UGameEngine>(GEngine);
	if (gameEngine)
	{
		TSharedPtr<SWindow> windowPtr = gameEngine->GameViewportWindow.Pin();
		SWindow* window = windowPtr.Get();
		if (window)
		{
			switch(type)
			{
				case EWindowMode::Type::Fullscreen:
				{
					window->Maximize();
						break;
				}
				case EWindowMode::Type::WindowedFullscreen :
				{
						window->Maximize();
						break;
				}
				case EWindowMode::Type::Windowed :
					{
						window->Minimize();
						break;
					}
				case EWindowMode::Type::NumWindowModes :
					{
						window->Minimize();
						break;
					}
			}
			return true;
		}
	}
	return false;
}
