// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Components/RichTextBlock.h"
#include "Engine/DataTable.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "EditorFuncLib.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UEditorFuncLib : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()
	
	//编辑器模式函数 供编辑器工具等使用
	UFUNCTION(BlueprintCallable, Category = "Editor")
	static void DuplicateRowWitchNewRowName(UDataTable* DataTable,FName OldRowName,FName NewRowName);
	UFUNCTION(BlueprintCallable, Category = "Editor")
	static void SetRowNewRichTextData(UDataTable* DataTable,FName RowName, FRichTextStyleRow  StructData);
	UFUNCTION(BlueprintCallable,BlueprintPure, Category = "Editor")
	static FTextBlockStyle RichTextStyleRowBreak(FRichTextStyleRow  StructData);
	UFUNCTION(BlueprintCallable, Category = "Editor")
	static void RichTextStyleRowSet(FRichTextStyleRow&  StructData,FTextBlockStyle Style);
};
