// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Components/ExponentialHeightFogComponent.h"
#include "Kismet/GameplayStatics.h"
#include "TheAwakener_FO/GameFramework/Base/AwGameInstance.h"
#include "TheAwakener_FO/GameFramework/Manager/AwDataManager.h"
#include "TheAwakener_FO/GameFramework/Manager/AwSequenceManager.h"
#include "TheAwakener_FO/GameFramework/Manager/AwRogueDataSystem.h"
#include "TheAwakener_FO/GamePlay/AOE/AOELauncher.h"
#include "TheAwakener_FO/GamePlay/Bullet/BulletLauncher.h"
#include "TheAwakener_FO/GameFramework/AwPlayerController.h"
#include "TheAwakener_FO/GameFramework/Base/AwPlayerState.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "TheAwakener_FO/GameFramework/AW_HUD.h"
#include "TheAwakener_FO/Gameframework/Base/AwGameModeBase.h"
#include "TheAwakener_FO/GameFramework/Base/AwGameState.h"
#include "TheAwakener_FO/GamePlay/Dialog/DialogModeActor.h"
#include "TheAwakener_FO/GamePlay/Roguelike/RogueCardInfo.h"
#include "TheAwakener_FO/GamePlay/Role/AwRoleInfo.h"
#include "TheAwakener_FO/GamePlay/Survivor/AW_GameState_Survivor.h"
#include "GameplayFuncLib.generated.h"


class FStringPool
{
public:
	static int32 Register(const FString& Str);
	static const FString& Get(int32 Index);

private:
	static TArray<FString> StringTable;
	static TMap<FString, int32> LookupMap;
};
/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UGameplayFuncLib : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()

public:
	static UAwDataManager* GetAwDataManager();

	UFUNCTION(BlueprintPure)
	static bool CanUseDebug();

	UFUNCTION(BlueprintPure)
	static bool IsRogueMode();
	
	/**
	 * @brief 移除buff
	 * @param Carrier 携带者
	 * @param BuffID buffID（string） 
	 */
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|Buff")
	static void RemoveBuffByID(AActor* Carrier, FString BuffID);
	
	// 根据角色信息创建角色（玩家）
	/**
	 * @param Transform
	 * @param PlayerClassId 职业 
	 * @param TypeId 角色类型（男/女，将来还会有种族）
	 * @return 
	 */
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|Create")
	static AAwCharacter* CreateCharacterByClass(FTransform Transform, FString PlayerClassId = "Warrior", FString TypeId = "TypeA");

	// 创建新游戏存档
	UFUNCTION(BlueprintCallable, Category="AW|GameSave")
	static FAwRoleInfo CreateNewGame(FString Name = "", FString TypeId = "TypeB", EAwRace Race = EAwRace::Human,
									 FString Appearance ="", FString Voice="", FString ClassId = "Warrior",
									 EChaElemental Elemental = EChaElemental::Fire);
	
	// 储存游戏
	UFUNCTION(BlueprintCallable, Category="AW|GameSave")
	static void SaveGame();

	// 读取存档
	UFUNCTION(BlueprintCallable, Category="AW|LoadGame")
	static FAwRoleInfo LoadGame(bool& HasSaveData, int LoadRoleIndex = 0);
	
	// 删除存档，目前是直接删除 save 文件，会导致多个 AwRoleInfo
	// TODO:每个存档分不同的文件
	UFUNCTION(BlueprintCallable, Category="AW|LoadGame")
	static void DeleteGame(int LoadRoleIndex = 0);
	
	// 根据存盘信息创建角色（仅限玩家）
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|Create")
	static AAwCharacter* CreateCharacterBySaveData(FTransform Transform, FAwCharacterInfo CharacterInfo);
		
	// 根据角色信息创建角色（怪物）
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|Create")
	static AAwCharacter* CreateCharacterByMobInfo(FTransform Transform, FString MobModelId, int MobLevel = 1, int Side = 1, EMobRank MobRank = EMobRank::Normal, FString LevelName = "",FString AlterId = "");
	
	// 创建木桩（在Rogue里面使用）
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|Create")
	static AAwCharacter* CreateStakePawn(FTransform Transform, FString MobModelId, int MobLevel = 1, int HPStar = 1, int AtkStar = 1, FString LevelName = "RogueHall");

	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|Create")
	static FMobModel SetMobModel(FMobModel MobModel, int HPStar = 1, int AtkStar = 1);
	
	static AAwCharacter* CreatCreateByMobModel(FMobModel MobModel, FTransform Transform, int MobLevel = 1, int Side = 1, EMobRank MobRank = EMobRank::Normal, FString LevelName = "", FString AlterId="");
	
	// 根据角色信息创建角色（NPC）
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|Create")
	static AAwCharacter* CreateCharacterByNpcInfo(FTransform Transform, FNpcInfo NpcInfo, FString LevelName = "");

	// 根据Spawn信息创建角色（NPC）
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|Create")
	static AAwCharacter* CreateCharacterBySpawnInfo(FNPCSpawnInfo SpawnInfo, FString LevelName = "");

	UFUNCTION(BlueprintCallable)
	//Multiplayer Param
	static AAwCharacter* ChangeRoguePawn(FString ClassId,int playerIndex,bool IsWorkaround = false);
	UFUNCTION(BlueprintCallable)
	//Multiplayer Param
	static void  RoguePlayerReGetRogueEffect(FString ClassId,int playerIndex,bool IsWorkaround = false);

	UFUNCTION(BlueprintCallable)
	static void ShowSurvivorLevelUpReward();

	UFUNCTION(BlueprintCallable)
	static void ChangeRogueBattleStyle(FString StyleId,const int PlayerIndex);
	
	// 创建对话系统（其实就是那个Actor）
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|Create")
	static ADialogModeActor* CreateDialogModeActor();

	//创建VFX
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|VFX")
	static UParticleSystemComponent* CreateVFXatLocation(UParticleSystem* Template, FTransform Transform, bool AutoDestroy = true, bool AutoActivateSystem = true);
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|VFX")
	static UParticleSystemComponent* CreateVFXByPathAtLocation(FString EffectPath, FTransform Transform, bool AutoDestroy = true, bool AutoActivateSystem = true);
	//创建VFX
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|VFX")
	static UParticleSystemComponent* CreateVFXAttached(UParticleSystem* Template, USceneComponent* AttachToComponent, FName AttachPointName, FTransform Transform, EAttachLocation::Type LocationType = EAttachLocation::KeepRelativeOffset, bool AutoDestroy = true, bool AutoActivateSystem = true);
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|VFX")
	static UParticleSystemComponent* CreateVFXByPathAttached(FString EffectPath, USceneComponent* AttachToComponent, FName AttachPointName, FTransform Transform, EAttachLocation::Type LocationType = EAttachLocation::KeepRelativeOffset, bool AutoDestroy = true, bool AutoActivateSystem = true);
	
	//播放系统音
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|Sound|SystemAudio")
	static UAudioComponent* PlaySystemAudio(USoundBase* Sound, bool bPersistAcrossLevelTransition = false);
	
	//停止所有系统音
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|Sound|SystemAudio")
	static void StopAllSystemAudio();

	//播放音效
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|Sound|SFX")
	static UAudioComponent* PlaySFXatLocation(USoundBase* Sound, FVector Location, FRotator Rotation,
		float VolumeMultiplier = 1.0f, float PitchMultiplier = 1.0f, float StartTime = 0.0f,
		USoundAttenuation* SoundAttenuation = nullptr, USoundConcurrency* SoundConcurrency = nullptr);
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|Sound|SFX")
	static UAudioComponent* PlaySFXByPathAtLocation(FString SoundPath, FVector Location, FRotator Rotation,
		float VolumeMultiplier = 1, float PitchMultiplier = 1, float StartTime = 0,
		USoundAttenuation* SoundAttenuation = nullptr, USoundConcurrency* SoundConcurrency = nullptr);

	//播放音效
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|Sound|SFX")
	static UAudioComponent* PlaySFXAttached(USoundBase* Sound, USceneComponent* AttachtoComponent, FName AttachPointName,
		FVector Location, FRotator Rotation, EAttachLocation::Type LocationType, bool StopWhenAttachedtoDestroyed,
		float VolumeMultiplier = 1.0f, float PitchMultiplier = 1.0f, float StartTime = 0.0f,
		USoundAttenuation* SoundAttenuation = nullptr, USoundConcurrency* SoundConcurrency = nullptr);
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|Sound|SFX")
	static UAudioComponent* PlaySFXByPathAttached(FString SoundPath, USceneComponent* AttachToComponent, FName AttachPointName,
		FVector Location, FRotator Rotation, EAttachLocation::Type LocationType = EAttachLocation::KeepRelativeOffset,
		bool StopWhenAttachedtoDestroyed = true, float VolumeMultiplier = 1.0f, float PitchMultiplier = 1.0f, float StartTime = 0.0f,
		USoundAttenuation* SoundAttenuation = nullptr, USoundConcurrency* SoundConcurrency = nullptr);

	//播放语音
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|Sound|Voice")
	static UAudioComponent* PlayVoiceAtLocation(USoundBase* Sound, FVector Location, FRotator Rotation,
		float VolumeMultiplier = 1.0f, float PitchMultiplier = 1.0f, float StartTime = 0.0f,
		USoundAttenuation* SoundAttenuation = nullptr, USoundConcurrency* SoundConcurrency = nullptr);
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|Sound|Voice")
	static UAudioComponent* PlayVoiceByPathAtLocation(FString SoundPath, FVector Location, FRotator Rotation,
		float VolumeMultiplier = 1, float PitchMultiplier = 1, float StartTime = 0, 
		USoundAttenuation* SoundAttenuation = nullptr, USoundConcurrency* SoundConcurrency = nullptr);

	//播放语音
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|Sound|Voice")
	static UAudioComponent* PlayVoiceAttached(USoundBase* Sound, USceneComponent* AttachtoComponent, FName AttachPointName,
		FVector Location, FRotator Rotation, EAttachLocation::Type LocationType, bool StopWhenAttachedtoDestroyed,
		float VolumeMultiplier = 1.0f, float PitchMultiplier = 1.0f, float StartTime = 0.0f,
		USoundAttenuation* SoundAttenuation = nullptr, USoundConcurrency* SoundConcurrency = nullptr);
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|Sound|Voice")
	static UAudioComponent* PlayVoiceByPathAttached(FString SoundPath, USceneComponent* AttachToComponent, FName AttachPointName,
		FVector Location, FRotator Rotation, EAttachLocation::Type LocationType = EAttachLocation::KeepRelativeOffset,
		bool StopWhenAttachedtoDestroyed = true, float VolumeMultiplier = 1.0f, float PitchMultiplier = 1.0f, float StartTime = 0.0f,
		USoundAttenuation* SoundAttenuation = nullptr, USoundConcurrency* SoundConcurrency = nullptr);

	//播放语音
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|Sound|Voice")
	static UAudioComponent* PlayVoice2D(USoundBase* Sound, float VolumeMultiplier = 1.0f, float PitchMultiplier = 1.0f, float StartTime = 0.0f);
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|Sound|Voice")
	static UAudioComponent* PlayVoice2DByPath(FString SoundPath, float VolumeMultiplier = 1.0f, float PitchMultiplier = 1.0f, float StartTime = 0.0f);

	//停止播放语音
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|Sound|BGM")
	static void StopVoice(UAudioComponent* Voice,float FadeOutTime = 1.0f);
	
	//播放BGN
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|Sound|BGM")
	static void PlayBGM(USoundBase* Sound, bool bPersistAcrossLevelTransition = false, float LastFadeOutTime = 1.0f);
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|Sound|BGM")
	static void PlayBgmByKey(FString BgmKey, bool bPersistAcrossLevelTransition = false, float LastFadeOutTime = 1.0f);
	//停止播放BGM
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|Sound|BGM")
	static void StopBGM(float FadeOutTime = 1.0f);

	//调整主音量
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|Sound|SoundVolume")
	static void SetMainSoundVolume(float NewVolume);
	//调整BGM音量
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|Sound|SoundVolume")
	static void SetBGMVolume(float NewVolume);
	//调整系统音音量
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|Sound|SoundVolume")
	static void SetSystemAudioVolume(float NewVolume);
	//调整音效音量
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|Sound|SoundVolume")
	static void SetSFXVolume(float NewVolume);
	//调整语音音量
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|Sound|SoundVolume")
	static void SetVoiceVolume(float NewVolume);

	//获取主音量
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|Sound|SoundVolume")
	static float GetMainSoundVolume();
	//获取BGM音量
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|Sound|SoundVolume")
	static float GetBGMVolume();
	//获取系统音音量
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|Sound|SoundVolume")
	static float GetSystemAudioVolume();
	//获取音效音量
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|Sound|SoundVolume")
	static float GetSFXVolume();
	//获取语音音量
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|Sound|SoundVolume")
	static float GetVoiceVolume();

	//用AOELauncher创建AOE
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|AOE")
	static AAWAoe* CreateAOEByLauncher(FAOELauncher Launcher,int Level = 4);

	//创建AOE
	// Level 用于动作升级,3级是原始伤害，低级有伤害降低
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|AOE")
	static AAWAoe* CreateAOE(AAwCharacter* Caster, FString AOEID, FVector AOEPosition, FVector Direction, float LifeSpan, FString TweenFuncName,int Level = 3);

	//创建Bullet
	// Level 用于动作升级,3级是原始伤害，低级有伤害降低
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|Bullet")
	static AAwBullet* CreateBullet(FBulletLauncher Launcher,int Level = 3);

	//创建BulletLauncher
	// UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|Bullet")
	// static FBulletLauncher* CreateBulletLauncher(AAwCharacter* Caster, FString BulletID, FVector Position, FVector Direction, float ActionPower, float Scale, FVector DirectionOffset, int Level, FString TweenFunc);

	//创建场景物件
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|SceneItem")
	static AAwSceneItem* CreateSceneItem(FSceneItemModel Model, int Side, FTransform Transform);

	// TODO: 优化：保存到地图数据里面，调用的时候直接查询地图数据，不使用射线检测
	static FVector GetPointOnGround(AAwCharacter* Character, int Height = 100000);
	
	/**
	 * 通过蓝图创建一个Actor
	 * @param BluePrintPath 蓝图路径
	 * @param Transform
	 */
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay")
	static AActor* CreateActorByBP(FString BluePrintPath, FTransform Transform);
	/**
	 * 让所有用户都显示一个PopText
	 * 
	 */
	static void ShowPopText(AAwCharacter* OnCharacter, FString Text, FLinearColor TextColor = FLinearColor::FromSRGBColor(FColor::Orange));
	static void ShowPopText(USceneComponent* OnHitBox, FString Text, FLinearColor TextColor = FLinearColor::FromSRGBColor(FColor::Orange));

	/**
	 * @brief 
	 * @param AwCharacter 
	 * @param Path example: Audio/Sound_effect_UltSFX/Others/UI_Item_Sounds/Drink_potion/Drink_potion_2
	 */
	UFUNCTION(BlueprintCallable)
	static void CharacterPlaySoundOnMouth(AAwCharacter* AwCharacter, FString Path);
	
	UFUNCTION(BlueprintCallable)
	static void CreateATestLootPackage(FTransform Transform);

	UFUNCTION(BlueprintCallable)
	static void CreateVoiceStimulate(FVector Position, int VoiceRange, AAwCharacter* VoiceCaster = nullptr);

	UFUNCTION(BlueprintCallable)
	static void SetActorOutline(AActor* TargetActor, bool ShowOutline = true);

	UFUNCTION(BlueprintCallable)
	static bool GetHitResultBetweenSceneComponent(USceneComponent* BeHitBox, USceneComponent* AttackBox, FVector& HitLocation, FVector& HitNormal);

	UFUNCTION(BlueprintPure, Category="AW|Get")
	static UAwGameInstance* GetAwGameInstance()
	{
		return UAwGameInstance::Instance;
	}
	
	UFUNCTION(BlueprintPure, Category="AW|Get")
	static UAwDataManager* GetDataManager()
	{
		if (UAwGameInstance::Instance && UAwGameInstance::Instance->DataManager)
			return UAwGameInstance::Instance->DataManager;
		return nullptr;
	}

	UFUNCTION(BlueprintPure, Category="AW|Get")
	static UAwUIManager* GetUiManager()
	{
		if (UAwGameInstance::Instance && UAwGameInstance::Instance->UIManager)
			return UAwGameInstance::Instance->UIManager;
		return nullptr;
	}
	UFUNCTION(BlueprintCallable, Category="Camera")
	static void UnifiedCameraFadeOut();
	UFUNCTION(BlueprintCallable, Category="Camera")
	static void UnifiedCameraFadeIn();
	UFUNCTION(BlueprintPure, Category="AW|Get")
	static UScoreManager* GetScoreManager()
	{
		if (UAwGameInstance::Instance && UAwGameInstance::Instance->ScoreManager)
			return UAwGameInstance::Instance->ScoreManager;
		return nullptr;
	}
	
	UFUNCTION(BlueprintPure, Category="AW|Get")
	static AAwPlayerController* GetAwPlayerController(int32 PlayerIndex)
	{
		if (UAwGameInstance::Instance)
			return Cast<AAwPlayerController>(UGameplayStatics::GetPlayerController(UAwGameInstance::Instance->GetWorld(), PlayerIndex));
		return nullptr;
	}
	
	UFUNCTION(BlueprintPure, Category="AW|Get")
	static TArray<AAwPlayerController*> GetAllLocalAwPlayerControllers()
	{
		TArray<AAwPlayerController*> Controllers;
		if(UAwGameInstance::Instance)
		{
			auto players = UAwGameInstance::Instance->GetLocalPlayers();
			for(auto player : players)
			{
				if (IsValid(player->PlayerController))
				{
					auto awpc = Cast<AAwPlayerController>(player->PlayerController);
					if (IsValid(awpc))
					{
						Controllers.Add(awpc);
					}
				}
			}
		}
		return Controllers;
	}
	static AAwCharacter* GetCharacterEquippedWeaponId(FString WeaponId)
	{
		UAwRogueDataSystem* RogueDataSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>();
		for (auto PC :GetAllLocalAwPlayerControllers())
		{
			FString CurrWeaponId = RogueDataSystem->GetCurrWeaponId(PC->CurCharacter->GetPawnClassId());
			if (CurrWeaponId == WeaponId)
			{
				return PC->CurCharacter;
			}
		}
		return nullptr;
	}
	UFUNCTION(BlueprintCallable, Category="AW|Get")
	static AAwCharacter* GetClosestPlayerCharacter(FVector Location);
	UFUNCTION(BlueprintCallable, Category="AW|Get")
	static bool HavePlayerCharacterInRange(FVector Location,float RadiusMin,float RadiusMax = -1);
	UFUNCTION(BlueprintCallable)
	static TArray<AAwPlayerController*> SetAllPlayerGameControlState(EGameControlState newState);
	// UFUNCTION(BlueprintCallable,CustomThunk, meta=(CustomStructureParam="OutData", AutoCreateRefTerm="Health,Speed,Name", AdvancedDisplay="Health,Speed,Name"))
	// static void SetAllPlayerWidgetInputState(AAwPlayerController Target,EGameControlState newState, bool FlashInput, bool ShowCursor);
	UFUNCTION(BlueprintCallable)
	static TArray<AAwPlayerController*> SetAllPlayerUIControlState(EGameUIControlState newState);
	UFUNCTION(BlueprintPure, Category="AW|Get")
	//Multiplayer Param
	static AAwPlayerController* GetWorkingAwPlayerController()
	{
		if (UAwGameInstance::Instance)
		{
			int pcid = UAwGameInstance::Instance->GetWorkingLocalPlayerControllerIndex();
			return GetLocalAwPlayerController(pcid);
		}
		return GetLocalAwPlayerController(0);
	}
	UFUNCTION(BlueprintPure, Category="AW|Get",meta=(ToolTip="As Same As GetWorkingAwPlayerController"))
	//Multiplayer Param
	static AAwPlayerController* GetMyAwPlayerController()
	{
		return GetWorkingAwPlayerController();
	}
	static AAwPlayerController* GetLocalAwPlayerController(int playerIndex)
	{
		if(UAwGameInstance::Instance)
		{
			// UE_LOG(LogTemp,Log,TEXT("Local players count: %d"),UAwGameInstance::Instance->GetNumLocalPlayers())
			auto players = UAwGameInstance::Instance->GetLocalPlayers();
			if (players.Num()>playerIndex)
				return Cast<AAwPlayerController>(UAwGameInstance::Instance->GetLocalPlayerByIndex(playerIndex)->GetPlayerController(UAwGameInstance::Instance->GetWorld()));
		}
		return nullptr;
	}
	static AAwPlayerController* GetLocalAwPlayerController(const FString& PawnClassID)
	{
		for(auto player : GetAllLocalAwPlayerControllers())
		{
			if (player->CurCharacter->PlayerClassId== PawnClassID)
				return player;
		}
		return nullptr;
	}
	
	UFUNCTION(BlueprintPure, Category="AW|Get")
	//Multiplayer Param
	static AAwCharacter* GetLocalAwPlayerCharacter(int playerIndex)
	{
		if(UAwGameInstance::Instance && UAwGameInstance::Instance->GetWorld())
			if (auto PC = GetLocalAwPlayerController(playerIndex))
			{
				return PC->CurCharacter;
			}
		return nullptr;
	}
	
	UFUNCTION(BlueprintPure, Category="AW|Get")
	//Multiplayer Param
	static AAwPlayerState* GetLocalAwPlayerState(int playerIndex)
	{
		if(UAwGameInstance::Instance && UAwGameInstance::Instance->GetWorld())
			if (GetLocalAwPlayerController(playerIndex))
			{
				return GetLocalAwPlayerController(playerIndex)->GetPlayerState<AAwPlayerState>();
			}
		return nullptr;
	}
	UFUNCTION(BlueprintCallable, Category="AW|Get", meta=(ExpandEnumAsExecs="Branches"))
	static AAwPlayerState* K2GetPlayerState(EAwResultExecPin& Branches,UActorComponent* Comp);
	UFUNCTION()
	static AAwPlayerState* GetPlayerState(const UActorComponent* Comp);
	UFUNCTION(BlueprintCallable, Category="AW|Get", meta=(ExpandEnumAsExecs="Branches"))
	static AAwPlayerController* K2GetPlayerController(EAwResultExecPin& Branches,UActorComponent* Comp);
	UFUNCTION()
	static AAwPlayerController* GetPlayerControllerByComp(const UActorComponent* Comp,bool Insure = true);
	UFUNCTION(BlueprintCallable, Category="AW|Get", meta=(ExpandEnumAsExecs="Branches"))
	static AAwPlayerController* K2GetPlayerControllerByWidget(EAwResultExecPin& Branches,UUserWidget* Widget);
	UFUNCTION()
	static AAwPlayerController* GetPlayerControllerByWidget(const UUserWidget* Widget);
	
	UFUNCTION(BlueprintPure, Category="AW|Get")
	static UTimelineManager* GetTimelineManager()
	{
		if (UAwGameInstance::Instance && UAwGameInstance::Instance->TimelineManager)
			return UAwGameInstance::Instance->TimelineManager;
		return nullptr;
	}

	UFUNCTION(BlueprintPure, Category="AW|Get")
	static UDamageManager* GetDamageManager()
	{
		if (UAwGameInstance::Instance && UAwGameInstance::Instance->DamageManager)
			return UAwGameInstance::Instance->DamageManager;
		return nullptr;
	}

	UFUNCTION(BlueprintPure, Category="AW|Get")
	static UBGMManager* GetBGMManager()
	{
		if (UAwGameInstance::Instance && UAwGameInstance::Instance->BGMManager)
			return UAwGameInstance::Instance->BGMManager;
		return nullptr;
	}

	UFUNCTION(BlueprintPure, Category = "AW|Get")
	static UTriggerManager* GetTriggerManager()
	{
		if (UAwGameInstance::Instance && UAwGameInstance::Instance->TriggerManager)
			return UAwGameInstance::Instance->TriggerManager;
		return nullptr;
	}
	UFUNCTION(BlueprintPure, Category="AW|ECS")
	static FString GetStringPool(int32 Key)
	{
		return  FStringPool::Get(Key);
	}

	UFUNCTION(BlueprintPure, Category="AW|ECS")
	static int RegStringPool(FString Name)
	{
		return  FStringPool::Register(Name);
	}

	UFUNCTION(BlueprintPure, Category= "AW|Get")
	static AAW_HUD* GetAwHud(int PlayerIndex)
	{
		return Cast<AAW_HUD>(GetLocalAwPlayerController(PlayerIndex)->GetHUD());
	}

	UFUNCTION(BlueprintPure, Category= "AW|Get")
	static UAwLootManager* GetLootManager()
	{
		if (UAwGameInstance::Instance && UAwGameInstance::Instance->LootManager)
			return UAwGameInstance::Instance->LootManager;
		return nullptr;
	}

	UFUNCTION(BlueprintPure, Category= "AW|Get")
	static UAwTeamManager* GetTeamManager()
	{
		if (UAwGameInstance::Instance && UAwGameInstance::Instance->TeamManager)
			return UAwGameInstance::Instance->TeamManager;
		return nullptr;
	}

	UFUNCTION(BlueprintPure, Category= "AW|Get")
	static UAwAvoidanceManager* GetAwAvoidanceManager()
	{
		if (UAwGameInstance::Instance && UAwGameInstance::Instance->AvoidanceManager)
			return UAwGameInstance::Instance->AvoidanceManager;
		return nullptr;
	}
	
	UFUNCTION(BlueprintPure, Category = "AW|Get")
	static AAwGameState* GetAwGameState()
	{
		if (GWorld && UAwGameInstance::Instance && UAwGameInstance::Instance->GetWorld() &&
			UGameplayStatics::GetGameState(UAwGameInstance::Instance->GetWorld()))
			return Cast<AAwGameState>(UGameplayStatics::GetGameState(UAwGameInstance::Instance->GetWorld()));
		return nullptr;
	}
	UFUNCTION(BlueprintPure, Category="AW|FlowControl")
	static bool IsEditor()
	{
		#if WITH_EDITOR
			return true;
		#else
			return false;
		#endif
	}
	/**
	 * 命中时候播放的特效
	 * 如果需要幸存者数据，但幸存者数据不存在，则返回默认版本
	 */
	UFUNCTION(BlueprintCallable)
	static UParticleSystem* GetHitVFX(FActionChangeInfo info){
		if(UAwGameInstance::Instance->isSurvivor && info.HitVFX_Svl){
			return info.HitVFX_Svl;
		}
		else{
			return info.HitVFX;
		}
	}

	/**
	 * 命中时候播放的音效，如果是空字符串，就代表不播放
	 * 如果需要幸存者数据，但幸存者数据不存在，则返回默认版本
	 */
	UFUNCTION(BlueprintCallable)
	static USoundBase* GetHitSFX(FActionChangeInfo info){
		if(UAwGameInstance::Instance->isSurvivor && info.HitSFX_Svl){
			return info.HitSFX_Svl;
		}
		else{
			return info.HitSFX;
		}
	}
	/**
	 * 命中时候播放的特效
	 */
	UFUNCTION(BlueprintCallable)
	static void SetHitVFX(FActionChangeInfo info, UParticleSystem* data){
		if(UAwGameInstance::Instance->isSurvivor){
			info.HitVFX_Svl = data;
		}
		else{
			info.HitVFX = data;
		}
	}
	UFUNCTION(BlueprintCallable)
	static FVector GetPointOnGround(FVector Location, float Radius);
	/**
	 * 命中时候播放的音效，如果是空字符串，就代表不播放
	 */
	UFUNCTION(BlueprintCallable)
	static void SetHitSFX(FActionChangeInfo info, USoundBase* data){
		if(UAwGameInstance::Instance->isSurvivor){
			info.HitSFX_Svl = data;
		}
		else{
			info.HitSFX = data;
		}
	}
	UFUNCTION(BlueprintPure, Category = "AW|Get")
	static AAW_GameState_Survivor * GetAwGameStateSurvivor()
	{
		if (GWorld && UAwGameInstance::Instance && UAwGameInstance::Instance->GetWorld() &&
			UGameplayStatics::GetGameState(UAwGameInstance::Instance->GetWorld()))
			return Cast<AAW_GameState_Survivor>(UGameplayStatics::GetGameState(UAwGameInstance::Instance->GetWorld()));
		return nullptr;
	}

	UFUNCTION(BlueprintPure, Category = "AW|Get")
	static AAwGameModeBase* GetAwGameMode()
	{
		if (GWorld && UGameplayStatics::GetGameMode(GWorld))
			return Cast<AAwGameModeBase>(UGameplayStatics::GetGameMode(UAwGameInstance::Instance->GetWorld()));
		return nullptr;
	}

	//逻辑上暂停游戏，除了某些角色
	UFUNCTION(BlueprintCallable, Category="AW|GamePause")
	static void PauseGameActors(TArray<AAwCharacter*> ExceptFor);
	
	UFUNCTION(BlueprintCallable, Category="AW|GameResume")
	static void ResumeGameActors();

	UFUNCTION(BlueprintCallable, Category="AW|GamePause")
	static void PauseGameForMenu();

	UFUNCTION(BlueprintCallable, Category="AW|GameResume")
	static void ResumeGameForMenu();

	UFUNCTION(BlueprintCallable, Category="AW|GamePause")
	static void PauseGameForDialog(AAwCharacter* TalkTo,const int PlayerIndex);

	UFUNCTION(BlueprintCallable, Category="AW|GameResume")
	static void ResumeGameForDialog();

	//获取以屏幕中心点射出的胶囊体射线穿过的人物列表
	UFUNCTION(BlueprintCallable)
	static TArray<AActor*> GetTracedActorsFromScreenCenter(UCameraComponent* Cam, float LineDistance, float HalfHeight, float Radius);

	UFUNCTION(BlueprintCallable)
	static int GetRoleSwitchValue(FString SwitchKey, bool& HasKey);

	UFUNCTION(BlueprintCallable)
	static void SetRoleSwitchValue(FString SwitchKey, int SwitchValue);

	UFUNCTION(BlueprintCallable)
	static int GetSwitchValueInGameState(FString SwitchKey, bool& HasKey);

	UFUNCTION(BlueprintCallable)
	static void SetSwitchValueInGameState(FString SwitchKey, int SwitchValue);

	UFUNCTION(BlueprintCallable)
	static void SetSecondFogData(UExponentialHeightFogComponent* Comp, FVector Data)
	{
		Comp->SecondFogData.FogDensity = Data.X;
		Comp->SecondFogData.FogHeightFalloff = Data.Y;
		Comp->SecondFogData.FogHeightOffset = Data.Z;
	}

	UFUNCTION(BlueprintCallable)
	static void PlayUIAudio(FString AudioId)
	{
		USoundCue* Cue = LoadObject<USoundCue>(nullptr,
		*UResourceFuncLib::GetAssetPath(GetAwDataManager()->
			GetUIAudioById(AudioId).AudioPath));
		if(Cue)
			UGameplayStatics::PlaySound2D(UAwGameInstance::Instance->GetWorld(), Cue);
	}

	UFUNCTION(BlueprintCallable)
	static ELanguage GetCurLanguage()
	{
		ELanguage Language = ELanguage::Chinese;
		 if (GetAwGameInstance())
		{
		 	if (IsRogueMode())
		 	{
		 		Language = URogueGameSetting::GetRogueGameSettings()->GetLanguage();
		 	}
		    else
		    {
		    	Language = GetAwGameInstance()->RoleInfo.CurLanguage;
		    }
		}
		
		return Language;
	}

	UFUNCTION(BlueprintCallable)
	static void StartAllTargetSignFunction();

	//玩家觉醒相关
	static  FAwActionSkillInfo* GetPlayerCurAwkeSkill(AAwPlayerController* pc);
	UFUNCTION(BlueprintCallable,BlueprintPure,meta=(pc="Owning Player Controller"))
	static FAwActionSkillInfo K2Node_GetPlayerCurAwkeSkill(AAwPlayerController* pc);
	UFUNCTION(BlueprintCallable,meta=(pc="Owning Player Controller"))
	static void SetPlayerCurAwakeSkill(AAwPlayerController* pc,FString NewAwakeSkillId = "");
	UFUNCTION(BlueprintCallable,meta=(pc="Owning Player Controller"))
    static void UnlockPlayerAwakeSkill(AAwPlayerController* pc,FString AwakeSkillId = "");
	UFUNCTION(BlueprintCallable,meta=(pc="Owning Player Controller"))
	static void ClearPlayerAwakeSkill(AAwPlayerController* pc);

	//RogueGamePaused 肉鸽游戏暂停
	UFUNCTION(BlueprintCallable)
	static void RogueGamePaused();
	UFUNCTION(BlueprintCallable)
	//RogueGameResume 肉鸽游戏暂停恢复
	static void RogueGameResume();
	
	UFUNCTION(BlueprintPure)
	static UTexture2D* GetTextureByPath(FString Path);

	UFUNCTION(BlueprintPure)
	static UTexture2D* GetItemIconTextureById(FString Id);
	
	UFUNCTION(BlueprintCallable)
	static FRogueCardInfo_BattleStyleUpgrade GetRogueCardInfo_BattleStyleUpgrade(FString UpgradeId);

	UFUNCTION(BlueprintCallable,meta=(pc="Owning Player Controller"))
	static FRogueCardInfo_BattleStyleUpgrade GetRogueCardInfo_BattleUpgrade(AAwPlayerController* pc,FString UpgradeId);
	
	UFUNCTION(BlueprintCallable)
	static FRogueCardInfo_Relic GetRogueCardInfo_Relic(FString RelicId, int Level);

	UFUNCTION(BlueprintCallable)
	static FRogueCardInfo_MagicItem GetRogueCardInfo_MagicItem(FString ItemId, int Level);

	UFUNCTION(BlueprintCallable)
	//Multiplayer Param
	static FRogueCardInfo_Action GetRogueCardInfo_Action(FRougeAbilityLevelInfo ActionInfo,int playerIndex);

	UFUNCTION(BlueprintCallable)
	static FRogueCardInfo_Room GetRogueCardInfo_Room(FRogueRoomInfo RoomInfo);

	UFUNCTION(BlueprintPure)
	static UObject* GetObjectByPath(FString Path);

	// 查找角色正面扇形多少角度内的夹角内的角色
	UFUNCTION(BlueprintCallable)
	static TArray<AAwCharacter*>  FindNearlyAngleCharacterInSector(float SectorAngle,const AAwCharacter* MyCharacter, TArray<AAwCharacter*> Characters);

	// 查找夹角最近的角色
	UFUNCTION(BlueprintCallable)
	static AAwCharacter* FindNearlyAngleCharacter(const AAwCharacter* MyCharacter,TArray<AAwCharacter*> Characters);

	// 设置Sequence的GameControlState
	UFUNCTION(BlueprintCallable)
	static void SetSequenceState();

	UFUNCTION(BlueprintCallable,BlueprintPure)
	static FString GetMobAlterId(FSubjectHandle Handle);

	/**
	 * 处理觉醒值转化和肉鸽道具能量恢复
	 * @param Attacker 攻击者
	 * @param Defender 防御者
	 * @param DamageInfo 伤害信息
	 */
	static void ProcessAwakeningAndRogueRecovery(AAwCharacter* Attacker, int DefenderHP, FDamageInfo& DamageInfo);
};


class FBulletModelPool
{
public:
	static int32 Register(const FBulletModel& Model);
	static const FBulletModel& Get(int32 Index);
	static bool Contains(const FString& Str);
	static bool Contains(const int32 Key);
	static int32 GetPoolSize();
	static void Clear();

private:
	static TMap<int32,FBulletModel> LookupMap;
};