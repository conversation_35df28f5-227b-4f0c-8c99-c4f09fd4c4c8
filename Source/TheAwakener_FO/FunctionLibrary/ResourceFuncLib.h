// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Kismet/KismetSystemLibrary.h"
#include "TheAwakener_FO/GamePlay/Characters/Action/ActionInfo.h"
#include "ResourceFuncLib.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UResourceFuncLib : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()

public:
	/**
	 * @brief Get the blueprint path can been loaded by file path in content.
	 * example:
	 *		FString strBPFileName = "/Game/ThirdPersonCPP/Blueprints/TestActor.TestActor_C";
	 *		UClass* pClass = LoadClass<AActor>(this, *strBPFileName);
	 * @param Path File path in content.
	 * @return The blueprint path can be loaded.
	 */
	UFUNCTION(BlueprintPure, Category="AW|Resource")
	static FString GetBpAssetPath(FString Path);

	UFUNCTION(BlueprintPure, Category="AW|Resource")
	static FString GetWbpAssetPath(FString Path);
	
	/**
	 * @brief Get the asset path can been loaded by file path in content.
	 * example:
	 *		FString strMeshFileName = "/Game/Geometry/Meshes/1M_Cube.1M_Cube";
	 *		UStaticMesh* pStaticMesh = LoadObject<UStaticMesh>(this, *strBPFileName);
	 * @param Path File path in content.
	 * @return The asset path can be loaded.
	 */
	UFUNCTION(BlueprintPure, Category="AW|Resource")
	static FString GetAssetPath(FString Path);
	
	UFUNCTION(BlueprintCallable, Category="AW|Resource")
	static UAnimationAsset* LoadAnimAsset(FString Path, bool GoGetAssetPath = true);

	UFUNCTION(BlueprintCallable, Category="AW|Resource")
	static USoundBase* LoadSoundBase(FString Path);

	UFUNCTION(BlueprintCallable, Category="AW|Resource")
	static UTexture2D* LoadTexture2D(FString Path);
	
	UFUNCTION(BlueprintCallable, Category="AW|Resource")
	static AActor* SpawnActorByBP(FString Path, FTransform Transform);

	/** Get file name by split path.("/") */
	UFUNCTION(BlueprintPure, Category = "AW Resource Manager")
	static FString GetFileNameByPath(const FString Path);
	/** Get the path of the class's name Texture. */
	UFUNCTION(BlueprintPure, Category = "AW Resource Manager")
	static FString GetClassNameTexPath(const FString Path);
	/** Get the path of the skill icon. */
	UFUNCTION(BlueprintPure, Category = "AW Resource Manager")
	static FString GetSkillIconPath(const FString Path);
	
	UFUNCTION(BlueprintPure)
	static FString GetThingPackageActorBpPath();

	template<typename T>
	static bool ExistAsset(const FString Path)
	{
		const FString AssetPath = GetAssetPath(Path);
		const T* BpClass = LoadObject<T>(nullptr, *AssetPath);
		if (BpClass)
		{
			// UKismetSystemLibrary::PrintString(GWorld, "Exist true! - "+AssetPath);
			return true;
		}
		else
		{
			// UKismetSystemLibrary::PrintString(GWorld, "Exist false! - "+AssetPath);
			return false;
		}
	}
	
	static FActionInfo ReplaceAnimPathByTypeId(FActionInfo Action, FString PlayerTypeId);
};
