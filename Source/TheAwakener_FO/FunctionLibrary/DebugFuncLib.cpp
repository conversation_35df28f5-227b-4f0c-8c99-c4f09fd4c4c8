// Fill out your copyright notice in the Description page of Project Settings.


#include "DebugFuncLib.h"

#include "GameplayFuncLib.h"

void UDebugFuncLib::KillMyself(int playerIndex)
{
	UGameplayFuncLib::GetLocalAwPlayerController(playerIndex)->Cur<PERSON>haracter->InstantKill(false);
}

void UDebugFuncLib::RevivedMyself(int playerIndex)
{
	UGameplayFuncLib::GetLocalAwPlayerController(playerIndex)->CurCharacter->RevivedOnSecondWind();
}

void UDebugFuncLib::FullyRestore(int playerIndex)
{
	UGameplayFuncLib::GetLocalAwPlayerController(playerIndex)->CurCharacter->FullyRestore(true,true,true);
}

void UDebugFuncLib::KillAllMobs()
{
	UGameplayFuncLib::GetAwGameState()->AllCharacters.Remove(nullptr);
	int DonotKillNum = 0;
	TArray<AAwCharacter*> AllCharacters;
	UGameplayFuncLib::GetAwGameState()->AllCharacters.GetKeys(AllCharacters);
	TArray<AAwCharacter*> DonotKillCharacters;
	while (DonotKillNum < AllCharacters.Num())
	{
		for (AAwCharacter* CurCharacter : AllCharacters)
		{
			if (CurCharacter && CurCharacter->MobClassId != "")
			{
				AAwCharacter* Cha = CurCharacter;
				UGameplayFuncLib::GetAwGameState()->AllCharacters.Remove(Cha);
				AllCharacters.Remove(Cha);
				Cha->InstantKill(true, true);
				//Cha->Destroy();
				break;
			}
			if(!DonotKillCharacters.Contains(CurCharacter))
			{
				DonotKillCharacters.Add(CurCharacter);
				DonotKillNum++;
			}
		}
	}
}

void UDebugFuncLib::KillAllEnemy()
{
	UGameplayFuncLib::GetAwGameState()->AllCharacters.Remove(nullptr);
	int DonotKillNum = 0;
	TArray<AAwCharacter*> AllCharacters;
	UGameplayFuncLib::GetAwGameState()->AllCharacters.GetKeys(AllCharacters);
	TArray<AAwCharacter*> DonotKillCharacters;
	while (DonotKillNum < AllCharacters.Num())
	{
		for (AAwCharacter* CurCharacter : AllCharacters)
		{
			if (CurCharacter && CurCharacter->IsEnemy(UGameplayFuncLib::GetAwGameState()->GetMyCharacter()))
			{
				AAwCharacter* Cha = CurCharacter;
				UGameplayFuncLib::GetAwGameState()->AllCharacters.Remove(Cha);
				AllCharacters.Remove(Cha);
				Cha->InstantKill(true, true);
				UE_LOG(LogTemp, Log, TEXT("InstantKill %s"), *Cha->GetName());
				//Cha->Destroy();
				break;
			}
			if(!DonotKillCharacters.Contains(CurCharacter))
			{
				DonotKillCharacters.Add(CurCharacter);
				DonotKillNum++;
			}
		}
	}
}
