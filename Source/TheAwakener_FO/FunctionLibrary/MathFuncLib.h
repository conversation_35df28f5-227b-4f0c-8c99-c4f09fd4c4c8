// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "MathFuncLib.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UMathFuncLib : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()
	
public:
	
	UFUNCTION(BlueprintPure, Category = "AW Math", meta = (UnsafeDuringActorConstruction = "true"))
	static FRotator Aw_RInterp(FRotator CurRot, FRotator TargetRot, float DeltaTime, float InterpSpeed);

	UFUNCTION(BlueprintPure, Category = "AW Math")
	static FRotator Aw_RotatorOffsetNormalize(FRotator CurRot, FRotator TargetRot);
	
	UFUNCTION()
	static float GetRotatorShortestAxial(float Current, float Target);

	// 规整 Roll，Yaw，Pitch 这些角度值
	// Type == 0 规整到 0~360
	// Type == 1 规整到 -180~180
	UFUNCTION(BlueprintPure)
	static float NormalizeAngle(float AngleValue, int Type = 0);
	
	/**
	 * @brief 计算向量 Input 在 Target 上的投影
	 */
	UFUNCTION()
	static FVector ProjectionV3ToV3(FVector InputV3, FVector TargetV3);

	/**
	 * @brief 计算向量 XY不变的情况下 在angle度的向量
	 */
	UFUNCTION()
	static FVector CalculateV3OnAngle(FVector V3, float Angle);

	
	/**
	 * @brief 计算向量与水平面的夹角
	 */
	UFUNCTION()
	static float GetHorizonAngleByV3(FVector V3);

	
	/**
	 * @brief 随机一个圆上的一个点
	 * @param Radius 圆的半径
	 * @return 返回圆上的一个点
	 */
	UFUNCTION()
	static FVector2D RandomV2OnCircle(float Radius = 1.0f);
	
	/**
	 * @brief 判断V2是否在V1的顺时针方向
	 */
	UFUNCTION()
	static bool AreClockwise(const FVector2D& V1, const FVector2D& V2);
	
	/**
	 * @brief 判断点是否在范围内
	 */
	UFUNCTION()
	static bool IsWithinRadius(FVector Center, float NearClippingRadius, float FarClippingRadius, FVector TargetPoint);

	/**
	 * @brief 判断点是否在扇形(有高度的扇形)内
	 *
	 *                    Far
	 *                   *****  
	 *              *             *
	 *          *                     *
	 *       *                           *
	 *     *                               *
	 *   *              Forward              * 
	 *    \                ↑                /
	 *     \               ↑               /
	 *      \              ↑              /
	 *       \                           /
	 *        \                         /
	 *         \          ***          /
	 *          \     *   Near    *   /
	 *           \ *               * /
	 *            N                 N
	 *            
	 *           
	 *           
	 *           
	 *
	 *					   C
	 *                  Center
	 *                  
	 * @param SectorCenter 扇形的中心点
	 * @param SectorForward 扇形的方向
	 * @param SectorHeight 扇形的高度
	 * @param SectorAngle 扇形的角度（0-360）
	 * @param NearClippingRadius 近半径
	 * @param FarClippingRadius 远半径
	 * @param TargetPoint 
	 * @return 
	 */
	UFUNCTION(BlueprintCallable)
	static bool IsInsideSector(FVector SectorCenter, FVector2D SectorForward, float SectorHeight, float SectorAngle,
								float NearClippingRadius, float FarClippingRadius, FVector TargetPoint);
	
	UFUNCTION(BlueprintCallable)
	static float FInterpTo( float Current, float Target, float DeltaTime, float InterpSpeed, float MinSpeed);
	
	UFUNCTION(BlueprintCallable)
	static FVector VInterpTo( const FVector& Current, const FVector& Target, float DeltaTime, float InterpSpeed, float MinSpeed);

	//返回2个向量的夹角；
	//返回值为正时，SecondDir在FirstDir的右边
	//返回值为负时，SecondDir在FirstDir的左边
	// -180~180
	UFUNCTION(BlueprintCallable)
	static float GetDegreeBetweenTwoVector(const FVector FirstDir, const FVector SecondDir);

	// 目标点是否在3D矩形内
	UFUNCTION(BlueprintCallable)
	static float IsInCube(FVector CubeCenter, FVector CubeExtent, FVector TargetPoint);

	// 根据权重随机几个Id
	UFUNCTION(BlueprintCallable)
	static TArray<FString> GetRandomIdInPool(TMap<FString, int> Pool, int Count = 1);

	// 乱序
	UFUNCTION(BlueprintCallable)
	static TArray<FString> DisorderStrArray(TArray<FString> List);
};
