// Fill out your copyright notice in the Description page of Project Settings.


#include "CallFuncLib.h"


UFunction* UCallFuncLib::GetUFunction(FString ClassPath, FString FuncName)
{
	UFunction* Func = nullptr;

	// 如果是蓝图脚本
	if (ClassPath.Contains("DesignerScript/"))
	{
		TArray<FString> StrArray;
		ClassPath.ParseIntoArray(StrArray,TEXT("/"));
		const FString BpName = StrArray.Last();
		const FSoftClassPath SoftClassPath(TEXT("/Game/"+ ClassPath + "." + BpName + "_C"));
		const UClass* BpClass = SoftClassPath.TryLoadClass<UObject>();
		if (BpClass)
			Func = BpClass->FindFunctionByName(FName(*FuncName));
	}
	// c++ 脚本
	else
	{
		if (!ClassPath.IsEmpty())
		{
			const FString Path = "/Script/CoreUObject.Class'/Script/ProjectAwaker."+ClassPath+"'";
			// UClass* CppClass = FindObject<UClass>(ANY_PACKAGE, *ClassPath);
			const UClass* CppClass = FindObject<UClass>(nullptr, *Path);
			if (CppClass)
				Func = CppClass->FindFunctionByName(FName(*FuncName));
		}
	}
		
	return Func;
}