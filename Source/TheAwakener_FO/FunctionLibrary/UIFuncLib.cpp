#include "UIFuncLib.h"

#include "Components/CanvasPanel.h"
#include "Components/CanvasPanelSlot.h"

void UIFuncLib::UpdateAnchorPreservingVisual(UWidget* Widget, const FAnchors& NewAnchors)
{
    if (!Widget) return;

    UCanvasPanelSlot* CanvasSlot = Cast<UCanvasPanelSlot>(Widget->Slot);
    if (!CanvasSlot) return;

    UPanelWidget* Parent = Widget->GetParent();
    if (!Parent) return;

    UCanvasPanel* Canvas = Cast<UCanvasPanel>(Parent);
    if (!Canvas) return;

    // 获取父容器尺寸
    FVector2D ParentSize = Canvas->GetCachedGeometry().GetLocalSize();

    // 当前锚点和偏移
    FAnchors OldAnchors = CanvasSlot->GetAnchors();
    FMargin OldOffsets = CanvasSlot->GetOffsets();
    FVector2D Alignment = CanvasSlot->GetAlignment();

    // 计算 widget 在父容器中的视觉绝对位置
    FVector2D OldAnchorPos = FVector2D(
        FMath::Lerp(OldAnchors.Minimum.X, OldAnchors.Maximum.X, 0.5f) * ParentSize.X,
        FMath::Lerp(OldAnchors.Minimum.Y, OldAnchors.Maximum.Y, 0.5f) * ParentSize.Y
    );

    FVector2D OldWidgetPos = OldAnchorPos + FVector2D(OldOffsets.Left, OldOffsets.Top);

    // 替换锚点
    CanvasSlot->SetAnchors(NewAnchors);

    // 计算新锚点的中心位置
    FVector2D NewAnchorPos = FVector2D(
        FMath::Lerp(NewAnchors.Minimum.X, NewAnchors.Maximum.X, 0.5f) * ParentSize.X,
        FMath::Lerp(NewAnchors.Minimum.Y, NewAnchors.Maximum.Y, 0.5f) * ParentSize.Y
    );

    // 保持视觉位置不变：重新计算 Offset
    FVector2D NewOffset = OldWidgetPos - NewAnchorPos;

    FMargin FinalOffsets;
    FinalOffsets.Left = NewOffset.X;
    FinalOffsets.Top = NewOffset.Y;
    FinalOffsets.Right = OldOffsets.Right;
    FinalOffsets.Bottom = OldOffsets.Bottom;

    // 应用偏移和尺寸
    CanvasSlot->SetOffsets(FinalOffsets);
    CanvasSlot->SetAlignment(Alignment); // 保留原本对齐点
}
