// Fill out your copyright notice in the Description page of Project Settings.


#include "DateTimeFuncLib.h"

int64 UDateTimeFuncLib::GetUnixTimestamp()
{
	const FDateTime Time = FDateTime::Now();
	return Time.ToUnixTimestamp();
}

int64 UDateTimeFuncLib::GetTimestamp()
{
	const FDateTime Time = FDateTime::Now();
	int64 Timestamp = Time.ToUnixTimestamp();
	const int32 Millisecond = Time.GetMillisecond();
	Timestamp = Timestamp * 1000 + Millisecond;
	return Timestamp;
}

FString UDateTimeFuncLib::CreateUniqueId(FString Key)
{
	const int64 TimeStamp = GetTimestamp();
	const int32 timestamp = TimeStamp & INT_FAST32_MAX;
	
	const int32 Rand1 = FMath::RandRange(0, timestamp);
	const int32 Rand2 = FMath::RandRange(0, timestamp);
	const int32 Rand3 = FMath::RandRange(0, timestamp);
	FString Id = Key+"_"+FString::FromInt(Rand1)+"_"+FString::FromInt(Rand2)+"_"+FString::FromInt(Rand3);
	
	// UE_LOG(LogTemp, Log, TEXT("---------- GetRandomId ----------"));
	// UE_LOG(LogTemp, Log, TEXT("Rand1:%d"), Rand1);
	// UE_LOG(LogTemp, Log, TEXT("Rand2:%d"), Rand2);
	// UE_LOG(LogTemp, Log, TEXT("Rand3:%d"), Rand3);
	// UE_LOG(LogTemp, Log, TEXT("Rand3:%s"), *Id);
	return Id;
}

FString UDateTimeFuncLib::FormatSecondsToHHMMSS(float seconds, bool useHH)
{
	const int32 Hours = FMath::FloorToInt(seconds / 3600);
	int32 Minutes = FMath::FloorToInt((seconds - Hours * 3600) / 60);
	const int32 Seconds = FMath::FloorToInt(seconds - Hours * 3600 - Minutes * 60);

	FString TimeStr;
	if (useHH)
	{
		TimeStr = FString::Printf(TEXT("%02d:%02d:%02d"), Hours, Minutes, Seconds);
	}
	else
	{
		Minutes = FMath::FloorToInt(seconds / 60);
		TimeStr = FString::Printf(TEXT("%02d:%02d"), Minutes, Seconds);
	}
	return TimeStr;
}
