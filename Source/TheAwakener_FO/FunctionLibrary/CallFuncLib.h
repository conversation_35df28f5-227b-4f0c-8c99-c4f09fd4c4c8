// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/FunctionLibrary/DataFuncLib.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "Kismet/KismetStringLibrary.h"
#include "Kismet/KismetSystemLibrary.h"
#include "CallFuncLib.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UCallFuncLib : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()
	
	template<typename... TReturns, typename... TArgs>
    static void ImplementFunction(UClass* OuterClass, UFunction* Function, TTuple<TReturns...>& OutParams, TArgs&&... Args)
    {
		void* FuncParamsStructAddr = (uint8*)FMemory_Alloca(Function->ParmsSize);
		uint8* OutParamsByte = (uint8*)&OutParams;
		TTuple<TArgs...> InParams(Forward<TArgs>(Args)...);
		uint8* InParamsByte = (uint8*)&InParams;

		if (Function->HasAnyFunctionFlags(FUNC_Native))
		{
			FFrame Frame(nullptr, Function, &FuncParamsStructAddr, nullptr, Function->ChildProperties);
			uint8* ReturnParams = nullptr;

			FOutParmRec** LastOut = &Frame.OutParms;

			for (TFieldIterator<FProperty> ParamIt(Function); ParamIt; ++ParamIt)
			{
				FProperty *Property = *ParamIt;
				if (Property->PropertyFlags & CPF_OutParm)
				{
					if (Property->PropertyFlags & CPF_ReturnParm)
					{
						ReturnParams = OutParamsByte;
						OutParamsByte += Property->GetSize();
					}
					else
					{
						for (;;)
						{
							if (*LastOut == nullptr)
							{
								(*LastOut) = (FOutParmRec*)FMemory_Alloca(sizeof(FOutParmRec));
								(*LastOut)->PropAddr = OutParamsByte;
								(*LastOut)->Property = Property;
								(*LastOut)->NextOutParm = nullptr;
								LastOut = &(*LastOut)->NextOutParm;

								OutParamsByte += Property->GetSize();

								break;
							}
							else
							{
								LastOut = &(*LastOut)->NextOutParm;
							}
						}
					}
				}
				else if(Property->PropertyFlags & CPF_Parm)
				{
					for (;;)
					{
						if (*LastOut == nullptr)
						{
							(*LastOut) = (FOutParmRec*)FMemory_Alloca(sizeof(FOutParmRec));
							(*LastOut)->PropAddr = (uint8*)(Property->ContainerPtrToValuePtr<void*>(&FuncParamsStructAddr));
							if (FObjectProperty*ObjectProperty = CastField<FObjectProperty>(Property))
							{
								ObjectProperty->SetObjectPropertyValue(const_cast<uint8*>((*LastOut)->PropAddr), *(UObject**)InParamsByte);
							}
							else
							{
								FMemory::Memcpy((*LastOut)->PropAddr, InParamsByte, Property->GetSize());
							}

							(*LastOut)->Property = Property;
							(*LastOut)->NextOutParm = nullptr;
							LastOut = &(*LastOut)->NextOutParm;

							InParamsByte += Property->GetSize();

							break;
						}
						else
						{
							LastOut = &(*LastOut)->NextOutParm;
						}
					}
				}
			}

			Function->Invoke(OuterClass, Frame, ReturnParams);
			return;
		}
		
		for (TFieldIterator<FProperty> i(Function); i; ++i)
		{
			FProperty* Property = *i;
			void* PropAddr = Property->ContainerPtrToValuePtr<void*>(FuncParamsStructAddr);
			if (Property->GetFName().ToString().StartsWith("__"))
			{
				//ignore private param like __WolrdContext of function in blueprint funcion library
				continue;
			}
			if (Property->PropertyFlags & CPF_OutParm)
			{
				FMemory::Memcpy(PropAddr, OutParamsByte, Property->GetSize());
				OutParamsByte += Property->GetSize();
			}
			else if (Property->PropertyFlags & CPF_Parm)
			{
				int32 PropSize = Property->GetSize();
				FMemory::Memcpy(PropAddr, InParamsByte, PropSize);
				InParamsByte += Property->GetSize();
			}
		}

		OuterClass->ProcessEvent(Function, FuncParamsStructAddr);

		OutParamsByte = (uint8*)&OutParams;
		for (TFieldIterator<FProperty> i(Function); i; ++i)
		{
			FProperty* Property = *i;
			if (Property->PropertyFlags & CPF_OutParm)
			{
				void* PropAddr = Property->ContainerPtrToValuePtr<void*>(FuncParamsStructAddr);
				FMemory::Memcpy(OutParamsByte, PropAddr, Property->GetSize());
				
				OutParamsByte += Property->GetSize();
			}
		}
    }
	
public:
	template<typename... TReturns, typename... TArgs>
	static void InvokeFunction(UFunction* Func, TTuple<TReturns...>& OutParams, TArgs&&... Args)
	{
		if (Func)
			ImplementFunction<TReturns...>(Func->GetOuterUClass(), Func, OutParams, Forward<TArgs>(Args)...);
		else
			UE_LOG(LogTemp, Error, TEXT("Can not find function: %s"), *Func->GetName());
	}

	// "ExampleClass.Func(1,2,3)","AwScript/Warrior/Aoe.Func(1,2,3)"
	static UFunction* GetUFunction(FString ClassPath, FString FuncName);

	/**
	 *Gather UFunction from Json Function(String)
	 *@param JsonFunction Designer uses string as a function
	 *@return UFunction used in game
	 */
	static  UFunction* JsonFuncToUFunc(FJsonFuncData JsonFunction)
	{
		return UCallFuncLib::GetUFunction(JsonFunction.ClassPath, JsonFunction.FunctionName);
	}

	/**
	 *Gather UFunction from Json Function(String)
	 *@param StrFunc Designer uses string as a function
	 *@return UFunction used in game
	 */
	static UFunction* StringToUFuncion(FString StrFunc)
	{
		return JsonFuncToUFunc(UDataFuncLib::SplitFuncNameAndParams(StrFunc));
	}

	/**
	 *获取一个FJsonFuncData
	 *@param StrFunc 约定的字符串，类文件名.函数名(参数...)
	 */
	static FJsonFuncData StringToJsonFuncData(FString StrFunc)
	{
		return UDataFuncLib::SplitFuncNameAndParams(StrFunc);
	}

	/**
	 *Gather UFunctions from Json Functions(Strings)
	 *@param JsonFunctions Designer uses string as a function
	 *@return UFunctions used in game
	 */
	static  TArray<UFunction*> JsonFuncToUFunc(TArray<FJsonFuncData*> JsonFunctions)
	{
		TArray<UFunction*> Res;
		if (JsonFunctions.Num())
		{
			for (int i = 0; i < JsonFunctions.Num(); i++)
			{
				UFunction* Func; 
				Func = UCallFuncLib::GetUFunction(JsonFunctions[i]->ClassPath, JsonFunctions[i]->FunctionName);
				Res.Add(Func);
			}
		}
		return Res;
	}
};
