// Fill out your copyright notice in the Description page of Project Settings.


#include "FileFuncLib.h"

#include "HAL/PlatformFileManager.h"
#include "Misc/FileHelper.h"

bool UFileFuncLib::SaveTextArray(FString Directory, FString FileName, FString ContentArray, bool bOverWrite)
{
	Directory += "//";
	Directory += FileName;
	TArray<FString> WritingContent;
	WritingContent.Empty();
	if (!bOverWrite)
	{
		if (FPlatformFileManager::Get().GetPlatformFile().FileExists(*Directory))
		{
			FFileHelper::LoadFileToStringArray(WritingContent, *Directory);
		}
	}
	WritingContent.Add(ContentArray);

	return FFileHelper::SaveStringArrayToFile(WritingContent, *Directory);
}
