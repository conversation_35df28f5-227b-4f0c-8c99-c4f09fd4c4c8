// Fill out your copyright notice in the Description page of Project Settings.


#include "ResourceFuncLib.h"
#include "Engine/Texture2D.h"
#include "GameplayFuncLib.h"

FString UResourceFuncLib::GetBpAssetPath(FString Path)
{
	if (Path.IsEmpty())
	{
		//UKismetSystemLibrary::PrintString(GWorld, "Path is empty!!!");
		return "";
	}
	
	Path.RemoveFromStart("/");
	Path.RemoveFromEnd(".uasset");
	TArray<FString> StrArray;
	Path.ParseIntoArray(StrArray,TEXT("/"));
	const FString BpName = StrArray.Last();
	return FString("Blueprint'/Game/"+ Path + "." + BpName + "_C'");
}
 
FString UResourceFuncLib::GetWbpAssetPath(FString Path)
{
	if (Path.IsEmpty())
	{
		//UKismetSystemLibrary::PrintString(GWorld, "Path is empty!!!");
		return "";
	}
	
	Path.RemoveFromStart("/");
	Path.RemoveFromEnd(".uasset");
	TArray<FString> StrArray;
	Path.ParseIntoArray(StrArray,TEXT("/"));
	const FString BpName = StrArray.Last();
	return FString("WidgetBlueprint'/Game/"+ Path + "." + BpName + "_C'");
}

FString UResourceFuncLib::GetAssetPath(FString Path)
{
	if (Path.IsEmpty())
	{
		//UKismetSystemLibrary::PrintString(GWorld, "Path is empty!!!");
		return "";
	}
	
	Path.RemoveFromStart("/");
	TArray<FString> StrArray;
	Path.ParseIntoArray(StrArray,TEXT("/"));
	const FString BpName = StrArray.Last();
	return FString("/Game/"+ Path + "." + BpName);
}

UAnimationAsset* UResourceFuncLib::LoadAnimAsset(FString Path, bool GoGetAssetPath)
{
	const FString AnimAssetPath = GoGetAssetPath ? GetAssetPath(Path) : Path;
	TArray<FString> APath;
	AnimAssetPath.ParseIntoArray(APath,TEXT(" "));
	UAnimationAsset* AnimAsset = LoadObject<UAnimationAsset>(nullptr,*APath[APath.Num() - 1]);
	if (!AnimAsset)
		UE_LOG(LogTemp, Error, TEXT("Load anim asset failed! (path: %s )"), *APath[APath.Num() - 1]);

	return AnimAsset;
}

USoundBase* UResourceFuncLib::LoadSoundBase(FString Path)
{
	const FString AssetPath = GetAssetPath(Path);
	USoundBase* SoundBase = LoadObject<USoundBase>(nullptr,*AssetPath);
	if (!SoundBase)
		UE_LOG(LogTemp, Error, TEXT("Load asset failed! (path: %s )"), *AssetPath);

	return SoundBase;
}

UTexture2D* UResourceFuncLib::LoadTexture2D(FString Path)
{
	const FString AssetPath = GetAssetPath(Path);
	UTexture2D* Texture2D = LoadObject<UTexture2D>(nullptr,*AssetPath);
	if (!Texture2D)
		UE_LOG(LogTemp, Error, TEXT("Load asset failed! (path: %s )"), *AssetPath);

	return Texture2D;
}

AActor* UResourceFuncLib::SpawnActorByBP(FString Path, FTransform Transform)
{
	const FString AssetPath = GetBpAssetPath(Path);
	UClass* BpClass = LoadClass<AActor>(nullptr, *AssetPath);
	AActor* Actor = nullptr;
	if (BpClass)
		Actor = GWorld->SpawnActor<AActor>(BpClass, Transform);
	
	if (!Actor)
		UE_LOG(LogTemp, Error, TEXT("Load asset failed! (path: %s )"), *AssetPath);

	return Actor;
}

FString UResourceFuncLib::GetFileNameByPath(const FString Path)
{
	if (Path.IsEmpty())
	{
		UE_LOG(LogTemp, Error, TEXT("Str is Empty!"));
		return "";
	}
	TArray<FString> StrArray;
	Path.ParseIntoArray(StrArray, TEXT("/"), false);
	const FString NewStr = StrArray[StrArray.Num() - 1];
	StrArray.Empty();
	NewStr.ParseIntoArray(StrArray, TEXT("."), false);
	return StrArray[0];
}

FString UResourceFuncLib::GetClassNameTexPath(const FString Path)
{
	if (Path.IsEmpty())
	{
		UE_LOG(LogTemp, Error, TEXT("Path is Empty!"));
		return "";
	}
	return "/Game/ArtResource/UI/Demo/UI/ChoiceClass/" + Path + "." + GetFileNameByPath(Path);
}

FString UResourceFuncLib::GetSkillIconPath(const FString Path)
{
	if (Path.IsEmpty())
	{
		UE_LOG(LogTemp, Error, TEXT("Path is Empty!"));
		return "";
	}
	return "/Game/ArtResource/UI/Demo/" + Path + "." + GetFileNameByPath(Path);
}

FString UResourceFuncLib::GetThingPackageActorBpPath()
{
	return "Core/Item/Thing/ThingPackage";
}

FActionInfo UResourceFuncLib::ReplaceAnimPathByTypeId(FActionInfo Action, FString PlayerTypeId)
{
	if (PlayerTypeId == "")
		return Action;

	const FString ReplaceFolderPath = "<Type>";
	for (int i = 0; i < Action.Anim.AnimPath.Num(); i++)
	{
		FString AnimPath = Action.Anim.AnimPath[i];
		if (!AnimPath.Contains(ReplaceFolderPath))
			continue;

		FString FolderName = UGameplayFuncLib::GetDataManager()->GetRolePawnByTypeId(PlayerTypeId).FolderName;
		Action.Anim.AnimPath[i] = AnimPath.Replace(*ReplaceFolderPath, *FolderName);
	}
	return Action;
}
