// Fill out your copyright notice in the Description page of Project Settings.


#include "EditorFuncLib.h"

//#include "DataTableEditorUtils.h"

void UEditorFuncLib::DuplicateRowWitchNewRowName(UDataTable* DataTable,FName OldRowName,FName NewRowName)
{
#if WITH_EDITOR
	if (!DataTable)
	{
		return;
	}
	/*
	FDataTableEditorUtils::DuplicateRow(DataTable,OldRowName,NewRowName);
	*/
	// 获取结构体内存
	uint8* OldRowData = *DataTable->GetRowMap().Find(OldRowName);
	
	FTableRowBase* NewRowData = (FTableRowBase*)FMemory::Malloc(DataTable->RowStruct->GetStructureSize());
	DataTable->RowStruct->InitializeStruct(NewRowData);
	DataTable->RowStruct->CopyScriptStruct(NewRowData, OldRowData);
	
	// Add to row 
	DataTable->AddRow(NewRowName,*NewRowData);

#endif
	
}

void UEditorFuncLib::SetRowNewRichTextData(UDataTable* DataTable,FName RowName, FRichTextStyleRow  StructData)
{
#if WITH_EDITOR
	if (!DataTable)
	{
		return;
	}
	DataTable->AddRow(RowName,StructData);
#endif
}

FTextBlockStyle UEditorFuncLib::RichTextStyleRowBreak(FRichTextStyleRow StructData)
{
	return StructData.TextStyle;
}

void UEditorFuncLib::RichTextStyleRowSet(FRichTextStyleRow& StructData, FTextBlockStyle Style)
{
#if WITH_EDITOR
	StructData.TextStyle = Style;
#endif
}
