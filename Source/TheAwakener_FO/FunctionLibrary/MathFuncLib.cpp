// Fill out your copyright notice in the Description page of Project Settings.

#include "MathFuncLib.h"

#include "GameplayFuncLib.h"
#include "Kismet/KismetMathLibrary.h"
#include "TheAwakener_FO/GameFramework/Base/AwGameState.h"

FRotator UMathFuncLib::Aw_RInterp(FRotator CurRot, FRotator TargetRot, float DeltaTime, float InterpSpeed)
{
	FRotator TargetRotator;
	TargetRotator.Roll = GetRotatorShortestAxial(CurRot.Roll, TargetRot.Roll) * DeltaTime * InterpSpeed + CurRot.Roll;
	TargetRotator.Pitch = GetRotatorShortestAxial(CurRot.Pitch, TargetRot.Pitch) * DeltaTime * InterpSpeed + CurRot.Pitch;
	TargetRotator.Yaw = GetRotatorShortestAxial(CurRot.Yaw, TargetRot.Yaw) * DeltaTime * InterpSpeed + CurRot.Yaw;

	return TargetRotator;
}

FRotator UMathFuncLib::Aw_RotatorOffsetNormalize(FRotator CurRot, FRotator TargetRot)
{
	FRotator rot = CurRot;
	FRotator rot2 = TargetRot;
	FRotator diff = rot2 - rot;
	// e.g. -178°-> 178°
	if (FMath::Abs(diff.Pitch) > 180)
	{
		if (diff.Pitch > 0)
		{
			diff.Pitch -= 360;
		}
		else
		{
			diff.Pitch += 360;
		}
	}
	if (FMath::Abs(diff.Yaw) > 180)
		{
		if (diff.Yaw > 0) {
			diff.Yaw -= 360;
		}
		else {
			diff.Yaw += 360;
		}
	}
	if (FMath::Abs(diff.Roll) > 180)
		{
		if (diff.Roll > 0) {
			diff.Roll -= 360;
		}
		else {
			diff.Roll += 360;
		}
	}

	return diff;
}

float UMathFuncLib::GetRotatorShortestAxial(float Current, float Target)
{
	if (Current < 0.0f)
	{
		if (Target <= Current)
		{
			return (Target - Current);
		}
		else
		{
			if ((Current - (Target - 360)) < (Target - Current))
			{
				return (Target - 360 - Current);
			}
			else
			{
				return (Target - Current);
			}
		}
	}
	else if (Current > 0.0f)
	{
		if (Target >= Current)
		{
			return (Target - Current);
		}
		else
		{
			if ((Target + 360 - Current) < (Current - Target))
			{
				return (Target + 360 - Current);
			}
			else
			{
				return (Target - Current);
			}
		}
	}
	return Target;
}

float UMathFuncLib::NormalizeAngle(float AngleValue, int Type)
{
	if (Type == 0)
	{
		// 规整到 0~360
		while (AngleValue < 0)
			AngleValue += 360;
		while (AngleValue > 360)
			AngleValue -= 360;
	}
	else
	{
		// 规整到 -180~180
		while (AngleValue < -180)
			AngleValue += 360;
		while (AngleValue > 180)
			AngleValue -= 360;
	}

	return AngleValue;
}

FVector UMathFuncLib::ProjectionV3ToV3(FVector InputV3, FVector TargetV3)
{
	const float DistSqr = FVector::DistSquared(TargetV3, FVector::ZeroVector);
	if (FMath::IsNearlyEqual(DistSqr, 0))
		return FVector::ZeroVector;

	return FVector::DotProduct(InputV3, TargetV3) / DistSqr * TargetV3;
}

FVector UMathFuncLib::CalculateV3OnAngle(FVector V3, float Angle)
{
	return FVector(V3.X, V3.Y, FVector::Dist(FVector(V3.X, V3.Y, 0), FVector::ZeroVector) * FMath::Tan(PI/180.f * Angle));
}

float UMathFuncLib::GetHorizonAngleByV3(FVector V3)
{
	const float bIsNormalUp = V3.Z >= 0 ? 1 : -1;
	const float NormalAngle = 180.f/PI * FMath::Acos(FVector::DotProduct(V3.GetSafeNormal(), FVector(V3.X, V3.Y, 0).GetSafeNormal()));
	return 90 - bIsNormalUp * NormalAngle;
}

FVector2D UMathFuncLib::RandomV2OnCircle(float Radius)
{
	FVector2D V2 = FVector2D(Radius,0);
	const int RandomDeg = FMath::RandRange(0,360);
	V2.X = FMath::Cos(PI/180.0f*RandomDeg)*Radius;
	V2.Y = FMath::Sin(PI/180.0f*RandomDeg)*Radius;
	return V2;
}

bool UMathFuncLib::AreClockwise(const FVector2D& V1, const FVector2D& V2)
{
	const float Sum = V1.X*V2.Y - V1.Y*V2.X;
	// UE_LOG(LogTemp, Log, TEXT("V1 = %s, V2 = %s, Sum = %f"), *V1.ToString(), *V2.ToString(), Sum);
	return Sum < 0;
}

bool UMathFuncLib::IsWithinRadius(FVector Center, float NearClippingRadius, float FarClippingRadius, FVector TargetPoint)
{
	const float DistSqr = FVector::DistSquared(Center, TargetPoint);
	return DistSqr > NearClippingRadius*NearClippingRadius && DistSqr < FarClippingRadius*FarClippingRadius;
}

bool UMathFuncLib::IsInsideSector(FVector SectorCenter, FVector2D SectorForward, float SectorHeight, float SectorAngle,
	float NearClippingRadius, float FarClippingRadius, FVector TargetPoint)
{
	bool Res = false;
	
	if (TargetPoint.Z >= SectorCenter.Z && TargetPoint.Z <= SectorCenter.Z + SectorHeight && // 在高度内
		IsWithinRadius(FVector(SectorCenter.X, SectorCenter.Y, 0), NearClippingRadius, FarClippingRadius, FVector(TargetPoint.X, TargetPoint.Y, 0))) // 在范围内
	{
		const float Angle = NormalizeAngle(SectorAngle);
		//如果是360度，直接返回true
		if (FMath::IsNearlyEqual(Angle, 360))
		{
			Res = true;
		}
		else
		{
			const FVector LeftForward = FVector(SectorForward.X, SectorForward.Y, 0).RotateAngleAxis(-Angle/2, FVector::UpVector);
			const FVector RightForward = FVector(SectorForward.X, SectorForward.Y, 0).RotateAngleAxis(Angle/2, FVector::UpVector);
			const FVector LeftV = LeftForward.GetSafeNormal() * FarClippingRadius;
			const FVector RightV = RightForward.GetSafeNormal() * FarClippingRadius;
			const FVector ActualV = TargetPoint - SectorCenter;

			//TODO:此处超过180度好像有bug
			if (//不在左边线的左边，并且，在右边线的左边
				!AreClockwise(FVector2D(LeftV), FVector2D(ActualV)) &&
				AreClockwise(FVector2D(RightV), FVector2D(ActualV))
				)
			{
				Res = true;
			}
		}
	}
	else
	{
		Res = false;
	}

	//  ---------- Debug ----------
	// UKismetSystemLibrary::PrintString(GWorld, "==============");
	// const float angle = NormalizeAngle(SectorAngle);
	// FVector LeftF = FVector(SectorForward.X, SectorForward.Y, 0).RotateAngleAxis(-angle/2, FVector::UpVector);
	// FVector RightF = FVector(SectorForward.X, SectorForward.Y, 0).RotateAngleAxis(angle/2, FVector::UpVector);
	// FVector l1 = SectorCenter + LeftF.GetSafeNormal() * NearClippingRadius;
	// FVector l2 = SectorCenter + LeftF.GetSafeNormal() * FarClippingRadius;
	// FVector r1 = SectorCenter + RightF.GetSafeNormal() * NearClippingRadius;
	// FVector r2 = SectorCenter + RightF.GetSafeNormal() * FarClippingRadius;
	// FVector f1 = SectorCenter + FVector(SectorForward.X, SectorForward.Y, 0).GetSafeNormal() * NearClippingRadius;
	// FVector f2 = SectorCenter + FVector(SectorForward.X, SectorForward.Y, 0).GetSafeNormal() * FarClippingRadius;
	// FColor Color = Res ? FColor::Red : FColor::Green;
	// DrawDebugLine(GWorld, SectorCenter, SectorCenter + FVector::UpVector * SectorHeight, Color);
	// DrawDebugLine(GWorld, SectorCenter, f2, Color);
	// DrawDebugLine(GWorld, l1, l2, Color);
	// DrawDebugLine(GWorld, r1, r2, Color);
	// DrawDebugLine(GWorld, l1, f1, Color);
	// DrawDebugLine(GWorld, r1, f1, Color);
	// DrawDebugLine(GWorld, l2, f2, Color);
	// DrawDebugLine(GWorld, r2, f2, Color);
	// --- Debug ---
	
	return Res;
}

float UMathFuncLib::FInterpTo(float Current, float Target, float DeltaTime, float InterpSpeed, float MinSpeed)
{
	// If no interp speed, jump to target value
	if( InterpSpeed <= 0.f )
	{
		return Target;
	}

	// Distance to reach
	const float Dist = Target - Current;

	// If distance is too small, just set the desired location
	if( FMath::Square(Dist) < SMALL_NUMBER )
	{
		return Target;
	}

	// Delta Move, Clamp so we do not over shoot.
	float DeltaMove = Dist * FMath::Clamp<float>(DeltaTime * InterpSpeed, 0.f, 1.f);
	
	if (DeltaMove != 0)
		DeltaMove = FMath::Min(FMath::Max(FMath::Abs(DeltaMove), MinSpeed), FMath::Abs(Dist)) * Dist/FMath::Abs(Dist);
	
	return Current + DeltaMove;
}

FVector UMathFuncLib::VInterpTo(const FVector& Current, const FVector& Target, float DeltaTime, float InterpSpeed, float MinSpeed)
{
	// If no interp speed, jump to target value
	if( InterpSpeed <= 0.f )
	{
		return Target;
	}

	// Distance to reach
	const FVector Dist = Target - Current;

	// If distance is too small, just set the desired location
	if( Dist.SizeSquared() < KINDA_SMALL_NUMBER )
	{
		return Target;
	}

	// Delta Move, Clamp so we do not over shoot.
	FVector	DeltaMove = Dist * FMath::Clamp<float>(DeltaTime * InterpSpeed, 0.f, 1.f);

	if(DeltaMove.X != 0)
		DeltaMove.X = FMath::Min(FMath::Max(FMath::Abs(DeltaMove.X), MinSpeed), FMath::Abs(Dist.X)) * (Dist.X / FMath::Abs(Dist.X));
	if(DeltaMove.Y != 0)
		DeltaMove.Y = FMath::Min(FMath::Max(FMath::Abs(DeltaMove.Y), MinSpeed), FMath::Abs(Dist.Y)) * (Dist.Y / FMath::Abs(Dist.Y));
	if(DeltaMove.Z != 0)
		DeltaMove.Z = FMath::Min(FMath::Max(FMath::Abs(DeltaMove.Z), MinSpeed), FMath::Abs(Dist.Z)) * (Dist.Z / FMath::Abs(Dist.Z));

	// UE_LOG(LogTemp, Log, TEXT("%s"), *DeltaMove.ToString());
	
	return Current + DeltaMove;
}

float UMathFuncLib::GetDegreeBetweenTwoVector(const FVector FirstDir, const FVector SecondDir)
{
	FVector V1 = FirstDir;
	V1.Normalize();
	FVector V2 = SecondDir;
	V2.Normalize();
	float Degree = UKismetMathLibrary::DegAcos(FVector::DotProduct(V1, V2));
	const FVector CrossResult = FVector::CrossProduct(V1, V2);
	if(CrossResult.Z < 0)
	{
		Degree = Degree * -1;
	}
	return Degree;
}

float UMathFuncLib::IsInCube(FVector CubeCenter, FVector CubeExtent, FVector TargetPoint)
{
	if (CubeExtent == FVector::ZeroVector)
		return false;

	if (TargetPoint.X > CubeCenter.X + CubeExtent.X || TargetPoint.X < CubeCenter.X - CubeExtent.X ||
		TargetPoint.Y > CubeCenter.Y + CubeExtent.Y || TargetPoint.Y < CubeCenter.Y - CubeExtent.Y ||
		TargetPoint.Z > CubeCenter.Z + CubeExtent.Z || TargetPoint.Z < CubeCenter.Z - CubeExtent.Z)
		return false;

	return true;
}

TArray<FString> UMathFuncLib::GetRandomIdInPool(TMap<FString, int> Pool, int Count)
{
	TArray<FString> Res;

	// Id - Weight
	for (int i = 0; i < Count; ++i)
	{
		int TotalRate = 0;
		for (const TTuple<FString, int> Each : Pool)
			TotalRate += Each.Value;

		if (TotalRate <= 0)
			break;
		
		int Rand = FMath::RandRange(0, TotalRate);
		
		FString NewId;
		for (const TTuple<FString, int> Each : Pool)
		{
			Rand -= Each.Value;
			if (Rand <= 0)
			{
				Res.Add(Each.Key);
				NewId = Each.Key;
				break;
			}
		}

		Pool.Remove(NewId);
	}

	return Res;
}

TArray<FString> UMathFuncLib::DisorderStrArray(TArray<FString> List)
{
	if (List.Num() <= 1)
		return List;

	TArray<FString> Res;
	while (List.Num() > 0)
	{
		const int RandInt = FMath::RandRange(0, List.Num() - 1);
		Res.Add(List[RandInt]);
		List.RemoveAt(RandInt);
	}

	return Res;
}
