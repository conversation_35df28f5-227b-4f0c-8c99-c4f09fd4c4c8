// Fill out your copyright notice in the Description page of Project Settings.


#include "DataFuncLib.h"

bool UDataFuncLib::CfgValueToBool(FString ConfigValue)
{
	if (ConfigValue.Equals("True") || ConfigValue.Equals("1"))
		return true;
	if (ConfigValue.Equals("False") || ConfigValue.Equals("0"))
		return false;
	
	UE_LOG(LogTemp,Error,TEXT("This Bool Value in config is Illegal param. 这个配置表里的bool参数是非法的。"));
	return false;
}

FString UDataFuncLib::SplitParamBetweenSplitChar(FString InString, FString LeftSplit, FString RightSplit,FString& LeftS,FString& RightS)
{
	FString Result = InString;
	if (InString.Contains(LeftSplit))
	{
		FString LeftString;
		FString RightString;
		InString.Split(LeftSplit,&LeftString,&RightString);
		LeftS = LeftString;
		
		if (RightString.Contains(RightSplit))
		{
			RightString.Split(RightSplit,&LeftString,&RightString);
			RightS = RightString;
		}
		Result = LeftString;
	}
	return  Result;
}

TArray<FString> UDataFuncLib::JsonValueArrayToFStringArray(TArray<TSharedPtr<FJsonValue>> JsonValues)
{
	TArray<FString> Array;
	for (const TSharedPtr<FJsonValue> Value : JsonValues)
		Array.Add(Value->AsString());
	return Array;
}

FVector UDataFuncLib::FStringToV3(FString Str)
{
	if (Str.IsEmpty())
	{
		return FVector();
	}
	FString Left;
	FString Right;
	Str.Split("V3(", &Left, &Right);
	Right.Split(")",&Left, &Right);
	TArray<FString> StrArray;
	Left.ParseIntoArray(StrArray,TEXT(","));
	return FVector(FCString::Atof(*StrArray[0]),FCString::Atof(*StrArray[1]),FCString::Atof(*StrArray[2]));
}

bool UDataFuncLib::ClipAtlerId(FString& MobId)
{
	// 如果ID末尾包含"-????"格式的拓展ID，则访问基础ID数据
	int32 DashIndex = MobId.Find(TEXT("-"), ESearchCase::IgnoreCase, ESearchDir::FromEnd);
	if (DashIndex != INDEX_NONE && DashIndex > 0)
	{
		MobId = MobId.Left(DashIndex);
		return true;
	}
	return false;
}

double UDataFuncLib::AwGetNumberField(TSharedPtr<FJsonObject> JsonObject, const FString& FieldName, double DefaultNumber)
{
	if (!JsonObject.IsValid())
		return DefaultNumber;
	double OutNumber;
	if(!JsonObject->TryGetNumberField(FieldName, OutNumber))
	{
		// debug的时候打开下面的log
		// UE_LOG(LogTemp, Error, TEXT("Failed to get [double] in JsonObject. FieldName is %s"), *FieldName);
		return DefaultNumber;
	}
	return OutNumber;
}

int32 UDataFuncLib::AwGetNumberField(TSharedPtr<FJsonObject> JsonObject, const FString& FieldName, int32 DefaultNumber)
{
	if (!JsonObject.IsValid())
		return DefaultNumber;
	int32 OutNumber;
	if(!JsonObject->TryGetNumberField(FieldName, OutNumber))
	{
		// debug的时候打开下面的log
		// UE_LOG(LogTemp, Error, TEXT("Failed to get [int32] in JsonObject. FieldName is %s"), *FieldName);
		return DefaultNumber;
	}
	return OutNumber;
}

uint32 UDataFuncLib::AwGetNumberField(TSharedPtr<FJsonObject> JsonObject, const FString& FieldName, uint32 DefaultNumber)
{
	if (!JsonObject.IsValid())
		return DefaultNumber;
	uint32 OutNumber;
	if(!JsonObject->TryGetNumberField(FieldName, OutNumber))
	{
		// debug的时候打开下面的log
		// UE_LOG(LogTemp, Error, TEXT("Failed to get [uint32] in JsonObject. FieldName is %s"), *FieldName);
		return DefaultNumber;
	}
	return OutNumber;
}

int64 UDataFuncLib::AwGetNumberField(TSharedPtr<FJsonObject> JsonObject, const FString& FieldName, int64 DefaultNumber)
{
	if (!JsonObject.IsValid())
		return DefaultNumber;
	int64 OutNumber;
	if(!JsonObject->TryGetNumberField(FieldName, OutNumber))
	{
		// debug的时候打开下面的log
		// UE_LOG(LogTemp, Error, TEXT("Failed to get [int64] in JsonObject. FieldName is %s"), *FieldName);
		return DefaultNumber;
	}
	return OutNumber;
}

FString UDataFuncLib::AwGetStringField(TSharedPtr<FJsonObject> JsonObject, const FString& FieldName, FString DefaultString)
{
	if (!JsonObject.IsValid())
		return DefaultString;
	FString OutString;
	if(!JsonObject->TryGetStringField(FieldName, OutString))
	{
		// debug的时候打开下面的log
		// UE_LOG(LogTemp, Error, TEXT("Failed to get [FString] in JsonObject. FieldName is %s"), *FieldName);
		return DefaultString;
	}
	return OutString;
}

TArray<FString> UDataFuncLib::AwGetStringArrayField(TSharedPtr<FJsonObject> JsonObject, const FString& FieldName, TArray<FString> DefaultStringArray)
{
	if (!JsonObject.IsValid())
		return DefaultStringArray;
	TArray<FString> OutArray;
	if (!JsonObject->TryGetStringArrayField(FieldName, OutArray))
	{
		// debug的时候打开下面的log
		// UE_LOG(LogTemp, Error, TEXT("Failed to get [TArray<FString>] in JsonObject. FieldName is %s"), *FieldName);
		return DefaultStringArray;
	}
	return OutArray;
}

TArray<FString> UDataFuncLib::AwGetJsonKeyField(TSharedPtr<FJsonObject> JsonObject)
{
	TArray<FString> Result;
	if (!JsonObject.IsValid())
	{
		return  Result;
	}
	 JsonObject->Values.GenerateKeyArray(Result);
	return Result;
}

bool UDataFuncLib::AwGetBoolField(TSharedPtr<FJsonObject> JsonObject, const FString& FieldName, bool DefaultBool)
{
	if (!JsonObject.IsValid())
		return DefaultBool;
	bool OutBool;
	if (!JsonObject->TryGetBoolField(FieldName, OutBool))
	{
		// debug的时候打开下面的log
		// UE_LOG(LogTemp, Error, TEXT("Failed to get [bool] in JsonObject. FieldName is %s"), *FieldName);
		return DefaultBool;
	}
	return OutBool;
}

TArray<TSharedPtr<FJsonValue>> UDataFuncLib::AwGetArrayField(TSharedPtr<FJsonObject> JsonObject, const FString& FieldName, TArray<TSharedPtr<FJsonValue>> DefaultArray)
{
	if (!JsonObject.IsValid())
		return DefaultArray;
	const TArray<TSharedPtr<FJsonValue>>* OutArray;
	if (!JsonObject->TryGetArrayField(FieldName, OutArray))
	{
		// debug的时候打开下面的log
		// UE_LOG(LogTemp, Error, TEXT("Failed to get [TArray<TSharedPtr<FJsonValue>>] in JsonObject. FieldName is %s"), *FieldName);
		return DefaultArray;
	}
	return *OutArray;
}