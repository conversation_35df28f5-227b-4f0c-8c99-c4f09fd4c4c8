// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Components/RichTextBlock.h"
#include "Kismet/GameplayStatics.h"
#include "CommonFuncLib.generated.h"

/**
 * 一些通用函数和算法
 * by MuXian
 */

UCLASS()
class THEAWAKENER_FO_API UCommonFuncLib : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()

public:
	template <typename T>
	 static bool ArrayContainsArray(TArray<T>Sources,TArray<T>Targets,bool bFullContain = false)
	{
		if (Targets.IsEmpty())
		{
			return  true;
		}
		bool Res = false;
		if (bFullContain)
		{
			Res = true;
			for (auto Target:Targets)
			{
				if (!Sources.Contains(Target))
				{
					Res = false;
					break; ;
				}
			}
		}
		else
		{
			for (auto Target:Targets)
			{
				if (Sources.Contains(Target))
				{
					Res = true;
					break; ;
				}
			}
		}
		return Res;
	}
	
	UFUNCTION(BlueprintCallable)
	static int AliasMethodRandom(TArray<float>WeightPool,bool ConstructRate = false);

	/**
	 * 通用权重抽取函数，支持任意数据结构的数组
	 * @param DataArray 数据数组
	 * @param WeightGetter Lambda表达式，用于从数据结构中获取权重值
	 * @param ConstructRate 是否需要构造权重比例（归一化）
	 * @return 选中元素的索引，如果数组为空返回-1
	 */
	template<typename T>
	static int WeightedRandomSelection(const TArray<T>& DataArray, TFunction<float(const T&)> WeightGetter, bool ConstructRate = true)
	{
		if (DataArray.Num() < 1)
		{
			UE_LOG(LogTemp, Warning, TEXT("WeightedRandomSelection: Data Array is Empty"));
			return -1;
		}

		// 构建权重数组
		TArray<float> WeightPool;
		WeightPool.Reserve(DataArray.Num());

		for (const T& Element : DataArray)
		{
			float Weight = WeightGetter(Element);
			WeightPool.Add(Weight);
		}

		// 使用现有的AliasMethodRandom算法
		return AliasMethodRandom(WeightPool, ConstructRate);
	}

	/**
	 * TMap权重抽取函数，返回选中的Key值
	 * @param WeightMap 权重映射表，Key为字符串，Value为权重数据
	 * @param WeightGetter Lambda表达式，用于从Value中获取权重值
	 * @param ConstructRate 是否需要构造权重比例（归一化）
	 * @return 选中的Key值，如果Map为空返回空字符串
	 */
	template<typename T,typename Tk>
	static Tk WeightedRandomSelectionFromMap(const TMap<Tk, T>& WeightMap, TFunction<float(const T&)> WeightGetter, bool ConstructRate = true)
	{
		if (WeightMap.Num() < 1)
		{
			UE_LOG(LogTemp, Warning, TEXT("WeightedRandomSelectionFromMap: Weight Map is Empty"));
			return Tk();
		}

		// 构建Key数组和权重数组
		TArray<Tk> Keys;
		TArray<float> WeightPool;
		WeightMap.GenerateKeyArray(Keys);
		WeightPool.Reserve(Keys.Num());

		for (const Tk& Key : Keys)
		{
			const T* ValuePtr = WeightMap.Find(Key);
			if (ValuePtr)
			{
				float Weight = WeightGetter(*ValuePtr);
				WeightPool.Add(Weight);
			}
			else
			{
				WeightPool.Add(0.0f);
			}
		}

		// 使用现有的AliasMethodRandom算法
		int SelectedIndex = AliasMethodRandom(WeightPool, ConstructRate);
		if (Keys.IsValidIndex(SelectedIndex))
		{
			return Keys[SelectedIndex];
		}
		UE_LOG(LogTemp,Error, TEXT("WeightedRandomSelectionFromMap: Keys not found"));
		return Tk();
	}

	UFUNCTION(BlueprintCallable)
		static bool SetWindowMode(EWindowMode::Type type);
};


