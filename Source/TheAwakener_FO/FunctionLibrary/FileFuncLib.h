// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "FileFuncLib.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UFileFuncLib : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()
	
	//文件读写及操作
	//写入文件
	UFUNCTION(BlueprintCallable, Category = "File operation")
	static bool SaveTextArray(FString Directory, FString FileName, FString ContentArray, bool bOverWrite);
};
