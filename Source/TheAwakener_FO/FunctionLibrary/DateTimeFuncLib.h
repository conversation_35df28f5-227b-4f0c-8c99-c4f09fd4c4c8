// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "DateTimeFuncLib.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UDateTimeFuncLib : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()
public:
	
	/** @brief 获取当前时间戳(精确到秒) */
	UFUNCTION(BlueprintPure, Category="AW|FunctionLibrary")
	static int64 GetUnixTimestamp();
	
	/** @brief 获取当前时间戳(精确到毫秒) */
	UFUNCTION(BlueprintPure, Category="AW|FunctionLibrary")
	static int64 GetTimestamp();

	/**
	 * @brief 更具当前时间戳获取一个id
	 * @Key 键值，拼在id的开头
	 * @return FString(<0~时间戳的随机数>_<0~时间戳的随机数>_<0~时间戳的随机数>)
	 */
	UFUNCTION(BlueprintPure, Category="AW|FunctionLibrary")
	static FString CreateUniqueId(FString Key);

	UFUNCTION(BlueprintPure, Category="AW|FunctionLibrary")
	static FString FormatSecondsToHHMMSS(float seconds,bool useHH = true);
};
