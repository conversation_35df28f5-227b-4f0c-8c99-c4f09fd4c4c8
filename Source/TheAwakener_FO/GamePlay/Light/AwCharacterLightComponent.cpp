// Fill out your copyright notice in the Description page of Project Settings.


#include "AwCharacterLightComponent.h"

#include "Engine/Level.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"

UAwCharacterLightComponent::UAwCharacterLightComponent()
{
	TickHandle = FTSTicker::GetCoreTicker().AddTicker(FTickerDelegate::CreateUObject(this,&UAwCharacterLightComponent::LightTick),0.0f);
}

void UAwCharacterLightComponent::BeginPlay()
{
	Super::BeginPlay();
	if (IsValid(GetOwner()))
	{
		UActorComponent* Cmp = GetOwner()->GetComponentByClass(USpringArmComponent::StaticClass());
		if (IsValid(Cmp))
		{
			OwnerSpring =Cast<USpringArmComponent>(Cmp);
		}
	}
	InitLightByData();

	UAwSequenceManager* SequenceManager = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwSequenceManager>();
	if (IsValid(SequenceManager)&&bBindSequenceEvent)
	{
		SequenceManager->OnGlobalSequencePlayEvent.AddDynamic(this,&UAwCharacterLightComponent::OnSequencePlay);
		SequenceManager->OnGlobalSequenceStopEvent.AddDynamic(this,&UAwCharacterLightComponent::OnSequenceStop);
	}
}

void UAwCharacterLightComponent::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
	Super::EndPlay(EndPlayReason);
	UAwSequenceManager* SequenceManager = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwSequenceManager>();
	if (IsValid(SequenceManager)&&bBindSequenceEvent)
	{
		SequenceManager->OnGlobalSequencePlayEvent.RemoveDynamic(this,&UAwCharacterLightComponent::OnSequencePlay);
		SequenceManager->OnGlobalSequenceStopEvent.RemoveDynamic(this,&UAwCharacterLightComponent::OnSequenceStop);
	}
}

bool UAwCharacterLightComponent::LightTick(float DeltaTime)
{
	if (OwnerSpring.IsValid()&&IsValid(CurveData))
	{
		AAwCharacter* Character = UGameplayFuncLib::GetClosestPlayerCharacter(OwnerSpring->GetUnfixedCameraPosition());
		const float SpringLength = 
			 OwnerSpring->TargetArmLength -
			 FVector::Distance(OwnerSpring->GetUnfixedCameraPosition(),
							   Character->OwnerPlayerController->PlayerCameraManager->GetCameraLocation());

		float LightPower = CurveData->GetFloatValue(SpringLength);

		SetIntensity(LightPower*BaseLightIntensity);
	}
	return  true;
}


void UAwCharacterLightComponent::InitLightByData()
{
	if (IsValid(GetWorld())&&IsValid(GetWorld()->GetCurrentLevel()))
	{
		FString L,R;
		FString LevelName = 	GetWorld()->GetCurrentLevel()->GetPathName();
		
		LevelName.Split("/", &L, &LevelName, ESearchCase::IgnoreCase, ESearchDir::FromEnd);
		LevelName = UDataFuncLib::SplitParamBetweenSplitChar(LevelName,".",":",L,R);
		if (IsValid(LevelData))
		{
			FString ContextString;
			FLevelLightData* Data = LevelData->FindRow<FLevelLightData>(FName(LevelName),ContextString);
			if (Data)
			{
				BaseLightIntensity = Data->LightIntensity;
			}
		}
	}
}

void UAwCharacterLightComponent::OnSequenceStop(ALevelSequenceActor* SequenceActor)
{
	SetActive(true);
	SetHiddenInGame(false);
}

void UAwCharacterLightComponent::OnSequencePlay(ALevelSequenceActor* SequenceActor)
{
	SetActive(false);
	SetHiddenInGame(true);
}


