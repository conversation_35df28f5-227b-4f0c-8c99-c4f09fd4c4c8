// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "LevelSequenceActor.h"
#include "Components/SpotLightComponent.h"
#include "Containers/Ticker.h"
#include "Engine/DataTable.h"
#include "GameFramework/SpringArmComponent.h"
#include "AwCharacterLightComponent.generated.h"

USTRUCT()
struct FLevelLightData  :public  FTableRowBase
{
	GENERATED_BODY()
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="AnimNotify")
	FString LevelName;
	
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="AnimNotify")
	float  LightIntensity= 1.0f;
	
};


UCLASS( ClassGroup=(Custom), meta=(BlueprintSpawnableComponent) )
class THEAWAKENER_FO_API UAwCharacterLightComponent : public USpotLightComponent
{
	GENERATED_BODY()
public:
	UAwCharacterLightComponent();
	
	UFUNCTION(BlueprintCallable)
	virtual void BeginPlay() override;

	virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;
	
	 bool LightTick(float DeltaTime) ;
	
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	float BaseLightIntensity = 1000;
	
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	UCurveFloat* CurveData = nullptr;

	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	UDataTable* LevelData = nullptr;

	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	bool  bBindSequenceEvent = true;
protected:
	void InitLightByData() ;
private:
	FTSTicker::FDelegateHandle TickHandle;
		TWeakObjectPtr<USpringArmComponent> OwnerSpring = nullptr;

private:
	UFUNCTION()
	void OnSequenceStop(ALevelSequenceActor* SequenceActor);
	UFUNCTION()
	void OnSequencePlay(ALevelSequenceActor* SequenceActor);
};



