// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "ItemModel.h"
#include "TheAwakener_FO/Gameframework/Timeline/TimelineNode.h"
#include "ItemObj.generated.h"

/**
 * 道具使用手法
 */
UENUM(BlueprintType)
enum class EItemUseMethod : uint8
{
	Use,		//使用（大多是喝下去）
	Throw,	//投掷（大多是丢出去）
	Enchant	//涂抹（大多是武器附魔）
};

/**
 * 道具使用的效果，返回值
 * (FItemObj Item, AAwCharacter* User, TArray<FString> DesignerParams)=>FItemUseResult
 */
USTRUCT(BlueprintType)
struct FItemUseResult
{
	GENERATED_BODY()
public:
	//使用成功没有，并不是一定用成功的
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool UseSuccessful = true;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<AActor*> Creaters;
	
	//消耗多少耐久度
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int DurabilityCost = 1;

	//消耗耐久度低于0的话会否删除ItemObj
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool DestroyOnRunningOut = true;

	//产生的Timeline
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<UTimelineNode*> Timelines ;

	FItemUseResult(){};
	FItemUseResult(bool UseSuccess, int CostDurability, bool RemoveOnRunOut, TArray<UTimelineNode*> TimelineNodes = TArray<UTimelineNode*>()):
		UseSuccessful(UseSuccess), DurabilityCost(CostDurability), DestroyOnRunningOut(RemoveOnRunOut), Timelines(TimelineNodes){};

	//合并多个ItemUseResult，产生一个唯一的UseResult结果
	static FItemUseResult Merge(TArray<FItemUseResult> Others)
	{
		bool UseSuccess = false;
		int TotalCost = 0;
		bool RemoveOnRunOut = false;
		TArray<UTimelineNode*> TimelineNodes;
		for (const FItemUseResult UseResult : Others)
		{
			if (UseResult.UseSuccessful == false) continue;
			UseSuccess = true;
			TotalCost += UseResult.DurabilityCost;
			if (UseResult.DestroyOnRunningOut) RemoveOnRunOut = true;
			for (UTimelineNode* Timeline : UseResult.Timelines)
			{
				if (Timeline) TimelineNodes.Add(Timeline);
			} 
		}
		return FItemUseResult(UseSuccess, TotalCost, RemoveOnRunOut, TimelineNodes);
	}
};

/**
 * 
 */
USTRUCT(BlueprintType)
struct FItemObj
{
	GENERATED_BODY()
public:
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FItemModel Model;

	//剩余耐久度
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int Durability = 1;

	FItemObj(){};
	FItemObj(FItemModel ItemModel):Model(ItemModel), Durability(ItemModel.Durability){};
	FItemObj(FItemModel ItemModel, int SetToDurability):Model(ItemModel), Durability(SetToDurability){};

	bool CanBeUsed() const
	{
		return Durability > 0;	
	}
};
