#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/FunctionLibrary/DataFuncLib.h"
#include "TheAwakener_FO/GamePlay/Equipment/Equipment.h"
#include "ItemModel.generated.h"

/**
 * 道具的使用方式信息
 */
USTRUCT(BlueprintType)
struct FItemUseMethod
{
	GENERATED_BODY()
public:
	//使用的动作的ActionId，对应的在那个动作里面会调用这个信息的UseEffect
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString UseActionId;

	/**
	 * 对应的的效果列表，每一个函数都将在Montage中对应的Notify被调用，都是JsonFunc
	 * (FItemObj Item, AAwCharacter* User, TArray<FString> DesignerParams)=>FItemUseResult
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FString> UseEffects;
	
	/**
	 * 如果使用结果导致ItemObj.Durability降低到0或者更低，是否会摧毁这个ItemObj
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	bool DestroyItemOnRunningOut = true;

	FItemUseMethod(){};
	FItemUseMethod(FString ActionId, TArray<FString> UseEffect, bool RemoveOnRunOut):
		UseActionId(ActionId), UseEffects(UseEffect), DestroyItemOnRunningOut(RemoveOnRunOut){};

	static FItemUseMethod FromJson(TSharedPtr<FJsonObject> JsonObj)
	{
		TArray<FString> UseEffect;
		if (JsonObj->HasField("UseEffects"))
		{
			for (const TSharedPtr<FJsonValue, ESPMode::ThreadSafe> UEff : JsonObj->GetArrayField("UseEffects"))
			{
				UseEffect.Add(UEff->AsString());
			} 
		}
		return FItemUseMethod(
			JsonObj->HasField("UseActionId") ? JsonObj->GetStringField("UseActionId") : "",
			UseEffect,
			JsonObj->HasField("RemoveOnUsed") ? JsonObj->GetBoolField("RemoveOnUsed") : true
		);
	}

	bool operator ==(const FItemUseMethod& Other) const
	{
		return this->UseEffects == Other.UseEffects && this->UseActionId == Other.UseActionId;
	}
};

/**
 * 道具的模板信息
 * 为什么没有icon和名字？那是因为那些应该属于背包
 */
USTRUCT(BlueprintType)
struct FItemModel
{
	GENERATED_BODY()
public:
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString Id;
	
	//使用时候的JsonFunc
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FItemUseMethod OnUse;

	//投掷时候的JsonFunc
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FItemUseMethod OnThrow;

	//对自己武器涂抹的JsonFunc
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FItemUseMethod OnEnchant;

	//耐久度，最低得是1
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int Durability = 1;

	//外观显示道具，【！！】这个并不影响两个道具全等
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FEquipmentAppearancePart> AppearanceParts;
	

	bool operator ==(const FItemModel& Other) const
	{
		return this->Id == Other.Id &&
			this->Durability == Other.Durability &&
				this->OnEnchant == Other.OnEnchant &&
					this->OnThrow == Other.OnThrow &&
						this->OnUse == Other.OnUse;
	}

	FItemModel(){};
	FItemModel(FString ItemId, FItemUseMethod UseMethod, FItemUseMethod ThrowMethod, FItemUseMethod EnchantMethod, int DurabilityValue):
		Id(ItemId), OnUse(UseMethod), OnThrow(ThrowMethod), OnEnchant(EnchantMethod), Durability(DurabilityValue){};
	
	static FItemModel FromJson(TSharedPtr<FJsonObject> JsonObj)
	{
		FString ModelId = JsonObj->HasField("Id") ? JsonObj->GetStringField("Id") : "";
		
		FItemUseMethod UseFunc;
		if (JsonObj->HasField("OnUse")) UseFunc = FItemUseMethod::FromJson(JsonObj->GetObjectField("OnUse"));
		
		FItemUseMethod ThrowFunc;
		if (JsonObj->HasField("OnThrow")) ThrowFunc = FItemUseMethod::FromJson(JsonObj->GetObjectField("OnThrow"));
		
		FItemUseMethod EnchantFunc;
		if (JsonObj->HasField("OnEnchant")) EnchantFunc = FItemUseMethod::FromJson(JsonObj->GetObjectField("OnEnchant"));
		
		TArray<FEquipmentAppearancePart> AppearanceParts;
		if (JsonObj->HasField("Appearance"))
		{
			int i = 0;
			for (const TSharedPtr<FJsonValue, ESPMode::ThreadSafe> AppearInfo : JsonObj->GetArrayField("Appearance"))
			{
				FEquipmentAppearancePart AppPart = FEquipmentAppearancePart();
				AppPart.BluePrintPath = AppearInfo->AsObject()->GetStringField("BluePrintPath");
				AppPart.BindPointIds = UDataFuncLib::AwGetStringArrayField(AppearInfo->AsObject(), "BindPointId");
				AppPart.Type = UDataFuncLib::FStringToEnum<EEquipmentAppearanceType>(AppearInfo->AsObject()->GetStringField("AppearanceType"));
				AppPart.PhysicalBoneName = UDataFuncLib::AwGetStringArrayField(AppearInfo->AsObject(), "PhysicalBoneName");
				//PartSlot写死，所以不会互相冲突
				AppPart.PartSlot = FString("SpecSlotFor_ItemInHand_").Append(ModelId).Append("_").Append(FString::FromInt(i));
				//Hide Parts和Show Parts在这里一律不需要
				AppearanceParts.Add(AppPart);
				i++;
			} 
		}
		
		FItemModel Model = FItemModel(
			ModelId, UseFunc, ThrowFunc, EnchantFunc,
			JsonObj->HasField("Durability") ? JsonObj->GetIntegerField("Durability") : 1
		);
		Model.AppearanceParts = AppearanceParts;
		return Model;
	}
};
