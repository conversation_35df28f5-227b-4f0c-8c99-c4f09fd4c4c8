// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/GameFramework/Base/AwGameModeBase.h"
#include "TheAwakener_FO/GamePlay/Map/DungeonMap.h"
#include "AwGameMode_RandomDungeon.generated.h"

/**
 * 
 */
 //DungeonLevel全部LoadFinish时的动态多播委托
DECLARE_DYNAMIC_MULTICAST_DELEGATE(FAllLoadFinishDelegate);
UCLASS()
class THEAWAKENER_FO_API AAwGameMode_RandomDungeon : public AAwGameModeBase
{
	GENERATED_BODY()
public:
	virtual	void BeginPlay() override;

	virtual void SyncDataToGameState(AAwGameState* AwGameState) override;

	virtual void Tick(float DeltaSeconds) override;

	// void NewPlayerInit_Implementation(AAwPlayerController* NewPlayer, FTransform PlayerStart);
	
	virtual void Logout(AController* Exiting) override;

	virtual void PlayerClientLoadFinish(AAwPlayerController* Player) override;

	void LeaveDungeon();

	TMap<FString, FString> GetTriggeredCampEvent(FString DungeonId);

	//暂时弃用
	void AddCampEventRecord();

	//获取怪物掉落Id
	TArray<FString> GetMobLootPackages_Implementation();
	//获取宝箱掉落Id
	TArray<FString> GetChestLootPackages_Implementation();

	//蓝图回调
	UFUNCTION(BlueprintImplementableEvent)
	void OnPlayerLoadAllLevelFinish();

	//Switch改变时的动态多播委托
	UPROPERTY(BlueprintAssignable, Category = "Events")
	FAllLoadFinishDelegate AllLevelLoadFinishDelegate;

protected:

	void GenerateRandomMap();

	void RandomNewMap(FString DungeonName);

	void AllPlayerLoadLevel();

	void SetPlayerToStartRoom(AAwPlayerController* Player);

	FVector GetPlayerSpawnPoint(FString RoomId);

	void GetCampProgressInDungeonSave();

public:
	UPROPERTY(BlueprintReadOnly)
	UAwGameInstance* GameIns = nullptr;

	TArray<AAwPlayerController*> PlayerList;
	UPROPERTY(BlueprintReadOnly)
	FAwDungeonModel CurDungeonInfo;
	UPROPERTY(BlueprintReadOnly)
	TArray<FDungeonTile> RoomList;
	UPROPERTY(BlueprintReadOnly)
	TArray<FDungeonRoadInfo> RoadList;

	UPROPERTY(BlueprintReadWrite)
	int RandNewMapTimes = 1000;

	UPROPERTY(BlueprintReadWrite)
	TMap<FString, int> CampProgressWhenEnterDungeon;

protected:
	float LastTickInterval = 0;

	TMap<FString, FString> CurTriggeEventList;
};
