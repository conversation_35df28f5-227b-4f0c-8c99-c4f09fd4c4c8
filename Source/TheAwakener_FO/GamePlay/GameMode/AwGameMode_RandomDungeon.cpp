// Fill out your copyright notice in the Description page of Project Settings.


#include "AwGameMode_RandomDungeon.h"

#include "NetworkReplayStreaming.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"

void AAwGameMode_RandomDungeon::BeginPlay()
{
	Super::BeginPlay();
	GameIns = UGameplayFuncLib::GetAwGameInstance();
	if (GameIns)
	{
		CurDungeonInfo = UGameplayFuncLib::GetAwDataManager()->GetDungeonModelById(GameIns->CurDungeonName);
		CurTriggeEventList = GetTriggeredCampEvent(CurDungeonInfo.Id);
		GenerateRandomMap();
		
		AllPlayerLoadLevel();
	}

}

void AAwGameMode_RandomDungeon::SyncDataToGameState(AAwGameState* AwGameState)
{
	Super::SyncDataToGameState(AwGameState);
	if (AwGameState)
	{
		AwGameState->DungeonRoomsInfo = RoomList;
		AwGameState->DungeonRoadsInfo = RoadList;
		UKismetSystemLibrary::PrintString(this, FString("SyncDungeonDataToGameStateSuccess"));
		//PlayerLoadLevel
		// for (auto& Player : PlayerList)
		// {
		// 	if (Player)
		// 	{
		// 		UKismetSystemLibrary::PrintString(this, Player->GetName().Append(FString(" LoadDungeonAlllLevel!")));
		// 		Player->LoadDungeonAllRoomLevel();
		// 	}
		// 		
		// }
	}
}

void AAwGameMode_RandomDungeon::Tick(float DeltaSeconds)
{
	Super::Tick(DeltaSeconds);
}

void AAwGameMode_RandomDungeon::GenerateRandomMap()
{
	if(GameIns)
	{
		FAwDungeonSave DungeonRecord = GameIns->RoleInfo.GetDungeonRecordByDungeonId(CurDungeonInfo.Id);
		if(DungeonRecord.DungeonId != "")
		{
			UFunction* Func;
			Func = UCallFuncLib::GetUFunction(CurDungeonInfo.RandomMapCondition.ClassPath, CurDungeonInfo.RandomMapCondition.FunctionName);
			if(Func)
			{
				TArray<FString> TriggeredEvents;
				CurTriggeEventList.GetKeys(TriggeredEvents);
				struct
				{
					FAwDungeonSave DungeonRecord;
					TArray<FString> EventList;
					TArray<FString> Params;
					bool Result = false;
				}FuncParam;
				FuncParam.DungeonRecord = DungeonRecord;
				FuncParam.EventList = TriggeredEvents;
				FuncParam.Params = CurDungeonInfo.RandomMapCondition.Params;
				this->ProcessEvent(Func, &FuncParam);
				if(FuncParam.Result)
				{
					RandomNewMap(CurDungeonInfo.Id);
					return;
				}
			}
			if(DungeonRecord.RoadList.Num() && DungeonRecord.RoomList.Num())
			{
				RoomList = DungeonRecord.RoomList;
				RoadList = DungeonRecord.RoadList;
				//UKismetSystemLibrary::PrintString(this, FString("Old DungeonLevel Num : ").Append(FString::FromInt(RoomList.Num())));
				return;
			}
		}
	}
	RandomNewMap(CurDungeonInfo.Id);
	
}

void AAwGameMode_RandomDungeon::RandomNewMap(FString DungeonName)
{
	RoomList.Empty();
	RoadList.Empty();
	TArray<FString> TriggeredEvents;
	CurTriggeEventList.GetKeys(TriggeredEvents);
	bool GenerateSuccess = UDungeonMap::GenerateDungeonMap(DungeonName,TriggeredEvents, RoomList, RoadList);
	for(int i = 0; i < RandNewMapTimes; i++)
	{
		if(!GenerateSuccess)
		{
			RoomList.Empty();
			RoadList.Empty();
			GenerateSuccess = UDungeonMap::GenerateDungeonMap(DungeonName,TriggeredEvents, RoomList, RoadList);
		}
		else
			break;
	}
	if (!GenerateSuccess)
	{
		RoomList.Empty();
		RoadList.Empty();
		GenerateSuccess = UDungeonMap::GenerateDungeonMap(DungeonName,TriggeredEvents, RoomList, RoadList, true);
	}
	if (GenerateSuccess)
	{
		if(GameIns)
		{
			bool HasRecord = false;
			for(int i = 0; i< GameIns->RoleInfo.DungeonRecords.Num(); i++)
			{
				if(GameIns->RoleInfo.DungeonRecords[i].DungeonId == DungeonName)
				{
					GameIns->RoleInfo.DungeonRecords[i].RoomList = RoomList;
					GameIns->RoleInfo.DungeonRecords[i].RoadList = RoadList;
					//UKismetSystemLibrary::PrintString(this, FString("New DungeonLevel Num : ").Append(FString::FromInt(RoomList.Num())));
					HasRecord = true;
					break;
				}
			}
			if(!HasRecord)
			{
				FAwDungeonSave DungeonRecord = FAwDungeonSave();
				DungeonRecord.DungeonId = DungeonName;
				DungeonRecord.RoomList = RoomList;
				DungeonRecord.RoadList = RoadList;
				//UKismetSystemLibrary::PrintString(this, FString("New DungeonLevel Num : ").Append(FString::FromInt(RoomList.Num())));
				for(auto CurCamp : CurDungeonInfo.Camps)
				{
					FAwDungeonCampSave NewCampSave = FAwDungeonCampSave();
					NewCampSave.CampId = CurCamp.CampId;
					NewCampSave.CampProgress = CurCamp.StartProgress;
					NewCampSave.LastCampProgress = NewCampSave.CampProgress;
					DungeonRecord.Camps.Add(NewCampSave);
				}
				GameIns->RoleInfo.DungeonRecords.Add(DungeonRecord);
			}
			UGameplayFuncLib::SaveGame();
			UKismetSystemLibrary::PrintString(this, FString("RandomNewMapSuccess"));
			return;
		}
	}
	UKismetSystemLibrary::PrintString(this, FString("RandomNewMapFailure"));
}

// void AAwGameMode_RandomDungeon::NewPlayerInit_Implementation(AAwPlayerController* NewPlayer, FTransform PlayerStart)
// {
// 	//Super::NewPlayerInit(NewPlayer);
//
// 	NewPlayer->InitPlayerCharacter(PlayerStart);
// 	PlayerList.Add(NewPlayer);
// 	UGameplayFuncLib::GetMyAwPlayerController()->SetInputEnable(false);
//
// 	// AAwGameState* AwGameState = Cast<AAwGameState>(UGameplayStatics::GetGameState(GWorld));
// 	// if (AwGameState)
// 	// {
// 	// 	if(AwGameState->DungeonRoomsInfo.Num())
// 	// 		NewPlayer->LoadDungeonAllRoomLevel();
// 	// }
// }

void AAwGameMode_RandomDungeon::Logout(AController* Exiting)
{
	Super::Logout(Exiting);
	AAwPlayerController* LogoutPlayer = Cast<AAwPlayerController>(Exiting);
	if (LogoutPlayer)
	{
		LogoutPlayer->LeaveDungeon(CurDungeonInfo.Id);
		PlayerList.Remove(LogoutPlayer);
	}	
}

void AAwGameMode_RandomDungeon::AllPlayerLoadLevel()
{
	for (AAwPlayerController* Player : PlayerList)
	{
		Player->LoadDungeonAllRoomLevel();
	}
}

void AAwGameMode_RandomDungeon::PlayerClientLoadFinish(AAwPlayerController* Player)
{
	SetPlayerToStartRoom(Player);
	//AddCampEventRecord();
	GetCampProgressInDungeonSave();
	UGameplayFuncLib::GetAwGameInstance()->RoleInfo.SwitchJustModified.Add("MineStoryStep",false);
	OnPlayerLoadAllLevelFinish();
	this->AllLevelLoadFinishDelegate.Broadcast();
}

void AAwGameMode_RandomDungeon::SetPlayerToStartRoom(AAwPlayerController* Player)
{
	UAwGameInstance* GameInstance = UGameplayFuncLib::GetAwGameInstance();
	if (GameInstance)
	{
		if(GameInstance->DungeonRoomLevelList.Num())
		{
			TArray<FString> RoomNames;
			GameInstance->DungeonRoomLevelList.GetKeys(RoomNames);
			FVector SpawnLoc = GetPlayerSpawnPoint(RoomNames[0]);
			Player->CurCharacter->SetActorLocation(SpawnLoc);
			Player->CurCharacter->SetActorRotation(FRotator(0,90,0));
			Player->CurCharacter->GetAwCameraComponent()->SetCameraRotationByForward(FRotator(0,90,0).Vector());
		}
	}
}

FVector AAwGameMode_RandomDungeon::GetPlayerSpawnPoint(FString RoomId)
{
	TArray<FVector> PointList;
	for (auto& Point : UGameplayFuncLib::GetAwGameState()->MapPointList)
	{
		if (Point.RoomId == RoomId && Point.Id.Contains("PlayerSpawnPoint"))
			PointList.Add(Point.Location);
	}
	if (PointList.Num())
	{
		return PointList[FMath::RandRange(0, PointList.Num() - 1)];
	}
	return FVector(0, 0, 100);
}

void AAwGameMode_RandomDungeon::GetCampProgressInDungeonSave()
{
	FAwDungeonSave CurDungeonSave = FAwDungeonSave();
	for(auto DungeonRecord : GameIns->RoleInfo.DungeonRecords)
	{
		if(DungeonRecord.DungeonId == GameIns->CurDungeonName)
		{
			CurDungeonSave = DungeonRecord;
			break;
		}
	}
	if(CurDungeonSave.DungeonId != "")
	{
		for(auto CurCampSave : CurDungeonSave.Camps)
		{
			CampProgressWhenEnterDungeon.Add(CurCampSave.CampId, CurCampSave.CampProgress);
		}
	}
}

void AAwGameMode_RandomDungeon::LeaveDungeon()
{
	for (AAwPlayerController* Player : PlayerList)
	{
		Player->LeaveDungeon(CurDungeonInfo.Id);
	}
	UAwGameInstance* GameInstance = UGameplayFuncLib::GetAwGameInstance();
	if (GameInstance)
	{
		UGameplayFuncLib::SaveGame();
		GameInstance->ChangeLevelByLevelName(GameInstance->LastLevelName, true, "listen");
	}
}

TMap<FString, FString> AAwGameMode_RandomDungeon::GetTriggeredCampEvent(FString DungeonId)
{
	TMap<FString, FString> CampEventList;
	if(GameIns)
	{
		FAwDungeonSave DungeonRecord = GameIns->RoleInfo.GetDungeonRecordByDungeonId(DungeonId);
		for(auto Camp : DungeonRecord.Camps)
		{
			for(auto CampModel : CurDungeonInfo.Camps)
			{
				if(Camp.CampId == CampModel.CampId)
				{
					for(auto CampEvent : CampModel.EventList)
					{
						if(CampEvent.ProgressMin <= Camp.CampProgress && CampEvent.ProgressMax >= Camp.CampProgress)
						{
							bool CanTriggeEvent = true;
							for(auto EventRecord : Camp.EventRecordList)
							{
								if(EventRecord.EventId == CampEvent.CampEventId)
								{
									if(EventRecord.TriggerTime >= CampEvent.Limit)
										CanTriggeEvent = false;
									break;
								}
							}
							if(CanTriggeEvent)
								CampEventList.Add(CampEvent.CampEventId, CampModel.CampId);
						}
					}
					break;
				}
			}
		}
	}
	return CampEventList;
}

void AAwGameMode_RandomDungeon::AddCampEventRecord()
{
	TArray<FString> TriggeredEvents;
	CurTriggeEventList.GetKeys(TriggeredEvents);
	if(!TriggeredEvents.Num()) return;
	FAwDungeonSave DungeonRecord = GameIns->RoleInfo.GetDungeonRecordByDungeonId(CurDungeonInfo.Id);
	TArray<FAwDungeonCampSave> CampSaveList;
	//已记录过的Event次数加一
	for(auto CampRecord : DungeonRecord.Camps)
	{
		FAwDungeonCampSave CampSave = CampRecord;
		for(int i = 0; i < CampSave.EventRecordList.Num(); i++)
		{
			if(TriggeredEvents.Contains(CampSave.EventRecordList[i].EventId))
			{
				CampSave.EventRecordList[i].TriggerTime++;
				TriggeredEvents.Remove(CampSave.EventRecordList[i].EventId);
			}
		}
		CampSaveList.Add(CampSave);
	}
	//没记录过的Event添加记录
	if(TriggeredEvents.Num())
	{
		for(FString EventId : TriggeredEvents)
		{
			for(int i = 0; i < CampSaveList.Num(); i++)
			{
				if(CampSaveList[i].CampId == CurTriggeEventList[EventId])
				{
					FCampEventRecord NewRecord = FCampEventRecord();
					NewRecord.EventId = EventId;
					NewRecord.TriggerTime = 1;
					CampSaveList[i].EventRecordList.Add(NewRecord);
					break;
				}
			}
		}
	}
	//把记录保存到Role中
	for(int i = 0; i < GameIns->RoleInfo.DungeonRecords.Num(); i++)
	{
		if(GameIns->RoleInfo.DungeonRecords[i].DungeonId == CurDungeonInfo.Id)
		{
			GameIns->RoleInfo.DungeonRecords[i].Camps = CampSaveList;
			break;
		}
	}
}

TArray<FString> AAwGameMode_RandomDungeon::GetMobLootPackages_Implementation()
{
	return CurDungeonInfo.MobLootTag;
}

TArray<FString> AAwGameMode_RandomDungeon::GetChestLootPackages_Implementation()
{
	return CurDungeonInfo.ChestLootTag;
}
