// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/Interface.h"
#include "DialogBubbleInterface.generated.h"

/**
 * 
 */


UINTERFACE(MinimalAPI)
class UDialogBubbleWidgetInterface : public UInterface
{
	GENERATED_BODY()
};

class THEAWAKENER_FO_API IDialogBubbleWidgetInterface
{
	GENERATED_BODY()

		// Add interface functions to this class. This is the class that will be inherited to implement this interface.
public:
	UFUNCTION(BlueprintCallable,BlueprintNativeEvent)
	void AddNewDialogBubble(const FString& DialogString,float LifeTime,float FadeIn,float FadeOut);

	UFUNCTION(BlueprintCallable, BlueprintNativeEvent)
	void StopCurDialogBubble();

	UFUNCTION(BlueprintCallable, BlueprintNativeEvent)
	void PauseDialogBubble(bool IsPaused);

	UFUNCTION(BlueprintCallable, BlueprintNativeEvent)
	void RemoveAllDialogBubble();
};

