// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "DialogStructs.h"
#include "Camera/CameraComponent.h"
#include "GameFramework/Actor.h"
#include "TheAwakener_FO/FunctionLibrary/MathFuncLib.h"
#include "TheAwakener_FO/UI/GameMain/DialogUI.h"
#include "DialogModeActor.generated.h"

//对话模式的相机（也就是我——伟大的对话系统）的移动信息
USTRUCT()
struct FDialogCameraMovePlan
{
	GENERATED_BODY()
public:
	UPROPERTY()
	bool Active = false;
	UPROPERTY()
	FTransform StartFrom = FTransform::Identity;
	UPROPERTY()
	FTransform Target = FTransform::Identity;
	UPROPERTY()
	float InSec = 0;
	UPROPERTY()
	float TimeElapsed = 0;

	FDialogCameraMovePlan(){};
	FDialogCameraMovePlan(FTransform ToTrans, float MoveInSec):
		Active(true), Target(ToTrans), InSec(MoveInSec), TimeElapsed(0){};

	FTransform ThisTickMove(float DeltaTime);
};

//显示文字的打字机效果的信息
USTRUCT()
struct FDialogTextShowing
{
	GENERATED_BODY()
public:
	UPROPERTY()
	bool Active = false;
	
	//显示了多少字了，因为傻屌的UE的deltaTime问题，只能用float，显示的时候转int就好
	UPROPERTY()
	float ShowTexts = 0;

	//总共多少字要显示，每次都数长度总是不对的嘛
	UPROPERTY()
	int TotalTexts = 0;

	//每秒显示多少个字儿，为什么是float……？？
	UPROPERTY()
	float ChaPerSec = 100.f;

	//偷懒
	UPROPERTY()
	FString TextSpeaker;
	TArray<TCHAR> TextDialog;

	FDialogTextShowing(){};
	FDialogTextShowing(FString Text, float ShowPerSec = 100.f):
		Active(true), ShowTexts(0), TotalTexts(Text.Len()),
		ChaPerSec(ShowPerSec), TextDialog(Text.GetCharArray()){};

	//每帧运行，返回是否显示完了
	bool RunTick(float DeltaTime);
};

// 开始聊天的时候角色的面向旋转
USTRUCT()
struct FDialogChaFaceTo
{
	GENERATED_BODY()
public:
	bool IsInDialog = false;
	// 原来的面向
	FVector OriFaceTo = FVector::ZeroVector;
	// 目标面向
	FVector TargetFaceTo = FVector::ZeroVector;

	FString ActionId_TurnLeft = "";
	FString ActionId_TurnRight = "";
	
	bool IsTurnRight() const
	{
		return UMathFuncLib::AreClockwise(FVector2D(OriFaceTo), FVector2D(TargetFaceTo));
	}
};

//结束对话时的动态委托
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam( FEndDialogDelegate, ADialogModeActor* , DialogActor);
/**
 * 没想到我堂堂的对话系统，竟然只是一台摄像机
 */
UCLASS(BlueprintType)
class THEAWAKENER_FO_API ADialogModeActor : public AActor
{
	GENERATED_BODY()
protected:
	virtual void BeginPlay() override;
private:
	UPROPERTY()
	FDialogCameraMovePlan MovePlan;
	UPROPERTY()
	FDialogTextShowing TypingInfo;
	
	UPROPERTY()
	FDialogScriptObj Dialog;
	
	UPROPERTY()
	UCameraComponent* Camera;

	UPROPERTY()
	EDialogEventClipType ClipPlaying = EDialogEventClipType::None;
	
	void PlayCurrentClip();	//开始运行当前DialogEventClip
	void EndCurrentClip();	//结束当前DialogEventClip，寻找下一个，决定是否要出

	// 当前表演的内容焦点角色
	UPROPERTY()
	AAwCharacter* CurrentEventFocusCha = nullptr;
	// 当前表演的焦点相机位置
	UPROPERTY()
	UCloseUpShotCamera* CurrentEventFocusCam = nullptr;
	UPROPERTY()
	UAudioComponent* CurVoiceAudio = nullptr;
	// 开始聊天的时候角色的面向旋转
	UPROPERTY()
	TMap<AAwCharacter*, FDialogChaFaceTo> ChaFaceTo;
	
	UPROPERTY()
	TArray<AActor*> HiddenActors;	//隐藏中的角色

	/**
	 * 根据焦点角色获得该隐藏谁，然后隐藏她们
	 * @param FocusGuy 焦点角色
	 * @param LineDistance 这个角色距离屏幕的距离（其实就是相机的Size嘛）
	 */
	void CheckForHideActors(AAwCharacter* FocusGuy, float LineDistance);
	void RestoreAllActorsVisible();

	UPROPERTY()
	bool Selecting = false;	//是否在选择对话
	UPROPERTY()
	float DelayToNextCmd = 0;	//显示轮盘还要多久
	UPROPERTY()
	float EachCmdDelay = 0;//0.15f;	//每次按确定之后等多久，这个
	
	UPROPERTY()
	float WaitSec = 0;	//等待多久后继续

	UFUNCTION()
	void SetDialogChaFaceTo(const AAwCharacter* Character, bool IsInDialog, FString LeftActionId = "", FString RightActionId = "");
public:
	ADialogModeActor();
	virtual void Tick(float DeltaTime) override;

	void StartDialog(FDialogScriptObj DialogObj);

	UFUNCTION(BlueprintCallable)
	void EndDialog();

	UPROPERTY(BlueprintReadOnly)
	FDialogScriptModel ScriptModel;

	//UI的指针
	UPROPERTY()
	UDialogUI* UI = nullptr;

	//对话结束时的动态委托
	UPROPERTY(BlueprintAssignable, Category = "Events")
	FEndDialogDelegate EndDialogDelegate;

	//为了实现对话中选项选择后跳出另外一个UI去解锁职业，解锁成功后返回对话自动开始接下面的对话，所以把Selection里的NextEventFunc的功能写一个蓝图调用
	UFUNCTION(BlueprintCallable)
	void DoSelectionGoToNextClipFunc(FString NextEventFunc);
};
