// Fill out your copyright notice in the Description page of Project Settings.


#include "DialogStructs.h"

#include "TheAwakener_FO/FunctionLibrary/DataFuncLib.h"

FDialogEventClip FDialogEventClip::FromJson(TSharedPtr<FJsonObject> JsonObj)
{
	const EDialogEventClipType ClipT = UDataFuncLib::AwGetEnumField(JsonObj, "Type", EDialogEventClipType::FocusTarget);
	FDialogEventClip Res;
	switch (ClipT)
	{
	case EDialogEventClipType::FocusTarget:
		return FDialogEventClip(
		UDataFuncLib::AwGetStringField(JsonObj, "Id", ""),
		UDataFuncLib::AwGetStringField(JsonObj, "NextId", ""),
		UDataFuncLib::AwGetEnumField(JsonObj, "TargetType", EDialogTargetType::Player),
		UDataFuncLib::AwGetNumberField(JsonObj, "ChaSlot", 0),
		UDataFuncLib::AwGetN<PERSON>berField(JsonObj, "CameraSlot", 0),
		JsonObj->HasField("InSec") ? JsonObj->GetNumberField("InSec") : 0
		);
	case EDialogEventClipType::Speak:
		return FDialogEventClip(
		UDataFuncLib::AwGetStringField(JsonObj, "Id", ""),
		UDataFuncLib::AwGetStringField(JsonObj, "NextId", ""),
		UDataFuncLib::AwGetEnumField(JsonObj, "TargetType", EDialogTargetType::Player),
		UDataFuncLib::AwGetNumberField(JsonObj, "Slot", 0),
		UDataFuncLib::AwGetStringField(JsonObj, "Name", ""),
		UDataFuncLib::AwGetStringField(JsonObj, "Text", "")
		);
	case  EDialogEventClipType::DoAction:
		return FDialogEventClip(
			UDataFuncLib::AwGetStringField(JsonObj, "Id", ""),
			UDataFuncLib::AwGetStringField(JsonObj, "NextId", ""),
			UDataFuncLib::AwGetStringField(JsonObj, "TodoActionId", ""),
	UDataFuncLib::AwGetEnumField(JsonObj, "TargetType", EDialogTargetType::Player),
	UDataFuncLib::AwGetNumberField(JsonObj, "Slot", 0)
		);
	case EDialogEventClipType::Wait:
		return FDialogEventClip(
		UDataFuncLib::AwGetStringField(JsonObj, "Id", ""),
		UDataFuncLib::AwGetStringField(JsonObj, "NextId", ""),
		UDataFuncLib::AwGetNumberField(JsonObj, "WaitSec", 0.0f)
		);
	case EDialogEventClipType::TurnToPlayer:
		return FDialogEventClip(
			UDataFuncLib::AwGetStringField(JsonObj, "Id", ""),
			UDataFuncLib::AwGetStringField(JsonObj, "NextId", ""),
			UDataFuncLib::AwGetEnumField(JsonObj, "TargetType", EDialogTargetType::Target),
			UDataFuncLib::AwGetStringField(JsonObj, "ActionId_TurnLeft", ""),
			UDataFuncLib::AwGetStringField(JsonObj, "ActionId_TurnRight", ""),
			UDataFuncLib::AwGetNumberField(JsonObj, "WaitSec", 0.0f)
		);
	case EDialogEventClipType::PlayAudio:
		return FDialogEventClip(
			UDataFuncLib::AwGetStringField(JsonObj, "Id", ""),
			UDataFuncLib::AwGetStringField(JsonObj, "NextId", ""),
			UDataFuncLib::AwGetEnumField(JsonObj, "TargetType", EDialogTargetType::Target),
			UDataFuncLib::AwGetStringField(JsonObj, "AudioId", "")
		);
	case EDialogEventClipType::HideDialogUI:
		return FDialogEventClip(
		UDataFuncLib::AwGetStringField(JsonObj, "Id", ""),
		UDataFuncLib::AwGetStringField(JsonObj, "NextId", "")
		);
	case EDialogEventClipType::GiveCurrencyToNPC:
		return FDialogEventClip(
		UDataFuncLib::AwGetStringField(JsonObj, "Id", ""),
		UDataFuncLib::AwGetStringField(JsonObj, "NextId", ""),
		UDataFuncLib::AwGetStringArrayField(JsonObj, "Params")
		);
	default:break;
	}
	return Res;
}

FDialogSelection FDialogSelection::FromJson(TSharedPtr<FJsonObject> JsonObj)
{
	return FDialogSelection(
	UDataFuncLib::AwGetStringArrayField(JsonObj, "Conditions"),
	UDataFuncLib::AwGetStringArrayField(JsonObj, "EnableChecks"),
	UDataFuncLib::AwGetStringField(JsonObj, "Icon", ""),
	UDataFuncLib::AwGetStringField(JsonObj, "Text", ""),
	UDataFuncLib::AwGetStringField(JsonObj, "NextEventFunc", ""),
	UDataFuncLib::AwGetStringArrayField(JsonObj, "Actions"),
	UDataFuncLib::AwGetBoolField(JsonObj, "StillInDialog", false)
	);
}

FDialogScriptClip FDialogScriptClip::FromJson(TSharedPtr<FJsonObject> JsonObj)
{
	TMap<FString, FDialogEventClip> ResDialogs;
	if (JsonObj->HasField("Dialogs"))
	{
		for (const TSharedPtr<FJsonValue, ESPMode::ThreadSafe> DArr : JsonObj->GetArrayField("Dialogs"))
		{
			FDialogEventClip EventClip = FDialogEventClip::FromJson(DArr->AsObject());
			if (EventClip.Id.IsEmpty() == false)
			{
				ResDialogs.Add(EventClip.Id, EventClip);
			}
			
		}
	}
	TArray<FString> Conditions = UDataFuncLib::AwGetStringArrayField(JsonObj, "Conditions");
	TArray<FDialogSelection> ResSelections;
	if (JsonObj->HasField("Selections"))
	{
		for (const TSharedPtr<FJsonValue, ESPMode::ThreadSafe> SArr : JsonObj->GetArrayField("Selections"))
		{
			ResSelections.Add(FDialogSelection::FromJson(SArr->AsObject()));
		}
	}

	TArray<FString> EndScript = UDataFuncLib::AwGetStringArrayField(JsonObj, "EndClipScript");
	
	return FDialogScriptClip(
		UDataFuncLib::AwGetStringField(JsonObj, "Id"),
		UDataFuncLib::AwGetStringField(JsonObj, "FirstEvent"),
		UDataFuncLib::AwGetStringField(JsonObj, "FirstEventFunc"),
		Conditions,
		ResDialogs,
		ResSelections,
		EndScript
	);
}

FDialogScriptModel FDialogScriptModel::FromJson(TSharedPtr<FJsonObject> JsonObj)
{
	TMap<FString, FDialogScriptClip> ScriptClips;
	if (JsonObj->HasField("Clips"))
	{
		for (const TSharedPtr<FJsonValue, ESPMode::ThreadSafe> CArr : JsonObj->GetArrayField("Clips"))
		{
			FDialogScriptClip SClip = FDialogScriptClip::FromJson(CArr->AsObject());
			if (SClip.Id.IsEmpty() == false) ScriptClips.Add(SClip.Id, SClip);
		}
	}
	TArray<FString> Conditions = UDataFuncLib::AwGetStringArrayField(JsonObj, "Conditions");
	TMap<int, FString> JoinNpc;
	if (JsonObj->HasField("NpcId"))
	{
		for (const TSharedPtr<FJsonValue, ESPMode::ThreadSafe> NArr : JsonObj->GetArrayField("NpcId"))
		{
			const int SlotId = UDataFuncLib::AwGetNumberField(NArr->AsObject(), "Slot", -1);
			if (SlotId >= 0 && JoinNpc.Contains(SlotId) == false)
			JoinNpc.Add(SlotId, UDataFuncLib::AwGetStringField(NArr->AsObject(), "NpcId"));
		} 
	}
	TArray<FString> EndScript = UDataFuncLib::AwGetStringArrayField(JsonObj, "EndDialogScript");
	
	return FDialogScriptModel(
	UDataFuncLib::AwGetStringField(JsonObj, "Id"),
	UDataFuncLib::AwGetStringField(JsonObj, "FirstClip"),
	Conditions,
	JoinNpc,
	ScriptClips,
	EndScript
	);
}