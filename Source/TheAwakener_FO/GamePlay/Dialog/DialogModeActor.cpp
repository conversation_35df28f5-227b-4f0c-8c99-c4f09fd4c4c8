// Fill out your copyright notice in the Description page of Project Settings.


#include "DialogModeActor.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/UI/Roguelike/RogueGiveCurrencyToNPC/RogueGiveCurrencyToNPC.h"

FTransform FDialogCameraMovePlan::ThisTickMove(float DeltaTime)
{
	const float WasPer = this->InSec > 0 ? FMath::Min((TimeElapsed / InSec), 1.f) : 1;
	const float CurPer = this->InSec > 0 ? FMath::Min((TimeElapsed + DeltaTime) / InSec, 1.f) : 1;
	const FVector LastVector = (this->Target.GetLocation() - this->StartFrom.GetLocation()) * WasPer + StartFrom.GetLocation();
	const FQuat LastQuat = (this->Target.GetRotation() - this->StartFrom.GetRotation()) * WasPer + StartFrom.GetRotation();
	const FVector CurVector = (this->Target.GetLocation() - this->StartFrom.GetLocation()) * CurPer + StartFrom.GetLocation();
	const FQuat CurQuat = (this->Target.GetRotation() - this->StartFrom.GetRotation()) * CurPer + StartFrom.GetRotation();
	FTransform Res = FTransform();
	Res.SetLocation(CurVector - LastVector);
	Res.SetRotation(CurQuat - LastQuat);
	return Res;
}

void ADialogModeActor::SetDialogChaFaceTo(const AAwCharacter* Character, bool IsInDialog, FString LeftActionId, FString RightActionId)
{
	if (!IsValid(Character))
		return;
	
	if (IsInDialog)
	{
		const FVector OriFaceTo = Character->GetActorForwardVector();
		AAwCharacter* NearestPlayer = UGameplayFuncLib::GetClosestPlayerCharacter(CurrentEventFocusCha->GetActorLocation());
		const FVector TargetFaceTo = (NearestPlayer->GetActorLocation() -
			CurrentEventFocusCha->GetActorLocation()).GetSafeNormal();
		if (ChaFaceTo.Contains(Character))
		{
			ChaFaceTo[Character].IsInDialog = true;
			// ChaFaceTo[Character].OriFaceTo = OriFaceTo;
			ChaFaceTo[Character].TargetFaceTo = TargetFaceTo;
		}
		else
		{
			FDialogChaFaceTo FaceTo;
			FaceTo.IsInDialog = true;
			FaceTo.OriFaceTo = OriFaceTo;
			FaceTo.TargetFaceTo = TargetFaceTo;
			FaceTo.ActionId_TurnLeft = LeftActionId;
			FaceTo.ActionId_TurnRight = RightActionId;
			ChaFaceTo.Add(Dialog.DialogTarget, FaceTo);
		}
	}
	else
	{
		if (ChaFaceTo.Contains(Character))
			ChaFaceTo[Character].IsInDialog = false;
	}
}

// Sets default values
ADialogModeActor::ADialogModeActor()
{
	PrimaryActorTick.bCanEverTick = true;
}

void ADialogModeActor::BeginPlay()
{
	Super::BeginPlay();
	Camera = Cast<UCameraComponent>(GetComponentByClass(UCameraComponent::StaticClass()));
}

void ADialogModeActor::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);
	//对话由1p判断既可
	if(UGameplayFuncLib::GetWorkingAwPlayerController()->GameControlState != EGameControlState::Dialog) return;
	
	if (MovePlan.Active)
	{
		if (MovePlan.TimeElapsed >= MovePlan.InSec)
		{
			MovePlan.Active = false;
			this->SetActorLocationAndRotation(
				MovePlan.Target.GetLocation(),
				MovePlan.Target.GetRotation()
			);

			if (ClipPlaying == EDialogEventClipType::FocusTarget && CurrentEventFocusCam && CurrentEventFocusCha)
			{
				const float LineDis = FMath::Abs(
					(CurrentEventFocusCam->GetComponentLocation() - CurrentEventFocusCha->GetActorLocation()).Size() * 1.5f
				);
				
			}
			
			EndCurrentClip();
		}else
		{
			const FTransform Moved = MovePlan.ThisTickMove(DeltaTime);
			MovePlan.TimeElapsed += DeltaTime;
			this->SetActorLocationAndRotation(
				this->GetActorLocation() + Moved.GetLocation(),
				this->GetActorRotation().Quaternion() + Moved.GetRotation()
			);
		}
	}

	if (WaitSec > 0)
	{
		
		WaitSec -= DeltaTime;
		if(ClipPlaying == EDialogEventClipType::Wait || ClipPlaying == EDialogEventClipType::TurnToPlayer)
		{
			if (WaitSec < 0)	//因为等于多跑了一帧，所以<0才能
				EndCurrentClip();
		}
		else
		{
			if(WaitSec < 0.3)
				UI->ShowCursor();
		}
	}

	bool CanPassDialogTyping = false;
	if (ClipPlaying == EDialogEventClipType::Speak && TypingInfo.Active == true)
	{
		CanPassDialogTyping = TypingInfo.RunTick(DeltaTime);
		
		if (UI)
		{
			FString ToShow;
			//文本逐字显示
			/*for (int m = 0; m < FMath::RoundToInt(TypingInfo.ShowTexts); m++)
				ToShow.AppendChar(TypingInfo.TextDialog[m]);*/

			//文本直接显示
			for (int i = 0; i < TypingInfo.TextDialog.Num(); ++i)
				ToShow.AppendChar(TypingInfo.TextDialog[i]);
			
			UI->ShowDialog(TypingInfo.TextSpeaker, ToShow);
		}
	}

	//最后判断操作(在Wait的时候拒绝检测输入)
	if (UGameplayFuncLib::GetAwGameState()->GetMyCharacter() && WaitSec <= 0)
	{
		// if (UGameplayFuncLib::GetAwGameState()->MyCharacter->IsActionOccur("StopDialog", EAwInputState::Press, true))
		// {
		// 	EndDialog();
		// }
		//else
		if (UGameplayFuncLib::GetAwGameState()->GetMyCharacter()->IsActionOccur("DialogConfirm", EAwInputState::Press, true) )
		{
			if (DelayToNextCmd > 0)
			{
				DelayToNextCmd -= DeltaTime;
			}else
			{
				if (Selecting == true)
				{
					UGameplayFuncLib::PlayUIAudio("Dialog_Confirm");
					if (UI->CurrentSelected())
					{
						if (UI->CurrentSelected()->Selection.SelectionEnabled == false)
						{
							//如果当前选项不可选，就会出现异常，这里doNothing先（TODO）
						}else
						{
							//停掉正在播放的语音
							if(CurVoiceAudio)
								CurVoiceAudio->FadeOut(0.2f,0.0f);
							//所有Action都会必然发生一次
							for (const FString ActFunc : UI->CurrentSelected()->Selection.ActionFunc)
							{
								const FJsonFuncData JsonFuncData = UCallFuncLib::StringToJsonFuncData(ActFunc);
								UFunction* Func = UCallFuncLib::JsonFuncToUFunc(JsonFuncData);
								if (Func)
								{
									//(FDialogScript这个对话当前数据，TArray<FString>策划配表数据)=>FString 
									struct
									{
										FDialogScriptObj DialogObj;
										TArray<FString> Params;
										FString Result;
									}FuncParams;
									FuncParams.DialogObj = Dialog;
									FuncParams.Params = JsonFuncData.Params;
									this->ProcessEvent(Func, &FuncParams);
								}
							} 
							//确认按键了，根据回调得到下一个事件，尝试拿事件，决定是否继续对话
							if (UI->CurrentSelected()->Selection.NextEventPickFunc.IsEmpty())
							{
								if(!UI->CurrentSelected()->Selection.bStillInDialog)
									EndDialog();
							}else
							{
								const FJsonFuncData JsonFuncData = UCallFuncLib::StringToJsonFuncData(UI->CurrentSelected()->Selection.NextEventPickFunc);
								UFunction* Func = UCallFuncLib::JsonFuncToUFunc(JsonFuncData);
								if (Func)
								{
									//(FDialogScript这个对话当前数据，TArray<FString>策划配表数据)=>FString 
									struct
									{
										FDialogScriptObj DialogObj;
										TArray<FString> Params;
										FString Result;
									}FuncParams;
									FuncParams.DialogObj = Dialog;
									FuncParams.Params = JsonFuncData.Params;
									this->ProcessEvent(Func, &FuncParams);
									if (FuncParams.Result.IsEmpty() || this->ScriptModel.Scripts.Contains(FuncParams.Result) == false)
									{
										//没有下个片段了
										EndDialog();
									}else
									{
										//跳转到下一段
										Selecting = false;
										UI->HideSelections();
										UI->HideDialog();
										Dialog.DialogClip = ScriptModel.Scripts[FuncParams.Result];
										Dialog.PlayingEventId = Dialog.DialogClip.FirstEventId;
										PlayCurrentClip();
									}
								}
							}
						}
					}
					else
					{
						//选的选项没东西
						EndDialog();

						
					}
					
				}else if (ClipPlaying == EDialogEventClipType::Speak && UI && UI->DialogShowing && UI->DialogDoneShown && CanPassDialogTyping == true)
				{
					UGameplayFuncLib::PlayUIAudio("Dialog_Next");
					EndCurrentClip();
				}
				
				DelayToNextCmd = EachCmdDelay;	//每次确定之后等这么久
			}
		}
		else if (UGameplayFuncLib::GetAwGameState()->GetMyCharacter()->IsActionOccur("DialogUp", EAwInputState::Press, true) )
		{
			if (UI) UI->PrevSelection();
		}
		else if (UGameplayFuncLib::GetAwGameState()->GetMyCharacter()->IsActionOccur("DialogDown", EAwInputState::Press, true) )
		{
			if (UI) UI->NextSelection();
		}
		
	}

	TArray<AAwCharacter*> WaitToRemove;
	for (TTuple<AAwCharacter*, FDialogChaFaceTo> FaceTo : ChaFaceTo)
	{
		if (IsValid(FaceTo.Key))
		{
			FVector2D TargetFaceTo = FVector2D(FaceTo.Value.IsInDialog ? FaceTo.Value.TargetFaceTo : FaceTo.Value.OriFaceTo);
			FaceTo.Key->GetMoveComponent()->SetForceRotate(TargetFaceTo);
			if (FaceTo.Value.IsInDialog == false &&
				FVector2D(FaceTo.Key->GetActorForwardVector()).Equals(TargetFaceTo, 0.01f))
					WaitToRemove.Add(FaceTo.Key);
		}
		else
			WaitToRemove.Add(nullptr);
	}
	for (AAwCharacter* ToRemove : WaitToRemove)
		ChaFaceTo.Remove(ToRemove);
}

void ADialogModeActor::StartDialog(FDialogScriptObj DialogObj)
{
	this->SetActorHiddenInGame(false);
	this->Dialog = DialogObj;
	this->Selecting = false;
	this->ScriptModel = UGameplayFuncLib::GetAwDataManager()->GetDialogById(Dialog.ModelId);
	if (ScriptModel.IsValidDialog() && ScriptModel.FirstClipId.IsEmpty() == false && ScriptModel.Scripts.Contains(ScriptModel.FirstClipId))
	{
		Dialog.DialogClip = ScriptModel.Scripts[ScriptModel.FirstClipId];
		Dialog.PlayingEventId = Dialog.DialogClip.FirstEventId;
		
		//为了实现第一句话随机，后面的选项内容都一样的 合理 需求
		//还要走一遍FirstEventIdFunc的函数
		const FJsonFuncData JsonFuncData = UCallFuncLib::StringToJsonFuncData(Dialog.DialogClip.FirstEventIdFunc);
		UFunction* Func = UCallFuncLib::JsonFuncToUFunc(JsonFuncData);
		if (Func)
		{
			struct
			{
				FDialogScriptObj DialogObj;
				TArray<FString> Params;
				FString Result;
			}FuncParams;
			FuncParams.DialogObj = Dialog;
			FuncParams.Params = JsonFuncData.Params;
			this->ProcessEvent(Func, &FuncParams);
			if (!FuncParams.Result.IsEmpty() && Dialog.DialogClip.Dialogs.Contains(FuncParams.Result))
			{
				Dialog.PlayingEventId = FuncParams.Result;
			}
		}
		
		PlayCurrentClip();
	}else
	{
		EndDialog();
	}
}

void ADialogModeActor::EndDialog()
{
	this->SetActorHiddenInGame(true);
	this->RestoreAllActorsVisible();
	this->UI->PlayHideDialogAnim();
	if(CurVoiceAudio)
		CurVoiceAudio->FadeOut(0.2f,0.0f);
	/*this->UI->HideDialog();
	this->UI->HideSelections();*/
	this->Selecting = false;
	UGameplayFuncLib::ResumeGameForDialog();
	if (UAwGameInstance::Instance && !UGameplayFuncLib::IsRogueMode())
	{
		const FString NpcId = this->Dialog.DialogTarget ? this->Dialog.DialogTarget->GetNpcId() : "";
		if (NpcId.IsEmpty() == false)
		{
			const FString SwitchKey = NpcId + "_CommunicationTimes";
			UAwGameInstance::Instance->RoleInfo.AddSwitchValue(SwitchKey,  1);
		}
	}
	
	UAwGameInstance* GameInstance = Cast<UAwGameInstance>(GWorld->GetGameInstance());
	for (FString EndScript : this->ScriptModel.EndDialogScript)
	{
		const FJsonFuncData JsonFunc = UCallFuncLib::StringToJsonFuncData(EndScript);
		UFunction* Func = UCallFuncLib::JsonFuncToUFunc(JsonFunc);
		if (Func)
		{
			struct
			{
				FDialogScriptObj DSObj;
				TArray<FString> Params;
				FString Result;
			}FuncParam;
			FuncParam.DSObj = Dialog;
			FuncParam.Params = JsonFunc.Params;
			this->Dialog.DialogTarget->ProcessEvent(Func, &FuncParam);
		}
	}

	if (this->Dialog.DialogTarget && this->Dialog.DialogTarget->NpcInfo.Personality.DialogAutoPicker.IsEmpty() == false)
	{
		const FJsonFuncData JsonFunc = UCallFuncLib::StringToJsonFuncData(this->Dialog.DialogTarget->NpcInfo.Personality.DialogAutoPicker);
		UFunction* Func = UCallFuncLib::JsonFuncToUFunc(JsonFunc);
		if (Func)
		{
			struct
			{
				FNpcInfo NpcInfo;
				TArray<FString> Params;
				FString Result;
			}FuncParam;
			FuncParam.NpcInfo = this->Dialog.DialogTarget->NpcInfo;
			FuncParam.Params = JsonFunc.Params;
			this->Dialog.DialogTarget->ProcessEvent(Func, &FuncParam);
			this->Dialog.DialogTarget->NpcInfo.Personality.DialogModelId = FuncParam.Result;
		}
	}

	for (TTuple<AAwCharacter*, FDialogChaFaceTo> FaceTo : ChaFaceTo)
	{
		if (FaceTo.Key)
		{
			// 转回去，左右动画需要反过来
			const FString ActionId = FaceTo.Value.IsTurnRight() ? FaceTo.Value.ActionId_TurnLeft : FaceTo.Value.ActionId_TurnRight;
			FaceTo.Key->PreorderAction(ActionId);
		}
		SetDialogChaFaceTo(FaceTo.Key, false);
	}
	if (this->Dialog.DialogTarget)
	{
		this->Dialog.DialogTarget->OnEndDialog();
	}
	this->EndDialogDelegate.Broadcast(this);
}

void ADialogModeActor::DoSelectionGoToNextClipFunc(FString NextEventFunc)
{
	const FJsonFuncData JsonFuncData = UCallFuncLib::StringToJsonFuncData(NextEventFunc);
	UFunction* Func = UCallFuncLib::JsonFuncToUFunc(JsonFuncData);
	if (Func)
	{
		//(FDialogScript这个对话当前数据，TArray<FString>策划配表数据)=>FString 
		struct
		{
			FDialogScriptObj DialogObj;
			TArray<FString> Params;
			FString Result;
		}FuncParams;
		FuncParams.DialogObj = Dialog;
		FuncParams.Params = JsonFuncData.Params;
		this->ProcessEvent(Func, &FuncParams);
		if (FuncParams.Result.IsEmpty() || this->ScriptModel.Scripts.Contains(FuncParams.Result) == false)
		{
			//没有下个片段了
			EndDialog();
		}else
		{
			//跳转到下一段
			Selecting = false;
			UI->HideSelections();
			UI->HideDialog();
			Dialog.DialogClip = ScriptModel.Scripts[FuncParams.Result];
			Dialog.PlayingEventId = Dialog.DialogClip.FirstEventId;
			PlayCurrentClip();
		}
	}
}

void ADialogModeActor::PlayCurrentClip()
{
	if (Dialog.DialogClip.Dialogs.Contains(Dialog.PlayingEventId))
	{
		//if (UI) UI->HideDialog();	//在这里隐藏掉对话框，这样能选指令的时候看到最后一句话
		const FDialogEventClip PlayingEvent = Dialog.DialogClip.Dialogs[Dialog.PlayingEventId];

		switch (PlayingEvent.TargetType)
		{
		case EDialogTargetType::Player:
			CurrentEventFocusCha =  UGameplayFuncLib::GetAwGameState()->GetMyCharacter();
			break;
		case EDialogTargetType::Target:
			CurrentEventFocusCha = Dialog.DialogTarget;
			break;
		case EDialogTargetType::Buddy:break;	//TODO 小弟咋做？还没做呢
		case EDialogTargetType::AttendNpc:
			CurrentEventFocusCha = Dialog.AttendNpc.Contains(PlayingEvent.SlotIndex) ? Dialog.AttendNpc[PlayingEvent.SlotIndex] : nullptr;
		break;
		}

		
		CurrentEventFocusCam = CurrentEventFocusCha ? CurrentEventFocusCha->GetDialogCameraByIndex(PlayingEvent.CameraIndex) : nullptr;
		
		if (PlayingEvent.ClipType == EDialogEventClipType::FocusTarget)
		{
			//-----------------------------------------镜头焦点------------------------------------
			
			if (CurrentEventFocusCam)
			{
				this->MovePlan = FDialogCameraMovePlan(CurrentEventFocusCam->GetComponentTransform(), PlayingEvent.CameraFocusInSec);
				this->MovePlan.StartFrom = this->GetActorTransform();
				this->MovePlan.TimeElapsed = 0;
				
				ClipPlaying = EDialogEventClipType::FocusTarget;

				if (CurrentEventFocusCha)
					this->CheckForHideActors(CurrentEventFocusCha, 3000);
			}else
			{
				EndCurrentClip();
			}
		}else if (PlayingEvent.ClipType == EDialogEventClipType::Speak)
		{
			//-------------------------------对话框出字儿---------------------------------------
			const FString ToShowText = UGameplayFuncLib::GetAwDataManager()->GetTextByKey(PlayingEvent.SpeakText);
			TypingInfo = FDialogTextShowing(ToShowText);
			TypingInfo.TextSpeaker = UGameplayFuncLib::GetAwDataManager()->GetTextByKey(PlayingEvent.SpeakerName);
			ClipPlaying = EDialogEventClipType::Speak;
			//Speak的CD
			WaitSec = 0.3;
			if(UI)
				UI->HideCursor();
		}else if (PlayingEvent.ClipType == EDialogEventClipType::Wait)
		{
			//--------------------------------等待时间-------------------------------------------
			WaitSec = PlayingEvent.WaitSec;
			ClipPlaying = EDialogEventClipType::Wait;
			if(UI)
				UI->HideCursor();
		}else if (PlayingEvent.ClipType == EDialogEventClipType::TurnToPlayer)
		{
			//--------------------------------转向玩家-------------------------------------------
			SetDialogChaFaceTo(CurrentEventFocusCha, true, PlayingEvent.ActionId_TurnLeft, PlayingEvent.ActionId_TurnRight);
			if (CurrentEventFocusCha && ChaFaceTo.Contains(CurrentEventFocusCha))
			{
				const FDialogChaFaceTo FaceTo = ChaFaceTo[CurrentEventFocusCha];
				const FString ActionId = FaceTo.IsTurnRight() ? FaceTo.ActionId_TurnRight : FaceTo.ActionId_TurnLeft;
				CurrentEventFocusCha->PreorderAction(ActionId);
			}
			WaitSec = PlayingEvent.WaitSec;
			ClipPlaying = EDialogEventClipType::TurnToPlayer;
		}
		else if (PlayingEvent.ClipType == EDialogEventClipType::DoAction)
		{
			//--------------------------------做动作----------------------------------------------
			if (CurrentEventFocusCha) CurrentEventFocusCha->PreorderAction(PlayingEvent.ActionId);
			ClipPlaying = EDialogEventClipType::DoAction;
			
			EndCurrentClip();	//先立即返回
		}
		else if(PlayingEvent.ClipType == EDialogEventClipType::PlayAudio)
		{
			//-------------------------------播放对应语音---------------------------------------
			const FString TextAudioFile = UGameplayFuncLib::GetAwDataManager()->GetDialogueAudioByKey(PlayingEvent.AudioId);
			if(CurVoiceAudio)
				CurVoiceAudio->FadeOut(0.2f,0.0f);
			CurVoiceAudio = UGameplayFuncLib::PlayVoice2DByPath(TextAudioFile);
			ClipPlaying = EDialogEventClipType::PlayAudio;

			EndCurrentClip();	//先立即返回
		}
		else if(PlayingEvent.ClipType == EDialogEventClipType::HideDialogUI)
		{
			//------------------------------隐藏对话框UI-----------------------------------------
			ClipPlaying = EDialogEventClipType::HideDialogUI;
			if(UI)
				UI->HideDialog();

			EndCurrentClip();	//先立即返回
		}
		else if(PlayingEvent.ClipType == EDialogEventClipType::GiveCurrencyToNPC)
		{
			//------------------------------显示赠送货币的UI-----------------------------------------
			//为了实现跳出另一个UI，根据另一个UI里的选项来跳转对话，成功的和失败的NextEventFunc在UI蓝图里手动调用
			ClipPlaying = EDialogEventClipType::GiveCurrencyToNPC;
			if(UI)
				UI->HideDialog();
			if(PlayingEvent.Params.Num() > 1)
			{
				FString CurrencyName = PlayingEvent.Params[0];
				int CurrencyNum = FCString::Atoi(*PlayingEvent.Params[1]);
				FString GiveSuccessEventId = "";
				FString GiveFailureEventId = "";
				if(PlayingEvent.Params.Num() > 2)
					GiveSuccessEventId = PlayingEvent.Params[2];
				if(PlayingEvent.Params.Num() > 3)
					GiveFailureEventId = PlayingEvent.Params[3];
				UUserWidget* Widget = UGameplayFuncLib::GetUiManager()->Show("RogueGiveCurrencyToNPC");
				URogueGiveCurrencyToNPC* GiveCurrencyWidget = Cast<URogueGiveCurrencyToNPC>(Widget);
				if(GiveCurrencyWidget)
				{
					GiveCurrencyWidget->InitClass(CurrencyName, CurrencyNum, GiveSuccessEventId, GiveFailureEventId);
				}
			}
			//EndCurrentClip();	//先立即返回
		}

		if (CurrentEventFocusCha)
			this->CheckForHideActors(CurrentEventFocusCha, 3000);
	}else
	{
		EndCurrentClip();
	}
}



void ADialogModeActor::EndCurrentClip()
{
	ClipPlaying = EDialogEventClipType::None;
	FDialogEventClip PlayingEvent = Dialog.DialogClip.Dialogs[Dialog.PlayingEventId];
	bool AtLeastOneSelectionCanUse = false;	//至少有一个选项可以工作，如果一个都没，一样不打开选择框
	if (PlayingEvent.NextId.IsEmpty() == false && Dialog.DialogClip.Dialogs.Contains(PlayingEvent.NextId))
	{
		//下一条Clip
		Dialog.PlayingEventId = PlayingEvent.NextId;
		PlayCurrentClip();
	}else if (Dialog.DialogClip.Selections.Num() > 0)
	{
		//如果没有下一条了，就考虑出选择菜单，如果没有选择菜单，就结束对话
		TArray<FDialogSelection> CanShowSelections;	//要显示的选项
		for (FDialogSelection Selection : Dialog.DialogClip.Selections)
		{
			bool CanRun = true;
			for (const FString Cond : Selection.ConditionFunc)
			{
				FJsonFuncData JsonFunc = UCallFuncLib::StringToJsonFuncData(Cond);
				UFunction* Func = UCallFuncLib::JsonFuncToUFunc(JsonFunc);
				if (Func)
				{
					struct
					{
						FDialogScriptObj DSObj;
						TArray<FString> Params;
						bool Result;
					}FuncParam;
					FuncParam.DSObj = Dialog;
					FuncParam.Params = JsonFunc.Params;
					this->ProcessEvent(Func, &FuncParam);
					if (FuncParam.Result == false)
					{
						CanRun = false;
						break;
					}
				}
			}
			if (CanRun == false) continue;

			Selection.SelectionEnabled = true;
			for (FString EnableChecker : Selection.EnableCheckFunc)
			{
				FJsonFuncData JsonFunc = UCallFuncLib::StringToJsonFuncData(EnableChecker);
				UFunction* Func = UCallFuncLib::JsonFuncToUFunc(JsonFunc);
				if (Func)
				{
					struct
					{
						FDialogScriptObj DSObj;
						TArray<FString> Params;
						bool Result;
					}FuncParam;
					FuncParam.DSObj = Dialog;
					FuncParam.Params = JsonFunc.Params;
					this->ProcessEvent(Func, &FuncParam);
					if (FuncParam.Result == false)
					{
						Selection.SelectionEnabled = false;
						break;
					}
				}
			} 
			CanShowSelections.Add(Selection);

			if (Selection.SelectionEnabled == true) AtLeastOneSelectionCanUse = true;
		}
		
		if (UI && CanShowSelections.Num() > 0 && AtLeastOneSelectionCanUse == true)
		{
			UI->ShowSelections(CanShowSelections);
			this->Selecting = true;
		}else
		{
			//没下文了，对话结束
			EndDialog();
		}
	}else
	{
		//先跑下没有Selection是运行的Action
		for (FString EndClipScript : Dialog.DialogClip.EndClipScript)
		{
			const FJsonFuncData JsonFunc = UCallFuncLib::StringToJsonFuncData(EndClipScript);
			UFunction* Func = UCallFuncLib::JsonFuncToUFunc(JsonFunc);
			if (Func)
			{
				struct
				{
					FDialogScriptObj DSObj;
					TArray<FString> Params;
					FString Result;
				}FuncParam;
				FuncParam.DSObj = Dialog;
				FuncParam.Params = JsonFunc.Params;
				this->Dialog.DialogTarget->ProcessEvent(Func, &FuncParam);
			}
		}
		EndDialog();
	}
}

void ADialogModeActor::CheckForHideActors(AAwCharacter* FocusGuy, float LineDistance)
{
	if (!FocusGuy || !FocusGuy->GetCapsuleComponent()) return;

	RestoreAllActorsVisible();	//先彻底回复一下吧，至少这样做比较快，之后优化一下
	
	const float HalfHeight = FocusGuy->GetCapsuleComponent()->GetScaledCapsuleHalfHeight() * 2;
	const float CapRadius = FocusGuy->GetCapsuleComponent()->GetScaledCapsuleRadius() * 2;
	
	this->HiddenActors.Empty();
	TMap<AAwCharacter*, FString> ToHide =
		UGameplayFuncLib::GetAwGameState()->AllCharacters;
		//UGameplayFuncLib::GetTracedActorsFromScreenCenter(this->Camera, LineDistance, HalfHeight, CapRadius);
	
	// for (AActor* Guy : HiddenActors)
	// {
	// 	//是角色，隐藏，其他再说
	// 	if (Guy->GetClass()->IsChildOf(AAwCharacter::StaticClass()))
	// 	{
	// 		AAwCharacter* Cha = Cast<AAwCharacter>(Guy);
	// 		if (!Cha || Cha == FocusGuy) continue;
	// 		
	// 		Cha->HideInCamera();
	// 		this->HiddenActors.Add(Cha);
	// 	}
	// }
	//暂时先不隐藏角色
	/*for (const TTuple<AAwCharacter*, FString> Hide : ToHide)
	{
		if (Hide.Key && Hide.Key != FocusGuy)
		{
			if (Hide.Key != FocusGuy)
			{
				Hide.Key->HideInCamera();
				this->HiddenActors.Add(Cast<AActor>(Hide.Key));
			}
			
		}
	} */
}

void ADialogModeActor::RestoreAllActorsVisible()
{
	for (AActor* Actor : HiddenActors)
	{
		if (Actor)
			if (Actor->GetClass()->IsChildOf(AAwCharacter::StaticClass()))
			{
				AAwCharacter* Cha = Cast<AAwCharacter>(Actor);
				if (Cha) Cha->RestoreInCamera();
			}
	}
	this->HiddenActors.Empty();
}

bool FDialogTextShowing::RunTick(float DeltaTime)
{
	this->ShowTexts += DeltaTime * this->ChaPerSec;
	const bool Res = this->ShowTexts >= this->TotalTexts;
	this->ShowTexts = FMath::Clamp(ShowTexts, 0.f, TotalTexts * 1.f);
	return Res;
}