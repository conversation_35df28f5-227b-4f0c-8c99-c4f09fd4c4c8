// Fill out your copyright notice in the Description page of Project Settings.


#include "DialogBubbleComponent.h"
#include "DialogBubbleInterface.h"
#include "Misc/FileHelper.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"

FDialogBubble FDialogBubble::FromJson(TSharedPtr<FJsonObject> JsonObj)
{
	//单句对话气泡json数据解析
	FDialogBubble NewDialogBubble = FDialogBubble();
	NewDialogBubble.DialogTextId = UDataFuncLib::AwGetStringField(JsonObj,"Text");

	NewDialogBubble.LifeTime = UDataFuncLib::AwGetNumberField(JsonObj,"LifeTime",0);
	NewDialogBubble.FadeInTime = UDataFuncLib::AwGetNumberField(JsonObj,"FadeInTime",NewDialogBubble.FadeInTime);
	NewDialogBubble.FadeOutTime = UDataFuncLib::AwGetNumberField(JsonObj, "FadeOutTime", NewDialogBubble.FadeOutTime);
	NewDialogBubble.NextDialogCoolDown = UDataFuncLib::AwGetNumberField(JsonObj, "NextDialogCoolDown", 0);

	NewDialogBubble.LifeTimeFunc = UCallFuncLib::StringToJsonFuncData(UDataFuncLib::AwGetStringField(JsonObj, "LifeTime"));
	NewDialogBubble.FadeInTimeFunc = UCallFuncLib::StringToJsonFuncData(UDataFuncLib::AwGetStringField(JsonObj, "FadeInTime"));
	NewDialogBubble.FadeOutTimeFunc = UCallFuncLib::StringToJsonFuncData(UDataFuncLib::AwGetStringField(JsonObj, "FadeOutTime"));
	NewDialogBubble.NextDialogCoolDownFunc = UCallFuncLib::StringToJsonFuncData(UDataFuncLib::AwGetStringField(JsonObj, "NextDialogCoolDown"));
	return NewDialogBubble;
}

void FDialogBubble::TrySetPropertyByPolicy(UObject* Caller)
{
	//必须有个Uobject以上的Caller才能通过UE反射 从对象的PROPERTY里通过函数名 找到目标函数内存 进行process调用
	if (!IsValid(Caller))
	{
		Caller = NewObject<UObject>();
	}
	auto ProcessLamda = [](FJsonFuncData Func,UObject* FuncCaller,float& Param)
	{
		UFunction* PolicyFunc = UCallFuncLib::GetUFunction(Func.ClassPath,
			Func.FunctionName);
		if (PolicyFunc)
		{
			struct
			{
				UObject* Caller;
				TArray<FString> Params;
				float Result;
			}ParamPolicyFuncParam;
			ParamPolicyFuncParam.Caller = FuncCaller;
			ParamPolicyFuncParam.Params = Func.Params;
			FuncCaller->ProcessEvent(PolicyFunc, &ParamPolicyFuncParam);
			Param = ParamPolicyFuncParam.Result;
		}
	};

	if (!LifeTimeFunc.FunctionName.IsEmpty())
	{
		ProcessLamda(LifeTimeFunc,Caller,LifeTime);
	}
	if (!FadeInTimeFunc.FunctionName.IsEmpty())
	{
		ProcessLamda(FadeInTimeFunc, Caller, FadeInTime);
	}
	if (!FadeOutTimeFunc.FunctionName.IsEmpty())
	{
		ProcessLamda(FadeOutTimeFunc, Caller, FadeOutTime);
	}
	if (!NextDialogCoolDownFunc.FunctionName.IsEmpty())
	{
		ProcessLamda(NextDialogCoolDownFunc, Caller, NextDialogCoolDown);
	}
}

FDialogBubbleGroupMobel FDialogBubbleGroupMobel::FromJson(TSharedPtr<FJsonObject> JsonObj)
{
	//对话气泡组json数组解析
	FDialogBubbleGroupMobel NewDialogBubbleGroup = FDialogBubbleGroupMobel();
	NewDialogBubbleGroup.DialogBubbleGroupID = UDataFuncLib::AwGetStringField(JsonObj,"DialogBubbleGroupID");
	NewDialogBubbleGroup.PlayType = UDataFuncLib::AwGetEnumField(JsonObj,"DialogBubbleGroupType",EDialogBubbleGroupType::Sequence);
	NewDialogBubbleGroup.Priority = UDataFuncLib::AwGetNumberField(JsonObj,"Priority",0);
	for (const TSharedPtr<FJsonValue> Value : UDataFuncLib::AwGetArrayField(JsonObj,"DialogBubbles"))
	{
		FDialogBubble NewDialogBubble = FDialogBubble::FromJson(Value->AsObject());
		NewDialogBubbleGroup.DialogBubbleGroup.Add(NewDialogBubble);
	}
	for (const TSharedPtr<FJsonValue> Value : UDataFuncLib::AwGetArrayField(JsonObj, "CheckCondition"))
	{
		NewDialogBubbleGroup.CheckCondition.Add(UDataFuncLib::SplitFuncNameAndParams(Value->AsString()));
	}

	return NewDialogBubbleGroup;
}



bool UDialogBubbleComponent::ChangeCurDialogBubbleGroup(FString MobID, bool ForceSubmit)
{
	if (Paused)
	{
		return false;
	}

	UAwDataManager* DataManager = UGameplayFuncLib::GetAwDataManager();
	FDialogBubbleGroupMobel Target = DataManager->GetDialogBubbleGroupById(MobID);
	//无效或与当前相等时直接退出
	if (Target.DialogBubbleGroupID.IsEmpty() || Target == CurDialogBubbleGroupMobel)
	{
		//UE_LOG(LogTemp, Log, TEXT("Want to change DialogBubbleGroup in same or empty"));
		return false;
	}
	//有效但不符合自身特定条件
	if (!CheckDialogGroupCondition(Target))
	{
		return false;
	}
	//优先级低于当前
	if (Target.Priority<CurDialogBubbleGroupMobel.Priority)
	{
		return false;
	}
	//是否无视当前不同对话组的强制冷却时间 懒得重写!= 所以!(==)
	if (ForceSubmit && !(Target == CurDialogBubbleGroupMobel))
	{
		ClearCurDialogBubbleCoolDown();
	}

	//如果符合强制对话冷却则立马提交一次
	if (CheckCurDialogBubbleCoolDown())
	{
		CurDialogGroupMobID = Target.DialogBubbleGroupID;
		CurDialogBubbleGroupMobel = Target;
		CurBubbleIndexInGroup = -1;
		SubmitNextDialogBubble();
		return true;
	}
	else if (CurDialogBubbleGroupMobel.PlayType == EDialogBubbleGroupType::RandomOnce)
	{
		UE_LOG(LogTemp, Log, TEXT("Want to change OnceDialogBubbleGroup Failed"));
		return false;
	}

	CurDialogGroupMobID = Target.DialogBubbleGroupID;
	CurDialogBubbleGroupMobel = Target;
	CurBubbleIndexInGroup = -1;
	return true;
}



bool UDialogBubbleComponent::ChangeCurDialogBubbleGroupInSpecialType(FString MobID, EDialogBubbleGroupType NewType, bool ForceSubmit)
{
	if (Paused)
	{
		return false;
	}
	UAwDataManager* DataManager = UGameplayFuncLib::GetAwDataManager();
	FDialogBubbleGroupMobel Target = DataManager->GetDialogBubbleGroupById(MobID);
	Target.PlayType = NewType;
	if (Target.DialogBubbleGroupID.IsEmpty() || Target == CurDialogBubbleGroupMobel)
	{
		UE_LOG(LogTemp, Log, TEXT("Want to change DialogBubbleGroup in same or empty"));
		return false;
	}
	if (!CheckDialogGroupCondition(Target))
	{
		return false;
	}
	if (Target.Priority < CurDialogBubbleGroupMobel.Priority)
	{
		return false;
	}
	if (ForceSubmit && !(Target == CurDialogBubbleGroupMobel))
	{
		ClearCurDialogBubbleCoolDown();
	}


	//如果符合上一句话的强制对话冷却则立马提交一次
	if (CheckCurDialogBubbleCoolDown())
	{
		CurDialogGroupMobID = Target.DialogBubbleGroupID;
		CurDialogBubbleGroupMobel = Target;
		CurBubbleIndexInGroup = -1;
		SubmitNextDialogBubble();
		return true;
	}
	else if(CurDialogBubbleGroupMobel.PlayType == EDialogBubbleGroupType::RandomOnce)
	{
		UE_LOG(LogTemp, Log, TEXT("Want to change OnceDialogBubbleGroup Failed"));
		return false;
	}

	CurDialogGroupMobID = Target.DialogBubbleGroupID;
	CurDialogBubbleGroupMobel = Target;
	CurBubbleIndexInGroup = -1;
	return true;
}

void UDialogBubbleComponent::CheckChangeCurSpecialDialogGroup()
{
	//检测特殊条件对话组
	FString TargetDialogGroupMobID;
	TArray<FString> TargetDialogGroupList;
	SpecialTriggerDialogBubbleGroupMap.GetKeys(TargetDialogGroupList);

	//获取字段在DataManager中的有效字段组
	TArray<FDialogBubbleGroupMobel> TargetMobels;
	UAwDataManager* DataManager = UGameplayFuncLib::GetAwDataManager();
	for (auto DialogGroupTarget : TargetDialogGroupList)
	{
		FDialogBubbleGroupMobel Target = DataManager->GetDialogBubbleGroupById(DialogGroupTarget);
		TargetMobels.Add(Target);
	}
	//优先级排序
	TargetMobels.Sort([=](FDialogBubbleGroupMobel A1, FDialogBubbleGroupMobel A2)
		{return A1.Priority > A2.Priority; }
	);
	//优先级高 且符合特殊条件 (注: 同优先并列级会只取一个)

	for (auto Target : TargetMobels)
	{
		if (CheckDialogGroupCondition(Target))
		{
			TargetDialogGroupMobID = Target.DialogBubbleGroupID;
			break;
		}
	}

	bool* ForceSubmit = SpecialTriggerDialogBubbleGroupMap.Find(TargetDialogGroupMobID);


	if (!TargetDialogGroupMobID.IsEmpty())
	{
		ChangeCurDialogBubbleGroup(TargetDialogGroupMobID, *ForceSubmit);
	}
	else if (TargetDialogGroupMobID.IsEmpty() && CurDialogGroupMobID != DefaultDialogGroupMobID)
	{
		//小心DefaultDialog为空
		ClearCurDialogBubble();
		ChangeCurDialogBubbleGroup(DefaultDialogGroupMobID);
	}
}


void UDialogBubbleComponent::SubmitNextDialogBubble()
{
	//组内无对话内容
	if (!CurDialogBubbleGroupMobel.DialogBubbleGroup.IsValidIndex(0))
	{
		return;
	}
	switch (CurDialogBubbleGroupMobel.PlayType)
	{
		//顺序时 index自动递增
		case EDialogBubbleGroupType::Sequence: 
		{	
			CurBubbleIndexInGroup++;
			CurBubbleIndexInGroup = FMath::Clamp(CurBubbleIndexInGroup,0,CurDialogBubbleGroupMobel.DialogBubbleGroup.Num()-1);
		}
		break;
		//顺序循环时 index自动递增且闭环
		case EDialogBubbleGroupType::SequenceLoop:
		{
			CurBubbleIndexInGroup++;
			CurBubbleIndexInGroup = FMath::Clamp(CurBubbleIndexInGroup, 0, FMath::Abs(CurBubbleIndexInGroup));
			CurBubbleIndexInGroup = CurBubbleIndexInGroup % CurDialogBubbleGroupMobel.DialogBubbleGroup.Num();
		}
		break;
		//仅循环一次 获取目标后 清空当前对话组内容 并直接调用
		case EDialogBubbleGroupType::RandomOnce:
		{
			if (CurDialogBubbleGroupMobel.DialogBubbleGroup.Num() < 2)
			{
				CurBubbleIndexInGroup = 0;
			}
			else
			{
				CurBubbleIndexInGroup = FMath::RandRange(0, CurDialogBubbleGroupMobel.DialogBubbleGroup.Num() - 1);
			}
		}
		break;
		//随机循环
		case EDialogBubbleGroupType::RandomLoop:
		{
			if (CurDialogBubbleGroupMobel.DialogBubbleGroup.Num() < 2)
			{
				return;
			}
			else
			{
				int  LastBubbleIndexInGroup = CurBubbleIndexInGroup;
				//避免极端情况一直随机到同一个
				do
				{
				  CurBubbleIndexInGroup = FMath::RandRange(0, CurDialogBubbleGroupMobel.DialogBubbleGroup.Num() - 1);
				} while (CurBubbleIndexInGroup == LastBubbleIndexInGroup);
			}
		}
		break;
	}
	//理论上 CurBubbleIndexInGroup必定有效
	if (CurDialogBubbleGroupMobel.DialogBubbleGroup.IsValidIndex(CurBubbleIndexInGroup))
	{
		CurDialogBubble = CurDialogBubbleGroupMobel.DialogBubbleGroup[CurBubbleIndexInGroup];
		//如果有策略变量 则尝试通过策略设置属性
		CurDialogBubble.TrySetPropertyByPolicy(this);
		TargetCoolDown = FMath::Max(CurDialogBubble.FadeInTime, CurDialogBubble.NextDialogCoolDown);
		//下放新增气泡 多态调用至实际widget
		if (GetWidget())
		{
			if (GetWidget()->Implements<UDialogBubbleWidgetInterface>())
			{
				UAwDataManager* DataManager = UGameplayFuncLib::GetAwDataManager();

				FString Text = DataManager->GetTextByKey(CurDialogBubble.DialogTextId);
				IDialogBubbleWidgetInterface::Execute_AddNewDialogBubble(GetWidget(), Text, CurDialogBubble.LifeTime, CurDialogBubble.FadeInTime, CurDialogBubble.FadeOutTime);
			}
		}
	}
}

void UDialogBubbleComponent::StopCurDialogBubble()
{
	ClearCurDialogBubbleCoolDown();
	ClearCurDialogBubble();
	//下放停止气泡 多态调用至实际widget
	if (GetWidget())
	{
		if (GetWidget()->Implements<UDialogBubbleWidgetInterface>())
		{
			IDialogBubbleWidgetInterface::Execute_StopCurDialogBubble(GetWidget());
		}
	}
}

void UDialogBubbleComponent::ClearCurDialogBubbleCoolDown()
{
	CurBubbleCoolDownTime = 0;
	TargetCoolDown = 0;
}

void UDialogBubbleComponent::ClearCurDialogBubble()
{
	CurBubbleIndexInGroup = -1;
	CurDialogGroupMobID = "";
	CurDialogBubbleGroupMobel = FDialogBubbleGroupMobel();
	CurDialogBubble = FDialogBubble();
}


void UDialogBubbleComponent::SubmitNewIndependentDialogBubble(FDialogBubble NewDialogBubble,bool ForceSubmit)
{
	if (Paused)
	{
		return;
	}

	if (!ForceSubmit)
	{
		if (!CheckCurDialogBubbleCoolDown())
		{
			return;
		}
	}
	ClearCurDialogBubbleCoolDown();
	CurDialogBubble = NewDialogBubble;
	TargetCoolDown = FMath::Max(CurDialogBubble.FadeInTime, CurDialogBubble.NextDialogCoolDown);
	if (GetWidget())
	{
		if (GetWidget()->Implements<UDialogBubbleWidgetInterface>())
		{
			UAwDataManager* DataManager = UGameplayFuncLib::GetAwDataManager();

			FString Text = DataManager->GetTextByKey(CurDialogBubble.DialogTextId);
			IDialogBubbleWidgetInterface::Execute_AddNewDialogBubble(GetWidget(),Text, CurDialogBubble.LifeTime, CurDialogBubble.FadeInTime, CurDialogBubble.FadeOutTime);
		}
	}

}

void UDialogBubbleComponent::SetComponentPaused(bool IsPaused)
{
	Paused = IsPaused;

	SetComponentTickEnabled(!Paused);

	if (GetWidget()->Implements<UDialogBubbleWidgetInterface>())
	{
		IDialogBubbleWidgetInterface::Execute_PauseDialogBubble(GetWidget(),IsPaused);
	}
}


bool UDialogBubbleComponent::CheckDialogGroupCondition(FDialogBubbleGroupMobel DialogGroupTarget)
{
	bool result = true;
	for (auto Condition : DialogGroupTarget.CheckCondition)
	{
		UFunction* ConditionFunc = UCallFuncLib::GetUFunction(Condition.ClassPath, Condition.FunctionName);
		if (ConditionFunc)
		{
			struct
			{
				AAwCharacter* CatchedPlayer;
				UDialogBubbleComponent* DialogBubbleComponent;
				TArray<FString> Params;
				bool Result = false;
			}ConditionFucParam;
			ConditionFucParam.CatchedPlayer = CatchedPlayer;
			ConditionFucParam.DialogBubbleComponent = this;
			ConditionFucParam.Params = Condition.Params;
			this->ProcessEvent(ConditionFunc, &ConditionFucParam);

			if (!ConditionFucParam.Result)
			{
				result = false;
				break;
			}
		}
	}
	return result;
}

UDialogBubbleComponent::UDialogBubbleComponent()
{
	//初始化默认常用对话组ID对应
	DefaultDialogBubbleGroupArray.Add("DefaultDailyDialogBubbles");
#if WITH_EDITOR
	JsonConfigPath = FPaths::ProjectContentDir() + "EditorData/DialogBubbleComponent/";
#endif

	//变量初始化
	CurBubbleCoolDownTime = 0;
	TargetCoolDown = 0;
	CurBubbleIndexInGroup = -1;
	CurDialogBubbleGroupMobel = FDialogBubbleGroupMobel();
	CurDialogBubble = FDialogBubble();
	SetComponentTickEnabled(false);

	Space = EWidgetSpace::Screen;

	Paused = false;
	CatchedPlayer = nullptr;

}


void UDialogBubbleComponent::BeginPlay()
{
	Super::BeginPlay();

	BindEventToTrigger();
	//获取拥有者角色

	//OwnerCharacter = Cast<AAwCharacter>(GetOwner());

	//初始首次判断 
	if (DialogBubbleRangeComp)
	{
		float Radius = DialogBubbleRangeComp->GetScaledSphereRadius();
		bool IsInRadius = UGameplayFuncLib::HavePlayerCharacterInRange(GetOwner()->GetActorLocation() , Radius);
		
		if (IsInRadius)
		{
			if (GetWidget()) 
			{
				GetWidget()->SetVisibility(ESlateVisibility::HitTestInvisible);
			}
			SetComponentTickEnabled(true);

			//如果在范围内直接判断初始实装对话组
			CheckChangeCurSpecialDialogGroup();

			if (CurDialogGroupMobID.IsEmpty())
			{
				if (!DefaultDialogGroupMobID.IsEmpty())
				{
					ChangeCurDialogBubbleGroup(DefaultDialogGroupMobID);
				}
			}
		}
		else
		{
			if (GetWidget())
			{
				GetWidget()->SetVisibility(ESlateVisibility::Collapsed);
			}
			SetComponentTickEnabled(false);
		}
	}
}

void UDialogBubbleComponent::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
	Super::TickComponent(DeltaTime, TickType, ThisTickFunction);

	CurBubbleCoolDownTime += DeltaTime;


	if (CheckPlayerInRange())
	{
		ChooseCurDefaultDialogGroup();
		CheckChangeCurSpecialDialogGroup();

		if (CheckCurDialogBubbleCoolDown())
		{
			SubmitNextDialogBubble();
		}
	}

	CheckShowCondition();
}

void UDialogBubbleComponent::TryCatchPlayer(UPrimitiveComponent* OverlappedComponent, AActor* Actor, UPrimitiveComponent* Component,  int BodyIndex, bool Sweep, const FHitResult& SweepResult)
{
	if (Paused)
	{
		return;
	}
	AAwCharacter* OverlapCharacter = Cast<AAwCharacter>(Actor);
	if (OverlapCharacter->IsPlayerCharacter())
	{
		CatchedPlayer = OverlapCharacter;

		if (GetWidget())
		{
			GetWidget()->SetVisibility(ESlateVisibility::HitTestInvisible);
		}		
		SetComponentTickEnabled(true);
		CatchedPlayer = OverlapCharacter;
	}

}

bool UDialogBubbleComponent::CheckPlayerInRange()
{
	bool result = false;
	if (!CatchedPlayer)
	{
		SetComponentTickEnabled(false);
		return result;
	}
	if (FVector::Dist(CatchedPlayer->GetActorLocation(),GetOwner()->GetActorLocation())> HideWidgetDistance)
	{
		if (GetWidget())
		{
			GetWidget()->SetVisibility(ESlateVisibility::Collapsed);
			StopCurDialogBubble();
		}
		CatchedPlayer = nullptr;
		//SetComponentTickEnabled(false); 
		//注意不能同一帧处理 因为身上widget的Visbility修改是下帧生效
		return result;
	}

	result = true;

	return result;
}

void UDialogBubbleComponent::CheckShowCondition()
{
	for (auto Condition : ShowConditions)
	{
		UFunction* ConditionFunc = UCallFuncLib::GetUFunction(Condition.ClassPath, Condition.FunctionName);
		if (ConditionFunc)
		{
			struct
			{
				UActorComponent* DialogBubbleComponent;
				TArray<FString> Params;
				bool Result;
			}ConditionFuncParam;
			ConditionFuncParam.DialogBubbleComponent = this;
			ConditionFuncParam.Params = Condition.Params;
			this->ProcessEvent(ConditionFunc, &ConditionFuncParam);
			if (!ConditionFuncParam.Result)
			{
				SetVisibility(false);
				break;
			}
		}
	}
	SetVisibility(true);
}



void UDialogBubbleComponent::ChooseCurDefaultDialogGroup()
{
	if (DefaultDialogBubbleGroupArray.IsValidIndex(0))
	{
		return;
	}
	UAwDataManager* DataManager = UGameplayFuncLib::GetAwDataManager();
	for (auto DialogGroup: DefaultDialogBubbleGroupArray)
	{
		FDialogBubbleGroupMobel Target = DataManager->GetDialogBubbleGroupById(DialogGroup);
		if (CheckDialogGroupCondition(Target))
		{
			DefaultDialogGroupMobID = Target.DialogBubbleGroupID;
		}
	}
}

void UDialogBubbleComponent::BindEventToTrigger()
{
	for (UActorComponent* Component : GetOwner()->GetComponents())
	{
		if (IsValid(Cast<UDialogBubbleSphere>(Component)))
		{
			DialogBubbleRangeComp = Cast<UDialogBubbleSphere>(Component);
			break;
		}
	}

	if (DialogBubbleRangeComp)
	{
		DialogBubbleRangeComp->OnComponentBeginOverlap.AddDynamic(this,&UDialogBubbleComponent::TryCatchPlayer);

		HideWidgetDistance = FMath::Max(HideWidgetDistance, DialogBubbleRangeComp->GetScaledSphereRadius()+100);
	}
}

bool UDialogBubbleComponent::CheckCurDialogBubbleCoolDown()
{
	//检查上句话冷却 切换时立即提交时会直接remove
	bool result = true;

	if (TargetCoolDown <= 0)
	{
		RemoveOnceDialog();
		CurBubbleCoolDownTime = 0;
		return result;
	}
	if (CurBubbleCoolDownTime>= TargetCoolDown)
	{
		RemoveOnceDialog();
		CurBubbleCoolDownTime = 0;
		return result;
	}

	result = false;
	return result;
}

void UDialogBubbleComponent::RemoveOnceDialog()
{
	if (CurDialogBubbleGroupMobel.PlayType == EDialogBubbleGroupType::RandomOnce)
	{
		ClearCurDialogBubble();
	}

}




void UDialogBubbleComponent::ReadDefaultJsonConfig()
{
#if WITH_EDITOR
	if (JsonConfigPath.IsEmpty() || JsonConfigName.IsEmpty())
	{
		return;
	}

	FString JsonFilePath = JsonConfigPath + JsonConfigName;

	FString JsonStr;
	FFileHelper::LoadFileToString(JsonStr, *JsonFilePath);

	TSharedPtr<FJsonObject> JsonObject = nullptr;

	const TSharedRef<TJsonReader<TCHAR>> JsonReader = TJsonReaderFactory<TCHAR>::Create(JsonStr);
	const bool bSuccess = FJsonSerializer::Deserialize(JsonReader, JsonObject);

	if (!bSuccess)
	{
		UE_LOG(LogTemp, Error, TEXT("[AW] Parse json (%s) failed"), *JsonStr);
		return;
	}

	if (JsonObject)
	{
		DefaultDialogGroupMobID = UDataFuncLib::AwGetStringField(JsonObject, "DefaultDialogGroupMobID");

		DefaultDialogBubbleGroupArray.Empty();
		DefaultDialogBubbleGroupArray =UDataFuncLib::AwGetStringArrayField(JsonObject,"DefaultDialogBubbleGroupArray");

		SpecialTriggerDialogBubbleGroupMap.Empty();
		for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObject, "SpecialTriggerDialogBubbleGroupMap"))
		{
			FString DialogBubbleGroup = UDataFuncLib::AwGetStringField(JsonValue->AsObject(), "DialogGroupMobID");
			bool ForceSubmit = UDataFuncLib::AwGetBoolField(JsonValue->AsObject(), "ForceSubmit");
			SpecialTriggerDialogBubbleGroupMap.Add(DialogBubbleGroup, ForceSubmit);
		}
	}
#endif
}


UDialogBubbleSphere::UDialogBubbleSphere()
{
	SphereRadius  = 500;

	SetCollisionEnabled(ECollisionEnabled::QueryOnly);
	SetCollisionObjectType(ECollisionChannel::ECC_GameTraceChannel8);
	SetCollisionResponseToAllChannels(ECollisionResponse::ECR_Ignore);
	SetCollisionResponseToChannel(ECollisionChannel::ECC_GameTraceChannel7,ECollisionResponse::ECR_Overlap);

}
