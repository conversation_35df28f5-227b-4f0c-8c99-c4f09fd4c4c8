// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Components/WidgetComponent.h"
#include "Components/SphereComponent.h"
#include "TheAwakener_FO/FunctionLibrary/DataFuncLib.h"
#include "DialogBubbleComponent.generated.h"


class AAwCharacter;
/**
 *
 */
UENUM(BlueprintType)
enum class EDialogBubbleGroupType :uint8
{
	Sequence,//顺序
	SequenceLoop,//顺序循环
	RandomOnce,//仅随机一次
	RandomLoop,//随机
};
//
USTRUCT(BlueprintType)
struct FDialogBubble
{
	GENERATED_BODY()
public:
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		FString DialogTextId;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		float LifeTime;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		float FadeInTime = 1.0f;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		float FadeOutTime = 1.0f;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		float NextDialogCoolDown = 0.f;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		FJsonFuncData LifeTimeFunc;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		FJsonFuncData FadeInTimeFunc;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		FJsonFuncData FadeOutTimeFunc;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		FJsonFuncData NextDialogCoolDownFunc;
public:
	FDialogBubble() {};
	//如果属性是通过策略函数决定
	void TrySetPropertyByPolicy(UObject* Caller = nullptr);
	static FDialogBubble FromJson(TSharedPtr<FJsonObject> JsonObj);
	bool operator == (const FDialogBubble& NewDialogBubble) const
	{
		bool result = true;

		if (DialogTextId != NewDialogBubble.DialogTextId)
		{
			result = false;
		}
		if (LifeTime != NewDialogBubble.LifeTime)
		{
			result = false;
		}
		if (FadeInTime != NewDialogBubble.FadeInTime)
		{
			result = false;
		}
		if (FadeOutTime != NewDialogBubble.FadeOutTime)
		{
			result = false;
		}
		if (NextDialogCoolDown != NewDialogBubble.NextDialogCoolDown)
		{
			result = false;
		}
		return result;
	}
};

USTRUCT(BlueprintType)
struct FDialogBubbleGroupMobel
{
	GENERATED_BODY()
public:
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		FString DialogBubbleGroupID;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		EDialogBubbleGroupType PlayType = EDialogBubbleGroupType::Sequence;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		uint8 Priority = 0;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		TArray<FDialogBubble> DialogBubbleGroup;
	UPROPERTY()
		TArray<FJsonFuncData> CheckCondition;
public:
	static FDialogBubbleGroupMobel FromJson(TSharedPtr<FJsonObject> JsonObj);
	bool operator == (const FDialogBubbleGroupMobel& NewDialogBubbleGroupMobel) const
	{
		bool result = true;

		if (DialogBubbleGroupID != NewDialogBubbleGroupMobel.DialogBubbleGroupID)
		{
			result = false;
		}
		if (PlayType != NewDialogBubbleGroupMobel.PlayType)
		{
			result = false;
		}
		if (Priority != NewDialogBubbleGroupMobel.Priority)
		{
			result = false;
		}
		if (DialogBubbleGroup != NewDialogBubbleGroupMobel.DialogBubbleGroup)
		{
			result = false;
		}
		if (CheckCondition != NewDialogBubbleGroupMobel.CheckCondition)
		{
			result = false;
		}
		return result;
	}
};


/**
 */
UCLASS(ClassGroup = "DialogBubble", hidecategories = (Object, LOD, Lighting, TextureStreaming),
	meta = (DisplayName = "DialogBubbleSphere", BlueprintSpawnableComponent))
	class THEAWAKENER_FO_API UDialogBubbleSphere : public USphereComponent
{
	GENERATED_BODY()
public:
	UDialogBubbleSphere();
};



UCLASS(Blueprintable, ClassGroup = "DialogBubble", meta = (BlueprintSpawnableComponent))
class THEAWAKENER_FO_API UDialogBubbleComponent : public UWidgetComponent
{
	GENERATED_BODY()

//InEditor
public:
#if WITH_EDITORONLY_DATA
	UPROPERTY( EditAnyWhere, Category = Editor)
		FString JsonConfigPath;
	UPROPERTY( EditAnyWhere, Category = Editor)
		FString JsonConfigName;
#endif
	UFUNCTION(CallInEditor, Category = Editor)
		void ReadDefaultJsonConfig();


//Dialog
public:
	UPROPERTY(BlueprintReadWrite, EditAnyWhere, Category = DialogBubble)
		FString DefaultDialogGroupMobID;
	UPROPERTY(BlueprintReadWrite, EditAnyWhere, Category = DialogBubble)
		TArray<FString> DefaultDialogBubbleGroupArray;
	//bool 用于是否无视不同组之间切换的强制冷却
	UPROPERTY(BlueprintReadWrite, EditAnyWhere, Category = DialogBubble)
		TMap<FString, bool> SpecialTriggerDialogBubbleGroupMap;
	UPROPERTY(BlueprintReadWrite, EditAnyWhere, Category = DialogBubble)
		TArray<FJsonFuncData> ShowConditions;
public:
	//改变当前对话气泡组并尝试进行一次新对话气泡 ForceSubmit是否无视上个不同组对话强制冷却 
	UFUNCTION(BlueprintCallable, Category = DialogBubble)
		bool ChangeCurDialogBubbleGroup(FString MobID,bool ForceSubmit = false);
	//改变当前对话气泡组并修改其播放方式后进行一次新对话气泡 ForceSubmit是否无视上个不同组对话强制冷却 
	UFUNCTION(BlueprintCallable, Category = DialogBubble)
		bool ChangeCurDialogBubbleGroupInSpecialType(FString MobID,EDialogBubbleGroupType NewType, bool ForceSubmit = false);

	//强制提交独立语句 通常是强制提交
	UFUNCTION(BlueprintCallable, Category = DialogBubble)
		void SubmitNewIndependentDialogBubble(FDialogBubble NewDialogBubble, bool ForceSubmit = true);
	//自动进行组内下一句对话气泡
	UFUNCTION(BlueprintCallable, Category = DialogBubble)
		void SubmitNextDialogBubble();
	//立刻停止当前对话气泡
	UFUNCTION(BlueprintCallable, Category = DialogBubble)
		void StopCurDialogBubble();
	//立刻清空强制冷却
	UFUNCTION(BlueprintCallable, Category = DialogBubble)
		void ClearCurDialogBubbleCoolDown();

	//立刻清空当前对话组信息
	UFUNCTION(BlueprintCallable, Category = DialogBubble)
		void ClearCurDialogBubble();

	UFUNCTION(BlueprintCallable, Category = DialogBubble)
		bool CheckDialogGroupCondition(FDialogBubbleGroupMobel DialogGroupTarget);

	//暂停组件供外部统一调用
	UFUNCTION(BlueprintCallable, Category = DialogBubble)
		void SetComponentPaused(bool IsPaused);
public:
	UDialogBubbleComponent();
	virtual void BeginPlay() override;
	virtual void TickComponent(float DeltaTime, enum ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

protected:
	float CurBubbleCoolDownTime;
	float TargetCoolDown;
	int CurBubbleIndexInGroup;
	FString CurDialogGroupMobID;
	FDialogBubbleGroupMobel CurDialogBubbleGroupMobel;
	FDialogBubble CurDialogBubble;

private:

	bool CheckCurDialogBubbleCoolDown();
	void RemoveOnceDialog();
	void CheckChangeCurSpecialDialogGroup();
	void ChooseCurDefaultDialogGroup();

	bool Paused = false;

	AAwCharacter* CatchedPlayer;
	//AAwCharacter* OwnerCharacter;
//Triger
public:
	UPROPERTY(BlueprintReadWrite, EditAnyWhere)
		float HideWidgetDistance = 800;
	UDialogBubbleSphere* DialogBubbleRangeComp;
public:
	UFUNCTION()
	void TryCatchPlayer(UPrimitiveComponent* OverlappedComponent, AActor* Actor, UPrimitiveComponent* Component,  int BodyIndex, bool Sweep, const FHitResult& SweepResult);

	bool CheckPlayerInRange();
	void CheckShowCondition();
private:
	void BindEventToTrigger();
};
