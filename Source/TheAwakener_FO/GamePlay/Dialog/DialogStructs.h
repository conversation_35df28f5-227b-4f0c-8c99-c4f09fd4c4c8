// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "DialogStructs.generated.h"

/**
 * EventClip类型枚举
 */
UENUM(BlueprintType)
enum class EDialogEventClipType : uint8
{
	None,				//啥也不是，你表里填这个可不就出bug了？
	FocusTarget,		//镜头给到某人
	Speak,				//文字对话
	DoAction,			//做动作
	Emotion,			//做表情（这可能到最后都不会有了，美术实在太慢了）
	Wait,				//保持当前状况停留多久
	TurnToPlayer,		//转向玩家
	PlayAudio,			//播放音效
	HideDialogUI,		//一些特殊的演出情况需要暂时隐藏对话框UI
	GiveCurrencyToNPC		//打开赠送货币给NPC的UI
};

/**
 * 对话对象类型枚举
 */
UENUM(BlueprintType)
enum class EDialogTargetType : uint8
{
	Player,		//玩家所选的角色
	Buddy,		//玩家所带小弟
	Target,		//交谈对象角色
	AttendNpc,	//出席的其他NPC
};

/**
 * 一段对话片段
 * 数据非常繁多，因为傻屌UE就不让union，咋办呢？
 */
USTRUCT(BlueprintType)
struct FDialogEventClip
{
	GENERATED_BODY()
public:
	//这段脚本片段的Id，用来被索引为
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString Id = "";

	//下一段脚本的Id
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString NextId = "";

	//类型
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	EDialogEventClipType ClipType = EDialogEventClipType::FocusTarget;

	//指向对象的类型
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	EDialogTargetType TargetType = EDialogTargetType::Player;

	//对象所在槽位
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int SlotIndex = 0;

	//几号位置上的相机
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int CameraIndex = 0;

	//镜头给过去耗费的时间
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	float CameraFocusInSec = 0.5f;

	//说话者的名字
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString SpeakerName = "";

	//说话的文字
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString SpeakText = "";

	//要做的动作的id
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString ActionId = "";

	//动作的id
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString ActionId_TurnLeft = "";

	//动作的id
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString ActionId_TurnRight = "";
	
	//镜头给过去耗费的时间
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	float WaitSec = 0;

	//播放的音效Id
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString AudioId = "";

	//其他参数
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FString> Params;

	FDialogEventClip(){};

	//FocusTarget
	FDialogEventClip(FString ClipId, FString NextClipId, EDialogTargetType Target,
		int Slot = 0, int CameraSlot = 0, float FocusInSec = 0.5f):
		Id(ClipId), NextId(NextClipId), ClipType(EDialogEventClipType::FocusTarget),
		TargetType(Target), SlotIndex(Slot), CameraIndex(CameraSlot), CameraFocusInSec(FocusInSec){};

	//Speak
	FDialogEventClip(FString ClipId, FString NextClipId, EDialogTargetType Target, int Slot, FString ShowName, FString ShowText):
		Id(ClipId), NextId(NextClipId), ClipType(EDialogEventClipType::Speak),
		TargetType(Target), SlotIndex(Slot), SpeakerName(ShowName), SpeakText(ShowText){};

	//DoAction
	FDialogEventClip(FString ClipId, FString NextClipId, FString TodoActionId, EDialogTargetType Target, int Slot):
		Id(ClipId), NextId(NextClipId), ClipType(EDialogEventClipType::DoAction),
		TargetType(Target), SlotIndex(Slot), ActionId(TodoActionId){};

	//Wait
	FDialogEventClip(FString ClipId, FString NextClipId, float ToWaitSec):
		Id(ClipId), NextId(NextClipId), ClipType(EDialogEventClipType::Wait), WaitSec(ToWaitSec){};

	//TurnToPlayer
	FDialogEventClip(FString ClipId, FString NextClipId, EDialogTargetType Target,
		FString ActionIdTurnLeft, FString ActionIdTurnRight, float ToWaitSec):
		Id(ClipId), NextId(NextClipId), ClipType(EDialogEventClipType::TurnToPlayer), TargetType(Target),
		ActionId_TurnLeft(ActionIdTurnLeft), ActionId_TurnRight(ActionIdTurnRight), WaitSec(ToWaitSec){};

	//PlayAudio
	FDialogEventClip(FString ClipId, FString NextClipId, EDialogTargetType Target, FString AudioKey):
		Id(ClipId), NextId(NextClipId), ClipType(EDialogEventClipType::PlayAudio),
		TargetType(Target), AudioId(AudioKey){};

	//HideDialogUI
	FDialogEventClip(FString ClipId, FString NextClipId):
		Id(ClipId), NextId(NextClipId), ClipType(EDialogEventClipType::HideDialogUI){};

	//GiveCurrencyToNPC
	FDialogEventClip(FString ClipId, FString NextClipId, TArray<FString> ActionParams):
		Id(ClipId), NextId(NextClipId), ClipType(EDialogEventClipType::GiveCurrencyToNPC), Params(ActionParams){};
	
	static FDialogEventClip FromJson(TSharedPtr<FJsonObject> JsonObj);
};


/**
 * 一个对话选项
 */
USTRUCT(BlueprintType)
struct FDialogSelection
{
	GENERATED_BODY()
public:
	//出现这个选项的条件，必须每个条件都满足才会出现选项
	//(FDialogScriptObj这个对话当前数据, TArray<FString>策划配表参数)=>bool
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FString> ConditionFunc;

	//是否可以选择这个东西的条件函数，参数同ConditionFunc（可以混用）
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FString> EnableCheckFunc;

	//当前是否可以被选择
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	bool SelectionEnabled = true;

	//选项的icon
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString Icon;

	//这个选项的字面
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString Text;

	//选择这个选项，会进入一段Id为多少的DialogScriptClip
	//(FDialogScript这个对话当前数据，TArray<FString>策划配表数据)=>FString 下一段脚本的Id(DialogScriptClip.Id)，如果是打开商店之类的，就返回空的Id把
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString NextEventPickFunc;

	//辅助事件，是在选定这个选项后发生的事情，请别在每一个事件的选项后加这个，不然会出现一些游戏设计层的bug，参数同NextEventPickFunc（可混用）
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FString> ActionFunc;

	//用于在对话中选项打开其他的UI且对话不完全关闭
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	bool bStillInDialog = false;

	FDialogSelection(){};
	FDialogSelection(TArray<FString> ConditionFunctions, TArray<FString> EnableFunctions,
		FString ShowIcon, FString ShowText, FString NextEventPicker, TArray<FString> ActionFunctions, bool StillInDialog):
		ConditionFunc(ConditionFunctions), EnableCheckFunc(EnableFunctions),
		Icon(ShowIcon), Text(ShowText), NextEventPickFunc(NextEventPicker),
		ActionFunc(ActionFunctions),bStillInDialog(StillInDialog){};

	static FDialogSelection FromJson(TSharedPtr<FJsonObject> JsonObj);
};

/**
 * 一个对话脚本片段
 */
USTRUCT(BlueprintType)
struct FDialogScriptClip
{
	GENERATED_BODY()
public:
	//脚本片段的Id
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString Id;

	//启动时运行的第一个脚本是哪条
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString FirstEventId;

	//为了实现第一句话随机，后面的选项内容都一样的 合理 需求
	//添加一个函数去返回FirstEventId的值,如果函数为空或者找不到,则用默认的FirstEventId的值
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString FirstEventIdFunc;

	//筛选条件
	//表格内多条Clip自己填写条件，依次筛选
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FString> ConditionFunc;
	
	//这个片段的剧情脚本<Id, FDialogEventClip>
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TMap<FString, FDialogEventClip> Dialogs;

	//选项，如果最终算出来一个选项都没有，那么就代表没下文了
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FDialogSelection> Selections;

	//在一些Clip的最后没有选项，需要在Clip结束时运行一些脚本
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FString> EndClipScript;

	FDialogScriptClip(){}
	FDialogScriptClip(FString ScriptClipId, FString FirstEvent, FString FirstEventFunc, TArray<FString> Conditions,TMap<FString, FDialogEventClip> DialogEvents, TArray<FDialogSelection> DialogSelections, TArray<FString> EndScript):
		Id(ScriptClipId), FirstEventId(FirstEvent), FirstEventIdFunc(FirstEventFunc), ConditionFunc(Conditions),Dialogs(DialogEvents), Selections(DialogSelections), EndClipScript(EndScript){};

	static FDialogScriptClip FromJson(TSharedPtr<FJsonObject> JsonObj);
};

/**
 * 一段对话脚本的数据（策划表）
 */
USTRUCT(BlueprintType)
struct FDialogScriptModel
{
	GENERATED_BODY()
public:
	//对话脚本Id
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString Id;

	//启动时运行的第一个脚本是哪条
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString FirstClipId;

	//筛选条件
	//多个Dialog自己填写条件，依次筛选
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FString> ConditionFunc;

	//参与人的NpcId，主角和对话目标肯定参与，不然对话个屁
	//<所在对话参与人槽位, NpcId>这个对话参与人槽位是严肃的
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TMap<int, FString> JoinNpcId;
	
	//对话片段<ScriptClipId, 具体片段>
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TMap<FString, FDialogScriptClip> Scripts;

	//在这个对话脚本结束时运行一些脚本
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FString> EndDialogScript;

	FDialogScriptModel(){};
	FDialogScriptModel(FString ModelId, FString FirstClip, TArray<FString> Conditions,TMap<int, FString> CatchNpcId, TMap<FString, FDialogScriptClip> ScriptClips, TArray<FString> EndScript):
		Id(ModelId), FirstClipId(FirstClip),ConditionFunc(Conditions), JoinNpcId(CatchNpcId), Scripts(ScriptClips), EndDialogScript(EndScript){};

	static FDialogScriptModel FromJson(TSharedPtr<FJsonObject> JsonObj);

	bool IsValidDialog() const {return Id.IsEmpty() == false && Scripts.Num() > 0;}
};

/**
 * 运行中的对话脚本Object
 */
USTRUCT(BlueprintType)
struct FDialogScriptObj
{
	GENERATED_BODY()
public:
	//Model的Id
	UPROPERTY(BlueprintReadOnly)
	FString ModelId;

	//对话目标指针
	UPROPERTY(BlueprintReadOnly)
	AAwCharacter* DialogTarget = nullptr;

	//对话发起者 本地多人时，不一定是谁说的话
	UPROPERTY(BlueprintReadOnly)
	AAwCharacter* DialogTrigger = nullptr;

	//参与人指针<槽位id, 角色指针>
	UPROPERTY(BlueprintReadOnly)
	TMap<int, AAwCharacter*> AttendNpc;

	//正在进行的脚本片段
	UPROPERTY(BlueprintReadOnly)
	FDialogScriptClip DialogClip;

	//正在进行的EventClip的Id
	UPROPERTY(BlueprintReadWrite)
	FString PlayingEventId;
};
