// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "UObject/Object.h"
#include "DamageManager.generated.h"

/**
 * 处理所有的伤害信息的管理器，也是整个游戏的伤害管理员
 */
UCLASS()
class THEAWAKENER_FO_API UDamageManager : public UObject
{
	GENERATED_BODY()
	
public:

	static void AddDamage(AAwCharacter* Attacker, AAwCharacter* Defender, FDamageInfo DamInfo);
	static void AddDamage(FDamageDealer Dealer);
	static void DoDamage(AAwCharacter* Attacker, AAwCharacter* Defender, FDamageInfo DamInfo);
	static void ApplayCritcalAndBuffECS(AAwCharacter* Attacker, FDamageInfo &DamInfo);
	static void DealBuff(AAwCharacter* Attacker, FDamageInfo &DamInfo,FBuffObj& Buff, TArray<FJsonFuncData> BuffFunc);
	
};
