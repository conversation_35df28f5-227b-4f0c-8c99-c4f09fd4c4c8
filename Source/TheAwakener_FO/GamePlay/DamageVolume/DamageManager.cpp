// Fill out your copyright notice in the Description page of Project Settings.


#include "DamageManager.h"

#include "TheAwakener_FO/FunctionLibrary/CallFuncLib.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/Gameframework/Timeline/TimelineNode.h"


void UDamageManager::DoDamage(AAwCharacter* Attacker, AAwCharacter* Defender, FDamageInfo DamInfo)
{
	FDamageInfo DamageInfo = DamInfo;
	//Timeline还是要的
	TArray<UTimelineNode*> DamageTimeline;
	
	if (Defender && Defender->Dead() == false)	//如果想要鞭尸，就把Dead判断去掉
	{
		//计算玩家暴击
		if(Attacker)
		{
			DamageInfo.CriticalChance += Attacker->CharacterObj.CurProperty.CriticalChance;
			if (DamageInfo.ValuePowerArea.Contains(EDamageArea::InjuredCriticalPower))
			{
				DamageInfo.ValuePowerArea[EDamageArea::InjuredCriticalPower] +=Attacker->CharacterObj.CurProperty.CriticalRate ;
			}
			else
			{
				DamageInfo.ValuePowerArea.Add(EDamageArea::InjuredCriticalPower,Attacker->CharacterObj.CurProperty.CriticalRate);
			}
		}
		//先走攻击者所有的Buff
		if (Attacker && Attacker->CharacterObj.Buff.Num())
		{
			for (int bi = 0; bi < Attacker->CharacterObj.Buff.Num(); bi++)
			{
				if (Attacker->CharacterObj.Buff[bi].Model.OnHit.Num())
				{
					for (int i = 0; i < Attacker->CharacterObj.Buff[bi].Model.OnHit.Num(); i++)
					{
						if (Attacker->CharacterObj.Buff.IsValidIndex(bi) == false) break;
						UFunction* Func = UCallFuncLib::JsonFuncToUFunc(Attacker->CharacterObj.Buff[bi].Model.OnHit[i]);
						if (IsValid(Func) == false) continue;;
						
						struct {
							FBuffObj BuffObj;
							FDamageInfo DamInfo;
							AAwCharacter* Target;
							TArray<FString> Params;

							FBuffDamageResult Result;
						} FuncParam;
						
						FuncParam.BuffObj = Attacker->CharacterObj.Buff[bi];
						FuncParam.DamInfo = DamageInfo;
						FuncParam.Target = Defender;
						FuncParam.Params = Attacker->CharacterObj.Buff[bi].Model.OnHit[i].Params;
			
						Attacker->ProcessEvent(Func, &FuncParam);
						Attacker->CharacterObj.Buff[bi] = FuncParam.Result.BuffObj;
						DamageInfo = FuncParam.Result.DamageInfo;
						//DInfo->CopyFrom(&FuncParam.Result.DamageInfo);
						//UKismetSystemLibrary::PrintString(this, FString("Damage After Buff").Append(FString::FromInt(DInfo->FinalDamage())));
						if (FuncParam.Result.TimelineNode) DamageTimeline.Add(FuncParam.Result.TimelineNode);
					}
				}
			}
		}
		//然后是挨打者所有的BeHurt
		if (Defender->CharacterObj.Buff.Num())
		{
			for (int bi = 0; bi < Defender->CharacterObj.Buff.Num(); bi++)
			{
				if (Defender->CharacterObj.Buff[bi].Model.OnBeHurt.Num())
				{
					for (int i = 0; i < Defender->CharacterObj.Buff[bi].Model.OnBeHurt.Num(); i++)
					{
						UFunction* Func = UCallFuncLib::JsonFuncToUFunc(Defender->CharacterObj.Buff[bi].Model.OnBeHurt[i]);
						if (IsValid(Func) == false) continue;;
						
						struct {
							FBuffObj BuffObj;
							FDamageInfo DamInfo;
							AAwCharacter* Attacker;
							TArray<FString> Params;

							FBuffDamageResult Result;
						} FuncParam;
						
						FuncParam.BuffObj = Defender->CharacterObj.Buff[bi];
						FuncParam.DamInfo = DamageInfo;
						FuncParam.Attacker = Attacker;
						FuncParam.Params = Defender->CharacterObj.Buff[bi].Model.OnBeHurt[i].Params;
			
						Defender->ProcessEvent(Func, &FuncParam);
						Defender->CharacterObj.Buff[bi] = FuncParam.Result.BuffObj;
						DamageInfo = FuncParam.Result.DamageInfo;
						if (FuncParam.Result.TimelineNode)
							DamageTimeline.Add(FuncParam.Result.TimelineNode);
					}
				}
			}
		}
		//计算最终伤害是否是一次暴击
		if(DamageInfo.JudgeFinalDamageCritical())
		{
			//触发攻击者暴击时的buff
			if (Attacker && Attacker->CharacterObj.Buff.Num())
			{
				for (int bi = 0; bi < Attacker->CharacterObj.Buff.Num(); bi++)
				{
					if (Attacker->CharacterObj.Buff[bi].Model.OnCrit.Num())
					{
						for (int i = 0; i < Attacker->CharacterObj.Buff[bi].Model.OnCrit.Num(); i++)
						{
							UFunction* Func = UCallFuncLib::JsonFuncToUFunc(Attacker->CharacterObj.Buff[bi].Model.OnCrit[i]);
							if (IsValid(Func) == false) continue;;
						
							struct {
								FBuffObj BuffObj;
								FDamageInfo DamInfo;
								AAwCharacter* Target;
								TArray<FString> Params;

								FBuffDamageResult Result;
							} FuncParam;
						
							FuncParam.BuffObj = Attacker->CharacterObj.Buff[bi];
							FuncParam.DamInfo = DamageInfo;
							FuncParam.Target = Defender;
							FuncParam.Params = Attacker->CharacterObj.Buff[bi].Model.OnCrit[i].Params;
			
							Attacker->ProcessEvent(Func, &FuncParam);
							Attacker->CharacterObj.Buff[bi] = FuncParam.Result.BuffObj;
							DamageInfo = FuncParam.Result.DamageInfo;
							//DInfo->CopyFrom(&FuncParam.Result.DamageInfo);
							//UKismetSystemLibrary::PrintString(this, FString("Damage After Buff").Append(FString::FromInt(DInfo->FinalDamage())));
							if (FuncParam.Result.TimelineNode) DamageTimeline.Add(FuncParam.Result.TimelineNode);
						}
					}
				}
			}
		}
		
		//如果可以击杀目标，则走一次BeforeKilled 并且不是治疗
		if (DamageInfo.FinalDamage()  >= Defender->CharacterObj.CurrentRes.HP&&!DamageInfo.IsHeal)
		{
			if (Defender->CharacterObj.Buff.Num())
			{
				for (int bi = 0; bi < Defender->CharacterObj.Buff.Num(); bi++)
				{
					if (Defender->CharacterObj.Buff[bi].Model.OnBeKilled.Num())
					{
						for (int i = 0; i < Defender->CharacterObj.Buff[bi].Model.OnBeKilled.Num(); i++)
						{
							UFunction* Func = UCallFuncLib::JsonFuncToUFunc(Defender->CharacterObj.Buff[bi].Model.OnBeKilled[i]);
							if (IsValid(Func) == false) continue;;
							
							struct {
								FBuffObj BuffObj;
								FDamageInfo DamInfo;
								AAwCharacter* Attacker;
								TArray<FString> Params;

								FBuffDamageResult Result;
							} FuncParam;
							
							FuncParam.BuffObj = Defender->CharacterObj.Buff[bi];
							FuncParam.DamInfo = DamageInfo;
							FuncParam.Attacker = Attacker;
							FuncParam.Params = Defender->CharacterObj.Buff[bi].Model.OnBeKilled[i].Params;
			
							Defender->ProcessEvent(Func, &FuncParam);
							
							Defender->CharacterObj.Buff[bi] = FuncParam.Result.BuffObj;
							DamageInfo = FuncParam.Result.DamageInfo;
							if (FuncParam.Result.TimelineNode)
								DamageTimeline.Add(FuncParam.Result.TimelineNode);
						}
					}
				}
			}
		}
		//如果依然可以击杀目标，则攻击者OnKill
		if (Attacker && DamageInfo.FinalDamage() >= Defender->CharacterObj.CurrentRes.HP)
		{
			for (int bi = 0; bi < Attacker->CharacterObj.Buff.Num(); bi++)
			{
				if (Attacker->CharacterObj.Buff[bi].Model.OnKill.Num())
				{
					for (int i = 0; i < Attacker->CharacterObj.Buff[bi].Model.OnKill.Num(); i++)
					{
						UFunction* Func = UCallFuncLib::JsonFuncToUFunc(Attacker->CharacterObj.Buff[bi].Model.OnKill[i]);
						if (IsValid(Func) == false) continue;;
						
						struct {
							FBuffObj BuffObj;
							FDamageInfo DamInfo;
							AAwCharacter* Target;
							TArray<FString> Params;

							FBuffDamageResult Result;
						} FuncParam;
						
						FuncParam.BuffObj = Attacker->CharacterObj.Buff[bi];
						FuncParam.DamInfo = DamageInfo;
						FuncParam.Target = Defender;
						FuncParam.Params = Attacker->CharacterObj.Buff[bi].Model.OnKill[i].Params;
			
						Attacker->ProcessEvent(Func, &FuncParam);
						Attacker->CharacterObj.Buff[bi] = FuncParam.Result.BuffObj;
						DamageInfo = FuncParam.Result.DamageInfo;
						if (FuncParam.Result.TimelineNode)
							DamageTimeline.Add(FuncParam.Result.TimelineNode);
					}
				}
			}
		}
	}
	else
	{
		return;
	}
	DamageInfo.DamagePower.Physical = DamageInfo.FinalDamage();
	Defender->BeDamaged(DamageInfo);

	// 处理觉醒值转化和肉鸽道具能量恢复
	UGameplayFuncLib::ProcessAwakeningAndRogueRecovery(Attacker, Defender->CharacterObj.CurrentRes.HP, DamageInfo);
	//
		//告诉Timeline干活
		if (DamageTimeline.Num())
			for (int i = 0; i < DamageTimeline.Num(); i++)
				UGameplayFuncLib::GetTimelineManager()->AddNode(DamageTimeline[i]);
}

void UDamageManager::ApplayCritcalAndBuffECS(AAwCharacter* Attacker, FDamageInfo& DamInfo)
{//计算玩家暴击
		if(Attacker)
		{
			DamInfo.CriticalChance += Attacker->CharacterObj.CurProperty.CriticalChance;
			if (DamInfo.ValuePowerArea.Contains(EDamageArea::InjuredCriticalPower))
			{
				DamInfo.ValuePowerArea[EDamageArea::InjuredCriticalPower] +=Attacker->CharacterObj.CurProperty.CriticalRate ;
			}
			else
			{
				DamInfo.ValuePowerArea.Add(EDamageArea::InjuredCriticalPower,Attacker->CharacterObj.CurProperty.CriticalRate);
			}
		}
		//先走攻击者所有的Buff
		if (Attacker && Attacker->CharacterObj.Buff.Num())
		{
			for (int bi = 0; bi < Attacker->CharacterObj.Buff.Num(); bi++)
			{
				DealBuff(Attacker,DamInfo,Attacker->CharacterObj.Buff[bi],Attacker->CharacterObj.Buff[bi].Model.OnHit);
			}
		}
		//计算最终伤害是否是一次暴击
		if(DamInfo.JudgeFinalDamageCritical())
		{
			//触发攻击者暴击时的buff
			if (Attacker && Attacker->CharacterObj.Buff.Num())
			{
				for (int bi = 0; bi < Attacker->CharacterObj.Buff.Num(); bi++)
				{
					DealBuff(Attacker,DamInfo,Attacker->CharacterObj.Buff[bi],Attacker->CharacterObj.Buff[bi].Model.OnCrit);
				}
			}
		}
}

void UDamageManager::DealBuff(AAwCharacter* Attacker, FDamageInfo& DamInfo,FBuffObj& Buff, TArray<FJsonFuncData> BuffFunc)
{
	if (BuffFunc.Num())
	{
		for (int i = 0; i < BuffFunc.Num(); i++)
		{
			UFunction* Func = UCallFuncLib::JsonFuncToUFunc(BuffFunc[i]);
			if (IsValid(Func) == false) continue;;
						
			struct {
				FBuffObj BuffObj;
				FDamageInfo DamInfo;
				AAwCharacter* Target;
				TArray<FString> Params;

				FBuffDamageResult Result;
			} FuncParam;
						
			FuncParam.BuffObj = Buff;
			FuncParam.DamInfo = DamInfo;
			FuncParam.Target = nullptr;
			FuncParam.Params = BuffFunc[i].Params;
			
			Attacker->ProcessEvent(Func, &FuncParam);
			Buff = FuncParam.Result.BuffObj;
			DamInfo = FuncParam.Result.DamageInfo;
		}
	}
}

void UDamageManager::AddDamage(AAwCharacter* Attacker, AAwCharacter* Defender, FDamageInfo DamInfo)
{
	if (!Defender) return;
	Defender->AddBeDamaged(FDamageDealer(Attacker, Defender, DamInfo));
}
void UDamageManager::AddDamage(FDamageDealer Dealer)
{
	if (!Dealer.Defender)return;
	Dealer.Defender->AddBeDamaged(Dealer);
}
