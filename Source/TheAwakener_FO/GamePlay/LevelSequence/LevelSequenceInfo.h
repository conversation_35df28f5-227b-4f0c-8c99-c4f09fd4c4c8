// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Engine/DataTable.h"
#include "LevelSequenceInfo.generated.h"

UENUM(BlueprintType)
enum class ESequenceType : uint8
{
	Global,
	Local
};

//直接使用UlevelSequence 也行但需要对资源的预加载等有处理 否则先考虑软引用
USTRUCT(BlueprintType)
struct FLevelSequenceInfo:public FTableRowBase
{
	GENERATED_BODY()
public:
	//
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		FString SequenceID;
	//LevelSequence资源路径
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		FString SequencePath;
	//类型
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		ESequenceType SequenceType = ESequenceType::Global;
};

