#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/GamePlay/Achievement/Achievement.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacterInfo.h"
#include "TheAwakener_FO/GamePlay/Characters/Creation/ClassObj.h"
#include "TheAwakener_FO/GamePlay/Characters/Creation/NpcInfo.h"
#include "TheAwakener_FO/GamePlay/Item/ItemObj.h"
#include "TheAwakener_FO/GamePlay/Map/AwDungeonSave.h"
#include "TheAwakener_FO/GamePlay/Map/AwMapInfo.h"
#include "TheAwakener_FO/GamePlay/Quest/AwQuest.h"
#include "TheAwakener_FO/GamePlay/Setting/RogueGameSetting.h"
#include "TheAwakener_FO/GamePlay/Trading/Deal.h"
#include "AwRoleInfo.generated.h"

USTRUCT()
struct FLanguage
{
	GENERATED_BODY()

	TMap<FString,FString> Language;
};


USTRUCT(BlueprintType)
struct FAwRoleInfo
{
	GENERATED_BODY()
private:
	//道具快捷栏的下标，也就是选中的要用的东西了
	int FocusItemIndex = -1;

	FItemObj* UsingItem = nullptr;

public:
	
	/**
	 * 关系到整体游戏进度的Switch
	 */
	UPROPERTY(BlueprintReadOnly)
	TMap<FString, int> Switch;
	
	FAwRoleInfo(){};
	
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FAwCharacterInfo MainCharacter;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FAwCharacterInfo> OtherCharacters;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FItemObj> ItemObjs;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TMap<FString, int> Currency;

	//Role进度下的Npc们
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TMap<FString, FNpcInfo> NpcInfo;

	/**
	 * 背包里所有的装备
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FEquipment> EquipmentObjs;
	//新拿到的装备的UniqueId <UniqueId, 部位>
	UPROPERTY()
	TMap<FString, EEquipmentPart> NewEquipmentUniqueId;

	// 当前语言
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	ELanguage CurLanguage = ELanguage::Chinese;
	// 当前手边按钮图片类型
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString CurGamepadButtonType = "PlayStation";
	//已学新手引导
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	TArray<FString> KnownNewbieArray;

	/**
	 * 关系到整体游戏进度的Switch。
	 * 
	 * Switch是否刚好被改变，只有当Switch值变化的时候会变成True，但是不会自动变成false
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TMap<FString, bool> SwitchJustModified;
	//改变（增加）switch的值
	int AddSwitchValue(FString SwitchKey, int AddValue);
	//设置Switch的值
	int SetSwitchValue(FString SwitchKey, int ToValue);
	//通过SwitchId获取Switch的值，没有返回 DefaultValue
	int GetSwitch(FString SwitchId, int DefaultValue = 0);
	
	/**
	 * 背包里的武器
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FWeaponObj> WeaponObjs;
	//新拿到的武器的UniqueId <UniqueId, 武器类别>
	UPROPERTY()
	TMap<FString, EWeaponType> NewWeaponUniqueId;
	
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FAwQuest> Quests;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FAwMapInfo> Maps;

	//玩家的主角（MainActor）的职业进度信息
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FBattleClassObj> BattleClassInfo;

	//所有的成就信息
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FAchievementObj> Achievements;

	//副本地图信息
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FAwDungeonSave> DungeonRecords;

	//道具快捷栏内的道具Id
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FString> QuickSlotItemId;

	//登录游戏的第几次
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int LoginTimes = 0;
	
	//音量相关
	//主音量
	UPROPERTY(BlueprintReadOnly)
	float MainVolume = 1.0f;
	//BGM音量
	UPROPERTY(BlueprintReadOnly)
	float BGMVolume = 1.0f;
	//系统音音量
	UPROPERTY(BlueprintReadOnly)
	float SystemAudioVolume = 1.0f;
	//音效音量
	UPROPERTY(BlueprintReadOnly)
	float SFXVolume = 1.0f;
	//语音音量
	UPROPERTY(BlueprintReadOnly)
	float VoiceVolume = 1.0f;
	//UI音效音量
	UPROPERTY(BlueprintReadOnly)
	float UISoundVolume = 1.0f;

	// TODO: Game Setting 之后需要封装到一个结构里面 -by QuanShenLong
	// 相机辅助瞄准
	UPROPERTY(BlueprintReadWrite)
	bool Enable_CameraAimAssist = true;
	
	//下一个快捷栏，返回快捷栏的index
	int NextQuickSlot();
	//上一个快捷栏，返回快捷栏的index
	int PrevQuickSlot();
	//当前快捷栏所选中的道具，即准备要使用的玩意儿
	FItemObj* QuickSlotFocusItem();
	//根据指针找到道具的下标
	int GetItemObjIndexByPointer(const FItemObj* Pointer);
	//尝试使用选中的的道具，返回是否用成功了
	bool UseItem(AAwCharacter* User, EItemUseMethod Method);
	//锁定要使用的道具为快捷栏选中的那个
	void SetQuickSlotFocusItemToUsingItem();
	//根据id锁定使用的道具，返回是否成功，失败会导致UsingItem变成nullptr
	bool SetUsingItemByModelId(FString ItemModelId);
	//当前使用中的道具
	FItemObj* GetUsingItem() const;
	
	//将FocusItemIndex指向某个道具的第一个，返回是否成了，如果东西没了，肯定就成不了，FocusItemIndex会变成0（还有道具）或者-1（没任何道具了）
	bool AutoSetFocusItemTo(FString ItemId);
	int GetItemObjCount(FString Id);
	/**
	 * 给予role一个东西，这个东西一定是新建的，暂时不支持Character等东西
	 */
	void GiveThing(FThingObj ThingObj);
	
	//是否足以完成一笔交易，注意，这里的Price只检查Currency，有非Currency就会认为不够
	bool EnoughForDeal(FDeal Deal);
	/**
	 * 完成一笔交易，我这里只负责移除对应的Currency，并且获得对应的东西
	 */
	void MakeDeal(FDeal Deal);

	/**
	 * 获得某条职业的信息的下标
	 */
	int GetBattleClassIndexById(FString ClassId);

	/**
	 * 根据WeaponObj指针获得某个WeaponObj的下标
	 */
	int GetWeaponObjIndexByPointer(const FWeaponObj* WeaponPointer);
	/**
	 * 找到某个Weapon的指针(如果持有这个Weapon在背包，否则就是nullptr）
	 */
	FWeaponObj* GetWeaponObjPointer(const FWeaponObj Weapon);
	/**
	 * 根据某个UID找到武器的下标
	 */
	int GetWeaponObjIndexByUniqueId(const FString UniqueId);

	/**
	 * 接收到成就信号，通告给对应的成就
	 * @param SignalKey 信号的Key，对应FAchievementProgressInfo里面的Key
	 * @return 因此升级进度的成就的id
	 */
	TArray<FString> BeepAchievement(FString SignalKey);

	FAwDungeonSave GetDungeonRecordByDungeonId(FString DungeonId);

	//调整音量
	void SaveMainVolume(float Value){MainVolume = Value;}
	void SaveBGMVolume(float Value){BGMVolume = Value;}
	void SaveSystemAudioVolume(float Value){SystemAudioVolume = Value;}
	void SaveSFXVolume(float Value){SFXVolume = Value;}
	void SaveVoiceVolume(float Value){VoiceVolume = Value;}
	void SaveUISoundVolume(float Value){UISoundVolume = Value;}

	//获取音量
	float GetMainVolume() const {return MainVolume;}
	float GetBGMVolume() const {return BGMVolume;}
	float GetSystemAudioVolume() const {return SystemAudioVolume;}
	float GetSFXVolume() const {return SFXVolume;}
	float GetVoiceVolume() const {return VoiceVolume;}
	float GetUISoundVolume() const {return UISoundVolume;}

	void SetCurLanguage(ELanguage Language){CurLanguage = Language;}
	ELanguage GetCurLanguage() const {return CurLanguage;}

	void SetCurGamepadButtonType(FString GamepadButtonType) {CurGamepadButtonType = GamepadButtonType;}
	FString GetCurGamepadButtonType() const {return CurGamepadButtonType;}

	
};

