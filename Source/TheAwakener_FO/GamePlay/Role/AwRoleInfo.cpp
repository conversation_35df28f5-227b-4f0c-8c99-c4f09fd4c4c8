#include "AwRoleInfo.h"

#include "TheAwakener_FO/FunctionLibrary/CallFuncLib.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/UI/GetEquippmentHint.h"
#include "TheAwakener_FO/GamePlay/Quest/AwQuestManager.h"

void FAwRoleInfo::SetQuickSlotFocusItemToUsingItem()
{
	UsingItem = QuickSlotFocusItem();
}

bool FAwRoleInfo::SetUsingItemByModelId(FString ItemModelId)
{
	UsingItem = nullptr;
	for (int i = 0; i < this->ItemObjs.Num(); i++)
	{
		if (ItemObjs[i].Model.Id == ItemModelId)
		{
			UsingItem = &ItemObjs[i];
			return true;
		}
	}
	return false;
}

FItemObj* FAwRoleInfo::GetUsingItem() const{
	return UsingItem;
}

//尝试使用FocusItemIndex的道具，返回是否用成功了
bool FAwRoleInfo::UseItem(AAwCharacter* User, EItemUseMethod Method)
{
	if (!User) return false;
	if (!UsingItem || UsingItem->CanBeUsed() == false) return false;
		
	FItemUseMethod UseMethod;
	switch(Method)
	{
	case EItemUseMethod::Use: UseMethod = UsingItem->Model.OnUse; break;
	case EItemUseMethod::Throw: UseMethod = UsingItem->Model.OnThrow; break;
	case EItemUseMethod::Enchant:UseMethod = UsingItem->Model.OnEnchant; break;
	}
	TArray<FItemUseResult> UseResults;
	for (const FString Effect : UseMethod.UseEffects)
	{
		const FJsonFuncData JsonFunc = UCallFuncLib::StringToJsonFuncData(Effect);
		UFunction* Func = UCallFuncLib::JsonFuncToUFunc(JsonFunc);
		if (IsValid(Func) == false) continue;
		struct {
			FItemObj Item;
			AAwCharacter* User;
			TArray<FString> Params;
				
			FItemUseResult Result;
		} FuncParam;
		FuncParam.Item = *UsingItem;
		FuncParam.User = User;
		FuncParam.Params = JsonFunc.Params;
				
		GWorld->ProcessEvent(Func, &FuncParam);
		UseResults.Add(FuncParam.Result);
	}
	FItemUseResult Res = FItemUseResult::Merge(UseResults);
	if (Res.UseSuccessful == false)
	{
		//TODO: 用东西失败了可以在这里干点什么
		return false;
	}
	const FString UsedItemId = UsingItem->Model.Id;
	const int ItemIndex = GetItemObjIndexByPointer(UsingItem);
	if (ItemIndex < 0) return true;	//也算用成功了
	ItemObjs[ItemIndex].Durability -= Res.DurabilityCost;
	if (ItemObjs[ItemIndex].Durability <= 0 && Res.DestroyOnRunningOut == true)
	{
		ItemObjs.RemoveAt(ItemIndex);
		
		if (GetItemObjCount(UsedItemId) <= 0)
		{
			NextQuickSlot();
		}
	}

	User->OnCharacterUseItem.Broadcast(User,*UsingItem);
	return true;
}


bool FAwRoleInfo::EnoughForDeal(FDeal Deal)
{
	bool bPriceCondition = false;
	bool bFuncCondition = false;

	for (FThingObj ThingObj : Deal.Price)
	{
		if (bPriceCondition)
			break;
		switch (ThingObj.Type)
		{
		case EThingType::Character:
			{
				TMap<FString, int> Mobs;
				for (FAwCharacterInfo Character : this->OtherCharacters)
				{
					if (Mobs.Contains(Character.ClassId))
						Mobs[Character.ClassId] += 1;
					else
						Mobs.Add(Character.ClassId, 1);
				}
				bPriceCondition |= Mobs.Contains(ThingObj.Id) && Mobs[ThingObj.Id] >= ThingObj.Count;
				break;
			}
		case EThingType::Buff:
			{
				TMap<FString, int> TempBuffs;
				// TODO: 从Zone里面取？Zone里面没有怎么办？？？
				// for (FBuffObj BuffObj : UGameplayFuncLib::GetAwGameState()->MyCharacter->Buff)
				// TODO: 从RoleInfo取？那每次添加buff RoleInfo里面也要加？
				for (FBuffObj Buff : this->MainCharacter.Buff)
				{
					if (TempBuffs.Contains(Buff.Model.Id))
						TempBuffs[Buff.Model.Id] += 1;
					else
						TempBuffs.Add(Buff.Model.Id, 1);
				}
				bPriceCondition |= TempBuffs.Contains(ThingObj.Id) && TempBuffs[ThingObj.Id] >= ThingObj.Count;
				break;
			}
		case EThingType::Currency:
			bPriceCondition |= this->Currency.Contains(ThingObj.Id) && this->Currency[ThingObj.Id] >= ThingObj.Count;
			break;
		case EThingType::Equipment:
			{
				TMap<FString, int> TempEquipObjs;
				for (FEquipment EquipObj : this->EquipmentObjs)
				{
					if (TempEquipObjs.Contains(EquipObj.Id))
						TempEquipObjs[EquipObj.Id] += 1;
					else
						TempEquipObjs.Add(EquipObj.Id, 1);
				}
				bPriceCondition |= TempEquipObjs.Contains(ThingObj.Id) && TempEquipObjs[ThingObj.Id] >= ThingObj.Count;
				break;
			}
		case EThingType::Switch:
			//TODO Switch - MainCharacter所在的地图的Switch数据
			bPriceCondition |= false;
			break;
		case EThingType::Item:
			{
				TMap<FString, int> TempItems;
				for (FItemObj ItemObj : this->ItemObjs)
				{
					if (TempItems.Contains(ItemObj.Model.Id))
						TempItems[ItemObj.Model.Id] += 1;
					else
						TempItems.Add(ItemObj.Model.Id, 1);
				}
				bPriceCondition |= TempItems.Contains(ThingObj.Id) && TempItems[ThingObj.Id] >= ThingObj.Count;
				break;
			}
		case EThingType::Pointer:
			//TODO 指针
			bPriceCondition |= false;
			break;
		
		default:
			break;
		}
	}
	
	if (Deal.FuncCondition.Num() <= 0)
		bFuncCondition = true;
	else
	{
		bFuncCondition = true;
		for (FJsonFuncData FuncCondition : Deal.FuncCondition)
		{
			UFunction* Func = UCallFuncLib::JsonFuncToUFunc(FuncCondition);
			if (IsValid(Func) == false) continue;
			
			struct {
				FDeal Deal;
				FAwRoleInfo RoleInfo;

				bool Result;
			} FuncParam;
			
			FuncParam.Deal = Deal;
			FuncParam.RoleInfo = *this;
			
			GWorld->ProcessEvent(Func, &FuncParam);
			
			bFuncCondition &= FuncParam.Result;
			if (!bFuncCondition)
				break;
		}
	}
	
	return bPriceCondition && bFuncCondition;
}

int FAwRoleInfo::GetItemObjCount(FString Id)
{
	int Res = 0;
	for (FItemObj Item : this->ItemObjs)
	{
		if (Item.Model.Id == Id) Res += 1;
	}
	return Res;
}

int FAwRoleInfo::GetItemObjIndexByPointer(const FItemObj* Pointer)
{
	if (!Pointer) return -1;
	for (int i = 0; i < ItemObjs.Num(); i++)
	{
		if (&ItemObjs[i] == Pointer)
		{
			return i;
		}
	}
	return -1;
}

int FAwRoleInfo::NextQuickSlot()
{
	if (QuickSlotItemId.Num() <= 0)
	{
		FocusItemIndex = -1;
		return -1;
	}
	FocusItemIndex += 1;
	if (FocusItemIndex >= QuickSlotItemId.Num())
	{
		FocusItemIndex = 0;
	}
	// SetQuickSlotFocusItemToUsingItem();
	return FocusItemIndex;
}

int FAwRoleInfo::PrevQuickSlot()
{
	if (QuickSlotItemId.Num() <= 0)
	{
		FocusItemIndex = -1;
		return -1;
	}
	FocusItemIndex -= 1;
	if (FocusItemIndex < 0)
	{
		FocusItemIndex = QuickSlotItemId.Num() - 1;
	}
	// SetQuickSlotFocusItemToUsingItem();
	return FocusItemIndex;
}

FItemObj* FAwRoleInfo::QuickSlotFocusItem()
{
	if (FocusItemIndex < 0 || QuickSlotItemId.Num() <= 0) return nullptr;
	FocusItemIndex = FMath::Clamp(FocusItemIndex, 0, QuickSlotItemId.Num() - 1);	//这个side effect如果不好就不要他就完了
	for (int i = 0; i < ItemObjs.Num(); i++)
	{
		if (ItemObjs[i].Model.Id == QuickSlotItemId[FocusItemIndex])
		{
			return &ItemObjs[i];
		}
	}
	return nullptr;
}

bool FAwRoleInfo::AutoSetFocusItemTo(FString ItemId)
{
	if (ItemObjs.Num() <= 0)
	{
		FocusItemIndex = -1;
		return false;
	}
	for (int i = 0; i < ItemObjs.Num(); i++)
	{
		if (ItemObjs[i].Model.Id == ItemId)
		{
			FocusItemIndex = i;
			return true;
		}
	}
	FocusItemIndex = 0;
	return false;
}

void FAwRoleInfo::GiveThing(FThingObj ThingObj)
{
		//To Do ... BroadCast  EveryCharacter Get Thing
		UAwGameInstance* GameInstance = UGameplayFuncLib::GetAwGameInstance();
		UAwQuestManager* QuestManger = nullptr;
		if (IsValid(GameInstance))
		{
			QuestManger = GameInstance->GetSubsystem<UAwQuestManager>();
		}
		if (IsValid(QuestManger))
		{
			QuestManger->TryCheckQuestTargetProgressByTypeAndKeyWord(EQuestTargetType::Collect,ThingObj.Id);
		}
	
		switch (ThingObj.Type)
		{
		case EThingType::Character:
			{
				const FAwCharacterInfo CharacterInfo = FThingObj::CreateCharacterByThing(ThingObj);
				for (int i = 0; i < ThingObj.Count; i++)
					this->OtherCharacters.Add(CharacterInfo);
				break;
			}
		case EThingType::Buff:
			{
				if (!UGameplayFuncLib::GetAwGameState()->GetMyCharacter()) return;
				const FAddBuffInfo AddBuffInfo = FThingObj::CreateBuffByThing(ThingObj);
				UGameplayFuncLib::GetAwGameState()->GetMyCharacter()->AddBuff(AddBuffInfo);
				break;
			}
		case EThingType::Currency:
			{
				if (this->Currency.Contains(ThingObj.Id) == false)
				{
					this->Currency.Add(ThingObj.Id, ThingObj.Count);
				}else
				{
					this->Currency[ThingObj.Id] += ThingObj.Count;
				}
				
				break;
			}
		case EThingType::Equipment:
			{
				for (int i = 0; i < ThingObj.Count; i++)
				{
					FEquipment ThisEquipment = FThingObj::CreateEquipmentByThing(ThingObj);
					this->EquipmentObjs.Add(ThisEquipment);
					this->NewEquipmentUniqueId.Add(ThisEquipment.UniqueId, ThisEquipment.PartType);
					if(UGameplayFuncLib::GetUiManager()->OpenedWidgets.Contains("GetEquippment"))
					{
						UGameplayFuncLib::GetUiManager()->Hide("GetEquippment");
						
						UGetEquippmentHint* GetEquippmentHint =
							Cast<UGetEquippmentHint>(UGameplayFuncLib::GetUiManager()->Show("GetEquippment",9));
						if(GetEquippmentHint)
						{
							GetEquippmentHint->Set(ThisEquipment.Id);
						}
					}
					else
					{
						UGetEquippmentHint* GetEquippmentHint =
							Cast<UGetEquippmentHint>(UGameplayFuncLib::GetUiManager()->Show("GetEquippment",9));
						
						if(GetEquippmentHint)
						{
							GetEquippmentHint->Set(ThisEquipment.Id);
						}
					}
				}
				break;
			}
		case EThingType::Item:
			{
				FItemModel ItemModel = FThingObj::CreateItemByThing(ThingObj);
				FItemObj ItemObj = FItemObj(ItemModel);
				for (int i = 0; i < ThingObj.Count; i++)
					this->ItemObjs.Add(ItemObj);
				break;
			}
		case EThingType::Switch:
			{
				//TODO:
				break;
			}
		case EThingType::WeaponModel:
			{
				for (int i = 0; i < ThingObj.Count; i++)
				{
					FWeaponObj WeaponObj = FThingObj::CreateWeaponByThing(ThingObj);
					this->WeaponObjs.Add(WeaponObj);
					this->NewWeaponUniqueId.Add(WeaponObj.UniqueId, WeaponObj.Model.WeaponType);

					//临时，各个职业2级武器不弹这个UI，另外显示特殊UI
					/*if(ThingObj.Id == "Elven_GreatSword" || ThingObj.Id == "Iron_Sword" || ThingObj.Id == "Skeleton_Buckler" || ThingObj.Id == "GoldenCross_Spear")
						continue;*/
					
					if(UGameplayFuncLib::GetUiManager()->OpenedWidgets.Contains("GetEquippment"))
					{
						UGameplayFuncLib::GetUiManager()->Hide("GetEquippment");
						
						UGetEquippmentHint* GetEquippmentHint =
							Cast<UGetEquippmentHint>(UGameplayFuncLib::GetUiManager()->Show("GetEquippment",9));
						if(GetEquippmentHint)
						{
							GetEquippmentHint->Set(WeaponObj.Model.Id);
						}
					}
					else
					{
						UGetEquippmentHint* GetEquippmentHint =
							Cast<UGetEquippmentHint>(UGameplayFuncLib::GetUiManager()->Show("GetEquippment",9));
						
						if(GetEquippmentHint)
						{
							GetEquippmentHint->Set(WeaponObj.Model.Id);
						}
					}
				}
				break;
			}
		case EThingType::Pointer:
			{
				//TODO:
				break;
			}
		default: break;
		}
}

void FAwRoleInfo::MakeDeal(FDeal Deal)
{
	for (const FThingObj Price : Deal.Price)
	{
		switch (Price.Type)
		{
		case EThingType::Currency: if (this->Currency.Contains(Price.Id)) Currency[Price.Id] -= Price.Count; break;
		default: break;
		}
	}
	for (const FThingObj Good : Deal.Goods)
	{
		this->GiveThing(Good);
	} 
}

int FAwRoleInfo::GetBattleClassIndexById(FString ClassId)
{
	for (int i = 0; i < BattleClassInfo.Num(); i++)
	{
		if(BattleClassInfo[i].ClassId == ClassId) return i;
	}
	return -1;
}

int FAwRoleInfo::GetWeaponObjIndexByPointer(const FWeaponObj* WeaponPointer)
{
	for (int i = 0; i < WeaponObjs.Num(); i++)
	{
		if (&WeaponObjs[i] == WeaponPointer)
		{
			return i;
		}
	}
	return -1;
}

FWeaponObj* FAwRoleInfo::GetWeaponObjPointer(const FWeaponObj Weapon)
{
	for (int i = 0; i < WeaponObjs.Num(); i++)
	{
		if (WeaponObjs[i] == Weapon)
		{
			return &WeaponObjs[i];
		}
	}
	return nullptr;
}

int FAwRoleInfo::GetWeaponObjIndexByUniqueId(const FString UniqueId)
{
	for (int i = 0; i < WeaponObjs.Num(); i++)
	{
		if (WeaponObjs[i].UniqueId == UniqueId)
		{
			return i;
		}
	}
	return -1;
}

TArray<FString> FAwRoleInfo::BeepAchievement(FString SignalKey)
{
	TArray<FString> Res;
	for (int i = 0; i < this->Achievements.Num(); i++)
	{
		if(Achievements[i].Done()) continue;

		if (Achievements[i].BeepBySignal(SignalKey) == true)
		{
			Res.Add(Achievements[i].Model.Id);
		}
	}
	return Res;
}

FAwDungeonSave FAwRoleInfo::GetDungeonRecordByDungeonId(FString DungeonId)
{
	for(FAwDungeonSave DungeonRecord : DungeonRecords)
	{
		if(DungeonRecord.DungeonId == DungeonId)
			return DungeonRecord;
	}
	return FAwDungeonSave();
}

int FAwRoleInfo::GetSwitch(FString SwitchId, int DefaultValue)
{
	if(Switch.Contains(SwitchId))
	{
		return Switch[SwitchId];
	}
	else
	{
		return DefaultValue;
	}
}

int FAwRoleInfo::AddSwitchValue(FString SwitchKey, int AddValue)
{
	if (Switch.Contains(SwitchKey) == false)
	{
		Switch.Add(SwitchKey, AddValue);
		SwitchJustModified.Add(SwitchKey, true);
		return AddValue;
	}else
	{
		Switch[SwitchKey] += AddValue;
		if (AddValue != 0) SwitchJustModified.Add(SwitchKey, true);
		return Switch[SwitchKey];
	}
}

int FAwRoleInfo::SetSwitchValue(FString SwitchKey, int ToValue)
{
	const  int WasValue = GetSwitch(SwitchKey);
	Switch.Add(SwitchKey, ToValue);
	if (WasValue != ToValue)
	{
		SwitchJustModified.Add(SwitchKey, true);
		UGameplayFuncLib::GetAwGameInstance()->SwitchChangeDelegate.Broadcast(SwitchKey);
	}
	UGameplayFuncLib::SaveGame();
	return ToValue;
}
