// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/GamePlay/Characters/Elemental/Elemental.h"
#include "UObject/Object.h"
#include "ElementalCrystal.generated.h"


/**
 * 单个的元素效果，一个水晶的一条有3个（3个等级）
 */
USTRUCT(BlueprintType)
struct FOneElementalEffect
{
	GENERATED_BODY()
public:
	//指向效果的脚本函数，但是不需要填写参数，所以末尾的括号"()"不要写
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString EffectFuncKey;

	//效果参数1，根据效果函数不同，意义也会不同，从若干个元素中随机出来1个
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<float> Param0;

	//参数效果2，根据效果参数不同，意义也不同，同样是随机的
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<float> Param1;
};

/**
 * 一条元素效果的模板
 */
USTRUCT(BlueprintType)
struct FElementalEffectLineModel
{
	GENERATED_BODY()
public:
	//效果的id，这个id可能会被UI引用
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString Id;
	
	//每个等级（应该只有3个）的效果
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FOneElementalEffect> Effect;

	//可以被随机抽取的Tag，有这个Tag的水晶才能抽到这个作为基础的元素效果之外的元素效果
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString Tag;
};

/**
 * 一条元素效果
 */
USTRUCT(BlueprintType)
struct FElementalEffectLineObj
{
	GENERATED_BODY()
public:
	//效果的id，这个id可能会被UI引用
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString Id;
	
	//每个等级的效果，是已经组织出来的可以转化为Json Function的字符串
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FString> Effect;

	//可以被随机抽取的Tag
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString Tag;

	static FElementalEffectLineObj FromModel(FElementalEffectLineModel Model);
};

/**
 * 一个元素水晶球的模板，根据模板才会产生出水晶球
 */
USTRUCT(BlueprintType)
struct FElementalCrystalModel
{
	GENERATED_BODY()
public:
	//这属于什么属性
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	EChaElemental Elemental = EChaElemental::Fire;

	//水晶球的模型信息，随机从中抽取
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FString> ViewPath;

	//固有的效果（3条）
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FElementalEffectLineModel> EffectLineModels;

	//随机选取效果的Tag
	FString RandomPickTag;
};

/**
 * 一个水晶球的实体
 */
USTRUCT(BlueprintType)
struct FElementalCrystalObj
{
	GENERATED_BODY()
public:
	//水晶球模板
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FElementalCrystalModel Model;

	//实际的效果（5条）
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FString> Effect;

	//解锁的等级
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<int> UnlockLevel;
};
