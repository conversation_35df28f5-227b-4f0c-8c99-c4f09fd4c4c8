// Fill out your copyright notice in the Description page of Project Settings.


#include "ElementalTalent.h"

#include "TheAwakener_FO/FunctionLibrary/DataFuncLib.h"

FElementalTalentUIInfo FElementalTalentUIInfo::FromJson(TSharedPtr<FJsonObject> JsonObj)
{
	FElementalTalentUIInfo Res = FElementalTalentUIInfo();
	Res.Id = UDataFuncLib::AwGetStringField(JsonObj, "Id");
	Res.Elemental = UDataFuncLib::AwGetEnumField<EChaElemental>(JsonObj, "Elemental",EChaElemental::Physical);
	Res.Level = JsonObj->GetIntegerField("Level");
	Res.UnlockCostEP = JsonObj->GetIntegerField("EP");
	Res.Name = UDataFuncLib::AwGetStringField(JsonObj, "Name");
	Res.BaseEffect =  UDataFuncLib::AwGetStringField(JsonObj, "BaseEffect");
	Res.ElementalTrigger = UDataFuncLib::AwGetEnumField<EElementalTriggerType>(JsonObj, "TriggerType",EElementalTriggerType::OnHit);
	Res.IconPath = UDataFuncLib::AwGetStringField(JsonObj, "Icon");
	if (JsonObj->HasField("TerrainEffect"))
		for (const TSharedPtr<FJsonValue, ESPMode::ThreadSafe> TerrEff : JsonObj->GetArrayField("TerrainEffect"))
		{
			if (TerrEff->AsObject()->HasField("Terrain") && TerrEff->AsObject()->HasField("Desc"))
			{
				Res.TerrainEffect.Add(
					UDataFuncLib::FStringToEnum<ETerrainType>(TerrEff->AsObject()->GetStringField("Terrain")),
					TerrEff->AsObject()->GetStringField("Desc")
				);
			}
		} 
	if (JsonObj->HasField("WeatherEffect"))
		for (const TSharedPtr<FJsonValue, ESPMode::ThreadSafe> TerrEff : JsonObj->GetArrayField("WeatherEffect"))
		{
			if (TerrEff->AsObject()->HasField("Weather") && TerrEff->AsObject()->HasField("Desc"))
			{
				Res.WeatherEffect.Add(
					UDataFuncLib::FStringToEnum<EWeatherType>(TerrEff->AsObject()->GetStringField("Weather")),
					TerrEff->AsObject()->GetStringField("Desc")
				);
			}
		}

	return Res;
}

FElementalTalent FElementalTalent::FromJson(TSharedPtr<FJsonObject> JsonObj)
{
	FElementalTalent Res = FElementalTalent();
	Res.Id = UDataFuncLib::AwGetStringField(JsonObj, "Id");
	Res.BaseEffect = UDataFuncLib::JsonValueArrayToFStringArray(UDataFuncLib::AwGetArrayField(JsonObj, "BaseEffect"));
	Res.ElementalType = UDataFuncLib::AwGetEnumField<EChaElemental>(JsonObj, "Elemental",EChaElemental::Physical);
	Res.ElementalTrigger = UDataFuncLib::AwGetEnumField<EElementalTriggerType>(JsonObj, "TriggerType",EElementalTriggerType::OnHit);	
	if (JsonObj->HasField("TerrainEffect"))
		for (const TSharedPtr<FJsonValue, ESPMode::ThreadSafe> TerrEff : JsonObj->GetArrayField("TerrainEffect"))
		{
			if (TerrEff->AsObject()->HasField("Terrain") && TerrEff->AsObject()->HasField("Effect"))
			{
				Res.TerrainEffect.Add(
					UDataFuncLib::FStringToEnum<ETerrainType>(TerrEff->AsObject()->GetStringField("Terrain")),
					TerrEff->AsObject()->GetStringField("Effect")
				);
			}
		} 
	if (JsonObj->HasField("WeatherEffect"))
		for (const TSharedPtr<FJsonValue, ESPMode::ThreadSafe> TerrEff : JsonObj->GetArrayField("WeatherEffect"))
		{
			if (TerrEff->AsObject()->HasField("Weather") && TerrEff->AsObject()->HasField("Effect"))
			{
				Res.WeatherEffect.Add(
					UDataFuncLib::FStringToEnum<EWeatherType>(TerrEff->AsObject()->GetStringField("Weather")),
					TerrEff->AsObject()->GetStringField("Effect")
				);
			}
		}

	return Res;
}

