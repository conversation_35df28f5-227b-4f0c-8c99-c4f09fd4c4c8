// Fill out your copyright notice in the Description page of Project Settings.


#include "ElementalCrystal.h"

FElementalEffectLineObj FElementalEffectLineObj::FromModel(FElementalEffectLineModel Model)
{
	FElementalEffectLineObj Res;
	Res.Id = Model.Id;
	Res.Tag = Model.Tag;
	for (int i = 0; i < Model.Effect.Num(); i++)
	{
		
		FString EffRes = Model.Effect[i].EffectFuncKey;
		EffRes.RemoveFromEnd("()");
		EffRes.Append("(");
		if (Model.Effect[i].Param0.Num() > 0)
		{
			EffRes.Append(FString::SanitizeFloat(Model.Effect[i].Param0[FMath::RandRange(0, Model.Effect[i].Param0.Num() - 1)]));
		}else
		{
			EffRes.Append("0");	//第一个参数如果没有要补一个0
		}
		
		if (Model.Effect[i].Param1.Num() > 0)
		{
			EffRes.Append(",");
			EffRes.Append(FString::SanitizeFloat(Model.Effect[i].Param1[FMath::RandRange(0, Model.Effect[i].Param1.Num() - 1)]));
		}
		EffRes.Append(")");
		
		Res.Effect.Add(EffRes);
	}

	return Res;
}