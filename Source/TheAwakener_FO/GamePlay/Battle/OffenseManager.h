// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/GamePlay/Characters/Battle/BattleStructs.h"
#include "TheAwakener_FO/GamePlay/Characters/HitBox/ActorCatcher.h"
#include "UObject/Object.h"
#include "OffenseManager.generated.h"

/**
 * 攻击管理器，其实只是一个函数库罢了
 */
UCLASS(BlueprintType)
class THEAWAKENER_FO_API UOffenseManager : public UObject
{
	GENERATED_BODY()
public:
	/**
	 * 发起一次侵犯
	 * @param Attacker 攻击者
	 * @param OffenseInfo 攻击信息
	 * @param TargetInfo 目标信息
	 * @param AttackerInCharge 攻击负责人，可以为nullptr
	 * @param IncludeAttackerHitBox 是否附带攻击者本身的动作的攻击盒作为命中判定盒子，通常来说，动作为true，其他false
	 */
	UFUNCTION(BlueprintCallable)
	static void DoOffense(UAttackHitManager* Attacker, FOffenseInfo OffenseInfo, FBeCaughtActorInfo TargetInfo, AAwCharacter* AttackerInCharge, bool IncludeAttackerHitBox);

	/**
	 * 发起一次来自Buff的侵犯
	 * @param OffenseInfo 攻击信息
	 * @param Target 挨打的人，只能是个角色，因为只有角色才有buff
	 * @param AttackerInCharge 攻击负责人
	 */
	UFUNCTION(BlueprintCallable)
	static void DoBuffOffense(FOffenseInfo OffenseInfo, AAwCharacter* Target, AAwCharacter* AttackerInCharge);

	static void DealBuffECS(AAwCharacter* AttackerInCharge, FOffenseInfo& OffenseInfo);
};
