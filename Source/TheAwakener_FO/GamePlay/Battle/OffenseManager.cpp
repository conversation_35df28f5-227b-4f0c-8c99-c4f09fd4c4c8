// Fill out your copyright notice in the Description page of Project Settings.


#include "OffenseManager.h"

#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "TheAwakener_FO/GamePlay/Characters/Battle/OffendedObject.h"

void UOffenseManager::DoOffense(UAttackHitManager* Attacker, FOffenseInfo OffenseInfo, FBeCaughtActorInfo TargetInfo, AAwCharacter* AttackerInCharge, bool IncludeAttackerHitBox)
{
	if (!TargetInfo.BeCaughtActor) return;	//没有挨打对象直接结束

	IOffendedObject* Target =Cast<IOffendedObject>(TargetInfo.BeCaughtActor);

	if (!Target) return;
	const FOffendedCaughtResult Res = Target->CanBeOffended(OffenseInfo, Attacker, AttackerInCharge, IncludeAttackerHitBox);
	if (Res.Hit)
	{
		UScoreManager* ScoreManager = UGameplayFuncLib::GetScoreManager();
		const AAwCharacter* Victim = Cast<AAwCharacter>(TargetInfo.BeCaughtActor);
		if (ScoreManager && Victim && AttackerInCharge && AttackerInCharge->IsPlayerCharacter())
		{
			ScoreManager->AddCombo(OffenseInfo.ComboOffenseTime);
		}

		if (AttackerInCharge){
			TArray<UTimelineNode*> Nodes;
			for (int i = 0; i < AttackerInCharge->CharacterObj.Buff.Num(); i++)
			{
				if(AttackerInCharge->CharacterObj.Buff[i].Model.Id.IsEmpty() == false)
				{
					for (const FJsonFuncData OnOffense : AttackerInCharge->CharacterObj.Buff[i].Model.OnOffense)
					{
						UFunction* Func = UCallFuncLib::JsonFuncToUFunc(OnOffense);
						if (Func)
						{
							struct
							{
								FBuffObj Buff;		//运行的buff
								AActor* Target;	//打中了什么，不一定是个角色的
								FOffenseInfo OInfo;	//用来打中的信息
								TArray<FString> Param;	//函数的参数
								FBuffOffenseResult Result;	//返回
							} FuncParam;
							FuncParam.Buff = AttackerInCharge->CharacterObj.Buff[i];
							FuncParam.Target = TargetInfo.BeCaughtActor;
							FuncParam.OInfo = OffenseInfo;
							FuncParam.Param = OnOffense.Params;
							AttackerInCharge->ProcessEvent(Func, & FuncParam);
							AttackerInCharge->CharacterObj.Buff[i] = FuncParam.Result.BuffObj;
							OffenseInfo = FuncParam.Result.OffenseInfo;
							if (FuncParam.Result.TimelineNode) Nodes.Add(FuncParam.Result.TimelineNode);
						}
					} 
				}
				
			}
			for (UTimelineNode* Node : Nodes) UGameplayFuncLib::GetTimelineManager()->AddNode(Node); 
		}
		
		Target->BeOffended(OffenseInfo, Attacker, AttackerInCharge, Res.BeCaughtActorInfo.CaughtHitBoxComponent, Res.AttackHitBox, false, false);
	}
}

void UOffenseManager::DoBuffOffense(FOffenseInfo OffenseInfo, AAwCharacter* Target, AAwCharacter* AttackerInCharge)
{
	if (!Target || Target->Dead(true)) return;
	const FOffendedCaughtResult Res = Target->CanBeOffended(OffenseInfo, nullptr, AttackerInCharge, false);
	if (Res.Hit)
	{
		Target->BeOffended(OffenseInfo, nullptr, AttackerInCharge, nullptr, nullptr, true, true);
	}
}

void UOffenseManager::DealBuffECS(AAwCharacter* AttackerInCharge, FOffenseInfo& OffenseInfo)
{
	for (int i = 0; i < AttackerInCharge->CharacterObj.Buff.Num(); i++)
	{
		if(AttackerInCharge->CharacterObj.Buff[i].Model.Id.IsEmpty() == false)
		{
			for (const FJsonFuncData OnOffense : AttackerInCharge->CharacterObj.Buff[i].Model.OnOffense)
			{
				UFunction* Func = UCallFuncLib::JsonFuncToUFunc(OnOffense);
				if (Func)
				{
					struct
					{
						FBuffObj Buff;		//运行的buff
						AActor* Target;	//打中了什么，不一定是个角色的
						FOffenseInfo OInfo;	//用来打中的信息
						TArray<FString> Param;	//函数的参数
						FBuffOffenseResult Result;	//返回
					} FuncParam;
					FuncParam.Buff = AttackerInCharge->CharacterObj.Buff[i];
					FuncParam.Target = nullptr;
					FuncParam.OInfo = OffenseInfo;
					FuncParam.Param = OnOffense.Params;
					AttackerInCharge->ProcessEvent(Func, & FuncParam);
					AttackerInCharge->CharacterObj.Buff[i] = FuncParam.Result.BuffObj;
					OffenseInfo = FuncParam.Result.OffenseInfo;
				}
			} 
		}
				
	}
}