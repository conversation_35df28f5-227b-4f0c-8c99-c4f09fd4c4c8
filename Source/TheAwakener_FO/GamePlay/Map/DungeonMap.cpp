// Fill out your copyright notice in the Description page of Project Settings.


#include "DungeonMap.h"

#include "Kismet/KismetMathLibrary.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"

bool UDungeonMap::GenerateDungeonMap(FString DungeonId, TArray<FString> TriggeEvents, TArray<FDungeonTile>& RoomList, TArray<FDungeonRoadInfo>& RoadList, bool UseDefaultStepLevel)
{
	const FAwDungeonModel DungeonModel = UGameplayFuncLib::GetAwDataManager()->GetDungeonModelById(DungeonId);
	TArray<FString> RoomTag;
	RoomTag.Add(DungeonId);
	TArray<FDungeonRoadModel> RoadMeshInfo = UGameplayFuncLib::GetAwDataManager()->GetDungeonRoadModelByDungeonTags(RoomTag);
	if(DungeonModel.TileMapList.Num() && RoadMeshInfo.Num())
	{
		//随机出用哪个TileMap
		int RandTileMapIndex = FMath::RandRange(0,DungeonModel.TileMapList.Num() - 1);
		FAwDungeonPresetTileMap UsingTileMap = DungeonModel.TileMapList[RandTileMapIndex];
		//获得每个Step的可用房间
		const TArray<FDungeonCanUseStep> AllStepCanUseRooms = GetCanUseRooms(DungeonModel,TriggeEvents, RandTileMapIndex);
		if(AllStepCanUseRooms.Num() > 0)
		{
			TArray<FDungeonTile> TileList;
			TArray<FDungeonRoadInfo> AllRoadList;
			for(FDungeonCanUseStep CurStep : AllStepCanUseRooms)
			{
				if(CurStep.CanUseRooms.Num() <= 0)
					return false;
				if(!GenerateDungeonRoom(CurStep, RoadMeshInfo, TileList,AllRoadList, UseDefaultStepLevel))
					return false;
			}
			RoomList = TileList;
			RoadList = AllRoadList;
			return true;
		}
	}
	return false;
}

TArray<FDungeonCanUseStep> UDungeonMap::GetCanUseRooms(FAwDungeonModel DungeonModel, TArray<FString> TriggeEvents, int UsingTileMapIndex)
{
	TArray<FDungeonCanUseStep> AllStepCanUseRooms;
	for(FAwDungeonStepInfo CurStep : DungeonModel.StepList)
	{
		if(CurStep.Step != -1)
		{
			//获取预设的TileMap里该Step的TileInfo
			FAwDungeonPresetTile CurTileInfo = FAwDungeonPresetTile();
			for(FAwDungeonPresetTile TileInfo :	DungeonModel.TileMapList[UsingTileMapIndex].TileList)
			{
				if(TileInfo.Step == CurStep.Step)
				{
					CurTileInfo = TileInfo;
					break;
				}
			}
			if(CurTileInfo.Step == -1)
				continue;
			//根据Condition得到当前Step所有可用房间和几率
			TMap<FString, int> CanUseRooms;
			int TotalValue = 0;
			for(FAwDungeonRoomCondition CurRoom : CurStep.RoomList)
			{
				UFunction* Func;
				Func = UCallFuncLib::GetUFunction(CurRoom.Condition.ClassPath, CurRoom.Condition.FunctionName);
				if (Func)
				{
					struct
					{
						FAwDungeonPresetTile TileInfo;
						TArray<FString> TriggeEvents;
						TArray<FString> Params;
						int Result;
					}FuncParam;
					FuncParam.TileInfo = CurTileInfo;
					FuncParam.TriggeEvents = TriggeEvents;
					FuncParam.Params = CurRoom.Condition.Params;
					if (IsValid(GWorld))
					{
						GWorld->ProcessEvent(Func, &FuncParam);
					}
					if(FuncParam.Result > 0)
					{
						CanUseRooms.Add(CurRoom.LevelPath,FuncParam.Result);
						TotalValue += FuncParam.Result;
					}
				}
				else if(CurRoom.Condition.ClassPath == "" && CurRoom.Condition.FunctionName == "")
				{
					CanUseRooms.Add(CurRoom.LevelPath,10);
					TotalValue += 10;
				}
			}
			if(TotalValue > 0)
			{
				FDungeonCanUseStep CurStepRooms = FDungeonCanUseStep();
				CurStepRooms.Step = CurStep.Step;
				CurStepRooms.TotalValue = TotalValue;
				CurStepRooms.TileInfo = CurTileInfo;
				CurStepRooms.CanUseRooms = CanUseRooms;
				AllStepCanUseRooms.Add(CurStepRooms);
			}
		}
	}
	return AllStepCanUseRooms;
}

FDungeonRoomLevelInfo UDungeonMap::GetRoomLevelInfo(FString LevelName)
{
	FDungeonRoomLevelInfo LevelInfo;
	return LevelInfo;
}

bool UDungeonMap::GenerateDungeonRoom(FDungeonCanUseStep CurStepInfo, TArray<FDungeonRoadModel> RoadMeshList, TArray<FDungeonTile>& RoomList, TArray<FDungeonRoadInfo>& RoadList, bool UseDefaultStepLevel)
{
	FDungeonTile NewRoomTile = FDungeonTile();
	if(CurStepInfo.TileInfo.RoomSeat.Num() == 0)
		return false;
	NewRoomTile.RoomSeat = CurStepInfo.TileInfo.RoomSeat;
	if(CurStepInfo.Step == -1)
		return false;
	NewRoomTile.StepValue = CurStepInfo.Step;
	//随机出用那个DungeonLevel
	for(int i = 0; i < 50; i++)
	{
		if(!UseDefaultStepLevel)
		{
			const int RandValue = FMath::RandRange(1,CurStepInfo.TotalValue);
			int CurValue = 0;
			TArray<FString> RandedLevel;
			for(auto RoomPercent : CurStepInfo.CanUseRooms)
			{
				if(RandValue > CurValue && RandValue <= CurValue + RoomPercent.Value)
				{
					NewRoomTile.LevelPath = RoomPercent.Key;
					break;
				}
				else
					CurValue += RoomPercent.Value;
			}
		}
		else
		{
			NewRoomTile.LevelPath = CurStepInfo.CanUseRooms.begin().Key();
		}
		if(NewRoomTile.LevelPath != "" && CheckLevelNotUsed(NewRoomTile.LevelPath, RoomList))
		{
			const FDungeonRoomLevelInfo CurRoomInfo = UGameplayFuncLib::GetAwDataManager()->GetDungeonLevelInfoByPath(NewRoomTile.LevelPath);
			//随机该房间的方向
			TArray<int> CanUseYawList = CheckCanUseRoomYaw(CurStepInfo.TileInfo, CurRoomInfo);
			if(CanUseYawList.Num())
			{
				const int RandYaw = CanUseYawList[FMath::RandRange(0,CanUseYawList.Num() - 1)];
				NewRoomTile.LevelRotYaw = RandYaw * 90;
				
				//获得哪些门开着
				TArray<FDungeonDoorInfo> OpenedDoorGroups = GetOpenedDoorGroups(CurStepInfo.TileInfo, CurRoomInfo.DoorGroups, RandYaw);
				
				NewRoomTile.OpenedDoors = RandDoorInDoorGroup(CurRoomInfo,OpenedDoorGroups,RandYaw);
				NewRoomTile.ClosedDoors = GetClosedDoorList(CurRoomInfo, NewRoomTile.OpenedDoors,RandYaw);
				
				//计算房间中心Seat点
				FVector2D TotalRoomSeat = FVector2D::ZeroVector;
				for(FVector2D CurSeat : NewRoomTile.RoomSeat)
				{
					TotalRoomSeat += CurSeat;
				}
				const FVector2D RoomCenterSeat = TotalRoomSeat / NewRoomTile.RoomSeat.Num();
				
				//计算房间的世界坐标
				NewRoomTile.RoomLocation = FVector(RoomCenterSeat.X * 15000, RoomCenterSeat.Y * 15000, 1500);
				
				if(RoomList.Num())
				{
					//获得LastStep
					int LastStep = -1;
					FVector LinkDoorLoc = FVector::ZeroVector;
					for(auto LinkDoor : NewRoomTile.OpenedDoors)
					{
						if(LinkDoor.LinkedStep < CurStepInfo.TileInfo.Step)
						{
							LastStep = LinkDoor.LinkedStep;
							LinkDoorLoc = LinkDoor.Location;
							break;
						}
					}
					
					if(LastStep != -1)
					{
						//获得与这个房间相连的上个房间的Door坐标和方向
						FVector LastDoorLoc = FVector::ZeroVector;
						FVector2D LastDoorDir = FVector2D::ZeroVector;
						for(auto LastRoom : RoomList)
						{
							if(LastRoom.StepValue == LastStep)
							{
								for(auto LastRoomDoor : LastRoom.OpenedDoors)
								{
									if(LastRoomDoor.LinkedStep == CurStepInfo.Step)
									{
										LastDoorLoc = LastRoomDoor.Location;
										LastDoorDir = LastRoomDoor.Direction;
										break;
									}
								}
								break;
							}
						}
						if(LastDoorLoc != FVector::ZeroVector && LastDoorDir != FVector2D::ZeroVector)
						{
							//随机通道
							FDungeonRoadModel UsingRoad = RoadMeshList[FMath::RandRange(0,RoadMeshList.Num() - 1)];
							FDungeonRoadInfo NewRoad = FDungeonRoadInfo();
							NewRoad.MeshPath = UsingRoad.MeshPath;
							NewRoad.Length = UsingRoad.Length;
							
							//计算相连的Door的位置
							FVector NewDoorLoc = LastDoorLoc + FVector(LastDoorDir.X, LastDoorDir.Y,0) * NewRoad.Length;
							FVector LocOffset = NewDoorLoc - LinkDoorLoc;
							NewRoomTile.RoomLocation = LocOffset;
							//检测房间之间有没有碰撞
							if(!CheckRoomLocCanUse(NewRoomTile.RoomLocation, CurRoomInfo.Length, CurRoomInfo.Width , NewRoomTile.LevelRotYaw, RoomList))
								continue;

							NewRoad.Rotation = UKismetMathLibrary::MakeRotFromX(FVector(LastDoorDir.X, LastDoorDir.Y,0));
							NewRoad.Location = LastDoorLoc + FVector(LastDoorDir.X, LastDoorDir.Y,0) * NewRoad.Length / 2;
							RoadList.Add(NewRoad);
						}
					}
				}
				for(FDungeonDoorInfo& Door : NewRoomTile.OpenedDoors)
				{
					Door.Location += NewRoomTile.RoomLocation;
				}

				for(FDungeonDoorInfo& Door : NewRoomTile.ClosedDoors)
				{
					Door.Location += NewRoomTile.RoomLocation;
				}
				
				RoomList.Add(NewRoomTile);
				return true;
			}
		}
	}
	
	return false;
}

TArray<int> UDungeonMap::CheckCanUseRoomYaw(FAwDungeonPresetTile TileInfo, FDungeonRoomLevelInfo RoomLevelInfo)
{
	TArray<int> CanUseYawList;
	for(int i = 0; i < 4; i++)
	{
		TArray<FDungeonDoorInfo> CheckedLevelDoorIndex = GetOpenedDoorGroups(TileInfo, RoomLevelInfo.DoorGroups, i);
		if(CheckedLevelDoorIndex.Num() >= TileInfo.DoorList.Num())
			CanUseYawList.Add(i);
	}
	return CanUseYawList;
}

TArray<FDungeonDoorInfo> UDungeonMap::GetOpenedDoorGroups(FAwDungeonPresetTile TileInfo, TArray<FDungeonRoomDoorGroup> DoorGroupList, int YawRotIndex)
{
	//旋转DoorGroup的RoomSeat和DoorDir
	TArray<FDungeonRoomDoorGroup> RotDoorGroup = DoorGroupList;
	int MinX = 100;
	int MinY = 100;
	for(int i = 0; i < RotDoorGroup.Num(); i++)
	{
		RotDoorGroup[i].Direction = RotDir(RotDoorGroup[i].Direction, YawRotIndex);
		RotDoorGroup[i].Seat = RotDir(RotDoorGroup[i].Seat, YawRotIndex);
		if(RotDoorGroup[i].Seat.X < MinX)
			MinX = RotDoorGroup[i].Seat.X;
		if(RotDoorGroup[i].Seat.Y < MinY)
			MinY = RotDoorGroup[i].Seat.Y;
	}
	
	//把RoomSeat的值都标准化（如果有值小于0，平移所有Seat，使所有值大于等于0）
	for(int i = 0; i < RotDoorGroup.Num(); i++)
	{
		RotDoorGroup[i].Seat.X += MinX * -1;
		RotDoorGroup[i].Seat.Y += MinY * -1;
	}
	
	//把RoomSeat的值都标准化（如果有值小于0，平移所有Seat，使所有值大于等于0）
	TArray<FAwDungeonPresetDoor> RotDoorList = TileInfo.DoorList;
	MinX = 100;
	MinY = 100;
	for(int i = 0; i < RotDoorList.Num(); i++)
	{
		if(RotDoorList[i].DoorSeat.X < MinX)
			MinX = RotDoorList[i].DoorSeat.X;
		if(RotDoorList[i].DoorSeat.Y < MinY)
			MinY = RotDoorList[i].DoorSeat.Y;
	}
	for(int i = 0; i < RotDoorList.Num(); i++)
	{
		RotDoorList[i].DoorSeat.X += MinX * -1;
		RotDoorList[i].DoorSeat.Y += MinY * -1;
	}

	//检查RoomSeat和DoorDir是否能一一对应
	TArray<FDungeonDoorInfo> CheckedLevelDoorIndex;
	for(auto CurTileDoor : RotDoorList)
	{
		for(int i = 0;i < RotDoorGroup.Num(); i++)
		{
			if(CurTileDoor.DoorDir.Equals(FVector2D(RotDoorGroup[i].Direction)) && CurTileDoor.DoorSeat.Equals(RotDoorGroup[i].Seat))
			{
				FDungeonDoorInfo NewDoorInfo = FDungeonDoorInfo();
				NewDoorInfo.DoorId = RotDoorGroup[i].GroupId;
				NewDoorInfo.LinkedStep = CurTileDoor.LinkedStep;
				CheckedLevelDoorIndex.Add(NewDoorInfo);
				break;
			}
		}
	}
	return CheckedLevelDoorIndex;
}

bool UDungeonMap::CheckLevelNotUsed(FString LevelPath, TArray<FDungeonTile> RoomList)
{
	for(FDungeonTile Tile : RoomList)
	{
		if(Tile.LevelPath == LevelPath)
			return false;
	}
	return true;
}

TArray<FDungeonDoorInfo> UDungeonMap::RandDoorInDoorGroup(FDungeonRoomLevelInfo RoomLevelInfo, TArray<FDungeonDoorInfo> DoorGroups, int YawRotIndex)
{
	TArray<FDungeonDoorInfo> DoorList;
	for(FDungeonDoorInfo DoorInfo : DoorGroups)
	{
		for(auto DoorGroup : RoomLevelInfo.DoorGroups)
		{
			if(DoorInfo.DoorId == DoorGroup.GroupId)
			{
				int RandIndex = FMath::RandRange(0,DoorGroup.Doors.Num() - 1);
				TArray<FString> DoorIds;
				DoorGroup.Doors.GetKeys(DoorIds);
				FDungeonDoorInfo NewDoorInfo = FDungeonDoorInfo();
				NewDoorInfo.DoorId = DoorIds[RandIndex];
				FVector DoorLoc =  DoorGroup.Doors[NewDoorInfo.DoorId];
				NewDoorInfo.Location = RotDoorLocation(DoorLoc,YawRotIndex * 90);
				NewDoorInfo.Direction = RotDir(FVector2D(DoorGroup.Direction),YawRotIndex);
				NewDoorInfo.LinkedStep = DoorInfo.LinkedStep;
				DoorList.Add(NewDoorInfo);
				break;
			}
		}
	}
	
	
	return DoorList;
}

TArray<FDungeonDoorInfo> UDungeonMap::GetClosedDoorList(FDungeonRoomLevelInfo RoomLevelInfo, TArray<FDungeonDoorInfo> OpenedDoors, int YawRotIndex)
{
	TArray<FDungeonDoorInfo> ClosedDoorList;
	for (FDungeonRoomDoorGroup DoorGroup : RoomLevelInfo.DoorGroups)
	{
		for (TTuple<FString, FVector> Door : DoorGroup.Doors)
		{
			bool IsClosedDoor = true;
			FVector RotatedLoc = RotDoorLocation(Door.Value,YawRotIndex * 90);
			for (FDungeonDoorInfo OpenedDoor : OpenedDoors)
			{
				if(RotatedLoc.Equals(OpenedDoor.Location, 1))
				{
					IsClosedDoor = false;
					break;
				}
			}
			if(IsClosedDoor)
			{
				FDungeonDoorInfo ClosedDoor = FDungeonDoorInfo();
				ClosedDoor.DoorId = Door.Key;
				ClosedDoor.Location = RotatedLoc;
				ClosedDoor.Direction = RotDir(FVector2D(DoorGroup.Direction),YawRotIndex);
				ClosedDoor.LinkedStep = -1;
				ClosedDoorList.Add(ClosedDoor);
			}
		}
	}
	return ClosedDoorList;
}

FVector UDungeonMap::RotDoorLocation(FVector OriginalLoc, int YawRot)
{
	FTransform NewTrans = FTransform();
	NewTrans.SetRotation(FRotator(0, YawRot, 0).Quaternion());
	return NewTrans.TransformPosition(OriginalLoc);
}

FVector UDungeonMap::RotDir(FVector CurDir, int YawRotIndex)
{
	FVector NewDir = CurDir;
	for (int i = 0; i < YawRotIndex; i++)
	{
		FVector Temp = NewDir;
		if (Temp.Y == 0)
			NewDir.X = 0;
		else
			NewDir.X = Temp.Y * -1;
		NewDir.Y = Temp.X;
	}
	return NewDir;
}

FVector2D UDungeonMap::RotDir(FVector2D CurDir, int YawRotIndex)
{
	FVector2D NewDir = CurDir;
	for (int i = 0; i < YawRotIndex; i++)
	{
		FVector2D Temp = NewDir;
		if (Temp.Y == 0)
			NewDir.X = 0;
		else
			NewDir.X = Temp.Y * -1;
		NewDir.Y = Temp.X;
	}
	return NewDir;
}

FVector2D UDungeonMap::RotRoomSeat(FVector2D CurRoomSeat, int YawRotIndex)
{
	FVector2D NewSeat = CurRoomSeat;
	for (int i = 0; i < YawRotIndex; i++)
	{
		FVector2D Temp = NewSeat;
		if (Temp.Y == 0)
			NewSeat.X = 0;
		else
			NewSeat.X = Temp.Y * -1;
		NewSeat.Y = Temp.X;
	}
	return NewSeat;
}

bool UDungeonMap::CheckRoomLocCanUse(FVector RoomLoc, int Length, int Width, int YawRotIndex, TArray<FDungeonTile> RoomList)
{
	int ActualLength;
	int ActualWidth;
	if(YawRotIndex == 0 || YawRotIndex == 180)
	{
		ActualLength = Length;
		ActualWidth = Width;
	}
	else
	{
		ActualLength = Width;
		ActualWidth = Length;
	}
	FVector CurMin = RoomLoc - FVector(ActualLength / 2, ActualWidth / 2, 0);
	FVector CurMax = RoomLoc + FVector(ActualLength / 2, ActualWidth / 2, 0);
	for(auto OtherRoom : RoomList)
	{
		const FDungeonRoomLevelInfo RoomLevelInfo = UGameplayFuncLib::GetAwDataManager()->GetDungeonLevelInfoByPath(OtherRoom.LevelPath);
		int OtherRoomLength;
		int OtherRoomWidth;
		if(OtherRoom.LevelRotYaw == 0 || OtherRoom.LevelRotYaw == 180)
		{
			OtherRoomLength = RoomLevelInfo.Length;
			OtherRoomWidth = RoomLevelInfo.Width;
		}
		else
		{
			OtherRoomLength = RoomLevelInfo.Width;
			OtherRoomWidth = RoomLevelInfo.Length;
		}
		FVector OtherMin = OtherRoom.RoomLocation - FVector(OtherRoomLength / 2, OtherRoomWidth / 2, 0);
		FVector OtherMax = OtherRoom.RoomLocation + FVector(OtherRoomLength / 2, OtherRoomWidth / 2, 0);

		if(CurMin.X > OtherMax.X || OtherMin.X > CurMax.X || CurMin.Y > OtherMax.Y || OtherMin.Y > CurMax.Y)
			continue;
		else
			return false;
	}
	return true;
}

