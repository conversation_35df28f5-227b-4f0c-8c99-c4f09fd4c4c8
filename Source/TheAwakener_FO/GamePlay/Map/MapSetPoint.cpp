// Fill out your copyright notice in the Description page of Project Settings.


#include "MapSetPoint.h"

#include "Kismet/KismetMathLibrary.h"

FMapSetPoint::FMapSetPoint(FString Id, FString RoomLevelPath, FVector Offset, FString RoomId, FTransform RoomTrans)
{
	this->Id = Id;
	this->RoomLevelPath = RoomLevelPath;
	this->Offset = Offset;
	this->RoomId = RoomId;
	this->Location = UKismetMathLibrary::TransformLocation(RoomTrans, Offset);
}
