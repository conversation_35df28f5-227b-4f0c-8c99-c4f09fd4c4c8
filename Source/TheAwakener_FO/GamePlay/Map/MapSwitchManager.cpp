// Fill out your copyright notice in the Description page of Project Settings.


#include "MapSwitchManager.h"

#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"

// Sets default values
AMapSwitchManager::AMapSwitchManager()
{
 	// Set this actor to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryActorTick.bCanEverTick = true;

}

// Called when the game starts or when spawned
void AMapSwitchManager::BeginPlay()
{
	Super::BeginPlay();
	if(UGameplayFuncLib::GetAwGameState())
		UGameplayFuncLib::GetAwGameState()->ImportantActors.Add(this->GetName(), this);
}

// Called every frame
void AMapSwitchManager::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);

}

void AMapSwitchManager::StartSwitch()
{
	for (const FString SScript : StartScripts)
	{
		const FJsonFuncData JsonFuncData = UCallFuncLib::StringToJsonFuncData(SScript);
		UFunction* Func = UCallFuncLib::JsonFuncToUFunc(JsonFuncData);
		if (Func)
		{
			struct
			{
				TArray<FString> Params;
				UTimelineNode* Node;
			}FuncParam;
			FuncParam.Params = JsonFuncData.Params;
			this->ProcessEvent(Func, &FuncParam);
		}
	}

	//所有被隐藏掉的角色
	TArray<AActor*> HiddenActors;

	for (AActor* HideActor : HideInStartList)
	{
		if (!HideActor) continue;
		//进入HideInStartList的都是重要的玩意儿
		UGameplayFuncLib::GetAwGameState()->ImportantActors.Add(HideActor->GetName(), HideActor);
		
		//如果是商店就要用这个隐藏掉，不然就会所有商品都刷了
		AMerchantShop* Shop = Cast<AMerchantShop>(HideActor);
		if (Shop)
		{
			UKismetSystemLibrary::PrintString(this, FString("Merchant To Hide ").Append(Shop->GetName()),
				true,true,FLinearColor::Red, 40);
			Shop->HideMe();
		}else
		{
			HideActor->SetActorHiddenInGame(true);
			HideActor->SetActorEnableCollision(false);
		}
		
		HiddenActors.Add(HideActor);
	}

	for (AActor* ShowActor : ShowInStartList)
	{
		if (!ShowActor) continue;
		UGameplayFuncLib::GetAwGameState()->ImportantActors.Add(ShowActor->GetName(), ShowActor);
		
		AMerchantShop* Shop = Cast<AMerchantShop>(ShowActor);
		if (Shop)
		{
			UKismetSystemLibrary::PrintString(this, FString("Merchant To Show ").Append(Shop->GetName()),
				true,true,FLinearColor::Green, 40);
			Shop->ShowMe();
		}else
		{
			ShowActor->SetActorHiddenInGame(false);
			ShowActor->SetActorEnableCollision(true);
		}
		HiddenActors.Remove(ShowActor);
	}
	
	for (FMapSwitchGroup SwitchGroup : GroupList)
	{
		for (FSwitchGroup CampGroup : SwitchGroup.CampGroups)
		{
			bool CanSwitch = true;
			for (const FJsonFuncData SwitchFunc : CampGroup.SwitchFuncList)
			{
				UFunction* Func = UCallFuncLib::GetUFunction(SwitchFunc.ClassPath, SwitchFunc.FunctionName);
				if (Func)
				{
					struct
					{
						TArray<FString> Params;
						bool Result;
					}FuncParam;
					FuncParam.Params = SwitchFunc.Params;
					this->ProcessEvent(Func, &FuncParam);
					if (!FuncParam.Result)
					{
						CanSwitch = false;
						break;
					}
				}
			}
			for(const auto Actor : CampGroup.SwitchActors)
			{
				if (!Actor) continue;
				if(CampGroup.bHideType)
				{
					Actor->SetActorHiddenInGame(!CanSwitch);
					Actor->SetActorEnableCollision(CanSwitch);
					AMerchantShop* Shop = Cast<AMerchantShop>(Actor);
					if (Shop)
					{
						if (CanSwitch)
							Shop->ShowMe();
						else
							Shop->HideMe();
					}else
					{
						Actor->SetActorHiddenInGame(!CanSwitch);
						Actor->SetActorEnableCollision(CanSwitch);
					}
					HiddenActors.Remove(Actor);
				}
				else
				{
					if(CanSwitch)
					{
						AMerchantShop* Shop = Cast<AMerchantShop>(Actor);
						if (Shop)
						{
							UKismetSystemLibrary::PrintString(this, FString("Merchant To Show ").Append(Shop->GetName()),
								true,true,FLinearColor::Green, 40);
							Shop->ShowMe();
						}else
						{
							Actor->SetActorHiddenInGame(false);
							Actor->SetActorEnableCollision(true);
						}
						HiddenActors.Remove(Actor);
					}
				}
			}
		}
	}

	for (AActor* HActor : HiddenActors)
	{
		AAwSceneItem* SItem = Cast<AAwSceneItem>(HActor);
		if (SItem)
		{
			SItem->Destroy();
		}
	} 
}

