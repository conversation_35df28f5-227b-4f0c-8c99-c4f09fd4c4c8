// Fill out your copyright notice in the Description page of Project Settings.


#include "AwMapScenePosition.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"

FAwMapPathNode FAwMapPathNode::FromScenePosition(const AAwMapScenePosition* ScenePos)
{
	return FAwMapPathNode(ScenePos->NodeId, ScenePos->GetActorLocation());
}

AAwMapScenePosition::AAwMapScenePosition()
{
	PrimaryActorTick.bCanEverTick = true;

	SceneComponent = CreateDefaultSubobject<USceneComponent>(TEXT("SceneComponent"));
	SceneComponent->SetupAttachment(GetRootComponent());
}

void AAwMapScenePosition::BeginPlay()
{
	Super::BeginPlay();
	SetToGameData();
}

void AAwMapScenePosition::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);
}

void AAwMapScenePosition::SetToGameData()
{
	// UKismetSystemLibrary::PrintString(this,FString("MyPos:").Append(this->GetActorLocation().ToString())
	// 	, true, true,  FLinearColor::Green, 60);

	const FAwMapPathNode Node = FAwMapPathNode::FromScenePosition(this);
	//UGameplayFuncLib::GetAwGameState()->AwMapPathNodes.Add(Node.NodeId, Node);
	this->Destroy();
}
