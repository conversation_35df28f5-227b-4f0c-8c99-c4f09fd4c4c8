// Fill out your copyright notice in the Description page of Project Settings.


#include "GameDestSign.h"

#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"


AGameDestSign::AGameDestSign()
{
	PrimaryActorTick.bCanEverTick = true;

	Root = CreateDefaultSubobject<USceneComponent>(TEXT("Root"));
	Root->SetupAttachment(GetRootComponent());
	Root->SetRelativeTransform(FTransform::Identity);

	Catcher = CreateDefaultSubobject<UAttackHitManager>(TEXT("Catcher"));
}

void AGameDestSign::StartFunction()
{
	bStartFunction = true;
	if (Catcher) Catcher->ActiveAllAttackHitBox();

	if (HideSignShownOnDestroyed == true)
	{
		for (AGameDestSign* Sign : this->ShowOtherSignOnDestroyed)
		{
			Sign->Hide();
		} 
	}

	if (LocationPicker.IsEmpty() == false)
	{
		const FJsonFuncData FuncData = UCallFuncLib::StringToJsonFuncData(LocationPicker);
		UFunction* Func = UCallFuncLib::JsonFuncToUFunc(FuncData);
		if (Func)
		{
			struct 
			{
				TArray<FString> Params;
				FTransform Result;
			}FuncParam;
			this->ProcessEvent(Func, &FuncParam);
			this->SetActorTransform(FuncParam.Result);
		}
	}
}

void AGameDestSign::Hide_Implementation()
{
	BigMapPosHide();
	this->SetActorHiddenInGame(true);
	this->SetActorEnableCollision(false);
}

void AGameDestSign::Show_Implementation(bool RemoveShowConditions)
{
	BigMapPosShow();
	this->SetActorHiddenInGame(false);
	this->SetActorEnableCollision(true);
	if (RemoveShowConditions)
	{
		this->ShowCondition.Empty();
	}
}



void AGameDestSign::BeginPlay()
{
	Super::BeginPlay();
	PlayerCha = UGameplayFuncLib::GetAwGameState() ? UGameplayFuncLib::GetAwGameState()->GetMyCharacter() : nullptr;
	bStartFunction = false;
	this->Hide();
}

void AGameDestSign::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);
	if(bStartFunction == false) return;
	if (PlayerCha )
	{
		bool PlayerIsEnter = false;
		for (const FBeCaughtActorInfo VTarget : Catcher->ThisTickValidTarget(false, true, true))
		{
			const AAwCharacter* CaughtGuy = Cast<AAwCharacter>(VTarget.BeCaughtActor);
			if (CaughtGuy && CaughtGuy == PlayerCha)
			{
				PlayerIsEnter = true;
				break;
			}
		}
		if (PlayerIsEnter == true)
		{
			bool FinalDestroyMe = false;
			for (const FString Enter : OnEnter) 
			{
				const FJsonFuncData FuncData = UCallFuncLib::StringToJsonFuncData(Enter);
				UFunction* Func = UCallFuncLib::JsonFuncToUFunc(FuncData);
				if (Func)
				{
					struct
					{
						AGameDestSign* Sign;
						AAwCharacter* EnterCha;
						TArray<FString> Params;
						bool Result;
					}FuncParam;
					FuncParam.Sign = this;
					FuncParam.EnterCha = this->PlayerCha;
					FuncParam.Params = FuncData.Params;
					this->ProcessEvent(Func, &FuncParam);
					if (FuncParam.Result == true) FinalDestroyMe = true;
				}
			}
			if (FinalDestroyMe)
			{
				this->Destroy();
				for (AGameDestSign* Sign : this->ShowOtherSignOnDestroyed)
				{
					Sign->Show();
				} 
			}

			for (AGameDestSign* Sign : this->CloseOtherSignOnEnter)
			{
				if (Sign) Sign->Destroy();
			} 
		}
		
	}else
	{
		PlayerCha = UGameplayFuncLib::GetAwGameState() ? UGameplayFuncLib::GetAwGameState()->GetMyCharacter() : nullptr;
	}

	if (this->ShowCondition.Num() > 0)
	{
		bool FinalShowMe = true;
		for (const FString SCondition : ShowCondition) 
		{
			const FJsonFuncData FuncData = UCallFuncLib::StringToJsonFuncData(SCondition);
			UFunction* Func = UCallFuncLib::JsonFuncToUFunc(FuncData);
			if (Func)
			{
				struct
				{
					TArray<FString> Params;
					bool Result;
				}FuncParam;
				FuncParam.Params = FuncData.Params;
				this->ProcessEvent(Func, &FuncParam);
				if (FuncParam.Result == false)
				{
					FinalShowMe = false;
					break;
				}
			}
		}
		if (FinalShowMe)
		{
			this->Show(bRemoveShowConditionOnce);
		}
		else
		{
			this->Hide();
		}
	}
	
	
}

