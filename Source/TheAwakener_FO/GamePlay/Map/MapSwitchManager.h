// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "TheAwakener_FO/FunctionLibrary/DataFuncLib.h"
#include "MapSwitchManager.generated.h"

USTRUCT(BlueprintType)
struct FSwitchGroup
{
	GENERATED_BODY()
public:
	//true的话，不符合条件的会Hide
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool bHideType = true;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FJsonFuncData> SwitchFuncList;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<AActor*> SwitchActors;
	
};

USTRUCT(BlueprintType)
struct FMapSwitchGroup
{
	GENERATED_BODY()
public:
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString GroupId = "";
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FSwitchGroup> CampGroups;
};

UCLASS(hideCategories=(Rendering, Physics, LOD, Activation, Input, Actor, Cooking, Collision))
class THEAWAKENER_FO_API AMapSwitchManager : public AActor
{
	GENERATED_BODY()
public:	
	// Sets default values for this actor's properties
	AMapSwitchManager();

protected:
	// Called when the game starts or when spawned
	virtual void BeginPlay() override;

public:	
	// Called every frame
	virtual void Tick(float DeltaTime) override;

	UFUNCTION(BlueprintCallable)
	void StartSwitch();

public:
	//关于房屋创建的东西
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FMapSwitchGroup> GroupList;

	//开始时候必定运行的脚本((TArray<FString> Params)=>UTimelineNode*，但是这个TimelineNode是不会执行的)
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FString> StartScripts;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<AActor*> HideInStartList;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<AActor*> ShowInStartList;
};
