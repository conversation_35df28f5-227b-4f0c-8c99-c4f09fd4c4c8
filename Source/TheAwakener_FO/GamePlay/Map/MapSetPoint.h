// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/Object.h"
#include "TheAwakener_FO/FunctionLibrary/DataFuncLib.h"
#include "MapSetPoint.generated.h"

/**
 * 地图上预设的点
 */
USTRUCT(BlueprintType)
struct FMapSetPoint
{
	GENERATED_BODY()
public:
	//【填写】坐标点得设置一个Id被索引
	UPROPERTY(BlueprintReadOnly)
	FString Id = "";

	//【填写】坐标点所在的地图的资源地址
	UPROPERTY(BlueprintReadOnly)
	FString RoomLevelPath = "";

	//【填写】放在房间内的坐标
	UPROPERTY(BlueprintReadOnly)
	FVector Offset = FVector(0, 0, 0);

	//【运行时】在世界的坐标（比如用来让AI走过去）
	UPROPERTY(BlueprintReadOnly)
	FVector Location = FVector(0, 0, 0);

	//【运行时】之前我记得说要有一个房间这一层的概念，那么就把房间Id放在数据里可以拿到
	UPROPERTY(BlueprintReadOnly)
	FString RoomId = "";

	FMapSetPoint():Id(""), RoomLevelPath(""), Offset(FVector(0, 0, 0)), Location(FVector(0, 0, 0)), RoomId("")
	{}

	FMapSetPoint(FString Id, FString RoomLevelPath, FVector Offset, FString RoomId, FTransform RoomTrans);
};

USTRUCT(BlueprintType)
struct FPresetMapPoint
{
	GENERATED_BODY()
public:
	//【填写】坐标点得设置一个Id被索引
	UPROPERTY(BlueprintReadOnly)
		FString Id = "";

	//【填写】放在房间内的坐标
	UPROPERTY(BlueprintReadOnly)
		FVector Offset = FVector(0, 0, 0);

	//【填写】这个点是在哪个Level上的
	UPROPERTY(BlueprintReadOnly)
		FString LevelPath = "";

	static FPresetMapPoint FromJson(TSharedPtr<FJsonObject> JsonObj)
	{
		FPresetMapPoint NewPoint = FPresetMapPoint();

		FString OffsetString = UDataFuncLib::AwGetStringField(JsonObj, "Offset");
		NewPoint.Offset.InitFromString(OffsetString);

		NewPoint.Id = UDataFuncLib::AwGetStringField(JsonObj, "Id");

		//NewPoint.LevelPath = UDataFuncLib::AwGetStringField(JsonObj, "LevelPath");

		return NewPoint;
	};
};