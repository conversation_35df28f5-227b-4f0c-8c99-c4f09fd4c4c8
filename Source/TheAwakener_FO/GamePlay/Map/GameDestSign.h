// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "TheAwakener_FO/GamePlay/Characters/HitBox/ActorCatcher.h"
#include "GameDestSign.generated.h"
UENUM(BlueprintType)
enum class ESignType : uint8
{
	MainTask,
	BranchTask,
	Newbie
};

UCLASS()
class THEAWAKENER_FO_API AGameDestSign : public AActor
{
	GENERATED_BODY()

protected:
	virtual void BeginPlay() override;

private:
	UPROPERTY()
	UAttackHitManager* Catcher;
	UPROPERTY()
	USceneComponent* Root ;
	UPROPERTY()
	AAwCharacter* PlayerCha = nullptr;
public:
	AGameDestSign();
	virtual void Tick(float DeltaTime) override;

	/**
	 *当进入到捕捉范围的时候，执行的回调(APlayerTargetSign* this, AAwCharacter* EnterGuy, TArray<FString> Params)=>bool
	 *返回是否要删除这个，如果有一个返回true，就会被删除（在所有脚本运行完毕之后）
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FString> OnEnter;

	/**
	 * 当进入捕获范围的时候关闭的其他的提示
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<AGameDestSign*> CloseOtherSignOnEnter;

	/**
	 * 当自己被销毁的时候显示的其他标志
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<AGameDestSign*> ShowOtherSignOnDestroyed;

	/**
	 * 自己销毁时显示出来的标志，是否应该在自己开始运行时隐藏掉
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	bool HideSignShownOnDestroyed = true;

	//为了防止在开始黑屏时会一瞬间显示，用该bool做为总控制
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	bool bStartFunction = false;

	/**
	 * Begin Play时候会根据这个来选择一个位置
	 * (TArray<FString> Param)=>FTransform
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString LocationPicker;
	
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	bool bRemoveShowConditionOnce = false;

	/**
	 * 显示这个标的条件，一旦显示过了，这些将被清空
	 * (TArray<FString> Params)=>bool，必须每个都返回true才能显示
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FString> ShowCondition;


	//标记类型
	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	ESignType  SignType;

	UFUNCTION(BlueprintCallable)
	void StartFunction();
	
	UFUNCTION(BlueprintCallable,BlueprintNativeEvent)
	void Hide();
	UFUNCTION(BlueprintCallable,BlueprintNativeEvent)
	void Show(bool RemoveShowConditions = true);
	

	//地图UI上标点隐藏和显示（实现在TargetSign蓝图里）
	UFUNCTION(BlueprintCallable,BlueprintImplementableEvent)
	void BigMapPosShow();

	UFUNCTION(BlueprintCallable,BlueprintImplementableEvent)
	void BigMapPosHide();
	//地图UI上标点隐藏和显示（实现在TargetSign蓝图里）

	
};
