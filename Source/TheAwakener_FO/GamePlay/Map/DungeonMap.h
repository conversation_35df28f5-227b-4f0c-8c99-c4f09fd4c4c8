// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "AwDungeonInfo.h"
#include "AwDungeonRoomInfo.h"
#include "DungeonMap.generated.h"

/**
 * 
 */

USTRUCT(BlueprintType)
struct FDungeonRoadInfo
{
	GENERATED_BODY()
public:
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FVector Location = FVector::ZeroVector;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FRotator Rotation = FRotator::ZeroRotator;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int Length = 0;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString MeshPath = "";
};

USTRUCT(BlueprintType)
struct FDungeonDoorInfo
{
	GENERATED_BODY()
public:
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString DoorId = "";
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FVector Location = FVector::ZeroVector;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FVector2D Direction = FVector2D::ZeroVector;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int LinkedStep = 0;
};

USTRUCT(BlueprintType)
struct FDungeonTile
{
	GENERATED_BODY()
public:
	//因为有可能一个房间占多个格子，所以用数组储存位置
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FVector2D> RoomSeat;
	//在实际随机完房间的旋转和开关的门后，计算出房间的世界坐标
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FVector RoomLocation = FVector::ZeroVector;
	//该房间到起点房间的Step值
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int StepValue = 0;
	//该房间所使用的地图
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString LevelPath = "";
	//该房间的实际旋转值
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int LevelRotYaw = 0;
	//该房间实际打开的DoorGroup
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FDungeonDoorInfo> OpenedDoors;

	//该房间实际要关闭的Door
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FDungeonDoorInfo> ClosedDoors;
};
USTRUCT()
struct FDungeonCanUseStep
{
	GENERATED_BODY()
public:
	int Step = -1;
	int TotalValue = 0;
	FAwDungeonPresetTile TileInfo;
	TMap<FString, int> CanUseRooms;
};

UCLASS()
class THEAWAKENER_FO_API UDungeonMap : public UObject
{
	GENERATED_BODY()
public:
	
	UFUNCTION(BlueprintCallable)
	static bool GenerateDungeonMap(FString DungeonId, TArray<FString> TriggeEvents, TArray<FDungeonTile>& RoomList, TArray<FDungeonRoadInfo>& RoadList, bool UseDefaultStepLevel = false);

private:
	//在DungeonModel中获得每个Step的所有可用房间列表及几率
	static TArray<FDungeonCanUseStep> GetCanUseRooms(FAwDungeonModel DungeonModel, TArray<FString> TriggeEvents, int UsingTileMapIndex);

	static FDungeonRoomLevelInfo GetRoomLevelInfo(FString LevelName);

	static bool GenerateDungeonRoom(FDungeonCanUseStep CurStepInfo, TArray<FDungeonRoadModel> RoadMeshList, TArray<FDungeonTile>& RoomList, TArray<FDungeonRoadInfo>& RoadList, bool UseDefaultStepLevel = false);

	static TArray<int> CheckCanUseRoomYaw(FAwDungeonPresetTile TileInfo, FDungeonRoomLevelInfo RoomLevelInfo);

	static TArray<FDungeonDoorInfo> GetOpenedDoorGroups(FAwDungeonPresetTile TileInfo, TArray<FDungeonRoomDoorGroup> DoorGroupList, int YawRotIndex);

	static bool CheckLevelNotUsed(FString LevelPath, TArray<FDungeonTile> RoomList);

	static TArray<FDungeonDoorInfo> RandDoorInDoorGroup(FDungeonRoomLevelInfo RoomLevelInfo, TArray<FDungeonDoorInfo> DoorGroups, int YawRotIndex);

	static TArray<FDungeonDoorInfo> GetClosedDoorList(FDungeonRoomLevelInfo RoomLevelInfo, TArray<FDungeonDoorInfo> OpenedDoors, int YawRotIndex);

	static FVector RotDoorLocation(FVector OriginalLoc, int YawRot);

	static FVector RotDir(FVector CurDir, int YawRotIndex);

	static FVector2D RotDir(FVector2D CurDir, int YawRotIndex);

	static FVector2D RotRoomSeat(FVector2D CurRoomSeat, int YawRotIndex);

	static bool CheckRoomLocCanUse(FVector RoomLoc, int Length, int Width, int YawRotIndex, TArray<FDungeonTile> RoomList);
};


