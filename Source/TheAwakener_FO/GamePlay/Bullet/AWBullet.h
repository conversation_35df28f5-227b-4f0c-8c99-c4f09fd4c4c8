// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/FunctionLibrary/DataFuncLib.h"
#include "TheAwakener_FO/GamePlay/Characters/ChaProp/ChaProp.h"
#include "TheAwakener_FO/GamePlay/Characters/HitBox/ActorCatcher.h"
#include "AwBullet.generated.h"

UENUM(BlueprintType)
enum class EBulletType : uint8
{
	Bullet,//子弹
	Magic,//魔法
	Item//道具
};

UENUM(BlueprintType)
enum class EBulletRemoveType : uint8
{
	HitTerrain,//撞到场景
	TimeOut,//自然销毁
};

USTRUCT(BlueprintType)
struct FBulletModel
{
	GENERATED_BODY()
public:
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
		FString Id = FString();
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
		FString BpPath = "";
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
		TArray<FString> Tags;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
		FString SightEffect;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
		bool CanHitFoe = true;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
		bool CanHitAlly = false;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
		int HitSameTarget = 1;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
		float HitDelay = 0.1;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
		int Life = 1;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
		float LifeSpan = 0;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
		float Size = 100;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
		EBulletType Type = EBulletType::Bullet;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
		TArray<FJsonFuncData> OnCreate;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
		TArray<FJsonFuncData> OnHit;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
		TArray<FJsonFuncData> OnRemoved;

	static FBulletModel FromJson(TSharedPtr<FJsonObject> JsonObj)
	{
		FBulletModel Model = FBulletModel();
		Model.Id = UDataFuncLib::AwGetStringField(JsonObj, "Id");

		Model.BpPath = UDataFuncLib::AwGetStringField(JsonObj, "BpPath");

		for (const TSharedPtr<FJsonValue> Value : UDataFuncLib::AwGetArrayField(JsonObj, "Tag"))
			Model.Tags.Add(Value->AsString());

		Model.SightEffect = UDataFuncLib::AwGetStringField(JsonObj, "SightEffect");
		
		Model.CanHitFoe = UDataFuncLib::AwGetBoolField(JsonObj, "CanHitFoe", true);

		Model.CanHitAlly = UDataFuncLib::AwGetBoolField(JsonObj, "CanHitAlly", false);

		Model.HitSameTarget = UDataFuncLib::AwGetNumberField(JsonObj, "HitSameTarget", 1);

		Model.HitDelay = UDataFuncLib::AwGetNumberField(JsonObj, "HitDelay", 0.1f);

		Model.Life = UDataFuncLib::AwGetNumberField(JsonObj, "Life", 1);

		Model.LifeSpan = UDataFuncLib::AwGetNumberField(JsonObj, "LifeSpan", 0.0f);

		Model.Size = UDataFuncLib::AwGetNumberField(JsonObj, "Size", 100);
		
		Model.Type = UDataFuncLib::AwGetEnumField<EBulletType>(JsonObj, "Type", EBulletType::Bullet);

		for (const TSharedPtr<FJsonValue> Value : UDataFuncLib::AwGetArrayField(JsonObj, "OnCreate"))
			Model.OnCreate.Add(UDataFuncLib::SplitFuncNameAndParams(Value->AsString()));
		
		for (const TSharedPtr<FJsonValue> Value : UDataFuncLib::AwGetArrayField(JsonObj, "OnHit"))
			Model.OnHit.Add(UDataFuncLib::SplitFuncNameAndParams(Value->AsString()));

		for (const TSharedPtr<FJsonValue> Value : UDataFuncLib::AwGetArrayField(JsonObj, "OnRemoved"))
			Model.OnRemoved.Add(UDataFuncLib::SplitFuncNameAndParams(Value->AsString()));

		return Model;
	};

};


UCLASS()
class THEAWAKENER_FO_API AAwBullet : public AActor
{
	GENERATED_BODY()
	
public:	
	// Sets default values for this actor's properties
	AAwBullet();
public:
	//唯一识别
	FString UID;
	
	UPROPERTY(BlueprintReadOnly, meta = (ExposeOnSpawn = "true"))
	FBulletModel Model;
	
	//Bullet生命周期长度
	UPROPERTY(BlueprintReadOnly)
	float BulletLifeSpan = 10;
	
	//子弹已运行的时间
	UPROPERTY(BlueprintReadOnly)
	float LivedTime = 0;
	
	//子弹还能命中几次
	UPROPERTY(BlueprintReadWrite)
	int Life = 1;
	
	//子弹飞行方向
	UPROPERTY(BlueprintReadOnly)
	FVector Direction;

    //Action技能等级，用于快速实现不同等级不同伤害
    UPROPERTY(BlueprintReadWrite, meta = (ExposeOnSpawn = "true"))
    int ActionLevel = 3;

	//计算轨迹，返回这一帧的坐标
	//通过ClassPath和FuncName，在调用时查找相应的UFunction
	FJsonFuncData TweenFunc;

	UFUNCTION(BlueprintCallable)
	void SetTweenFunc(FJsonFuncData NewTween ){TweenFunc = NewTween;}
	
	//子弹飞向的目标坐标，仅仅只是给Tween函数用的，大多时候没卵用
	UPROPERTY(BlueprintReadOnly)
	FVector TargetLocaiton;

	//子弹的发射者
	UPROPERTY(BlueprintReadWrite, meta = (ExposeOnSpawn = "true"))
	AAwCharacter* Caster;
	//子弹被发射出来时候，发射者的角色属性
	UPROPERTY(BlueprintReadWrite, meta = (ExposeOnSpawn = "true"))
	FChaProp CasterProp;

	//子弹攻击相关数值
	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	FAttackInfo AttackInfo;

	//能否攻击到Caster
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		bool CanHitCaster = false;
	
	UPROPERTY(VisibleAnywhere, BlueprintReadWrite)
		class UCapsuleComponent* TouchStaticCollision;
	UPROPERTY(VisibleAnywhere, BlueprintReadWrite)
		class UStaticMeshComponent* Mesh;
	UPROPERTY(VisibleAnywhere, BlueprintReadWrite)
		class UAttackHitManager* AttackHitManager;
	UPROPERTY(VisibleAnywhere, BlueprintReadWrite)
		class UParticleSystemComponent* VFX;
	UPROPERTY(VisibleAnywhere, BlueprintReadWrite)
		class UStaticMeshComponent* ShowMesh;

private:
	//创建时的transform
	FTransform OriginTransform;
	//是否暂停
	bool bPause;

protected:

	bool CallOnRemoved(EBulletRemoveType RemoveType = EBulletRemoveType::TimeOut);

	void CallOnHit(FBeCaughtActorInfo CaughtTarget);

	bool CheckCanHitTarget(FBeCaughtActorInfo CaughtTarget);

	void RecordHitTarget(FBeCaughtActorInfo CaughtTarget);

protected:
	// Called when the game starts or when spawned
	virtual void BeginPlay() override;
public:	
	//初始化函数
	void Init(FBulletModel InModel, AAwCharacter* InCaster, FChaProp InCasterProp);
	// Called every frame
	virtual void Tick(float DeltaTime) override;

	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|Bullet")
	void Pause();

	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|Bullet")
	void Resume();

	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|Bullet")
		FTransform GetOriginTransform() { return OriginTransform; }

	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|Bullet")
		void SetOriginTransform(FTransform Trans) { OriginTransform = Trans; }
	
	UFUNCTION()
	virtual void OnHit(UPrimitiveComponent* HitComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, FVector NormalImpulse, const FHitResult& Hit);

	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|Bullet")
		TArray<FBeCaughtActorInfo> GetCatchedTargets();

	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|Bullet")
		TArray<AActor*> GetLeavingTargets();

	//蓝图回调
	UFUNCTION(BlueprintImplementableEvent, Category = "Bullet")
	void OnHitTarget(FBeCaughtActorInfo CaughtTarget);

	//蓝图回调
	UFUNCTION(BlueprintImplementableEvent, Category = "Bullet")
	void OnHitTerrain( );
};
