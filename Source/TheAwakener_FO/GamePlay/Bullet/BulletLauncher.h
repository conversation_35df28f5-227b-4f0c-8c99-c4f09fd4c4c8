// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "AwBullet.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "BulletLauncher.generated.h"

/**
 * 
 */

USTRUCT(BlueprintType)

struct THEAWAKENER_FO_API FBulletLauncher
{
	GENERATED_BODY()
public:
	UPROPERTY(BlueprintReadWrite, meta = (ExposeOnSpawn = "true"))
	FBulletModel Model;
	UPROPERTY(BlueprintReadWrite, meta = (ExposeOnSpawn = "true"))
	FVector Position = FVector::ZeroVector;
	UPROPERTY(BlueprintReadWrite, meta = (ExposeOnSpawn = "true"))
	FVector Direction = FVector::ZeroVector;
	UPROPERTY(BlueprintReadWrite, meta = (ExposeOnSpawn = "true"))
	FJsonFuncData TweenFunc;
	UPROPERTY(BlueprintReadWrite, meta = (ExposeOnSpawn = "true"))
	float Duration;
	UPROPERTY(BlueprintReadWrite, meta = (ExposeOnSpawn = "true"))
	AAwCharacter* Caster;
	UPROPERTY(BlueprintReadWrite, meta = (ExposeOnSpawn = "true"))
	FChaProp CasterProp;
	UPROPERTY(BlueprintReadWrite, meta = (ExposeOnSpawn = "true"))
	FVector TargetLocation;

	FBulletLauncher():
		Model(FBulletModel()), Position(FVector::ZeroVector), Direction(FVector::ZeroVector), TweenFunc(FJsonFuncData()),
		Duration(1.f), Caster(nullptr), TargetLocation(FVector::ZeroVector){};
	FBulletLauncher(
		AAwCharacter* BulletCaster,
		FBulletModel BulletModel,
		FVector Pos,
		FVector Dir,
		float LifeTimeInSec,
		FString TweenFuncName,
		FVector TargetLoc
	):
		Model(BulletModel),
		Position(Pos),
		Direction(Dir),
		TweenFunc(UDataFuncLib::SplitFuncNameAndParams(TweenFuncName)),
		Duration(LifeTimeInSec),
		Caster(BulletCaster),
		CasterProp(BulletCaster ? BulletCaster->CharacterObj.CurProperty : FChaProp()),
		TargetLocation(TargetLoc)
	{};

};
