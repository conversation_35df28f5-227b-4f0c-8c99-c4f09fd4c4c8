// Fill out your copyright notice in the Description page of Project Settings.



#include "AwBullet.h"
#include "Components/StaticMeshComponent.h"

#include "TheAwakener_FO/FunctionLibrary/CallFuncLib.h"
#include "TheAwakener_FO/GameFramework/Timeline/TimelineNode.h"
#include "GameFramework/Character.h"
#include "Kismet/KismetGuidLibrary.h"
#include "Kismet/KismetMathLibrary.h"
#include "Particles/ParticleSystemComponent.h"
#include "TheAwakener_FO/GameFramework/Base/AwGameInstance.h"
#include "TheAwakener_FO/GamePlay/ApparatusCustom/ECSDamageLib.h"
#include "TheAwakener_FO/GamePlay/AssetUserData/AttackHitBoxSign.h"
#include "UObject/ConstructorHelpers.h"

#define TRACE_CLIMB ECC_GameTraceChannel2

// Sets default values
AAwBullet::AAwBullet()
{
 	// Set this actor to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryActorTick.bCanEverTick = true;

	AttackHitManager = CreateDefaultSubobject<UAttackHitManager>(TEXT("AttackHitManager"));

	TouchStaticCollision = CreateDefaultSubobject<UCapsuleComponent>(TEXT("RootMesh"));
	//TouchStaticCollision->SetupAttachment(GetRootComponent());
	SetRootComponent(TouchStaticCollision);
	TouchStaticCollision->SetCollisionObjectType(ECollisionChannel::ECC_WorldDynamic);
	TouchStaticCollision->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
	TouchStaticCollision->SetCollisionResponseToAllChannels(ECollisionResponse::ECR_Ignore);
	TouchStaticCollision->SetCollisionResponseToChannel(ECollisionChannel::ECC_WorldStatic,ECollisionResponse::ECR_Block);
	TouchStaticCollision->SetCollisionResponseToChannel(ECollisionChannel::ECC_GameTraceChannel4,ECollisionResponse::ECR_Block);

	TouchStaticCollision->OnComponentHit.AddDynamic(this, &AAwBullet::OnHit);
	
	//CreateMesh
	Mesh = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("Mesh"));
	Mesh->SetupAttachment(TouchStaticCollision);


	//Add Mesh AssetUserData  This May Cannot construct because lose its template After Package
	/*
	EObjectFlags Flags = RF_Public;
	UAttackHitBoxSign* HitBoxSign = NewObject< UAttackHitBoxSign >(Mesh, NAME_None, Flags);
	Mesh->AddAssetUserData(HitBoxSign);
	*/
	
	//Mesh Collision
	Mesh->SetGenerateOverlapEvents(true);
	Mesh->SetCollisionProfileName("AttackHitBox");
	

	ShowMesh = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("ShowMesh"));
	ShowMesh->SetupAttachment(TouchStaticCollision);
	ShowMesh->SetCollisionObjectType(ECollisionChannel::ECC_WorldDynamic);
	ShowMesh->SetCollisionResponseToAllChannels(ECollisionResponse::ECR_Ignore);
	
	bReplicates = true;
	UID = UKismetGuidLibrary::NewGuid().ToString();
}

// Called when the game starts or when spawned
void AAwBullet::BeginPlay()
{
	Super::BeginPlay();
	
	// OriginTransform = this->GetActorTransform();

	AttackHitManager->ActiveAllAttackHitBox();
	//需要手动调AttackHitManager->Update，所以不调用Start
	//AttackHitManager->Start();
}

void AAwBullet::Init(FBulletModel InModel, AAwCharacter* InCaster, FChaProp InCasterProp)
{
	Caster = InCaster;
	CasterProp = InCasterProp;
	Model = InModel;
	Life = InModel.Life;
	
	if (Model.SightEffect != "")
	{
		//读取VFX的地址
		TArray<FString> StrArray;
		Model.SightEffect.ParseIntoArray(StrArray, TEXT("/"));
		const FString BpName = StrArray.Last();
		const FString SoftClassPath = (TEXT("ParticleSystem'/Game/" + Model.SightEffect + "." + BpName + "'"));
		//根据地址载入VFX资源
		static ConstructorHelpers::FObjectFinder<UParticleSystem> BulletParticleSystem(*SoftClassPath);
		if (BulletParticleSystem.Succeeded())
		{
			VFX->SetTemplate(BulletParticleSystem.Object);
		}
	}
	//Call OnCreate
	if (Model.OnCreate.Num())
	{
		UAwGameInstance* GameInstance = Cast<UAwGameInstance>(GWorld->GetGameInstance());
		for (int i = 0; i < Model.OnCreate.Num(); i++)
		{
			UFunction* Func = UCallFuncLib::GetUFunction(Model.OnCreate[i].ClassPath, Model.OnCreate[i].FunctionName);
			if (Func)
			{
				struct
				{
					AAwBullet* Bullet;
					TArray<FString> Params;
					UTimelineNode* Result = nullptr;
				}FuncParam;
				FuncParam.Bullet = this;
				FuncParam.Params = Model.OnCreate[i].Params;
				if (Caster)
					Caster->ProcessEvent(Func, &FuncParam);
				else
					this->ProcessEvent(Func, &FuncParam);
				if (FuncParam.Result)
					GameInstance->TimelineManager->AddNode(FuncParam.Result);
			}
		}
	}
}

// Called every frame
void AAwBullet::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);
	if (!bPause)
	{
		if (Life <= 0)
		{
			TouchStaticCollision->SetCollisionEnabled(ECollisionEnabled::NoCollision);
			Destroy();
		}
		LivedTime += DeltaTime;
		//Check Lifespan
		if (Life > 0 && !this->IsActorBeingDestroyed())
		{
			if (BulletLifeSpan - LivedTime <= 0)
			{
				if (CallOnRemoved(EBulletRemoveType::TimeOut))
				{
					TouchStaticCollision->SetCollisionEnabled(ECollisionEnabled::NoCollision);
					Destroy();
					return;
				}
			}
		}
		if(this->IsActorBeingDestroyed()) return;
		//Tween
		FHitResult* HitResult = nullptr;
		UFunction* Func = UCallFuncLib::GetUFunction(TweenFunc.ClassPath, TweenFunc.FunctionName);
		if (Func)
		{
			struct
			{
				float TimeElapsed;
				AAwBullet* Bullet;
				TArray<FString> Params;

				FVector Result;
			}TweenFuncParam;
			TweenFuncParam.TimeElapsed = LivedTime;
			TweenFuncParam.Bullet = this;
			TweenFuncParam.Params = TweenFunc.Params;
			if (Caster)
				Caster->ProcessEvent(Func, &TweenFuncParam);
			else
				this->ProcessEvent(Func, &TweenFuncParam);
			FVector MoveToLoc = UKismetMathLibrary::TransformLocation(OriginTransform,  TweenFuncParam.Result);
			Direction = MoveToLoc - this->GetActorLocation();
			Direction.Normalize();
			//this->SetActorRotation(UKismetMathLibrary::FindLookAtRotation(this->GetActorLocation(),MoveToLoc));
			this->SetActorLocation(MoveToLoc,true,HitResult);
		}
        if(HitResult)
        {
        	if(HitResult->bBlockingHit)
        	{
        		UKismetSystemLibrary::PrintString(GWorld, HitResult->GetActor()->GetName());
        	}
        }
		//Call OnHit From CaughtList
		for (FBeCaughtActorInfo CaughtTarget : GetCatchedTargets())
		{
			if (CheckCanHitTarget(CaughtTarget))
			{
				CallOnHit(CaughtTarget);
			}
		}
	
		//Update Records
		AttackHitManager->Update(DeltaTime);
	}
}

void AAwBullet::Pause()
{
	bPause = true;
}

void AAwBullet::Resume()
{
	bPause = false;
}

FTransform AAwBullet::GetOriginTransform()
{
	return OriginTransform;
}

void AAwBullet::SetOriginTransform(FTransform Trans)
{
	OriginTransform = Trans;
}

void AAwBullet::OnHit(UPrimitiveComponent* HitComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, FVector NormalImpulse, const FHitResult& Hit)
{
	AAwCharacter* Character = Cast<AAwCharacter>(OtherActor);
	if(Character) return;
	const bool DestroyBullet = CallOnRemoved(EBulletRemoveType::HitTerrain);
	OnHitTerrain();
	if (DestroyBullet)
	{
		TouchStaticCollision->SetCollisionEnabled(ECollisionEnabled::NoCollision);
		Destroy();
	}
		
}

TArray<FBeCaughtActorInfo> AAwBullet::GetCatchedTargets()
{
	TArray<FBeCaughtActorInfo> CaughtList;
	for (FBeCaughtActorInfo ActorInfo : AttackHitManager->ThisTickValidTarget(true, false, false))
	{
		if (ActorInfo.BeCaughtActor != this->Caster || CanHitCaster)
		{
			CaughtList.Add(ActorInfo);
		}
	}
	return CaughtList;
}

TArray<AActor*> AAwBullet::GetLeavingTargets()
{
	TArray<AActor*> CaughtList;
	for (FBeCaughtActorInfo ActorInfo : AttackHitManager->ThisTickValidTarget(true, false, false))
	{
		if (ActorInfo.BeCaughtActor != this->GetOwner())
		{
			CaughtList.Add(ActorInfo.BeCaughtActor);
		}
	}
	return CaughtList;
}

bool AAwBullet::CallOnRemoved(EBulletRemoveType RemoveType)
{
	bool ShouldDestroyBullet = true;
	//Call OnRemoved
	if (Model.OnRemoved.Num())
	{
		for (int i = 0; i < Model.OnRemoved.Num(); i++)
		{
			UFunction* Func = UCallFuncLib::GetUFunction(Model.OnRemoved[i].ClassPath, Model.OnRemoved[i].FunctionName);
			if (Func)
			{
				struct
				{
					AAwBullet* Bullet;
					EBulletRemoveType RemoveType;
					TArray<FString> Params;
					bool Result;
				}FuncParam;
				FuncParam.Bullet = this;
				FuncParam.RemoveType = RemoveType;
				FuncParam.Params = Model.OnRemoved[i].Params;
				if (Caster)
					Caster->ProcessEvent(Func, &FuncParam);
				else
					this->ProcessEvent(Func, &FuncParam);
				if (!FuncParam.Result)
					ShouldDestroyBullet = false;
			}
		}
	}
	return ShouldDestroyBullet;
}

void AAwBullet::CallOnHit(FBeCaughtActorInfo CaughtTarget)
{
	//Call OnHit
	if (Model.OnHit.Num())
	{
		const UAwGameInstance* GameInstance = Cast<UAwGameInstance>(GWorld->GetGameInstance());
		for (int i = 0; i < Model.OnHit.Num(); i++)
		{
			UFunction* Func = UCallFuncLib::GetUFunction(Model.OnHit[i].ClassPath, Model.OnHit[i].FunctionName);
			if (Func)
			{
				struct
				{
					AAwBullet* Bullet;
					FBeCaughtActorInfo CaughtInfo;
					TArray<FString> Params;
					UTimelineNode* Result = nullptr;
				}FuncParam;
				FuncParam.Bullet = this;
				FuncParam.CaughtInfo = CaughtTarget;
				FuncParam.Params = Model.OnHit[i].Params;
				if (Caster)
					Caster->ProcessEvent(Func, &FuncParam);
				else
					this->ProcessEvent(Func, &FuncParam);
				if (FuncParam.Result)
					GameInstance->TimelineManager->AddNode(FuncParam.Result);
			}
		}
	}
	OnHitTarget(CaughtTarget);
	RecordHitTarget(CaughtTarget);
	//UCharacterHitBox* CharacterHitBox = Cast<UCharacterHitBox>(CaughtTarget.BeHitBox);
	UCharacterHitBoxData* CharacterHitBox = Cast<UCharacterHitBoxData>(CaughtTarget.CaughtHitBoxData);
	if (CharacterHitBox)
	{
		if(CharacterHitBox->AsJustDodge.Active == false) Life--;
	}
	if (Life <= 0)
	{
		TouchStaticCollision->SetCollisionEnabled(ECollisionEnabled::NoCollision);
		Destroy();
	}
}

bool AAwBullet::HasECSHitProtection(FSubjectHandle Subject) const
{
	return UECSDamageLib::HasHitProtection(Subject,GetEcsAttackFlag());
}

bool AAwBullet::CheckCanHitTarget(FBeCaughtActorInfo CaughtTarget)
{
	AAwCharacter* CaughtCharacter = Cast<AAwCharacter>(CaughtTarget.BeCaughtActor);
	if (!CanHitCaster)
	{
		if (CaughtCharacter)
		{
			if (Caster && !Caster->IsEnemy(CaughtCharacter))
				return false;
		}
		if (CaughtTarget.BeCaughtActor == Caster)
			return false;
	}
	
	if (CaughtTarget.BeCaughtActor)
	{
		return AttackHitManager->TargetCanBeHitByHitRecord(CaughtTarget.BeCaughtActor, UID, 0) && Life > 0;
	}
	if (CaughtTarget.BeCaughtSubject == nullptr)
	{
		return false;
	}
	return !HasECSHitProtection(CaughtTarget.BeCaughtSubject);
}

EFlagmarkBit AAwBullet::GetEcsAttackFlag() const
{
	return AttackHitManager->GetECSAttackFlag("Mesh");
}

void AAwBullet::AddECSHitLog(const FSubjectHandle& SubjectHandle, int HittedTime, float HitDelay)
{
	UECSDamageLib::AddHitLog(SubjectHandle,GetEcsAttackFlag(),HittedTime,HitDelay);
}

void AAwBullet::RecordHitTarget(FBeCaughtActorInfo CaughtTarget)
{
	//获得之前命中过几次
	int HittedTime = Model.HitSameTarget;
	for (auto Record : AttackHitManager->HitRecords)
	{
		if (Record.Target == CaughtTarget.BeCaughtActor)
		{
			HittedTime = Record.CanHits;
			break;
		}
	}
	// HittedTime--;
	//UKismetSystemLibrary::PrintString(this,CaughtTarget.BeCaughtActor->GetName()+"_"+FString::FromInt(HittedTime),true,false,FColor::Red,10.f);
	if (CaughtTarget.BeCaughtActor)
	{
		FOffenseHitRecord NewRecord = FOffenseHitRecord(UID, 0, CaughtTarget.BeCaughtActor, HittedTime, Model.HitDelay);
		AttackHitManager->AddHitRecord(NewRecord);
	}
	else
	{
		AddECSHitLog(CaughtTarget.BeCaughtSubject,HittedTime,Model.HitDelay);
	}
}
