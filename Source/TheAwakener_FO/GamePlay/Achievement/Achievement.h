// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/Object.h"
#include "Achievement.generated.h"

/**
 * 成就进度的信息
 */
USTRUCT(BlueprintType)
struct FAchievementProgressInfo
{
	GENERATED_BODY()
public:
	//信息关键字，收到这个信息就会增加进度
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString Key;

	//收到这个信息关键字，进度加多少
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int ProgressIncreased = 1;

	FAchievementProgressInfo(){};
	FAchievementProgressInfo(FString ReceiveKey, int Progress):
		Key(ReceiveKey), ProgressIncreased(Progress){};
};

/**
 * 成就Model
 */
USTRUCT(BlueprintType)
struct FAchievementModel
{
	GENERATED_BODY()
public:
	//成就id
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString Id;
	
	//接受的信息关键字
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FAchievementProgressInfo> ProgressKey;

	//每一级的进度所需要的升级经验，有多少个就有多少级
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<int> ExpRequired;

	static FAchievementModel FromJson(TSharedPtr<FJsonObject> JsonObj);

	//因为某个信号，能够获得到少进度
	int ProgressByBeepKey(FString SignalKey);
};

/**
 * 成就Obj
 */
USTRUCT(BlueprintType)
struct FAchievementObj
{
	GENERATED_BODY()
public:
	//模板
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FAchievementModel Model;

	//当前所属阶段
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int Level = 0;

	//当前阶段进度
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int Progress = 0;

	//是否完成了，直接inline
	bool Done() const
	{
		return Level >= Model.ExpRequired.Num();
	}

	FAchievementObj(){};
	FAchievementObj(FAchievementModel AchievementModel):
		Model(AchievementModel){};

	/**
	 *添加成就完成度
	 *@param Exp 要增加的进度
	 *@return 是否升级
	 */
	bool AddExp(int Exp);

	/**
	 * 因为受到某个信号而增加进度
	 * @param SignalKey 信号
	 * @return 是否升级
	 */
	bool BeepBySignal(FString SignalKey);
};
