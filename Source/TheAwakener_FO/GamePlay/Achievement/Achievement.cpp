// Fill out your copyright notice in the Description page of Project Settings.


#include "Achievement.h"

#include "TheAwakener_FO/FunctionLibrary/DataFuncLib.h"

FAchievementModel FAchievementModel::FromJson(TSharedPtr<FJsonObject> JsonObj)
{
	FAchievementModel Model = FAchievementModel();
	Model.Id = UDataFuncLib::AwGetStringField(JsonObj, "Id");
	if (JsonObj-><PERSON><PERSON><PERSON>("ExpRequired"))
	{
		for (const TSharedPtr<FJsonValue, ESPMode::ThreadSafe> Exp : JsonObj->GetArrayField("ExpRequired"))
		{
			Model.ExpRequired.Add(Exp->AsNumber());
		}
	}else
	{
		Model.ExpRequired.Add(1);
	}
	if (JsonObj-><PERSON><PERSON><PERSON>("ProgressKey"))
	{
		for (const TSharedPtr<FJsonValue, ESPMode::ThreadSafe> Keys : JsonObj->Get<PERSON><PERSON>y<PERSON>ield("ProgressKey"))
		{
			if (Keys->AsObject()-><PERSON><PERSON><PERSON>("Key"))
			{
				Model.ProgressKey.Add(FAchievementProgressInfo(
					Keys->AsObject()->GetStringField("Key"),
					Keys->AsObject()->HasField("Plus") ? Keys->AsObject()->GetIntegerField("Plus") : 1
				));
			}
		} 
	}
	return Model;
}

bool FAchievementObj::AddExp(int Exp)
{
	bool Res = false;
	if (Level >= Model.ExpRequired.Num()) return false;
	this->Progress += Exp;
	while (Level < Model.ExpRequired.Num() && Progress >= Model.ExpRequired[Level] )
	{
		Progress -= Model.ExpRequired[Level];
		Level += 1;
		Res = true;
	}
	return Res;
}

int FAchievementModel::ProgressByBeepKey(FString SignalKey)
{
	int Res = 0;
	for (const FAchievementProgressInfo PKey : this->ProgressKey)
	{
		if (PKey.Key == SignalKey) Res += PKey.ProgressIncreased;
	}
	return Res;
}

bool FAchievementObj::BeepBySignal(FString SignalKey)
{
	const int ExpGain = Model.ProgressByBeepKey(SignalKey);
	return AddExp(ExpGain);
}