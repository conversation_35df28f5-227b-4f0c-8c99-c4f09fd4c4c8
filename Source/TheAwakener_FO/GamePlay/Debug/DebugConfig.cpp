// Fill out your copyright notice in the Description page of Project Settings.


#include "DebugConfig.h"
#include "TheAwakener_FO/FunctionLibrary/DataFuncLib.h"

FDebugConfig FDebugConfig::FromJson(TSharedPtr<FJsonObject> JsonObj)
{
	FDebugConfig Res;
	Res.CameraSpringMoveSpeedLimit = UDataFuncLib::AwGetNumberField(JsonObj, "CameraSpringMoveSpeedLimit", 300.00f);
	Res.CameraMoveSpeedLimit = UDataFuncLib::AwGetNumberField(JsonObj, "CameraMoveSpeedLimit", 1000.00f);
	Res.ShowPopText = UDataFuncLib::AwGetBoolField(JsonObj, "ShowPopText", true);
	
	if (JsonObj->Has<PERSON>ield("PopText"))
	{
		for (const TSharedPtr<FJsonValue, ESPMode::ThreadSafe> PText : JsonObj->GetArrayField("PopText"))
		{
			FString TId = UDataFuncLib::AwGetStringField(PText->AsObject(), "Id", "");
			if (TId.IsEmpty() == false)
			{
				Res.PopTextInfo.Add(TId, FPopTextLauncher::FromJson(PText->AsObject()));
			} 
		}
	}

	Res.StartHasFall = UDataFuncLib::AwGetNumberField(JsonObj, "StartFall" , 0.5f);
	Res.JumpHasFall = UDataFuncLib::AwGetNumberField(JsonObj, "JumpFall" , 0.5f);
	Res.FreezingRate = UDataFuncLib::AwGetNumberField(JsonObj, "FreezingRate", 0.3f);
	Res.MinFreezingTime = UDataFuncLib::AwGetNumberField(JsonObj, "FreezingMin", 0.01f);
	Res.FreezingPassedThreshold = UDataFuncLib::AwGetNumberField(JsonObj, "FreezingPassedThreshold", 1.0f);
	Res.FreezingMinEnemyCount = UDataFuncLib::AwGetNumberField(JsonObj, "FreezingMinEnemyCount", 10);
	Res.IncreaseMobPropsWithLevel = UDataFuncLib::AwGetBoolField(JsonObj, "IncreaseMobPropsWithLevel", false);
	Res.SP_FreezeDur_Short = UDataFuncLib::AwGetNumberField(JsonObj, "SP_FreezeDur_Short", 1.0f);
	Res.SP_FreezeDur_Long = UDataFuncLib::AwGetNumberField(JsonObj, "SP_FreezeDur_Long", 3.0f);
	Res.SP_RecoverySpeed = UDataFuncLib::AwGetNumberField(JsonObj, "SP_RecoverySpeed", 2000.0f);
	Res.SP_SprintConsumeSpeed = UDataFuncLib::AwGetNumberField(JsonObj, "SP_SprintConsumeSpeed", 100.0f);
	Res.MP_RecoverySpeed = UDataFuncLib::AwGetNumberField(JsonObj, "MP_RecoverySpeed", 2000.0f);
	Res.MP_FreezeDur = UDataFuncLib::AwGetNumberField(JsonObj, "MP_FreezeDur", 1.0f);

	Res.TempFloat = UDataFuncLib::AwGetNumberField(JsonObj, "TempFloat", 1.0f);
	Res.RelicDiceWeight = UDataFuncLib::AwGetNumberField(JsonObj, "RelicDiceWeight", 1.0f);
	Res.NewRelicDiceWeight = UDataFuncLib::AwGetNumberField(JsonObj, "NewRelicDiceWeight", 1.0f);
	Res.AntiRelicDiceWeight = UDataFuncLib::AwGetNumberField(JsonObj, "AntiRelicDiceWeight", 1.0f);
	Res.AntiRelicDice = UDataFuncLib::AwGetBoolField(JsonObj, "AntiRelicDice", false);

	Res.RogueItemDamageRecoverBase = UDataFuncLib::AwGetNumberField(JsonObj, "RogueItemDamageRecoverBase", 1.0f);
	Res.RogueItemDamageRecoverOnceMax = UDataFuncLib::AwGetNumberField(JsonObj, "RogueItemDamageRecoverOnceMax", 1.0f);
	
	Res.CameraOffset.InitFromString(UDataFuncLib::AwGetStringField(JsonObj, "CameraOffset", "X=20 Y=90 Z=90"));
	Res.CameraArmLength = UDataFuncLib::AwGetNumberField(JsonObj, "CameraArmLength", 1.0f);

	Res.ShowAttackBox = UDataFuncLib::AwGetBoolField(JsonObj, "ShowAttackBox", false);
	Res.ShowNormalHitBox = UDataFuncLib::AwGetBoolField(JsonObj, "ShowNormalHitBox", false);
	Res.ShowGuardHitBox = UDataFuncLib::AwGetBoolField(JsonObj, "ShowGuardHitBox", false);
	Res.ShowJustDodgeBox = UDataFuncLib::AwGetBoolField(JsonObj, "ShowJustDodgeBox", false);

	Res.Version = UDataFuncLib::AwGetStringField(JsonObj,"Version");
	
	return Res;
}
