// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/UI/HUD/PopUpText.h"
#include "DebugConfig.generated.h"

/**
 * 游戏中Debug的Config，在最终版的时候，请把这里的变量和值写进代码里，至少定义一个常量
 */
USTRUCT(BlueprintType)
struct FDebugConfig
{
	GENERATED_BODY()
public:
	//所有要跳的数字的信息
	UPROPERTY()
	TMap<FString, FPopTextLauncher> PopTextInfo;
	//是否显示跳数字
	UPROPERTY()
	bool ShowPopText = false;
	
	//镜头速度除以多少
	UPROPERTY()
	float CameraSpringMoveSpeedLimit = 3.00f;
	//移动速度每秒最多多少米
	UPROPERTY()
	float CameraMoveSpeedLimit = 1000.00f;

	//下落时，HasFall达到多少时，开始算是下落了
	UPROPERTY()
	float StartHasFall = 0.3f;
	//上升时，HasFall达到多少，才算下落
	UPROPERTY()
	float JumpHasFall = 0.5f;
	
	//硬直时间倍率，在第二个攻击产生时，Freezing会发生的变化
	UPROPERTY()
	float FreezingRate = 0.3f;
	//硬直最小时间，第二次攻击产生时，硬直最低设置到这个值
	UPROPERTY()
	float MinFreezingTime = 0.01f;
	//攻击方硬直结束后，多长时间内再次命中采用上面所述的变化版本（由于没找到之前的硬直衰减代码，ECS单独处理
	UPROPERTY()
	float FreezingPassedThreshold = 1;
	//ECS怪物太多，连续击中足够的怪物才会有顿帧
	UPROPERTY()
	float FreezingMinEnemyCount = 10;
	//是否使用随等级增加血量
	UPROPERTY()
	bool IncreaseMobPropsWithLevel = false;
	
	// 使用体力值之后（没有清空）停止多少秒开始恢复体力值
	UPROPERTY()
	float SP_FreezeDur_Short = 1.0f;
	// 体力值清空之后停止多少秒开始恢复体力值
	UPROPERTY()
	float SP_FreezeDur_Long = 3.0f;
	// 体力值恢复速度，每秒恢复多少值
	UPROPERTY()
	float SP_RecoverySpeed = 2000;
	UPROPERTY()
	float SP_SprintConsumeSpeed = 100;
	
	UPROPERTY()
	float MP_RecoverySpeed = 2000;
	UPROPERTY()
	float MP_FreezeDur = 1.0f;
	
	UPROPERTY()
	float TempFloat = 0.0f;

	UPROPERTY()
	FVector CameraOffset = FVector(20,90,90);
	UPROPERTY()
	float CameraArmLength = 400;

	UPROPERTY()
	bool ShowAttackBox = false;
	UPROPERTY()
	bool ShowNormalHitBox = false;
	UPROPERTY()
	bool ShowGuardHitBox = false;
	UPROPERTY()
	bool ShowJustDodgeBox = false;

	UPROPERTY()
	float RelicDiceWeight = 1;
	
	UPROPERTY()
	float NewRelicDiceWeight = 1;

	UPROPERTY()
	bool AntiRelicDice = false;

	UPROPERTY()
	float AntiRelicDiceWeight = 1;

	UPROPERTY()
	float RogueItemDamageRecoverBase = 1;

	UPROPERTY()
	float RogueItemDamageRecoverOnceMax = 1;
	
	UPROPERTY(BlueprintReadOnly,VisibleAnywhere)
	FString Version = "";
	
	static FDebugConfig FromJson(TSharedPtr<FJsonObject> JsonObj);
};