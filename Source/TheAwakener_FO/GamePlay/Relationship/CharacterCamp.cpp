// Fill out your copyright notice in the Description page of Project Settings.


#include "CharacterCamp.h"

#include "TheAwakener_FO/FunctionLibrary/DataFuncLib.h"

FCharacterCamp FCharacterCamp::FromJson(TSharedPtr<FJsonObject> JsonObj)
{
	FCharacterCamp Res = FCharacterCamp();
	Res.CampId = UDataFuncLib::AwGetNumberField(JsonObj, "CampId", 0);
	if (JsonObj-><PERSON><PERSON>ield("Sides"))
	{
		for (const TSharedPtr<FJsonValue, ESPMode::ThreadSafe> SInfo : JsonObj->GetArrayField("Sides"))
		{
			uint8 ThisSideId = SInfo->AsNumber();
			if (Res.Sides.Contains(ThisSideId) == false) Res.Sides.Add(ThisSideId);
		} 
	}
	if (JsonObj->Has<PERSON>ield("CanAttackCamps"))
	{
		for (const TSharedPtr<FJsonValue, ESPMode::ThreadSafe> SInfo : JsonObj->GetArrayField("CanAttackCamps"))
		{
			uint8 ThisCampId = SInfo->AsNumber();
			if (Res.CanAttackCampId.Contains(ThisCampId) == false) Res.CanAttackCampId.Add(ThisCampId);
		} 
	}
	if (JsonObj->HasField("CanInteractSides"))
	{
		for (const TSharedPtr<FJsonValue, ESPMode::ThreadSafe> SInfo : JsonObj->GetArrayField("CanInteractSides"))
		{
			uint8 ThisSideId = SInfo->AsNumber();
			if (Res.CanInteractSideId.Contains(ThisSideId) == false) Res.CanInteractSideId.Add(ThisSideId);
		} 
	}
	return Res;
}
