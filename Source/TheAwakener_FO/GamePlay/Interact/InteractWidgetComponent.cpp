// Fill out your copyright notice in the Description page of Project Settings.


#include "InteractWidgetComponent.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"

UInteractWidgetComponent::UInteractWidgetComponent()
{
	SetVisibility(false);
	SetWidgetSpace(EWidgetSpace::Screen);
	SetInitialSharedLayerName("WidgetComponentScreenLayer_High");
	SetInitialLayerZOrder(-80);
	
}

void UInteractWidgetComponent::BeginPlay()
{
	Super::BeginPlay();

}

void UInteractWidgetComponent::SetCurWidget(EInteractTargetType InteractType, TSubclassOf<UUserWidget> TargetClass,AAwPlayerController* Controller)
{

	UUserWidget* OldWidget = GetWidget();
	
	if (OldWidget->IsValidLowLevel())
	{
		if (OldWidget->GetClass()== TargetClass)
		{
			return;
		}
		
		/*
		//setwidget本身有GC  如果手动GC 虽然可以保证即时性 但是特定情况下频繁调用GC会导致主线程ide wait时间过长 引起卡顿
		OldWidget->MarkPendingKill();
		OldWidget = nullptr;
		SetWidget(nullptr);
		UKismetSystemLibrary::CollectGarbage();
		*/
				
	}
	if (Controller&& TargetClass)
	{
		UUserWidget* NewWidget = CreateWidget(Controller, TargetClass);
		SetWidget(NewWidget);
	}

}
