// Fill out your copyright notice in the Description page of Project Settings.


#include "InfoBubbleWidgetComponent.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"

UInfoBubbleWidgetComponent::UInfoBubbleWidgetComponent()
{
	SetComponentTickEnabled(false);
	Paused = false;
	CatchedPlayer = nullptr;
	SetInitialSharedLayerName("WidgetComponentScreenLayer_Higher");
	SetInitialLayerZOrder(-90);
}


void UInfoBubbleWidgetComponent::BeginPlay()
{
	Super::BeginPlay();

	InitWidgetInfo();
	BindEventToTrigger();
	//首次检测 以免初始就在范围内不触发overlap
	if (TriggerRangeComp)
	{
		float Radius = TriggerRangeComp->GetScaledSphereRadius();
		bool IsInRadius = UGameplayFuncLib::HavePlayerCharacterInRange(GetOwner()->GetActorLocation() , Radius);
		if (IsInRadius)
		{
			if (GetWidget())
			{
				GetWidget()->SetVisibility(ESlateVisibility::HitTestInvisible);
			}
			SetComponentTickEnabled(true);
		}
		else
		{
			if (GetWidget())
			{
				GetWidget()->SetVisibility(ESlateVisibility::Collapsed);
			}
			SetComponentTickEnabled(false);
		}
	}
}

void UInfoBubbleWidgetComponent::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
	Super::TickComponent(DeltaTime, TickType, ThisTickFunction);
	if (GetWorld()&& GetWorld()->IsGameWorld())
	{
		CheckPlayerInRange();
		CheckAddtionalCondition();
	}

}

void UInfoBubbleWidgetComponent::TryCatchPlayer(UPrimitiveComponent* OverlappedComponent, AActor* Actor, UPrimitiveComponent* Component, int BodyIndex, bool Sweep, const FHitResult& SweepResult)
{
	if (Paused)
	{
		return;
	}
	AAwCharacter* OverlapCharacter = Cast<AAwCharacter>(Actor);
	if (OverlapCharacter->IsPlayerCharacter())
	{
		CatchedPlayer = OverlapCharacter;
		if (GetWidget())
		{
			GetWidget()->SetVisibility(ESlateVisibility::HitTestInvisible);
		}
		SetComponentTickEnabled(true);
		CatchedPlayer = OverlapCharacter;
	}
}

void UInfoBubbleWidgetComponent::SetComponentPaused(bool IsPaused)
{
	Paused = IsPaused;

	SetComponentTickEnabled(!Paused);
}

void UInfoBubbleWidgetComponent::InitWidgetInfo()
{
	if (!GetWidget())
	{
		return;
	}
	if (GetWidget()->Implements<UInfoWidgetInterface>())
	{
		IInfoWidgetInterface::Execute_SetInfoWidgetOwner(GetWidget(), GetOwner());
		IInfoWidgetInterface::Execute_SetInfoWidgetInfo(GetWidget(), DefalutWidgetText, DefalutWidgetInfo);
		IInfoWidgetInterface::Execute_ChangeInfoWidgetState(GetWidget(), TEXT("Defalut"),TMap<FString,FString>());
	}

	GetWidget()->SetVisibility(ESlateVisibility::Collapsed);
}

bool UInfoBubbleWidgetComponent::CheckPlayerInRange()
{
	bool result = false;
	if (!CatchedPlayer)
	{
		SetComponentTickEnabled(false);
		return result;
	}
	if (FVector::Dist(CatchedPlayer->GetActorLocation(), GetOwner()->GetActorLocation()) > HideWidgetDistance)
	{
		if (GetWidget())
		{
			GetWidget()->SetVisibility(ESlateVisibility::Collapsed);
		}
		CatchedPlayer = nullptr;

		return result;
	}

	result = true;

	return result;
}

void UInfoBubbleWidgetComponent::CheckAddtionalCondition()
{
	//通知widget做状态变更
	for (auto Condition: StateConditions)
	{
		UFunction* ConditionFunc = UCallFuncLib::GetUFunction(Condition.ConditionFunc.ClassPath, Condition.ConditionFunc.FunctionName);
		if (ConditionFunc)
		{
			struct
			{
				UActorComponent* InfoBubbleComponent;
				TArray<FString> Params;
				bool Result;
			}ConditionFuncParam;
			ConditionFuncParam.InfoBubbleComponent = this;
			ConditionFuncParam.Params = Condition.ConditionFunc.Params;
			this->ProcessEvent(ConditionFunc, &ConditionFuncParam);
			if (ConditionFuncParam.Result)
			{
				if (GetWidget()&&GetWidget()->Implements<UInfoWidgetInterface>())
				{
					IInfoWidgetInterface::Execute_ChangeInfoWidgetState(GetWidget(), Condition.StateString, Condition.ExParams);
				}
				break;
			}
		}
	}
}

void UInfoBubbleWidgetComponent::BindEventToTrigger()
{
	for (UActorComponent* Component : GetOwner()->GetComponents())
	{
		if (IsValid(Cast<UInfoWidgetSphere>(Component)))
		{
			TriggerRangeComp = Cast<UInfoWidgetSphere>(Component);
			break;
		}
	}

	if (TriggerRangeComp)
	{
		TriggerRangeComp->OnComponentBeginOverlap.AddDynamic(this, &UInfoBubbleWidgetComponent::TryCatchPlayer);

		HideWidgetDistance = FMath::Max(HideWidgetDistance, TriggerRangeComp->GetScaledSphereRadius() + 100);
	}
}

UInfoWidgetSphere::UInfoWidgetSphere()
{
	SphereRadius = 1000;

	SetCollisionEnabled(ECollisionEnabled::QueryOnly);
	SetCollisionObjectType(ECollisionChannel::ECC_GameTraceChannel8);
	SetCollisionResponseToAllChannels(ECollisionResponse::ECR_Ignore);
	SetCollisionResponseToChannel(ECollisionChannel::ECC_GameTraceChannel7, ECollisionResponse::ECR_Overlap);
}
