// Fill out your copyright notice in the Description page of Project Settings.


#include "BeInteractedBox.h"
#include "InfoBubbleWidgetComponent.h"
#include "RogueInteract.h"
#include "InteractActor/InteractEventTrigger.h"
#include "InteractActor/InteractGoDungeonActor.h"
#include "InteractActor/InteractShopActor.h"
#include "InteractActor/InteractTerrainItem.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GamePlay/ChangeClassOrSkill/ChangeClassOrSkillPoint.h"

#include "TheAwakener_FO/GamePlay/Thing/ThingPackageActor.h"
#include "TheAwakener_FO/GamePlay/Trading/MerchantShop.h"

#include "TheAwakener_FO/UI/Shop.h"


//当前是否被激活中，比如被pickup的掉落物就是返回false的
bool UBeInteractedBox::CurrentActive(AActor* InteractPromoter) const
{
	AAwCharacter* Character = Cast<AAwCharacter>(InteractPromoter);
	switch (this->InteractType)
	{
	case EInteractTargetType::Loot:
		{
			if (!InteractPromoter) return false;
			AThingPackageActor* Loot = Cast<AThingPackageActor>(this->GetOwner());
			if (!Loot) return false;
			return Loot->CanBeLoot(Cast<AAwCharacter>(InteractPromoter));
		}
	case EInteractTargetType::Dialog:
		{
 			if (!InteractPromoter) return false;
			AAwCharacter* TalkerToMe = Cast<AAwCharacter>(InteractPromoter);
			AAwCharacter* Me = Cast<AAwCharacter>(this->GetOwner());
			if (!Me || !TalkerToMe || Me->Dead(true) || TalkerToMe->Dead(true)) return false;
			//返回是否有交流的可能性
			return Me->CanBeInteract(TalkerToMe);
		}
	case EInteractTargetType::Revive:
		{
			if (!InteractPromoter) return false;
			AAwCharacter* Reviver = Cast<AAwCharacter>(InteractPromoter);
			if (!Reviver || Reviver->Dead(true)) return false;
			AAwCharacter* Me = Cast<AAwCharacter>(this->GetOwner());
			if (!Me || Me->InSecondWind() == false) return false;

			return true;
		}
	case EInteractTargetType::Shop:
		{
			AAwPlayerController* MyController = Character->OwnerPlayerController;
			if (!MyController) return false;
			return 	MyController->GameControlState != EGameControlState::Shopping;
		}
	case EInteractTargetType::ItemOnTerrain:
		{
			const AInteractTerrainItem* Me = Cast<AInteractTerrainItem>(this->GetOwner());
			if (!Me || Me->CanInteractNow == false) return false;
			return true;
		}
	case EInteractTargetType::EventTrigger:
	case EInteractTargetType::TapGetLoot:
		{
			AInteractEventTrigger* Me = Cast<AInteractEventTrigger>(this->GetOwner());
			if (!Me) return false;
			return Me->CanBeInteractNow();
		}
		
	case EInteractTargetType::GoDungeon:
		return true;
		
	case EInteractTargetType::ChangeClass:
	case EInteractTargetType::ChangeSkill:
		return true;
	
	case EInteractTargetType::RogueChangePawn:
	case EInteractTargetType::RogueSkillSelection:
	case EInteractTargetType::RogueChangeAwakeSkill:
	case EInteractTargetType::RogueTalent:
	case EInteractTargetType::RogueSetStakePawn:
	case EInteractTargetType::RogueBattleUpgradePreview:
	case EInteractTargetType::RogueManual:
	case EInteractTargetType::RogueChangeWeapon:
	case EInteractTargetType::RogueChangeSkin:
		return true;
		
	default: return false;
	}
}

//返回一个拾取评分，评分越高越容易成为交互目标
float UBeInteractedBox::InteractScore(FVector PromoterLocation, FRotator PromoterRotation) const
{
	const FVector ThisLocation = this->GetOwner()->GetActorLocation();
	const float DistanceSquared = FVector::DistSquared(ThisLocation, PromoterLocation);

	float Value = 0;
	switch (this->InteractType)
	{
	case EInteractTargetType::Loot:
		{
			Value = 1.000f; //拾取的优先级是1.000f
			break;
		}
	case EInteractTargetType::Revive:
		{
			Value = 5.000f; //复活队友优先级5.000f
			break;
		}
	case EInteractTargetType::Dialog:
		{
			Value = 0.4f;	//交谈优先级
			break;
		}
	case EInteractTargetType::Shop:
		{
			Value = 0.5f;
			break;
		}
	case EInteractTargetType::ItemOnTerrain:
		{
			Value = 0.6f;
			break;
		}
	case EInteractTargetType::GoDungeon:
		{
			Value = 0.5f;
			break;
		}
	case EInteractTargetType::ChangeClass:
	case EInteractTargetType::ChangeSkill:
		{
			Value = 0.5f;
			break;
		}
	case EInteractTargetType::EventTrigger:
	case EInteractTargetType::TapGetLoot:
		{
			const AInteractEventTrigger* Me = Cast<AInteractEventTrigger>(this->GetOwner());
			Value = Me ? Me->InteractValue : 0.6f;
		}break;

	case EInteractTargetType::RogueChangePawn:
	case EInteractTargetType::RogueSkillSelection:
	case EInteractTargetType::RogueChangeAwakeSkill:
	case EInteractTargetType::RogueTalent:
	case EInteractTargetType::RogueSetStakePawn:
	case EInteractTargetType::RogueBattleUpgradePreview:
	case EInteractTargetType::RogueManual:
	case EInteractTargetType::RogueChangeWeapon:
	case EInteractTargetType::RogueChangeSkin:
		{ Value = 1; break; }
	default : { Value = 0; break; } 
	}
	return FMath::IsNearlyZero(DistanceSquared) ? Value : (Value / DistanceSquared);
}

//执行交互行为
bool UBeInteractedBox::Interact(AActor* InteractPromoter)
{
	AAwCharacter* Character = Cast<AAwCharacter>(InteractPromoter);
	switch (this->InteractType)
	{
	case EInteractTargetType::Loot:
		{
			if (!InteractPromoter) return false;
			AThingPackageActor* Loot = Cast<AThingPackageActor>(this->GetOwner());
			if (!Loot) return false;
			if (!Character) return false;
			Loot->PickUp(Character);
			return true;
		}
	case EInteractTargetType::Shop:
		{
			AMerchantShop* Actor  = Cast<AMerchantShop>(this->GetOwner());
			if (Actor) Actor->OnInteract(Character);
			UGameMain* MainUI = nullptr;
			if (UGameplayFuncLib::GetUiManager()->OpenedWidgets.Contains("GameMain"))
				MainUI = Cast<UGameMain>(UGameplayFuncLib::GetUiManager()->OpenedWidgets["GameMain"]);

			Actor->SetShopItemPriceUIAttributes();
			if (MainUI)
			{
				MainUI->HiddeMainUI("InHide");
				MainUI->SetCurItem(Actor);
			}
			
			return true;
		}
	case EInteractTargetType::Dialog:
		{
			AAwCharacter* Me = Cast<AAwCharacter>(this->GetOwner());
			if (!Me || Me->Dead(true)) return false;

			//Todo 开始交谈
			UGameplayFuncLib::PauseGameForDialog(Me,Character->OwnerPlayerController->GetLocalPCIndex());
			return true;
		}
	case EInteractTargetType::Revive:
		{
			AAwCharacter* Me = Cast<AAwCharacter>(this->GetOwner());
			if (!Me || Me->InSecondWind() == false) return false;
			//TODO how to revive ?
			Me->RevivedOnSecondWind();
			return true;
		}
	case EInteractTargetType::ItemOnTerrain:
		{
			AInteractTerrainItem* Me = Cast<AInteractTerrainItem>(this->GetOwner());
			if (!Me) return false;
			return Me->OnBeInteracted();
		}
	case EInteractTargetType::GoDungeon:
		{
			AInteractGoDungeonActor* Actor  = Cast<AInteractGoDungeonActor>(this->GetOwner());
			if (Actor) Actor->OnInteract();
			return true;
		}
	case EInteractTargetType::EventTrigger:
	case EInteractTargetType::TapGetLoot:
		{
			AInteractEventTrigger* Actor  = Cast<AInteractEventTrigger>(this->GetOwner());
			if (Actor) Actor->OnBeInteracted();
			return true;
		}
	case EInteractTargetType::ChangeClass:
	case EInteractTargetType:: ChangeSkill:
		{
			AChangeClassOrSkillPoint* Actor = Cast<AChangeClassOrSkillPoint>(this->GetOwner());
			if (Actor)
				Actor->Interact(Character->OwnerPlayerController);
			return true;
		}
	case EInteractTargetType::RogueChangePawn:
	case EInteractTargetType::RogueSkillSelection:
	case EInteractTargetType::RogueChangeAwakeSkill:
	case EInteractTargetType::RogueTalent:
	case EInteractTargetType::RogueSetStakePawn:
	case EInteractTargetType::RogueBattleUpgradePreview:
	case EInteractTargetType::RogueManual:
	case EInteractTargetType::RogueChangeWeapon:
	case EInteractTargetType::RogueChangeSkin:
		{
			if (ARogueInteract* RogueInteract = Cast<ARogueInteract>(this->GetOwner()))
				RogueInteract->Interact(this->InteractType);
			return true;
		}
	
	default: return false;
	}
}

//被聚焦
void UBeInteractedBox::OnGetFocus(AActor* InteractPromoter)
{
	UInfoBubbleWidgetComponent* InfoWidgetComponent = Cast<UInfoBubbleWidgetComponent>(GetOwner()->GetComponentByClass(UInfoBubbleWidgetComponent::StaticClass()));

	if (InfoWidgetComponent)
	{
		if (InfoWidgetComponent->GetWidget()&& InfoWidgetComponent->GetWidget()->Implements<UInfoWidgetInterface>())
		{
			IInfoWidgetInterface::Execute_ChangeInfoWidgetState(InfoWidgetComponent->GetWidget(),TEXT("Focus"),TMap<FString,FString>());
		}
	}
	OnGetFocusFunc();
}

//不再被聚集
void UBeInteractedBox::OnLoseFocus(AActor* InteractPromoter)
{
	UInfoBubbleWidgetComponent* InfoWidgetComponent = Cast<UInfoBubbleWidgetComponent>(GetOwner()->GetComponentByClass(UInfoBubbleWidgetComponent::StaticClass()));

	if (InfoWidgetComponent)
	{
		if (InfoWidgetComponent->GetWidget() && InfoWidgetComponent->GetWidget()->Implements<UInfoWidgetInterface>())
		{
			IInfoWidgetInterface::Execute_ChangeInfoWidgetState(InfoWidgetComponent->GetWidget(), TEXT("LoseFocus"), TMap<FString, FString>());
		}
	}
	OnLoseFocusFunc();
	//旧逻辑 已弃用
	/*
	switch (this->InteractType)
	{
	case EInteractTargetType::Loot:
		{
			AThingPackageActor* Loot  = Cast<AThingPackageActor>(this->GetOwner());
			if (!Loot) return;
			Loot->HideUI();
			AAwCharacter* Me = Cast<AAwCharacter>(InteractPromoter);
		}break;
	case EInteractTargetType::Dialog:
		{
			const AAwCharacter* Actor = Cast<AAwCharacter>(this->GetOwner());
			if (Actor) Actor->InteractWidgetVisible(false);
		}break;
	case EInteractTargetType::Shop:
		{
			AMerchantShop* Actor  = Cast<AMerchantShop>(this->GetOwner());
			if (!Actor) return;
			Actor->HideInteractSign();
			Actor->EndInteract(Cast<AAwCharacter>(InteractPromoter));
			
			UGameMain* MainUI = nullptr;
			if (UGameplayFuncLib::GetUiManager()->OpenedWidgets.Contains("GameMain"))
				MainUI = Cast<UGameMain>(UGameplayFuncLib::GetUiManager()->OpenedWidgets["GameMain"]);
			if (MainUI)
				MainUI->ShowMainUI("InHide");
			Actor->HideShopPriceUI();
			Actor->HideShopItemUI(true);
			
		}break;
	case EInteractTargetType::ItemOnTerrain:
		{
			const AInteractTerrainItem* Me = Cast<AInteractTerrainItem>(this->GetOwner());
			if (Me) Me->HideInteractSign();
		}break;
	case EInteractTargetType::EventTrigger:
		{
			const AInteractEventTrigger* Me = Cast<AInteractEventTrigger>(this->GetOwner());
			if (Me) Me->HideInteractSign();
		}break;
	case EInteractTargetType::GoDungeon:
		{
			AInteractShopActor* Actor  = Cast<AInteractShopActor>(this->GetOwner());
			if (!Actor) return;
			Actor->HideUI();
		}break;
	case EInteractTargetType::ChangeClassOrSkill:
		{
			AChangeClassOrSkillPoint* Actor = Cast<AChangeClassOrSkillPoint>(this->GetOwner());
			if (!Actor) return;
			Actor->HideInteractButtonUI();
		}break;
	default:break;
	}*/
}