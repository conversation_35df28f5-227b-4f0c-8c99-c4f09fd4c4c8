// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "InteractShopActor.generated.h"

UCLASS()
class THEAWAKENER_FO_API AInteractShopActor : public AActor
{
	GENERATED_BODY()

public:
	// Sets default values for this actor's properties
	AInteractShopActor();

protected:
	// Called when the game starts or when spawned
	virtual void BeginPlay() override;

public:
	// Called every frame
	virtual void Tick(float DeltaTime) override;

	UFUNCTION(BlueprintCallable, BlueprintNativeEvent)
	void ShowUI();

	UFUNCTION(BlueprintCallable, BlueprintNativeEvent)
	void HideUI();

	UFUNCTION(BlueprintCallable, BlueprintNativeEvent)
	void OnInteract();
};
