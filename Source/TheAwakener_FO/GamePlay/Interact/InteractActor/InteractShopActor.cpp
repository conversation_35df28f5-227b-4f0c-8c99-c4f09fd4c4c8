// Fill out your copyright notice in the Description page of Project Settings.


#include "InteractShopActor.h"


// Sets default values
AInteractShopActor::AInteractShopActor()
{
	// Set this actor to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryActorTick.bCanEverTick = true;
}

// Called when the game starts or when spawned
void AInteractShopActor::BeginPlay()
{
	Super::BeginPlay();
	
}

// Called every frame
void AInteractShopActor::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);
}

void AInteractShopActor::OnInteract_Implementation()
{
}

void AInteractShopActor::HideUI_Implementation()
{
}

void AInteractShopActor::ShowUI_Implementation()
{
}

