// Fill out your copyright notice in the Description page of Project Settings.


#include "InteractEventTrigger.h"

#include "TheAwakener_FO/FunctionLibrary/CallFuncLib.h"
#include "TheAwakener_FO/FunctionLibrary/DataFuncLib.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"


// Sets default values
AInteractEventTrigger::AInteractEventTrigger()
{
	PrimaryActorTick.bCanEverTick = true;

	Root = CreateDefaultSubobject<USceneComponent>(TEXT("Root"));
	Root->SetupAttachment(GetRootComponent());
	Root->SetRelativeTransform(FTransform::Identity);
}

void AInteractEventTrigger::BeginPlay()
{
	Super::BeginPlay();
	
	Box = Cast<UBeInteractedBox>(GetComponentByClass(UBeInteractedBox::StaticClass()));

}

void AInteractEventTrigger::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);
	
	if (this->InfinityInteractTimes == false && this->CanInteractTimes <= 0)
	{
		this->CanInteractNow = false;
		if (DestroyWhileInteractEnds) this->Destroy();
	}
	else
	{
		if (this->Cooldown > 0)
		{
			this->Cooldown -= DeltaTime;
			if (this->Cooldown <= 0 && Box)
			{
				if(this->InfinityInteractTimes == true ||  this->CanInteractTimes > 0)
					this->CanInteractNow = true;
			}
		}
		else
			this->CanInteractNow = true;
	}
}


void AInteractEventTrigger::OnBeInteracted()
{
	if (this->CanInteractNow == false) return;
	if (this->InfinityInteractTimes == false && this->CanInteractTimes <= 0) return;

	for (const FString Action : this->InteractActions)
	{
		const FJsonFuncData FuncData = UCallFuncLib::StringToJsonFuncData(Action);
		UFunction* Func = UCallFuncLib::JsonFuncToUFunc(FuncData);
		if (Func)
		{
			struct
			{
				TArray<FString> Params;
			}FuncParam;
			FuncParam.Params = FuncData.Params;
			this->ProcessEvent(Func, &FuncParam);
		}
	}

	if(this->CooldownAfterInteracted > 0)
	{
		this->Cooldown = CooldownAfterInteracted;
		this->CanInteractNow = false;
	}

	OnInteracted();
	UE_LOG(LogTemp, Log, TEXT("Interact Event %i times or %s"),this->CanInteractTimes,this->InfinityInteractTimes?TEXT("Infinite"):TEXT("Times Limit"));
	this->OnInteractedDelegate.Broadcast(this);
}

bool AInteractEventTrigger::CanBeInteractNow()
{
	if (this->CanInteractNow == false) return false;

	for (const FString Condition : this->InteractConditions)
	{
		const FJsonFuncData FuncData = UCallFuncLib::StringToJsonFuncData(Condition);
		UFunction* Func = UCallFuncLib::JsonFuncToUFunc(FuncData);
		if (Func)
		{
			struct
			{
				TArray<FString> Params;
				bool Result;
			}FuncParam;
			FuncParam.Params = FuncData.Params;
			this->ProcessEvent(Func, &FuncParam);
			if (FuncParam.Result == false) return false;
		}
	} 

	return true;
}

void AInteractEventTrigger::OnInteracted_Implementation()
{
}

void AInteractEventTrigger::OnCloseInteract_Implementation()
{
}