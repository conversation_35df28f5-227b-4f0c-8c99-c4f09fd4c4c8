// Fill out your copyright notice in the Description page of Project Settings.


#include "InteractTerrainItem.h"

#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GamePlay/Item/ItemModel.h"
#include "TheAwakener_FO/GamePlay/Item/ItemObj.h"


// Sets default values
AInteractTerrainItem::AInteractTerrainItem()
{
	PrimaryActorTick.bCanEverTick = true;

	Root = CreateDefaultSubobject<USceneComponent>(TEXT("Root"));
	Root->SetupAttachment(GetRootComponent());
	Root->SetRelativeTransform(FTransform::Identity);
}

void AInteractTerrainItem::BeginPlay()
{
	Super::BeginPlay();

	Box = Cast<UBeInteractedBox>(GetComponentByClass(UBeInteractedBox::StaticClass()));
}


void AInteractTerrainItem::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);

	if (this->Cooldown > 0)
	{
		this->Cooldown -= DeltaTime;
		if (this->Cooldown <= 0 && Box)
		{
			this->CanInteractNow = true;
		}
	}
	if (this->InfinityInteractTimes == false && this->CanInteractTimes <= 0)
	{
		this->CanInteractNow = false;
	}
}

bool AInteractTerrainItem::OnBeInteracted()
{
	if (this->CanInteractNow == false) return false;

	const FItemModel GiveItemModel = UGameplayFuncLib::GetDataManager()->GetItemById(this->GatherItemId);
	if (GiveItemModel.Id.IsEmpty()) return false;
	UAwGameInstance::Instance->RoleInfo.ItemObjs.Add(FItemObj(GiveItemModel));

	if (AutoUseOnGatherItem)
	{
		const AAwCharacter* User = UGameplayFuncLib::GetAwGameState()->GetMyCharacter();
		if (User && User->Dead(true) == false)
		{
			if (UAwGameInstance::Instance->RoleInfo.SetUsingItemByModelId(GatherItemId)){
				const FItemObj* ToUse = UAwGameInstance::Instance->RoleInfo.GetUsingItem();
            	if (ToUse)
            	{
            		FItemUseMethod UseWay;
            		switch (AutoUseByMethod)
            		{
            		case EItemUseMethod::Use: UseWay = ToUse->Model.OnUse; break;
            		case EItemUseMethod::Throw: UseWay = ToUse->Model.OnThrow; break;
            		case EItemUseMethod::Enchant: UseWay = ToUse->Model.OnEnchant; break;
            		}
        
            		User->PreorderAction(UseWay.UseActionId);
            		//User->PreorderActionWithCancelCheck(UseWay.UseActionId);
            	}
			}
			
		}
	}

	if (this->CooldownAfterInteracted > 0)
	{
		this->Cooldown = this->CooldownAfterInteracted;
		this->CanInteractNow = false;
	}

	return true;
}