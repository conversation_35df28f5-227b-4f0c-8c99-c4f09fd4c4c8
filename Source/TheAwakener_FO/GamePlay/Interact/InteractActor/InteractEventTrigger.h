// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Components/WidgetComponent.h"
#include "GameFramework/Actor.h"
#include "TheAwakener_FO/GamePlay/Interact/BeInteractedBox.h"
#include "InteractEventTrigger.generated.h"

//interact成功时的动态多播委托
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnInteractedDelegate, AInteractEventTrigger*, InteractedActor);
UCLASS()
class THEAWAKENER_FO_API AInteractEventTrigger : public AActor
{
	GENERATED_BODY()
protected:
	virtual void BeginPlay() override;
private:
	UPROPERTY()
	USceneComponent* Root;
	UPROPERTY()
	UBeInteractedBox* Box;

	
public:
	AInteractEventTrigger();
	virtual void Tick(float DeltaTime) override;

	//可以交互（获得ItemObj）的次数
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int CanInteractTimes = 1;

	//是否可以无限次数交互，如果是，CanInteractTimes将无效
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	bool InfinityInteractTimes = false;

	//可以交互的条件函数，与关系，全部通过才能交互(TArray<FString> Params)=>Bool
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FString> InteractConditions;

	//交互发生的事件函数(TArray<Params>)=>UTimelineNode*
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FString> InteractActions;
	
	//每次交互之后冷却多久（秒）
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	float CooldownAfterInteracted = 0;

	//还剩下多久到下次交互
	UPROPERTY(BlueprintReadOnly)
	float Cooldown = 0;

	//是否可以交互状态
	UPROPERTY(BlueprintReadOnly)
	bool CanInteractNow =  true;

	//交互的优先级有多大，越大越容易被交互
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	float InteractValue = 0.6f;

	//不能交互了就删除自己
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	bool DestroyWhileInteractEnds = true;

	//被交互了
	void OnBeInteracted();

	//当前是否可以被交互
	bool CanBeInteractNow();

	//蓝图回调
	UFUNCTION(BlueprintNativeEvent)
	void OnInteracted();

	//蓝图回调
	UFUNCTION(BlueprintCallable,BlueprintNativeEvent)
	void OnCloseInteract();

	//Switch改变时的动态多播委托
	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnInteractedDelegate OnInteractedDelegate;
};
