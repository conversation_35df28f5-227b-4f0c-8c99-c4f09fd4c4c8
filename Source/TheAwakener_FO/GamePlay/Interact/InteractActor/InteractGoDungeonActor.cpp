// Fill out your copyright notice in the Description page of Project Settings.


#include "InteractGoDungeonActor.h"


// Sets default values
AInteractGoDungeonActor::AInteractGoDungeonActor()
{
	// Set this actor to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryActorTick.bCanEverTick = true;
}

// Called when the game starts or when spawned
void AInteractGoDungeonActor::BeginPlay()
{
	Super::BeginPlay();
	
}

// Called every frame
void AInteractGoDungeonActor::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);
}

void AInteractGoDungeonActor::OnInteract_Implementation()
{
	
}

void AInteractGoDungeonActor::HideUI_Implementation()
{
	
}

void AInteractGoDungeonActor::ShowUI_Implementation()
{
	
}

