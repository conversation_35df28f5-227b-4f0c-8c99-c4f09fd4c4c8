// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Components/WidgetComponent.h"
#include "GameFramework/Actor.h"
#include "TheAwakener_FO/GamePlay/Interact/BeInteractedBox.h"
#include "TheAwakener_FO/GamePlay/Item/ItemObj.h"
#include "InteractTerrainItem.generated.h"

/**
 * 可以用来交互的地形，交互后可以获得一个ItemObj
 */
UCLASS()
class THEAWAKENER_FO_API AInteractTerrainItem : public AActor
{
	GENERATED_BODY()
protected:
	virtual void BeginPlay() override;
private:
	UPROPERTY()
	USceneComponent* Root;
	UPROPERTY()
	UBeInteractedBox* Box;

public:
	AInteractTerrainItem();
	virtual void Tick(float DeltaTime) override;

	//可以交互（获得ItemObj）的次数
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int CanInteractTimes = 1;

	//是否可以无限次数交互，如果是，CanInteractTimes将无效
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	bool InfinityInteractTimes = false;

	//交互之后获得的物品的ItemModel.Id，必须是json表中配置了的东西
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString GatherItemId;

	//交互之后是否自动使用交互获得的东西
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	bool AutoUseOnGatherItem = true;

	//使用这个物品的方法，如果不会自动使用，那这条就没有意义
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	EItemUseMethod AutoUseByMethod = EItemUseMethod::Use;

	//每次交互之后冷却多久（秒）
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	float CooldownAfterInteracted = 0;

	//还剩下多久到下次交互
	UPROPERTY(BlueprintReadOnly)
	float Cooldown = 0;

	//是否可以交互状态
	UPROPERTY(BlueprintReadOnly)
	bool CanInteractNow =  true;

	//被交互了，返回是否交互成功
	bool OnBeInteracted();

};
