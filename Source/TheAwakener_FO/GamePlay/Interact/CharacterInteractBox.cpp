// Fill out your copyright notice in the Description page of Project Settings.


#include "CharacterInteractBox.h"

#include "ShaderPrintParameters.h"
#include "InteractActor/InteractEventTrigger.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"

UCharacterInteractBox::UCharacterInteractBox()
{
	//允许tick总开关 只有构造时调用才能导致 正确注册于UE引擎的FTickManager
	PrimaryComponentTick.bCanEverTick = true;
	PrimaryComponentTick.bStartWithTickEnabled = true;
}

void UCharacterInteractBox::InteractionUpdate(float DeltaTime)
{
	//交互范围内可能目标
	if (InteractInfos.Num() <= 0) return;


	for (TArray<FInteractInfo>::TIterator it(InteractInfos); it; ++it)
	{
		if (it->InOverlap == false)
		{
			if (it->Focusing)
			{
				//移除失效关注目标
				it->FocusLoseInSec -= DeltaTime;
				if (it->FocusLoseInSec <= 0)
				{
					if (it->Target)
					{
						it->Target->OnLoseFocus(GetOwner());
					}
					it.RemoveCurrent();
				}
			}
			else
			{
				if (it->Target)
				{
					it->Target->OnLoseFocus(GetOwner());
				}
				it.RemoveCurrent();
			}
		}
	}


	AutoFocusInteraction(MaxFocusNum);	//筛选出高优先级目标

}

void UCharacterInteractBox::AutoFocusInteraction(int MaxCount)
{
	
	TArray<TTuple<int, float>> Scores;		//每条InteractInfo的评分，<indexOfInteractInfo, 评分>
	TArray<int> OldFocusIndex;
	const FVector ThisLocation = this->GetOwner()->GetActorLocation();
	const FRotator ThisRotate = this->GetOwner()->GetActorRotation();
	for (int i = 0; i < this->InteractInfos.Num(); i++)
	{
		if (InteractInfos[i].Focusing)
		{
			OldFocusIndex.Add(i);
		}
		if (!InteractInfos[i].Target)
			continue;

		if (InteractInfos[i].Target && InteractInfos[i].Target->CurrentActive(this->GetOwner()) == false)
		{
			if (InteractInfos[i].Focusing)
			{
				InteractInfos[i].Target->OnLoseFocus(this->GetOwner());
			}
			continue;
		}

		const float  ThisScore =  InteractInfos[i].Target->InteractScore(ThisLocation, ThisRotate);
		if (ThisScore > 0)
			Scores.Add(TTuple<int, float>(i, ThisScore));
	}

	if (UGameplayFuncLib::GetAwGameState()->GetMyCharacter() == CharacterOwner)
		CharacterOwner->GetCmdComponent()->InCanInteraction = Scores.Num()> 0;

	if (Scores.Num() <= 0) 
	{ OnLoseFocus(); 
	return; }


	//排序得高优先级
	Scores.Sort([](const TTuple<int, float>& V1, const TTuple<int, float>& V2)
		{
			return V1.Value < V2.Value;
		});

	//最多关注多少个交互对象
	const int TargetCount = FMath::Min(Scores.Num(), MaxCount);

	TArray<int> NewFocusIndex;
	
	for (int i = 0; i < TargetCount; i++)
	{
		NewFocusIndex.Add(Scores[i].Key);
	}

	for (int i = 0; i < this->InteractInfos.Num(); i++)
	{
		if (NewFocusIndex.Contains(i))
		{
			if (InteractInfos[i].InOverlap == true)
				InteractInfos[i].FocusLoseInSec = InteractInfos[i].Target ? InteractInfos[i].Target->LoseFocusInSec : 0;

			InteractInfos[i].Focusing = true;

			if (InteractInfos[i].Target)
			{
				OnGetFocus(InteractInfos[i]);
			}
		}
		else if(InteractInfos[i].Focusing)
		{
			InteractInfos[i].Target->OnLoseFocus(GetOwner());
			InteractInfos[i].Focusing = false;
		}
	}

}

void UCharacterInteractBox::AddInteractTarget(FInteractInfo Target)
{
	for (int i = 0; i < this->InteractInfos.Num(); i++)
	{
		if (InteractInfos[i].Target && InteractInfos[i].Target == Target.Target)
		{
			//已经存在，刷新一下
			InteractInfos[i].InOverlap = true;
			return;
		}
	}
	InteractInfos.Add(Target);

	if (!IsComponentTickEnabled()&& InteractInfos.Num()>0)
	{
		SetComponentTickEnabled(true);
	}

}

void UCharacterInteractBox::RemoveInteractTarget(FInteractInfo Target)
{
	for (int i = 0; i < this->InteractInfos.Num(); i++)
	{
		if (InteractInfos[i].Target && InteractInfos[i].Target == Target.Target)
		{
			InteractInfos[i].InOverlap = false;
			break;
		}
	}

}



void UCharacterInteractBox::InteractWithAllFocusTargets()
{
	//延续前人逻辑 但是实际执行应该根据类别 执行不同数量 且应按类别先分类排序一遍 但现在目标只有一个所以没区别
	AActor* ThisActor = Cast<AActor>(GetOwner());
	for (int i = 0; i < InteractInfos.Num(); i++)
	{
		if (InteractInfos[i].Focusing == true)
		{
			if (!InteractInfos[i].Target) continue;

			if (InteractInfos[i].Target) {
				InteractInfos[i].Target->Interact(ThisActor);

				UUserWidget* InteractWidget = CharacterOwner->InteractWidget->GetWidget();
				if(InteractWidget)
				{
					if (InteractWidget->Implements<UInteractWidgetInterface>())
					{
						IInteractWidgetInterface::Execute_OnInteractWidgetActive(InteractWidget);
					}
				}
			}
			if (InteractInfos[i].Target->LoseFocusOnInteract)
			{
				OnLoseFocus();
				InteractInfos[i].Focusing = false;
			}

		}
	}
}

void UCharacterInteractBox::TerminateInteract()
{
	if (InteractInfos.Num() <= 0) return;
	OnLoseFocus();

}

void UCharacterInteractBox::BeginPlay()
{
	Super::BeginPlay();

	OnComponentBeginOverlap.AddDynamic(this, &UCharacterInteractBox::OnOverlapBegin);
	OnComponentEndOverlap.AddDynamic(this, &UCharacterInteractBox::OnOverlapEnd);

	//除构造外 重新开启tick总开关并注册自身tick相关function 于引擎Tick的方法 注:是SetComponentTickEnable的更底层
	/*
	PrimaryComponentTick.Target = this;
	PrimaryComponentTick.bCanEverTick = true;
	PrimaryComponentTick.RegisterTickFunction(GetComponentLevel());
	PrimaryComponentTick.SetTickFunctionEnable(true);
	*/
	SetComponentTickEnabled(false);

	CharacterOwner = Cast<AAwCharacter>(this->GetOwner());

}

void UCharacterInteractBox::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
	Super::TickComponent(DeltaTime, TickType, ThisTickFunction);

	if (!CheckAddtionalCondition())
	{
		return;
	}

	if (InteractInfos.Num() <= 0)
	{
		OnLoseFocus();
		SetComponentTickEnabled(false);
		return;
	}
	InteractionUpdate(DeltaTime);
	
}

	
void UCharacterInteractBox::OnOverlapBegin(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp,
	int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult)
{
	UBeInteractedBox* Target = Cast<UBeInteractedBox>(OtherComp);
	if (!Target) return;
	
	if (IgnoreInteractTypes.Contains(Target->InteractType))
	{
		return;
	}

	if (!CharacterOwner) return;
	if (Target->InteractType == EInteractTargetType::TouchGetLoot)
	{
		TouchLootTargets.Add(Target);
		return;
	}
	
	AddInteractTarget(FInteractInfo(Target));
}

void UCharacterInteractBox::OnOverlapEnd(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex)
{
	UBeInteractedBox* Target = Cast<UBeInteractedBox>(OtherComp);
	if (!Target) return;
	
	if (!CharacterOwner) return;
	
	RemoveInteractTarget(FInteractInfo(Target));
}

void UCharacterInteractBox::OnGetFocus(FInteractInfo TargetInfo)
{
	if (!TargetInfo.Target)
	{
		UE_LOG(LogTemp, Error, TEXT("Interact Focus On InVaild Object"));
		return;
	}
    if(!CharacterOwner)
    {
    	UE_LOG(LogTemp, Error, TEXT("Interact Initiator must be a Vaild AAwcharacter"));
	    return;;
    }

	if (!CharacterOwner->InteractWidget)
	{
		UE_LOG(LogTemp, Error, TEXT("AAwcharacter has no interact widgetcomponent"));
		return;
	}
	
	const TSubclassOf<UUserWidget> CurWidgetClass= CharacterOwner->InteractWidget?CharacterOwner->InteractWidget->GetWidgetClass():nullptr;
	TSubclassOf<UUserWidget> NewWidgetClass = nullptr;
	if (InteractWidgetMap.Find(TargetInfo.Target->InteractType))
	{
		NewWidgetClass = *InteractWidgetMap.Find(TargetInfo.Target->InteractType);
	}
	else
	{
		return;
	}

	if (CurWidgetClass!=NewWidgetClass&&IsValid(NewWidgetClass))
	{
		//内置GC
		CharacterOwner->InteractWidget->SetCurWidget(TargetInfo.Target->InteractType, NewWidgetClass,CharacterOwner->OwnerPlayerController);
	}

	UUserWidget* InteractWidget = CharacterOwner->InteractWidget->GetWidget();

	if (!InteractWidget)
	{
		return;
	}
	
	FString InteractWidgetText = TargetInfo.Target->SpecialInteractWidgetText;

	if (InteractWidget->Implements<UInteractWidgetInterface>())
	{
		
		TMap<FString, FString>Params = TargetInfo.Target->ExtraInteractWidgetParams;
		IInteractWidgetInterface::Execute_SetInteractWidgetText(InteractWidget, InteractWidgetText);
		IInteractWidgetInterface::Execute_SetInteractWidgetExtraSource(InteractWidget,Params );
	}

	TargetInfo.Target->OnGetFocus(GetOwner());

	CharacterOwner->InteractWidgetVisible(true);

	UActionComponent* ActionComponent = CharacterOwner->GetActionComponent();
	ActionComponent->AddActionById("PickUp");
}

void UCharacterInteractBox::OnLoseFocus()
{
	if (!CharacterOwner)
	{
		return;
	}
	CharacterOwner->InteractWidgetVisible(false);

	UActionComponent* ActionComponent = CharacterOwner->GetActionComponent();
	ActionComponent->RemoveActionById("PickUp");
}

TArray<EInteractTargetType> UCharacterInteractBox::HasSomethingToInteract()
{
	TArray<EInteractTargetType> Res;
	if (!CharacterOwner)
	{
		return Res;
	}
	if (!CharacterOwner->IsPlayerCharacter()) return Res;

	for (const FInteractInfo Info : this->InteractInfos)
		if (Info.Focusing == true && Info.Target && Res.Contains(Info.Target->InteractType) == false)
		{
			Res.Add(Info.Target->InteractType);
		}
	return Res;
}

void UCharacterInteractBox::CheckLootArrive()
{
	for(auto loot : TouchLootTargets)
	{
		if (!IsValid(loot))continue;
		auto Loot = Cast<AInteractEventTrigger>(loot->GetOwner());
			
		float squareDist = FVector::DistSquared2D(loot->GetComponentLocation(),GetOwner()->GetActorLocation()); 
		if (squareDist<LootArriveTolerant)
		{
			if (Loot) Loot->OnInteracted();
		}
	}
	TouchLootTargets.RemoveAll([](auto Element)
	{
		return !IsValid(Element);
	});
}

bool UCharacterInteractBox::CheckAddtionalCondition()
{
	bool result =  true;
	for (auto Condition : AdditionalShowConditions)
	{
		UFunction* ConditionFunc = UCallFuncLib::GetUFunction(Condition.ClassPath, Condition.FunctionName);
		if (ConditionFunc)
		{
			struct
			{
				UActorComponent* CharacterInteractComponent;
				TArray<FString> Params;
				bool Result;
			}ConditionFuncParam;
			ConditionFuncParam.CharacterInteractComponent = this;
			ConditionFuncParam.Params = Condition.Params;
			this->ProcessEvent(ConditionFunc, &ConditionFuncParam);
			if (!ConditionFuncParam.Result)
			{
				result = false;
			}
		}
	}
	CharacterOwner->InteractWidgetVisible(result);
	return result;
}
