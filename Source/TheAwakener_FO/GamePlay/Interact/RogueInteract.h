// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "BeInteractedBox.h"
#include "GameFramework/Actor.h"
#include "RogueInteract.generated.h"

UCLASS()
class THEAWAKENER_FO_API ARogueInteract : public AActor
{
	GENERATED_BODY()

public:
	// Sets default values for this actor's properties
	ARogueInteract();

protected:
	// Called when the game starts or when spawned
	virtual void BeginPlay() override;

public:
	// Called every frame
	virtual void Tick(float DeltaTime) override;
	
	void Interact(EInteractTargetType Type);
};
