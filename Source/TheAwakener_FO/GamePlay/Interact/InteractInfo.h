// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "BeInteractedBox.h"
#include "InteractInfo.generated.h"

/**
 * 正在交互中的信息记录
 */
USTRUCT(BlueprintType)
struct FInteractInfo
{
	GENERATED_BODY()
public:
	//交互的是谁
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	UBeInteractedBox* Target = nullptr;

	//还剩多久移除掉，Focus==false之后会逐渐减少
	UPROPERTY()
	float FocusLoseInSec = 1.0f;

	//是否正在被Focus
	UPROPERTY()
	bool Focusing = false;

	//是否还在InteractBox的包围之下，不在包围之下并且也没有Focus就完蛋了
	UPROPERTY()
	bool InOverlap = true;

	FInteractInfo(){};
	FInteractInfo(UBeInteractedBox* InteractTarget):
		Target(InteractTarget), FocusLoseInSec(InteractTarget ? InteractTarget->LoseFocusInSec : 0){};
};
