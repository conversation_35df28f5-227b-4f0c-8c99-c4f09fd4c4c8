// Fill out your copyright notice in the Description page of Project Settings.


#include "RogueInteract.h"

#include "BeInteractedBox.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"


// Sets default values
ARogueInteract::ARogueInteract()
{
	// Set this actor to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryActorTick.bCanEverTick = true;
}

// Called when the game starts or when spawned
void ARogueInteract::BeginPlay()
{
	Super::BeginPlay();
	
}

// Called every frame
void ARogueInteract::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);
}

void ARogueInteract::Interact(EInteractTargetType Type)
{
	FString ShowUIName = "";
	switch (Type)
	{
	case EInteractTargetType::Loot: break;
	case EInteractTargetType::Revive: break;
	case EInteractTargetType::Dialog: break;
	case EInteractTargetType::Shop: break;
	case EInteractTargetType::ItemOnTerrain: break;
	case EInteractTargetType::GoDungeon: break;
	case EInteractTargetType::ChangeClass: break;
	case EInteractTargetType::ChangeSkill: break;
		
	case EInteractTargetType::RogueChangePawn: ShowUIName = "RogueCareer_Main"; break;
	case EInteractTargetType::RogueSkillSelection: ShowUIName = "RogueSkillSelection"; break;
	case EInteractTargetType::RogueChangeAwakeSkill: ShowUIName = "AwakSkill_Main"; break;
	case EInteractTargetType::RogueTalent: ShowUIName = "RogueTalent"; break;
	case EInteractTargetType::RogueSetStakePawn: ShowUIName = "RogueSetStakePawn"; break;
	case EInteractTargetType::RogueBattleUpgradePreview: ShowUIName = "RogueBattleUpgradePreview"; break;
	case EInteractTargetType::RogueManual: ShowUIName = "RogueManual"; break;
	case EInteractTargetType::RogueChangeWeapon: ShowUIName = "Rogue_ChangeWeapon"; break;
	case EInteractTargetType::RogueChangeSkin: ShowUIName = "Rogue_ChangeSkin"; break;
	case EInteractTargetType::TapGetLoot: ShowUIName = "Interact_Take"; break;
	case EInteractTargetType::EventTrigger: break;
	default: ;
	}

	if (ShowUIName == "InteractChangePawn") return;

	if (UGameplayFuncLib::GetUiManager())
		UGameplayFuncLib::GetUiManager()->Show(ShowUIName);
}

