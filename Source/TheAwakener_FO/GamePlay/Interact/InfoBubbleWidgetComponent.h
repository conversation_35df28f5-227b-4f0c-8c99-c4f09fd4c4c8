// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/Interface.h"
#include "Components/WidgetComponent.h"
#include "Components/SphereComponent.h"
#include "TheAwakener_FO/FunctionLibrary/DataFuncLib.h"
#include "InfoBubbleWidgetComponent.generated.h"

class AAwCharacter;

USTRUCT(BlueprintType)
struct FInfoWidgetStateCheckData
{
	GENERATED_BODY()
public:
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		FJsonFuncData ConditionFunc;
		UPROPERTY(BlueprintReadWrite, EditAnywhere)
		FString StateString;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		TMap<FString, FString> ExParams;
};


UINTERFACE(MinimalAPI)
class UInfoWidgetInterface : public UInterface
{
	GENERATED_BODY()
};

class THEAWAKENER_FO_API IInfoWidgetInterface
{
	GENERATED_BODY()

		// Add interface functions to this class. This is the class that will be inherited to implement this interface.
public:
	UFUNCTION(BlueprintCallable, BlueprintNativeEvent)
		void SetInfoWidgetOwner( AActor* Owner);
	UFUNCTION(BlueprintCallable, BlueprintNativeEvent)
		void SetInfoWidgetInfo(const FString &TextString, const TMap<FString, FString>& ExParams);
	UFUNCTION(BlueprintCallable, BlueprintNativeEvent)
		void ChangeInfoWidgetState(const FString& TextString,const TMap<FString,FString>& ExParams);
};

UCLASS(ClassGroup = "InfoWidget", hidecategories = (Object, LOD, Lighting, TextureStreaming),
	meta = (DisplayName = "InfoWidgetSphere", BlueprintSpawnableComponent))
	class THEAWAKENER_FO_API UInfoWidgetSphere : public USphereComponent
{
	GENERATED_BODY()
public:
	UInfoWidgetSphere();
};



UCLASS(Blueprintable, ClassGroup = "InfoWidget", meta = (BlueprintSpawnableComponent))
class THEAWAKENER_FO_API UInfoBubbleWidgetComponent : public UWidgetComponent
{
	GENERATED_BODY()
public:
	UInfoBubbleWidgetComponent();

	virtual void BeginPlay()override;
	virtual void TickComponent(float DeltaTime, enum ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

	UFUNCTION()
		void TryCatchPlayer(UPrimitiveComponent* OverlappedComponent, AActor* Actor, UPrimitiveComponent* Component, int BodyIndex, bool Sweep, const FHitResult& SweepResult);
	//暂停组件供外部统一调用
	UFUNCTION(BlueprintCallable, Category = InfoBubble)
		void SetComponentPaused(bool IsPaused);
public:
	UPROPERTY(BlueprintReadWrite, EditAnyWhere, Category = InfoBubble)
		float HideWidgetDistance = 2000;
	UPROPERTY(BlueprintReadWrite, EditAnyWhere, Category = InfoBubble)
		FString DefalutWidgetText;
	UPROPERTY(BlueprintReadWrite, EditAnyWhere, Category = InfoBubble)
		TMap<FString, FString> DefalutWidgetInfo;
	UPROPERTY(BlueprintReadWrite, EditAnyWhere, Category = InfoBubble)
		TArray<FInfoWidgetStateCheckData> StateConditions;

	UPROPERTY()
	UInfoWidgetSphere* TriggerRangeComp = nullptr;

	UPROPERTY()
	AAwCharacter* CatchedPlayer = nullptr;
private:
	void InitWidgetInfo();

	bool CheckPlayerInRange();

	void CheckAddtionalCondition();

	void BindEventToTrigger();



	bool Paused = false;
};


