// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Components/CapsuleComponent.h"
#include "BeInteractedBox.generated.h"

class AAwCharacter;

/**
 * 可交互对象的类型
 * 因为这个需要支持蓝图更简单的配置，所以不用接口实现，组成枚举，也是因为它本身类型应该是严肃的
 */
UENUM(BlueprintType)
enum class EInteractTargetType : uint8
{
	// AThingPackageActor 掉落物
	Loot,
	// AwCharacter，复活这个角色
	Revive,
	// AwCharacter(?), 和这个角色交谈
	Dialog,
	// 打开商店UI，仅此而已了，当然，我们还是需要增加一个参数的，比如商店的id
	Shop,
	// 可交互地形物件（比如火把）
	ItemOnTerrain,
	// 进入地牢
	GoDungeon,
	// 改变玩家职业
	ChangeClass,
	// 改变玩家技能
	ChangeSkill,

	// Rogue切换Pawn
	RogueChangePawn,
	// Rogue切换技能
	RogueSkillSelection,
	// Rogue切换觉醒技能
	RogueChangeAwakeSkill,
	// Rogue天赋
	RogueTalent,
	// Rogue修改木桩
	RogueSetStakePawn,
	// Rogue修改战斗预览
	RogueBattleUpgradePreview,
	//Rogue图鉴
	RogueManual,
	//Rogue换武器
	RogueChangeWeapon,
	//Rogue换皮肤
	RogueChangeSkin,
	//交互事件触发器（这个并不推荐，但是他确实能解决很多问题）
	EventTrigger,
	//触碰拾取的掉落物
	TouchGetLoot,
	//点击拾取的掉落物
	TapGetLoot,
};

/**
 *  被交互的盒子，当CharacterInteractBox碰上这个，才会有了交互的可能性
 */
UCLASS(ClassGroup="Collision", editinlinenew, hidecategories=(Object,LOD,Lighting,TextureStreaming), meta=(DisplayName="Be Interact Capsule", BlueprintSpawnableComponent))
class THEAWAKENER_FO_API UBeInteractedBox : public UCapsuleComponent
{
	GENERATED_BODY()
public:
	/**
	 * 交互的类型，或者说交互方式，这会决定交互的类以及调用的函数
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	EInteractTargetType InteractType;

	// 头上显示的特殊文本，（比如电梯把手Trigger叫交互或者叫使用）
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString SpecialInteractWidgetText;

	// 传入UMG的额外参数
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TMap<FString,FString> ExtraInteractWidgetParams;
	
	/**
	 * 成为焦点之后在离开overlap之后多少秒内会脱离焦点
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	float LoseFocusInSec = 0.2f;

	/**
	 * 开始交互就是去焦点，至少掉落物得这样，其实大多东西都如此
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	bool LoseFocusOnInteract = true;

	/**
	 * 参数，如果有必要的话
	 * Shop就是ShopId，毕竟打开商店需要传一个ShopInfo，也就是FTrading，就拿这个FTrading的Id来（放在[0]）吧
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FString> Param;
	
	/**
	 * 当前是否被激活中，比如被pickup的掉落物就是返回false的
	 * @param InteractPromoter 执行交互的对象，一些可交互的内容也许需要一个交互对象才能得知是否可以交互，比如掉落物（AThingPackageActor）需要一个谁拾取的（AwCharacter）
	 * @return 返回是不是激活中，不是的话就得丢失焦点，并且不可选择为焦点
	 */
	bool CurrentActive(AActor* InteractPromoter = nullptr) const;

	//返回一个拾取评分，评分越高越容易成为交互目标
	float InteractScore(FVector PromoterLocation, FRotator PromoterRotation) const;

	/**
	 *执行交互行为
	* @param InteractPromoter 执行交互的对象，一些可交互的内容也许需要一个交互对象才能得知是否可以交互，比如掉落物（AThingPackageActor）需要一个谁拾取的（AwCharacter）
	* @return 返回成没成
	 */
	bool Interact(AActor* InteractPromoter);

	/**
	 * 当被选定为交互焦点的时候
	 * @param InteractPromoter 执行交互的对象，一些可交互的内容也许需要一个交互对象才能得知是否可以交互，比如掉落物（AThingPackageActor）需要一个谁拾取的（AwCharacter）
	 */
	void OnGetFocus(AActor* InteractPromoter);

	/**
	 * 当被剥夺交互焦点的时候
	 * @param InteractPromoter 执行交互的对象，一些可交互的内容也许需要一个交互对象才能得知是否可以交互，比如掉落物（AThingPackageActor）需要一个谁拾取的（AwCharacter）
	 */
	void OnLoseFocus(AActor* InteractPromoter);

	UFUNCTION(BlueprintImplementableEvent)
	void OnGetFocusFunc();
	UFUNCTION(BlueprintImplementableEvent)
	void OnLoseFocusFunc();
};
