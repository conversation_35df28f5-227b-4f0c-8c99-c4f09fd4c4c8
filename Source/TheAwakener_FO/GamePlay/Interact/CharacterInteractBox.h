// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "InteractInfo.h"
#include "Components/SphereComponent.h"
#include "TheAwakener_FO/FunctionLibrary/DataFuncLib.h"
#include "CharacterInteractBox.generated.h"

/**
 * 角色交互捕捉框，这个是捕捉对方的框，有这个框的角色才能捕获其他单位决定是否有交互
 * 这个仅适用于AAwCharacter
 */
//DECLARE_STATS_GROUP(TEXT("STATGROUP_InteractBox"), STATGROUP_InteractBox, STATCAT_Test);
//DECLARE_CYCLE_STAT(TEXT("InteractBox_Tick"), STAT_InteractBoxTick, STATGROUP_InteractBox);


UCLASS(ClassGroup="Collision",BlueprintType,Blueprintable, meta=(DisplayName="Character Interact Catcher", BlueprintSpawnableComponent))
class THEAWAKENER_FO_API UCharacterInteractBox : public USphereComponent
{
	GENERATED_BODY()
public:

	UCharacterInteractBox();

	UPROPERTY()
	AAwCharacter* CharacterOwner;
	//不抓取的类别，不在这个列表里的BeInteractBox都会被抓
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<EInteractTargetType> IgnoreInteractTypes;

	//交互范围内且符合可交互枚举集合的交互对象信息
	UPROPERTY()
	TArray<FInteractInfo> InteractInfos;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TMap<EInteractTargetType, TSubclassOf<UUserWidget>>InteractWidgetMap;

	//交互范围内且符合可交互枚举集合的交互对象信息
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
		TArray<FJsonFuncData> AdditionalShowConditions;
	//最可能保持选中这么多个，TODO：这个规则可能会改变，
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
		int MaxFocusNum = 1;

	//更新当前交互目标
	void InteractionUpdate(float DeltaTime);
	//选择最高优先级目标作为当前目标
	void AutoFocusInteraction(int MaxCount);
	/**
	* 添加一个东西到交互对象列表里
	*/
	UFUNCTION()
		void AddInteractTarget(FInteractInfo Target);

	/**
	 * 尝试删除一个InteractInfo
	 */
	UFUNCTION()
		void RemoveInteractTarget(FInteractInfo Target);

	/**
	 * 立即和选中的所有Interact对象交互
	 */
	UFUNCTION()
		void InteractWithAllFocusTargets();
	//强制终止交互
	void TerminateInteract();

	virtual void BeginPlay() override;
	virtual void TickComponent(float DeltaTime, enum ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)override;

    
	UFUNCTION(BlueprintCallable)
	void OnOverlapBegin(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp,
		int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult);

	UFUNCTION(BlueprintCallable)
	void OnOverlapEnd(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex);

	/**
	* 找到交互焦点
	*/
	void OnGetFocus(FInteractInfo TargetInfo);

	/**
	 * 当失去交互焦点的时候
	 */
	void OnLoseFocus();

	/**
	* 是否有可以交互的东西
	* 返回可以交互的东西的交互类型集合
	*/
	TArray<EInteractTargetType> HasSomethingToInteract();
	UFUNCTION(BlueprintCallable)
	void CheckLootArrive();
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	float LootArriveTolerant = 5000;
private:
	bool CheckAddtionalCondition();
	TArray<UBeInteractedBox*> TouchLootTargets;
};
