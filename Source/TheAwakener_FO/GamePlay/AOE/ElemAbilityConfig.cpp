// The copyright of this project belongs to Shanghai Minghuan Software Technology Co., Ltd


#include "ElemAbilityConfig.h"

#include "TheAwakener_FO/FunctionLibrary/DataFuncLib.h"

FElemAbilityConfig FElemAbilityConfig::FromJson(TSharedPtr<FJsonObject> JsonObj)
{
	FElemAbilityConfig Result;
	Result.DamageMultipliers = TArray<float>{0,1,2,3};
	if(JsonObj-><PERSON><PERSON><PERSON>("Level1"))
	{
		Result.DamageMultipliers[0] = UDataFuncLib::AwGetNumberField(JsonObj, "Level1",1.0f);
	}
	if(JsonObj-><PERSON><PERSON><PERSON>("Level2"))
	{
		Result.DamageMultipliers[1] = UDataFuncLib::AwGetNumberField(JsonObj, "Level2",1.0f);
	}
	if(JsonObj-><PERSON><PERSON><PERSON>("Level3"))
	{
		Result.DamageMultipliers[2] = UDataFuncLib::AwGetNumberField(JsonObj, "Level3",1.0f);
	}
	if(JsonObj-><PERSON><PERSON><PERSON>("Level4"))
	{
		Result.DamageMultipliers[3] = UDataFuncLib::AwGetNumberField(JsonObj, "Level4",1.0f);
	}
	return Result;
}
