// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "AWAoeBase.h"
#include "AOELauncher.generated.h"

/**
 * 
 */
USTRUCT(BlueprintType)
struct THEAWAKENER_FO_API FAOELauncher
{
	GENERATED_BODY()
public:
	UPROPERTY(BlueprintReadWrite, meta = (ExposeOnSpawn = "true"))
	FAOEModel Model;
	UPROPERTY(BlueprintReadWrite, meta = (ExposeOnSpawn = "true"))
	FVector Position = FVector::ZeroVector;
	UPROPERTY(BlueprintReadWrite, meta = (ExposeOnSpawn = "true"))
	FVector Direction = FVector::ZeroVector;
	UPROPERTY(BlueprintReadWrite, meta = (ExposeOnSpawn = "true"))
	float AOELifeSpan;
	//FTween Tween;
	UPROPERTY(BlueprintReadWrite, meta = (ExposeOnSpawn = "true"))
	FJsonFuncData TweenFunc;

	UPROPERTY(BlueprintReadWrite, meta = (ExposeOnSpawn = "true"))
	AAwCharacter* Caster;
	UPROPERTY(BlueprintReadWrite, meta = (ExposeOnSpawn = "true"))
	FChaProp CasterProp;

	FAOELauncher() :
		Model(FAOEModel()), Position(FVector::ZeroVector), Direction(FVector::ZeroVector), AOELifeSpan(0), TweenFunc(FJsonFuncData()),
		Caster(nullptr)
	{
	};

	FAOELauncher(AAwCharacter* AOECaster, FAOEModel AOEModel, FVector Pos, FVector Dir, float LifeSpan, FString TweenFuncName)
	{
		Caster = AOECaster;
		CasterProp = Caster ? Caster->CharacterObj.CurProperty : FChaProp();
		Model = AOEModel;
		Position = Pos;
		Direction = Dir;
		AOELifeSpan = LifeSpan;
		TweenFunc = UDataFuncLib::SplitFuncNameAndParams(TweenFuncName);
	}

};

