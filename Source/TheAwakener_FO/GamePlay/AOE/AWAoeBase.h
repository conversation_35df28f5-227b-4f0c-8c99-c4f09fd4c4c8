// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "TheAwakener_FO/FunctionLibrary/DataFuncLib.h"
#include "TheAwakener_FO/GamePlay/Characters/HitBox/ActorCatcher.h"
#include "AWAoeBase.generated.h"

USTRUCT(BlueprintType)
struct FAOEModel
{
	GENERATED_BODY()
public:
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString Id = "";
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString BpPath = "";
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FString> Tags;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float TickIntervalTime = 0;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float AOELifeSpan = 0;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FJsonFuncData> OnCreate;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FJsonFuncData> OnTick;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FJsonFuncData> OnRemoved;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FJsonFuncData> OnCharacterEnter;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FJsonFuncData> OnCharacterLeave;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FJsonFuncData> OnActorEnter;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FJsonFuncData> OnActorLeave;
	

	static FAOEModel FromJson(TSharedPtr<FJsonObject> JsonObj)
	{
		FAOEModel Model = FAOEModel();
		Model.Id = UDataFuncLib::AwGetStringField(JsonObj, "Id");

		Model.BpPath = UDataFuncLib::AwGetStringField(JsonObj, "BpPath");
		
		for (const TSharedPtr<FJsonValue> Value : UDataFuncLib::AwGetArrayField(JsonObj, "Tag"))
			Model.Tags.Add(Value->AsString());

		Model.TickIntervalTime = UDataFuncLib::AwGetNumberField(JsonObj, "TickTime", 0.0f);

		Model.AOELifeSpan = UDataFuncLib::AwGetNumberField(JsonObj, "AOELifeSpan", 0.0f);
		
		for (const TSharedPtr<FJsonValue> Value : UDataFuncLib::AwGetArrayField(JsonObj, "OnCreate"))
			Model.OnCreate.Add(UDataFuncLib::SplitFuncNameAndParams(Value->AsString()));
		
		for (const TSharedPtr<FJsonValue> Value : UDataFuncLib::AwGetArrayField(JsonObj, "OnTick"))
			Model.OnTick.Add(UDataFuncLib::SplitFuncNameAndParams(Value->AsString()));
		
		for (const TSharedPtr<FJsonValue> Value : UDataFuncLib::AwGetArrayField(JsonObj, "OnRemoved"))
			Model.OnRemoved.Add(UDataFuncLib::SplitFuncNameAndParams(Value->AsString()));
		
		for (const TSharedPtr<FJsonValue> Value : UDataFuncLib::AwGetArrayField(JsonObj, "OnCharacterEnter"))
			Model.OnCharacterEnter.Add(UDataFuncLib::SplitFuncNameAndParams(Value->AsString()));
		
		for (const TSharedPtr<FJsonValue> Value : UDataFuncLib::AwGetArrayField(JsonObj, "OnCharacterLeave"))
			Model.OnCharacterLeave.Add(UDataFuncLib::SplitFuncNameAndParams(Value->AsString()));
		
		for (const TSharedPtr<FJsonValue> Value : UDataFuncLib::AwGetArrayField(JsonObj, "OnActorEnter"))
			Model.OnActorEnter.Add(UDataFuncLib::SplitFuncNameAndParams(Value->AsString()));
		
		for (const TSharedPtr<FJsonValue> Value : UDataFuncLib::AwGetArrayField(JsonObj, "OnActorLeave"))
			Model.OnActorLeave.Add(UDataFuncLib::SplitFuncNameAndParams(Value->AsString()));
	
		return Model;
	};
};

UCLASS(Blueprintable)
class THEAWAKENER_FO_API AAWAoe : public AActor
{
	GENERATED_BODY()
	
public:	
	// Sets default values for this actor's properties
	AAWAoe();

public:
	UPROPERTY(BlueprintReadWrite,EditAnywhere, meta = (ExposeOnSpawn = "true"))
	FAOEModel Model;

	//AOE生命周期长度
	UPROPERTY(BlueprintReadWrite)
	float AOELifeSpan;
	//AOE已运行的时间
	UPROPERTY(BlueprintReadOnly)
	float LivedTime;
	UPROPERTY(BlueprintReadOnly)
	FTransform OrgTransform;
	//获取AOE剩余时间
	UFUNCTION(BlueprintCallable,BlueprintPure)
	float GetAOEDurationTime() { return AOELifeSpan > 0 ? AOELifeSpan - LivedTime : 0; }

	//AOE已经的执行次数
	UPROPERTY(BlueprintReadOnly)
	int CurTickedNum;

	//通过ClassPath和FuncName，在调用时查找相应的UFunction
	FJsonFuncData TweenFunc;
	FString TweenFuncClassPath;
	FString TweenFuncName;

	UFUNCTION(BlueprintCallable)
	void SetTweenFunc(FJsonFuncData NewTween ){TweenFunc = NewTween;}
	//一些策划用于逻辑的参数
	UPROPERTY(BlueprintReadWrite)
	TMap<FString, FString> Param;

	UPROPERTY(BlueprintReadWrite, meta = (ExposeOnSpawn = "true"))
	AAwCharacter* Caster;

	UPROPERTY(BlueprintReadWrite, meta = (ExposeOnSpawn = "true"))
	FChaProp CasterProp;

	UPROPERTY(VisibleAnywhere, BlueprintReadWrite)
	class USceneComponent* SceneComponent;

	UPROPERTY(VisibleAnywhere, BlueprintReadWrite)
	class UAttackHitManager* AttackHitManager;

	UPROPERTY(VisibleAnywhere, BlueprintReadWrite)
	class UStaticMeshComponent* CatchMesh;

	UPROPERTY(VisibleAnywhere, BlueprintReadWrite)
	class UStaticMeshComponent* ShowMesh;

	//暂时先预设5个VFX
	UPROPERTY(VisibleAnywhere, BlueprintReadWrite)
		UParticleSystemComponent* VFX01;
	UPROPERTY(VisibleAnywhere, BlueprintReadWrite)
		UParticleSystemComponent* VFX02;
	UPROPERTY(VisibleAnywhere, BlueprintReadWrite)
		UParticleSystemComponent* VFX03;
	UPROPERTY(VisibleAnywhere, BlueprintReadWrite)
		UParticleSystemComponent* VFX04;
	UPROPERTY(VisibleAnywhere, BlueprintReadWrite)
		UParticleSystemComponent* VFX05;

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	UParticleSystem* HitVFX;

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	USoundBase* HitSFX;
	
    //Action技能等级，用于快速实现不同等级不同伤害
    UPROPERTY(BlueprintReadWrite, meta = (ExposeOnSpawn = "true"))
    int ActionLevel = 3;
	
private:
	//距离上次Tick的时间
	float LastTickDuration;
	//创建坐标
	FVector OriginLocation;
	//创建方向
	FVector OriginDirection;
	//是否暂停
	bool bPause;
	//是否执行OnTick
	bool bPauseTick;

protected:
	// Called when the game starts or when spawned
	virtual void BeginPlay() override;

public:	
	//初始化函数
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|AOE")
	void Init(FAOEModel InModel, AAwCharacter* InCaster, FChaProp InCasterProp);
	// Called every frame
	virtual void Tick(float DeltaTime) override;

	virtual void Destroyed() override;

	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|AOE")
	void Pause(bool PauseTick = false);

	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|AOE")
	void Resume();

	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|AOE")
	TArray<FBeCaughtActorInfo> CharacterInRange(bool IncludeEnterCha = true, bool IncludeLeavingCha = true);

	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|AOE")
	TArray<FBeCaughtActorInfo> GetEnterCharacters();

	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|AOE")
	TArray<FBeCaughtActorInfo> GetCaughtCharacters(bool IncludeEnterCha = true, bool IncludeLeavingCha = true);

	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|AOE")
	TArray<FVector> GetVectors(TArray<FBeCaughtActorInfo> Targets, bool IgnoreDead = true, bool FriendlyFire = false,bool Repeat = false, int Num = -1);
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|AOE")
	TArray<FBeCaughtActorInfo> GetCaughtCharactersSortByLoc(bool IncludeEnterCha = true, bool IncludeLeavingCha = true);
	
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|AOE")
	TArray<AAwCharacter*> GetLeavingCharacters();

	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|AOE")
	TArray<FBeCaughtActorInfo> GetEnterActors();

	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|AOE")
	TArray<FBeCaughtActorInfo> GetCatchedActors();

	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|AOE")
	TArray<AActor*> GetLeavingActors();

	//蓝图回调
	UFUNCTION(BlueprintImplementableEvent, Category = "AOE")
	void OnAOETick();

	//蓝图回调
	UFUNCTION(BlueprintImplementableEvent, Category = "AOE")
	void OnCharacterEnter();
	
	UFUNCTION(BlueprintCallable,BlueprintPure, Category = "AOE")
	bool HaveEnemy(TArray<FBeCaughtActorInfo> Caughts);
	
	//蓝图回调
	UFUNCTION(BlueprintImplementableEvent, Category = "AOE")
	void OnActorEnter();

	//蓝图回调
	UFUNCTION(BlueprintImplementableEvent, Category = "AOE")
	void OnCharacterLeave();

	//蓝图回调
	UFUNCTION(BlueprintImplementableEvent, Category = "AOE")
	void OnActorLeave();

};