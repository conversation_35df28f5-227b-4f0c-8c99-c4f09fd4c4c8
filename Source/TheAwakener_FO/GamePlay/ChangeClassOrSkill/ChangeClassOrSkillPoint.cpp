// Fill out your copyright notice in the Description page of Project Settings.


#include "ChangeClassOrSkillPoint.h"

#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/UI/GameMain/ChangeClass.h"
#include "TheAwakener_FO/UI/GameMain/ChangeSkill.h"


// Sets default values
AChangeClassOrSkillPoint::AChangeClassOrSkillPoint()
{
	// Set this actor to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryActorTick.bCanEverTick = true;

	InteractionType = EInteractionType::TeleportationPoint;
}

// Called when the game starts or when spawned
void AChangeClassOrSkillPoint::BeginPlay()
{
	Super::BeginPlay();
	InteractButtonUI = Cast<UWidgetComponent>(GetComponentByClass(UWidgetComponent::StaticClass()));
	
}

// Called every frame
void AChangeClassOrSkillPoint::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);
}

void AChangeClassOrSkillPoint::ShowInteractButtonUI()
{
	if(IsValid(InteractButtonUI))
		InteractButtonUI->SetHiddenInGame(false);
}

void AChangeClassOrSkillPoint::HideInteractButtonUI()
{
	if(IsValid(InteractButtonUI))
		InteractButtonUI->SetHiddenInGame(true);
}

void AChangeClassOrSkillPoint::Interact(AAwPlayerController* Controller)
{
	UGameMain* MainUI = nullptr;
	if (UGameplayFuncLib::GetUiManager()->OpenedWidgets.Contains("GameMain"))
		MainUI = Cast<UGameMain>(UGameplayFuncLib::GetUiManager()->OpenedWidgets["GameMain"]);
	if (MainUI) MainUI->HiddeMainUI("InHide");
	
	switch (InteractionType)
	{
	case EInteractionType::TeleportationPoint:
		{
			break;
		}
	case EInteractionType::ChangeClass:
		{
			UGameplayFuncLib::PlayUIAudio("OptionMenu_Open");
			UChangeClass* ChangeClass = Cast<UChangeClass>(UGameplayFuncLib::GetUiManager()->Show("ChangeClass"));

			
			Controller->GameControlState = EGameControlState::ChangeSkillOrClass;
			break;
		}
	case EInteractionType::ChangeSkill:
		{
			UGameplayFuncLib::PlayUIAudio("OptionMenu_Open");
			UChangeSkill* ChangeSkill = Cast<UChangeSkill>(UGameplayFuncLib::GetUiManager()->Show("ChangeSkill"));
			if (ChangeSkill) 
				ChangeSkill->SetSkillToRoulette();
			/*ChangeSkill->SetSkillToMainRoulette();
			ChangeSkill->SetSkillToSecondRoulette();
			ChangeSkill->SetSkillToThirdRoulette();*/
			Controller->GameControlState = EGameControlState::ChangeSkillOrClass;
			
			break;
		}
		default:
		{
			
			break;
		}
	}
}
          