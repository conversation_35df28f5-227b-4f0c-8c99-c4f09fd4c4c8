// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Components/WidgetComponent.h"
#include "GameFramework/Actor.h"
#include "ChangeClassOrSkillPoint.generated.h"

UENUM()
enum class EInteractionType: uint8
{
	//传送点
	TeleportationPoint,
	//改变职业
	ChangeClass,
	//改变技能
	ChangeSkill
};


UCLASS()
class THEAWAKENER_FO_API AChangeClassOrSkillPoint : public AActor
{
	GENERATED_BODY()

public:

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UWidgetComponent* InteractButtonUI;

	//交互类型
	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	EInteractionType InteractionType;
	
	// Sets default values for this actor's properties
	AChangeClassOrSkillPoint();

protected:
	// Called when the game starts or when spawned
	virtual void BeginPlay() override;

public:
	// Called every frame
	virtual void Tick(float DeltaTime) override;


	UFUNCTION(BlueprintCallable)
	void ShowInteractButtonUI();

	UFUNCTION(BlueprintCallable)
	void HideInteractButtonUI();

	UFUNCTION(BlueprintCallable)
	void Interact(AAwPlayerController* Controller);
};


