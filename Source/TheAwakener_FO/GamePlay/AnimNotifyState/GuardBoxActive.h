// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotifyState.h"
#include "GuardBoxActive.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UGuardBoxActive : public UAnimNotifyState
{
	GENERATED_BODY()
public:
	//防御盒子的名称
	UPROPERTY()
	TArray<FString> GuardBoxNames;

	virtual void NotifyBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float TotalDuration,
		const FAnimNotifyEventReference& EventReference) override;
	virtual void NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
		const FAnimNotifyEventReference& EventReference) override;
};
