// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotifyState.h"
#include "TheAwakener_FO/GamePlay/Move/KeepMoveDir.h"
#include "KeepMovingInDirection.generated.h"

/**
 * 在这段时间内，动作将保持NotifyBegin时候接收到的角色移动输入，直到NotifyEnd的时候还原
 */
UCLASS(BlueprintType)
class THEAWAKENER_FO_API UKeepMovingInDirection : public UAnimNotifyState
{
	GENERATED_BODY()
public:
	/**
	 *记录的移动方向与新输入的移动方向（受到Acceptance影响）之间的Merge关系：
	 *Cover：只采用记录的移动方向，即notifyBegin时候的输入方向
	 *Plus：notifyBegin时候记录的方向与当前输入的方向进行相加然后除以2得出方向
	 *IgnoreWhileNotZero：当输入方向接近于0的时候启用记录的方向，否则启用输入方向
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	EMoveDirMergeRule DirectionMergeRule = EMoveDirMergeRule::Cover;

	virtual void NotifyBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float TotalDuration,
		const FAnimNotifyEventReference& EventReferences) override;
	virtual void NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
		const FAnimNotifyEventReference& EventReferences) override;
};
