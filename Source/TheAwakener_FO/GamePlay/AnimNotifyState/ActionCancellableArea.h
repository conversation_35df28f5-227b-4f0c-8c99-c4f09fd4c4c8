// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotifyState.h"
#include "TheAwakener_FO/GamePlay/Characters/Action/ActionInfo.h"
#include "ActionCancellableArea.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UActionCancellableArea : public UAnimNotifyState
{
	GENERATED_BODY()

private:
	
	FActionInfo MontageAction;
	
public:
	/**
	 * 这是不是一个状态机动画，如果用在BlendSpace，那就一定是了
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	bool IsStateAnim = true;
	/**
	 *在这一段，哪些Tag的可以Cancel我
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int CancelPointIndex = 0;

	/**
	 * 需要在一段时间内挨揍多少次（beOffendedRec.Num() >=这个）才能激活？
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int ActiveAfterBeOffended = 0;

	virtual void NotifyBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float TotalDuration, const FAnimNotifyEventReference& EventReference) override;
	virtual void NotifyTick(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float FrameDeltaTime, const FAnimNotifyEventReference& EventReference) override;
	virtual void NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, const FAnimNotifyEventReference& EventReference) override;
};
