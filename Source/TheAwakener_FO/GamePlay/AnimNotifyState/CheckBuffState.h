// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotifyState.h"
#include "CheckBuffState.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UCheckBuffState : public UAnimNotifyState
{
	GENERATED_BODY()
public:
	//需要检查BuffId
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString BuffId;
	
	//每个tick检查，如果有BUFF，就会跳转到这个时间点（秒）
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FName HasBuffToSection;

	//结束时检查，如果没有BUFF，就会跳转到这个时间点（秒）
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FName NotHasBuffToSection;

	virtual void NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
		const FAnimNotifyEventReference& EventReference) override;
	virtual void NotifyTick(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float FrameDeltaTime,
		const FAnimNotifyEventReference& EventReference) override;
};
