// Fill out your copyright notice in the Description page of Project Settings.


#include "TakeOffOrLanding.h"

#include "Components/SkeletalMeshComponent.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"

void UTakeOffOrLanding::NotifyTick(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float FrameDeltaTime,
	const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyTick(MeshComp, Animation, FrameDeltaTime, EventReference);
	AAwCharacter* Cha = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (Cha)
	{
		Cha->TakeOff(FlySpeed);
	}
}
