// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotifyState.h"
#include "ManaChecker.generated.h"

/**
 * 每帧扣除耐力，并且做耐力检查，如果耐力不足则可能取消动作并且解除攀附
 */
UCLASS()
class THEAWAKENER_FO_API UManaChecker : public UAnimNotifyState
{
	GENERATED_BODY()
public:
	/**
	 * 以每秒减少多少点的速度扣除Stamina，因为UE傻逼的Update，会导致Stamina不被扣除，那也就这么地了
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int CostStaminaPerSec = 0;

	/**
	 * 在Stamina低于多少的时候，动作会被强行结束，结束的是Montage，如果没有Montage，那可就不能怪我了
	 * 当然，如果恰好动作切换了，导致下一个动作被结束了，也不能怪我，谁让非要用BlendOut不可呢？
	 * 是<=的，所以写0会在<=0的时候结束当前动作，当然，如果是-1就结束不了了……
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int TerminateMontageBelowStamina = 0;

	/**
	 * 是否在低于标准的时候强行结束Attach关系
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool TerminateAttachmentBelowStamina = true;

	virtual void NotifyTick(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float FrameDeltaTime,
		const FAnimNotifyEventReference& EventReference) override;
};
