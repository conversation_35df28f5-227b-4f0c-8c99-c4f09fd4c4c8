// Fill out your copyright notice in the Description page of Project Settings.


#include "AnimRateModify.h"

#include "Components/SkeletalMeshComponent.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "TheAwakener_FO/GamePlay/Roguelike/BattleUpgrade/RogueBattleUpgradeSubSystem.h"

void UAnimRateModify::NotifyBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float TotalDuration,
                                  const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyBegin(MeshComp, Animation, TotalDuration, EventReference);

	AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Me) return;
	
	float NewAnimRate = AnimRate;

	if (bIsPowerAnim)
	{
		const int Stack = Me->GetBuffStackTotal("Rogue_PowerSpeedUp");
		NewAnimRate *= 1 + Stack*0.01;
	}
	
	Me->GetAwAnimInstance()->AnimRateScaleModifies.Add(FString::FromInt(GetUniqueID()), NewAnimRate);
}

void UAnimRateModify::NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyEnd(MeshComp, Animation, EventReference);

	const AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Me) return;

	const FString Uid = FString::FromInt(GetUniqueID());
	if (Me->GetAwAnimInstance()->AnimRateScaleModifies.Contains(Uid))
		Me->GetAwAnimInstance()->AnimRateScaleModifies.Remove(Uid);
}
