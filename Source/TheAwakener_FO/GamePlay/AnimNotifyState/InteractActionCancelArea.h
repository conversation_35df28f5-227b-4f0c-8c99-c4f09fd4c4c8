// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotifyState.h"
#include "TheAwakener_FO/GamePlay/Interact/BeInteractedBox.h"
#include "UObject/Object.h"
#include "InteractActionCancelArea.generated.h"

/**
 * 交互行为Cancel分叉
 * 这个和普通的Cancellable有一个区别，就是当有Interact可以发生的时候，角色会优先去Preorder Interact对应的动作
 */
UCLASS()
class THEAWAKENER_FO_API UInteractActionCancelArea : public UAnimNotifyState
{
	GENERATED_BODY()
private:
	virtual void NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
		const FAnimNotifyEventReference& EventReference) override;
	virtual void NotifyBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float TotalDuration,
		const FAnimNotifyEventReference& EventReference) override;
	virtual void NotifyTick(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float FrameDeltaTime,
		const FAnimNotifyEventReference& EventReference) override;
public:
	/**
	 * 这是不是一个状态机动画，如果用在BlendSpace，那就一定是了
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	bool IsStateAnim = true;
	/**
	 *在这一段，哪些Tag的可以Cancel我
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int CancelPointIndex = 0;
	/**
	 * 需要在一段时间内挨揍多少次（beOffendedRec.Num() >=这个）才能激活？
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int ActiveAfterBeOffended = 0;
	/**
	 * 默认的交互动作对应的CancelIndex
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int InteractCancelPointIndex = 1;
	
};
