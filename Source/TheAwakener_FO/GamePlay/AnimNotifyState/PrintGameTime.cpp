// Fill out your copyright notice in the Description page of Project Settings.


#include "PrintGameTime.h"

#include "Kismet/GameplayStatics.h"
#include "Kismet/KismetSystemLibrary.h"
#include "Components/SkeletalMeshComponent.h"

void UPrintGameTime::NotifyBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float TotalDuration,
	const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyBegin(MeshComp, Animation, TotalDuration, EventReference);

	UKismetSystemLibrary::PrintString(MeshComp, " - PrintGameTime - Begin - ");
	UKismetSystemLibrary::PrintString(MeshComp, FString::SanitizeFloat(UGameplayStatics::GetTimeSeconds(MeshComp)));
}

void UPrintGameTime::NotifyTick(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float FrameDeltaTime,
	const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyTick(MeshComp, Animation, FrameDeltaTime, EventReference);

	UKismetSystemLibrary::PrintString(MeshComp, FString::SanitizeFloat(UGameplayStatics::GetTimeSeconds(MeshComp)));
}

void UPrintGameTime::NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyEnd(MeshComp, Animation, EventReference);

	UKismetSystemLibrary::PrintString(MeshComp, " - PrintGameTime - End - ");
	UKismetSystemLibrary::PrintString(MeshComp, FString::SanitizeFloat(UGameplayStatics::GetTimeSeconds(MeshComp)));
}
