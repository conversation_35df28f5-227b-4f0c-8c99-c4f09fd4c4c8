// Fill out your copyright notice in the Description page of Project Settings.


#include "DebugNotifyState.h"

#include "Kismet/KismetSystemLibrary.h"
#include "TheAwakener_FO/GamePlay/Buff/BuffManager.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"

void UDebugNotifyState::NotifyBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float TotalDuration,
	const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyBegin(MeshComp, Animation, TotalDuration, EventReference);
	
	UKismetSystemLibrary::PrintString(this, FString("[Debug] Notify Begin"),
		true, true, FLinearColor::Green, 10);

	// AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	// if (!Me) return;
	//
	// Me->PlayAttachedVFX(
	// 	"Temp/ArtRes/Environment/Scene/FantasyDungeon/FX/Fire1",
	// 	"EyeFire", EVFXBindPointType::Eye, false
	// );
	//
	// if (Me->CharacterObj.Buff.Num())
	// {
	// 	const int WasStack = Me->CharacterObj.Buff[0].Stack;
	// 	UBuffManager::ModifyBuffStack(&Me->CharacterObj.Buff[0], 1);
	// 	UKismetSystemLibrary::PrintString(Me, FString("BuffMod:")
	// 		.Append(Me->CharacterObj.Buff[0].Model.Id).Append(">>")
	// 		.Append(FString::FromInt(WasStack)).Append(" / ")
	// 		.Append(FString::FromInt(Me->CharacterObj.Buff[0].Stack)).Append("//")
	// 		.Append(FString::SanitizeFloat(Me->CharacterObj.Buff[0].Duration))
	// 		, true, true, FLinearColor::Green, 10);
	// }
}

void UDebugNotifyState::NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyEnd(MeshComp, Animation, EventReference);
	
	UKismetSystemLibrary::PrintString(this, FString("[Debug] Notify End"),
		true, true, FLinearColor::Red, 10);

	// AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	// if (!Me) return;
	//
	// Me->StopAttachedVFX("EyeFire");
	//
	// if (Me->CharacterObj.Buff.Num())
	// {
	// 	const int WasStack = Me->CharacterObj.Buff[0].Stack;
	// 	UBuffManager::ModifyBuffStack(&Me->CharacterObj.Buff[0], 1);
	// 	UKismetSystemLibrary::PrintString(Me, FString("BuffMod:")
	// 		.Append(Me->CharacterObj.Buff[0].Model.Id).Append(">>")
	// 		.Append(FString::FromInt(WasStack)).Append(" / ")
	// 		.Append(FString::FromInt(Me->CharacterObj.Buff[0].Stack)).Append("//")
	// 		.Append(FString::SanitizeFloat(Me->CharacterObj.Buff[0].Duration))
	// 		, true, true, FLinearColor::Green, 10);
	// }
}

void UDebugNotifyState::NotifyTick(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float FrameDeltaTime,
	const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyTick(MeshComp, Animation, FrameDeltaTime, EventReference);

	UKismetSystemLibrary::PrintString(this, FString("[Debug] Notify Tick"),
		true, true, FLinearColor::Red, 10);
}

