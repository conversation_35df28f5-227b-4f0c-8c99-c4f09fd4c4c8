// Fill out your copyright notice in the Description page of Project Settings.


#include "InteractActionCancelArea.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "TheAwakener_FO/GamePlay/Interact/CharacterInteractBox.h"

void UInteractActionCancelArea::NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyEnd(MeshComp, Animation, EventReference);
	const AAwCharacter* Character = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Character) return;
	if (this->IsStateAnim == true)
	{
		Character->RemoveStateCancelTag(CancelPointIndex);
	}else
	{
		Character->RemoveMontageCancelTag(CancelPointIndex);
	}
}

void UInteractActionCancelArea::NotifyBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float TotalDuration,
	const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyBegin(MeshComp, Animation, TotalDuration, EventReference);

	AAwCharacter* Character = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Character ) return;
	
	UCharacterInteractBox* CharacterInteractBox =Cast<UCharacterInteractBox>(Character->GetComponentByClass(UCharacterInteractBox::StaticClass()));
	const TArray<EInteractTargetType> InteractTargets = CharacterInteractBox ? CharacterInteractBox ->HasSomethingToInteract() : TArray<EInteractTargetType>();
	if (InteractTargets.Num() > 0)
	{
		Character->AddCancelTag(this->IsStateAnim == true ? ECancelTagType::State : ECancelTagType::Montage, this->InteractCancelPointIndex);
		
		if (this->IsStateAnim == true)
		{
			Character->RemoveStateCancelTag(CancelPointIndex);
		}else
		{
			Character->RemoveMontageCancelTag(CancelPointIndex);
		}
	}else if (Character->BeOffendedRec.Num() >= this->ActiveAfterBeOffended)
	{
		Character->AddCancelTag(this->IsStateAnim == true ? ECancelTagType::State : ECancelTagType::Montage, this->CancelPointIndex);

		if (this->IsStateAnim == true)
		{
			Character->RemoveStateCancelTag(InteractCancelPointIndex);
		}else
		{
			Character->RemoveMontageCancelTag(InteractCancelPointIndex);
		}
	}
		
}

void UInteractActionCancelArea::NotifyTick(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float FrameDeltaTime,
	const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyTick(MeshComp, Animation, FrameDeltaTime, EventReference);

	AAwCharacter* Character = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Character ) return;

	UCharacterInteractBox* CharacterInteractBox = Cast<UCharacterInteractBox>(Character->GetComponentByClass(UCharacterInteractBox::StaticClass()));
	const TArray<EInteractTargetType> InteractTargets = CharacterInteractBox ? CharacterInteractBox->HasSomethingToInteract() : TArray<EInteractTargetType>();
	if (InteractTargets.Num() > 0)
	{
		Character->AddCancelTag(this->IsStateAnim == true ? ECancelTagType::State : ECancelTagType::Montage, this->InteractCancelPointIndex);
		
		if (this->IsStateAnim == true)
		{
			Character->RemoveStateCancelTag(CancelPointIndex);
		}else
		{
			Character->RemoveMontageCancelTag(CancelPointIndex);
		}
	}else if (Character->BeOffendedRec.Num() >= this->ActiveAfterBeOffended)
	{
		Character->AddCancelTag(this->IsStateAnim == true ? ECancelTagType::State : ECancelTagType::Montage, this->CancelPointIndex);

		if (this->IsStateAnim == true)
		{
			Character->RemoveStateCancelTag(InteractCancelPointIndex);
		}else
		{
			Character->RemoveMontageCancelTag(InteractCancelPointIndex);
		}
	}
}
