// Fill out your copyright notice in the Description page of Project Settings.


#include "ManaChecker.h"

#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"

void UManaChecker::NotifyTick(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float FrameDeltaTime,
	const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyTick(MeshComp, Animation, FrameDeltaTime, EventReference);
	AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Me) return;
	bool Below = Me->CostMana(this->CostStaminaPerSec * FrameDeltaTime, this->TerminateMontageBelowStamina);
	if (Below == true)
	{
		Me->StopCurrentAction(true);
		if (TerminateAttachmentBelowStamina == true)
		{
			Me->StopAttaching();
		}
	}
	//UKismetSystemLibrary::PrintString(this, FString("Stamina:").Append(FString::FromInt(Me->SP)).Append(FString(" / ")).Append(FString::FromInt(Me->SPMax)));
}
