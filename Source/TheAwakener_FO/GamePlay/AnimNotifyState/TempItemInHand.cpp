// Fill out your copyright notice in the Description page of Project Settings.


#include "TempItemInHand.h"

#include "Components/SkeletalMeshComponent.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"


void UTempItemInHand::NotifyBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float TotalDuration,
	const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyBegin(MeshComp, Animation, TotalDuration, EventReference);
	
	AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Me) return;

	const FItemObj* UsingItem =  UAwGameInstance::Instance->RoleInfo.GetUsingItem();
	if (!UsingItem || UsingItem->Model.Id.IsEmpty())
	{
		if (UGameplayFuncLib::GetAwGameInstance())
		{
			//如果玩家选中的东西还在就保留，否则自动选一个或者置空
			const FItemObj* SelItem = UGameplayFuncLib::GetAwGameInstance()->RoleInfo.QuickSlotFocusItem();
			const FString ItemId = SelItem ? SelItem->Model.Id : "";

			if (ItemId.IsEmpty()) return;	//没有选中道具
		}
		//return; //搞不定就逃避了

		UsingItem =  UAwGameInstance::Instance->RoleInfo.GetUsingItem();
	}
	if (!UsingItem || UsingItem->Model.Id.IsEmpty()) return;	//依然没有东西在使用中
		
	for (const FEquipmentAppearancePart AppPart : UsingItem->Model.AppearanceParts) 
		Me->AddTempItemInHand(AppPart);
}

void UTempItemInHand::NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyEnd(MeshComp, Animation, EventReference);
	
	AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Me) return;

	Me->RemoveAllItemsInHand();	//不管咋地，全部干掉，不然道具没了也没依据干掉
}