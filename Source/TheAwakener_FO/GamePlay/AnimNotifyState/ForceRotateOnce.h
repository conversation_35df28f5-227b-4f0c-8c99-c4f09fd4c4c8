// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotifyState.h"
#include "ForceRotateOnce.generated.h"

/**
 * 在这个阶段，允许玩家强行执行一次转向操作方向的操作
 */
UCLASS(Blueprintable)
class THEAWAKENER_FO_API UForceRotateOnce : public UAnimNotifyState
{
	GENERATED_BODY()

private:
	UPROPERTY()
	FString ActionId;
public:
	//这个的Index，开启和关闭依赖于Index，2段一样的Index是可以互相关闭的，如果第一段的NotifyEnd在第二段的NotifyBegin之前
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	uint8 Index = 0;
	
	//允许最大旋转的角度
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float DegreeLimit = 180.f;

	virtual void NotifyBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float TotalDuration,
		const FAnimNotifyEventReference& EventReference) override;
	virtual void NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
		const FAnimNotifyEventReference& EventReference) override;
};
