

#include "CreateBeam.h"

#include "Kismet/KismetMathLibrary.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"

void UCreateBeam::NotifyTick(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float FrameDeltaTime,
	const FAnimNotifyEventReference& EventReference)
{
	PRAGMA_DISABLE_DEPRECATION_WARNINGS
	Super::NotifyTick(MeshComp, Animation, FrameDeltaTime, EventReference);
	PRAGMA_DISABLE_DEPRECATION_WARNINGS
}

void UCreateBeam::NotifyBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float TotalDuration,
	const FAnimNotifyEventReference& EventReference)
{
	PRAGMA_DISABLE_DEPRECATION_WARNINGS
	Super::NotifyBegin(MeshComp, Animation, TotalDuration);
	PRAGMA_ENABLE_DEPRECATION_WARNINGS

	AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Me) return;
	if (Me->GetLocalRole() != ENetRole::ROLE_Authority) return;
	if (Me->Dead() || Me->IsPendingKillPending()) return;
	
	BeamAOE = UGameplayFuncLib::CreateAOE(Me, AOEId, FVector::Zero(), FVector::Zero(), LifeSpan, TweenFunc);

	if(BeamAOE && Attached)
	{
		BeamAOE->AttachToComponent(MeshComp,FAttachmentTransformRules::KeepRelativeTransform,SocketName);
		BeamAOE->SetActorRelativeLocation(OffsetTransform.GetLocation());
		BeamAOE->SetActorRelativeRotation(OffsetTransform.GetRotation());
		BeamAOE->SetActorRelativeScale3D(OffsetTransform.GetScale3D());
	}
}

void UCreateBeam::NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	PRAGMA_DISABLE_DEPRECATION_WARNINGS
	Super::NotifyEnd(MeshComp, Animation, EventReference);
	PRAGMA_DISABLE_DEPRECATION_WARNINGS
	
	if(BeamAOE)
	{
		BeamAOE->AOELifeSpan = 0.1;
		//BeamAOE->Destroyed();
	}
}
