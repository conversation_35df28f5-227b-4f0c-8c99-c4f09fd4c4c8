// Fill out your copyright notice in the Description page of Project Settings.


#include "CheckOnGroundState.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"


void UCheckOnGroundState::NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyEnd(MeshComp, Animation, EventReference);
	AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Me || Me->OnGround() == true || this->NotOnGroundLoopBack.IsNone()) return;

	UAnimMontage* Montage = Me->GetCurrentActiveMontage();
	FAnimMontageInstance* MontageInstance = Me->GetActiveMontageInstance();
	if (Montage && MontageInstance)
	{
		const int32 SectionID = Montage->GetSectionIndex(NotOnGroundLoopBack);
		float StartTime = 0.f;
		float EndTime = 0.f;
		Montage->GetSectionStartAndEndTime(SectionID, StartTime, EndTime);
		MontageInstance->SetPosition(StartTime); 
	}
}

void UCheckOnGroundState::NotifyTick(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float FrameDeltaTime,
	const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyTick(MeshComp, Animation, FrameDeltaTime, EventReference);
	AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Me || Me->OnGround() == false) return;
	if (this->OnGroundToSection.IsNone())
	{
		//Me->PreorderActionByMontageState(ECharacterMontageState::Landing);
	}else
	{
		UAnimMontage* Montage = Me->GetCurrentActiveMontage();
		FAnimMontageInstance* MontageInstance = Me->GetActiveMontageInstance();
		if (Montage && MontageInstance)
		{
			const int32 SectionID = Montage->GetSectionIndex(OnGroundToSection);
			float StartTime = 0.f;
			float EndTime = 0.f;
			Montage->GetSectionStartAndEndTime(SectionID, StartTime, EndTime);
			MontageInstance->SetPosition(StartTime); 
		}
	}
	if (ShakeInfo.Shake && this->GetWorld())
	{
		const FVector FinalPos = ShakeInfo.Offset + Me->GetActorLocation();
		APlayerCameraManager::PlayWorldCameraShake(
			this->GetWorld(),
			ShakeInfo.Shake, FinalPos,
			ShakeInfo.FullShockRange, ShakeInfo.LoseShockRange,
			ShakeInfo.FallOff, ShakeInfo.DoRotate
		);
	}
	
}