// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "AimRotateLimitInfo.generated.h"

USTRUCT(BlueprintType)
struct FAimRotateLimitInfo
{
	GENERATED_BODY()

	// Uid
	UPROPERTY()
	FString Uid;

	// 原来的相机的旋转，左右限制都是根据这个值来的。
	UPROPERTY()
	FRotator OriCameraRotate = FRotator::ZeroRotator;
	
	// 旋转相机是否受到限制
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool IsActive = false;
	
	// 相机的上下旋转限制，正负多少
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float RotateLimit_Pitch = 45;

	// 相机的水平旋转限制，正负多少
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float RotateLimit_Yaw = 45;
	
	// 相机的水平旋转超过限制度数之后，是否旋转角色对齐
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool CanYawReset = false;
	
	FAimRotateLimitInfo(){};
	
	FAimRotateLimitInfo(
		FString Uid_new,
		FRotator OriCameraRotate_new,
		bool IsActive_new = false,
		float RotateLimit_Pitch_new = 45,
		float RotateLimit_Yaw_new = 45,
		bool CanYawReset_new = true )
	{
		this->Uid = Uid_new;
		this->OriCameraRotate = OriCameraRotate_new;
		this->IsActive = IsActive_new;
		this->RotateLimit_Pitch = RotateLimit_Pitch_new;
		this->RotateLimit_Yaw = RotateLimit_Yaw_new;
		this->CanYawReset = CanYawReset_new;
	}

	void Empty()
	{
		this->Uid = "";
		this->OriCameraRotate = FRotator::ZeroRotator;
		this->IsActive = false;
		this->RotateLimit_Pitch = 45;
		this->RotateLimit_Yaw = 45;
		this->CanYawReset = true;
	}
};
