// Fill out your copyright notice in the Description page of Project Settings.


#include "AddBuffInAction.h"

#include "Components/SkeletalMeshComponent.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"

void UAddBuffInAction::NotifyBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float TotalDuration,
	const FAnimNotifyEventReference& EventReference) 
{
	Super::NotifyBegin(MeshComp, Animation, TotalDuration, EventReference);
	
	AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Me) return;
	FBuffModel BuffModel = UGameplayFuncLib::GetAwDataManager()->GetBuffModelById(BuffId);
	if (BuffModel.ValidBuffModel() == false) return;
	Me->AddBuff(FAddBuffInfo(Me, Me, BuffModel, BuffStack, TotalDuration, true, false));
}

void UAddBuffInAction::NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyEnd(MeshComp, Animation, EventReference);
	
	AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Me) return;
	Me->RemoveBuffById(BuffId);
}
