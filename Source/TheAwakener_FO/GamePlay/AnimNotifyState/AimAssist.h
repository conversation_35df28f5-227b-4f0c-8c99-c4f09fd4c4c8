// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotifyState.h"
#include "AimAssist.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UAimAssist : public UAnimNotifyState
{
	GENERATED_BODY()
public:
	//辅助检测的半角角度
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float AssistDegreeRange = 30.0f;
	//辅助检测的半径距离
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float AssistDistanceRange = 500.0f;
	//旋转速度：Speed = 1.0(用1秒时间旋转180度)； Speed = 2.0(用0.5秒时间旋转180度)
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float AssistRotSpeed = 1.0f;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float AssistRotSpeedInLock = 10.0f;
	//辅助检测是角度优先还是距离优先
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool DegreeFirst = true;

	// 相机 Yaw轴 辅助旋转速度，默认值为10，<=0的时候不开启
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float AssistCameraSpeed = 5.0f;
	
	AActor* TargetActor = nullptr;

	virtual void NotifyBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float TotalDuration,
		const FAnimNotifyEventReference& EventReference) override;
	virtual void NotifyTick(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float FrameDeltaTime,
		const FAnimNotifyEventReference& EventReference) override;
	virtual void NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
		const FAnimNotifyEventReference& EventReference) override;
private:
	bool HasLockTarget = false;
};
