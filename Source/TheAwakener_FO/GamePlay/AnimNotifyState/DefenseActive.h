// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotifyState.h"
#include "TheAwakener_FO/GamePlay/Characters/Battle/BattleStructs.h"
#include "TheAwakener_FO/GamePlay/Roguelike/BattleStyle/AttackModifyByBattleStyleUpgrade.h"
#include "DefenseActive.generated.h"

/**
 * 开启一系列特殊防御信息
 */
UCLASS()
class THEAWAKENER_FO_API UDefenseActive : public UAnimNotifyState
{
	GENERATED_BODY()
public:
	//防御信息
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FDefenseInfo Defense;

	//是否对所有的受击框有效
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool AffectAllCharacterHitBoxes = false;
	
private:
	FDefenseInfo NewDefense;
	
public:
	virtual void NotifyBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float TotalDuration,
		const FAnimNotifyEventReference& EventReference) override;
	virtual void NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
		const FAnimNotifyEventReference& EventReference) override;
};
