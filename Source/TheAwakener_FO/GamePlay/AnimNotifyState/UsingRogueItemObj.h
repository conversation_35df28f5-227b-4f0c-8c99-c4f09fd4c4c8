// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotifyState.h"
#include "UsingRogueItemObj.generated.h"

/**
 * 使用道具（ItemObj）的Notify
 */
UCLASS()
class THEAWAKENER_FO_API UUsingRogueItemObj : public UAnimNotifyState
{
	GENERATED_BODY()
public:
	//使用的效果分组
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FString> UseMethodTags = {"Default"};
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool bUseHealPotion = false;

	virtual void NotifyBegin(USkeletalMeshComponent * MeshComp, UAnimSequenceBase * Animation, float TotalDuration, const FAnimNotifyEventReference& EventReference) override;
	virtual void NotifyEnd(USkeletalMeshComponent * MeshComp, UAnimSequenceBase * Animation, const FAnimNotifyEventReference& EventReference) override;
private:
	FString UID ="";
};

