// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotifyState.h"
#include "ActionGravityTimes.generated.h"

/**
 * 动作的受重力影响倍率，在动作有RootMotion的情况下，是否还会受到重力影响
 * 1.0f就是受到标准重力影响，0.f是常态，所以结束后会被重置到0.f
 * 允许设置负数，吃到反向重力
 */
UCLASS()
class THEAWAKENER_FO_API UActionGravityTimes : public UAnimNotifyState
{
	GENERATED_BODY()
private:
	inline void SetGravityTimes(USkeletalMeshComponent* MeshComp, float Times = 1.f) const;
public:
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float GravityTimes = 1.f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool TerrainCollisionEnable = true;
	
	virtual void NotifyBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float TotalDuration, const FAnimNotifyEventReference& EventReference) override;
	virtual void NotifyTick(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float FrameDeltaTime, const FAnimNotifyEventReference& EventReference) override;
	virtual void NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, const FAnimNotifyEventReference& EventReference) override;
};
