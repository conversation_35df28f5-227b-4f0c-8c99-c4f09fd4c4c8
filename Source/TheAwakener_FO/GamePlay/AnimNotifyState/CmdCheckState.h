// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotifyState.h"
#include "TheAwakener_FO/GamePlay/Roguelike/BattleStyle/AttackModifyByBattleStyleUpgrade.h"
#include "CmdCheckState.generated.h"

/**
 * 
 */
UCLASS(BlueprintType)
class THEAWAKENER_FO_API UCmdCheckState : public UAnimNotifyState
{
	GENERATED_BODY()

public:
	//对应的按键，如果没有填写，那就是当前技能需要的按键
	UPROPERTY(EditAnywhere, BlueprintReadOnly)
	TArray<FString> Cmd;

	/**
	 *按键的类型条件，如果符合才会通过。
	 *比如加特林机枪，既可以是Hold也可以是Press，那么2个都是true就可以，有一个就能通过
	 *如果2个都写False，就会默认通过
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	bool CmdHold = true;

	/**
	 *按键的类型条件，如果符合才会通过。
	 *比如加特林机枪，既可以是Hold也可以是Press，那么2个都是true就可以，有一个就能通过
	 *如果2个都写False，就会默认通过
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	bool CmdPress = false;
	
	//按键按住了就走这里了
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FName GotoSectionWhileHoldingOrPressCmd;
	
	//按键没有按住的时候就会去这里
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FName GotoSectionWhileNotHoldingOrPressCmd;

	// 这次判断是否有去Loop动画？
	// 在 Notify End 的时候使用
	bool IsGotoLoop = false;

	FString UniqueID = "";
	
	// 最大Loop次数
	// 0 - 无限
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int MaxLoopNum = 0;
	
	virtual void NotifyTick(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float FrameDeltaTime,
		const FAnimNotifyEventReference& EventReference) override;

	virtual void NotifyBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
		float TotalDuration, const FAnimNotifyEventReference& EventReference) override;
	
	virtual void NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
		const FAnimNotifyEventReference& EventReference) override;
};
