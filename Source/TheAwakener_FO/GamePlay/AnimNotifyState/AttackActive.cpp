// Fill out your copyright notice in the Description page of Project Settings.


#include "AttackActive.h"

#include "Components/SkeletalMeshComponent.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GamePlay/ApparatusCustom/ECSDamageLib.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "TheAwakener_FO/GamePlay/Roguelike/BattleUpgrade/RogueBattleUpgradeSubSystem.h"

void UAttackActive::NotifyBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float TotalDuration,
                                const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyBegin(MeshComp, Animation, TotalDuration, EventReference);
	AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Me) return;
	if(Me->GetLocalRole() != ENetRole::ROLE_Authority) return;
	
	NewOffenseInfo = this->OffenseInfo;
	
	NewOffenseInfo.AttackInfo.Elemental = Me->GetElemental(
		NewOffenseInfo.AttackInfo.Elemental,
		this->ElementSource,
		this->ElementalInWeaponSlot
	);
	NewOffenseInfo.AttackInfo.AttackerActionChange.HitStun.AutoSetActive();
	NewOffenseInfo.AttackInfo.DefenderActionChange.HitStun.AutoSetActive();

	NewOffenseInfo = URogueWeapon::ChangeAttackHitBoxByWeapon(Me, NewOffenseInfo);
	
	if (AbilityIds.Num() > 0)
	{
		UAwRogueDataSystem* DataSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>();
		UAwDataManager* DataManager = UGameplayFuncLib::GetDataManager();
		TMap<ERogueAbilitySlot, FRougeAbilityLevelInfo> CurAbilityLevelInfos = DataSystem->GetCurAbilityLevelInfos(Me->GetPawnClassId());
		for (FString AbilityId : AbilityIds)
		{
			bool IsEquip = false;
			int level = 0;
			for (TTuple<ERogueAbilitySlot, FRougeAbilityLevelInfo> Info : CurAbilityLevelInfos)
				if (Info.Value.AbilityInfoId == AbilityId)
				{
					IsEquip = true;
					level = Info.Value.Level;
					break;
				}
			if (IsEquip)
			{
				FRogueBattleAbilityInfo AbilityInfo = DataManager->GetRogueBattleAbilityInfo(Me->GetPawnClassId(), AbilityId);
				float AtkScale = 1;
				if(AbilityInfo.AtkScale.Num() > level)
					AtkScale = AbilityInfo.AtkScale[level];
				float BreakScale = 1;
				if(AbilityInfo.BreakScale.Num() > level)
					BreakScale = AbilityInfo.BreakScale[level];
				NewOffenseInfo.AttackInfo.DamagePower.Physical *= AtkScale;
				NewOffenseInfo.AttackInfo.DamagePower.Break *= BreakScale;
				break;
			}
		}
		
	}
	
	Me->AddActionHitInfo(NewOffenseInfo);	//TODO 这里只是碰到了
	for (const FString ThisHitBoxName : NewOffenseInfo.AttackHitBoxName) 
		Me->ActiveOffenseHitBox(ThisHitBoxName);
	Me->ActiveECSAttackBoxs(NewOffenseInfo.AttackHitBoxName);
}

void UAttackActive::NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyEnd(MeshComp, Animation, EventReference);
	AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	//UKismetSystemLibrary::PrintString(this, FString("Attack Deactived").Append(Me ? " ON ME":"_______").Append(HitBoxName[0]));
	if (!Me) return;
	if (Me->GetLocalRole() != ENetRole::ROLE_Authority) return;

	NewOffenseInfo = URogueWeapon::ChangeAttackHitBoxByWeapon(Me, NewOffenseInfo);
	
	for (const FString ThisHitBoxName : NewOffenseInfo.AttackHitBoxName) 
		Me->StopOffenseHitBox(ThisHitBoxName);
	UAwAnimInstance* AnimInstance = Cast<UAwAnimInstance>(MeshComp->GetAnimInstance());
	// 检查当前Montage是否处于Pause状态
    if (AnimInstance&&!AnimInstance->GetIsFreezing()&& Me)
    {
		Me->DeactiveECSAttackBoxs(NewOffenseInfo.AttackHitBoxName);
    	
		if (NewOffenseInfo.AttackHitBoxName.Num())
		{
			auto flag = Me->GetAttackHitComponent()->GetECSAttackFlag(NewOffenseInfo.AttackHitBoxName[0]);
			//清理ECS命中记录
			UECSDamageLib::CleanActionHittedECS(flag,Me->GetWorld());
		}
    }
	Me->RemoveActionHitInfo(NewOffenseInfo);

}