// Fill out your copyright notice in the Description page of Project Settings.


#include "CheckAnimNotfiyStateBuff.h"

#include "Animation/AnimNotifyEndDataContext.h"
#include "TheAwakener_FO/FunctionLibrary/CommonFuncLib.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"


UCheckAnimNotfiyStateBuff::UCheckAnimNotfiyStateBuff()
{
	bIsNativeBranchingPoint = true;
}

void UCheckAnimNotfiyStateBuff::NotifyBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
                                            float TotalDuration, const FAnimNotifyEventReference& EventReference)
{
	PRAGMA_DISABLE_DEPRECATION_WARNINGS
	Super::NotifyBegin(MeshComp, Animation,TotalDuration);
	PRAGMA_ENABLE_DEPRECATION_WARNINGS
	
	AAwCharacter* Character = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Character) return;
	const UAwAnimInstance* AnimInstance = Character->GetAwAnimInstance();
	if (!AnimInstance) return;

	if (Character->GetAwAnimInstance()->CurMontageUID ==UID||Character->GetAwAnimInstance()->CurMontageUID.IsEmpty())
	{
		return;
	}

	// if (UGameplayFuncLib::GetUiManager()) 
	// 	for (const FString Str : CheckBuffTags)
	// 		UGameplayFuncLib::GetUiManager()->OnTriggerRougeBattleTag_ByFStr(Str);
	
	UID = Character->GetAwAnimInstance()->CurMontageUID;
	//auto引用 在montage Pause 和 resume的时候 重复触发会崩
	for (int i=0;i<Character->CharacterObj.Buff.Num();++i)
	{
		if (UCommonFuncLib::ArrayContainsArray(Character->CharacterObj.Buff[i].Model.Tags,CheckBuffTags,bContainsAllTags))
		{
			for (const auto FunString : Character->CharacterObj.Buff[i].Model.OnAnimNotfiyBegin)
			{
				UFunction* Func = UCallFuncLib::JsonFuncToUFunc(FunString);
				if (IsValid(Func) == false) continue;;
				
				struct {
					FBuffObj BuffObj;
					int32 WasStack;
					TArray<FString> Params;

					FBuffRunResult  Result;
				} FuncParam;
							
				FuncParam.BuffObj = Character->CharacterObj.Buff[i];
				FuncParam.WasStack = Character->CharacterObj.Buff[i].Stack;
				FuncParam.Params = FunString.Params;
				
				Character->ProcessEvent(Func, &FuncParam);
				//Character->CharacterObj.Buff[i] = FuncParam.Result.BuffObj;
			}
		}
	}

}

void UCheckAnimNotfiyStateBuff::NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{

	PRAGMA_DISABLE_DEPRECATION_WARNINGS
	Super::NotifyEnd(MeshComp, Animation);
	PRAGMA_ENABLE_DEPRECATION_WARNINGS
	AAwCharacter* Character = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Character) return;
	const UAwAnimInstance* AnimInstance = Character->GetAwAnimInstance();
	if (!AnimInstance) return;

	bool IsPause = !Character->GetAwAnimInstance()->Montage_IsPlaying(nullptr);
	//保证Event是正常触发 非montage暂停-恢复等非正常因素触发
	if (IsPause&&UID == Character->GetAwAnimInstance()->CurMontageUID)
	{
		return;
	}

	// if (UGameplayFuncLib::GetUiManager()) 
	// 	for (const FString Str : CheckBuffTags)
	// 		UGameplayFuncLib::GetUiManager()->OnTriggerRougeBattleTag_ByFStr(Str);
	
	for (int i=0;i<Character->CharacterObj.Buff.Num();++i)
	{
		if (UCommonFuncLib::ArrayContainsArray(Character->CharacterObj.Buff[i].Model.Tags,CheckBuffTags,bContainsAllTags))
		{
			for (const auto FunString : Character->CharacterObj.Buff[i].Model.OnAnimNotfiyEnd)
			{
				UFunction* Func = UCallFuncLib::JsonFuncToUFunc(FunString);
				if (IsValid(Func) == false) continue;;
				
				struct {
					FBuffObj BuffObj;
					int32 WasStack;
					TArray<FString> Params;

					FBuffRunResult  Result;
				} FuncParam;
							
				FuncParam.BuffObj = Character->CharacterObj.Buff[i];
				FuncParam.WasStack = Character->CharacterObj.Buff[i].Stack;
				FuncParam.Params = FunString.Params;
				
				Character->ProcessEvent(Func, &FuncParam);
				//Character->CharacterObj.Buff[i] = FuncParam.Result.BuffObj;
			}
		}
	}

	//UKismetSystemLibrary::PrintString(this,"RemoveBuff:"+CheckBuffTags[0],true,true,FColor::Red,20.f);
}
