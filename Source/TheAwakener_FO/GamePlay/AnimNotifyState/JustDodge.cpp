// Fill out your copyright notice in the Description page of Project Settings.


#include "JustDodge.h"

#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"

void UJustDodge::NotifyBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float TotalDuration,
	const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyBegin(MeshComp, Animation, TotalDuration, EventReference);
	AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Me) return;
	Me->SetJustDodge(this->JustDodgeInfo);
	for (const FString BoxId : ActiveCharacterHitBoxId)
	{
		TTuple<USceneComponent*, UCharacterHitBoxData*> HitBoxInfo = Me->GetCharacterHitBoxByName(BoxId);
		UCharacterHitBoxData* HitBox = HitBoxInfo.Get<1>();
		if (HitBox)
		{
			HitBox->Active = true;
		}
	}
}

void UJustDodge::NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyEnd(MeshComp, Animation, EventReference);
	AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Me) return;
	Me->SetJustDodge(FJustDodgeInfo());
	for (const FString BoxId : ActiveCharacterHitBoxId)
	{
		TTuple<USceneComponent*, UCharacterHitBoxData*> HitBoxInfo = Me->GetCharacterHitBoxByName(BoxId);
		UCharacterHitBoxData* HitBox = HitBoxInfo.Get<1>();
		if (HitBox)
		{
			HitBox->Active = false;
		}
	} 
}

