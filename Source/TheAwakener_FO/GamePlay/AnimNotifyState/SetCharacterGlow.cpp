// Fill out your copyright notice in the Description page of Project Settings.


#include "SetCharacterGlow.h"

#include "Components/SkeletalMeshComponent.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"

void USetCharacterGlow::NotifyBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float TotalDuration,
	const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyBegin(MeshComp, Animation, TotalDuration, EventReference);
	
	AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Me) return;

	FGlowExtendTweenInfo GlowInfo = FGlowExtendTweenInfo(this->InTime, this->OutTime);
	GlowInfo.TotalTime = TotalDuration;
	GlowInfo.TimeElapsed = 0;
	GlowInfo.ShiningDarkness = 0.4f;
	GlowInfo.Color = GlowColor;
	Me->AddGlowExtendInfo(GlowInfo);
}

void USetCharacterGlow::NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyEnd(MeshComp, Animation, EventReference);

	AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Me) return;
	
	Me->EmptyGlowExtendInfos();
}

