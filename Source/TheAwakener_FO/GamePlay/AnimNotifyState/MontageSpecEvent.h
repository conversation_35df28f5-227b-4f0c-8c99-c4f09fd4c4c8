// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotifyState.h"
#include "TheAwakener_FO/GamePlay/Characters/Action/ActionComponent.h"
#include "MontageSpecEvent.generated.h"

/**
 * 蒙太奇上一些特殊处理，比如符合条件后下马之类的
 */
UCLASS()
class THEAWAKENER_FO_API UMontageSpecEvent : public UAnimNotifyState
{
	GENERATED_BODY()
private:
	UPROPERTY()
	AAwCharacter* OwnerCharacter;
	
	void DoPreorderAction(FActionParam Param = FActionParam());
public:
	/**
	 *如果函数不存在，FActionId和FActionParam（TODO：暂时为空）
	 *这是一个(AwCharacter*, FActionInfo, UMontageNatureLoop*)=>FString
	 *(谁、什么动作、这个NotifyState)=>预约哪个动作的Id
	 *在动画播放到这个State的时候就会调用，如果返回的是空字符串，就不会执行，否则就真的PreorederAction了
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString LoopCheck;

	virtual void NotifyBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float TotalDuration,
		const FAnimNotifyEventReference& EventReference) override;
	virtual void NotifyTick(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float FrameDeltaTime,
		const FAnimNotifyEventReference& EventReference) override;
	virtual void NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
		const FAnimNotifyEventReference& EventReference) override;
};
