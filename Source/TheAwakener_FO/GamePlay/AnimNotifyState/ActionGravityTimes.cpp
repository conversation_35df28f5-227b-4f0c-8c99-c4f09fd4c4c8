// Fill out your copyright notice in the Description page of Project Settings.


#include "ActionGravityTimes.h"

#include "Components/SkeletalMeshComponent.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"

void UActionGravityTimes::SetGravityTimes(USkeletalMeshComponent* MeshComp, float Times) const
{
	AAwCharacter* Character = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Character) return;
	Character->SetActionGravityTimes(Times);
}

void UActionGravityTimes::NotifyBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float TotalDuration,
	const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyBegin(MeshComp, Animation, TotalDuration, EventReference);
	SetGravityTimes(MeshComp, this->GravityTimes);
	
	if (!TerrainCollisionEnable)
	{
		AAwCharacter* Character = Cast<AAwCharacter>(MeshComp->GetOwner());
		if (Character)
			Character->TerrainCollisionEnable = TerrainCollisionEnable;
	}
}

void UActionGravityTimes::NotifyTick(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float FrameDeltaTime,
	const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyTick(MeshComp, Animation, FrameDeltaTime, EventReference);
	SetGravityTimes(MeshComp, this->GravityTimes);	
}

void UActionGravityTimes::NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyEnd(MeshComp, Animation, EventReference);
	SetGravityTimes(MeshComp, 1);
	
	AAwCharacter* Character = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (Character)
		Character->TerrainCollisionEnable = true;
}