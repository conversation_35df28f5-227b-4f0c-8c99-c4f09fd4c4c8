// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotifyState.h"
#include "TheAwakener_FO/GamePlay/Camera/AwCameraShakeInfo.h"
#include "CheckOnGroundState.generated.h"

/**
 *  检查是否落地的AnimNotifyState
 *  这个东西用来解决Loop阶段任何一帧都可能落地的问题：
 *  如果这个的NotifyEnd没有走到，则每个Tick检查是否落地，落地会跳转到落地后的点
 *  如果这个NotifyEnd时候没有落地，就会回到指定的未落地循环起点
 */
UCLASS()
class THEAWAKENER_FO_API UCheckOnGroundState : public UAnimNotifyState
{
	GENERATED_BODY()
public:
	//每个tick检查，如果落地了，就会跳转到这个时间点（秒）
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FName OnGroundToSection;

	//结束时检查，如果没有落地，就会跳转到这个时间点（秒）
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FName NotOnGroundLoopBack;

	//落地震屏，如果Shake是空，就不会真震动
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FAwCameraShakeInfo ShakeInfo;

	virtual void NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
		const FAnimNotifyEventReference& EventReference) override;
	virtual void NotifyTick(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float FrameDeltaTime,
		const FAnimNotifyEventReference& EventReference) override;
};
