// Fill out your copyright notice in the Description page of Project Settings.


#include "ModifyRootMotionRate.h"

#include "Components/SkeletalMeshComponent.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "TheAwakener_FO/GamePlay/Roguelike/BattleUpgrade/RogueBattleUpgradeSubSystem.h"

void UModifyRootMotionRate::NotifyBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
                                        float TotalDuration, const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyBegin(MeshComp, Animation, TotalDuration, EventReference);

	UniqueID = FString::FromInt(this->GetUniqueID());

	AAwCharacter* Character = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Character) return;
	
	Character->RootMotionRate.Add(UniqueID, ModifyRate);
}

void UModifyRootMotionRate::NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyEnd(MeshComp, Animation, EventReference);

	AAwCharacter* Character = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Character) return;

	Character->RootMotionRate.Remove(UniqueID);
}
