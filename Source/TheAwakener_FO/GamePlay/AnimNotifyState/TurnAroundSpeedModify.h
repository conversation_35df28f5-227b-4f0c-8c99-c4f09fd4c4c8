// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotifyState.h"
#include "TurnAroundSpeedModify.generated.h"

/**
 * 角色转身速度变化，1.00f=100%
 */
UCLASS()
class THEAWAKENER_FO_API UTurnAroundSpeedModify : public UAnimNotifyState
{
	GENERATED_BODY()
public:
	//旋转速度的倍率
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float ToTurnSpeedTimes = 1.00f;

	virtual void NotifyBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float TotalDuration,
		const FAnimNotifyEventReference& EventReference) override;
	virtual void NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
		const FAnimNotifyEventReference& EventReference) override;
};
