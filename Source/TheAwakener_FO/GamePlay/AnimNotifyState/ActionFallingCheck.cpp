// Fill out your copyright notice in the Description page of Project Settings.


#include "ActionFallingCheck.h"

#include "Components/SkeletalMeshComponent.h"

void UActionFallingCheck::NotifyTick(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float FrameDeltaTime,
                                     const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyTick(MeshComp, Animation, FrameDeltaTime, EventReference);
	
	AAwCharacter* OwnerCharacter = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (OwnerCharacter == nullptr /*|| OwnerCharacter->Dead()  == true*/) return;
	//	UActionComponent* Comp = OwnerCharacter->GetActionComponent();
	const ECharacterActionState CurState = OwnerCharacter->CurrentActionState();

	
	// if (!OwnerCharacter->IsPlayerCharacter())
	// {
	// 	switch (CurState)
	// 	{
	// 	case ECharacterActionState::Ground: UKismetSystemLibrary::PrintString(this, "Ground"); break;
	// 	case ECharacterActionState::Falling: UKismetSystemLibrary::PrintString(this, "Falling"); break;
	// 	case ECharacterActionState::Flying: UKismetSystemLibrary::PrintString(this, "Flying"); break;
	// 	case ECharacterActionState::Attached: UKismetSystemLibrary::PrintString(this, "Attached"); break;
	// 	default: break;
	// 	}
	// }
	
	if (CurState == ECharacterActionState::Ground)
	{
		if (TerminateOnHitGround == true)
		{
			if (TerminateToSection.IsNone())
			{
				//OwnerCharacter->StopCurrentAction(true);
				OwnerCharacter->PreorderActionByMontageState(ECharacterMontageState::Landing);
			}else
			{
				UAnimMontage* Montage = OwnerCharacter->GetCurrentActiveMontage();
				FAnimMontageInstance* MontageInstance = OwnerCharacter->GetActiveMontageInstance();
				if (Montage && MontageInstance)
				{
					const int32 SectionID = Montage->GetSectionIndex(this->TerminateToSection);
					if (SectionID == INDEX_NONE) OwnerCharacter->StopCurrentAction(true);
					float StartTime = 0.f;
					float EndTime = 0.f;
					Montage->GetSectionStartAndEndTime(SectionID, StartTime, EndTime);
					MontageInstance->SetPosition(StartTime); 
				}else
				{
					OwnerCharacter->StopCurrentAction(true);
				}
			}
		}
	}else if (CurState == ECharacterActionState::Falling)
	{
		if (TerminateOnFalling == true)
		{
			if (TerminateToSection.IsNone())
			{
				OwnerCharacter->StopCurrentAction(true);
			}else
			{
				UAnimMontage* Montage = OwnerCharacter->GetCurrentActiveMontage();
				FAnimMontageInstance* MontageInstance = OwnerCharacter->GetActiveMontageInstance();
				if (Montage && MontageInstance)
				{
					const int32 SectionID = Montage->GetSectionIndex(this->TerminateToSection);
					if (SectionID == INDEX_NONE) OwnerCharacter->StopCurrentAction(true);
					float StartTime = 0.f;
					float EndTime = 0.f;
					Montage->GetSectionStartAndEndTime(SectionID, StartTime, EndTime);
					MontageInstance->SetPosition(StartTime); 
				}else
				{
					OwnerCharacter->StopCurrentAction(true);
				}
			}
			
		}
		else if (FallingLoop == true)
		{
			UAnimMontage* Montage = OwnerCharacter->GetCurrentActiveMontage();
			FAnimMontageInstance* MontageInstance = OwnerCharacter->GetActiveMontageInstance();
			if (Montage && MontageInstance)
			{
				const int32 SectionID = Montage->GetSectionIndex(this->LoopBackToSection);
				float StartTime = 0.f;
				float EndTime = 0.f;
				Montage->GetSectionStartAndEndTime(SectionID, StartTime, EndTime);
				MontageInstance->SetPosition(StartTime); 
			}
		}
	}
}
