// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotifyState.h"
#include "ScreenRadialBlur.generated.h"

/**
 * 开始动态模糊
 */
UCLASS()
class THEAWAKENER_FO_API UScreenRadialBlur : public UAnimNotifyState
{
	GENERATED_BODY()
public:
	//模糊度，推荐0.01f-3.00f
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	float BlurDepth = 0.5f;

	virtual void NotifyBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float TotalDuration,
		const FAnimNotifyEventReference& EventReference) override;
};
