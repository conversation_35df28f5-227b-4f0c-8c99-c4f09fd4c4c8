// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotifyState.h"
#include "TakeOffOrLanding.generated.h"

/**
 * 原地起飞或者降落
 */
UCLASS()
class THEAWAKENER_FO_API UTakeOffOrLanding : public UAnimNotifyState
{
	GENERATED_BODY()
public:
	//飞行上升的的速度，每秒多少cm
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	float FlySpeed = 500;

	virtual void NotifyTick(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float FrameDeltaTime,
		const FAnimNotifyEventReference& EventReference) override;
};
