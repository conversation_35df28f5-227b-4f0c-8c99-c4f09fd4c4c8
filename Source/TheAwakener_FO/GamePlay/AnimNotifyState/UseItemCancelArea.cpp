// Fill out your copyright notice in the Description page of Project Settings.


#include "UseItemCancelArea.h"

#include "Components/SkeletalMeshComponent.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"

void UUseItemCancelArea::NotifyTick(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float FrameDeltaTime,
	const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyTick(MeshComp, Animation, FrameDeltaTime, EventReference);
	
	if (this->ActionCmd.IsEmpty()) return;
	
	const AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Me) return;

	if (Me->IsActionOccur(ActionCmd) == false) return;
	
	const FItemObj* UsingItem = UGameplayFuncLib::GetAwGameInstance()->RoleInfo.GetUsingItem();
	if (!UsingItem)
	{
		if (this->ActionWhileUseFailed.IsEmpty() == false)
		{
			Me->PreorderAction(ActionWhileUseFailed);
			return;
		}
	}
	FString ToUseAction = "";
	switch (UseMethod)
	{
	case EItemUseMethod::Enchant: ToUseAction = UsingItem->Model.OnEnchant.UseActionId; break;
	case EItemUseMethod::Use: ToUseAction = UsingItem->Model.OnUse.UseActionId; break;
	case EItemUseMethod::Throw: ToUseAction = UsingItem->Model.OnThrow.UseActionId; break;
	}
	if (ToUseAction.IsEmpty() == false)
	{
		Me->PreorderAction(ToUseAction);
	}
}
