// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Engine/DataTable.h"
#include "Animation/AnimNotifies/AnimNotifyState_Trail.h"
#include "CheckBuffAnimNotifyState_Trail.generated.h"


USTRUCT()
struct FAnimStateTrailData  :public  FTableRowBase
{
	GENERATED_BODY()
	/** The particle system to use for this trail. */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = Trail)
	TObjectPtr<UParticleSystem> PSTemplate;
	
	/** Name of the first socket defining this trail. */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = Trail)
	FName FirstSocketName;

	/** Name of the second socket defining this trail. */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = Trail)
	FName SecondSocketName;
	
	/** 
	Controls the way width scale is applied. In each method a width scale of 1.0 will mean the width is unchanged from the position of the sockets. A width scale of 0.0 will cause a trail of zero width.
	From Centre = Trail width is scaled outwards from the centre point between the two sockets.
	From First = Trail width is scaled outwards from the position of the first socket.
	From Second = Trail width is scaled outwards from the position of the Second socket.
	*/
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = Trail)
	TEnumAsByte<enum ETrailWidthMode> WidthScaleMode;

	/** Name of the curve to drive the width scale. */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = Trail)
	FName WidthScaleCurve;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = Trail)
	bool bRecycleSpawnedSystems = true;

	UPROPERTY(EditAnywhere, Category = Rendering)
	bool bRenderGeometry  = true;

	/** If true, render stars at each spawned particle point along the trail */
	UPROPERTY( EditAnywhere, Category = Rendering)
	bool bRenderSpawnPoints  = false;

	/** If true, render a line showing the tangent at each spawned particle point along the trail */
	UPROPERTY(EditAnywhere, Category = Rendering)
	bool bRenderTangents  = false;

	/** If true, render the tessellated path between spawned particles */
	UPROPERTY( EditAnywhere, Category = Rendering)
	bool bRenderTessellation = false;
};

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UCheckBuffAnimNotifyState_Trail : public UAnimNotifyState_Trail
{
	GENERATED_BODY()
public:
	
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FString> AbilityIdCheck; 
	
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FString> CheckBuffTags;
	
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	UDataTable* ParticleParameterData = nullptr;
	
	virtual void NotifyBegin(USkeletalMeshComponent * MeshComp, UAnimSequenceBase * Animation, float TotalDuration, const FAnimNotifyEventReference& EventReference) override;
	virtual void NotifyEnd(USkeletalMeshComponent * MeshComp, UAnimSequenceBase * Animation, const FAnimNotifyEventReference& EventReference) override;

	void OnParticleTrailBegin(USkeletalMeshComponent * MeshComp, UAnimSequenceBase * Animation, float TotalDuration, const FAnimNotifyEventReference& EventReference);
	void OnParticleTrailEnd(USkeletalMeshComponent * MeshComp, UAnimSequenceBase * Animation, const FAnimNotifyEventReference& EventReference);
};



