// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotifyState.h"
#include "BlendSpaceAnimIndex.generated.h"

/**
 * 在期间改变角色状态的BlendSpace的Anim所指向的Index
 * 比如你需要架盾的时候脚上动作不一样，就得用这个了
 */
UCLASS(BlueprintType)
class THEAWAKENER_FO_API UBlendSpaceAnimIndex : public UAnimNotifyState
{
	GENERATED_BODY()
public:
	//要把BlendSpace的index设成多少，过大会被当做0来使用的
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int ToIndex = 0;

	virtual void NotifyTick(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float FrameDeltaTime,
		const FAnimNotifyEventReference& EventReference) override;
	virtual void NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
		const FAnimNotifyEventReference& EventReference) override;
};
