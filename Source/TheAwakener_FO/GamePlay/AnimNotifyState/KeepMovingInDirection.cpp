// Fill out your copyright notice in the Description page of Project Settings.


#include "KeepMovingInDirection.h"

#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"

void UKeepMovingInDirection::NotifyBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float TotalDuration,
	const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyBegin(MeshComp, Animation, TotalDuration, EventReference);
	AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Me) return;
	Me->SetKeepMoving(true, this->DirectionMergeRule);
}

void UKeepMovingInDirection::NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyEnd(MeshComp, Animation, EventReference);
	AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Me) return;
	Me->SetKeepMoving(false, this->DirectionMergeRule);
}

