// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotifyState.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "ActionFallingCheck.generated.h"

/**
 * 一个Montage动作到Falling、Ground状态时候要进行的检查
 */
UCLASS()
class THEAWAKENER_FO_API UActionFallingCheck : public UAnimNotifyState
{
	GENERATED_BODY()
public:
	//当切换到Ground的时候，这个动作就结束了
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool TerminateOnHitGround = true;

	//当切换到Falling状态的时候，这个动作就结束了
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool TerminateOnFalling = true;
	
	/**
	 *没有达到HitGround之前，动作是否需要循环，到达这个区域的第一个tick就会导致循环
	 *如果这个动作TerminateOnFalling，那么这项写了也白写
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool FallingLoop = false;

	//Falling Loop 返回到哪一秒重播，要是这个时间点晚于当前段的位置，那就……
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FName LoopBackToSection;

	/**
	 * 所谓的Terminate，是跳转到某个Section吗？如果是，这就要有Section名字，如果不是，就是None
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FName TerminateToSection;

	virtual void NotifyTick(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float FrameDeltaTime,
		const FAnimNotifyEventReference& EventReference) override;
};
