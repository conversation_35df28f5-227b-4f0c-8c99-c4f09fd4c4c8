// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotifyState.h"
#include "TheAwakener_FO/GamePlay/Roguelike/BattleStyle/RogueBattleTag.h"
#include "CheckAnimNotfiyStateBuff.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UCheckAnimNotfiyStateBuff : public UAnimNotifyState
{
	GENERATED_BODY()
public:
	UCheckAnimNotfiyStateBuff();
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		TArray<FString> CheckBuffTags;
	
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		bool  bContainsAllTags = false;
	
	virtual void NotifyBegin(USkeletalMeshComponent * MeshComp, UAnimSequenceBase * Animation, float TotalDuration, const FAnimNotifyEventReference& EventReference) override;
	virtual void NotifyEnd(USkeletalMeshComponent * MeshComp, UAnimSequenceBase * Animation, const FAnimNotifyEventReference& EventReference) override;
private:
	   FString UID ="";
};
