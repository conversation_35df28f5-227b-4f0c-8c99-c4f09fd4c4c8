// Fill out your copyright notice in the Description page of Project Settings.


#include "CheckBuffAnimNotifyState_Trail.h"

#include "ParticleEmitterInstances.h"
#include "Particles/ParticleSystemComponent.h"
#include "Animation/AnimInstance.h"
#include "TheAwakener_FO/FunctionLibrary/CommonFuncLib.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"


typedef TInlineComponentArray<UParticleSystemComponent*, 8> ParticleSystemComponentArray;

static void GetCandidateSystems(USkeletalMeshComponent& MeshComp, ParticleSystemComponentArray& Components)
{
	if (AActor* Owner = MeshComp.GetOwner())
	{
		Owner->GetComponents(Components);
	}
	else
	{
		// No actor owner in some editor windows. Get PSCs spawned by the MeshComp.
		ForEachObjectWithOuter(&MeshComp, [&Components](UObject* Child)
		{
			if (UParticleSystemComponent* ChildPSC = Cast<UParticleSystemComponent>(Child))
			{
				Components.Add(ChildPSC);
			}
		}, false, RF_NoFlags, EInternalObjectFlags::Garbage);
	}
}


void UCheckBuffAnimNotifyState_Trail::NotifyBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
                                            float TotalDuration, const FAnimNotifyEventReference& EventReference)
{

	// ensure deprecated path is called because a call to Super is not made
	PRAGMA_DISABLE_DEPRECATION_WARNINGS
	Super::NotifyBegin(MeshComp, Animation, TotalDuration);
	PRAGMA_ENABLE_DEPRECATION_WARNINGS
	AAwCharacter* Character = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Character) return;
	if (!ParticleParameterData)
	{
		//Super::NotifyBegin(MeshComp,Animation,TotalDuration,EventReference);
		OnParticleTrailBegin(MeshComp,Animation,TotalDuration,EventReference);
	}
	else
	{
		for (auto Tag : CheckBuffTags)
		{
			bool bUseParentNotify = false;

			for (auto Buff : Character->CharacterObj.Buff)
			{
				if (Buff.Model.Tags.Contains(Tag))
				{
					if (AbilityIdCheck.Num() == 0)
					{
						bUseParentNotify = true;
						break;
					}
					else
					{
						if (UCommonFuncLib::ArrayContainsArray(Buff.Model.Tags, AbilityIdCheck, false))
						{
							bUseParentNotify = true;
							break;
						}
					}
				}
			}
			if (bUseParentNotify)
			{
				FName RowName = FName(Tag);
				FAnimStateTrailData* Data  = ParticleParameterData->FindRow<FAnimStateTrailData>(RowName,nullptr);
				if (Data)
				{
					PSTemplate = Data->PSTemplate;
					FirstSocketName = Data->FirstSocketName;
					SecondSocketName = Data->SecondSocketName;
					WidthScaleMode = Data->WidthScaleMode;
					WidthScaleCurve = Data->WidthScaleCurve;
					bRecycleSpawnedSystems = Data->bRecycleSpawnedSystems;
#if WITH_EDITORONLY_DATA
					bRenderGeometry = Data->bRenderGeometry;
					bRenderSpawnPoints = Data->bRenderSpawnPoints;
					bRenderTangents = Data->bRenderTangents;
					bRenderTessellation = Data->bRenderTessellation;
#endif
				}
				//Super::NotifyBegin(MeshComp,Animation,TotalDuration,EventReference);
				OnParticleTrailBegin(MeshComp,Animation,TotalDuration,EventReference);
			}
		}
	}
	
	Received_NotifyBegin(MeshComp, Animation, TotalDuration, EventReference);
	
}

void UCheckBuffAnimNotifyState_Trail::NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	AAwCharacter* Character = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Character) return;
	PRAGMA_DISABLE_DEPRECATION_WARNINGS
	Super::NotifyEnd(MeshComp, Animation);
	PRAGMA_ENABLE_DEPRECATION_WARNINGS
	//OnParticleTrailEnd(MeshComp,Animation,EventReference);
	
	if (!ParticleParameterData)
	{
		//Super::NotifyEnd(MeshComp,Animation,EventReference);
		OnParticleTrailEnd(MeshComp,Animation,EventReference);
	}
	else
	{
		for (auto Tag : CheckBuffTags)
		{
			bool bUseParentNotify = true;
			/*
			bool bUseParentNotify = false;
			for (auto Buff : Character->CharacterObj.Buff)
			{
				if (Buff.Model.Tags.Contains(Tag))
				{
					bUseParentNotify = true;
					break;
				}
			}
			*/
			if (bUseParentNotify)
			{
				FName RowName = FName(Tag);
				FAnimStateTrailData* Data  = ParticleParameterData->FindRow<FAnimStateTrailData>(RowName,nullptr);
				if (Data)
				{
					PSTemplate = Data->PSTemplate;
					FirstSocketName = Data->FirstSocketName;
					SecondSocketName = Data->SecondSocketName;
					WidthScaleMode = Data->WidthScaleMode;
					WidthScaleCurve = Data->WidthScaleCurve;
#if WITH_EDITORONLY_DATA
					bRecycleSpawnedSystems = Data->bRecycleSpawnedSystems;
					bRenderGeometry = Data->bRenderGeometry;
					bRenderSpawnPoints = Data->bRenderSpawnPoints;
					bRenderTangents = Data->bRenderTangents;
					bRenderTessellation = Data->bRenderTessellation;
#endif
				}
				//Super::NotifyEnd(MeshComp,Animation,EventReference);
				OnParticleTrailEnd(MeshComp,Animation,EventReference);
			}
		}
	}
	
	Received_NotifyEnd(MeshComp, Animation, EventReference);
}

void UCheckBuffAnimNotifyState_Trail::OnParticleTrailBegin(USkeletalMeshComponent * MeshComp, UAnimSequenceBase * Animation, float TotalDuration, const FAnimNotifyEventReference& EventReference)
{
	bool bError = ValidateInput(MeshComp);

	if (MeshComp->GetWorld()->GetNetMode() == NM_DedicatedServer)
	{
		return;
	}

	UParticleSystem* ParticleSystemTemplate = GetOverridenPSTemplate(MeshComp, Animation);
	if (ParticleSystemTemplate != nullptr)
	{
		PSTemplate = ParticleSystemTemplate;
	}

	if(PSTemplate == nullptr)
	{
		UE_LOG(LogTemp, Warning, TEXT("Trail Notify: Null PSTemplate for trail notify in anim: %s"), *GetPathNameSafe(Animation));
		return;
	}

	ParticleSystemComponentArray Children;
	GetCandidateSystems(*MeshComp, Children);

	float Width = GetCurveWidth(MeshComp);

	UParticleSystemComponent* RecycleCandidates[3] = {nullptr, nullptr, nullptr}; // in order of priority
	bool bFoundExistingTrail = false;
	for (UParticleSystemComponent* ParticleComp : Children)
	{
		if (ParticleComp->IsActive())
		{
			UParticleSystemComponent::TrailEmitterArray TrailEmitters;
			ParticleComp->GetOwnedTrailEmitters(TrailEmitters, this, false);

			bool bSame = PSTemplate == ParticleComp->Template && ParticleComp->GetOuter() == MeshComp;
			
			if (TrailEmitters.Num() > 0&&bSame)
			{
				// This has active emitters, we'll just restart this one.
				bFoundExistingTrail = true;
				//If there are any trails, ensure the template hasn't been changed. Also destroy the component if there are errors.
				//if (bError || (PSTemplate != ParticleComp->Template && ParticleComp->GetOuter() == MeshComp))
				if (bError || (PSTemplate != ParticleComp->Template && ParticleComp->GetOuter() == MeshComp))
				{
					//The PSTemplate was changed so we need to destroy this system and create it again with the new template. May be able to just change the template?
					ParticleComp->DestroyComponent();
				}
				else
				{
					for (FParticleAnimTrailEmitterInstance* Trail : TrailEmitters)
					{
						Trail->BeginTrail();
						Trail->SetTrailSourceData(FirstSocketName, SecondSocketName, WidthScaleMode, Width);

	#if WITH_EDITORONLY_DATA
						//Trail->SetTrailDebugData(bRenderGeometry, bRenderSpawnPoints, bRenderTessellation, bRenderTangents);
	#endif
					}
				}

				break;
			}
			
		}
		else if (ParticleComp->bAllowRecycling && !ParticleComp->IsActive())
		{
			// We prefer to recycle one with a matching template, and prefer one created by us.
			// 0: matching template, owned by mesh
			// 1: matching template, owned by actor
			// 2: non-matching template, owned by actor or mesh
			int32 RecycleIndex = 0;
			if (ParticleComp->Template == PSTemplate)
			{
				RecycleIndex = (ParticleComp->GetOuter() == MeshComp ? 0 : 1);
			}
			RecycleCandidates[RecycleIndex] = ParticleComp;
		}
	}

	if (!bFoundExistingTrail && !bError)
	{
		// Spawn a new component from PSTemplate, or recycle an old one.
		UParticleSystemComponent* RecycleComponent = (RecycleCandidates[0] ? RecycleCandidates[0] : (RecycleCandidates[1] ? RecycleCandidates[1] : RecycleCandidates[2]));
		UParticleSystemComponent* NewParticleComp = (RecycleComponent ? RecycleComponent : NewObject<UParticleSystemComponent>(MeshComp));
		NewParticleComp->bAutoDestroy = (RecycleComponent ? false : !bRecycleSpawnedSystems);
		NewParticleComp->bAllowRecycling = true;
		NewParticleComp->SecondsBeforeInactive = 0.0f;
		NewParticleComp->bAutoActivate = false;
		NewParticleComp->bOverrideLODMethod = false;
		NewParticleComp->SetRelativeScale3D_Direct(FVector(1.f));
		NewParticleComp->bAutoManageAttachment = true; // Let it detach when finished (only happens if not auto-destroying)
		NewParticleComp->SetAutoAttachParams(MeshComp, NAME_None);

		// When recycling we can avoid setting the template if set already.
		if (NewParticleComp->Template != PSTemplate)
		{
			NewParticleComp->SetTemplate(PSTemplate);
		}

		// Recycled components are usually already registered
		if (!NewParticleComp->IsRegistered())
		{
			NewParticleComp->RegisterComponentWithWorld(MeshComp->GetWorld());
		}

		NewParticleComp->AttachToComponent(MeshComp, FAttachmentTransformRules::KeepRelativeTransform);
		NewParticleComp->ActivateSystem(true);

		UParticleSystemComponent::TrailEmitterArray TrailEmitters;
		NewParticleComp->GetOwnedTrailEmitters(TrailEmitters, this, true);

		for (FParticleAnimTrailEmitterInstance* Trail : TrailEmitters)
		{
			Trail->BeginTrail();
			Trail->SetTrailSourceData(FirstSocketName, SecondSocketName, WidthScaleMode, Width);

#if WITH_EDITORONLY_DATA
			//Trail->SetTrailDebugData(bRenderGeometry, bRenderSpawnPoints, bRenderTessellation, bRenderTangents);
#endif
		}
	}
}

void UCheckBuffAnimNotifyState_Trail::OnParticleTrailEnd(USkeletalMeshComponent * MeshComp, UAnimSequenceBase * Animation, const FAnimNotifyEventReference& EventReference)
{
	if (MeshComp->GetWorld()->GetNetMode() == NM_DedicatedServer)
	{
		return;
	}

	ParticleSystemComponentArray Children;
	GetCandidateSystems(*MeshComp, Children);

	for (UParticleSystemComponent* ParticleComp : Children)
	{
		if (ParticleComp->IsActive())
		{
			UParticleSystemComponent::TrailEmitterArray TrailEmitters;
			ParticleComp->GetOwnedTrailEmitters(TrailEmitters, this, false);
			for (FParticleAnimTrailEmitterInstance* Trail : TrailEmitters)
			{
				Trail->EndTrail();
			}
		}
	}
}