// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotifyState.h"
#include "ExtendMoveLoop.generated.h"

/**
 * 延长移动距离的段，为了做到雪鬼兽滑道面前双刀的那个效果
 * 【注意】请把Begin放在Loop段之外
 */
UCLASS()
class THEAWAKENER_FO_API UExtendMoveLoop : public UAnimNotifyState
{
	GENERATED_BODY()
private:
	virtual void NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
		const FAnimNotifyEventReference& EventReference) override;
	virtual void NotifyBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float TotalDuration,
		const FAnimNotifyEventReference& EventReference) override;
	virtual void NotifyTick(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float FrameDeltaTime,
		const FAnimNotifyEventReference& EventReference) override;
public:
	//是否根据距离来延长，如果是，那么角色移动导致了延长距离归0之后就会结束
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	bool Distance = true;
	
	//延长最大距离为多少，
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	float MaxDistance = 0;

	//是否根据时间来延长，如果是，那么时间也会被当做一个结束条件
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	bool Duration = false;

	//延长最大时间是多少
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	float MaxDuration = 0;

	//在这段延长期内，多少范围内出现敌人就立即停下了
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	float FindEnemyDistance = 600;

	//如果需要延长，那么动画Loop到哪个Section
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString ExtendLoopAnimStartSection;

	//如果延长中发现目标或者结束之后，跳跃到哪个Section
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString ExtendEndJumpToSection;
};
