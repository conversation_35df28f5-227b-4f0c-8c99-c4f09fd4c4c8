// Fill out your copyright notice in the Description page of Project Settings.


#include "ActiveComponentInState.h"

#include "Components/SkeletalMeshComponent.h"


void UActiveComponentInState::NotifyBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float TotalDuration,
                                          const FAnimNotifyEventReference& EventReference)
{
	PRAGMA_DISABLE_DEPRECATION_WARNINGS
	Super::NotifyBegin(MeshComp, Animation, TotalDuration);
	PRAGMA_ENABLE_DEPRECATION_WARNINGS

	 TargetComponent = MeshComp->GetOwner()->GetComponentByClass(ComponetClass);
	if (IsValid(TargetComponent))
	{
		TargetComponent->Activate();
	}
}


void UActiveComponentInState::NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	PRAGMA_DISABLE_DEPRECATION_WARNINGS
	Super::NotifyEnd(MeshComp, Animation, EventReference);
	PRAGMA_ENABLE_DEPRECATION_WARNINGS
	if (IsValid(TargetComponent))
	{
		TargetComponent->Deactivate();
	}
}
