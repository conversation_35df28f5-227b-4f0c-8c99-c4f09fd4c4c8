// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotifyState.h"
#include "HitGroundDuck.generated.h"

/**
 * 如果落地就会蹲一下
 */
UCLASS()
class THEAWAKENER_FO_API UHitGroundDuck : public UAnimNotifyState
{
	GENERATED_BODY()
private:
	UPROPERTY()
	bool Ducked = false;
public:
	virtual void NotifyTick(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float FrameDeltaTime,
		const FAnimNotifyEventReference& EventReference) override;
};
