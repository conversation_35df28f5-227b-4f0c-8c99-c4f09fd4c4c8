// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotifyState.h"
#include "TheAwakener_FO/GamePlay/Characters/Battle/BattleStructs.h"
#include "JustDodge.generated.h"

/**
 * 临时将所有的CharacterHitBox设定为JustDodge，此时被命中会被当作JustDodge来处理
 * 若2段JustDodge发生重叠，则会出问题
 */
UCLASS(BlueprintType)
class THEAWAKENER_FO_API UJustDodge : public UAnimNotifyState
{
	GENERATED_BODY()
public:
	//JustDodge的信息
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FJustDodgeInfo JustDodgeInfo;

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FString> ActiveCharacterHitBoxId;
	
	virtual void NotifyBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float TotalDuration,
		const FAnimNotifyEventReference& EventReference) override;
	virtual void NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
		const FAnimNotifyEventReference& EventReference) override;
};
