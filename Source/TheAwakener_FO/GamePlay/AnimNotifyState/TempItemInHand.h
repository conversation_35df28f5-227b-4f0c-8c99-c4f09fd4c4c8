// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotifyState.h"
#include "TempItemInHand.generated.h"

/**
 * 临时为角色添加一些视觉物件在身上，名字虽然是“InHand”但未必是
 * 只是有一点，这个“一些物件”仅仅来自于当前正在使用的道具，即便他没有真的被使用
 * 比如你在攻击动作里面拉了这个NotifyState，攻击动作并不是吃药动作，但是因为Role下面的ItemObjs[FoucsItemIndex]是一瓶药水
 * 这时候就会在攻击动作时候，拿到这瓶药水的Model下的Appearance添加给角色了
 */
UCLASS()
class THEAWAKENER_FO_API UTempItemInHand : public UAnimNotifyState
{
	GENERATED_BODY()
public:
	virtual void NotifyBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float TotalDuration,
		const FAnimNotifyEventReference& EventReference) override;
	virtual void NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
		const FAnimNotifyEventReference& EventReference) override;
};
