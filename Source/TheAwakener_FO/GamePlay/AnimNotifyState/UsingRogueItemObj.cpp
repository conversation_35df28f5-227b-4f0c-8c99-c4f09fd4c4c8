// Fill out your copyright notice in the Description page of Project Settings.


#include "UsingRogueItemObj.h"

#include "Components/SkeletalMeshComponent.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"

void UUsingRogueItemObj::NotifyBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	float TotalDuration, const FAnimNotifyEventReference& EventReference)
{
	PRAGMA_DISABLE_DEPRECATION_WARNINGS
	Super::NotifyBegin(MeshComp, Animation,TotalDuration);
	PRAGMA_ENABLE_DEPRECATION_WARNINGS
	
	AAwCharacter* Character = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Character) return;

	const UAwAnimInstance* AnimInstance = Character->GetAwAnimInstance();
	if (!AnimInstance) return;

	if (Character->GetAwAnimInstance()->CurMontageUID ==UID||Character->GetAwAnimInstance()->CurMontageUID.IsEmpty())
	{
		return;
	}

	UID = Character->GetAwAnimInstance()->CurMontageUID;

	UAwRogueItemSubSystem* SubSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueItemSubSystem>();
	if (!SubSystem)
	{return;}
	auto PC = UGameplayFuncLib::GetPlayerControllerByComp(MeshComp);
	if (!PC)
	{
		UE_LOG(LogTemp, Error, TEXT("PlayerController Not Found in %s"), *MeshComp->GetOwner()->GetFullName());
		return;
	}
	SubSystem->OnUsingCurRogueItem(this->UseMethodTags,PC->GetLocalPCIndex());
	
}

void UUsingRogueItemObj::NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	PRAGMA_DISABLE_DEPRECATION_WARNINGS
	Super::NotifyEnd(MeshComp, Animation);
	PRAGMA_ENABLE_DEPRECATION_WARNINGS

	AAwCharacter* Character = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Character) return;

	const UAwAnimInstance* AnimInstance = Character->GetAwAnimInstance();
	if (!AnimInstance) return;

	bool IsPause = !Character->GetAwAnimInstance()->Montage_IsPlaying(nullptr);
	//保证Event是正常触发 非montage暂停-恢复等非正常因素触发
	if (IsPause&&UID == Character->GetAwAnimInstance()->CurMontageUID)
	{
		return;
	}

	UAwRogueItemSubSystem* SubSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueItemSubSystem>();
	if (!SubSystem)
	{return;}
	auto PC = UGameplayFuncLib::GetPlayerControllerByComp(MeshComp);
	if (!PC)
	{
		UE_LOG(LogTemp, Error, TEXT("PlayerController Not Found in %s"), *MeshComp->GetOwner()->GetFullName());
		return;
	}
	SubSystem->OnUsingCurRogueItemEnd(this->UseMethodTags,PC->GetLocalPCIndex());
}
