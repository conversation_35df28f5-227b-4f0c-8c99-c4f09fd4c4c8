// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotifyState.h"
#include "TheAwakener_FO/GamePlay/Roguelike/BattleStyle/AttackModifyByBattleStyleUpgrade.h"
#include "AnimRateModify.generated.h"

/**
 * 
 */
UCLASS(BlueprintType)
class THEAWAKENER_FO_API UAnimRateModify : public UAnimNotifyState
{
	GENERATED_BODY()
public:
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float AnimRate = 1;
	
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool bIsPowerAnim = false;
	
	virtual void NotifyBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float TotalDuration,
		const FAnimNotifyEventReference& EventReference) override;
	virtual void NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
		const FAnimNotifyEventReference& EventReference) override;
};
