// Fill out your copyright notice in the Description page of Project Settings.


#include "BeamEffect.h"

#include "Components/SkeletalMeshComponent.h"
#include "Kismet/GameplayStatics.h"
#include "Particles/ParticleSystem.h"
#include "Particles/ParticleSystemComponent.h"
#include "TheAwakener_FO/FunctionLibrary/CallFuncLib.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"

UBeamEffect::UBeamEffect()
{
	Attached = true;
	Scale = FVector(1.f);

#if WITH_EDITORONLY_DATA
	NotifyColor = FColor(192, 255, 99, 255);
#endif // WITH_EDITORONLY_DATA
}

void UBeamEffect::PostLoad()
{
	Super::PostLoad();
	
	RotationOffsetQuat = FQuat(RotationOffset);
}

#if WITH_EDITOR
void UBeamEffect::PostEditChangeProperty(FPropertyChangedEvent& PropertyChangedEvent)
{
	Super::PostEditChangeProperty(PropertyChangedEvent);

	if (PropertyChangedEvent.MemberProperty && PropertyChangedEvent.MemberProperty->GetFName() == GET_MEMBER_NAME_CHECKED(UBeamEffect, RotationOffset))
	{
		RotationOffsetQuat = FQuat(RotationOffset);
	}
}
#endif

void UBeamEffect::NotifyBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float TotalDuration,
	const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyBegin(MeshComp, Animation, TotalDuration, EventReference);
	
	
	if (PSTemplate)
	{
		if (PSTemplate->IsLooping())
		{
			UE_LOG(LogTemp, Warning, TEXT("Particle Notify: Anim '%s' tried to spawn infinitely looping particle system '%s'. Spawning suppressed."), *GetNameSafe(Animation), *GetNameSafe(PSTemplate));
		}

		if (Attached)
		{
			TheParticle = UGameplayStatics::SpawnEmitterAttached(PSTemplate, MeshComp, SocketName, LocationOffset, RotationOffset, Scale);
		}
		else
		{
			const FTransform MeshTransform = MeshComp->GetSocketTransform(SocketName);
			FTransform SpawnTransform;
			SpawnTransform.SetLocation(MeshTransform.TransformPosition(LocationOffset));
			SpawnTransform.SetRotation(MeshTransform.GetRotation() * RotationOffsetQuat);
			SpawnTransform.SetScale3D(Scale);
			TheParticle = UGameplayStatics::SpawnEmitterAtLocation(MeshComp->GetWorld(), PSTemplate, SpawnTransform);
		}

		if (TheParticle)
		{
			// calculate the end
			AAwCharacter* OwnerCharacter = Cast<AAwCharacter>(MeshComp->GetOwner());
			if (OwnerCharacter)
			{
				UFunction* Func = UCallFuncLib::GetUFunction(GetBeamEndPointFunc.ClassPath, GetBeamEndPointFunc.FunctionName);
				if (Func)
				{
					struct
					{
						AAwCharacter* Character;
						FVector OffSet;
						TArray<FString> Params;
						
						FVector Result;
					}FuncParam;
					FuncParam.Character = OwnerCharacter;
					FuncParam.OffSet = BeamEndOffSet;
					FuncParam.Params = GetBeamEndPointFunc.Params;
					this->ProcessEvent(Func, &FuncParam);
					const FVector EndPos = FuncParam.Result;
					TheParticle->SetBeamEndPoint(0, EndPos);
				}
			}

			FDurationParticleComp ParticleComp = FDurationParticleComp();
			ParticleComp.ThisParticle = TheParticle;
			ParticleComp.Duration = TotalDuration;
			if (UGameplayFuncLib::GetAwGameState())
				UGameplayFuncLib::GetAwGameState()->DurationParticleCompList.Add(ParticleComp);
		}
	}
	else
	{
		UE_LOG(LogTemp, Warning, TEXT("Particle Notify: Particle system is null for particle notify '%s' in anim: '%s'"), *GetNotifyName(), *GetPathNameSafe(Animation));
	}
}

void UBeamEffect::NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyEnd(MeshComp, Animation, EventReference);
#if WITH_EDITOR
	if (TheParticle)
		TheParticle->DestroyComponent();
#endif
}
