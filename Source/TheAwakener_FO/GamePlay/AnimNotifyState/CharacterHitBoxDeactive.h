// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotifyState.h"
#include "CharacterHitBoxDeactive.generated.h"

/**
 * 受击框在持续时间内会被关闭
 */
UCLASS(BlueprintType)
class THEAWAKENER_FO_API UCharacterHitBoxDeactive : public UAnimNotifyState
{
	GENERATED_BODY()
private:
	void DeactiveHitBoxes(USkeletalMeshComponent* MeshComp);
	
public:
	//受击框
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FString> CharacterHitBoxName;

	//是否是所有的，如果是所有的，那么CharacterHitBoxName填什么都一样了
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	bool AllCharacterHitBoxes = false;

	virtual void NotifyBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float TotalDuration,
		const FAnimNotifyEventReference& EventReference) override;
	virtual void NotifyTick(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float FrameDeltaTime,
		const FAnimNotifyEventReference& EventReference) override;
	virtual void NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
		const FAnimNotifyEventReference& EventReference) override;

};
