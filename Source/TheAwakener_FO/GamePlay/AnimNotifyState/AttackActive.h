// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotifyState.h"
#include "TheAwakener_FO/GamePlay/Characters/Battle/BattleStructs.h"
#include "TheAwakener_FO/GamePlay/Roguelike/BattleStyle/AttackModifyByBattleStyleUpgrade.h"
#include "AttackActive.generated.h"

/**
 * 这里是一个攻击段的AnimNotifyState，他的作用是告诉角色开始做伤害检查
 * 如果角色身上有记录某个攻击框的碰撞信息，我就认为是碰到了，那么在这里，就是判断伤不伤害等了
 */
UCLASS(BlueprintType)
class THEAWAKENER_FO_API UAttackActive : public UAnimNotifyState
{
	GENERATED_BODY()
public:
	/**
	 * 元素属性来源算法
	 * 在此处默认的元素值，如果筛选模式是Element，或者是Physical并且这里的AttackInfo.Elemental设置Physical就会导致向下搜索
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	EElementalPickMethod ElementSource;

	/**
	 * 如果ElementSource不是Set，那么取角色身上哪个部位的武器来参考？主手、副手、远程、暗器？
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	ESlotInWeaponObj ElementalInWeaponSlot = ESlotInWeaponObj::MainWeapon;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FString> AbilityIds;
	
	/**
	 *改变角色的动作值，如果动作值没有填写，那是打不出伤害的
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FOffenseInfo OffenseInfo;

private:
	FOffenseInfo NewOffenseInfo;

public:
	virtual void NotifyBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float TotalDuration,
		const FAnimNotifyEventReference& EventReference) override;
	virtual void NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
		const FAnimNotifyEventReference& EventReference) override;
};
