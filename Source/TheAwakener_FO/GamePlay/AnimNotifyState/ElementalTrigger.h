// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotifyState.h"
#include "ElementalTrigger.generated.h"

/**
 * 元素效果触发点
 * 在这个NotifyState开始时，会给角色添加指定的ElementalTriggerTag，结束时移除
 * 所以请小心Montage之间的AnimNotifyState的冲突
 */
UCLASS()
class THEAWAKENER_FO_API UElementalTrigger : public UAnimNotifyState
{
	GENERATED_BODY()
public:
	//要在这段期间被加上的ElementalTriggerTag
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FString> ElementalTriggerTags;

	virtual void NotifyBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float TotalDuration,
		const FAnimNotifyEventReference& EventReference) override;
	virtual void NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
		const FAnimNotifyEventReference& EventReference) override;
};
