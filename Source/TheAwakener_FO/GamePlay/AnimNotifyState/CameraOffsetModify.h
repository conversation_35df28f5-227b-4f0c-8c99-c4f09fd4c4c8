// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotifyState.h"
#include "TheAwakener_FO/GamePlay/Camera/AwCameraPosInfo.h"
#include "CameraOffsetModify.generated.h"

/**
 * 在这个时间段内，会给摇臂叠加一个镜头控制
 */
UCLASS()
class THEAWAKENER_FO_API UCameraOffsetModify : public UAnimNotifyState
{
	GENERATED_BODY()
private:
	FString UniqueId;
	
public:
	//摇臂的变化
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FAwCameraPosInfo Modifer;

	virtual void NotifyBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float TotalDuration,
		const FAnimNotifyEventReference& EventReference) override;
	virtual void NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
		const FAnimNotifyEventReference& EventReference) override;
};
