// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotifyState.h"
#include "SetCharacterGlow.generated.h"

class AAwCharacter;
/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API USetCharacterGlow : public UAnimNotifyState
{
	GENERATED_BODY()
public:
	/**
	 * @brief 过多久完全变色
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float InTime = 0.1f;
	/**
	 * @brief 过多久变回0
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float OutTime = 0.1f;
	/**
	 * 外发光颜色，正常值0~255对应0.0~1.0，因为是自发光所以值可以超过1.0以表示发光颜色强烈程度
	 * 默认的金色值为（23, 7, 0, 1）
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FLinearColor GlowColor = FLinearColor(23,7,0);
	
	virtual void NotifyBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float TotalDuration,
		const FAnimNotifyEventReference& EventReference) override;
	virtual void NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, const FAnimNotifyEventReference& EventReference) override;
};
