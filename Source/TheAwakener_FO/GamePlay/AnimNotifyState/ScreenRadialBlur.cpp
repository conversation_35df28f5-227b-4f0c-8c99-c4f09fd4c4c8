// Fill out your copyright notice in the Description page of Project Settings.


#include "ScreenRadialBlur.h"

#include "Components/SkeletalMeshComponent.h"
#include "Kismet/GameplayStatics.h"
#include "TheAwakener_FO/GameFramework/Base/AwGameModeBase.h"

void UScreenRadialBlur::NotifyBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float TotalDuration,
	const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyBegin(MeshComp, Animation, TotalDuration, EventReference);

	AGameModeBase* AMode = UGameplayStatics::GetGameMode(MeshComp);
	AAwGameModeBase* GMode = Cast<AAwGameModeBase>(AMode);
	if (GMode)
	{
		GMode->SetRadialBlur(this->BlurDepth, TotalDuration, false, false, 0);
	}
}
