// Fill out your copyright notice in the Description page of Project Settings.


#include "AimState.h"

#include "Components/SkeletalMeshComponent.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"


void UAimState::NotifyBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float TotalDuration,
	const FAnimNotifyEventReference& EventReference) 
{
	Super::NotifyBegin(MeshComp, Animation, TotalDuration, EventReference);

	UniqueID = FString::FromInt(GetUniqueID());
	
	AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Me) return;
	
	Me->SetCmdInAiming(Me->IsActionOccur(this->OpenActionCmdId, this->ActionCmdInputState));

	if(Hud != "" && Me->OwnerPlayerController)
	{
		const FString BpPath = UResourceFuncLib::GetBpAssetPath(UGameplayFuncLib::GetUiManager()->GetWbpPathById(Hud));
		UClass* WidgetClass = LoadClass<UUserWidget>(nullptr, *BpPath);
		CrossHair = CreateWidget<UUserWidget>(Me->OwnerPlayerController,WidgetClass);
		if(CrossHair)
			CrossHair->AddToViewport();
	}

	if (IsActiveAimBlendSpace)
		Me->PlayAimBlendSpace(AimBlendSpaceIndex);

	if (AimRotateLimitInfo.IsActive && Me->GetAwCameraComponent())
	{
		FAimRotateLimitInfo NewInfo = this->AimRotateLimitInfo;
		NewInfo.Uid = UniqueID;
		NewInfo.OriCameraRotate = Me->GetAwCameraComponent()->GetCameraRotate();
		NewInfo.IsActive = true;
		Me->GetAwCameraComponent()->AimRotateLimitInfo = NewInfo;
	}
}

void UAimState::NotifyTick(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float FrameDeltaTime,
	const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyTick(MeshComp, Animation, FrameDeltaTime, EventReference);

	AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Me) return;
	
	Me->SetCmdInAiming(Me->IsActionOccur(this->OpenActionCmdId, this->ActionCmdInputState));
}

void UAimState::NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyEnd(MeshComp, Animation, EventReference);
	
	AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Me) return;
	
	Me->SetCmdInAiming(false);
	Me->SetAimDirection();
	if(CrossHair)
		CrossHair->RemoveFromParent();

	Me->StopAimBlendSpace();

	if (AimRotateLimitInfo.IsActive && Me->GetAwCameraComponent() &&
		Me->GetAwCameraComponent()->AimRotateLimitInfo.Uid == UniqueID)
		Me->GetAwCameraComponent()->AimRotateLimitInfo.Empty();
}