// Fill out your copyright notice in the Description page of Project Settings.


#include "CmdCheckState.h"

#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "TheAwakener_FO/GamePlay/Roguelike/BattleStyle/RogueBattleStyleSubSystem.h"


void UCmdCheckState::NotifyTick(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float FrameDeltaTime,
                                const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyTick(MeshComp, Animation, FrameDeltaTime, EventReference);
	
	IsGotoLoop = true;
	
	AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Me) return;

	TArray<TTuple<FString, EAwInputState>> CheckInput;
	if (Cmd.Num() <= 0)
	{
		FActionInfo* Action = Me->CurrentAction() ;
		if (Action == nullptr) return;
		for (FString TheCmd : Action->Commands)
		{
			// CheckInput.Add(TTuple<FString, EAwInputState>(TheCmd, EAwInputState::Hold));
			if (CmdPress == true) CheckInput.Add(TTuple<FString, EAwInputState>(TheCmd, EAwInputState::Press));
			if (CmdHold == true) CheckInput.Add(TTuple<FString, EAwInputState>(TheCmd, EAwInputState::Hold));
		}
	}else
	{
		for (FString TheCmd : Cmd)
		{
			// CheckInput.Add(TTuple<FString, EAwInputState>(TheCmd, EAwInputState::Hold));
			if (CmdPress == true) CheckInput.Add(TTuple<FString, EAwInputState>(TheCmd, EAwInputState::Press));
			if (CmdHold == true) CheckInput.Add(TTuple<FString, EAwInputState>(TheCmd, EAwInputState::Hold));
		} 
	}

	const int LoopedCount = Me->GetActionComponent()->MontageLoopCount.Contains(UniqueID) ?
		                        Me->GetActionComponent()->MontageLoopCount[UniqueID] : 0;
	const bool CanLoop = MaxLoopNum <= 0 || LoopedCount < MaxLoopNum;
	
	if (CheckInput.Num() > 0 && Me->AnyActionOccur(CheckInput) && CanLoop)
	{
		if (GotoSectionWhileHoldingOrPressCmd.IsNone()) return;
		
		const UAnimMontage* Montage = Me->GetCurrentActiveMontage();
		FAnimMontageInstance* MontageInstance = Me->GetActiveMontageInstance();

		if (!Montage ) return;
		const int32 SectionID = Montage->GetSectionIndex(GotoSectionWhileHoldingOrPressCmd);
		if (SectionID != INDEX_NONE)
		{
			float StartTime = 0.f;
			float EndTime = 0.f;
			Montage->GetSectionStartAndEndTime(SectionID, StartTime, EndTime);
			MontageInstance->SetPosition(StartTime);
		}
	}else
	{
		if (GotoSectionWhileNotHoldingOrPressCmd.IsNone()) return;
		
		const UAnimMontage* Montage = Me->GetCurrentActiveMontage();
		FAnimMontageInstance* MontageInstance = Me->GetActiveMontageInstance();

		if (!Montage ) return;
		const int32 SectionID = Montage->GetSectionIndex(GotoSectionWhileNotHoldingOrPressCmd);
		if (SectionID != INDEX_NONE)
		{
			float StartTime = 0.f;
			float EndTime = 0.f;
			Montage->GetSectionStartAndEndTime(SectionID, StartTime, EndTime);
			MontageInstance->SetPosition(StartTime);
			IsGotoLoop = false;
		}
	}
}

void UCmdCheckState::NotifyBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float TotalDuration,
	const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyBegin(MeshComp, Animation, TotalDuration, EventReference);
	IsGotoLoop = false;
	UniqueID = FString::FromInt(GetUniqueID());
}

void UCmdCheckState::NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
                               const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyEnd(MeshComp, Animation, EventReference);
	if (IsGotoLoop)
	{
		const AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
		if (!Me) return;
		
		const UAnimMontage* Montage = Cast<UAnimMontage>(Animation);
		if (!Montage) return;
		const FAnimMontageInstance* MontageInstance = Me->GetAwAnimInstance()->GetActiveInstanceForMontage(Montage);
		if (!MontageInstance) return;
		
		if (MontageInstance->IsPlaying())
		{
			if (Me->GetActionComponent()->MontageLoopCount.Contains(UniqueID))
				Me->GetActionComponent()->MontageLoopCount[UniqueID] += 1;
			else
				Me->GetActionComponent()->MontageLoopCount.Add(UniqueID, 1);
		}
	}
}
