// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotifyState.h"
#include "ModifyRootMotionRate.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UModifyRootMotionRate : public UAnimNotifyState
{
	GENERATED_BODY()

private:
	FString UniqueID;
	
public:
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float ModifyRate = 1.0f;
	
	virtual void NotifyBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float TotalDuration,
		const FAnimNotifyEventReference& EventReference) override;

	virtual void NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
		const FAnimNotifyEventReference& EventReference) override;
};
