// Fill out your copyright notice in the Description page of Project Settings.


#include "HideCharacterMeshAndCollisionState.h"

#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"

void UHideCharacterMeshAndCollisionState::NotifyBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	float TotalDuration, const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyBegin(MeshComp, Animation, TotalDuration, EventReference);
	
	AAwCharacter* Character = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Character) return;

	Character->HideMeshAndCollision(true);
}

void UHideCharacterMeshAndCollisionState::NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyEnd(MeshComp, Animation, EventReference);
	
	AAwCharacter* Character = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Character) return;

	Character->HideMeshAndCollision(false);
}
