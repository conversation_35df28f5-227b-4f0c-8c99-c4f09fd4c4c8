// Fill out your copyright notice in the Description page of Project Settings.


#include "SetMoveAndRotate.h"

#include "Components/SkeletalMeshComponent.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"

void UActionMoveAndRotate::NotifyBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float TotalDuration,
	const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyBegin(MeshComp, Animation, TotalDuration, EventReference);
	
	AAwCharacter* Character = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (Character == nullptr) return;
	
	Character->SetInputAcceptance(FVector(MoveSpeedTimes, MoveSpeedTimes, 0), RotateTimes);
	Character->SetActionMoveSpeedLevelLimit(this->SetSpdLvOn, this->SetToSpdLv);
	
	Character->GetMoveComponent()->SetUseRootMotionRotation(UseRootMotionRotation);
}

void UActionMoveAndRotate::NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyEnd(MeshComp, Animation, EventReference);

	AAwCharacter* Character = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (Character == nullptr) return;

	//UKismetSystemLibrary::PrintString(Character, FString("Stop Setting Acceptance:: ").Append(FString::FromInt(this->GetUniqueID())));
	Character->SetInputAcceptance(FVector::ZeroVector, 0.f);
	Character->SetActionMoveSpeedLevelLimit(false, 0);

	Character->GetMoveComponent()->SetUseRootMotionRotation(false);
}