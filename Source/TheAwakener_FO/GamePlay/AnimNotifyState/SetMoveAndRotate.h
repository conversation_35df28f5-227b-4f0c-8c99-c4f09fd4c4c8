// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotifyState.h"
#include "SetMoveAndRotate.generated.h"

/**
 * 设置动作中的移动、旋转倍率的条子
 */
UCLASS()
class THEAWAKENER_FO_API UActionMoveAndRotate : public UAnimNotifyState
{
	GENERATED_BODY()
	
	
public:
	//可以接受移动指令的倍率，1.00f=100%
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float MoveSpeedTimes;

	//可以接受转身指令的倍率，1.00f=100%
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float RotateTimes;

	//当MoveSpeedTimes > 0时，是否需要启用脚部动作（下半身融合的blendspace会走路）
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool SetSpdLvOn = false;

	//如果要设置下半身融合，那么融合的动作的“移动速度”应该是几档？（设置为这个值，而非最大这个值）
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int SetToSpdLv = 0;

	//是否使用动画原来的RootMotion数据的旋转值
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool UseRootMotionRotation = false;

	virtual void NotifyBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float TotalDuration,
		const FAnimNotifyEventReference& EventReference) override;
	virtual void NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
		const FAnimNotifyEventReference& EventReference) override;
};