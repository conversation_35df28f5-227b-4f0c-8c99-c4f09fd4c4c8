
#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotifyState.h"
#include "TheAwakener_FO/GamePlay/AOE/AWAoeBase.h"

#include "CreateBeam.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UCreateBeam : public UAnimNotifyState
{
	GENERATED_BODY()

	UPROPERTY()
	AAWAoe* BeamAOE;

public:
	
	//BeamAOE的Id
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString AOEId;

	//实际时间跟随实际State长度
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	float LifeSpan = 99999;
	
	UPROPERTY(BlueprintReadOnly, EditAnywhere)
	FString TweenFunc;
	
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FTransform OffsetTransform = FTransform();

	// Should attach to the bone/socket
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="AnimNotify")
	bool Attached = true; 	//~ Does not follow coding standard due to redirection from BP

	// SocketName to attach to
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AnimNotify")
	FName SocketName;
	
	virtual void NotifyTick(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float FrameDeltaTime,
		const FAnimNotifyEventReference& EventReference) override;

	virtual void NotifyBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
		float TotalDuration, const FAnimNotifyEventReference& EventReference) override;
	
	virtual void NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
		const FAnimNotifyEventReference& EventReference) override;
};
