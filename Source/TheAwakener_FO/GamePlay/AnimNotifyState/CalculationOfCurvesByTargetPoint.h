// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotifyState.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "CalculationOfCurvesByTargetPoint.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UCalculationOfCurvesByTargetPoint : public UAnimNotifyState
{
	GENERATED_BODY()

	
public:
	//抛物线期望长度
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float ExpectedLength = 0;

	//抛物线期望高度
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float ExpectedHeight = 0;

	//抛物线期望高度最小值
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float ExpectedHeightMin = 0;

	//抛物线期望高度最大值
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float ExpectedHeightMax = 0;

	//抛物线水平速度是否固定
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool bRegularHorizontalSpeed = false;

	//抛物线水平速度
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float ExpectedHorizontalSpeed = 0;

	//抛物线水平速度最小值
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float ExpectedHorizontalSpeedMin = 0;
	
	//抛物线水平速度最大值
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float ExpectedHorizontalSpeedMax = 0;
	
	//抛物线高度长度比
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float HeightLengthRatio = 1;

	//最大阻力速度比
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float DragSpeedPer = 1;

	//阻力生效区间
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float DragLenPer = 1;

	//
	float TimeElapsed = 0;

	FTransform OriginTransform;

	float ActualHorizontalSpeed = 0;
	
	virtual void NotifyBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float TotalDuration, const FAnimNotifyEventReference& EventReference) override;
    virtual void NotifyTick(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float FrameDeltaTime, const FAnimNotifyEventReference& EventReference) override;
    virtual void NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, const FAnimNotifyEventReference& EventReference) override;
private:
	FString UID ="";
};
