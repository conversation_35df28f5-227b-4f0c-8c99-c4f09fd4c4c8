// Fill out your copyright notice in the Description page of Project Settings.


#include "ActionBreakCheck.h"

void UActionBreakCheck::NotifyTick(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float FrameDeltaTime,
	const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyTick(MeshComp, Animation, FrameDeltaTime, EventReference);

	AAwCharacter* OwnerCharacter = Cast<AAwCharacter>(MeshComp->GetOwner());
	if(OwnerCharacter == nullptr) return;
	bool bInBreak = OwnerCharacter->GetBreakSystemComponent()->bInBreak;
	if(bInBreak == false)
	{
		if (!BreakEndToSection.IsNone())
		{
			UAnimMontage* Montage = OwnerCharacter->GetCurrentActiveMontage();
			FAnimMontageInstance* MontageInstance = OwnerCharacter->GetActiveMontageInstance();
			if (Montage && MontageInstance)
			{
				const int32 SectionID = Montage->GetSectionIndex(this->BreakEndToSection);
				if (SectionID == INDEX_NONE) OwnerCharacter->StopCurrentAction(true);
				float StartTime = 0.f;
				float EndTime = 0.f;
				Montage->GetSectionStartAndEndTime(SectionID, StartTime, EndTime);
				MontageInstance->SetPosition(StartTime); 
			}else
			{
				OwnerCharacter->StopCurrentAction(true);
			}
		}
	}
}
