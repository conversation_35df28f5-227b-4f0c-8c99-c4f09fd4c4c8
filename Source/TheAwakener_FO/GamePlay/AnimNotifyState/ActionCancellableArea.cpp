// Fill out your copyright notice in the Description page of Project Settings.


#include "ActionCancellableArea.h"

#include "Components/SkeletalMeshComponent.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"

void UActionCancellableArea::NotifyBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float TotalDuration,
	const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyBegin(MeshComp, Animation, TotalDuration, EventReference);
	
	const AAwCharacter* Character = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Character || Character->BeOffendedRec.Num() < this->ActiveAfterBeOffended) return;
	
	MontageAction = Character->GetActionComponent()->CurrMontageAction();

	Character->AddCancelTag(this->IsStateAnim == true ? ECancelTagType::State : ECancelTagType::Montage, this->CancelPointIndex, 0, MontageAction);
}

void UActionCancellableArea::NotifyTick(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float FrameDeltaTime,
	const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyTick(MeshComp, Animation, FrameDeltaTime, EventReference);

	if (this->IsStateAnim == false) return;
	
	const AAwCharacter* Character = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Character || Character->BeOffendedRec.Num() < this->ActiveAfterBeOffended) return;

	Character->AddCancelTag(ECancelTagType::State, this->CancelPointIndex, 0, MontageAction);
}


void UActionCancellableArea::NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyEnd(MeshComp, Animation, EventReference);

	const AAwCharacter* Character = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Character) return;
	if (this->IsStateAnim == true)
	{
		Character->RemoveStateCancelTag(CancelPointIndex);
	}else
	{
		Character->RemoveMontageCancelTag(CancelPointIndex, MontageAction);
	}
}