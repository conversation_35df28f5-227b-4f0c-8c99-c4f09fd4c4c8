// Fill out your copyright notice in the Description page of Project Settings.


#include "AimAssist.h"

#include "Components/SkeletalMeshComponent.h"
#include "Kismet/KismetMathLibrary.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"

AAwCharacter* GetClosestTarget(AAwCharacter* Me ,float CheckDis, float CheckDregee, bool bDregeeFirst)
{
	if (!Me) return nullptr;
	FVector2D MoveDir = Me->GetCmdComponent()->GetMoveDir();
	MoveDir.Normalize();
	if(MoveDir.Equals(FVector2D::ZeroVector))
		MoveDir = FVector2D(Me->GetActorForwardVector());
	//获取符合条件的Enemy
	AAwGameState* GameState = UGameplayFuncLib::GetAwGameState();
	if(!GameState) return nullptr;
	AAwCharacter* MinCha = nullptr;
	float MinParam = 0;
	for(auto CurCharacter : GameState->AllCharacters)
	{
		if(Me->IsEnemy(CurCharacter.Key) && !CurCharacter.Key->Dead())
		{
			float Dis = FVector::Dist(CurCharacter.Key->GetActorLocation(),Me->GetActorLocation());
			if(Dis > CheckDis) continue;
			FVector2D ToChaDir = FVector2D(CurCharacter.Key->GetActorLocation() - Me->GetActorLocation());
			ToChaDir.Normalize();
			float Degree = FMath::Abs(UKismetMathLibrary::DegAcos(UKismetMathLibrary::DotProduct2D(MoveDir, ToChaDir)));
			if(Degree > CheckDregee) continue;
			
			if(!MinCha)
			{
				MinCha = CurCharacter.Key;
				if(bDregeeFirst)
					MinParam = Degree;
				else
					MinParam = Dis;
				continue;
			}
			if(bDregeeFirst)
			{
				if(Degree < MinParam)
				{
					MinCha = CurCharacter.Key;
					MinParam = Degree;
				}
			}
			else
			{
				if(Dis < MinParam)
				{
					MinCha = CurCharacter.Key;
					MinParam = Dis;
				}
			}
		}
	}
	return MinCha;
}

void UAimAssist::NotifyBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float TotalDuration,
	const FAnimNotifyEventReference& EventReference) 
{
	Super::NotifyBegin(MeshComp, Animation, TotalDuration, EventReference);
	
	AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Me) return;
	UAwCameraComponent* CameraComponent = Me->GetAwCameraComponent();
	if (IsValid(CameraComponent)&&IsValid(CameraComponent->GetOnLockTarget().Get()))
	{
		TargetActor = CameraComponent->GetOnLockTarget().Get();
		HasLockTarget= true;
		return;
	}
	TargetActor = GetClosestTarget(Me, AssistDistanceRange, AssistDegreeRange, DegreeFirst);
	HasLockTarget = false;
}

void UAimAssist::NotifyTick(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float FrameDeltaTime,
	const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyTick(MeshComp, Animation, FrameDeltaTime, EventReference);
	
	AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Me) return;
	if(!TargetActor)
		TargetActor = GetClosestTarget(Me, AssistDistanceRange, AssistDegreeRange, DegreeFirst);
	if(!TargetActor)
		return;
	FVector TargetDir = TargetActor->GetActorLocation() - Me->GetActorLocation();
	TargetDir.Normalize();
	
	// 辅助瞄准去除相机辅助
	/*
	// --- rotate Camera -----
	UAwCameraComponent* CameraComponent = Me->GetAwCameraComponent();
	if (CameraComponent && Me->OwnerPlayerController->GetInputCameraDir().IsNearlyZero() && AssistCameraSpeed>0 &&
		URogueGameSetting::GetRogueGameSettings() && URogueGameSetting::GetRogueGameSettings()->GetAimAssist())
	{
		if (!HasLockTarget)
		{
			const FRotator OriRotator = CameraComponent->GetCameraRotate();
			const FRotator ToRotator = FMath::RInterpTo(OriRotator, TargetDir.Rotation(), FrameDeltaTime, 10);
			float AddYaw = ToRotator.Yaw - OriRotator.Yaw; 
		
			if(AddYaw >= 180) AddYaw -= 360;
			else if(AddYaw <= -180) AddYaw += 360;
		
			AddYaw = FMath::Clamp<float>(AddYaw, -5, 5);
		
			//UKismetSystemLibrary::PrintString(Me, "_AddYaw_1:"+ FString::SanitizeFloat(AddYaw));
		
			CameraComponent->AddYawInput(AddYaw);
		}
	}
	// --- --- ---
	*/
	
	// --- rotate Character ---
	if(URogueGameSetting::GetRogueGameSettings() && URogueGameSetting::GetRogueGameSettings()->GetAimAssist())
	{
		const FRotator TargetRot = TargetDir.Rotation();
		const FRotator CurRot = Me->GetActorRotation();

		float FinalAssistRotSpeed = AssistRotSpeed;
		
		float OffsetYaw = TargetRot.Yaw - CurRot.Yaw;
		if(OffsetYaw >= 180) OffsetYaw -= 360;
		else if(OffsetYaw <= -180) OffsetYaw += 360;

		if (!HasLockTarget)
		{
			if(FMath::Abs(OffsetYaw) < 5 || FMath::Abs(OffsetYaw) > AssistDegreeRange)
				return;
		}
		else if(URogueGameSetting::GetRogueGameSettings()->GetAimAssist())
		{
			if(FMath::Abs(OffsetYaw) < 180 * FrameDeltaTime * AssistRotSpeedInLock*2)
			{
				Me->SetFixRotation(TargetRot.Yaw, 360);
				return;
			}
			FinalAssistRotSpeed = AssistRotSpeedInLock;
		}
		float TargetYaw = 0;
		if(OffsetYaw > 0)
			TargetYaw = CurRot.Yaw + 180 * FrameDeltaTime * FinalAssistRotSpeed;
		else if(OffsetYaw < 0)
			TargetYaw = CurRot.Yaw - 180 * FrameDeltaTime * FinalAssistRotSpeed;
		float UnderDegree =  HasLockTarget?360:AssistDegreeRange ;
		Me->SetFixRotation(TargetYaw, UnderDegree);
	}
	// --- --- ---
}

void UAimAssist::NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyEnd(MeshComp, Animation, EventReference);
	
	AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Me) return;
	Me->StopFixRotation();
}