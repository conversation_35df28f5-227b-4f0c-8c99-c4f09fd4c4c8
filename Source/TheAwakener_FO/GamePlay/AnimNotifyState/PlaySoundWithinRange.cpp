// Fill out your copyright notice in the Description page of Project Settings.


#include "PlaySoundWithinRange.h"

#include "Components/AudioComponent.h"
#include "Components/SkeletalMeshComponent.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"

void UPlaySoundWithinRange::NotifyBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
                                        float TotalDuration, const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyBegin(MeshComp, Animation, TotalDuration, EventReference);

	AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Me) return;

	TArray<UActorComponent*> TempActor;
	Me->GetComponents(TempActor);
	
	for (UActorComponent* Actor : TempActor)
	{
		const FString Temp = Actor->GetName();
		if(Temp == AudioComponentName)
		{
			AudioComponent = Cast<UAudioComponent>(Actor);
		}
	}

	if(AudioComponent)
	{
		if(BeginSound)
		{
			AudioComponent->SetSound(BeginSound);
			AudioComponent->Play();
		}
		else if(LoopSound)
		{
			AudioComponent->SetSound(LoopSound);
			AudioComponent->Play();
		}
	}
	
	
}

void UPlaySoundWithinRange::NotifyTick(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	float FrameDeltaTime, const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyTick(MeshComp, Animation, FrameDeltaTime, EventReference);

	if(AudioComponent&&!AudioComponent->IsPlaying())
	{
		if(!LoopSound) return;
		AudioComponent->SetSound(LoopSound);
		AudioComponent->Play();
	}
	
}

void UPlaySoundWithinRange::NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyEnd(MeshComp, Animation, EventReference);

	
	if(AudioComponent)
	{
		AudioComponent->Stop();
		if(EndSound)
		{
			AudioComponent->SetSound(EndSound);
			AudioComponent->Play();
		}
	}
}
