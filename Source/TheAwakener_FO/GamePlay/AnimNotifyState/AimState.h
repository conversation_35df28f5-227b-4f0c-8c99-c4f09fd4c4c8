// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "AimRotateLimitInfo.h"
#include "Animation/AnimNotifies/AnimNotifyState.h"
#include "TheAwakener_FO/GameFramework/Input/EnumCmd.h"
#include "AimState.generated.h"

/**
 * 允许开启瞄准状态
 * 在这段时间内，如果保持当前要检查的Action对应的Cmd存在，就会开启瞄准状态
 */
UCLASS(BlueprintType)
class THEAWAKENER_FO_API UAimState : public UAnimNotifyState
{
	GENERATED_BODY()
private:
	UPROPERTY()
	FString UniqueID;
	
public:
	//开启瞄准状态的Action，(类似Action1之类的，具体请参看ActionCmd的json），若没有填写这个就会让这个Notify无效，
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString OpenActionCmdId;

	//开启瞄准时候这个ActionCmd所处的按键状态
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	EAwInputState ActionCmdInputState;

	//开启瞄准时候启用的HUD
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString Hud;

	UPROPERTY()
	UUserWidget* CrossHair;

	// Rotate In Aim

	// 是否启用瞄准的 BlendSpace
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="Rotate In Aim")
	bool IsActiveAimBlendSpace = false;
	
	// 启用的瞄准BlendSpace的Index
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="Rotate In Aim")
	int AimBlendSpaceIndex = 0;

	// 瞄准旋转限制的信息
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="Rotate In Aim")
	FAimRotateLimitInfo AimRotateLimitInfo;
	
	virtual void NotifyBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float TotalDuration,
	                         const FAnimNotifyEventReference& EventReference) override;
	virtual void NotifyTick(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float FrameDeltaTime,
		const FAnimNotifyEventReference& EventReference) override;
	virtual void NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
		const FAnimNotifyEventReference& EventReference) override;
};
