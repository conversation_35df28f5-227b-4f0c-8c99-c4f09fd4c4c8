// Fill out your copyright notice in the Description page of Project Settings.


#include "MontageSpecEvent.h"

#include "Components/SkeletalMeshComponent.h"
#include "TheAwakener_FO/FunctionLibrary/CallFuncLib.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"


void UMontageSpecEvent::NotifyBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float TotalDuration,
	const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyBegin(MeshComp, Animation, TotalDuration, EventReference);
	if (!OwnerCharacter) OwnerCharacter = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!OwnerCharacter) OwnerCharacter = Cast<AAwCharacter>(MeshComp);
	if (!OwnerCharacter || OwnerCharacter->Dead()) return;
	DoPreorderAction();
} 

void UMontageSpecEvent::NotifyTick(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float FrameDeltaTime,
	const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyTick(MeshComp, Animation, FrameDeltaTime, EventReference);
	if (!OwnerCharacter) OwnerCharacter = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!OwnerCharacter) OwnerCharacter = Cast<AAwCharacter>(MeshComp);
	if (!OwnerCharacter || OwnerCharacter->Dead()) return;
	DoPreorderAction();
}

void UMontageSpecEvent::NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyEnd(MeshComp, Animation, EventReference);
	if (!OwnerCharacter) OwnerCharacter = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!OwnerCharacter) OwnerCharacter = Cast<AAwCharacter>(MeshComp);
	if (!OwnerCharacter || OwnerCharacter->Dead()) return;
	DoPreorderAction();
}

void UMontageSpecEvent::DoPreorderAction(FActionParam Param)
{
	if (LoopCheck.IsEmpty()) return;

	const FJsonFuncData FuncData = UCallFuncLib::StringToJsonFuncData(LoopCheck);
	UFunction* Func = UCallFuncLib::JsonFuncToUFunc(FuncData);
	if (IsValid(Func) == true)
	{
		struct {
			AAwCharacter* Cha;
			FActionInfo Act;
			UMontageSpecEvent* LoopC;
			TArray<FString> Param;
				
			FString Result;
		} FuncParam;
			
		FuncParam.Cha = OwnerCharacter;
		FuncParam.Act = *OwnerCharacter->CurrentAction();
		FuncParam.LoopC = this;
		FuncParam.Param = FuncData.Params;
				
		OwnerCharacter->ProcessEvent(Func, &FuncParam);
		const FString ActionId = FuncParam.Result;

		if (ActionId.IsEmpty() == false)
		{
			OwnerCharacter->PreorderAction(ActionId, Param);
		}
	}
}