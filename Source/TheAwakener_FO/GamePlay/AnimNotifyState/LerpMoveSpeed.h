// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotifyState.h"
#include "LerpMoveSpeed.generated.h"

/**
 * 可以输入的移动速度倍率逐渐提升或者逐渐下降
 * 【注意1】如果下一段的Begin在这一段的End之前，会出现问题，UE傻屌设定导致，所以请自觉遵守规则
 * 【注意2】当与SetMoveAndRotate共存时，可能存在不可思议的问题，尤其当对方设置了移动速度允许倍率时，请慎重
 */
UCLASS()
class THEAWAKENER_FO_API ULerpMoveSpeed : public UAnimNotifyState
{
	GENERATED_BODY()
public:
	//起始速度倍率（0以下后果自负）
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	float StartMoveSpeedTimes = 0;

	//最终速度倍率（0以下后果自负），若这个小于Start，则会减速
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	float EndMoveSpeedTimes = 1;

	//改变这个所使用的Ease函数
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString EaseFunc;

	virtual void NotifyBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float TotalDuration,
		const FAnimNotifyEventReference& EventReference) override;
	virtual void NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
		const FAnimNotifyEventReference& EventReference) override;
};
