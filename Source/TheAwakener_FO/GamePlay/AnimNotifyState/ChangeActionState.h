// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotify.h"
#include "TheAwakener_FO/GamePlay/Characters/Action/CharacterActionState.h"
#include "ChangeActionState.generated.h"


UCLASS(BlueprintType)
class THEAWAKENER_FO_API UChangeActionState : public UAnimNotify
{
	GENERATED_BODY()
public:
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	ECharacterActionState TargetState = ECharacterActionState::Ground;
	virtual void Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, const FAnimNotifyEventReference& EventReference) override;
};
