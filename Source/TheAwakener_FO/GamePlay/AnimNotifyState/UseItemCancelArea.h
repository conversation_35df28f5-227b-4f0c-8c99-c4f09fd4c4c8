// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotifyState.h"
#include "TheAwakener_FO/GamePlay/Item/ItemObj.h"
#include "UObject/Object.h"
#include "UseItemCancelArea.generated.h"

/**
 * 使用道具来Cancel动作
 * 在一个Montage的这个段，只要按下对应的按钮（必须为Press），就会“用”道具
 * 其实就是根据道具的使用方式，切换了一个动作
 */
UCLASS()
class THEAWAKENER_FO_API UUseItemCancelArea : public UAnimNotifyState
{
	GENERATED_BODY()
private:
	virtual void NotifyTick(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float FrameDeltaTime,
		const FAnimNotifyEventReference& EventReference) override;
public:
	//按什么Action对应的Command可以触发这个，如果不填可是不会触发的
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	FString ActionCmd = FString();

	//使用这个道具的方式
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	EItemUseMethod UseMethod = EItemUseMethod::Use;

	//如果无法使用道具，那么Preorder这个Id动作，如果不填写或者动作找不到，就不会Preorder
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString ActionWhileUseFailed = FString();
};
