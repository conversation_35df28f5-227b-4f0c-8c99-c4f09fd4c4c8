// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/FunctionLibrary/DataFuncLib.h"
#include "AwQuest.generated.h"

struct FAwQuest;
/**
 * 
 */
UENUM(BlueprintType)
enum class EQuestType :uint8
{
	Main,//主线任务
	Branch,//支线任务
	Hiden //隐藏任务

};

UENUM(BlueprintType)
enum class EQuestState :uint8
{
	InActive,//未激活
	Active,//激活 未接取
	Doing, //接取 执行中
	Finish //完成
};

//任务目标类型
UENUM(BlueprintType)
enum class EQuestTargetType :uint8
{
	Kill,//击杀
	Protect, //保护
	Collect, //收集
	Cost, //消耗 包括使用道具
	Interact,//交互
	TimeLimit,//时限
	Trigger //触发
};
UENUM(BlueprintType)
enum class ETargetUIType :uint8
{
	Text,
	Progress, 
	CountDown 
};

USTRUCT(BlueprintType)
struct FAwTargetKeyWordGroup
{
	GENERATED_BODY()
	// Key 为 keyword  value为 TargetFullId  同一个FullID可能存在于不同的keyWord Group
		TMap<FString,TArray<FString>> QuestTargetGroup;
};

//任务目标
USTRUCT(BlueprintType)
struct FAwQuestTarget
{
	GENERATED_BODY()
	//目标的ID 同时作为名字
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "QuestTarget")
		FString Id = "";
	//目标的描述
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "QuestTarget")
		FString Desc = "";
	//任务目标类型
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "QuestTarget")
		EQuestTargetType TargetType = EQuestTargetType::Kill;
	//目标UI类型
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "QuestTarget")
		ETargetUIType UIType = ETargetUIType::Text;

	//任务目标关键字
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "QuestTarget")
		TArray<FString> TargetKeyWords;

	//任务目标进度check函数
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "QuestTarget")
		FJsonFuncData ProgressCheckFun;
	
	//失败条件
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "QuestTarget")
		TArray<FJsonFuncData> FailedConditions;
	//初始进度 感觉很少能用到
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "QuestTarget")
		int StartProgress = 0;
	//目标进度
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "QuestTarget")
		int MaxProgress = 100;
	//当前进度
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "QuestTarget")
		int CurProgress = 0;
	
	//可加可减 通用修改
	void ModfiyQuestTargetProgress(int AddProgress = 1);

	//特定Check  
	void CheckProgress(bool& bNeedModfiy);
	//重置
	void ResetQuestTargetProgress();
	//目标是否完成
	bool IsQuestTargetComplete();
	//目标是否失败
	bool IsQuestTargetFailed();
public:
	static FAwQuestTarget FromJson(TSharedPtr<FJsonObject> JsonObj);
};



//任务阶段
USTRUCT(BlueprintType)
struct FAwQuestStage
{
	GENERATED_BODY()
	//阶段的ID 同时作为名字
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "QuestStage")
	FString Id;

	//阶段所属地图
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "QuestStage")
	FString StageLevel;
	
	//阶段目标
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "QuestStage")
	TMap<FString, FAwQuestTarget >TargetList;

	//阶段变化函数 比如 阶段一完成进入阶段二 或者 阶段一 目标完成1,2失败3,4 进入分支阶段三
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "QuestStage")
		FJsonFuncData StageChangePolicy;

	//进入Stage需要进行的调用 理论上此处调用只能掉全局 感觉不如Manager的Stage广播回调
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "QuestStage")
		TArray<FJsonFuncData> OnStageIn;
public:
	//如果bool为true 但是FString为空 则说明已经完成任务所有阶段 
	bool GetChangeStage(FAwQuest* OwnerQuest,UObject*Caller,FString&NewStage);

	static FAwQuestStage FromJson(TSharedPtr<FJsonObject> JsonObj);

	void Reset();
};

//任务主体
USTRUCT(BlueprintType)
struct FAwQuest
{
	GENERATED_BODY()

	//任务的ID 同时作为名字
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quest")
		FString Id;
	//任务的描述信息 用于外部显示
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quest")
		FString Desc;
	//主线,支线,隐藏
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quest")
		EQuestType QuestType = EQuestType::Branch;
	//未激活,激活,接取,完成
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quest")
		EQuestState QuestState = EQuestState::InActive;

	//任务的当前阶段
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quest")
		FString StartStage;

	//任务的当前阶段
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quest")
		FString CurStage;
	//任务的所有阶段
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quest")
		TMap<FString, FAwQuestStage> StageMap;

	//接取条件
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quest")
		TArray<FJsonFuncData> AcceptConditions;
	//激活条件
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quest")
		TArray<FJsonFuncData> ActiveConditions;

	//奖励
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quest")
		TArray<FString> LootList;

	//激活后行为
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quest")
		TArray<FJsonFuncData> ActiveActions;

	//接受后行为
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quest")
		TArray<FJsonFuncData> AcceptActions;

	//完成后行为
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quest")
		TArray<FJsonFuncData> CompleteActions;

public:
	static FAwQuest FromJson(TSharedPtr<FJsonObject> JsonObj);

	FAwQuestStage* GetCurStage();

	void Reset();
};

//任务目标相关
USTRUCT(BlueprintType)
struct FAwQuestComponentData
{
	GENERATED_BODY()
public:
	//BeginPlay时触发相关任务调用
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "QuestComponent")
		TArray<FJsonFuncData> OnBegin;
	//EndPlay时触发相关的任务
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "QuestComponent")
		TArray<FJsonFuncData> OnEnd;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "QuestComponent");
		TMap<FString, FJsonFuncDataGroup> OnRelatedQuestActive;
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "QuestComponent");
		TMap<FString, FJsonFuncDataGroup> OnRelatedQuestAccept;
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "QuestComponent");
		TMap<FString, FJsonFuncDataGroup> OnRelatedQuestStageChange;
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "QuestComponent");
		TMap<FString, FJsonFuncDataGroup> OnRelatedQuestFinish;
};

