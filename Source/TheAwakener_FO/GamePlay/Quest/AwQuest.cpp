// Fill out your copyright notice in the Description page of Project Settings.


#include "AwQuest.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "AwQuestManager.h"

FAwQuest FAwQuest::FromJson(TSharedPtr<FJsonObject> JsonObj)
{
	FAwQuest QuestMetaData = FAwQuest();
	QuestMetaData.Id = UDataFuncLib::AwGetStringField(JsonObj, "Id");
	QuestMetaData.Desc = UDataFuncLib::AwGetStringField(JsonObj, "Desc");
	QuestMetaData.QuestType = UDataFuncLib::AwGetEnumField(JsonObj,"Type",EQuestType::Branch);
	QuestMetaData.QuestState = UDataFuncLib::AwGetEnumField(JsonObj, "State", EQuestState::InActive);
	QuestMetaData.StartStage = UDataFuncLib::AwGetStringField(JsonObj, "StartStage");
	QuestMetaData.CurStage = QuestMetaData.StartStage;
	for (auto StageJson: UDataFuncLib::AwGetArrayField(JsonObj, "StageList"))
	{
		FAwQuestStage StageMetaData = FAwQuestStage::FromJson(StageJson->AsObject());
		QuestMetaData.StageMap.Add(StageMetaData.Id, StageMetaData);
	}  
	for (const auto AcceptCondition: UDataFuncLib::AwGetArrayField(JsonObj, "AcceptConditions"))
	{
		QuestMetaData.AcceptConditions.Add(UDataFuncLib::SplitFuncNameAndParams(AcceptCondition->AsString()));
	}
	for (const auto ActiveCondition : UDataFuncLib::AwGetArrayField(JsonObj, "ActiveConditions"))
	{
		QuestMetaData.ActiveConditions.Add(UDataFuncLib::SplitFuncNameAndParams(ActiveCondition->AsString()));
	}
	
	//不确定是否还需要
	QuestMetaData.LootList = UDataFuncLib::AwGetStringArrayField(JsonObj,"LootList");
	
	for (const auto ActiveAction : UDataFuncLib::AwGetArrayField(JsonObj, "ActiveActions"))
	{
		QuestMetaData.ActiveActions.Add(UDataFuncLib::SplitFuncNameAndParams(ActiveAction->AsString()));
	}
	for (const auto AcceptAction : UDataFuncLib::AwGetArrayField(JsonObj, "AcceptActions"))
	{
		QuestMetaData.AcceptActions.Add(UDataFuncLib::SplitFuncNameAndParams(AcceptAction->AsString()));
	}
	for (const auto CompleteAction : UDataFuncLib::AwGetArrayField(JsonObj, "CompleteActions"))
	{
		QuestMetaData.CompleteActions.Add(UDataFuncLib::SplitFuncNameAndParams(CompleteAction->AsString()));
	}

    return QuestMetaData;
}

void FAwQuest::Reset()
{
	GetCurStage()->Reset();
	//阶段未必回滚
	//CurStage = StartStage;
}

FAwQuestStage* FAwQuest::GetCurStage()
{
	return StageMap.Find(CurStage);
}

FAwQuestStage FAwQuestStage::FromJson(TSharedPtr<FJsonObject> JsonObj)
{
	FAwQuestStage StageMetaData = FAwQuestStage();
	StageMetaData.Id = UDataFuncLib::AwGetStringField(JsonObj, "Id");
	StageMetaData.StageLevel = UDataFuncLib::AwGetStringField(JsonObj, "Level");
	StageMetaData.StageChangePolicy = UDataFuncLib::SplitFuncNameAndParams(UDataFuncLib::AwGetStringField(JsonObj,"StageChangePolicy"));
	for (const auto InStageAction : UDataFuncLib::AwGetArrayField(JsonObj, "InStageAction"))
	{
		StageMetaData.OnStageIn.Add(UDataFuncLib::SplitFuncNameAndParams(InStageAction->AsString()));
	}

	//目标和阶段分开存 如果并存参考Quest 主要影响配置的可读性 也考虑目标高重复性通过变量控制差异性 以减少配置压力
	for (auto Target:UDataFuncLib::AwGetStringArrayField(JsonObj, "TargetList")) 
	{
		FAwQuestTarget QuestTarget = UGameplayFuncLib::GetAwDataManager()->GetQuestTargetById(Target);
		StageMetaData.TargetList.Add(QuestTarget.Id,QuestTarget);

	}
	return StageMetaData;
}

void FAwQuestStage::Reset()
{
	TArray<FAwQuestTarget> MyTargets;
	TargetList.GenerateValueArray(MyTargets);

	for (auto&Target:MyTargets)
	{
		Target.ResetQuestTargetProgress();
	}
}

FAwQuestTarget FAwQuestTarget::FromJson(TSharedPtr<FJsonObject> JsonObj)
{
	FAwQuestTarget TargetMetaData = FAwQuestTarget();
	TargetMetaData.Id = UDataFuncLib::AwGetStringField(JsonObj,"Id");
	TargetMetaData.Desc = UDataFuncLib::AwGetStringField(JsonObj,"Desc");
	TargetMetaData.TargetType = UDataFuncLib::AwGetEnumField(JsonObj,"Type",EQuestTargetType::Kill);
	TargetMetaData.UIType = UDataFuncLib::AwGetEnumField(JsonObj, "UIType", ETargetUIType::Text);
	TargetMetaData.TargetKeyWords = UDataFuncLib::AwGetStringArrayField(JsonObj,"KeyWords");
	for (const auto Func: UDataFuncLib::AwGetArrayField(JsonObj, "FailedCondition"))
	{
		TargetMetaData.FailedConditions.Add(UDataFuncLib::SplitFuncNameAndParams(Func->AsString()));
	}
	TargetMetaData.ProgressCheckFun = UDataFuncLib::SplitFuncNameAndParams( UDataFuncLib::AwGetStringField(JsonObj,"ProgressCheckFun"));
	TargetMetaData.StartProgress = UDataFuncLib::AwGetNumberField(JsonObj, "StartProgress", 0);
	TargetMetaData.MaxProgress = UDataFuncLib::AwGetNumberField(JsonObj,"MaxProgress",0);
	TargetMetaData.CurProgress = TargetMetaData.StartProgress;
	
	return TargetMetaData;
}

void FAwQuestTarget::ModfiyQuestTargetProgress(int AddProgress)
{
    CurProgress += AddProgress;

	//是否允许超杀 超收集
	CurProgress = FMath::Clamp(CurProgress,0,CurProgress);
   // CurProgress = FMath::Clamp(CurProgress,0,MaxProgress);
}

void FAwQuestTarget::CheckProgress(bool& bNeedModfiy)
{
		bNeedModfiy = false;
		UFunction* Func = UCallFuncLib::GetUFunction( ProgressCheckFun.ClassPath,  ProgressCheckFun.FunctionName);
		if (Func )
		{
			struct
			{
				TArray<FString> Params;
				int ResultProgress;
			}FuncParam;

			FuncParam.Params = ProgressCheckFun.Params;
			UAwQuestManager* QuestManager = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwQuestManager>();
			if (!IsValid(QuestManager))
			{
				return;
			}
			QuestManager->ProcessEvent(Func, &FuncParam);
			bNeedModfiy = FuncParam.ResultProgress == CurProgress;
			//是否允许超杀 超收集
			//CurProgress =FMath::Clamp(FuncParam.ResultProgress,0,MaxProgress);
		}
}

void FAwQuestTarget::ResetQuestTargetProgress()
{
    CurProgress = StartProgress;
}

bool FAwQuestTarget::IsQuestTargetComplete()
{
    return CurProgress>=MaxProgress;
}

bool FAwQuestTarget::IsQuestTargetFailed()
{
	if (!UGameplayFuncLib::GetAwGameInstance())
	{
		return false;
	}
	UAwQuestManager* QuestManager = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwQuestManager>();

	//没有有效失败check则为 不会失败 与通过全部失败条件作区分
	bool bFitAllCondiation = true;
	bool bNoFailure = true;

	if (IsValid(QuestManager))
	{
		for (auto Condition : FailedConditions)
		{
			UFunction* ConditionFunc = UCallFuncLib::GetUFunction(Condition.ClassPath, Condition.FunctionName);
			if (ConditionFunc)
			{
				struct
				{
					TArray<FString> Params;
					bool Result;
				}ConditionFuncParam;

				ConditionFuncParam.Params = Condition.Params;
				QuestManager->ProcessEvent(ConditionFunc, &ConditionFuncParam);
				if (ConditionFuncParam.Result)
				{
					bNoFailure = false;
				}
				else
				{
					bFitAllCondiation = false;
				}
			}
		}
	}
    return bFitAllCondiation&&(!bNoFailure);
}

bool FAwQuestStage::GetChangeStage(FAwQuest* OwnerQuest,UObject* Caller, FString& NewStage)
{
	if (!IsValid(Caller)||!OwnerQuest)
	{
		return false;
	}
	NewStage.Empty();
	UFunction* ConditionFunc = UCallFuncLib::GetUFunction(StageChangePolicy.ClassPath, StageChangePolicy.FunctionName);
	if (ConditionFunc)
	{
		struct
		{
			FAwQuestStage* CurStage;
			TArray<FString> Params;
			FString Result;
		}StageChangePolicyFuncParam;

		StageChangePolicyFuncParam.CurStage = this;
		StageChangePolicyFuncParam.Params = StageChangePolicy.Params;
		Caller->ProcessEvent(ConditionFunc, &StageChangePolicyFuncParam);		
		NewStage = StageChangePolicyFuncParam.Result;
	}
	else
	{
		TArray<FAwQuestTarget> MyTargets;
		TargetList.GenerateValueArray(MyTargets);
		NewStage = "Next";
		for (auto Target: MyTargets)
		{
			if (!Target.IsQuestTargetComplete()) 
			{
				NewStage.Empty();
			}
			if (Target.IsQuestTargetFailed())
			{
				//默认不变但是结果true就是失败且刷新任务 是否影响Stage失败 -> 是否影响 Quest失败 ->是否重置相关的调用
				NewStage = Id;
			}
		}
	}

	TArray<FString> Stages;
	OwnerQuest->StageMap.GetKeys(Stages);

	if (NewStage == "Next")
	{
		int index = Stages.IndexOfByKey(Id);
		index++;
		//下个阶段有效则目标为下个阶段
		if (Stages.IsValidIndex(index))
		{
			NewStage = Stages[index];
		}
		else
		{
			NewStage.Empty();
		}
		return true;
	}//新阶段有效则新阶段
	else if(Stages.Contains(NewStage))
	{
		return true;
	}

	return false;
}


