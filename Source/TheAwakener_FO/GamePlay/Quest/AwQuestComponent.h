// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "AwQuest.h"
#include "TheAwakener_FO/FunctionLibrary/DataFuncLib.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "TheAwakener_FO/GamePlay/Trigger/AwSceneItem.h"
#include "AwQuestComponent.generated.h"


//任务相关实体对象 
UCLASS(BlueprintType, Blueprintable,ClassGroup=(Quest), meta=(BlueprintSpawnableComponent) )
class THEAWAKENER_FO_API UAwQuestComponent : public UActorComponent
{
	GENERATED_BODY()
public:
	virtual void BeginPlay() override;
	virtual void EndPlay(const EEndPlayReason::Type EndPlayReason)override;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "QuestComponent")
	FAwQuestComponentData MyQuestData;

	UFUNCTION()
	virtual	void OnOwnerCharacterDead(AAwCharacter* Owner);
	UFUNCTION()
	virtual	void OnOwnerItemDestroy(AAwSceneItem* Owner);

	//Call Back
	UFUNCTION()
		virtual void RelatedQuestActiveCallBack(FAwQuest Quest);
	UFUNCTION()
		virtual void RelatedQuestAcceptCallBack(FAwQuest Quest);
	UFUNCTION()
		virtual void RelatedQuestStageChangeCallBack(FAwQuest Quest);
	UFUNCTION()
		virtual void RelatedQuestFinishCallBack(FAwQuest Quest);
private:
	// Bind and UnBind
	virtual void BindRelatedQuestDelegate();
	virtual void UnBindRelatedQuestDelegate();

};
