// Fill out your copyright notice in the Description page of Project Settings.


#include "AwQuestComponent.h"
#include "AwQuestManager.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
void UAwQuestComponent::BeginPlay()
{
	Super::BeginPlay();

	AAwCharacter* CharacterOwner = Cast<AAwCharacter>(GetOwner());
	AAwSceneItem* ItemOwner = Cast<AAwSceneItem>(GetOwner());

	if (CharacterOwner)
	{
		CharacterOwner->OnCharacterDeadDelegate.AddDynamic(this, &UAwQuestComponent::OnOwnerCharacterDead);
	}
	if (ItemOwner)
	{
		ItemOwner->OnItemDistoryDelegate.AddDynamic(this, &UAwQuestComponent::OnOwnerItemDestroy);
	}

	for (auto Func : MyQuestData.OnBegin)
	{
		UFunction* BeginFunc = UCallFuncLib::GetUFunction(Func.<PERSON>, Func.FunctionName);
		if (BeginFunc)
		{
			struct
			{
				TArray<FString> Params;
			}BeginFuncParam;

			BeginFuncParam.Params = Func.Params;
			this->ProcessEvent(BeginFunc, &BeginFuncParam);
		}
	}

	//BindRelatedQuestDelegate();
}



void UAwQuestComponent::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
	Super::EndPlay(EndPlayReason);
	UnBindRelatedQuestDelegate();
}

void UAwQuestComponent::OnOwnerCharacterDead(AAwCharacter* Owner)
{

	for (auto Func : MyQuestData.OnEnd)
	{
		UFunction* EndFunc = UCallFuncLib::GetUFunction(Func.ClassPath, Func.FunctionName);
		if (EndFunc)
		{
			struct
			{
				TArray<FString> Params;
			}EndFuncParam;

			EndFuncParam.Params = Func.Params;
			if (!IsValid(GetOwner()))
			{
				return;
			}
			GetOwner()->ProcessEvent(EndFunc, &EndFuncParam);

		}
	}

}

void UAwQuestComponent::OnOwnerItemDestroy(AAwSceneItem* Owner)
{

	for (auto Func : MyQuestData.OnEnd)
	{
		UFunction* EndFunc = UCallFuncLib::GetUFunction(Func.ClassPath, Func.FunctionName);
		if (EndFunc)
		{
			struct
			{
				TArray<FString> Params;
			}EndFuncParam;

			EndFuncParam.Params = Func.Params;
			if (!IsValid(GetOwner()))
			{
				return;
			}
			GetOwner()->ProcessEvent(EndFunc, &EndFuncParam);

		}
	}

}

void UAwQuestComponent::BindRelatedQuestDelegate()
{
	UAwGameInstance* GameInstance = UGameplayFuncLib::GetAwGameInstance();
	if (IsValid(GameInstance))
	{
		UAwQuestManager* QuestManager = GameInstance->GetSubsystem<UAwQuestManager>();
		if (IsValid(QuestManager))
		{
			QuestManager->OnActiveQuest.AddDynamic(this,&UAwQuestComponent::RelatedQuestActiveCallBack);
			QuestManager->OnAcceptQuest.AddDynamic(this,&UAwQuestComponent::RelatedQuestAcceptCallBack);
			QuestManager->OnQuestStageChange.AddDynamic(this, &UAwQuestComponent::RelatedQuestStageChangeCallBack);
			QuestManager->OnFinishQuest.AddDynamic(this, &UAwQuestComponent::RelatedQuestFinishCallBack);
		}
	}
}

void UAwQuestComponent::UnBindRelatedQuestDelegate()
{
	UAwGameInstance* GameInstance = UGameplayFuncLib::GetAwGameInstance();
	if (IsValid(GameInstance))
	{
		UAwQuestManager* QuestManager = GameInstance->GetSubsystem<UAwQuestManager>();
		if (IsValid(QuestManager))
		{
			QuestManager->OnActiveQuest.RemoveDynamic(this, &UAwQuestComponent::RelatedQuestActiveCallBack);
			QuestManager->OnAcceptQuest.RemoveDynamic(this, &UAwQuestComponent::RelatedQuestAcceptCallBack);
			QuestManager->OnQuestStageChange.RemoveDynamic(this, &UAwQuestComponent::RelatedQuestStageChangeCallBack);
			QuestManager->OnFinishQuest.RemoveDynamic(this, &UAwQuestComponent::RelatedQuestFinishCallBack);
		}
	}
}

void UAwQuestComponent::RelatedQuestActiveCallBack(FAwQuest Quest)
{
	if (MyQuestData.OnRelatedQuestActive.IsEmpty())
	{
		return;
	}
	FJsonFuncDataGroup Funcs = *MyQuestData.OnRelatedQuestActive.Find(Quest.Id);
	for (auto Func : Funcs.JsonFuncDataGroup)
	{
		UFunction* CallBackFunc = UCallFuncLib::GetUFunction(Func.ClassPath, Func.FunctionName);
		if (CallBackFunc)
		{
			struct 
			{
				UObject* Caller;
				UClass* CallerClass;
				TArray<FString> Params;
			}FuncParam;
			FuncParam.Caller = this;
			FuncParam.CallerClass = this->GetClass();
			FuncParam.Params = Func.Params;
			this->ProcessEvent(CallBackFunc,&FuncParam);
		}
	}
}

void UAwQuestComponent::RelatedQuestAcceptCallBack(FAwQuest Quest)
{
	if (MyQuestData.OnRelatedQuestAccept.IsEmpty())
	{
		return;
	}
	FJsonFuncDataGroup Funcs = *MyQuestData.OnRelatedQuestAccept.Find(Quest.Id);
	for (auto Func : Funcs.JsonFuncDataGroup)
	{
		UFunction* CallBackFunc = UCallFuncLib::GetUFunction(Func.ClassPath, Func.FunctionName);
		if (CallBackFunc)
		{
			struct
			{
				UObject* Caller;
				UClass* CallerClass;
				TArray<FString> Params;
			}FuncParam;
			FuncParam.Caller = this;
			FuncParam.CallerClass = this->GetClass();
			FuncParam.Params = Func.Params;
			this->ProcessEvent(CallBackFunc, &FuncParam);
		}
	}
}

void UAwQuestComponent::RelatedQuestStageChangeCallBack(FAwQuest Quest)
{
	if (MyQuestData.OnRelatedQuestStageChange.IsEmpty())
	{
		return;
	}
	FJsonFuncDataGroup Funcs = *MyQuestData.OnRelatedQuestStageChange.Find(Quest.Id);
	for (auto Func : Funcs.JsonFuncDataGroup)
	{
		UFunction* CallBackFunc = UCallFuncLib::GetUFunction(Func.ClassPath, Func.FunctionName);
		if (CallBackFunc)
		{
			struct
			{
				UObject* Caller;
				UClass* CallerClass;
				TArray<FString> Params;
			}FuncParam;
			FuncParam.Caller = this;
			FuncParam.CallerClass = this->GetClass();
			FuncParam.Params = Func.Params;
			this->ProcessEvent(CallBackFunc, &FuncParam);
		}
	}
}

void UAwQuestComponent::RelatedQuestFinishCallBack(FAwQuest Quest)
{
	if (MyQuestData.OnRelatedQuestFinish.IsEmpty())
	{
		return;
	}
	FJsonFuncDataGroup Funcs = *MyQuestData.OnRelatedQuestFinish.Find(Quest.Id);
	for (auto Func : Funcs.JsonFuncDataGroup)
	{
		UFunction* CallBackFunc = UCallFuncLib::GetUFunction(Func.ClassPath, Func.FunctionName);
		if (CallBackFunc)
		{
			struct
			{
				UObject* Caller;
				UClass* CallerClass;
				TArray<FString> Params;
			}FuncParam;
			FuncParam.Caller = this;
			FuncParam.CallerClass = this->GetClass();
			FuncParam.Params = Func.Params;
			this->ProcessEvent(CallBackFunc, &FuncParam);
		}
	}
}
