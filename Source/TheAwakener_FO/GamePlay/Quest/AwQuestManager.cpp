// Fill out your copyright notice in the Description page of Project Settings.


#include "AwQuestManager.h"
#include "TheAwakener_FO/FunctionLibrary/DataFuncLib.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"

#include "TheAwakener_FO/DesignerScript/Trigger/TriggerScript.h"


void UAwQuestManager::InitManager()
{
	//暂时的 实际应该还要读存档相关  这里获取的只是 元数据
	AllQuests = UGameplayFuncLib::GetAwDataManager()->GetAllMetaQuests();
}

bool UAwQuestManager::TryActiveQuest(FString QuestId, bool Force)
{	
	if (!CheckQuestIsLegal(QuestId,EQuestState::InActive,Force))
	{
		return false;
	}

	//是否满足激活条件
	FAwQuest* Quest = AllQuests.Find(QuestId);
	for (auto Condition : Quest->ActiveConditions)
	{
		UFunction* ConditionFunc = UCallFuncLib::GetUFunction(Condition.ClassPath, Condition.FunctionName);
		if (ConditionFunc)
		{
			struct
			{
				TArray<FString> Params;
				bool Result;
			}ConditionFuncParam;

			ConditionFuncParam.Params = Condition.Params;
			this->ProcessEvent(ConditionFunc, &ConditionFuncParam);
			if (!ConditionFuncParam.Result)
			{
				return false;
			}
		}
	}

	//正式激活
	Quest->QuestState = EQuestState::Active;
	CurActiveQuests.Add(QuestId);

	FString print = "Success Active Quest:" + QuestId;
	GEngine->AddOnScreenDebugMessage(NULL,10,FColor::FColor(255,0,255),print);
	//执行自身相关全局激活动作
	for (auto Action:Quest->ActiveActions)
	{
		UFunction* ActionFunc = UCallFuncLib::GetUFunction(Action.ClassPath, Action.FunctionName);
		if (ActionFunc)
		{
			struct
			{
				TArray<FString> Params;
			}ActionFuncParam;

			ActionFuncParam.Params = Action.Params;
			this->ProcessEvent(ActionFunc, &ActionFuncParam);
		}
	}

	//全局任务激活广播
	if (OnActiveQuest.IsBound())
	{
		OnActiveQuest.Broadcast(*Quest);
	}

	return true;
}

bool UAwQuestManager::TryAcceptQuest(FString QuestId, bool Force)
{
	if (!CheckQuestIsLegal(QuestId, EQuestState::Active, Force))
	{
		return false;
	}

	FAwQuest* Quest = AllQuests.Find(QuestId);
	//是否满足接取条件
	for (auto Condition : Quest->AcceptConditions)
	{
		UFunction* ConditionFunc = UCallFuncLib::GetUFunction(Condition.ClassPath, Condition.FunctionName);
		if (ConditionFunc)
		{
			struct
			{
				TArray<FString> Params;
				bool Result;
			}ConditionFuncParam;

			ConditionFuncParam.Params = Condition.Params;
			this->ProcessEvent(ConditionFunc, &ConditionFuncParam);
			if (!ConditionFuncParam.Result)
			{
				return false;
			}
		}
	}

	FString print = "Success Accept Quest:" + QuestId;
	GEngine->AddOnScreenDebugMessage(NULL, 10, FColor::FColor(255, 0, 255), print);
	Quest->QuestState = EQuestState::Doing;

	//正式接取
	CurActiveQuests.Remove(QuestId);
	CurAcceptedQuests.Add(QuestId);

	//接取时确认一次初始targets 并加入分组
	TArray<FAwQuestTarget>Targets;
	if (Quest->GetCurStage())
	{
		Quest->GetCurStage()->TargetList.GenerateValueArray(Targets);
	}
	for (auto Target:Targets)
	{
		AddTargetInGroup(&Target,QuestId+SplitChar+Quest->CurStage+SplitChar+Target.Id);
		TryCheckQuestTargetProgress(QuestId+SplitChar+Quest->CurStage+SplitChar+Target.Id);
	}
	
	//执行自身相关全局接受动作
	for (auto Action : Quest->AcceptActions)
	{
		UFunction* ActionFunc = UCallFuncLib::GetUFunction(Action.ClassPath, Action.FunctionName);
		if (ActionFunc)
		{
			struct
			{
				TArray<FString> Params;
			}ActionFuncParam;

			ActionFuncParam.Params = Action.Params;
			this->ProcessEvent(ActionFunc, &ActionFuncParam);
		}
	}

	//全局任务接取广播
	if (OnAcceptQuest.IsBound())
	{
		OnAcceptQuest.Broadcast(*Quest);
	}

	return true;
}

bool UAwQuestManager::TryCompleteQuest(FString QuestId, bool Force)
{
	if (!CheckQuestIsLegal(QuestId, EQuestState::Doing,Force))
	{
		return false;
	}
	FAwQuest* Quest = AllQuests.Find(QuestId);

	FString print = "Success Complete Quest:" + QuestId;
	GEngine->AddOnScreenDebugMessage(NULL, 10, FColor::FColor(255, 0, 255), print);

	Quest->QuestState = EQuestState::Finish;
	//移除当前任务阶段所有目标
	TArray<FAwQuestTarget>Targets;
	if (Quest->GetCurStage())
	{
		Quest->GetCurStage()->TargetList.GenerateValueArray(Targets);
	}
	for (auto Target:Targets)
	{
		RemoveTargetInGroup(&Target,QuestId+SplitChar+Quest->CurStage+SplitChar+Target.Id);
	}
	//从接取中移除已完成任务
	CurAcceptedQuests.Remove(QuestId);
	FinishedQuests.Add(QuestId);
	
	//获取奖励 不确定是否还需要 和额外行为存在一定重复
	UTriggerScript::PlayerGetItemByPackageIds(Quest->LootList);
	
	//执行额外行为
	for (auto CompleteAction :Quest->CompleteActions)
	{
		UFunction* ConditionFunc = UCallFuncLib::GetUFunction(CompleteAction.ClassPath, CompleteAction.FunctionName);
		if (ConditionFunc)
		{
			struct
			{
				TArray<FString> Params;
				FString Result;
			}CompleteActionFuncParam;

			CompleteActionFuncParam.Params = CompleteAction.Params;
			this->ProcessEvent(ConditionFunc, &CompleteActionFuncParam);
		}
	}

	//全局任务完成广播
	if (OnFinishQuest.IsBound())
	{
		OnFinishQuest.Broadcast(*Quest);
	}
	
	return true;
}

bool UAwQuestManager::TryChangeQuestTargetProgress(FString QuestTargetFullID, int ChangeMount)
{	
	//FullID 应为   QuestID.StageID.TargetID 结构
	FString SubString;
	FString QuestID;
	FString StageID;
	FString TargetID;
	if (QuestTargetFullID.Contains(SplitChar))
	{
		QuestTargetFullID.Split(SplitChar,&QuestID,&SubString);
	}

	if (SubString.Contains(SplitChar))
	{
		SubString.Split(SplitChar,&StageID,&TargetID);
	}
	//确保存在 且有效
	FAwQuest* Quest = AllQuests.Find(QuestID);
	if (!Quest||!CurAcceptedQuests.Contains(QuestID))
	{
		return false;			
	}

	FAwQuestStage* QuestStage = Quest->StageMap.Find(StageID);

	if (!QuestStage||(Quest->CurStage!=StageID))
	{
		return false;
	}

	FAwQuestTarget* QuestTarget = QuestStage->TargetList.Find(TargetID);

	if (!QuestTarget)
	{
		return false;
	}
	//实际目标进度改变
	QuestTarget->ModfiyQuestTargetProgress(ChangeMount);
	
	FString print = "QuestTarget:"+QuestTargetFullID +"_Progress Change:"+FString::FromInt(QuestTarget->CurProgress)+"/"+ FString::FromInt(QuestTarget->MaxProgress);
	GEngine->AddOnScreenDebugMessage(NULL, 10, FColor::FColor(255, 0, 255), print);
	//改变后调用判断
	if (QuestTarget->IsQuestTargetComplete()|| QuestTarget->IsQuestTargetFailed())
	{
		FString NewStage;
		//如果目标的变化足以对阶段产生影响 且下个目标阶段不为空
		if (QuestStage->GetChangeStage(Quest,this,NewStage))
		{
			//变化 但没有下个阶段 就是完成
			if (NewStage.IsEmpty())
			{
				TryCompleteQuest(QuestID);
				return true;
			}
			//切换下个阶段
			TryChangeQuestStage(QuestID+SplitChar+NewStage,false);
			if (OnQuestStageChange.IsBound())
			{
				OnQuestStageChange.Broadcast(*Quest);
			}
			FString print2 = print +"\n"+ "QuestStageChangeTo:" + QuestID + SplitChar + NewStage;
			GEngine->AddOnScreenDebugMessage(NULL, 10, FColor::FColor(255, 0, 255), print2);
		}
	}

	return true;
}

bool UAwQuestManager::TryAbandonQuest(FString QuestId, bool Force)
{
	if (!CheckQuestIsLegal(QuestId, EQuestState::Doing, Force))
	{
		return false;
	}
	FAwQuest* Quest = AllQuests.Find(QuestId);
	//移除当前任务阶段所有目标
	TArray<FAwQuestTarget>Targets;
	if (Quest->GetCurStage())
	{
		Quest->GetCurStage()->TargetList.GenerateValueArray(Targets);
	}
	for (auto Target:Targets)
	{
		RemoveTargetInGroup(&Target,QuestId+SplitChar+Quest->CurStage+SplitChar+Target.Id);
	}
	//从已接取中移除
	CurAcceptedQuests.Remove(QuestId);
	//重置激活
	Quest->Reset();
	Quest->QuestState = EQuestState::InActive;
	Quest->CurStage = Quest->StartStage;
	//不够完善 放弃可能并不意味着只是回到激活
	TryActiveQuest(Quest->Id);
	return true;
}

bool UAwQuestManager::TryRefreshQuest(FString QuestId, bool Force)
{
	if (!CheckQuestIsLegal(QuestId, EQuestState::Doing, Force))
	{
		return false;
	}
	FAwQuest* Quest = AllQuests.Find(QuestId);
	Quest->Reset();
	return true;
}

bool UAwQuestManager::TryChangeQuestStage(FString NewFullID, bool bFirst)
{	//分割有效信息
	FString QuestID;
	FString StageID;

	if (NewFullID.Contains(SplitChar))
	{
		NewFullID.Split(SplitChar, &QuestID, &StageID);
	}
	//确保有效
	FAwQuest* Quest = AllQuests.Find(QuestID);
	if (!Quest)
	{
		return false;
	}

	FAwQuestStage* QuestStage = Quest->StageMap.Find(StageID);

	if (!QuestStage)
	{
		return false;
	}

	//移除当前任务阶段所有目标
	TArray<FAwQuestTarget>Targets;
	QuestStage->TargetList.GenerateValueArray(Targets);
	for (auto Target:Targets)
	{
		RemoveTargetInGroup(&Target,NewFullID+SplitChar+Target.Id);
	}
	
	//原地TP 即刷新
	if (Quest->CurStage == StageID)
	{
		//To Do Refresh
		Quest->Reset();
	}
	//调用 阶段变更 配置函数
	for (auto FuncScript : QuestStage->OnStageIn)
	{
		UFunction* Func = UCallFuncLib::GetUFunction(FuncScript.ClassPath, FuncScript.FunctionName);
		if (Func)
		{
			struct
			{
				FAwQuestStage CurStage;
				TArray<FString> Params;
				FString Result;
			}StageChangePolicyFuncParam;

			StageChangePolicyFuncParam.Params = FuncScript.Params;
			this->ProcessEvent(Func, &StageChangePolicyFuncParam);

		}
	}
	//最后阶段变更
	Quest->CurStage = StageID;

	return false;
}

bool UAwQuestManager::TryCheckQuestTargetProgress(FString FullId)
{
	//安全判断  理论需要修改时会重复调用2次 待优化
	FString SubString;
	FString QuestID;
	FString StageID;
	FString TargetID;
	if (FullId.Contains(SplitChar))
	{
		FullId.Split(SplitChar,&QuestID,&SubString);
	}

	if (SubString.Contains(SplitChar))
	{
		SubString.Split(SplitChar,&StageID,&TargetID);
	}
	//确保存在 且有效
	FAwQuest* Quest = AllQuests.Find(QuestID);
	if (!Quest||!CurAcceptedQuests.Contains(QuestID))
	{
		return false;			
	}
	FAwQuestStage* QuestStage = Quest->StageMap.Find(StageID);

	if (!QuestStage||(Quest->CurStage!=StageID))
	{
		return false;
	}

	FAwQuestTarget* QuestTarget = QuestStage->TargetList.Find(TargetID);
	if (!QuestTarget)
	{
		return false;
	}
	bool bNeedModfiy = false;
	int PreModfiyProgress = QuestTarget->CurProgress;
	QuestTarget->CheckProgress(bNeedModfiy);
	if (bNeedModfiy)
	{
		int ModfiyMount = QuestTarget->CurProgress - PreModfiyProgress;
		 return  TryChangeQuestTargetProgress(FullId,ModfiyMount);
	}
	return  true;
}

bool UAwQuestManager::TryCheckQuestTargetProgressByTypeAndKeyWord(EQuestTargetType Type, FString KeyWord)
{
	TArray<FString>FullIds = GetAllFitableTargetFullId(Type,KeyWord) ;
	if (FullIds.IsEmpty())
	{
		return  false;
	}
	//确保目标ID唯一；
	TSet<FString> FullIdSet = TSet<FString>(FullIds);

	for (auto Id :FullIdSet)
	{
		TryCheckQuestTargetProgress(Id);
	}
	return true;
}

bool UAwQuestManager::CheckQuestActivity(FString QuestId, bool Active)
{
	EQuestState PreSate = Active ? EQuestState::Active : EQuestState::InActive;
	return CheckQuestIsLegal(QuestId, PreSate, false);
}

bool UAwQuestManager::CheckQuestStage(FString QuestId, FString Stage)
{
	FAwQuest* Quest = AllQuests.Find(QuestId);
	if (Quest)
	{
		return Quest->CurStage == Stage;
	}
	return false;
}


TArray<FString> UAwQuestManager::GetCurLevelQuests(const FString CurLevelName)
{
	return *AllLevelQuests.Find(CurLevelName);
}

TArray<FString> UAwQuestManager::GetCurLevelActiveQuests(const FString CurLevelName)
{
	TArray<FString> Result = TArray<FString>();
	for (auto quest: GetCurLevelQuests(CurLevelName))
	{
		if (CheckQuestIsLegal(quest,EQuestState::Active))
		{
			Result.Add(quest);
		}
	}

	return Result;
}

TArray<FString> UAwQuestManager::GetCurLevelAcceptedQuests(const FString CurLevelName)
{
	TArray<FString> Result = TArray<FString>();
	for (auto quest : GetCurLevelQuests(CurLevelName))
	{
		if (CheckQuestIsLegal(quest, EQuestState::Doing))
		{
			Result.Add(quest);
		}
	}
	return Result;
}

bool UAwQuestManager::CheckQuestIsLegal(FString QuestId, EQuestState PreSate, bool IgnorePreSate)
{
	if (!AllQuests.Contains(QuestId))
	{
		UE_LOG(LogTemp, Error, TEXT("Quest:%s is not a valid Quest"), *QuestId);
		GEngine->AddOnScreenDebugMessage(NULL, 10, FColor::FColor(255, 0, 255), TEXT("Quest: is not a valid Quest"));
		return false;
	}
	if (IgnorePreSate)
	{
		//should delete
		GEngine->AddOnScreenDebugMessage(NULL, 10, FColor::FColor(255, 0, 255), TEXT("PreState is not fit"));
		return true;
	}
	return 	AllQuests.Find(QuestId)->QuestState == PreSate;
}

TArray<FString> UAwQuestManager::GetAllFitableTargetFullId(EQuestTargetType Type, FString KeyWord)
{
	TArray<FString>IdResult = TArray<FString>();
	if (CurAcceptedQuestTargetGroup.Find(Type))
	{
		IdResult = *(CurAcceptedQuestTargetGroup.Find(Type)->QuestTargetGroup.Find(KeyWord));
	}
	return  IdResult;
}

void UAwQuestManager::AddTargetInGroup(const FAwQuestTarget* Target,const FString FullId)
{
	FAwTargetKeyWordGroup* CurGroup = CurAcceptedQuestTargetGroup.Find(Target->TargetType);
	if (CurGroup)
	{
		for (auto KeyWordGroup:Target->TargetKeyWords)
		{
			if (CurGroup->QuestTargetGroup.Contains(KeyWordGroup))
			{
				CurGroup->QuestTargetGroup.Find(KeyWordGroup)->Add(FullId);
			}
		}
	}
	else
	{
		for(auto KeyWordGroup:Target->TargetKeyWords)
		{
			FAwTargetKeyWordGroup NewGroup;
			TArray<FString>Temp;
			Temp.Add(FullId);
			NewGroup.QuestTargetGroup.Add(KeyWordGroup,Temp);
			CurAcceptedQuestTargetGroup.Add(Target->TargetType,NewGroup);
		}
	}
}

void UAwQuestManager::RemoveTargetInGroup(const FAwQuestTarget* Target,const FString FullId)
{
	FAwTargetKeyWordGroup* CurGroup = CurAcceptedQuestTargetGroup.Find(Target->TargetType);
	if (CurGroup)
	{
		for (auto KeyWordGroup:Target->TargetKeyWords)
		{
			if (CurGroup->QuestTargetGroup.Contains(KeyWordGroup))
			{
				CurGroup->QuestTargetGroup.Find(KeyWordGroup)->Remove(FullId);
			}
		}
	}
}
