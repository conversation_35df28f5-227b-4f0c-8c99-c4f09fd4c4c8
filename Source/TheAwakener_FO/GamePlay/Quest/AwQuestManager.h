// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "AwQuest.h"
#include "Subsystems/GameInstanceSubsystem.h"
#include "AwQuestManager.generated.h"


DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnQuestStateChange,FAwQuest,Quest);


UCLASS()
class THEAWAKENER_FO_API UAwQuestManager : public UGameInstanceSubsystem
{
	GENERATED_BODY()
public:
	//所有任务 实际状态 不同于DataManager里的元数据
	TMap<FString, FAwQuest> AllQuests;

	//所有任务软引用 包括他们的所处地图
	TMap<FString, TArray<FString>> AllLevelQuests;

	//任务目标分类软引用
	TMap<EQuestTargetType,FAwTargetKeyWordGroup> CurAcceptedQuestTargetGroup;
	
	//当前已接受的任务 进行中的任务软引用
	TSet<FString> CurAcceptedQuests;

	//当前已激活的任务 但未接取的任务软引用
	TSet<FString> CurActiveQuests;

	//已经完成的任务软引用
	TSet<FString> FinishedQuests;

	virtual void InitManager();
public:
	//尝试激活任务
	UFUNCTION(BlueprintCallable, Category = "QuestManager")
		bool TryActiveQuest(FString QuestId,bool Force = false);
	//尝试接受任务
	UFUNCTION(BlueprintCallable, Category = "QuestManager")
		bool TryAcceptQuest(FString QuestId,bool Force = false);
	//尝试完成任务
	UFUNCTION(BlueprintCallable, Category = "QuestManager")
		bool TryCompleteQuest(FString QuestId,bool Force = false);
	//尝试修改任务目标进度
	UFUNCTION(BlueprintCallable, Category = "QuestManager")
		bool TryChangeQuestTargetProgress(FString QuestTargetFullID,int ChangeMount);
	//尝试放弃任务
	UFUNCTION(BlueprintCallable, Category = "QuestManager")
		bool TryAbandonQuest(FString QuestId, bool Force = false);
	//尝试刷新任务(阶段)
	UFUNCTION(BlueprintCallable, Category = "QuestManager")
		bool TryRefreshQuest(FString QuestId, bool Force = false);
	//尝试改变任务阶段
	UFUNCTION(BlueprintCallable, Category = "QuestManager")
		bool TryChangeQuestStage(FString NewFullId,bool bFirst= true);
	//尝试检查任务目标状态
	UFUNCTION(BlueprintCallable, Category = "QuestManager")
		bool TryCheckQuestTargetProgress(FString FullId);
	// 通过关键词与类别检测任务目标 主要应用于  同时存在复数 相同类型 交集关键词的任务
	UFUNCTION(BlueprintCallable,Category = "QuestManager")
		bool TryCheckQuestTargetProgressByTypeAndKeyWord(EQuestTargetType Type,FString KeyWord);
	//Check 供全局快速调用
	
	//检查Quest激活状态是否等于目标激活状态
	UFUNCTION(BlueprintCallable, Category = "QuestManager")
		bool CheckQuestActivity(FString QuestId, bool Active = false);
	//检查任务阶段
	UFUNCTION(BlueprintCallable, Category = "QuestManager")
		bool CheckQuestStage(FString QuestId, FString Stage);
	//Get
	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "QuestManager")
		TArray<FString> GetCurLevelQuests(const FString CurLevelName);
	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "QuestManager")
		TArray<FString> GetCurLevelActiveQuests(const FString CurLevelName);
	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "QuestManager")
		TArray<FString> GetCurLevelAcceptedQuests(const FString CurLevelName);
	
	//多播委托 提供全局调用时点 主要为脚本不好获取的对象以及全局高重复度调用的对象服务
	UPROPERTY(BlueprintAssignable, Category = "QuestManager")
		FOnQuestStateChange OnActiveQuest;
	UPROPERTY(BlueprintAssignable, Category = "QuestManager")
		FOnQuestStateChange OnAcceptQuest;
	UPROPERTY(BlueprintAssignable, Category = "QuestManager")
		FOnQuestStateChange OnFinishQuest;
	UPROPERTY(BlueprintAssignable, Category = "QuestManager")
		FOnQuestStateChange OnQuestStageChange;
	//ID分隔符
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "QuestTarget")
		FString SplitChar = ".";
private:
	bool CheckQuestIsLegal(FString QuestId,EQuestState PreSate ,bool IgnorePreSate = false);
	TArray<FString> GetAllFitableTargetFullId(EQuestTargetType Type,FString KeyWord);
	void AddTargetInGroup(const FAwQuestTarget* Target,const FString FullId);
	void RemoveTargetInGroup(const FAwQuestTarget* Target,const FString FullId);
};



