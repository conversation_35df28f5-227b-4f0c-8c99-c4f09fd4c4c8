// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GroundChecker.generated.h"

/**
 * Notify版本的Ground Check
 * 作用是放在一个Montage某个位置上，判断如果没有落地，就跳到某个section；如果落地了，就会继续走下去。
 * 但是因为只有一个Notify，所以他常用的情景是一个Section是Loop起点，然后距离这个Section很短的后面有一个这个GroundChecker
 * 此时如果走到这个GroundChecker角色尚未落地，就会回到Loop重新播放，下次到这个GroundChecker再做检查，如果落地就会继续播放下去
 */
UCLASS()
class THEAWAKENER_FO_API UGroundChecker : public UAnimNotify
{
	GENERATED_BODY()
public:
	/**
	 * 如果角色尚未落地，就会跳转到这个Section继续
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FName GotoSection;

	/**
	 * 若角色落地了，是否要预约一下下蹲动作
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	bool PreorderLandingAction = false;

	virtual void Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, const FAnimNotifyEventReference& EventReference) override;
};
