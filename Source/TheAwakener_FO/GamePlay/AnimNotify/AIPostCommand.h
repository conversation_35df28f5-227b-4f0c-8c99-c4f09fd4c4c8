// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotify.h"
#include "TheAwakener_FO/FunctionLibrary/DataFuncLib.h"
#include "AIPostCommand.generated.h"

/**
 * AI发起Command，实际上选择一个AI要执行的Action就可以了，因为AI的命令和玩家命令还不一样
 */
UCLASS()
class THEAWAKENER_FO_API UAIPostCommand : public UAnimNotify
{
	GENERATED_BODY()
public:
	/**
	 * 要发起的Action，这要是空的，这个Notify就没意义了，这将走一个JsonFunc返回结果
	 * (AAwCharacter* AIGuy, TArray<FString> StateTags, TArray<FString> Params)=>FString
	 * 执行这个的AI角色，这个角色当前的一些AI使用技能的状态，函数的参数，返回：使用的动作Id（如"Action1"）
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FJsonFuncData AIUseAction;

	//是否只有AI角色才对此条有效？如果是false，玩家控制角色也会受到影响
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	bool OnlyAI = true;

	virtual void Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, const FAnimNotifyEventReference& EventReference) override;
};
