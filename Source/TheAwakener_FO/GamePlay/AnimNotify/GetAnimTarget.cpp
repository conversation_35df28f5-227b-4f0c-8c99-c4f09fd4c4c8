// Fill out your copyright notice in the Description page of Project Settings.


#include "GetAnimTarget.h"

#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"

void UGetAnimTarget::Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	Super::Notify(MeshComp, Animation, EventReference);
	const AAwCharacter* Character = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Character) return;
	if (!Character->OwnerPlayerController) return;
	Character->OwnerPlayerController->GetAimedTargetFromScreenCenter(AimDistance);
}
