
#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotify.h"
#include "Engine/Classes/GameFramework/ForceFeedbackAttenuation.h"
#include "PlayForceFeedBack.generated.h"

USTRUCT(BlueprintType)
struct FAwForceFeedBackInfo
{
	GENERATED_BODY()

public:
 
	// 振动文件
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	UForceFeedbackEffect* ForceFeedbackEffect;

	// 是否直接在手柄上播放
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	bool IsPlayOnGamepad = false;
	
	// 是否是 Attach，否的话创建在世界坐标，坐标根据 PosOffset 走
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	bool IsAttach = true;

	// Attach 的 SocketName，如果为空的话，Attach 在 root 点上
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FName SocketName;

	// Attach的时候为偏移位置，否则为世界坐标
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FVector PosOffset = FVector::ZeroVector;

	// 效果倍率
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	float IntensityMultiplier = 1;

	// 衰减数据
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	UForceFeedbackAttenuation* ForceFeedbackAttenuation;
};

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UPlayForceFeedBack : public UAnimNotify
{
	GENERATED_BODY()
	
public:
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FAwForceFeedBackInfo AwForceFeedBackInfo;
	
	virtual void Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
		const FAnimNotifyEventReference& EventReference) override;

	static void Play(const FAwForceFeedBackInfo& AwForceFeedBackInfo, USkeletalMeshComponent* MeshComp);
};
