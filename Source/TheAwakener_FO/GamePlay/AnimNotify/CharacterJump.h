// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "CharacterJump.generated.h"

/**
 * 角色动作到这里开始会跳起来，但是跳只给了y的力
 */
UCLASS()
class THEAWAKENER_FO_API UCharacterJump : public UAnimNotify
{
	GENERATED_BODY()
public:
	//最高点是多高（单位：厘米）
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	float MaxHeight = 200.f;

	//多少秒内达到最高距离（单位：秒）
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	float ReachTopInSec = 0.4f;

	virtual void Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, const FAnimNotifyEventReference& EventReference) override;
};
