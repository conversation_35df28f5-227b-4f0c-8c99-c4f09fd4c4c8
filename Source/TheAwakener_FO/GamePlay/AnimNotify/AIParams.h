// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotify.h"
#include "AIParams.generated.h"

USTRUCT(BlueprintType)
struct FAIParamsStr
{
	GENERATED_BODY()
public:
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
		FString ParamsName = "";
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
		FString ParamsValue = "";
};

/**
 * 用来在AI中，修改AIConponent 的 Params 变量
 */
UCLASS()
class THEAWAKENER_FO_API UAIParams : public UAnimNotify
{
	GENERATED_BODY()
public:
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FAIParamsStr> Params;

	virtual void Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, const FAnimNotifyEventReference& EventReference) override;
	
};
