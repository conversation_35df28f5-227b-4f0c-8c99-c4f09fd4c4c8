// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotify.h"
#include "TheAwakener_FO/GamePlay/Equipment/Equipment.h"
#include "AnimationFX.generated.h"

/**
 * 在这个时间点，按照规则来播放视觉特效(VFX)和音频特效(SFX)
 */
UCLASS(BlueprintType)
class THEAWAKENER_FO_API UAnimationFX : public UAnimNotify
{
	GENERATED_BODY()
public:
	virtual void Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, const FAnimNotifyEventReference& EventReference) override;
	
	/**
	 * 基础的VFX的Key，如果这个是空字符串，那就是不要播放视觉特效
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString VFXKey = FString();

	/**
	 * 基础的SX的Key，如果这个是空字符串，那就是不要播放音频特效
	 * 如果视觉和音频特效都是空字符串，那若不是为了调试，就是拉这个notify的人脑子烧坏了
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString SFXKey = FString();

	/**
	 * 在角色的哪个骨骼上播放
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FName SocketName;
	
	/**
	 * 是否要检查性别，这条只对音效有意义，视觉特效不检查性别（规则就是规则，我们没有性别歧视，只有男女有别）
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool CheckGender = false;
	
	/**
	 * 是否需要检查某个身体碰撞框（包含并按照优先级CharacterHitBox→AttackHitBox→CatchHitBox）碰到的地面地形
	 * 若是空字符串或者对应的碰撞框不存在或者碰撞框当时Active是false，就代表不用检测
	 * 如果存在，则会根据返回的地形作为一个KeyText
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString CheckActiveHitBoxOnTerrain = FString();

	/**
	 * 是否要检查某个部位的装备
	 * 首先对应部位得穿着装备，不然写是也没用，然后才会看装备的第几个关键字（FEquipment.FXKey的index）
	 * 如果对应index的关键字存在，就会成为关键字，否则就会被无视
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	EEquipmentPart CheckEquipmentAtPart = EEquipmentPart::Invalid;

	/**
	 * 当EquipmentPart检测合理之后，才会有意义
	 * 如果对应index（FEquipment.FXKey的index）的关键字存在，就会成为关键字，否则就会被无视
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int EquipmentFXIndex = 0;

	/**
	 * 特效尾部数字，我知道你们喜欢尾部加个数字作为“同类文件但是内容不相同，也许是等级也许是多个的文件的文件名”来用，那就加上
	 * 如果这个数字<0，则不会加上尾数
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int TailNumber = -1;
};
