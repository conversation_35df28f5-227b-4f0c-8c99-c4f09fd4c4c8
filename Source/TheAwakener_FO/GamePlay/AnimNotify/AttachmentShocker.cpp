// Fill out your copyright notice in the Description page of Project Settings.


#include "AttachmentShocker.h"

#include "Components/SkeletalMeshComponent.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"

void UAttachmentShocker::Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	Super::Notify(MeshComp, Animation, EventReference);
	
	AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Me) return;

	TArray<AAwCharacter*> GuysOnMe;
	for (const FString ThisPartId : this->PartId)
	{
		TArray<AAwCharacter*> GuysOnPart = Me->CharacterAttachment.GetCharacterAttachingOnPart(ThisPartId);
		for (AAwCharacter* GOnPart : GuysOnPart)
		{
			if (GuysOnMe.Contains(GOnPart) == false) GuysOnMe.Add(GOnPart);
		} 
	}
	for (AAwCharacter* OnMe : GuysOnMe)
	{
		//TODO：需要新的玩法来支持这个
		const int Dis = this->BreakPower; //- OnMe->CurrentAction()->Balance;
		if (Dis >= this->StopAttachThreshold)
		{
			OnMe->StopAttaching();
		}else if (Dis >= 0)
		{
			//TODO: 震荡一下？咋整？
		}
	} 
}
