// Fill out your copyright notice in the Description page of Project Settings.


#include "AIUseActionTagModify.h"

#include "Components/SkeletalMeshComponent.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"

void UAIUseActionTagModify::Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	Super::Notify(MeshComp, Animation, EventReference);

	const AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Me) return;
	
	for (FString RState : this->RemoveAIStates) Me->GetAIComponent()->UseActionTags.Remove(RState);
	for (FString AState : this->AddAIStates) Me->GetAIComponent()->UseActionTags.Add(AState); 
}
