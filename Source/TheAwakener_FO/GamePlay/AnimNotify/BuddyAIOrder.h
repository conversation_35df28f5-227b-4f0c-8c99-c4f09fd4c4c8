// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotify.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "BuddyAIOrder.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UBuddyAIOrder : public UAnimNotify
{
	GENERATED_BODY()
public:
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	EBuddyAIOrderType OrderType = EBuddyAIOrderType::Auto;

	virtual void Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, const FAnimNotifyEventReference& EventReference) override;
};
