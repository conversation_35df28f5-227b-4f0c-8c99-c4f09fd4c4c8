// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotify.h"
#include "FightingWillModify.generated.h"

/**
 * 改变角色的战斗情绪（AI的“状态”）的值
 */
UCLASS(BlueprintType)
class THEAWAKENER_FO_API UFightingWillModify : public UAnimNotify
{
	GENERATED_BODY()
public:
	//要改变的量
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int ModifyValue = -10;

	//因为改变而导致FightingWill.Value降低到0以下的时候，FightingWill.Level的变化规则
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int LevelModifyRule = -1;

	virtual void Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, const FAnimNotifyEventReference& EventReference) override;
};
