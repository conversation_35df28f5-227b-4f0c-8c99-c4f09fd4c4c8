// Fill out your copyright notice in the Description page of Project Settings.


#include "ModifyBuff.h"

#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"

void UModifyBuff::Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	Super::Notify(MeshComp, Animation, EventReference);

	AAwCharacter* Character = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Character) return;

	TArray<FBuffObj*> BuffObjs = Character->GetBuff(BuffId);
	for (FBuffObj* BuffObj : BuffObjs)
	{
		if (BuffStack >= 0)
			BuffObj->Stack = BuffStack;
		if (BuffDuration >= 0)
			BuffObj->Duration = BuffDuration;
	}
}
