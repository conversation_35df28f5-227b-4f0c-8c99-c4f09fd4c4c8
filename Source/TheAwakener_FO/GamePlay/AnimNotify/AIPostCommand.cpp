// Fill out your copyright notice in the Description page of Project Settings.


#include "AIPostCommand.h"

#include "TheAwakener_FO/FunctionLibrary/CallFuncLib.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"

void UAIPostCommand::Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	Super::Notify(MeshComp, Animation, EventReference);

	if (AIUseAction.ClassPath.IsEmpty() || AIUseAction.FunctionName.IsEmpty()) return;
	
	AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Me || (Me->IsPlayerCharacter() && OnlyAI == true)) return;

	FString ToUseActionId;
	
	UFunction* Func = UCallFuncLib::JsonFuncToUFunc(AIUseAction);
	if (Func)
	{
		struct
		{
			AAwCharacter* AIGuy;
			TArray<FString> TagParams;
			TArray<FString> Params;
			FString Result;
		} FuncParam;
		FuncParam.AIGuy = Me;
		FuncParam.TagParams = Me->CurrentUseActionTags();
		FuncParam.Params = AIUseAction.Params;
		Me->ProcessEvent(Func, &FuncParam);
		ToUseActionId = FuncParam.Result;
	}

	if (ToUseActionId.IsEmpty() == false)
	{
		Me->AddAIAction(ToUseActionId);
		//UKismetSystemLibrary::PrintString(Me, FString("DoComboAction : ").Append(ToUseActionId));
	}
}
