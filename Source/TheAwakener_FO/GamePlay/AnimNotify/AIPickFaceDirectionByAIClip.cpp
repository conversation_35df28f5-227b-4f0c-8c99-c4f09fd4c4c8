// Fill out your copyright notice in the Description page of Project Settings.


#include "AIPickFaceDirectionByAIClip.h"

#include "Components/SkeletalMeshComponent.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"

void UAIPickFaceDirectionByAIClip::Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	Super::Notify(MeshComp, Animation, EventReference);

	const AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Me || Me->IsPlayerCharacter()) return;	//玩家直接不执行

	const FVector AITarDir = Me->GetAITargetDirection();
	
	const FVector2D TargetDir = FVector2D(AITarDir.X, AITarDir.Y);

	Me->SetAIMoveAndFaceDir(
		TargetDir, TargetDir, 0	//TODO: 这里都是0好吗？应该没问题，毕竟会被动作和移动限制的，如有不对劲，改之
	);
}
