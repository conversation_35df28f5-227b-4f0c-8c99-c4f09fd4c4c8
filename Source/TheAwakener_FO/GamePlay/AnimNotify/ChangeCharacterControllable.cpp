// Fill out your copyright notice in the Description page of Project Settings.


#include "ChangeCharacterControllable.h"

#include "Components/SkeletalMeshComponent.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"


void UChangeCharacterControllable::Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
                                     const FAnimNotifyEventReference& EventReference)
{
	Super::Notify(MeshComp, Animation, EventReference);
	
	AAwCharacter* OwnerCharacter = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (OwnerCharacter == nullptr) return;
	AAwPlayerController* Controller = nullptr;
	if (!Controller)
	{
		return;
	}
	OwnerCharacter->SetPlayerControllable_Implementation(Controller,Controllable);

}
