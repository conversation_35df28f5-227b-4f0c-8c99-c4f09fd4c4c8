// Fill out your copyright notice in the Description page of Project Settings.


#include "PlayCameraShake.h"

#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"

void UPlayCameraShake::Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	Super::Notify(MeshComp, Animation, EventReference);
	
	if (!ShakeInfo.Shake) return;
	const AActor* Me = MeshComp->GetOwner();
	if (!Me) return;
	UWorld* World = Me->GetWorld();
	if (!World) return;

	const FVector FinalPos = this->ShakeInfo.Offset + Me->GetActorLocation();
	APlayerCameraManager::PlayWorldCameraShake(
		World, ShakeInfo.Shake, FinalPos, ShakeInfo.FullShockRange, ShakeInfo.LoseShockRange,
		ShakeInfo.FallOff, ShakeInfo.DoRotate
	);
}
