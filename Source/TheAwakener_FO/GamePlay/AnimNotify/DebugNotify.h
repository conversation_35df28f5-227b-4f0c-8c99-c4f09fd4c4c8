// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotify.h"
#include "DebugNotify.generated.h"

/**
 * 测试用的，千万别出现在正式工程里，代码爱咋改咋改，就是测试嘛
 */
UCLASS(BlueprintType)
class THEAWAKENER_FO_API UDebugNotify : public UAnimNotify
{
	GENERATED_BODY()
public:
	virtual void Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, const FAnimNotifyEventReference& EventReference) override;

	//谁
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString ChaName;

	//说啥
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString Dialog;

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	float Force = 1;

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	float Height = 100;
};
