// Fill out your copyright notice in the Description page of Project Settings.


#include "ShowCmdPopUp.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"

void UShowCmdPopUp::Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	Super::Notify(MeshComp, Animation, EventReference);
	
	if (UAwGameInstance::Instance && UAwGameInstance::Instance->bIsInit)
		if (UAwUIManager* UIManager = UGameplayFuncLib::GetUiManager())
			UIManager->ShowCmdTipPopUp(CmdTipInfos, Duration);
}
