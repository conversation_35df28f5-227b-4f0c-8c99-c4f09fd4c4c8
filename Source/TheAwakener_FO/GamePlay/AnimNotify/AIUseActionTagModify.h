// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotify.h"
#include "UObject/Object.h"
#include "AIUseActionTagModify.generated.h"

/**
 * 直接在Montage修改AIComponent->UseActionTag
 */
UCLASS()
class THEAWAKENER_FO_API UAIUseActionTagModify : public UAnimNotify
{
	GENERATED_BODY()
private:
	virtual void Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, const FAnimNotifyEventReference& EventReference) override;
	
public:
	//要添加的AIState
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	TArray<FString> AddAIStates;
	
	//要删除的AIState
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	TArray<FString> RemoveAIStates;

	
};
