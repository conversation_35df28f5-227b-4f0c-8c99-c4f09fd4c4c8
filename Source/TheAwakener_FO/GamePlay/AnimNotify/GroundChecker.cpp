// Fill out your copyright notice in the Description page of Project Settings.


#include "GroundChecker.h"

#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"

void UGroundChecker::Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	Super::Notify(MeshComp, Animation, EventReference);
	
	AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Me || GotoSection.IsNone()) return;

	if (Me->OnGround() == false)
	{
		UAnimMontage* Montage = Me->GetCurrentActiveMontage();
		FAnimMontageInstance* MontageInstance = Me->GetActiveMontageInstance();
		if (Montage && MontageInstance)
		{
			const int32 SectionID = Montage->GetSectionIndex(GotoSection);
			float StartTime = 0.f;
			float EndTime = 0.f;
			Montage->GetSectionStartAndEndTime(SectionID, StartTime, EndTime);
			MontageInstance->SetPosition(StartTime); 
		}
	}else if (this->PreorderLandingAction == true)
	{
		Me->PreorderActionByMontageState(ECharacterMontageState::Landing);
	}
}
