// Fill out your copyright notice in the Description page of Project Settings.


#include "CheckAnimNotfiyBuff.h"

#include "Components/SkeletalMeshComponent.h"
#include "TheAwakener_FO/FunctionLibrary/CommonFuncLib.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GamePlay/Roguelike/BattleStyle/RogueBattleStyleSubSystem.h"
#include "TheAwakener_FO/GamePlay/Roguelike/BattleUpgrade/RogueBattleUpgradeSubSystem.h"


void UCheckAnimNotfiyBuff::Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,const FAnimNotifyEventReference& EventReference)
{
	Super::Notify(MeshComp, Animation, EventReference);
	
	AAwCharacter* Character = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Character) return;

	if (UGameplayFuncLib::GetUiManager()) 
		for (const FString Str : CheckBuffTags)
			UGameplayFuncLib::GetUiManager()->OnTriggerRougeBattleTag_ByFStr(Str);
	
	for (auto it = Character->CharacterObj.Buff.CreateIterator();it;++it)
	{
		if (UCommonFuncLib::ArrayContainsArray((*it).Model.Tags,CheckBuffTags,bContainsAllTags))
		{
			for (auto FunString : (*it).Model.OnAnimNotfiy)
			{
				UFunction* Func = UCallFuncLib::JsonFuncToUFunc(FunString);
				if (IsValid(Func) == false) continue;;
				
				struct {
					FBuffObj BuffObj;
					int32 WasStack;
					TArray<FString> Params;

					FBuffRunResult  Result;
				} FuncParam;
							
				FuncParam.BuffObj = (*it);
				FuncParam.WasStack = (*it).Stack;
				FuncParam.Params = FunString.Params;
				//叠加动画事件的Params;
				if (bUseAnimTransform)
				{
					FuncParam.Params.Add("AnimParams");
					FuncParam.Params.Add(Rotator.ToString());
					FuncParam.Params.Add(PosOffset.ToString());					
				}
				
				Character->ProcessEvent(Func, &FuncParam);
				(*it) = FuncParam.Result.BuffObj;
			}
		}
	}
}