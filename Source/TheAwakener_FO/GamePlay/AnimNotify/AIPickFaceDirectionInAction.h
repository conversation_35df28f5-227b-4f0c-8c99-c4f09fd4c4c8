// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotify.h"
#include "AIPickFaceDirectionInAction.generated.h"

/**
 * AI在这个Notify的时候会根据JsonFunction申请旋转角度
 */
UCLASS()
class THEAWAKENER_FO_API UAIPickFaceDirectionInAction : public UAnimNotify
{
	GENERATED_BODY()
public:
	/**
	 * 执行这个的时候会输入多少度，这里是一个函数返回的，若这个函数不存在，那么这个Notify就没有意义了
	 * (AAwCharacter* AICha, TArray<FString> TagParams, TArray<FString> Params)=>FVector
	 * (执行的AI角色，本Notify内的定制参数，策划参数)=>要转向的方向，程序这里最后会根据这个算出AI期望转多少度
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString AIRotateFunc;

	/**
	 * 一些定制参数，作为暗号给回调函数，也许策划需要根据“这是第几个点”来决定拗不拗
	 */
	UPROPERTY(EditAnywhere, BlueprintReadOnly)
	TArray<FString> TagParams;
	
	/**
	 * 是否是纯AI执行的，如果不是纯AI执行，那么玩家操作的角色也会被执行对应的Func
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool OnlyAI = true;

	virtual void Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, const FAnimNotifyEventReference& EventReference) override;
};
