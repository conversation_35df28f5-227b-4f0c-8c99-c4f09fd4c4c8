// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/FunctionLibrary/DataFuncLib.h"
#include "TriggerAIScript.generated.h"

/**
 * 直接执行一个AI的Action
 */
UCLASS()
class THEAWAKENER_FO_API UTriggerAIScript : public UAnimNotify
{
	GENERATED_BODY()
public:
	//要执行的逻辑脚本函数，和填写json标的一样——FuncName(Params)
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FString> AIScriptFunctions;

	//要执行的逻辑脚本函数，和填写json标的一样——FuncName(Params)
	//直接用FString填写不方便，所以用这个
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FJsonFuncData> AIScriptFunctionList;

	virtual void Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, const FAnimNotifyEventReference& EventReference) override;
};
