// Fill out your copyright notice in the Description page of Project Settings.


#include "AIPickFaceDirectionInAction.h"

#include "Components/SkeletalMeshComponent.h"
#include "TheAwakener_FO/FunctionLibrary/CallFuncLib.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"

void UAIPickFaceDirectionInAction::Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	Super::Notify(MeshComp, Animation, EventReference);
	
	if (this->AIRotateFunc.IsEmpty()) return;

	AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Me || (Me->IsPlayerCharacter() && OnlyAI == true)) return;
	FVector2D TargetDir = FVector2D(Me->GetActorRotation().Vector().X, Me->GetActorRotation().Vector().Y);

	const FJsonFuncData FuncData = UCallFuncLib::StringToJsonFuncData(AIRotateFunc);
	UFunction* Func = UCallFuncLib::JsonFuncToUFunc(FuncData);
	if (Func)
	{
		struct
		{
			AAwCharacter* AIGuy;
			TArray<FString> TagParams;
			TArray<FString> Params;
			FVector2D Result;
		} FuncParam;
		FuncParam.AIGuy = Me;
		FuncParam.TagParams = this->TagParams;
		FuncParam.Params = FuncData.Params;
		Me->ProcessEvent(Func, &FuncParam);
		TargetDir = FuncParam.Result;
	}

	Me->SetAIMoveAndFaceDir(
		TargetDir, TargetDir, 0	//TODO: 这里都是0好吗？应该没问题，毕竟会被动作和移动限制的，如有不对劲，改之
	);
}
