// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/GamePlay/Characters/Battle/BattleStructs.h"
#include "UObject/Object.h"
#include "CreateBreakableClone.generated.h"

/**
 * 制作一个可以被摧毁的（带进攻性的）Clone
 */
UCLASS()
class THEAWAKENER_FO_API UCreateBreakableClone : public UAnimNotify
{
	GENERATED_BODY()
public:
	//存在多久（秒）
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	float LifeDuration = 2;

	//相对所在位置的偏移
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FVector Offset;

	//是否带攻击性
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	bool DoOffense = true;

	//攻击信息
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FOffenseInfo OffenseInfo;

	//防御能力
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FDefenseInfo DefenseInfo;

	//生命值情况
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FChaResource ChaResource = FChaResource(1);
	
	virtual void Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, const FAnimNotifyEventReference& EventReference) override;
};
