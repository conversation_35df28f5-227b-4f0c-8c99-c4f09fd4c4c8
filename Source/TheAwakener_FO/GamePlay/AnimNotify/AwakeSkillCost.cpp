// Fill out your copyright notice in the Description page of Project Settings.


#include "AwakeSkillCost.h"

#include "Components/SkeletalMeshComponent.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"


void UAwakeSkillCost::Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	Super::Notify(MeshComp, Animation, EventReference);
	AAwPlayerState* PlayerState = UGameplayFuncLib::GetPlayerControllerByComp(MeshComp)->GetPlayerState<AAwPlayerState>();
	if (PlayerState)
	{
		PlayerState->ReduceCurAwakeSkillMinCost();
	}
}
