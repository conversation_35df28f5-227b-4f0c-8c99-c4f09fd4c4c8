// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"

#include "Animation/AnimNotifies/AnimNotify_PlayParticleEffect.h"
#include "Engine/DataTable.h"

#include "CheckBuffAnimNotfiy_PlayParticleEffect.generated.h"


USTRUCT()
struct FAnimPlayParticleEffectData  :public  FTableRowBase
{
	GENERATED_BODY()
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="AnimNotify")
	TObjectPtr<UParticleSystem> PSTemplate;

	// Location offset from the socket
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="AnimNotify")
	FVector LocationOffset;

	// Rotation offset from socket
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="AnimNotify")
	FRotator RotationOffset;

	// Scale to spawn the particle system at
	UPROPERTY(EditAnywhere, Category="AnimNotify")
	FVector Scale ;

	// Should attach to the bone/socket
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="AnimNotify")
	uint32 Attached:1; 	//~ Does not follow coding standard due to redirection from BP

	// SocketName to attach to
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AnimNotify")
	FName SocketName;
};
/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UCheckBuffAnimNotfiy_PlayParticleEffect : public UAnimNotify_PlayParticleEffect
{
	GENERATED_BODY()
public:
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FString> AbilityIdCheck; 
	
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		TArray<FString> CheckBuffTags;
	
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		UDataTable* ParticleParameterData = nullptr;
	virtual void Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, const FAnimNotifyEventReference& EventReference) override;
	
};
