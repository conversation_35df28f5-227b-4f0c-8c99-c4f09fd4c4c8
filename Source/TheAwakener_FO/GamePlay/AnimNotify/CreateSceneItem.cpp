// Fill out your copyright notice in the Description page of Project Settings.


#include "CreateSceneItem.h"

#include "Components/SkeletalMeshComponent.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "Kismet/KismetMathLibrary.h"

void UCreateSceneItem::Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	Super::Notify(MeshComp, Animation, EventReference);
	AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Me) return;
	if (Me->GetLocalRole() != ENetRole::ROLE_Authority) return;
	if (Me->Dead() || Me->IsPendingKillPending()) return;

	//需要重设一下Transform的Rotation
	FTransform CharacterTrans = Me->GetTransform();
	CharacterTrans.SetRotation(FRotator(0,CharacterTrans.GetRotation().Rotator().Yaw,0).Quaternion());
	
	FVector ActualLoc = UKismetMathLibrary::TransformLocation(CharacterTrans, OffsetTransform.GetLocation());
	FVector ActualDir = UKismetMathLibrary::TransformRotation(CharacterTrans, OffsetTransform.GetRotation().Rotator()).Quaternion().GetForwardVector();
	if(FromOffsetGetGroundLoc)
	{
		TArray<TEnumAsByte<EObjectTypeQuery>> ObjectList;
		ObjectList.Add(EObjectTypeQuery::ObjectTypeQuery1);
		FHitResult HitResult;
		bool bHit = UKismetSystemLibrary::LineTraceSingleForObjects(Me, ActualLoc, ActualLoc + FVector(0,0,TraceZ),
			ObjectList, false, TArray<AActor*>(), EDrawDebugTrace::None,HitResult, true
			//,FLinearColor::Red,FLinearColor::Green, 10
			);
		if(bHit)
		{
			ActualLoc = HitResult.Location;
		}
		else
		{
			ActualLoc.Z = Me->GetActorLocation().Z;
		}
	}
	FSceneItemModel Model = UGameplayFuncLib::GetDataManager()->GetSceneItemModelById(SceneItemId);
	Model.LifeSpan = LifeSpan;
	FRotator ActualRot = UKismetMathLibrary::MakeRotFromX(ActualDir);
	AAwSceneItem* SceneItem= UGameplayFuncLib::CreateSceneItem(Model,Side,FTransform(ActualRot,ActualLoc, FVector(1,1,1)));
	if (SceneItem)
	{
		SceneItem->SetOwner(Me);
	}
}
