// Fill out your copyright notice in the Description page of Project Settings.


#include "AddVoiceStimulate.h"

#include "Components/SkeletalMeshComponent.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"

void UAddVoiceStimulate::Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	Super::Notify(MeshComp, Animation, EventReference);

	AAwCharacter* Character = Cast<AAwCharacter>(MeshComp->GetOwner());
	if(Character)
	{
		FVector BoneLocation = MeshComp->GetOwner()->GetActorLocation();
		if(MeshComp->GetBoneIndex(FName(SocketName)) != INDEX_NONE)
			BoneLocation = MeshComp->GetBoneLocation(FName(SocketName));
		UGameplayFuncLib::CreateVoiceStimulate(<PERSON>Loc<PERSON>, VoiceRange, Character);
	}
}

