// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotify.h"
#include "UObject/Object.h"
#include "FightWillModifyInGivenLevel.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UFightWillModifyInGivenLevel : public UAnimNotify
{
	GENERATED_BODY()
public:
	//在哪个FightWill.Level下修改FightWill的值
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int GivenLevel = 0;
	
	//要改变的量
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int ModifyValue = -10;

	//因为改变而导致FightingWill.Value降低到0以下的时候，FightingWill.Level的变化规则
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int LevelModifyRule = -1;

	virtual void Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, const FAnimNotifyEventReference& EventReference) override;
};
