// Fill out your copyright notice in the Description page of Project Settings.


#include "CharacterJump.h"

#include "Components/SkeletalMeshComponent.h"


void UCharacterJump::Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
                            const FAnimNotifyEventReference& EventReference)
{
	Super::Notify(MeshComp, Animation, EventReference);

	const AAwCharacter* OwnerCharacter = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (OwnerCharacter == nullptr) return;

	OwnerCharacter->AwJump(MaxHeight, ReachTopInSec);
	
	// UKismetSystemLibrary::PrintString(this,
	// 	FString("Let's jump(MaxHeight:").Append(FString::FromInt(MaxHeight)).Append("cm, In ").Append(FString::FromInt(ReachTopInSec * 1000)).Append("ms")
	// );
}
