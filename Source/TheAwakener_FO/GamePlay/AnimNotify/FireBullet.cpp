// Fill out your copyright notice in the Description page of Project Settings.


#include "FireBullet.h"

#include "Components/SkeletalMeshComponent.h"
#include "Kismet/KismetMathLibrary.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"


void UFireBullet::Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	Super::Notify(MeshComp, Animation, EventReference);
	
	AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Me) return;
	if (Me->GetLocalRole() != ENetRole::ROLE_Authority) return;
	if(Me->Dead() || Me->IsPendingKillPending()) return;
	
	const FBulletModel BulletModel = UGameplayFuncLib::GetDataManager()->GetBulletModelById(this->BulletId);
	if (BulletModel.Id.IsEmpty()) return;

	const USceneComponent* AttBox = Me->GetAttackHitBoxByName(this->FireAttackHitBoxName);
	if (!AttBox) return;

	FVector TargetLoc = Me->GetActorRotation().RotateVector(NoTargetOffset) + AttBox->GetComponentLocation();
	FVector BaseTargetLoc = TargetLoc;
	switch (BulletTarget)
	{
	case EBulletTargetType::Self:
		{
		}break;
	case EBulletTargetType::AITargetAlly:
		{
			const AAwCharacter* TargetGuy = Me->GetAIComponent()->GetTargetAlly().Character;
			if (TargetGuy)
				TargetLoc =  TargetGuy->GetActorLocation(); 
		}break;
	case EBulletTargetType::AITargetEnemy:
		{
			const AAwCharacter* TargetGuy = Me->GetAIComponent()->GetTargetEnemy().Character;
			if (TargetGuy)
				TargetLoc =  TargetGuy->GetActorLocation();
			
		}break;
	case EBulletTargetType::AITargetLocation:
		{
			TargetLoc =  Me->GetAIComponent()->GetTargetLocation();
		}break;
	}

	FVector TargetDirection = TargetLoc- AttBox->GetComponentLocation();
	TargetDirection.Normalize();

	//索敌角度以外则视为丢失目标 
	float Angle = UMathFuncLib::GetDegreeBetweenTwoVector(TargetDirection,Me->GetActorForwardVector());
	if (FMath::Abs(Angle)>LoseTargetAngle)
	{
		TargetLoc=BaseTargetLoc;
	}
	
	FVector StartLoc = Me->GetActorRotation().RotateVector(FireLocationOffset) + AttBox->GetComponentLocation();
	FVector BulletDir = TargetLoc - StartLoc;
	BulletDir.Normalize();
	FRotator TempBulletRotator = BulletDir.Rotation() + BulletRotatorOffset;
	FVector TempBulletDir = TempBulletRotator.Vector();
	
	TempBulletDir.Z = UKismetMathLibrary::FClamp(TempBulletDir.Z,-ZMaxDir,ZMaxDir);

	
	/*FBulletLauncher Launcher = FBulletLauncher(Me, BulletModel,
		StartLoc, BulletDir, this->BulletDuration,
		this->TweenFunc, TargetLoc
	);*/

	FBulletLauncher Launcher = FBulletLauncher(Me, BulletModel,
		StartLoc, TempBulletDir, this->BulletDuration,
		this->TweenFunc, TargetLoc
	);
	if (bUseFireBoxRotation)
	{
		FJsonFuncData SetRotFunc = UDataFuncLib::SplitFuncNameAndParams("BulletScript.SetBulletRotation()");
		SetRotFunc.Params.Add(FString::SanitizeFloat(AttBox->GetComponentRotation().Roll));
		SetRotFunc.Params.Add(FString::SanitizeFloat(AttBox->GetComponentRotation().Pitch));
		SetRotFunc.Params.Add(FString::SanitizeFloat(AttBox->GetComponentRotation().Yaw));
		TArray<FJsonFuncData> ModelCreateList = Launcher.Model.OnCreate;
		Launcher.Model.OnCreate.Insert(SetRotFunc, 0);
	}
	AAwBullet* Bullet = UGameplayFuncLib::CreateBullet(Launcher);
	if(Bullet)
		Bullet->AttackInfo = this->AttackInfo;
	
	
}

//AAwCharacter* Caster, FString BulletID, FVector Position, FVector Direction, float ActionPower, float Scale, FVector DirectionOffset, int Level, FString TweenFunc