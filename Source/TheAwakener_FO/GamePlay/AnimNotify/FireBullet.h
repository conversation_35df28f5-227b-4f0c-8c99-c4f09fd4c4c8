// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "FireBullet.generated.h"

/**
 * 子弹目标获取类型
 */
UENUM()
enum class EBulletTargetType
{
	Self,
	AITargetEnemy,
	AITargetAlly,
	AITargetLocation
};

/**
 * 发射一颗子弹。
 * 【注意】这和“子弹发射点”概念不同，在这里是直接发射预设的子弹，而不是“只是约定了一个发射点，发不发射由动作决定”
 * 为什么只能发射一颗？
 * 因为即使是真的需要同时发射多个，也应该是同一个位置多放几个这个Notify，发射子弹应该是严肃的，要被策划认真调整的，尤其是这种预设的子弹
 */
UCLASS()
class THEAWAKENER_FO_API UFireBullet : public UAnimNotify
{
	GENERATED_BODY()
public:
	//子弹的Id，首先这个子弹肯定得配在json，否则一切白瞎
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString BulletId;

	//子弹发射点的Id，这必须是一个AttackHitBox，没有就去增加一个就好了
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString FireAttackHitBoxName;

	//子弹发射点的偏移值
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FVector FireLocationOffset = FVector::ZeroVector;

	//子弹的移动函数
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString TweenFunc;

	//子弹选择移动目标的方式
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	EBulletTargetType BulletTarget = EBulletTargetType::Self;

	//子弹目标不存在时候的默认目标（以自己的Transform为准偏移）
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FVector NoTargetOffset = FVector::ZeroVector;
	
	//想要子弹射出时方向偏移角度的值（ -180 到 180 ）
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FRotator BulletRotatorOffset = FRotator::ZeroRotator;

	//索敌角度 为0-n角度绝对值
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	float LoseTargetAngle = 180;
	
	// 子弹Z轴偏移最大值
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float ZMaxDir = 0.0f;

	//子弹初始旋转使用firebox的旋转
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool bUseFireBoxRotation = true;
	
	//设置为动作值
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float ActionPower = 1.f;

	//子弹的等级
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int BulletLevel = 1;

	//子弹生命周期（秒）
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float BulletDuration = 1.00f;

	//子弹的攻击相关数值
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FAttackInfo AttackInfo;

	virtual void Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, const FAnimNotifyEventReference& EventReference) override;
};
