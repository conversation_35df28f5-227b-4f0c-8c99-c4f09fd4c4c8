// Fill out your copyright notice in the Description page of Project Settings.


#include "AIParams.h"

#include "Components/SkeletalMeshComponent.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"

void UAIParams::Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	Super::Notify(MeshComp, Animation, EventReference);

	AAwCharacter* Character = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Character) return;
	if (!Character->GetAIComponent()) return;
	for (FAIParamsStr CurParam : Params)
	{
		if (Character->GetAIComponent()->Params.Contains(CurParam.ParamsName))
		{
			Character->GetAIComponent()->Params[CurParam.ParamsName] = CurParam.ParamsValue;
		}
		else
		{
			Character->GetAIComponent()->Params.Add(CurParam.ParamsName, CurParam.ParamsValue);
		}
	}
}
