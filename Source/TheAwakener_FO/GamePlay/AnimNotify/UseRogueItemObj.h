// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/GamePlay/Item/ItemObj.h"
#include "UseRogueItemObj.generated.h"

/**
 * 使用道具（ItemObj）的Notify
 */
UCLASS()
class THEAWAKENER_FO_API UUseRogueItemObj : public UAnimNotify
{
	GENERATED_BODY()
public:
	//使用的效果分组
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FString> UseMethodTags = {"Default"};
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool bUseHealPotion = false;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool bUseCost = true;
	//如果使用失败，则跳转到某个Section，不写就是失败与否都不会跳转
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FName GoToSectionOnUseFail = FName();

	virtual void Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, const FAnimNotifyEventReference& EventReference) override;
};
