// Fill out your copyright notice in the Description page of Project Settings.


#include "StopCurrentMontage.h"

#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"


void UStopCurrentMontage::Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	Super::Notify(MeshComp, Animation, EventReference);
	
	AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Me) return;
	FAnimMontageInstance* Montage = Me->GetActiveMontageInstance();
	if (!Montage) return;
	Montage->Stop(FAlphaBlend(this->BlendOutInSec));
}
