// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotify.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "LoopCheck.generated.h"

/**
 * 动画中的循环检查点
 */
UCLASS()
class THEAWAKENER_FO_API ULoopCheck : public UAnimNotify
{
	GENERATED_BODY()
	
public:
	/**
	 *按键的类型条件，如果符合才会通过。
	 *比如加特林机枪，既可以是Hold也可以是Press，那么2个都是true就可以，有一个就能通过
	 *如果2个都写False，就会默认通过
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	bool CmdHold = false;

	/**
	 *按键的类型条件，如果符合才会通过。
	 *比如加特林机枪，既可以是Hold也可以是Press，那么2个都是true就可以，有一个就能通过
	 *如果2个都写False，就会默认通过
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	bool CmdPress = false;

	/**
	 *即便通过了，也不是直接有效。
	 *如果函数不存在，则按键通过就会当做返回0，走向第1个跳转点。
	 *这是一个(AwCharacter*, FActionInfo, ULoopCheck*)=>int的函数
	 *(谁、什么动作、这个点)=>跳转到哪个GotoTime下标
	 *返回值代表了去第几个跳转点，如果跳转点<0或者超出长度，就会当做【条件未通过而不进行跳转】。
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString CheckFunction;

	/**
	 *在成功loop时候才会触发的回调函数
	 *(AwCharacter*, FActionInfo, ULoopCheck*, int)=>TimelineNode*
	 *(谁、什么动作、这个点、跳到第几个Goto点)=>表演项目
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString OnLoop;

	/**
	 * 是否在全部条件通过（CheckFunction返回>0的值）之后直接继续，而非跳转？
	 * 如果仅仅只是判断这个点没通过就返回某个时间点，那么这个应该是True
	 * 如果是True，在条件满足之后将不会走到CheckFunction返回的值对应的点
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	bool AutoContinueOnMeetCondition = true;

	/**
	 * CheckFunction返回值指向某個Section的Id
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FName> GotoSection;

	
	
	virtual void Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, const FAnimNotifyEventReference& EventReference) override;
};
