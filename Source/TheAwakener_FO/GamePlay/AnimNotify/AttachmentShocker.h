// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotify.h"
#include "AttachmentShocker.generated.h"

/**
 * 尝试把身上攀附的人震下来
 */
UCLASS()
class THEAWAKENER_FO_API UAttachmentShocker : public UAnimNotify
{
	GENERATED_BODY()
public:
	/**
	 * 要针对的部位id
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FString> PartId;

	/**
	 * 对这个部位的人进行Break多少的震动
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int BreakPower = 10;

	/**
	 *如果BreakPower对抗角色的Balance结果>=这个值，角色就会掉下来
	 *否则将会做失衡动作？
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int StopAttachThreshold = 0;

	virtual void Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, const FAnimNotifyEventReference& EventReference) override;
};
