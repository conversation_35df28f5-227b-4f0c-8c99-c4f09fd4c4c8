// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotify.h"
#include "ModifyBuff.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UModifyBuff : public UAnimNotify
{
	GENERATED_BODY()

public:
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString BuffId;

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int BuffStack = -1;

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	float BuffDuration = -1;
	
	virtual void Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, const FAnimNotifyEventReference& EventReference) override;
};
