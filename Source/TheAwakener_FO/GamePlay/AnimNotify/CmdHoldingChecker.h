// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotify.h"
#include "CmdHoldingChecker.generated.h"

/**
 * 当前动作对应的Cmd是否Holding中，如果Holding就会继续播放，否则跳到指定的Section
 */
UCLASS()
class THEAWAKENER_FO_API UCmdHoldingChecker : public UAnimNotify
{
	GENERATED_BODY()
public:
	/**
	 * 如果按键没有按住，就会跳转到这个Section，否则会继续走下去
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FName GotoSection;

	virtual void Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, const FAnimNotifyEventReference& EventReference) override;
};
