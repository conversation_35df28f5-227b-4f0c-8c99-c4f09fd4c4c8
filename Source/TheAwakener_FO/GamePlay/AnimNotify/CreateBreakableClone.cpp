// Fill out your copyright notice in the Description page of Project Settings.


#include "CreateBreakableClone.h"

#include "Components/SkeletalMeshComponent.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GamePlay/Characters/AvatarCharacter/BreakableClone.h"

void UCreateBreakableClone::Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	Super::Notify(MeshComp, Animation, EventReference);

	AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	
	if (!Me) return;
	
	ABreakableClone* Ava = UGameplayFuncLib::GetAwGameInstance()->MakeBreakableClone(Me, Offset, LifeDuration,
		DoOffense, OffenseInfo, DefenseInfo, ChaResource,
		true, FLinearColor(0.96f, 0.47f, 0.15f, 1.f));
	if (!Ava) return;
	Ava->PlayCloneAnim();
}
