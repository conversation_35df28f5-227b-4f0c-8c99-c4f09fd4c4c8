// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotify.h"
#include "UObject/Object.h"
#include "AIComboStrike.generated.h"

/**
 * 仅根据AIState来决定是否做下一个action的
 * 是Preorder的，不走Cancel，所以基本必出，也不用配置Cancellable
 */
UCLASS()
class THEAWAKENER_FO_API UAIComboStrike : public UAnimNotify
{
	GENERATED_BODY()
private:
	virtual void Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, const FAnimNotifyEventReference& EventReference) override;
public:
	/**
	 *需要在FightingWill多少的时候才能执行
	 *【魔法效果】：如果一个都没，就会不关心FightingWill检测，当做通过
	 */
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	TArray<int> UnderFightingWillLevel;
	
	//在这个点AIState有哪个Key就算满足？
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	FString AIStateContains;

	//要做的下一个动作，直接得是动作Id
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	FString NextActionId;

	//下一个动作从第几秒开始做起
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	float NextActionStartSec = 0;

	//成功预约连招后，删除AIStateContains对应的AIState
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	bool DeleteAIStateContains = true;
	
	//成功预约连招后，要删除的AIState
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	TArray<FString> RemoveAIStates;

	//成功预约连招后，要添加的AIState
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	TArray<FString> AddAIStates;
};
