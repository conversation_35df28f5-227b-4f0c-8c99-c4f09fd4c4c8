// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotify.h"
#include "GetAnimTarget.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UGetAnimTarget : public UAnimNotify
{
	GENERATED_BODY()
public:
	//检测距离
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	float AimDistance = 2500.0f;

	virtual void Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, const FAnimNotifyEventReference& EventReference) override;
};
