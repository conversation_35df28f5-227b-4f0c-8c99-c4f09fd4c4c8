// Fill out your copyright notice in the Description page of Project Settings.


#include "CheckBuffAnimNotfiy_PlayParticleEffect.h"

#include "Components/SkeletalMeshComponent.h"
#include "TheAwakener_FO/FunctionLibrary/CommonFuncLib.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"


void UCheckBuffAnimNotfiy_PlayParticleEffect::Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,const FAnimNotifyEventReference& EventReference)
{
	AAwCharacter* Character = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Character) return;

	PRAGMA_DISABLE_DEPRECATION_WARNINGS
	Super::Notify(MeshComp, Animation);
	PRAGMA_ENABLE_DEPRECATION_WARNINGS
	// Don't call super to avoid unnecessary call in to blueprints
	
	if (!ParticleParameterData)
	{
		SpawnParticleSystem(MeshComp, Animation);
	}
	else
	{
		for (auto Tag : CheckBuffTags)
		{
			bool bUseParentNotify = false;

			for (auto Buff : Character->CharacterObj.Buff)
			{
				if (Buff.Model.Tags.Contains(Tag))
				{
					if (AbilityIdCheck.Num() == 0)
					{
						bUseParentNotify = true;
						break;
					}
					else
					{
						if (UCommonFuncLib::ArrayContainsArray(Buff.Model.Tags, AbilityIdCheck, false))
						{
							bUseParentNotify = true;
							break;
						}
					}
				}
			}
			if (bUseParentNotify)
			{
				FName RowName = FName(Tag);
				FAnimPlayParticleEffectData* Data  = ParticleParameterData->FindRow<FAnimPlayParticleEffectData>(RowName,nullptr);
				if (Data)
				{
					PSTemplate = Data->PSTemplate;
					LocationOffset = Data->LocationOffset;
					RotationOffset = Data->RotationOffset;
					Scale = Data ->Scale;
					Attached = Data->Attached;
					SocketName = Data->SocketName;
				}
				SpawnParticleSystem(MeshComp, Animation);
			}
		}
	}

}


