// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotify.h"
#include "TheAwakener_FO/GamePlay/Dialog/DialogBubbleComponent.h"
#include "ChangeDialogBubbleGroup.generated.h"

/**
 * 
 */

UCLASS()
class THEAWAKENER_FO_API UChangeDialogBubbleGroup : public UAnimNotify
{
	GENERATED_BODY()
public:
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		FString DialogBubbleGroupId;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		EDialogBubbleGroupType PlayType;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		bool UseNewPlayType;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		bool bForceSubmit;

	virtual void Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, const FAnimNotifyEventReference& EventReference) override;

};

UCLASS()
class THEAWAKENER_FO_API USubmitNewIndependentDialogBubble : public UAnimNotify
{
	GENERATED_BODY()
public:
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		FDialogBubble NewDialogBubble;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		bool bForceSubmit;

	virtual void Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, const FAnimNotifyEventReference& EventReference) override;

};