// Fill out your copyright notice in the Description page of Project Settings.


#include "LoopCheck.h"
#include "TheAwakener_FO/FunctionLibrary/CallFuncLib.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/Gameframework/Timeline/TimelineManager.h"

void ULoopCheck::Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	Super::Notify(MeshComp, Animation, EventReference);
	
	AAwCharacter* OwnerCharacter = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!OwnerCharacter) return;
	
	const FActionInfo* Action = OwnerCharacter->CurrentAction() ;
	if (Action == nullptr) return;
	
	//判断按键是否通过了
	TArray<TTuple<FString, EAwInputState>> CheckInput;
	for (FString Cmd : Action->Commands)
	{
		if (CmdHold == true) CheckInput.Add(TTuple<FString, EAwInputState>(Cmd, EAwInputState::Hold));
		if (CmdPress == true) CheckInput.Add(TTuple<FString, EAwInputState>(Cmd, EAwInputState::Press));
	}

	if (CheckInput.Num() <= 0 || OwnerCharacter->AnyActionOccur(CheckInput) == true)
	{
		//通过检查
		int GoIndex = 0;
		if (CheckFunction.IsEmpty() == false)
		{
			const FJsonFuncData FuncData =  UCallFuncLib::StringToJsonFuncData(CheckFunction);
			UFunction* Func = UCallFuncLib::JsonFuncToUFunc(FuncData);
			if (IsValid(Func) == true)
			{
				struct {
					AAwCharacter* Cha;
					FActionInfo Act;
					TArray<FString> Param;
				
					int Result;
				} FuncParam;
			
				FuncParam.Cha = OwnerCharacter;
				FuncParam.Act = *Action;
				FuncParam.Param = FuncData.Params;
				
				OwnerCharacter->ProcessEvent(Func, &FuncParam);
				GoIndex = FuncParam.Result;
			}
		}else
		{
			GoIndex = 0;
		}

		UAnimMontage* Montage = OwnerCharacter->GetCurrentActiveMontage();
		FAnimMontageInstance* MontageInstance = OwnerCharacter->GetActiveMontageInstance();
		if (Montage && MontageInstance)
		{
			if (GotoSection.Num() > 0 && (this->AutoContinueOnMeetCondition == false || GoIndex <= 0))
			{
				//只有非自动继续才会跳转
				GoIndex = FMath::Clamp(GoIndex, 0, this->GotoSection.Num() - 1);
				//Montage->JumpToSectionName(GoToSectionName[GoIndex]);
				const int32 SectionID = Montage->GetSectionIndex(GotoSection[GoIndex]);
				if (SectionID != INDEX_NONE)
				{
					float StartTime = 0.f;
					float EndTime = 0.f;
					Montage->GetSectionStartAndEndTime(SectionID, StartTime, EndTime);
					MontageInstance->SetPosition(StartTime); 
				}
			}
		}
		if (OnLoop.IsEmpty() == false)
		{
			const FJsonFuncData FuncData =  UCallFuncLib::StringToJsonFuncData(OnLoop);
			UFunction* Func = UCallFuncLib::JsonFuncToUFunc(FuncData);
			if (IsValid(Func) == true)
			{
				struct {
					AAwCharacter* Cha;
					FActionInfo Act;
					int GotoIndex;
					TArray<FString> Param;
			
					UTimelineNode* Result = nullptr;
				} FuncParam;
		
				FuncParam.Cha = OwnerCharacter;
				FuncParam.Act = *Action;
				FuncParam.GotoIndex = GoIndex;
				FuncParam.Param = FuncData.Params;
			
				OwnerCharacter->ProcessEvent(Func, &FuncParam);
				if (FuncParam.Result)
					UGameplayFuncLib::GetTimelineManager()->AddNode(FuncParam.Result);
			}
		}
	}
	
}
