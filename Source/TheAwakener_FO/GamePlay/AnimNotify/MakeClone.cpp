// Fill out your copyright notice in the Description page of Project Settings.


#include "MakeClone.h"

#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GamePlay/Characters/AvatarCharacter/AvatarCharacter.h"

void UMakeClone::Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	Super::Notify(MeshComp, Animation, EventReference);

	AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	
	if (!Me) return;

	AAvatarCharacter* Ava = UGameplayFuncLib::GetAwGameInstance()->MakeCharacterClone(Me, StayTime, true, FLinearColor(0.96f, 0.47f, 0.15f, 1.f));
	if (!Ava) return;
	Ava->PlayCloneAnim();
}
