// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotify.h"
#include "ChangeCharacterControllable.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UChangeCharacterControllable : public UAnimNotify
{
	GENERATED_BODY()
	
public:

	//是否受到控制
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	bool Controllable = false;
	
	virtual void Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, const FAnimNotifyEventReference& EventReference) override;
};
