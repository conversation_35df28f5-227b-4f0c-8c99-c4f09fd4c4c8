// Fill out your copyright notice in the Description page of Project Settings.


#include "BuddyAIOrder.h"

#include "Components/SkeletalMeshComponent.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"

void UBuddyAIOrder::Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	Super::Notify(MeshComp, Animation, EventReference);
	
	AAwCharacter* OwnerCharacter = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (OwnerCharacter == nullptr) return;
	
	const UAwTeamManager* TeamManager = UGameplayFuncLib::GetTeamManager();
	if(TeamManager)
	{
		for(const auto BuddyCharacter : TeamManager->TeamMateList)
		{
			if(BuddyCharacter->Dead(true)) continue;
			FAIOrder NewOrder = FAIOrder();
			switch (OrderType)
			{
			case EBuddyAIOrderType::MoveToTarget:
				{
					if(UGameplayFuncLib::GetAwGameState()->AimCharacter)
						NewOrder = BuddyCharacter->GetAIComponent()->MakeMoveToTargetAIOrder(UGameplayFuncLib::GetAwGameState()->AimCharacter);
					else if(UGameplayFuncLib::GetAwGameState()->AimLocation != FVector::ZeroVector)
						NewOrder = BuddyCharacter->GetAIComponent()->MakeMoveToLocationAIOrder(UGameplayFuncLib::GetAwGameState()->AimLocation);
					break;
				}
			case EBuddyAIOrderType::MoveToPlayer:
				{
					NewOrder = BuddyCharacter->GetAIComponent()->MakeMoveToTargetAIOrder(OwnerCharacter);
					break;
				}
			case EBuddyAIOrderType::AssistFirst:
				{
					NewOrder = BuddyCharacter->GetAIComponent()->MakeAssistFirstAIOrder();
					break;
				}
			case EBuddyAIOrderType::Auto:
				{
					NewOrder = BuddyCharacter->GetAIComponent()->MakeAutoAIOrder();
					break;
				}
			}
			BuddyCharacter->GetAIComponent()->SetCurAIOrder(NewOrder);
		}
	}
}

