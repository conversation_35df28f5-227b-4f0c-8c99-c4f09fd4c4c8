// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotify_PlayParticleEffect.h"
#include "UObject/Object.h"
#include "PlayParticleOnlyCharacter.generated.h"


/**
 * 仅在角色（AwCharacter）身上播放粒子特效
 */
UCLASS()
class THEAWAKENER_FO_API UPlayParticleOnlyCharacter : public UAnimNotify_PlayParticleEffect
{
	GENERATED_BODY()
private:
	virtual void Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, const FAnimNotifyEventReference& EventReference) override;
};
