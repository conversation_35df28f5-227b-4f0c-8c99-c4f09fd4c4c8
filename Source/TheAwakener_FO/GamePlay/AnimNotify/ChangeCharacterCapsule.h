// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotify.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "ChangeCharacterCapsule.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UChangeCharacterCapsule : public UAnimNotify
{
	GENERATED_BODY()
	
public:

	//胶囊体高度
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	float CapsuleHalfHeight = 110.0f;

	//胶囊体半径
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	float CapsuleRadius = 45.0f;


	virtual void Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, const FAnimNotifyEventReference& EventReference) override;
};
