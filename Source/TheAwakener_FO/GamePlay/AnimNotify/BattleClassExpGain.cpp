// Fill out your copyright notice in the Description page of Project Settings.


#include "BattleClassExpGain.h"

#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"

void UBattleClassExpGain::Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	Super::Notify(MeshComp, Animation, EventReference);

	const AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Me || Me->IsPlayerCharacter() == false) return;

	if (UGameplayFuncLib::GetAwGameInstance() && UGameplayFuncLib::GetAwGameState() && UGameplayFuncLib::GetAwGameState()->GetMyCharacter())
	{
		const int BattleClassIndex = UGameplayFuncLib::GetAwGameInstance()->RoleInfo.GetBattleClassIndexById(UGameplayFuncLib::GetAwGameState()->GetMyCharacter()->PlayerClassId);
		if (BattleClassIndex >= 0)
		{
			const int LevelUp = UGameplayFuncLib::GetAwGameInstance()->RoleInfo.BattleClassInfo[BattleClassIndex].LevelExp.AddExp(ExpGain);
			if (LevelUp > 0)
			{
				UKismetSystemLibrary::PrintString(this, FString("Class Level Up ").Append(FString::FromInt(LevelUp)),
					true, true, FLinearColor::Green, 30);
			}
		}
	}
}
