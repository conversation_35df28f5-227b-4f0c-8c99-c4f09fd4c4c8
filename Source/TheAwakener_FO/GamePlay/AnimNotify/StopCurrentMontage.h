// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotify.h"
#include "StopCurrentMontage.generated.h"

/**
 * 停掉当前的Montage，也就是到此为止了
 * 用于多分叉道路的终点，比如当前
 */
UCLASS()
class THEAWAKENER_FO_API UStopCurrentMontage : public UAnimNotify
{
	GENERATED_BODY()
public:
	//淡出时间，如果这个是0.1，请注意在真正结束前0.1秒的地方放这个，不然还会继续播放下一个动作的0.1秒
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float BlendOutInSec = 0.1f;

	virtual void Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, const FAnimNotifyEventReference& EventReference) override;
};
