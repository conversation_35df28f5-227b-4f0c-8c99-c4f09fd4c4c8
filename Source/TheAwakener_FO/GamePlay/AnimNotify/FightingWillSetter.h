// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotify.h"
#include "UObject/Object.h"
#include "FightingWillSetter.generated.h"

/**
 * 将Fighting Will的等级设置为新的值
 */
UCLASS()
class THEAWAKENER_FO_API UFightingWillSetter : public UAnimNotify
{
	GENERATED_BODY()
public:
	//设置的目标等级
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	int ToLevel;

	//如果目标等级等于现在的等级，是否要重置Value
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	bool ForceResetValue = true;

	virtual void Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, const FAnimNotifyEventReference& EventReference) override;
};
