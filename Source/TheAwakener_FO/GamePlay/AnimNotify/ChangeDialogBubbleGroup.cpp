// Fill out your copyright notice in the Description page of Project Settings.


#include "ChangeDialogBubbleGroup.h"

#include "Components/SkeletalMeshComponent.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"




void UChangeDialogBubbleGroup::Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	Super::Notify(MeshComp, Animation, EventReference);

	AAwCharacter* OwnerCharacter = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (OwnerCharacter == nullptr) return;

	UDialogBubbleComponent* DialogBubbleComponent = Cast<UDialogBubbleComponent>(OwnerCharacter->GetComponentByClass(UDialogBubbleComponent::StaticClass()));
	if (DialogBubbleComponent)
	{
		if (UseNewPlayType)
		{
			DialogBubbleComponent->ChangeCurDialogBubbleGroupInSpecialType(DialogBubbleGroupId, PlayType, bForceSubmit);
		}
		else
		{
			DialogBubbleComponent->ChangeCurDialogBubbleGroup(DialogBubbleGroupId,bForceSubmit);
		}
	}
}

void USubmitNewIndependentDialogBubble::Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	Super::Notify(MeshComp, Animation, EventReference);

	AAwCharacter* OwnerCharacter = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (OwnerCharacter == nullptr) return;

	UDialogBubbleComponent* DialogBubbleComponent = Cast<UDialogBubbleComponent>(OwnerCharacter->GetComponentByClass(UDialogBubbleComponent::StaticClass()));
	if (DialogBubbleComponent)
	{
		DialogBubbleComponent->SubmitNewIndependentDialogBubble(NewDialogBubble,bForceSubmit);
	}
}
