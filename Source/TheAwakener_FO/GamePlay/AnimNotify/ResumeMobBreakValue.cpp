// Fill out your copyright notice in the Description page of Project Settings.


#include "ResumeMobBreakValue.h"

#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"

void UResumeMobBreakValue::Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
                                  const FAnimNotifyEventReference& EventReference)
{
	Super::Notify(MeshComp, Animation, EventReference);
	
	AAwCharacter* Character = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Character) return;
	if(Character->GetBreakSystemComponent())
		Character->GetBreakSystemComponent()->ResumeBreakValue();
}

