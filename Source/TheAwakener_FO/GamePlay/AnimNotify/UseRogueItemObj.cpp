// Fill out your copyright notice in the Description page of Project Settings.


#include "UseRogueItemObj.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"

void UUseRogueItemObj::Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, const FAnimNotifyEventReference& EventReference)
{
	Super::Notify(MeshComp, Animation, EventReference);
	
	AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	auto PC = UGameplayFuncLib::GetPlayerControllerByComp(MeshComp);
	if (!Me || !PC) return;

	UAwRogueItemSubSystem* SubSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueItemSubSystem>();
	if (!SubSystem)
	{return;}
	
	bool UseSuccess =false;
	
	if (bUseHealPotion)
	{
		UseSuccess = SubSystem->UseRogueItemById(this->UseMethodTags,"HealingPotion_Rogue",PC->GetLocalPCIndex(),bUseCost);
	}
	else
	{
		UseSuccess = SubSystem->UseCurRogueItem(this->UseMethodTags,PC->GetLocalPCIndex(),bUseCost);
	}
	
	if (UseSuccess == true || this->GoToSectionOnUseFail.IsNone() == true) return;
	UAnimMontage* Montage = Me->GetCurrentActiveMontage();
	FAnimMontageInstance* MontageInstance = Me->GetActiveMontageInstance();
	if (Montage && MontageInstance)
	{
		const int32 SectionID = Montage->GetSectionIndex(this->GoToSectionOnUseFail);
		if (SectionID != INDEX_NONE)
		{
			float StartTime = 0.f;
			float EndTime = 0.f;
			Montage->GetSectionStartAndEndTime(SectionID, StartTime, EndTime);
			MontageInstance->SetPosition(StartTime); 
		}
	}
}
