// Fill out your copyright notice in the Description page of Project Settings.


#include "ChangeCharacterCapsule.h"

#include "Components/SkeletalMeshComponent.h"

class AAwCharacter;

void UChangeCharacterCapsule::Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
                                      const FAnimNotifyEventReference& EventReference)
{
	Super::Notify(MeshComp, Animation, EventReference);

	const AAwCharacter* OwnerCharacter = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (OwnerCharacter == nullptr) return;

	USqueezeCapsule* Temp = Cast<USqueezeCapsule>(OwnerCharacter->GetComponentByClass(USqueezeCapsule::StaticClass()));
	if(Temp == nullptr) return;
	Temp->SetCapsuleHalfHeight(CapsuleHalfHeight);
	Temp->SetCapsuleRadius(CapsuleRadius);
}
