// Fill out your copyright notice in the Description page of Project Settings.


#include "DebugNotify.h"

#include "Components/SkeletalMeshComponent.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "TheAwakener_FO/GamePlay/Thing/ThingUIInfo.h"

void UDebugNotify::Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	Super::Notify(MeshComp, Animation, EventReference);

	AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	// if (!Me)
	// {
	// 	UKismetSystemLibrary::PrintString(this, FString("No ME found"));
	// 	return;
	// }
	// Me->ShowTextBubble(this->Dialog, this-><PERSON><PERSON><PERSON>, 20);
	//
	// UGameplayFuncLib::GetAwGameInstance()->UIManager->MessageDialog("Guardian1_Speech5", TArray<FString>(), TArray<FString>());

	if (Me)
	{
		FVector V3 = Me->GetActorForwardVector() * Force;
		V3.Z = Height;
		FForceMoveInfo MoveInfo = FForceMoveInfo(); 
		MoveInfo.Active = true;
		MoveInfo.Type = EForceMoveType::KnockOut;
		MoveInfo.Velocity = V3; 
		MoveInfo.InSec = 0.5;
		MoveInfo.AutoTerminateOnTimeUp = false;
		MoveInfo.TerminateOnGround = true;
		MoveInfo.TerminateOnActionChange = false;
		
		Me->AddForceMove(MoveInfo);
	}
}
