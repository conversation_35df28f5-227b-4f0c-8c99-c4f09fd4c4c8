// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotify.h"
#include "TheAwakener_FO/GamePlay/AOE/AOELauncher.h"
#include "CreateAOE.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UCreateAOE : public UAnimNotify
{
	GENERATED_BODY()
public:
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		FString AOEId;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		float LifeSpan = 0;
	UPROPERTY(BlueprintReadOnly, EditAnywhere)
		FString TweenFunc;
	//以调用这个Notify的Character的Transform为坐标系原点的相对位置
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		FTransform OffsetTransform = FTransform();
	//是否已OffsetTransform.Location为起点向下做射线检测地面坐标为AOE的生成位置
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		bool FromOffsetGetGroundLoc = false;
	//已OffsetTransform.Location为起点向下做射线的射线长度
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		float TraceZ = 0;
	
	virtual void Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, const FAnimNotifyEventReference& EventReference) override;
};
