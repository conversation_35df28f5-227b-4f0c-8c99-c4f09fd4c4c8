// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotify.h"
#include "CreateSceneItem.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UCreateSceneItem : public UAnimNotify
{
	GENERATED_BODY()
public:
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString SceneItemId;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	float LifeSpan = 0;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int Side = 0;
	//以调用这个Notify的Character的Transform为坐标系原点的相对位置
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FTransform OffsetTransform = FTransform();
	//是否已OffsetTransform.Location为起点向下做射线检测地面坐标为SceneItem的生成位置
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	bool FromOffsetGetGroundLoc = false;
	//已OffsetTransform.Location为起点向下做射线的射线长度
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	float TraceZ = 0;
	
	virtual void Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, const FAnimNotifyEventReference& EventReference) override;
};
