// Fill out your copyright notice in the Description page of Project Settings.


#include "AIComboStrike.h"

#include "Components/SkeletalMeshComponent.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"

void UAIComboStrike::Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	Super::Notify(MeshComp, Animation, EventReference);

	if (this->NextActionId.IsEmpty()) return;

	const AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Me) return;

	if (UnderFightingWillLevel.Num() > 0)
	{
		if (UnderFightingWillLevel.Contains(Me->FightingWill.Level) == false)
		{
			return;
		}
	}

	if (!this->AIStateContains.IsEmpty() && !Me->GetAIComponent()->UseActionTags.Contains(AIStateContains)) return;

	Me->PreorderAction(this->NextActionId, FActionParam(), this->NextActionStartSec);

	if (this->DeleteAIStateContains) Me->GetAIComponent()->UseActionTags.Remove(AIStateContains);
	for (FString RState : this->RemoveAIStates) Me->GetAIComponent()->UseActionTags.Remove(RState);
	for (FString AState : this->AddAIStates) Me->GetAIComponent()->UseActionTags.Add(AState); 
}
