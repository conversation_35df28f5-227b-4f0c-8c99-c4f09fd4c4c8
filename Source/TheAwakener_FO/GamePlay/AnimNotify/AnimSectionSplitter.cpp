// Fill out your copyright notice in the Description page of Project Settings.


#include "AnimSectionSplitter.h"

#include "Components/SkeletalMeshComponent.h"
#include "TheAwakener_FO/FunctionLibrary/CallFuncLib.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"

void UAnimSectionSplitter::Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	Super::Notify(MeshComp, Animation, EventReference);

	if (this->GotoSection.Num() <= 0) return;
	
	AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Me) return;

	UAnimMontage* Montage = Me->GetCurrentActiveMontage();
	FAnimMontageInstance* MontageInstance = Me->GetActiveMontageInstance();
	if (!Montage || !MontageInstance) return;

	const FActionInfo* Action = Me->CurrentAction() ;
	const FJsonFuncData FuncData =  UCallFuncLib::StringToJsonFuncData(CheckFunction);
	UFunction* Func = UCallFuncLib::JsonFuncToUFunc(FuncData);
	int GoIndex = 0;
	if (IsValid(Func) == true)
	{
		struct {
			AAwCharacter* Cha;
			FActionInfo Act;
			TArray<FString> Param;
				
			int Result;
		} FuncParam;
			
		FuncParam.Cha = Me;
		FuncParam.Act = *Action;
		FuncParam.Param = FuncData.Params;
				
		Me->ProcessEvent(Func, &FuncParam);
		GoIndex = FuncParam.Result;
	}

	GoIndex = FMath::Clamp(GoIndex, 0, GotoSection.Num() - 1);
	if (GotoSection[GoIndex].IsNone() == false)
	{
		const int32 SectionID = Montage->GetSectionIndex(GotoSection[GoIndex]);
		float StartTime = 0.f;
		float EndTime = 0.f;
		Montage->GetSectionStartAndEndTime(SectionID, StartTime, EndTime);
		UKismetSystemLibrary::PrintString(this, FString("Splitter:").Append(FString::FromInt(StartTime * 1000)));
		//MontageInstance->SetPosition(StartTime);
		Me->GetAwAnimInstance()->Montage_Play(Montage, 1, EMontagePlayReturnType::MontageLength, StartTime);
	}
}
