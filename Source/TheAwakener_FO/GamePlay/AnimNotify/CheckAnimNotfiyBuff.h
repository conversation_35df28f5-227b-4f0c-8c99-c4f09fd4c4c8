// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotify.h"
#include "TheAwakener_FO/GamePlay/Roguelike/BattleStyle/RogueBattleTag.h"
#include "CheckAnimNotfiyBuff.generated.h"

class AAw<PERSON>haracter;

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UCheckAnimNotfiyBuff : public UAnimNotify
{
	GENERATED_BODY()
public:
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		TArray<FString> CheckBuffTags;
	
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		bool  bContainsAllTags = false;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		bool  bUseAnimTransform = false;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		FRotator Rotator= FRotator::ZeroRotator;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		FVector PosOffset = FVector::ZeroVector;
	
	virtual void Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, const FAnimNotifyEventReference& EventReference) override;
};
