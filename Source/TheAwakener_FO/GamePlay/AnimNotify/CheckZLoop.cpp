// Fill out your copyright notice in the Description page of Project Settings.


#include "CheckZLoop.h"

#include "Components/SkeletalMeshComponent.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"

void UCheckZLoop::Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	Super::Notify(MeshComp, Animation, EventReference);
	
	AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Me) return;
	UAnimMontage* Montage = Me->GetCurrentActiveMontage();
	FAnimMontageInstance* MontageInstance = Me->GetActiveMontageInstance();
	if (!Montage || !MontageInstance) return;
	
	const float ZChanged = Me->ZChange();
	if (FMath::IsNearlyZero(ZChanged))
	{
		GoToSection(this->ZNearZeroSection, Montage, MontageInstance);
	}else
	{
	    const FName GoSection = ZChanged > 0 ? this->ZUpSection : this->ZDownSection;
	    UKismetSystemLibrary::PrintString(Me, FString("Z Loop ").Append(GoSection.ToString()));
		GoToSection(GoSection, Montage, MontageInstance);
	}
}


void UCheckZLoop::GoToSection(FName SecName, UAnimMontage* Montage, FAnimMontageInstance* MontageInstance) 
{
	const int32 SectionID = Montage->GetSectionIndex(SecName);
	if (SectionID != INDEX_NONE)
	{
		float StartTime = 0.f;
		float EndTime = 0.f;
		Montage->GetSectionStartAndEndTime(SectionID, StartTime, EndTime);
		MontageInstance->SetPosition(StartTime); 
	}
}