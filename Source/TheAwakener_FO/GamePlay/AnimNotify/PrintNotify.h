// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "PrintNotify.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UPrintNotify : public UAnimNotify
{
	GENERATED_BODY()
public:

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString InString = "";

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	bool bPrintToScreen = true;

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	bool bPrintToLog = true;

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FLinearColor TextColor = FLinearColor(0, 0.66, 1);

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	float Duration = 2;
	
	virtual void Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, const FAnimNotifyEventReference& EventReference) override;
};
