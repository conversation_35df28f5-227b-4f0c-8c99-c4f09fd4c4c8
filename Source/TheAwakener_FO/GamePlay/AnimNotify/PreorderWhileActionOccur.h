// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotify.h"
#include "TheAwakener_FO/GameFramework/Input/EnumCmd.h"
#include "PreorderWhileActionOccur.generated.h"

/**
 * 当一个按键的条件符合的时候，就会预约跳转到某个Action
 */
UCLASS(BlueprintType)
class THEAWAKENER_FO_API UPreorderWhileActionOccur : public UAnimNotify
{
	GENERATED_BODY()
public:
	//检查的按键，这如果是空的，代表不检查按键直接PreorderAction
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FString> Commands;

	//按键状态，这要是None，代表不检查按键，直接PreorderAction
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	EAwInputState InputState = EAwInputState::Press;
	
	//要切换到的动作的Id，这如果是空的，这个Notify就没用了
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString ToActionId;

	//切换后从第几秒开始播放
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float FromSec;

	virtual void Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, const FAnimNotifyEventReference& EventReference) override;
};
