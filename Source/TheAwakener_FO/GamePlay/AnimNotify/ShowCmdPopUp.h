// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotify.h"
#include "TheAwakener_FO/UI/Roguelike/Common/CmdTipPopUp.h"
#include "ShowCmdPopUp.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UShowCmdPopUp : public UAnimNotify
{
	GENERATED_BODY()

public:
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FRogueCmdTipInfo> CmdTipInfos;

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	float Duration = 0.8f;

	virtual void Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
		const FAnimNotifyEventReference& EventReference) override;
};
