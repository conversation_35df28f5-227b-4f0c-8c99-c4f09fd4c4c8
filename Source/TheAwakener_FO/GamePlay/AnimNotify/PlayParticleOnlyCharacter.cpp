// Fill out your copyright notice in the Description page of Project Settings.


#include "PlayParticleOnlyCharacter.h"

#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"

void UPlayParticleOnlyCharacter::Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	if (!Cast<AAwCharacter>(MeshComp->GetOwner())) return;
	
	Super::Notify(MeshComp, Animation, EventReference);
}
