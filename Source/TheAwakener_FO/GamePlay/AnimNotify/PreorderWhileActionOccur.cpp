// Fill out your copyright notice in the Description page of Project Settings.


#include "PreorderWhileActionOccur.h"

#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"

void UPreorderWhileActionOccur::Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	Super::Notify(MeshComp, Animation, EventReference);
	
	if (this->ToActionId.IsEmpty()) return;

	AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Me) return;

	for (FString Command : Commands)
	{
		if (Command.IsEmpty() || this->InputState == EAwInputState::None || Me->IsActionOccur(Command, this->InputState) == true)
		{
			Me->PreorderAction(this->ToActionId, FActionParam(), this->FromSec);
			break;
		}
	}
}
