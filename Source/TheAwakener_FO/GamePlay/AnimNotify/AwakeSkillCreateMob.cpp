// Fill out your copyright notice in the Description page of Project Settings.


#include "AwakeSkillCreateMob.h"

#include "Components/SkeletalMeshComponent.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "Kismet/KismetMathLibrary.h"
#include "TheAwakener_FO/FunctionLibrary/DateTimeFuncLib.h"

void UAwakeSkillCreateMob::Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
                        const FAnimNotifyEventReference& EventReference)
{
	Super::Notify(MeshComp, Animation, EventReference);
	
	AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Me) return;
	if (Me->GetLocalRole() != ENetRole::ROLE_Authority) return;
	if (Me->Dead() || Me->IsPendingKillPending()) return;

	//需要重设一下Transform的Rotation
	FTransform CharacterTrans = Me->GetTransform();
	CharacterTrans.SetRotation(FRotator(0,CharacterTrans.GetRotation().Rotator().Yaw,0).Quaternion());
	
	FVector ActualLoc = UKismetMathLibrary::TransformLocation(CharacterTrans, OffsetTransform.GetLocation());
	FVector ActualDir = UKismetMathLibrary::TransformRotation(CharacterTrans, OffsetTransform.GetRotation().Rotator()).Quaternion().GetForwardVector();
	if(FromOffsetGetGroundLoc)
	{
		TArray<TEnumAsByte<EObjectTypeQuery>> ObjectList;
		ObjectList.Add(EObjectTypeQuery::ObjectTypeQuery1);
		FHitResult HitResult;
		bool bHit = UKismetSystemLibrary::LineTraceSingleForObjects(Me, ActualLoc, ActualLoc + FVector(0,0,TraceZ),
			ObjectList, false, TArray<AActor*>(), EDrawDebugTrace::None,HitResult, true
			//,FLinearColor::Red,FLinearColor::Green, 10
			);
		if(bHit)
		{
			ActualLoc = HitResult.Location;
		}
		else
		{
			ActualLoc.Z = Me->GetActorLocation().Z;
		}
	}

	CharacterTrans.SetLocation(ActualLoc);
		//SpawnInfo
		FNPCSpawnInfo NewSpawnInfo = FNPCSpawnInfo();
		NewSpawnInfo.NpcInfo = Mob.NpcInfo;
		NewSpawnInfo.Tag = Mob.Tag;
		NewSpawnInfo.Buffs = Mob.Buffs;
		NewSpawnInfo.AIClips = Mob.AIClips;
		NewSpawnInfo.OnCreate = Mob.OnCreate;
		NewSpawnInfo.SpawnTrans = CharacterTrans;
		NewSpawnInfo.PathNodeQueueId = Mob.PathNodeQueueId;
		NewSpawnInfo.PathNodeQueueIndex = Mob.PathNodeQueueIndex;
		NewSpawnInfo.QuestData = Mob.QuestData;
		FString UniqueId = UDateTimeFuncLib::CreateUniqueId("NPCSpawnInfo");
		UGameplayFuncLib::GetAwGameState()->NPCSpawnInfoList.Add(UniqueId, NewSpawnInfo);
	
		UFunction* ActionFunc;
		ActionFunc = UCallFuncLib::GetUFunction("TriggerScript", "SpawnNPCBySpawnInfo");
		if (ActionFunc)
		{
			struct
			{
				TArray<FString> Params;
				UTimelineNode* Result = nullptr;
			}ActionFuncParam;
			ActionFuncParam.Params.Add(UniqueId);
			ActionFuncParam.Params.Add(NewSpawnInfo.Tag);
			this->ProcessEvent(ActionFunc, &ActionFuncParam);
			AAwPlayerState* MyPlayerState = UGameplayFuncLib::GetPlayerControllerByComp(MeshComp)->GetPlayerState<AAwPlayerState>();
			if (MyPlayerState)
			{
				UE_LOG(LogClass,Error,TEXT("SkillCreateMob::Notify Error,can not get PlayerState. It is called from something else."));
				MyPlayerState->ClearAwakeSkillCreature.Broadcast();
			}
			if (ActionFuncParam.Result)
			{
				const UAwGameInstance* GameInstance = Cast<UAwGameInstance>(GWorld->GetGameInstance());
				GameInstance->TimelineManager->AddNode(ActionFuncParam.Result);
			}
		}
}
