// Fill out your copyright notice in the Description page of Project Settings.


#include "FightWillModifyInGivenLevel.h"

#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"

void UFightWillModifyInGivenLevel::Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	Super::Notify(MeshComp, Animation, EventReference);

	AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Me) return;

	if(Me->FightingWill.Level == this->GivenLevel)
		Me->FightingWill.ModifyValue(this->ModifyValue, this->LevelModifyRule, true);
}
