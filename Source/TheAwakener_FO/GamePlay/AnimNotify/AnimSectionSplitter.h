// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotify.h"
#include "AnimSectionSplitter.generated.h"

/**
 * 动画根据条件进行区间分流，而不是单独的指向某帧，这是和LoopCheck的根本区别
 */
UCLASS()
class THEAWAKENER_FO_API UAnimSectionSplitter : public UAnimNotify
{
	GENERATED_BODY()
public:
	/**
	 *这是一个(AwCharacter*, FActionInfo, UAnimSectionSplitter*, Params)=>FString的函数
	 *(谁、什么动作、这个点)=>跳转到哪个Section
	 *返回值代表了去第几个跳转点，如果跳转点<0或者超出长度，就会当做【条件未通过而不进行跳转】。
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString CheckFunction;

	/**
	 * 要跳转的Section的id，如果不存在，就不会跳转
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FName> GotoSection;

	virtual void Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, const FAnimNotifyEventReference& EventReference) override;
};
