// Fill out your copyright notice in the Description page of Project Settings.


#include "AISaveTargetEnemyLocation.h"

#include "Components/SkeletalMeshComponent.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"

void UAISaveTargetEnemyLocation::Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	Super::Notify(MeshComp, Animation, EventReference);

	AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Me) return;
	if (Me->GetLocalRole() != ENetRole::ROLE_Authority) return;
	if(Me->Dead() || Me->IsPendingKillPending()) return;

	if(Me->GetAIComponent()->GetTargetEnemy().Character)
		Me->GetAIComponent()->SetTargetLocation(Me->GetAIComponent()->GetTargetEnemy().Character->GetActorLocation());
}
