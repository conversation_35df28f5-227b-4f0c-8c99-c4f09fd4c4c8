// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "BattleClassExpGain.generated.h"

/**
 * 获得职业经验（给当前的职业）
 */
UCLASS()
class THEAWAKENER_FO_API UBattleClassExpGain : public UAnimNotify
{
	GENERATED_BODY()
public:
	//经验值获取量
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int ExpGain = 1;

	virtual void Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, const FAnimNotifyEventReference& EventReference) override;
};
