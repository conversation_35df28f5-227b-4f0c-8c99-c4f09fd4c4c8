// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"

#include "Animation/AnimNotifies/AnimNotify_PlaySound.h"
#include "Engine/DataTable.h"
#include "CheckBuffAnimNotfiy_PlaySound.generated.h"


USTRUCT()
struct FAnimPlaySoundData  :public  FTableRowBase
{
	GENERATED_BODY()
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="AnimNotify")
	TObjectPtr<USoundBase> Sound;

	// Volume Multiplier
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="AnimNotify")
	float VolumeMultiplier = 1.0f;

	// Pitch Multiplier
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="AnimNotify")
	float PitchMultiplier = 1.0f;

	// If this sound should follow its owner
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AnimNotify")
	uint32 bFollow:1;

#if WITH_EDITORONLY_DATA
	UPROPERTY(Config, EditAnywhere, Category = "AnimNotify")
	uint32 bPreviewIgnoreAttenuation:1;
#endif

	// Socket or bone name to attach sound to
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="AnimNotify")
	FName AttachName;
};
/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UCheckBuffAnimNotfiy_PlaySound : public UAnimNotify_PlaySound
{
	GENERATED_BODY()
public:
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FString> AbilityIdCheck;
	
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		TArray<FString> CheckBuffTags;
	
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		UDataTable* SoundData = nullptr;
	virtual void Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, const FAnimNotifyEventReference& EventReference) override;
	
};
