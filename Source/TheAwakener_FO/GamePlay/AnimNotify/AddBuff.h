// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotify.h"
#include "AddBuff.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UAddBuff : public UAnimNotify
{
	GENERATED_BODY()
public:
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		FString BuffId;

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		int BuffStack;

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		bool SetToDuration;

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		float BuffTime;

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		bool bInfinity;
	
	virtual void Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, const FAnimNotifyEventReference& EventReference) override;
};
