// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimMontage.h"
#include "Animation/AnimNotifies/AnimNotify.h"
#include "CheckZLoop.generated.h"

/**
 *  根据z坐标变化进行跳转，None的点代表不跳转
 */
UCLASS(BlueprintType)
class THEAWAKENER_FO_API UCheckZLoop : public UAnimNotify
{
	GENERATED_BODY()
private:

	static void GoToSection(FName SecName, UAnimMontage* Montage, FAnimMontageInstance* MontageInstance);
public:
	//当Z向上的时候，跳转到那个Section
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FName ZUpSection;

	//当Z向下的时候，跳转到那个Section
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FName ZDownSection;

	//当Z接近0的时候，跳转到那个Section
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FName ZNearZeroSection;

	virtual void Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, const FAnimNotifyEventReference& EventReference) override;
};
