// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotify.h"
#include "AIPickFaceDirectionByAIClip.generated.h"

/**
 * AI在这个Notify的时候会根据JsonFunction申请旋转角度
 * 因为具体的数据都在AIComponent了，所以不会有任何参数需要填写
 * 如果需要更定制化，则应该使用AIPickFaceDirectionInAction
 */
UCLASS()
class THEAWAKENER_FO_API UAIPickFaceDirectionByAIClip : public UAnimNotify
{
	GENERATED_BODY()
	
	virtual void Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, const FAnimNotifyEventReference& EventReference) override;
};
