// Fill out your copyright notice in the Description page of Project Settings.


#include "CheckBuffAnimNotfiy_PlaySound.h"

#include "TheAwakener_FO/FunctionLibrary/CommonFuncLib.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"


void UCheckBuffAnimNotfiy_PlaySound::Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,const FAnimNotifyEventReference& EventReference)
{
	AAwCharacter* Character = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Character) return;

	PRAGMA_DISABLE_DEPRECATION_WARNINGS
	Super::Notify(MeshComp, Animation);
	PRAGMA_ENABLE_DEPRECATION_WARNINGS
	// Don't call super to avoid unnecessary call in to blueprints
	
	if (!SoundData)
	{
		Super::Notify(MeshComp, Animation, EventReference);
	}
	else
	{
		for (auto Tag : CheckBuffTags)
		{
			bool bUseParentNotify = false;

			for (auto Buff : Character->CharacterObj.Buff)
			{
				if (Buff.Model.Tags.Contains(Tag))
				{
					if (AbilityIdCheck.Num() == 0)
					{
						bUseParentNotify = true;
						break;
					}
					else
					{
						if (UCommonFuncLib::ArrayContainsArray(Buff.Model.Tags, AbilityIdCheck, false))
						{
							bUseParentNotify = true;
							break;
						}
					}
				}
			}
			if (bUseParentNotify)
			{
				FName RowName = FName(Tag);
				FAnimPlaySoundData* Data  = SoundData->FindRow<FAnimPlaySoundData>(RowName,nullptr);
				if (Data)
				{
					Sound = Data->Sound;
					VolumeMultiplier = Data->VolumeMultiplier;
					PitchMultiplier = Data->PitchMultiplier;
					bFollow = Data->bFollow;
#if WITH_EDITORONLY_DATA
					bPreviewIgnoreAttenuation = Data->bPreviewIgnoreAttenuation;
#endif
					AttachName = Data->AttachName;
				}
				Super::Notify(MeshComp, Animation, EventReference);
			}
		}
	}

}


