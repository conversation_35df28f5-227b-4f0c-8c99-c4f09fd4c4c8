// Fill out your copyright notice in the Description page of Project Settings.


#include "PickUpItem.h"

#include "Components/SkeletalMeshComponent.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "TheAwakener_FO/GamePlay/Interact/CharacterInteractBox.h"

void UPickUpItem::Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	Super::Notify(MeshComp, Animation, EventReference);

	AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (Me == nullptr || Me->Dead(true))
		return;

	UCharacterInteractBox* InteractBox =Cast<UCharacterInteractBox>(Me->GetComponentByClass(UCharacterInteractBox::StaticClass()));
	if (InteractBox)
	{
		InteractBox->InteractWithAllFocusTargets();
	}
	else
	{
		UE_LOG(LogTemp,Warning,TEXT("Anim owner cant interact with others because it has no interact box"));
	}
	// if( UGameplayFuncLib::GetLootManager()->WillPickUpLootActor )
	// 	UGameplayFuncLib::GetLootManager()->WillPickUpLootActor->PickUp(Me);
}
