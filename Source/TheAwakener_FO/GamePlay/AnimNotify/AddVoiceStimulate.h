// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotify.h"
#include "AddVoiceStimulate.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UAddVoiceStimulate : public UAnimNotify
{
	GENERATED_BODY()
public:
	/**
	 * 在角色的哪个骨骼位置去生成声音刺激（如果为空，默认在Root点）
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString SocketName = "";

	/**
	 * 声音的刺激范围
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int VoiceRange = 500;
	
	virtual void Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, const FAnimNotifyEventReference& EventReference) override;
};
