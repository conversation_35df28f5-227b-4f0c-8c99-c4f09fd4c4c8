// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Components/CapsuleComponent.h"
#include "TheAwakener_FO/GamePlay/Characters/ChaPart/ChaPart.h"
#include "TheAwakener_FO/GamePlay/Characters/ChaPart/EnumChaPart.h"
#include "TheAwakener_FO/GamePlay/Characters/Battle/BattleStructs.h"
#include "CharacterHitBox.generated.h"

// 角色部位种类
UENUM(BlueprintType)
enum class ECharacterHitBoxType: uint8
{
	//一般受击框
	Normal,
	//格挡框
	Guard,
	//just躲避框
	JustDodge
};

/**
 * 在蓝图里，角色受到攻击的盒子
 */
UCLASS(ClassGroup="Collision", editinlinenew, hidecategories=(Object,LOD,Lighting,TextureStreaming), meta=(DisplayName="Character Hit Box", BlueprintSpawnableComponent))
class THEAWAKENER_FO_API UCharacterHitBox : public UCapsuleComponent
{
	GENERATED_BODY()

	UCharacterHitBox()
	{
		ShapeColor = FColor(124, 255, 0, 255);
	}
	
public:
	virtual void BeginPlay() override;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString Id;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	ECharacterHitBoxType Type;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	EChaPartType PartType;

	// 伤害盒生效角度，如果攻击actor和受击actor正面的夹角 > 设定的值，则这个伤害盒不起效
	// 默认值是 360/2 = 180
	// 例如这个是个防御框，值为90，即只对正面有防御性能
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int CheckDeg = 180;
	
	//挂向了哪个Seat的AttachPoint，如果这不是空，就能抓了
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString SeatPointId;

	//所属的部位
	FChaPart* BelongsToPart = nullptr;

	//被视为Just Dodge，通常都是false的，只有被Montage AnimState开启了才会变成true
	UPROPERTY(BlueprintReadOnly)
	FJustDodgeInfo AsJustDodge = FJustDodgeInfo();

	/**
	 * 默认情况下是否是启用的
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool DefaultActive = Type == ECharacterHitBoxType::Normal;
	
	//这个盒子的默认防御信息，在没有特殊的情况下，就会启用这个防御信息作为受攻击的时候的防御信息
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FDefenseInfo DefaultDefenseInfo;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float HitPhysicalAnimBlendRate = 1.0f;
	
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int Priority = 1;

	virtual void SetActive(bool bNewActive, bool bReset) override;
private:
	ECollisionEnabled::Type DefaultCollisionEnable;
};
