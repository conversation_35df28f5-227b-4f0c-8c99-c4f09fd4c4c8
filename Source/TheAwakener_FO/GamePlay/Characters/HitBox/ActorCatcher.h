// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/GamePlay/ApparatusCustom/Traits/Attacks.h"
#include "TheAwakener_FO/GamePlay/AssetUserData/BeCaughtHitBox.h"
#include "TheAwakener_FO/GamePlay/Characters/Attack/ActionTouchInfo.h"
#include "TheAwakener_FO/GamePlay/Characters/Battle/BattleStructs.h"
#include "ActorCatcher.generated.h"

class AAwBullet;
/**
 * 抓取范围内的类别
 */
UENUM(BlueprintType)
enum class EActorCaughtState : uint8
{
	Caught,	//抓住的
	Enter,		//刚被抓的
	Leave		//正在离开
};

/**
 * 抓住的Actor的信息
 */
USTRUCT(BlueprintType)
struct FBeCaughtActorInfo
{
	GENERATED_BODY()
public:
	//是被那个AttackBox捕捉到的
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString AttackBoxName ;
	
	//Actor是哪个
	UPROPERTY(BlueprintReadOnly)
	AActor* BeCaughtActor = nullptr;
	UPROPERTY(BlueprintReadOnly)
	FSubjectHandle BeCaughtSubject;

	FSubjectHandle AttackBoxSubject;
	
	//碰到的盒子对象
	UPROPERTY(BlueprintReadOnly)
	USceneComponent* CaughtHitBoxComponent = nullptr;
	//碰到的盒子的信息
	UPROPERTY(BlueprintReadOnly)
	UBeCaughtHitBox* CaughtHitBoxData = nullptr;

	//是否刚被抓
	UPROPERTY(BlueprintReadOnly)
	EActorCaughtState CaughtState = EActorCaughtState::Enter;

	//Leaving之后弥留多久，归0之后移除掉
	UPROPERTY(BlueprintReadOnly)
	float DelayDuration = 0;

	FBeCaughtActorInfo(){};
	FBeCaughtActorInfo(FString NameOfAttackBox, AActor* CaughtGuy, USceneComponent* CaughtBox, float LeavingDelay):
		AttackBoxName(NameOfAttackBox), BeCaughtActor(CaughtGuy),
		CaughtHitBoxComponent(CaughtBox), CaughtHitBoxData(CaughtBox->GetAssetUserData<UBeCaughtHitBox>()),
		DelayDuration(LeavingDelay){};

	// ECS怪物专用构造函数
	FBeCaughtActorInfo(FString NameOfAttackBox, FSubjectHandle CaughtMonster, float LeavingDelay):
		AttackBoxName(NameOfAttackBox), BeCaughtActor(nullptr), BeCaughtSubject(CaughtMonster),
		CaughtHitBoxComponent(nullptr), CaughtHitBoxData(nullptr),
		DelayDuration(LeavingDelay){};
	

	bool operator == (const FBeCaughtActorInfo& Other) const
	{
		//因为多个盒子会捕捉到同一个目标，所以不同盒子不是同一条数据，所以不能BoxName相等
		//return BeCaughtActor == Other.BeCaughtActor && BeHitBox == Other.BeHitBox;
		return BeCaughtActor == Other.BeCaughtActor &&
			CaughtHitBoxComponent == Other.CaughtHitBoxComponent &&
				CaughtHitBoxData == Other.CaughtHitBoxData;
	}
};

/**
 * 是否可以攻击目标的检测结果
 */
USTRUCT(BlueprintType)
struct FOffendedCaughtResult
{
	GENERATED_BODY()
public:
	//是否可以侵犯目标
	UPROPERTY(BlueprintReadOnly)
	bool Hit = false;

	//如果可以，那么侵犯目标的攻击信息是什么
	UPROPERTY(BlueprintReadOnly)
	FOffenseInfo OffenseInfo;

	//如果可以，被命中的信息是什么
	UPROPERTY(BlueprintReadOnly)
	FBeCaughtActorInfo BeCaughtActorInfo;

	//造成命中的盒子
	UPROPERTY(BlueprintReadOnly)
	USceneComponent* AttackHitBox = nullptr;

	FOffendedCaughtResult(){};
	FOffendedCaughtResult(FOffenseInfo OffendingInfo):
		Hit(true), OffenseInfo(OffendingInfo){};
	FOffendedCaughtResult(FOffenseInfo OffendingInfo, FBeCaughtActorInfo CaughtActorInfo, USceneComponent* OffenseHitBox):
		Hit(true),OffenseInfo(OffendingInfo), BeCaughtActorInfo(CaughtActorInfo), AttackHitBox(OffenseHitBox){};
};

/**
 * 这是一个管理用Component，如果要用攻击框，就应该配套加上这个东西，用来专门管理命中信息等
 */
UCLASS(ClassGroup="Collision", editinlinenew, hidecategories=(LOD,Lighting,TextureStreaming), meta=(DisplayName="Attack Hit Manager", BlueprintSpawnableComponent))
class THEAWAKENER_FO_API UAttackHitManager: public UActorComponent
{
	GENERATED_BODY()
protected:
	virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;
	virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;
private:
	//所有的命中盒子<盒子名称id，盒子指针>
	UPROPERTY()
	TMap<int32, UPrimitiveComponent*> AttackBoxes;
	//当前激活中的AttackHitBoxes
	UPROPERTY()
	TArray<int32> LivingAttackBoxes;
	//是否开始了
	UPROPERTY()
	bool Started = false;
	//Apparatus Trait for AttackBox
	TMap<int32, FSubjectHandle> AttackBoxEntity;

	TArray<FSubjectHandle*> ActionHitEnemiesECS;
public:
	UAttackHitManager();
	//命中的记录
	UPROPERTY(BlueprintReadOnly)
	TArray<FOffenseHitRecord> HitRecords;

	UFUNCTION()
	void BindAttackBoxECS(const int32& BoxKey,const FSubjectHandle& Subject,const FTrAttackBox& AttackBox);

	//抓到的角色们（包括传统Actor和ECS怪物）
	UPROPERTY(BlueprintReadOnly)
	TArray<FBeCaughtActorInfo> Actors;

	//是否允许同一个目标不同部位分别Enter 
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	bool bSameActorEnter = true;
	//时间推进
	void Update(float DeltaTime);

	//清理所有的命中纪录，比如角色换动作的时候
	UFUNCTION(BlueprintCallable)
	void ClearAllHitRecords();

	/**
	 *仅仅检查HitRecord的情况下，判断角色是否能被命中
	 *@param Target 目标角色
	 *@param AttackSourceId 攻击信息的SourceId
	 *@param Index 攻击信息的Index
	 *@return true代表可以被命中
	 */
	UFUNCTION(BlueprintCallable)
	bool TargetCanBeHitByHitRecord(AActor* Target, FString AttackSourceId, int Index);

	//TODO 即将干掉
	//某个角色信息正在离开，【magic】如果BeHitBox==nullptr，则会删除所有Actor相等的
	// UFUNCTION(BlueprintCallable)
	// void SetCaughtActorAsLeaving(const AActor* Actor, const UBeHitBoxBase* BeHitBox);

	//获得所有刚进入的角色的数据
	UFUNCTION(BlueprintCallable)
	TArray<FBeCaughtActorInfo> EnterActors();

	//获得所有属于其中的角色的数据
	UFUNCTION(BlueprintCallable)
	TArray<FBeCaughtActorInfo> CaughtActors();

	//获得所有正在离开的角色数据
	UFUNCTION(BlueprintCallable)
	TArray<FBeCaughtActorInfo> LeavingActors();

	//TODO: 即将被干掉
	/**
	 * 新加入一个角色到碰撞，OverlapBegin时候干的
	 * @param AttackBoxName 碰到的碰撞盒名
	 * @param Actor 被碰到的角色
	 * @param BeHitBox 碰到的盒子指针
	 * @param LeavingDelay 在OverlapEnd的时候，Leaving可以延迟多少秒才真的移除掉
	 */
	//UFUNCTION(BlueprintCallable)
	//void AddActor(FString AttackBoxName, AActor* Actor, UBeHitBoxBase* BeHitBox, float LeavingDelay);

	/**
	 * 新加入一个角色到碰撞，OverlapBegin时候干的
	 * @param AttackBoxName 碰到的碰撞盒名
	 * @param Actor 被碰到的角色
	 * @param CaughtBox 碰到的盒子指针
	 * @param LeavingDelay 在OverlapEnd的时候，Leaving可以延迟多少秒才真的移除掉
	 */
	UFUNCTION(BlueprintCallable)
	void AddActor(FString AttackBoxName, AActor* Actor, USceneComponent* CaughtBox, float LeavingDelay);

	/**
	 * 新加入一个ECS怪物到碰撞，使用现有的AddActor但传入Subject
	 * @param AttackBoxName 碰到的碰撞盒名
	 * @param Monster 被碰到的ECS怪物Subject
	 * @param LeavingDelay 在离开的时候，Leaving可以延迟多少秒才真的移除掉
	 */
	UFUNCTION(BlueprintCallable)
	void AddECSMonster(FString AttackBoxName, FSubjectHandle Monster, float LeavingDelay);

	//TODO: 即将被干掉
	//通过AttackHitBox的Name移除抓住的角色
	// UFUNCTION(BlueprintCallable)
	// void RemoveActorByAttackBoxName(FString AttackBoxName, AActor* Actor, UBeHitBoxBase* BeHitBox);

	//通过AttackHitBox的Name移除抓住的角色
	UFUNCTION(BlueprintCallable)
	void RemoveActorByAttackBoxName(FString AttackBoxName, AActor* Actor, USceneComponent* CaughtBox);

	//新增一个记录数据
	UFUNCTION(BlueprintCallable)
	void AddHitRecord(FOffenseHitRecord Record);

	//激活一个攻击框
	UFUNCTION(BlueprintCallable)
	void ActiveAttackHitBox(FString BoxName);

	//激活所有攻击框
	UFUNCTION(BlueprintCallable)
	void ActiveAllAttackHitBox();
	
	//取消一个攻击框的激活
	UFUNCTION(BlueprintCallable)
	void DeactiveAttackBox(FString BoxName);

	//关闭所有的激活的攻击框
	UFUNCTION(BlueprintCallable)
	void DeactiveAllAttackBox();
	
	//激活一批ECS攻击框
	UFUNCTION(BlueprintCallable)
	void ActiveECSAttack(const FString& BoxName,EFlagmarkBit HitFlag);
	//取消一批ECS攻击框的激活
	UFUNCTION(BlueprintCallable)
	void DeactiveECSAttack(const FString& BoxName);
	//获取ECS攻击框的Flag
	UFUNCTION(BlueprintCallable)
	EFlagmarkBit GetECSAttackFlag(const FString& BoxName);
	//同步攻击碰撞盒和ECS碰撞盒的位置
	UFUNCTION(BlueprintCallable)
	void SyncECSAttackBoxPosition(const int32& BoxKey);
	//根据名字获得一个攻击盒的指针，可能是nullptr的
	UFUNCTION(BlueprintCallable)
	UPrimitiveComponent* GetAttackBoxByName(const FString& AttackBoxName);
	UFUNCTION(BlueprintCallable)
	UPrimitiveComponent* GetAttackBoxByKey(const int32 BoxKey);

	UFUNCTION(BlueprintCallable,BlueprintPure)
	//获得激活中的盒子的名字
	TArray<FString> GetAllActiveAttackBoxNames();
	UFUNCTION(BlueprintCallable,BlueprintPure)
	//获得激活中的盒子的Key
	TArray<int32> GetAllActiveAttackBoxKeys();

	EAttackBoxType AttackBoxType = Melee;
	AAwCharacter* GetSourceCharacter();
	void InitAttackBox();
	UFUNCTION()
	virtual void BeginPlay() override;

	//启动这个Timer来运行Update的功能，并不是所有的对象都需要这样来运行，比如aoe，可能就可以有自身的Timer来执行Update就好了
	UFUNCTION(BlueprintCallable)
	void Start()
	{
		Started = true;
	};
	
	UFUNCTION()
    virtual void OnOverlapBegin(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult);
    UFUNCTION()
    virtual void OnOverlapEnd(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex);
	UFUNCTION()
	void SetupECSCollision(AAwCharacter* Char,UActorComponent* HitBox,UPrimitiveComponent* Comp,AMechanism* Mechanism,int32 BoxNameKey);
	/**
	 * 添加一个攻击盒子，如果对应Id的盒子已经存在，就会添加失败
	 * @param Id 要当做的id
 	 * @param Box 要添加的盒子
	 */
	UFUNCTION(BlueprintCallable)
	void AddAttackHitBox(int32 BoxKey, UPrimitiveComponent* Box);
	
	/**
	 *当前条件下符合被抓住的目标信息（包括传统Actor和ECS怪物），注意：这里并不做HitRecord检测，，因为这里只负责告诉你是否有盒子命中，而不知道你的攻击sourceId和Index（没理由要知道）
	 *@param MergeSameTarget 是否需要合并同一个挨打的目标的信息，合并的话，返回值内同一个Actor只会有一条（TODO：目前是遍历的第一条，也许需要有一个pick合适的一条的方案）
	 *@param IncludeJustEnter 是否包含刚被抓住的目标，大多情况应该是包含的
	 *@param IncludeLeaving 是否包含正在离开范围的目标，大多情况是不应该的
	 *@param TargetType 目标类型过滤：0=所有目标，1=仅传统Actor，2=仅ECS怪物
	 *@return 符合条件的信息组
	 */
	// 为了保持向后兼容性的重载版本（不暴露给Blueprint）
	TArray<FBeCaughtActorInfo> ThisTickValidTargetInternal(bool MergeSameTarget = true, bool IncludeJustEnter = true, bool IncludeLeaving = false, int32 TargetType = 0);
	UFUNCTION(BlueprintCallable)
	TArray<FBeCaughtActorInfo> ThisTickValidTarget(bool MergeSameTarget, bool IncludeJustEnter, bool IncludeLeaving);

	/**
	 *当前条件下符合被抓住的ECS怪物信息，为了保持向后兼容性而保留的便捷方法
	 *@param MergeSameTarget 是否需要合并同一个挨打的怪物的信息
	 *@param IncludeJustEnter 是否包含刚被抓住的怪物
	 *@param IncludeLeaving 是否包含正在离开范围的怪物
	 *@return 符合条件的ECS怪物信息组（BeCaughtSubject有效，BeCaughtActor为nullptr）
	 */
	UFUNCTION(BlueprintCallable)
	TArray<FBeCaughtActorInfo> ThisTickValidECSTarget(bool MergeSameTarget = true, bool IncludeJustEnter = true, bool IncludeLeaving = false);

	/**
	 * 全面检查本帧能否捕捉的目标
	 * @param Target 检查是否能打的目标
	 * @param WithOffending 使用这些攻击信息作为参考
	 * @param IncludeJustEnter 如果正在进入算不算有效
	 * @param IncludeLeaving 如果正在离开是否视作有效
	 * @return 有效命中的信息
	 */
	UFUNCTION(BlueprintCallable)
	TArray<FOffendedCaughtResult> GetThisTickTargetCaughtInfo(AActor* Target, TArray<FOffenseInfo> WithOffending, bool IncludeJustEnter = true, bool IncludeLeaving = true);

	//检查ECS怪物命中
	void CheckECSMonsterHit();
};


