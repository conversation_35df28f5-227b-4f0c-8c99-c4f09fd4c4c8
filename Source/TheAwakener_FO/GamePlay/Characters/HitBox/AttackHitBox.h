// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Components/CapsuleComponent.h"
#include "AttackHitBox.generated.h"

/**
 * 在蓝图里，角色发动攻击的盒子
 */
UCLASS(ClassGroup="Collision", editinlinenew, hidecategories=(Object,LOD,Lighting,TextureStreaming), meta=(DisplayName="Attack Hit Box", BlueprintSpawnableComponent))
class THEAWAKENER_FO_API UAttackHitBox : public UCapsuleComponent
{
	GENERATED_BODY()
	virtual void BeginPlay() override;
	UAttackHitBox()
	{
		ShapeColor = FColor(255, 89, 0, 255);
	}
	
public:
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	bool DefaultActive = false;
	
	virtual void SetActive(bool bNewActive, bool bReset) override;
	
private:
	ECollisionEnabled::Type DefaultCollisionEnable;
};
