// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Components/CapsuleComponent.h"
#include "TheAwakener_FO/FunctionLibrary/DataFuncLib.h"
#include "InterruptComponent.generated.h"


/**
 * 
 */
class AAwCharacter;

UENUM(BlueprintType)
enum class EInterruptableType :uint8
{
	//无法被打扰
	UnInterruptable,
	//仅能被玩家打扰
	OnlyCanInterruptedFromPlayer,
	//能被所有角色打扰 不确定是否区分敌人 理论上敌人必进战斗 进战斗就不进行常态打扰行为的判断
	CanInterruptedFromAnyone,
};

UENUM(BlueprintType)
enum class EInterruptedLevel :uint8
{
	//无打扰
	NoInterruption,
	//对当前打扰行为低兴趣
	LowInterested,
	//对当前打扰行为中兴趣
	MidInterested,
	//对当前打扰行为高兴趣
	HighInterested
};



USTRUCT()
struct FInterruptInfo
{
	GENERATED_BODY()
public:
	UPROPERTY()
		EInterruptedLevel OrgInterestedLevel = EInterruptedLevel::NoInterruption;
	
	//原打算用于重复行为耐受降低兴趣等级 暂无用
	UPROPERTY()
		EInterruptedLevel CurInterestedLevel= EInterruptedLevel::NoInterruption;
	
	UPROPERTY()
		AAwCharacter* InterruptSource = nullptr;
	UPROPERTY()
		float lifetime = 0;
	void ReSet() {
		CurInterestedLevel = EInterruptedLevel::NoInterruption;
		OrgInterestedLevel = EInterruptedLevel::NoInterruption;
		InterruptSource = nullptr;
		lifetime = 0;
	}
};
//能Overlap且该组件枚举不为UnInterruptable 就能触发干扰打断状态
UCLASS(ClassGroup = "Interrupt", hidecategories = (Object, LOD, Lighting, TextureStreaming),
	meta = (DisplayName = "InterruptComponent", BlueprintSpawnableComponent))
class THEAWAKENER_FO_API UInterruptComponent : public UCapsuleComponent
{
	GENERATED_BODY()

public:
	UInterruptComponent();
protected:
	virtual void BeginPlay() override;
	virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

public:
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
		AAwCharacter* OwnerCharacter = nullptr;

	//是否能被打扰
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = Interrupt)
		EInterruptableType InterruptableType;
	//是否同步角色胶囊体碰撞形状
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = Interrupt)
		bool bSyncOwnerCapsule = true;
	//重复打扰的记忆时间 如果该时间小于等于0 则表示不长记性 否则持续时间内不会被反复打扰
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = Interrupt)
		float MaxMemoryTime = 0.0;

	//碰撞检测半径偏移 以角色椭圆碰撞为基准
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = Interrupt)
		float RadiusOffset = 0;
	//碰撞检测半高偏移 以角色椭圆碰撞为基准
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = Interrupt)
		float HalfHeightOffset = 0;
	//兴趣等级判断脚本 优先级高于默认判断方式
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = Interrupt)
		FJsonFuncData InterestLevelScript;
	//当前被打扰等级
	UPROPERTY()
		EInterruptedLevel InterruptedLevel = EInterruptedLevel::NoInterruption;
	//最后一次被打扰的信息
	UPROPERTY()
		FInterruptInfo LastInterruptionInfo;
public:
	//当前是否处于被打扰状态
	UFUNCTION(BlueprintCallable)
		bool IsInInterruption() {return InterruptedLevel!= EInterruptedLevel::NoInterruption;}
	UFUNCTION(BlueprintCallable)
		EInterruptedLevel GetInterruptedState() { return InterruptedLevel;}

	UFUNCTION(BlueprintCallable)
		void OnOverlapBegin(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor,
			UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult);
	UFUNCTION(BlueprintCallable)
		void OnOverlapEnd(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor,
			UPrimitiveComponent* OtherComp, int32 OtherBodyIndex);
	    //打扰行为的兴趣程度判断
		void IsInterruptedSourceInterested(AAwCharacter* OtherCharacter, const FHitResult& SweepResult);
		//历史记忆的tick 检测 主要判断是否有过期的需要移除
		void TickCheckInterruptionHistory(float DeltaTime);

};
