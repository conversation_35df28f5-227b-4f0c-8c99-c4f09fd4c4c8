// Fill out your copyright notice in the Description page of Project Settings.


#include "TrackSqueezeWeigthModifer.h"

UTrackSqueezeWeigthModifer::UTrackSqueezeWeigthModifer()
{
	PrimaryComponentTick.bCanEverTick = true;
}

void UTrackSqueezeWeigthModifer::BeginPlay()
{
	Super::BeginPlay();

	SqueezeComp = Cast<USqueezeComp>(this->GetOwner()->GetComponentByClass(USqueezeComp::StaticClass()));
	TrackMoveComponent = Cast<UTrackMoveComponent>(this->GetOwner()->GetComponentByClass(UTrackMoveComponent::StaticClass()));
	
	if (SqueezeComp)
		DefaultWeight = SqueezeComp->Weight;
}

void UTrackSqueezeWeigthModifer::TickComponent(float DeltaTime, ELevelTick TickType,
	FActorComponentTickFunction* ThisTickFunction)
{
	Super::TickComponent(DeltaTime, TickType, ThisTickFunction);

	if (IsValid(TrackMoveComponent) && IsValid(SqueezeComp))
	{
		if (TrackMoveComponent->MoveForce*LastMoveForce < 0)
			SetWeight(GetWeigthByMoveForce(true)); // 板车撞墙回头
		else
			// 根据当前移动速度修改weight，速度越大weight越大，速度超过max的一半开始增加。
			SetWeight(FMath::Max(SqueezeComp->Weight, GetWeigthByMoveForce(false)));

		if (FMath::IsNearlyZero(TrackMoveComponent->MoveForce))
			RestoreWeight();
			
		// UKismetSystemLibrary::PrintString(this, FString::SanitizeFloat(SqueezeComp->Weight));
		
		LastMoveForce = TrackMoveComponent->MoveForce;
	}
}

void UTrackSqueezeWeigthModifer::SetWeight(int NewWeight)
{
	if (IsValid(SqueezeComp))
		SqueezeComp->Weight = NewWeight;
}

void UTrackSqueezeWeigthModifer::RestoreWeight()
{
	if (IsValid(SqueezeComp))
		SqueezeComp->Weight = DefaultWeight;
}

int UTrackSqueezeWeigthModifer::GetWeigthByMoveForce(bool IsBlockWall)
{
	if (IsBlockWall)
		return DefaultWeight + 3;

	if (IsValid(TrackMoveComponent) &&
		TrackMoveComponent->MoveForce > TrackMoveComponent->MaxMoveForce / 2)
	{
		const float DiffMoveForce = TrackMoveComponent->MoveForce - TrackMoveComponent->MaxMoveForce/2;
		return DefaultWeight + FMath::Floor(DiffMoveForce/250);
	}
	
	return DefaultWeight;
}

