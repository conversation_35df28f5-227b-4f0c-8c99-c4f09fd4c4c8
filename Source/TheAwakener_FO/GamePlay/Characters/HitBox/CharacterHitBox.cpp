// Fill out your copyright notice in the Description page of Project Settings.


#include "CharacterHitBox.h"

#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GameFramework/Manager/AwDataManager.h"


void UCharacterHitBox::BeginPlay()
{
	Super::BeginPlay();
	DefaultCollisionEnable = GetCollisionEnabled();
	SetCollisionEnabled(DefaultActive?DefaultCollisionEnable:ECollisionEnabled::NoCollision);
	
	if (UGameplayFuncLib::GetDataManager() &&
		this->Type == ECharacterHitBoxType::Normal && UGameplayFuncLib::GetDataManager()->DebugConfig.ShowNormalHitBox ||
		this->Type == ECharacterHitBoxType::Guard && UGameplayFuncLib::GetDataManager()->DebugConfig.ShowGuardHitBox ||
		this->Type == ECharacterHitBoxType::JustDodge && UGameplayFuncLib::GetDataManager()->DebugConfig.ShowJustDodgeBox)
		this->SetHiddenInGame(DefaultActive?false:true);
}

void UCharacterHitBox::SetActive(bool bNewActive, bool bReset)
{
	Super::SetActive(bNewActive, bReset);
	if (IsActive())
	{
		SetCollisionEnabled(DefaultCollisionEnable);
	}
	else
	{
		SetCollisionEnabled(ECollisionEnabled::NoCollision);
	}

	if (UGameplayFuncLib::GetDataManager() &&
		this->Type == ECharacterHitBoxType::Normal && UGameplayFuncLib::GetDataManager()->DebugConfig.ShowNormalHitBox ||
		this->Type == ECharacterHitBoxType::Guard && UGameplayFuncLib::GetDataManager()->DebugConfig.ShowGuardHitBox ||
		this->Type == ECharacterHitBoxType::JustDodge && UGameplayFuncLib::GetDataManager()->DebugConfig.ShowJustDodgeBox)
		this->SetHiddenInGame(IsActive()?false:true);
}
