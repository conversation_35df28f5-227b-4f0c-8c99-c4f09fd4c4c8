// Fill out your copyright notice in the Description page of Project Settings.


#include "AttackHitBox.h"

#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GameFramework/Manager/AwDataManager.h"

void UAttackHitBox::BeginPlay()
{
	Super::BeginPlay();
	DefaultCollisionEnable = GetCollisionEnabled();
	SetCollisionEnabled(DefaultActive?DefaultCollisionEnable:ECollisionEnabled::NoCollision);
	
	if (UGameplayFuncLib::GetDataManager() &&
		UGameplayFuncLib::GetDataManager()->DebugConfig.ShowAttackBox)
		this->SetHiddenInGame(DefaultActive?false:true);
}

void UAttackHitBox::SetActive(bool bNewActive, bool bReset)
{
	Super::SetActive(bNewActive, bReset);
	if (IsActive())
	{
		SetCollisionEnabled(DefaultCollisionEnable);
	}
	else
	{
		SetCollisionEnabled(ECollisionEnabled::NoCollision);
	}

	if (UGameplayFuncLib::GetDataManager() &&
		UGameplayFuncLib::GetDataManager()->DebugConfig.ShowAttackBox)
		this->SetHiddenInGame(IsActive()?false:true);
}
