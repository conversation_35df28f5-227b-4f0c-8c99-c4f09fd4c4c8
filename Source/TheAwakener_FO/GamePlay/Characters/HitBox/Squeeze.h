// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Components/BoxComponent.h"
#include "Components/CapsuleComponent.h"
#include "Components/SphereComponent.h"
#include "Squeeze.generated.h"

/**
 * 挤开类型
 */
UENUM(BlueprintType)
enum class ESqueezeType : uint8
{
	Character,	//AwCharacter，调用AwMoveComponent的AddSqueeze接口
	Character_CanNotBeSqueeze, // 角色但是不能被挤开
	Terrain,	//地形，不调用移动接口
	Train,		//板车、火车等在轨道上走的，调用...接口
	
	None
};


/**
 * 胶囊体形状的挤开盒
 */
UCLASS(ClassGroup="Squeeze", hidecategories=(Object,LOD,Lighting,TextureStreaming),
	meta=(DisplayName="SqueezeCapsule", BlueprintSpawnableComponent))
class THEAWAKENER_FO_API USqueezeCapsule : public UCapsuleComponent
{
	GENERATED_BODY()

	USqueezeCapsule()
	{
		ShapeColor = FColor(0, 255, 255, 255);
	}
};

/**
 * 球形的 挤开盒
 */
UCLASS(ClassGroup="Squeeze", hidecategories=(Object,LOD,Lighting,TextureStreaming),
	meta=(DisplayName="SqueezeSphere", BlueprintSpawnableComponent))
class THEAWAKENER_FO_API USqueezeSphere : public USphereComponent
{
	GENERATED_BODY()

	USqueezeSphere()
	{
		ShapeColor = FColor(0, 255, 255, 255);
	}
};

/**
 * 矩形的 挤开盒
 */
UCLASS(ClassGroup="Squeeze", hidecategories=(Object,LOD,Lighting,TextureStreaming),
	meta=(DisplayName="SqueezeBox", BlueprintSpawnableComponent))
class THEAWAKENER_FO_API USqueezeBox : public UBoxComponent
{
	GENERATED_BODY()

	USqueezeBox()
	{
		ShapeColor = FColor(0, 255, 255, 255);
	}
};


/**
 * 挤开组件
 */
UCLASS(ClassGroup="Squeeze", hidecategories=(Object,LOD,Lighting,TextureStreaming),
	meta=(DisplayName="SqueezeComp", BlueprintSpawnableComponent))
class THEAWAKENER_FO_API USqueezeComp : public USceneComponent
{
	GENERATED_BODY()
	
private:
	UPROPERTY()
	TArray<UPrimitiveComponent*> OverlappedComps;

public:
	// 挤开类型
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	ESqueezeType SqueezeType = ESqueezeType::Terrain;

	// 质量，类型为int，用来计算相互挤开的优先级。质量大的挤开质量小的
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int Weight = 3;
	
	// 挤开的碰撞
	UPROPERTY()
	UShapeComponent* ShapeCollision = nullptr;

	UPROPERTY()
	bool IsEnable = true;
	
	USqueezeComp();
	virtual void BeginPlay() override;
	virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;
	
	UFUNCTION(BlueprintCallable)
	void OnOverlapBegin(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor,
		UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult);
	
	UFUNCTION(BlueprintCallable)
	void OnOverlapEnd(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor,
		UPrimitiveComponent* OtherComp, int32 OtherBodyIndex);
	
	void DoSqueeze();
	void CloseSqueeze();
	void AddSqueezeForce(FVector Force, ESqueezeType BlockType, const UShapeComponent* BlockShapeComp) const;

	/**
	 * 获取不同挤开盒的最开半径长度
	 */
	UFUNCTION()
	float GetMaxRadius(const UShapeComponent* TargetShapeComp) const;

	UFUNCTION()
	float GetHalfHeight(const UShapeComponent* TargetShapeComp) const;
	
	UFUNCTION()
	static int GetSqueezeWeight(const UShapeComponent* TargetShapeComp);

	UFUNCTION()
	static ESqueezeType GetSqueezeType(const UShapeComponent* TargetShapeComp);

	UFUNCTION(BlueprintCallable)
	void SetSqueezeType(ESqueezeType Type);
	
	UFUNCTION()
	static USqueezeComp* GetSqueezeCompByActor(const AActor* Actor);
};
