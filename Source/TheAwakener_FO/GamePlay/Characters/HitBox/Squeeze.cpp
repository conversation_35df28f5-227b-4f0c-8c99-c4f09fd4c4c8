// Fill out your copyright notice in the Description page of Project Settings.


#include "Squeeze.h"

#include "Kismet/KismetSystemLibrary.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "TheAwakener_FO/GamePlay/Move/TrackMoveComponent.h"

USqueezeComp::USqueezeComp()
{
	PrimaryComponentTick.bCanEverTick = true;
}

void USqueezeComp::BeginPlay()
{
	Super::BeginPlay();
	
	for (UActorComponent* Component : GetOwner()->GetComponents())
	{
		if(IsValid(Cast<USqueezeCapsule>(Component)) ||
		   IsValid(Cast<USqueezeSphere>(Component)) ||
		   IsValid(Cast<USqueezeBox>(Component)))
		{
			ShapeCollision = Cast<UShapeComponent>(Component);
			break;
		}
	}
	
	if (IsValid(ShapeCollision))
	{
		ShapeCollision->OnComponentBeginOverlap.AddDynamic(this, &USqueezeComp::OnOverlapBegin);
		ShapeCollision->OnComponentEndOverlap.AddDynamic(this, &USqueezeComp::OnOverlapEnd);
	}
}

void USqueezeComp::TickComponent(float DeltaTime, ELevelTick TickType,
	FActorComponentTickFunction* ThisTickFunction)
{
	Super::TickComponent(DeltaTime, TickType, ThisTickFunction);
	if (IsEnable)
		DoSqueeze();
}

void USqueezeComp::OnOverlapBegin(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor,
                                  UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult)
{
	if (IsEnable)
		this->OverlappedComps.Add(OtherComp);
}

void USqueezeComp::OnOverlapEnd(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor,
	UPrimitiveComponent* OtherComp, int32 OtherBodyIndex)
{
	if (this->OverlappedComps.Contains(OtherComp))
		this->OverlappedComps.Remove(OtherComp);
}

void USqueezeComp::DoSqueeze()
{
	switch (SqueezeType)
	{
	case ESqueezeType::Character:
	case ESqueezeType::Train:
		{
			for (const UPrimitiveComponent* Comp : OverlappedComps)
			{
				if (Comp->GetOwner() == GetOwner())
					continue;

				const UShapeComponent* ShapeComp = Cast<UShapeComponent>(Comp);
				
				int BlockerWeight = 0;
				ESqueezeType BlockType = ESqueezeType::None;
				const USqueezeComp* BlockSqueezerComp = nullptr;
				const AActor* Blocker = Cast<AActor>(ShapeComp->GetOwner());
				if (Blocker)
				{
					BlockSqueezerComp = GetSqueezeCompByActor(Blocker);
					if (BlockSqueezerComp)
					{
						BlockType = BlockSqueezerComp->SqueezeType;
						BlockerWeight = BlockSqueezerComp->Weight;
					}
				}
				
				if (BlockType == ESqueezeType::Terrain || BlockerWeight >= Weight)
				{
					const float ForceRate = BlockerWeight*1.0 / (BlockerWeight + Weight);
					
					FVector End = this->ShapeCollision->GetComponentLocation(); //射线射向射向自己
					FVector Start;
					FVector Dir = End - ShapeComp->GetComponentLocation();

					if (FVector2D(Dir).SizeSquared() < 1)
					{
						const FVector2D V2 = UMathFuncLib::RandomV2OnCircle(1);
						Dir.X = V2.X;
						Dir.Y = V2.Y;
					}
					
					// if (Cast<AAwCharacter>(this->GetOwner())->IsPlayerCharacter())
					// 	UKismetSystemLibrary::PrintString(this->GetOwner(), " ---==> " + Dir.ToString());
					
					if((GetHalfHeight(ShapeComp) + GetHalfHeight(this->ShapeCollision) -
					  FMath::Abs(this->ShapeCollision->GetComponentLocation().Z - ShapeComp->GetComponentLocation().Z)
					  ) / GetHalfHeight(ShapeComp) <= 0.5f && SqueezeType == ESqueezeType::Terrain)
					{
						// 纵向挤开
						Start = End + FVector(0, 0, Dir.Z).GetSafeNormal() *
							(GetMaxRadius(ShapeCollision) + GetMaxRadius(ShapeComp));
					}
					else
					{
						// 横向挤开
						Start = End + FVector(Dir.X, Dir.Y, 0).GetSafeNormal() *
							(GetMaxRadius(ShapeCollision) + GetMaxRadius(ShapeComp));
					}

					TArray<FHitResult> HitResult = TArray<FHitResult>();
					bool bIsHit = false;
					
					const USqueezeSphere* SphereComp = Cast<USqueezeSphere>(this->ShapeCollision);
					const USqueezeBox* BoxComp = Cast<USqueezeBox>(this->ShapeCollision);
					const USqueezeCapsule* CapsuleComp = Cast<USqueezeCapsule>(this->ShapeCollision);
					
					TArray<TEnumAsByte<EObjectTypeQuery> > ObjectTypes;
					ObjectTypes.Add(ObjectTypeQuery18);
					
					if (IsValid(SphereComp))
					{
						bIsHit = UKismetSystemLibrary::SphereTraceMultiForObjects(this, Start, End,
							SphereComp->GetScaledSphereRadius(),
							ObjectTypes, false, TArray<AActor*>(), EDrawDebugTrace::None,
							HitResult, true);
					}
					else if (IsValid(BoxComp))
					{
						bIsHit = UKismetSystemLibrary::BoxTraceMultiForObjects(this, Start, End,
							BoxComp->GetScaledBoxExtent(), BoxComp->GetComponentRotation(),
							ObjectTypes, false, TArray<AActor*>(), EDrawDebugTrace::None,
							HitResult, true);
					}
					else if (IsValid(CapsuleComp))
					{
						bIsHit = UKismetSystemLibrary::CapsuleTraceMultiForObjects(this, Start, End,
							CapsuleComp->GetScaledCapsuleRadius(), CapsuleComp->GetScaledCapsuleHalfHeight(),
							ObjectTypes, false, TArray<AActor*>(), EDrawDebugTrace::None,
							HitResult, true);
						// bIsHit = UKismetSystemLibrary::CapsuleTraceMulti(
						// 	this, Start, End,
						// 	CapsuleComp->GetScaledCapsuleRadius(), CapsuleComp->GetScaledCapsuleHalfHeight(),
						// 	ETraceTypeQuery::TraceTypeQuery3, false, TArray<AActor*>(), EDrawDebugTrace::None,
						// 	HitResult, true);
					}

					if (bIsHit)
					{
						// if (Cast<AAwCharacter>(this->GetOwner())->IsPlayerCharacter())
						// {
						// 	UKismetSystemLibrary::PrintString(this->GetOwner(), " --- ");
						// 	UKismetSystemLibrary::PrintString(this->GetOwner(), " ---> " +
						// 		ShapeComp->GetName() + ":(" + ShapeComp->GetOwner()->GetName() +
						// 		") " + FString::FromInt(ShapeComp->GetUniqueID()));
						// 	for (FHitResult Result : HitResult)
						// 	{
						// 		UKismetSystemLibrary::PrintString(this->GetOwner(), " ---" +
						// 			Result.Component->GetName() + ":(" + Result.Component->GetOwner()->GetName() +
						// 			") " + FString::FromInt(Result.Component->GetUniqueID()));
						// 	}
						// }
						for (FHitResult Result : HitResult)
						{
							if(Result.Component == ShapeComp)
							{
								// if (Cast<AAwCharacter>(this->GetOwner())->IsPlayerCharacter())
								// 	UKismetSystemLibrary::PrintString(this->GetOwner(), " ---==> " + Dir.ToString());
								
								AddSqueezeForce((Result.Location - End)*ForceRate, BlockType, ShapeComp);
								if (BlockSqueezerComp && BlockerWeight > Weight)
									BlockSqueezerComp->AddSqueezeForce((Result.Location - End)*(1 - ForceRate)*-1, SqueezeType, ShapeComp);
								break;
							}
						}
					}
				}
			}
		}
		break;
	case ESqueezeType::Terrain:		//地形不会被挤开
	default:
		break;
	}
}

void USqueezeComp::CloseSqueeze()
{
	IsEnable = false;
	if (ShapeCollision)
		this->ShapeCollision->DestroyComponent();
}

void USqueezeComp::AddSqueezeForce(FVector Force, ESqueezeType BlockType, const UShapeComponent* BlockShapeComp) const
{
	switch (SqueezeType)
	{
	case ESqueezeType::Character:
		{
			// if (BlockType == ESqueezeType::Character)
			// {
			// 	constexpr float FixForce = 3;
			// 	if(FMath::Abs(Force.X) > FixForce) Force.X = FixForce * (Force.X >= 0 ? 1 : -1);
			// 	if(FMath::Abs(Force.Y) > FixForce) Force.Y = FixForce * (Force.Y >= 0 ? 1 : -1);
			// }
			
			const AAwCharacter* Character = Cast<AAwCharacter>(GetOwner());
			if (IsValid(Character))
			{
				Character->AddSqueeze(Force);
				// if (!Character->IsPlayerCharacter())
				// {
				// 	FString Str = Character->GetName() + "  -  Force: " + Force.ToString();
				// 	UKismetSystemLibrary::PrintString(Character, *Str);
				// 	if (FMath::Abs(Force.X) > 15 || FMath::Abs(Force.Y) > 15)
				// 	{
				// 		UKismetSystemLibrary::PrintString(Character, "!!");
				// 	}
				// }
			}
		}
		break;
	case ESqueezeType::Train:
		{
			UTrackMoveComponent* TrackMoveComp = Cast<UTrackMoveComponent>(GetOwner()->GetComponentByClass(UTrackMoveComponent::StaticClass()));
			if (IsValid(TrackMoveComp))
			{
				if (BlockType == ESqueezeType::Terrain)
					TrackMoveComp->BlockWall(BlockShapeComp);
				else
					TrackMoveComp->Push(Force);
			}
		}
		break;
		
	case ESqueezeType::Terrain:						//不会被挤开
	case ESqueezeType::Character_CanNotBeSqueeze:	//不会被挤开
	case ESqueezeType::None:						//不会被挤开
	default:										//不会被挤开
		break;
	}
}

float USqueezeComp::GetMaxRadius(const UShapeComponent* TargetShapeComp) const
{
	const USqueezeSphere* SphereComp = Cast<USqueezeSphere>(TargetShapeComp);
	if (IsValid(SphereComp))
		return SphereComp->GetScaledSphereRadius();

	const USqueezeCapsule* CapsuleComp = Cast<USqueezeCapsule>(TargetShapeComp);
	if (IsValid(CapsuleComp))
		return CapsuleComp->GetScaledCapsuleHalfHeight();

	const USqueezeBox* BoxComp = Cast<USqueezeBox>(TargetShapeComp);
	if (IsValid(BoxComp))
	{
		float MaxRadius = 0;
		const FVector BoxExtent = BoxComp->GetScaledBoxExtent();
		if (BoxExtent.X > MaxRadius) MaxRadius = BoxExtent.X;
		if (BoxExtent.Y > MaxRadius) MaxRadius = BoxExtent.Y;
		if (BoxExtent.X > MaxRadius) MaxRadius = BoxExtent.Z;
		return MaxRadius * 1.415f; //*根号2
	}
	
	return 0;
}

float USqueezeComp::GetHalfHeight(const UShapeComponent* TargetShapeComp) const
{
	const USqueezeSphere* SphereComp = Cast<USqueezeSphere>(TargetShapeComp);
	if (IsValid(SphereComp))
		return SphereComp->GetScaledSphereRadius();

	const USqueezeCapsule* CapsuleComp = Cast<USqueezeCapsule>(TargetShapeComp);
	if (IsValid(CapsuleComp))
		return CapsuleComp->GetScaledCapsuleHalfHeight();

	const USqueezeBox* BoxComp = Cast<USqueezeBox>(TargetShapeComp);
	if (IsValid(BoxComp))
		return BoxComp->GetScaledBoxExtent().Y;
	
	return 0;
}

int USqueezeComp::GetSqueezeWeight(const UShapeComponent* TargetShapeComp)
{
	TArray<USqueezeComp*> OutComponents;
	TargetShapeComp->GetOwner()->GetComponents<USqueezeComp>(OutComponents);

	for (const USqueezeComp* OutComponent : OutComponents)
	{
		if(OutComponent->ShapeCollision == TargetShapeComp)
			return OutComponent->Weight;
	}
	
	return 0;
}

ESqueezeType USqueezeComp::GetSqueezeType(const UShapeComponent* TargetShapeComp)
{
	TArray<USqueezeComp*> OutComponents;
	TargetShapeComp->GetOwner()->GetComponents<USqueezeComp>(OutComponents);
	
	for (const USqueezeComp* OutComponent : OutComponents)
	{
		if(OutComponent->ShapeCollision == TargetShapeComp)
			return OutComponent->SqueezeType;
	}
	
	return ESqueezeType::Terrain;
}

void USqueezeComp::SetSqueezeType(ESqueezeType Type)
{
	if (this->SqueezeType != Type)
		this->SqueezeType = Type;
}


USqueezeComp* USqueezeComp::GetSqueezeCompByActor(const AActor* Actor)
{
	UActorComponent* Comp = Actor->GetComponentByClass(USqueezeComp::StaticClass());
	if (IsValid(Comp)) 
		return Cast<USqueezeComp>(Comp);
	
	return nullptr;
}
