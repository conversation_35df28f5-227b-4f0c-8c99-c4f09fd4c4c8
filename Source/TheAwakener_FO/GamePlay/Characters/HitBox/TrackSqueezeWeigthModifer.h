// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Squeeze.h"
#include "TheAwakener_FO/GamePlay/Move/TrackMoveComponent.h"
#include "TrackSqueezeWeigthModifer.generated.h"

/**
 * 
 */
UCLASS(ClassGroup="Squeeze", hidecategories=(Object,LOD,Lighting,TextureStreaming),
	meta=(DisplayName="Track Squeeze Weigth Modifer", BlueprintSpawnableComponent))
class THEAWAKENER_FO_API UTrackSqueezeWeigthModifer : public USceneComponent
{
	GENERATED_BODY()

	
private:
	UPROPERTY()
	int DefaultWeight = 0;

	UPROPERTY()
	float LastMoveForce = 0;
	
public:
	UTrackSqueezeWeigthModifer();
	virtual void BeginPlay() override;
	virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;
	
	UPROPERTY(BlueprintReadWrite)
	USqueezeComp* SqueezeComp;

	UPROPERTY(BlueprintReadWrite)
	UTrackMoveComponent* TrackMoveComponent;
	
	UFUNCTION(BlueprintCallable)
	void SetWeight(int NewWeight);

	UFUNCTION(BlueprintCallable)
	void RestoreWeight();
	
	UFUNCTION(BlueprintCallable)
	int GetWeigthByMoveForce(bool IsBlockWall);
};
