// Fill out your copyright notice in the Description page of Project Settings.


#include "InterruptComponent.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "TheAwakener_FO/GameFramework/AwPlayerController.h"
#include "TheAwakener_FO/FunctionLibrary/CallFuncLib.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"

UInterruptComponent::UInterruptComponent()
{
	PrimaryComponentTick.bCanEverTick = true;
	this->SetCollisionResponseToAllChannels(ECollisionResponse::ECR_Ignore);
	this->SetCollisionResponseToChannel(ECollisionChannel::ECC_Pawn , ECollisionResponse::ECR_Overlap);
	this->OnComponentBeginOverlap.AddDynamic(this,&UInterruptComponent::OnOverlapBegin);
	this->OnComponentEndOverlap.AddDynamic(this,&UInterruptComponent::OnOverlapEnd);
	CapsuleRadius += RadiusOffset;
	CapsuleHalfHeight += HalfHeightOffset;
}

void UInterruptComponent::BeginPlay()
{
	Super::BeginPlay();
	OwnerCharacter = Cast<AAwCharacter>(GetOwner());

}

void UInterruptComponent::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
	Super::TickComponent(DeltaTime,TickType,ThisTickFunction);
	//同步角色的椭圆形碰撞检测形状 如果有设置额外偏移参数则加上额外参数
	if (OwnerCharacter&&bSyncOwnerCapsule)
	{
		float PawnRadius = OwnerCharacter->GetCapsuleComponent()->GetScaledCapsuleRadius();
		float PawnHalfHeight = OwnerCharacter->GetCapsuleComponent()->GetScaledCapsuleHalfHeight();
		SetCapsuleRadius(PawnRadius+RadiusOffset);
		SetCapsuleHalfHeight(PawnHalfHeight+HalfHeightOffset);
	}
	//检查当前记忆的打扰历史
	if (MaxMemoryTime>0)
	{
		TickCheckInterruptionHistory(DeltaTime);
	}

}

void UInterruptComponent::OnOverlapBegin(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult)
{
	AAwCharacter* Source = Cast<AAwCharacter>(OtherActor);
	if (!Source)
	{
		return;
	}

	if (LastInterruptionInfo.InterruptSource == Source)
	{
		return;
	}

	if (InterruptableType != EInterruptableType::UnInterruptable)
	{	
		bool CanBeInterrupted = Source->IsPlayerCharacter() ? true : InterruptableType == EInterruptableType::CanInterruptedFromAnyone ;
		if (CanBeInterrupted)
		{
			IsInterruptedSourceInterested(Source, SweepResult);

			if (LastInterruptionInfo.CurInterestedLevel> InterruptedLevel)
			{
				InterruptedLevel = LastInterruptionInfo.OrgInterestedLevel;
				return;
			}
			LastInterruptionInfo.OrgInterestedLevel = InterruptedLevel;
			LastInterruptionInfo.CurInterestedLevel = InterruptedLevel;
			LastInterruptionInfo.InterruptSource = Source;
			LastInterruptionInfo.lifetime = 0.0f;
		}
	}
}

void UInterruptComponent::OnOverlapEnd(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex)
{

	AAwCharacter* Source = Cast<AAwCharacter>(OtherActor);
	if (!Source)
	{
		return;
	}
	if (InterruptableType != EInterruptableType::UnInterruptable)
	{
		if (LastInterruptionInfo.InterruptSource == Source)
		{
			LastInterruptionInfo.ReSet();
			InterruptedLevel = EInterruptedLevel::NoInterruption;
		}
	}
	else
	{
		LastInterruptionInfo.ReSet();
		InterruptedLevel = EInterruptedLevel::NoInterruption;
	}
}

void UInterruptComponent::IsInterruptedSourceInterested(AAwCharacter* OtherCharacter, const FHitResult& SweepResult)
{
	if (!OtherCharacter)
	{
		return;
	}
	//如果有兴趣级别判断脚本 则按脚本逻辑执行

	if (!InterestLevelScript.ClassPath.IsEmpty()&& !InterestLevelScript.FunctionName.IsEmpty())
	{
		UFunction* Func = UCallFuncLib::GetUFunction(InterestLevelScript.ClassPath, InterestLevelScript.FunctionName);
		if (Func)
		{
			struct
			{
				AAwCharacter* Cha;
				FHitResult HitInfo;
				TArray<FString> DesignerParam;
				EInterruptedLevel Result;
			} FuncParam;
			FuncParam.Cha = OtherCharacter;
			FuncParam.HitInfo = SweepResult;
			FuncParam.DesignerParam = InterestLevelScript.Params;
			this->ProcessEvent(Func, &FuncParam);
			InterruptedLevel = FuncParam.Result;
			return;
		}
	}

	//默认逻辑 根据目标的速度判断兴趣等级
	int TargetSpeedLV =  OtherCharacter->GetMoveSpeedLevel();

	if (TargetSpeedLV<=1)
	{
		InterruptedLevel = EInterruptedLevel::LowInterested;
	}
	else if (TargetSpeedLV <= 2)
	{
		InterruptedLevel = EInterruptedLevel::MidInterested;
	}
	else
	{
		InterruptedLevel = EInterruptedLevel::HighInterested;
	}	
}

void UInterruptComponent::TickCheckInterruptionHistory(float DeltaTime)
{
	LastInterruptionInfo.lifetime += DeltaTime;
	if (LastInterruptionInfo.lifetime>=MaxMemoryTime)
	{
		LastInterruptionInfo.ReSet();
		InterruptedLevel = EInterruptedLevel::NoInterruption;
	}
}


