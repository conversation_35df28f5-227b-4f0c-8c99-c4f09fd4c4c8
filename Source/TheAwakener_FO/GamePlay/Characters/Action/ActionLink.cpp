// Fill out your copyright notice in the Description page of Project Settings.


#include "ActionLink.h"

FActionLink FActionLink::FromJson(TSharedPtr<FJsonObject> JsonObj)
{
	FActionLink Res = FActionLink();
	Res.Id = UDataFuncLib::AwGetStringField(JsonObj, "Id");
	Res.MainActionId = UDataFuncLib::AwGetStringField(JsonObj, "MainActionId");
	Res.LinkTag = FCancelTag();
	Res.SetToActionId = UDataFuncLib::AwGetStringField(JsonObj, "SetToActionId");
	if (JsonObj-><PERSON><PERSON>ield("SetTag")) Res.Method_SetTag =  JsonObj->GetBoolField("SetTag");
	if (JsonObj-><PERSON><PERSON>ield("LinkTag"))
	{
		Res.LinkTag.Tag = JsonObj->GetObjectField("LinkTag")->GetStringField("Tag");
		Res.LinkTag.FromSec = JsonObj->GetObject<PERSON>ield("LinkTag")->GetNumberField("FromSec");
	}
	Res.LinkActionId = UDataFuncLib::AwGetStringArrayField(JsonObj, "LinkActionId");
	Res.LinkEffectKeys = UDataFuncLib::AwGetStringArrayField(JsonObj, "LinkEffectKeys");
	return Res;
}


FActionSelection FActionSelection::FromJson(TSharedPtr<FJsonObject> JsonObj)
{
	FActionSelection Res = FActionSelection();
	Res.Id = UDataFuncLib::AwGetStringField(JsonObj, "Id");
	Res.ClassId = UDataFuncLib::AwGetStringField(JsonObj, "ClassId");
	Res.CmdAction = UDataFuncLib::AwGetStringField(JsonObj, "CmdAction");
	Res.CandidateActionId = UDataFuncLib::AwGetStringArrayField(JsonObj, "ActionId");
	return Res;
}

FActionSelectionElementalTriggerUIInfo FActionSelectionElementalTriggerUIInfo::FromJson(TSharedPtr<FJsonObject> JsonObj)
{
	FActionSelectionElementalTriggerUIInfo Res = FActionSelectionElementalTriggerUIInfo();
	Res.SlotId = UDataFuncLib::AwGetStringField(JsonObj, "SlotId");
	Res.Icon = UDataFuncLib::AwGetStringField(JsonObj, "Icon");
	Res.Description = UDataFuncLib::AwGetStringField(JsonObj, "Desc");
	return Res;
}

FActionSelectionMainActionUIInfo FActionSelectionMainActionUIInfo::FromJson(TSharedPtr<FJsonObject> JsonObj)
{
	FActionSelectionMainActionUIInfo Res = FActionSelectionMainActionUIInfo();
	Res.Description = UDataFuncLib::AwGetStringField(JsonObj, "Desc");
	Res.Icon = UDataFuncLib::AwGetStringField(JsonObj, "Icon");
	Res.ActionId = UDataFuncLib::AwGetStringField(JsonObj, "ActionId");
	Res.Name = UDataFuncLib::AwGetStringField(JsonObj, "Name");
	if (JsonObj->HasField("Slots"))
	{
		for (TSharedPtr<FJsonValue, ESPMode::ThreadSafe> SInfo : JsonObj->GetArrayField("Slots"))
		{
			Res.Slots.Add(FActionSelectionElementalTriggerUIInfo::FromJson(SInfo->AsObject()));
		} 
	}
	return Res;
}

FActionSelectionUIInfo FActionSelectionUIInfo::FromJson(TSharedPtr<FJsonObject> JsonObj)
{
	FActionSelectionUIInfo Res = FActionSelectionUIInfo();
	Res.Id = UDataFuncLib::AwGetStringField(JsonObj, "Id");
	Res.InAir = UDataFuncLib::AwGetBoolField(JsonObj, "InAir");
	Res.CmdIcon = UDataFuncLib::AwGetStringField(JsonObj, "CmdIcon");
	Res.Name = UDataFuncLib::AwGetStringField(JsonObj, "Name");
	if (JsonObj->HasField("Actions"))
	{
		for (TSharedPtr<FJsonValue, ESPMode::ThreadSafe> Act : JsonObj->GetArrayField("Actions"))
		{
			Res.Actions.Add(FActionSelectionMainActionUIInfo::FromJson(Act->AsObject()));
		} 
	}
	return Res;
}

FActionLinkCandidateActionUIInfo FActionLinkCandidateActionUIInfo::FromJson(TSharedPtr<FJsonObject> JsonObj)
{
	FActionLinkCandidateActionUIInfo Res = FActionLinkCandidateActionUIInfo();
	Res.ActionName = UDataFuncLib::AwGetStringField(JsonObj, "ActionName");
	Res.ActionDesc = UDataFuncLib::AwGetStringField(JsonObj, "ActionDesc");
	Res.ActionIcon = UDataFuncLib::AwGetStringField(JsonObj, "ActionIcon");
	Res.ActionId = UDataFuncLib::AwGetStringField(JsonObj, "ActionId");
	Res.TriggerPoint = UDataFuncLib::AwGetStringField(JsonObj, "Trigger");
	return Res;
}

FActionLinkUIInfo FActionLinkUIInfo::FromJson(TSharedPtr<FJsonObject> JsonObj)
{
	FActionLinkUIInfo Res = FActionLinkUIInfo();
	Res.Id = UDataFuncLib::AwGetStringField(JsonObj, "Id");
	Res.MainActionDesc = UDataFuncLib::AwGetStringField(JsonObj, "MainActionDesc");
	Res.MainActionIcon = UDataFuncLib::AwGetStringField(JsonObj, "MainActionIcon");
	Res.MainActionName = UDataFuncLib::AwGetStringField(JsonObj, "MainActionName");
	if (JsonObj->HasField("Slots"))
	{
		for (TSharedPtr<FJsonValue, ESPMode::ThreadSafe> SInfo : JsonObj->GetArrayField("Slots"))
		{
			Res.Slots.Add(FActionSelectionElementalTriggerUIInfo::FromJson(SInfo->AsObject()));
		} 
	}
	if (JsonObj->HasField("CandidateActions"))
	{
		for (TSharedPtr<FJsonValue, ESPMode::ThreadSafe> Act : JsonObj->GetArrayField("CandidateActions"))
		{
			Res.CandidateActions.Add(FActionLinkCandidateActionUIInfo::FromJson(Act->AsObject()));
		} 
	}
	return Res;
}