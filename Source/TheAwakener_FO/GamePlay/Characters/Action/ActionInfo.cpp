// Fill out your copyright notice in the Description page of Project Settings.


#include "ActionInfo.h"

float FActionInfo::GetFromSecByTagId(FString TagId)
{
	for (FCancelTag Tag : Tags)
	{
		if (Tag.Tag == TagId)
		{
			return Tag.FromSec;
		}
	}
	return 0;
}

bool FActionInfo::CanBeCancelledBy(FActionInfo* OtherAction)
{
	for (const TTuple<int, TArray<FString>> CancelledTag : this->CanBeCancelledTag)
	{
		for (const FString Value : CancelledTag.Value)
		{
			for (const FCancelTag Tag : OtherAction->Tags)
			{
				if (Tag.Tag == Value)
				{
					return true;
				}
			} 
		} 
	}
	return false;
}

bool FActionInfo::operator ==(const FActionInfo& Other) const
{
	return this->OriginId == Other.OriginId && this->Anim == Other.Anim;
}

FActionInfoModifyApplication FActionInfoModifyApplication::ModifyIdTo(FString ToId)
{
	FActionInfoModifyApplication Res = FActionInfoModifyApplication();
	Res.ModifyId = true;
	Res.IdModifyTo = ToId;
	return Res;
}

FActionInfoModifyApplication FActionInfoModifyApplication::ModifyTagTo(FCancelTag ToTag, bool ToAdd)
{
	FActionInfoModifyApplication Res = FActionInfoModifyApplication();
	Res.ModifyTag = true;
	Res.CancelTagModifyTo = ToTag;
	Res.AddThisTag = ToAdd;
	return Res;
}

FActionInfoModifyApplication FActionInfoModifyApplication::ModifyActionCmd(TArray<FString> AddCmd, TArray<FString> RemoveCmd)
{
	FActionInfoModifyApplication Res = FActionInfoModifyApplication();
	Res.ModifyCmd = true;
	Res.ToAddCmd = AddCmd;
	Res.ToRemoveCmd = RemoveCmd;
	return Res;
}