// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "CancelTagInfo.generated.h"

/**
 * Cancel Tag的类型
 */
UENUM()
enum ECancelTagType
{
	//montage动画的
	Montage,
	//状态机的
	State,
	//临时添加的Montage动画的CancelTag
	Temporary
};

/**
 *  Cancel Tag的信息，用于记录当前角色被激活的CancelTag
 */
USTRUCT(BlueprintType)
struct FCancelTagInfo
{
	GENERATED_BODY()
public:
	//这是一个几号的点
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int CancelPointIndex;

	//这里面具体有哪些Tag
	UPROPERTY()
	TArray<FString> Tags;

	//有效时间
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	float Duration = 0;

	FCancelTagInfo(): CancelPointIndex(-1), Tags(TArray<FString>()){};
	FCancelTagInfo(int PointIndex, TArray<FString> TagsInPoint, float LifeTime = 0):
		 CancelPointIndex(PointIndex), Tags(TagsInPoint), Duration(LifeTime){};

	bool operator ==(const FCancelTagInfo& Other) const
	{
		return Other.CancelPointIndex == this->CancelPointIndex;
	}
};
