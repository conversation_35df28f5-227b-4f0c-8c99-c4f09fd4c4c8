// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "ActionInfo.h"
#include "ActionLink.generated.h"


/**
 * 技能安排数据，所谓技能连接，就是每个角色都可以自定义使用的技能
 * 比如大剑战士，可以自定义格挡技能以及制定格挡之后自动触发的那个动作
 * 上述说的格挡技能，就是一个主技能，在确定主技能之后，就能配置其对应节点的后续动作
 * 选择对应的动作还可能激活元素触发点和一些动作原本没有被激活的特性
 * 比如某个动作原本配了一段格挡框，但是这个格挡框效果激活是需要配在大剑格挡之后，否则就不激活
 */
USTRUCT(BlueprintType)
struct FActionLink
{
	GENERATED_BODY()
public:
	//给个id把，总会有用的，这是这个技能安排数据的id，其实是为了和UI挂钩的
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString Id;

	//启动动作的Id，启动动作就是比如战士格挡这样，是这个链条的启动器
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString MainActionId;

	/**
	 * 改变方式是否是改变CancelTag （True就是改变CancelTag）
	 * 当一个动作是自动连接后续的时候这里应该是false，使用改变id策略，比如大剑格挡
	 * 当一个动作是开启Cancel点式链接，他就应该是true的，比如双刀的JustHit
	 * 这里有一个缺陷：就是一个Action不能同时成为一个改变tag和改变id的后续动作
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool Method_SetTag = true;

	/**
	 * 如果SetTag==false，那么就得把对应的Action设置到一个id（这是一个有缺陷的临时做法）
	 *	就是设置到这个值了，原本id为这个值的动作将会启用现在被设置进去的动作的原本id（id交换)
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString SetToActionId;

	/**
	 * 用来链接后续动作的那个Be Cancelled Tag，当然首先你得保证这个动作真有这个Tag
	 * 当改变后续技能的时候，原来的后续动作的CancelTag中（Tag值相等的）将被删除掉这个值
	 * 新的后续动作的CancelTag会被加入这个值
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FCancelTag LinkTag;

	/**
	 * 所有可选后续动作列表，毕竟原本这些动作中是没有这个CancelTag的，得记录下来
	 * 这里必须都是精准的动作OriginId。
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FString> LinkActionId;

	/**
	 * 在AwCharacter中开启的一些Key，当这些Key激活时，Montage里面会有特殊处理（比如激活某些元素的爆发点）
	 * 当然光这个地方有这个Key是不行的，要选中的动作也有这个Key才行，并且动作中有对应的Notify，才能发动
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FString> LinkEffectKeys;

	static FActionLink FromJson(TSharedPtr<FJsonObject> JsonObj);

	bool operator ==(const FActionLink& Other) const
	{
		return Other.Id == this->Id && Other.MainActionId == this->MainActionId;
	}
};

/**
 * 一个角色会有多个这个，也就是可自定义链接
 */
USTRUCT(BlueprintType)
struct FActionSelection
{
	GENERATED_BODY()
public:
	//给个id把，总会有用的，这是这个技能安排数据的id
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString Id;

	//属于哪个职业的
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString ClassId = "Warrior";
	
	/**
	 * 要将这个动作设置到Action多少
	 * 注意：这不会改变Cmd中其他的Action值，而且改了能不能发动还得看Cancel关系
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString CmdAction = "Action0";
	
	/**
	 *其实只需要一个ActionLink数组就足够了，数组内的互相冲突
	 *也就是说数组内的MainAction只能选一个
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FString> CandidateActionId;

	static FActionSelection FromJson(TSharedPtr<FJsonObject> JsonObj);

	bool operator ==(const FActionSelection& Other)const
	{
		return Other.Id == this->Id && Other.ClassId == this->ClassId;
	}
};

/**
 * ActionSelection中的元素激活槽数据，当然现在还是忽悠人的
 */
USTRUCT(BlueprintType)
struct FActionSelectionElementalTriggerUIInfo
{
	GENERATED_BODY()
public:

	//槽位id
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString SlotId;

	//槽位的图标
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString Icon;

	//槽位的激活效果描述
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString Description;

	static FActionSelectionElementalTriggerUIInfo FromJson(TSharedPtr<FJsonObject> JsonObj);
};

/**
 * ActionSelection中的MainAction的数据
 */
USTRUCT(BlueprintType)
struct FActionSelectionMainActionUIInfo
{
	GENERATED_BODY()

public:
	//Action的Id
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString ActionId;

	//图标
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString Icon;

	//描述文字
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString Description;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString Name;
	
	//元素槽
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FActionSelectionElementalTriggerUIInfo> Slots;

	static FActionSelectionMainActionUIInfo FromJson(TSharedPtr<FJsonObject> JsonObj);
};

/**
 * ActionSelection需要的UI数据
 */
USTRUCT(BlueprintType)
struct FActionSelectionUIInfo
{
	GENERATED_BODY()
public:
	//挂向ActionSelection的Id
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString Id;

	//是否是一个空中动作，显然false就代表是地上的
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	bool InAir = false;

	//按键Icon图片位置
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString CmdIcon = "";

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString Name = "";
	
	//可替换动作的信息
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FActionSelectionMainActionUIInfo> Actions;

	static FActionSelectionUIInfo FromJson(TSharedPtr<FJsonObject> JsonObj);
};

/**
 * 子技能信息
 */
USTRUCT(BlueprintType)
struct FActionLinkCandidateActionUIInfo
{
	GENERATED_BODY()
public:
	//子技能id
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString ActionId;

	//子技能名称
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString ActionName;
	
	//子技能描述
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString ActionDesc;

	//子技能图标
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString ActionIcon;

	//子技能发动时间点
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString TriggerPoint;

	static FActionLinkCandidateActionUIInfo FromJson(TSharedPtr<FJsonObject> JsonObj);
};

/**
 * 某个技能选择子技能
 */
USTRUCT(BlueprintType)
struct FActionLinkUIInfo
{
	GENERATED_BODY()
public:
	//挂向ActionLink的id
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString Id;

	//主技能的icon
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString MainActionIcon;

	//主技能的名字
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString MainActionName;

	//主技能描述
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString MainActionDesc;

	//槽位信息
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FActionSelectionElementalTriggerUIInfo> Slots;

	//子技能信息
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FActionLinkCandidateActionUIInfo> CandidateActions;

	static FActionLinkUIInfo FromJson(TSharedPtr<FJsonObject> JsonObj);
};
