// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/FunctionLibrary/CallFuncLib.h"
#include "MontageAnimPicker.generated.h"

/**
 * 检测当前角色状态，调整播放动画的数据
 */
USTRUCT(BlueprintType)
struct FMontageAnimPicker
{
	GENERATED_BODY()
public:
	//检查用的Function，每帧都会调，如果这个是空，那么就是不做检查的
	//这些函数都在DesignerScript/MontageAnimPickFunc下
	//(AAwCharacter*, FActionParam)=>int，输入的是角色和当前的参数，返回的是AnimPath的index
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	UFunction* TickChecker = nullptr;

	//对应每个蒙太奇的文件路径，从ArtResource这层开始的，必须至少有1个，如果没有Func，那么执行的就是这个
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FString> AnimPath;

	static FMontageAnimPicker Create(FString FuncKey, TArray<FString>Paths)
	{
		FMontageAnimPicker MontageAnimPicker = FMontageAnimPicker();
		MontageAnimPicker.TickChecker = UCallFuncLib::StringToUFuncion(FuncKey);
		MontageAnimPicker.AnimPath = Paths;
		return MontageAnimPicker;
	}

	static FMontageAnimPicker FromJson(TSharedPtr<FJsonObject> JsonObj)
	{
		FMontageAnimPicker Model;
		Model.TickChecker = UCallFuncLib::StringToUFuncion(UDataFuncLib::AwGetStringField(JsonObj, "StateFunc"));
		TArray<TSharedPtr<FJsonValue, ESPMode::ThreadSafe>>Obj = UDataFuncLib::AwGetArrayField(JsonObj, "Anim");
		for (int i = 0; i < Obj.Num(); i++)
			Model.AnimPath.Add(Obj[i]->AsString());
		return Model;
	}
};
