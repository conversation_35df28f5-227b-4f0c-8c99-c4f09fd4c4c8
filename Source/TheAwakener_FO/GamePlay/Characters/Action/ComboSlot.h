// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "ComboSlot.generated.h"

/**
 * 
 */
USTRUCT(BlueprintType)
struct FComboSlot
{
	GENERATED_BODY()
public:
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString ActionId;

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString Cmd;

	TArray<FComboSlot*> NextSlots;

	void AddNext(FString Action, FString Command)
	{
		FComboSlot CS = FComboSlot();
		CS.ActionId = Action;
		CS.Cmd = Command;
		this->NextSlots.Add(&CS);
	}

	void AddNext(FComboSlot* Slot)
	{
		this->NextSlots.Add(Slot);
	}
};
