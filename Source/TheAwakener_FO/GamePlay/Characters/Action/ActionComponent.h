// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "ActionInfo.h"
#include "ActionLink.h"
#include "ActionPlanInfo.h"
#include "ArmState.h"
#include "CancelTagInfo.h"
#include "CharacterActionState.h"
#include "Animation/AnimMontage.h"
#include "TheAwakener_FO/GamePlay/Characters/Creation/ClassModel.h"
#include "TheAwakener_FO/GamePlay/Characters/ControlState/ControlState.h"
#include "TheAwakener_FO/GamePlay/Characters/Creation/MobModel.h"
#include "TheAwakener_FO/GamePlay/Roguelike/BattleUpgrade/RogueBattleUpgrade.h"
#include "ActionComponent.generated.h"

class AAwCharacter;//cheat IDE


DECLARE_DYNAMIC_MULTICAST_DELEGATE(FUseItemDelegate);
UCLASS( ClassGroup=(Custom), meta=(BlueprintSpawnableComponent) )
class THEAWAKENER_FO_API UActionComponent : public UActorComponent
{
	GENERATED_BODY()
	
private:
	//所有初始化挂载的动作
	UPROPERTY()
	TArray<FActionInfo> Actions;
	//TArray<FActionInfo*> LearntActions;
	//所有动态预挂载动作
	TMap<FString,FActionInfo> PreLoadActions;
	
	//Ai专用动作细节信息
	UPROPERTY()
	TMap<FString, FAiActionDetail>AiActionDetailsMap;

	UPROPERTY()
	TArray<FString> CancelActionTags;
	UPROPERTY()
	AAwCharacter* OwnerCharacter;

	FString LastPlayedMontageName;

	struct
	{
		AAwCharacter* Character;
		FActionParam ActionParam;
		TArray<FString> Params;

		int Result;
	} BlendSpacePickFuncParam;

	TArray<FActionPlanInfo> PreorderActionList;
	
	FVector ControlDir;	//每一帧操作导致角色应该取的面向
	FVector2D ChaDir;	//角色的面向2D坐标

	//是否处于武器拔出状态
	UPROPERTY()
	EArmState Armed = EArmState::Armed;
	//状态对应的有BlendSpace的Action的Id<动画状态<所处武装状态,Id>>
	TMap<ECharacterActionState, TMap<EArmState, FString>> StateActions;	
	TMap<ECharacterMontageState, TMap<EArmState, FString>> PreorderActionKeys;  //预约好的一些动作的Action的Id的关键字

	FString PlayingStateAction;

	void ForceStopMontage(bool StateJustChanged = false);

	/**
	 * @brief
	 * FSting - AnimFullName
	 * FControlState - 来自某个动画的ControlState
	 */
	TMap<FString, FControlState> ControlStates;

	/**
	 *当前可以进行Cancel的动作的Tag
	 */
	TArray<FActionPlanInfo> GetNowCanCancelledActions();
	//static void SortingActionsByPriority(TArray<FActionPlanInfo>& ActionInfos);

	//当前角色的动画状态
	ECharacterActionState CharacterActionState = ECharacterActionState::Ground;
	
	/**
	 *更换当前的blend space，根据状态和情况
	 *@param JustChange 刚好切换到这个动作，也就是说上一个动作不是这个动作
	 *@param MontageJustFreed montage动作是否这帧恰好被置为nullptr
	 */
	void PlayBlendSpace(bool JustChange = false, bool MontageJustFreed = false);
	
	/**
	 * @brief 切换MontageAction
	 * @param PlayActionInfo 要播放的动作的信息
	 */
	bool DoMontage(FActionPlanInfo PlayActionInfo);

	/**
	 * 检查当前的动作是否应该更换动画片，如果需要就更换并返回true
	 */
	bool CheckCurrMontageAnim();

	void CleanUpControlStates(FString AnimFullName);
	void CalculateActionControlState();

	//来自Notify的Acceptance 设置
	FVector NotifyMoveAcceptance = FVector::ZeroVector;
	float NotifyRotateAcceptance = 0;
	void AcceptanceRecheck() const;


	//状态机控制的blendspace的action，这是总存在的，要不是因为初始化还没到，要不就是出bug了才会没有
	FActionInfo BlendSpaceAction = FActionInfo();
	//当前动作的montage对应的action
	FActionInfo MontageAction = FActionInfo();
	//当前持续性保持动作
	FActionInfo KeepAction = FActionInfo();

	float CurActionDuration = -1;
	//播放montage action对应的动画片
	inline void PlayMontageAction(FActionPlanInfo ActionPlanInfo, FString MontageAnimNam);

	//当前激活中的CancelTag：<AnimNotifyState的UniqueId，Tag>
	//TMap<uint32, TArray<FString>> CurrCancelTags;
	TArray<FCancelTagInfo> StateCancelTags;
	// <ActionId, Tags>
	TMap<FString, TArray<FCancelTagInfo>> MontageCancelTags;
	TArray<FCancelTagInfo> TemporaryCancelTags;	//临时添加的Montage动画的CancelTag，注意是只有Montage允许

	/**
	 *获取一个Preorder的Action的Id，用来调用PreorderAction()
	 *@param MontageState 约定的MontageState，比如Hurt等
	 *@return Action的Id，很可能是空字符串，代表没找到对应的Action
	 */
	FString GetPreorderActionId(ECharacterMontageState MontageState);

	bool HasPreOrdered(FString ActionId);

	bool WasPlayMontage = false;

	bool HasTagInMontageCancelTags(FString Tag);
	bool HasTagInStateCancelTags(FString Tag);
	bool HasTagInTemporaryCancelTags(FString Tag);

	//当前被AnimNotifyState设定的速度
	bool IsSettingToSpdLv = false;	//是否正在被动画设置为某个阶段的移动速度？
	int SettingToSpdLv = 0;		//被动化设置为多少档移动速度

	//尝试获取目标ActionId对应的信息
	FAiActionDetail* TryFindAiActionDetail(FString AiActionId)
	{return AiActionDetailsMap.Find(AiActionId);}

	//检查当前持续行为
	bool TickCheckAiKeepAction(float DeltaTime);
	//产生新的持续行为
	void CreateNewKeepAction(FActionPlanInfo WillAction);
protected:
	virtual void BeginPlay() override;
	
public:
	//是否暂停了，逻辑上的暂停了
	UPROPERTY()
	bool Paused = false;

	//当前情况下，是否会把正在做的动作加入到候选动作列表（仅支持MontageAction），如果候选动作列表里有别的动作的话
	bool AddCurrentActionToCheckList = false;
	
	//当前的CharacterState的得走哪个下标获取动画
	UPROPERTY()
	int BlendSpaceAnimIndex = 0;

	// 蒙太奇中通过 CmdCheckState 判断的循环的次数
	// FString - UCmdCheckState.UniqueID
	// int Loop次数
	TMap<FString, int> MontageLoopCount;
	
	bool IsMontageActionPlaying() const;

	//使用主动道具时的委托
	UPROPERTY(BlueprintAssignable,Category = "Events")
	FUseItemDelegate UseItemDelegate;
	
	inline void SetupStateActions(TMap<FString, TMap<EArmState, FString>> Info);
	inline void SetupMontageActions(TMap<FString, TMap<EArmState, FString>> Info);
	
	// 角色当前正在做的动作
	FActionInfo* CurrAction()
	{
		FActionInfo* InstantAnimAction = this->MontageAction.Id.IsEmpty() ? &this->BlendSpaceAction : &this->MontageAction;
		return this->KeepAction.Id.IsEmpty() ? InstantAnimAction: &this->KeepAction;
	}

	FActionInfo CurrMontageAction() { return this->MontageAction; }
 
	void AddCancelTag(ECancelTagType TagType, int CancelPointIndex,  float Duration = 0, FActionInfo MAction = FActionInfo());
	void RemoveAllCancelTags();
	void RemoveMontageCancelTag(int CancelPointIndex, FActionInfo MAction = FActionInfo());
	void RemoveStateCancelTag(int CancelPointIndex);
	void RemoveTemporaryCancelTag(int CancelPointIndex);

	FControlState ActionControlState;
	
	UActionComponent();
	virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;
	void TickCheckAndDoAction(float DeltaTime);
	
	void SetUp(FBattleClassModel ClassInfo, FString TypeId);
	void ResetActions(FBattleClassModel ClassInfo, FString StyleId, FString TypeId);
	void RemoveActionWithCmdById(FString ActionId, FString Cmd);
	void AddActionWithCmdById(FString ActionId, FString Cmd);
	void AddActionWithCmd(FActionInfo Action, FString Cmd);
	void AddActionsByClassModel(FBattleClassModel ClassModel, FString TypeId);
	void ChangeStateActionsByClassModel(FBattleClassModel ClassModel);
	void SetUp(FMobModel MobModel);

	void AddActionById(FString ActionId);
	void RemoveActionById(FString ActionId);
	
	/***
	 *立即添加一组动作，其实是为了BaseAction的
	 *@param ToAddActions 要添加的动作
	 *@param InstantLearn 是否立即学会
	 */
	void AddActions(TArray<FActionInfo> ToAddActions, bool InstantLearn = true);
	
	/**
	 * @brief 预定Action
	 * @param Info 要预约的ActionInfo
	 * @param  ActionParam 一些其他的播放参数
	 */
	UFUNCTION(NetMulticast, Reliable)
	void PreorderAction(FActionChangeInfo Info,  FActionParam ActionParam = FActionParam());

	UFUNCTION(NetMulticast, Reliable)
	void PreorderActionById(const FString& ActionId,  FActionParam ActionParam = FActionParam(), float FromSec = 0);
	
	UFUNCTION(NetMulticast, Reliable)
	void TryPreorderActionById(const FString& ActionId,  FActionParam ActionParam = FActionParam(), float FromSec = 0);
	
	FActionInfo* GetActionById(FString Id);
	FActionInfo* GetActionByOriginId(FString Id);
	FActionInfo* GetActionByMontageState(ECharacterMontageState MontageState);
	FActionInfo* GetActionByActionState(ECharacterActionState ActionState);

	ECharacterActionState GetCharacterActionState() const;

	/**
	 *获取可以Cancel的动作
	 *@param PrevActions 需要检查的动作们
	 *@return 可以Cancel PrevActions中至少一个的Action组成的数组
	 */
	TArray<FActionInfo*> GetCancellableActions(TArray<FActionInfo*> PrevActions);
	TArray<FActionInfo*> GetCancellableActions(TArray<FString> PrevActions);

	/**
	 * 动作是否处于可以Cancel的范围内
	 */
	bool ActionCanCancelCurAction(FString ActionId);

	/**
	 *是否拥有某个Action
	 *@param ActionId 要检查的Action的id
	 */
	bool HasAction(FString ActionId);

	//立即停止当前的动作，并重新计算要播放的动作
	void StopCurrentAction(bool Force = true);

	//变更角色当前的动画状态，同时也得变更动画
	void ChangeCharacterState(ECharacterActionState ToState, bool Force = false);

	ECharacterActionState CurrentActionState()const {return this->CharacterActionState;}

	/**
	 *Notify改变Acceptance
	 */
	void SetInputAcceptance(FVector MoveAcceptance, float RotateAcceptance);

	/**
	 *获得当前正在播放的montage动画片
	 */
	FAnimMontageInstance* CurrentActiveMontage() const;

	/**
	 *根据MontageState来预约一个动作
	 *@param MontageState 约定的montage state
	 */
	void PreorderActionByMontageState(ECharacterMontageState MontageState, FActionParam Param = FActionParam(), float FromSec = 0);

	// 获取当前的Montage动画
	FString GetMontageAnim(FActionInfo* Action, FActionParam Param, bool IsNewAction);

	//是否正在播放一个Montage动画
	bool IsInMontageAction() const {return !this->MontageAction.Id.IsEmpty();}


	bool OnGround() const
	{
		return this->CharacterActionState == ECharacterActionState::Ground;
	}

	UFUNCTION(BlueprintPure)
	AAwCharacter* GetOwnerCharacter() { return OwnerCharacter; };

	/**
	 *学会一个动作
	 *@param Action 要学的动作的id
	 *@return 是否学成了，如果早已经学会，也会返回false的
	 */
	bool LearnAction(FActionInfo Action);

	/**
	 * 是否是一个HurtAction
	 * 要扩展Hurt种类可以走这里
	 */
	bool IsHurtAction(FActionInfo ActionInfo);

	/**
	 * 找到一个动作，其id为WasId，然后将id改为NewId
	 * @param  WasId 原本的动作id
	 * @param NewId 新的名称
	 * @return 改没改成
	 */
	bool ChangeActionId(FString WasId, FString NewId);

	/**
	 * 改变Actions中的某个Action的值
	 * @param ActionOriginId 要改的action的Id，这是指Action在json表时候的id
	 * @param Modifer 要改变的手法
	 * @return 改没改成，没找到自然就改不成了
	 */
	bool ModifyActionInfo(FString ActionOriginId, FActionInfoModifyApplication Modifer);

	/**
	 * 更换一个主技能
	 * @param ActionSelection 从哪一个组里面换
	 * @param SelectActionId 现在选中的这个Action的Id（OriginId）
	 */
	UFUNCTION(BlueprintCallable)
	void ChangeMainAction( FActionSelection ActionSelection, FString SelectActionId, bool Save = true);

	/**
	 * 更换一个技能后续技能
	 * @param ActionLink 从哪一个组里面换
	 * @param SelectedActionId 现在选中的这个Action的Id（OriginId）
	 */
	UFUNCTION(BlueprintCallable)
	void ChangeLinkedAction( FActionLink ActionLink, FString SelectedActionId, bool Save = true);

	/**
	 * 某个ActionSelection，当前状态下选中的技能是哪个
	 * @param ActionSelection 哪个组
	 * @return 这个组中当前被选中的技能的Id，空字符串代表没有被选中，若多个被选中，则是循环第一个结果
	 */
	UFUNCTION(BlueprintCallable)
	FString CurrentSelectedMainActionId(FActionSelection ActionSelection);

	/**
	 * 某个ActionSelection，当前状态下选中的技能是哪个
	 * @param ActionLink 哪个组
	 * @return 这个组中当前被选中的技能的Id，空字符串代表没有被选中，若多个被选中，则是循环第一个结果
	 */
	UFUNCTION(BlueprintCallable)
	FString CurrentLinkedSelectActionId(FActionLink ActionLink); 

	/**
	 * 被AnimNotifyState强行设置为动画档次
	 * @param On 是否启用，false代表关闭，true代表开启
	 * @param Lv 设置为多少级，0-3，会被Clamp的
	 */
	UFUNCTION()
	void SetToSpdLv(bool On, int Lv = 0);

	void SetToArms(EArmState ToArms){this->Armed = ToArms;}
	EArmState GetArmState() const { return this->Armed; }

	//获得当前可以Cancel的Action列表
	TArray<FActionInfo*> CurrentCanCancelledActions();

	//获取当前动作的Commands
	UFUNCTION()
	TArray<FString> GetCurActionCommands()
	{
		return this->CurrAction()->Commands;
	}

	//获取当前动作是否是翻滚
	UFUNCTION()
	bool IsDodgeAction()
	{
		return this->GetCurActionCommands().Contains("Dodge");
	}

	/**
	 * 获取当前动作对应的FRougeAbilityLevelInfo
	 * 用于在Buff生效时确认当前技能信息如Level等属性
	 * @return 当前动作对应的FRougeAbilityLevelInfo，如果找不到则返回默认值
	 */
	UFUNCTION(BlueprintCallable)
	struct FRougeAbilityLevelInfo GetCurrentActionAbilityLevelInfo() const;
};


