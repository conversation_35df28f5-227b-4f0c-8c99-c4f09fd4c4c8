// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/Object.h"
#include "InputAcceptanceModifier.generated.h"

/**
 * 一个计时解决InputAcceptance的玩意儿
 */
USTRUCT()
struct FInputAcceptanceModifier
{
	GENERATED_BODY()
public:
	//是否激活
	UPROPERTY()
	bool Active = false;
	//起始倍率
	UPROPERTY()
	float StartTimes = 0;
	//结束倍率
	UPROPERTY()
	float EndTimes = 1;
	//整个时间长度
	UPROPERTY()
	float Duration = 0;
	//当前走了多久了
	UPROPERTY()
	float TimeElapsed = 0;
	//回调Ease函数
	UPROPERTY()
	FString EaseFunc;

	FInputAcceptanceModifier(){};
	FInputAcceptanceModifier(float TimesOnStart, float TimesOnEnd, float TotalTime, FString Ease = ""):
		Active(true), StartTimes(TimesOnStart), EndTimes(TimesOnEnd), Duration(TotalTime), TimeElapsed(0), EaseFunc(Ease){}

	/**
	 * 每个tick调用，返回当前Times值
	 * @param DeltaTime 经过了多少秒
	 * @param AllowOverValue 是否允许超出0-1的范围计算结果
	 * @return 当前的倍率
	 */
	float Update(float DeltaTime, bool AllowOverValue = false);
};
