// Fill out your copyright notice in the Description page of Project Settings.


#include "ActionComponent.h"

#include "Animation/AnimMontage.h"
#include "TheAwakener_FO/FunctionLibrary/CallFuncLib.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GameFramework/Manager/AwDataManager.h"
#include "TheAwakener_FO/GameFramework/Manager/AwRogueDataSystem.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"

UActionComponent::UActionComponent()
{
	PrimaryComponentTick.bCanEverTick = true;
}

void UActionComponent::TryPreorderActionById_Implementation(const FString& ActionId, FActionParam ActionParam,
	float FromSec)
{
	if (HasPreOrdered(ActionId)) return;
	FActionInfo* Action = GetActionById(ActionId);
	if (!Action) return;
	if (Action->CanUseOnFalling == EUsableInState::Not && CharacterActionState == ECharacterActionState::Falling ||
		Action->CanUseOnFalling == EUsableInState::Only && CharacterActionState != ECharacterActionState::Falling)
		return;;
	for (const FCancelTag Tag : Action->Tags)
	{
		if (HasTagInMontageCancelTags(Tag.Tag) ||
			this->MontageAction.Id.IsEmpty() && HasTagInStateCancelTags(Tag.Tag) ||
			HasTagInTemporaryCancelTags(Tag.Tag))
		{
			FActionChangeInfo AInfo = FActionChangeInfo(EActionChangeMethod::ChangeActionInfo, ActionId );
			AInfo.FromSec = FromSec;
			PreorderAction(AInfo, ActionParam);
			break;
		}
	}
}

FActionInfo* UActionComponent::GetActionById(FString Id)
{
	if (PreLoadActions.Contains(Id))
		return &PreLoadActions[Id];
	
	for (int i = 0; i < Actions.Num(); ++i)
	{
		if (Actions[i].Id == Id)
			return &Actions[i];
	}
	
	return nullptr;
}

FActionInfo* UActionComponent::GetActionByOriginId(FString Id)
{
	if (PreLoadActions.Contains(Id))
		return &PreLoadActions[Id];
	
	for (int i = 0; i < Actions.Num(); ++i)
	{
		if (Actions[i].OriginId == Id)
			return &Actions[i];
	}
	
	return nullptr;
}

void UActionComponent::CleanUpControlStates(FString AnimFullName)
{
	if (ControlStates.Contains(AnimFullName))
	{
		if (ControlStates[AnimFullName].CanMove == EControlStateType::Normal &&
			ControlStates[AnimFullName].CanRotate == EControlStateType::Normal &&
			ControlStates[AnimFullName].CanJump == EControlStateType::Normal &&
			ControlStates[AnimFullName].CanChangeAction == EControlStateType::Normal
		)
			ControlStates.Remove(AnimFullName);
	}
}

void UActionComponent:: CalculateActionControlState()
{
	FControlState NewControlState;
	for (TTuple<FString, FControlState> ControlState : ControlStates)
		NewControlState = NewControlState + ControlState.Value;
	ActionControlState = NewControlState;
}

bool UActionComponent::HasTagInMontageCancelTags(FString Tag)
{
	if(!this->MontageAction.Id.IsEmpty() && this->MontageCancelTags.Contains(this->MontageAction.Id))
	{
		for (FCancelTagInfo Info : this->MontageCancelTags[this->MontageAction.Id])
			if (Info.Tags.Contains(Tag))
				return true;
	}
	return false;
}
bool UActionComponent::HasTagInStateCancelTags(FString Tag)
{
	for (FCancelTagInfo MTag : this->StateCancelTags)
	{
		if (MTag.Tags.Contains(Tag)) return true;
	}
	return false;
}
bool UActionComponent::HasTagInTemporaryCancelTags(FString Tag)
{
	for (FCancelTagInfo TempCancelTag : this->TemporaryCancelTags)
	{
		if (TempCancelTag.Tags.Contains(Tag)) return true;
	}
	return false;
}

TArray<FActionInfo*> UActionComponent::CurrentCanCancelledActions()
{
	TArray<FActionInfo*> ActionInfos;
	TArray<FActionInfo> CheckActions = Actions;
	
	//玩家预加载技能 根据玩家当前信息 进入筛选预备组
	if (OwnerCharacter->IsPlayerCharacter())
	{
		AAwPlayerState* playerState = UGameplayFuncLib::GetPlayerState(this);
		if (!playerState)
			return ActionInfos;
		if (!playerState)
			return ActionInfos;
		FAwActionSkillInfo* SkillInfo =  playerState->CurAwakeSkill;
		if(!SkillInfo)
		{
			FString SkillActionId = SkillInfo->SkillActionId;
			FString SkillCloseActionId =SkillInfo->SkillCloseActionId;
			if (!SkillInfo->bUsing)
			{
				if (PreLoadActions.Find(SkillActionId))
				{
					CheckActions.Add(*PreLoadActions.Find(SkillActionId));
				}
			}
			else
			{
				if (PreLoadActions.Find(SkillCloseActionId))
				{
					CheckActions.Add(*PreLoadActions.Find(SkillCloseActionId));
				}
			}
		}
	}
	else
	{
		//ToDo 非觉醒技能  非玩家
	}
	
	for (FActionInfo Action : CheckActions)
	{
		if (
			(Action.CanUseOnFalling == EUsableInState::Not && CharacterActionState == ECharacterActionState::Falling) ||
			(Action.CanUseOnFalling == EUsableInState::Only && CharacterActionState != ECharacterActionState::Falling)
		) continue;	
		for (const FCancelTag Tag : Action.Tags)
		{
			if (HasTagInMontageCancelTags(Tag.Tag) || (this->MontageAction.Id.IsEmpty() && HasTagInStateCancelTags(Tag.Tag)) || HasTagInTemporaryCancelTags(Tag.Tag))
			{
				ActionInfos.Add(&Action);
				
				break;
			}
		}
	}
	
	return ActionInfos;
}

TArray<FActionPlanInfo> UActionComponent::GetNowCanCancelledActions()
{
	TArray<FActionPlanInfo> ActionInfos;
	TArray<FActionInfo> CheckActions = Actions;

	//玩家预加载技能 根据玩家当前信息 进入筛选预备组
	if (OwnerCharacter->IsPlayerCharacter())
	{
		AAwPlayerState* playerState = UGameplayFuncLib::GetPlayerState(this);
		if (!playerState)
			return ActionInfos;
		FAwActionSkillInfo* SkillInfo =  playerState->CurAwakeSkill;
		if(SkillInfo)
		{
			FString SkillActionId = SkillInfo->SkillActionId;
			FString SkillCloseActionId =SkillInfo->SkillCloseActionId;
			if (!SkillInfo->bUsing)
			{
				if (PreLoadActions.Find(SkillActionId))
				{
					CheckActions.Add(*PreLoadActions.Find(SkillActionId));
				}
			}
			else
			{
				if (PreLoadActions.Find(SkillCloseActionId))
				{
					CheckActions.Add(*PreLoadActions.Find(SkillCloseActionId));
				}
			}
		}
	}


	
	for (FActionInfo Action : CheckActions)
	{
		if (Action.CanUseOnFalling == EUsableInState::Not && CharacterActionState == ECharacterActionState::Falling ||
			Action.CanUseOnFalling == EUsableInState::Only && CharacterActionState != ECharacterActionState::Falling)
				continue;
		
		for (const FCancelTag Tag : Action.Tags)
		{
			if (HasTagInMontageCancelTags(Tag.Tag) ||
				this->MontageAction.Id.IsEmpty() && HasTagInStateCancelTags(Tag.Tag) ||
				HasTagInTemporaryCancelTags(Tag.Tag))
			{
				ActionInfos.Add(FActionPlanInfo(Action, FForceMoveInfo(), Tag.FromSec));
				break;
			}
		}
	}
	
	return ActionInfos;
}

// void UActionComponent::SortingActionsByPriority(TArray<FActionPlanInfo>& ActionInfos)
// {
// 	for (int i = 0; i < ActionInfos.Num(); i++)
// 	{
// 		for (int j = 0; j < ActionInfos.Num() - 1; j++)
// 		{
// 			if (ActionInfos[j].Action->Priority > ActionInfos[j + 1].Action->Priority)
// 			{
// 				FPreorderActionInfo JC = ActionInfos[j];
// 				ActionInfos[j] = ActionInfos[j+1];
// 				ActionInfos[j+1] = JC;
// 			}
// 		}
// 	}
// }





bool UActionComponent::TickCheckAiKeepAction(float DeltaTime)
{

	FAiActionDetail* KeepActionDetailInfo = TryFindAiActionDetail(KeepAction.Id);
	if (!KeepActionDetailInfo)
	{
		return false;
	}
	EActionType CurActionType = KeepActionDetailInfo->ActionType;
	switch (CurActionType)
	{
		case EActionType::HasDuration:
		{
			if (KeepActionDetailInfo->Duration <= 0)
			{
				//异常持续时间的持续行为 如果有montage会同样按montageAction进行执行
				break;
			}

			CurActionDuration = FMath::Clamp(CurActionDuration, 0.0f, CurActionDuration);
			//判断持续性行为是否达到目标时间或动画长度之间的最大时间
			float MontageTime = OwnerCharacter->GetCurrentActiveMontage() == nullptr ? 0 : OwnerCharacter->GetCurrentActiveMontage()->GetSectionTimeLeftFromPos(0);
			float CompareTime = FMath::Max(MontageTime , KeepActionDetailInfo->Duration);
			CurActionDuration += DeltaTime;
			if (CurActionDuration<CompareTime)
			{
				return true;
			}
			else
			{
				//持续结束
				CurActionDuration = -1;
				KeepAction = FActionInfo();
				return false;
			}
		}
			break;
		case EActionType::Loop:
		{
			//ToDo
		}
		break;
	}
	return false;
}

void UActionComponent::CreateNewKeepAction(FActionPlanInfo WillAction)
{
	FAiActionDetail* NewAiActionDetailInfo = TryFindAiActionDetail(WillAction.ActionInfo.Id);
	if (NewAiActionDetailInfo != nullptr)
	{
		if (NewAiActionDetailInfo->ActionType != EActionType::Instant)
		{
			//判断新行为持续行为是否通过策略函数配置
			UFunction* DurationPolicyFunc = UCallFuncLib::GetUFunction(NewAiActionDetailInfo->ActionDurationPolicy.ClassPath,
				NewAiActionDetailInfo->ActionDurationPolicy.FunctionName);
			if (DurationPolicyFunc)
			{
				struct
				{
					UObject* Caller;
					TArray<FString> Params;
					float Result;
				}ParamPolicyFuncParam;
				ParamPolicyFuncParam.Caller = this;
				ParamPolicyFuncParam.Params = NewAiActionDetailInfo->ActionDurationPolicy.Params;
				this->ProcessEvent(DurationPolicyFunc, &ParamPolicyFuncParam);
				NewAiActionDetailInfo->Duration = ParamPolicyFuncParam.Result;
			}
			//新行为开始作为持续性行为
			FActionInfo* FindAction = GetActionById(WillAction.ActionInfo.Id);
			if(FindAction)
				KeepAction = *FindAction;
		}
	}
}

void UActionComponent::BeginPlay()
{
	Super::BeginPlay();
	
}

void UActionComponent::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
	Super::TickComponent(DeltaTime, TickType, ThisTickFunction);

	if (!IsValid(OwnerCharacter)) return;
	const bool IsPlayerCharacter = this->OwnerCharacter->IsPlayerCharacter();
	auto PC = UGameplayFuncLib::GetPlayerControllerByComp(this);
	UCmdComponent* CmdComp = OwnerCharacter->GetCmdComponent();
	if (!CmdComp) return;
	
	if (IsPlayerCharacter)
	{
		/*if (CmdComp->IsActionOccur("PauseMenu", EAwInputState::Press, true))
			UGameplayFuncLib::PauseGameForMenu();
		if (CmdComp->IsActionOccur("PauseResume", EAwInputState::Press, true))
			UGameplayFuncLib::ResumeGameForMenu();*/
		
		if (CmdComp->IsActionOccur("ManualMenu", EAwInputState::Press, true))
		{
			UGameplayFuncLib::GetUiManager()->Show("Rogue_BattleManual");
		}
		
		if (CmdComp->IsActionOccur("PauseMenu", EAwInputState::Press, true))
			UGameplayFuncLib::RogueGamePaused();
		if (CmdComp->IsActionOccur("RoguePauseResume", EAwInputState::Press, true))
			UGameplayFuncLib::RogueGameResume();
	}
	
	if (Paused == true)
		return;
	
	TickCheckAndDoAction(DeltaTime);

	// 判断是否按下使用道具
	if (IsPlayerCharacter)
	{
		if (CmdComp->IsActionOccur("Sprint", EAwInputState::Press, true))
			CmdComp->OnPressSprintAction();
		
		if (UAwRogueItemSubSystem* RogueItemSubSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueItemSubSystem>())
		{
			if (CmdComp->IsActionOccur("DrinkPotion", EAwInputState::Press, true))
				RogueItemSubSystem->UseHealingPotionAction(PC->GetLocalPCIndex());
			if (CmdComp->IsActionOccur("UseItem", EAwInputState::Press, true))
			{
				UseItemDelegate.Broadcast();
				RogueItemSubSystem->UseRogueItemAction(PC->GetLocalPCIndex(),true);
			}
			if (CmdComp->IsActionOccur("UseItem2", EAwInputState::Press, true))
			{
				UseItemDelegate.Broadcast();
				RogueItemSubSystem->UseRogueItemAction(PC->GetLocalPCIndex(),false);
			}
		}

		if (UAwCameraComponent* CameraComponent = OwnerCharacter->GetAwCameraComponent())
		{
			if (CmdComp->IsActionOccur("AwakeSkill", EAwInputState::Press, true))
				UKismetSystemLibrary::PrintString(this, "  >>> Remove Awake Skill Cmd <<<");
			
			if (CmdComp->IsActionOccur("ResetCamera", EAwInputState::Press, true))
			{
				CameraComponent->LockTarget();
				if (PC)
					PC->CheckLockSign();
				// CameraComponent->SetCameraRotationByForward(OwnerCharacter->GetActorForwardVector());
			}
		}
	}
}

void UActionComponent::TickCheckAndDoAction(float DeltaTime)
{
	if (OwnerCharacter != nullptr)
	{
		if (OwnerCharacter->Dead())
		{
			this->DoMontage(FActionPlanInfo(*GetActionById(GetPreorderActionId(ECharacterMontageState::Dead)), FForceMoveInfo()));
			return;
		}else if (OwnerCharacter->InSecondWind())
		{
			const FActionInfo* Anim = GetActionById(*GetPreorderActionId(ECharacterMontageState::SecondWind));
			if (this->DoMontage(FActionPlanInfo(*Anim, FForceMoveInfo())) == true)
			{
				return;
			}
			//TODO: 毕竟这里还可能做别的动作的，所以只有成功切换的那下才返回……
			return;
		}

		if (OwnerCharacter->ControlState.CanChangeAction == EControlStateType::NoControl)
			return;

		TArray<FActionPlanInfo> WillActions;	//这帧可能切换到的动作

		//如果可以操作，则操作输入一部分可选项
		if (OwnerCharacter->ControlState.CanChangeAction != EControlStateType::OutOfControl)
		{
			TArray<FActionPlanInfo> CancelActions = GetNowCanCancelledActions();
			for (int i = 0; i < CancelActions.Num(); i++)
			{
				for (const FString Cmd : CancelActions[i].ActionInfo.Commands)
				{
					if (OwnerCharacter->IsActionOccur(Cmd))
					{
						if (CharacterActionState != ECharacterActionState::Falling ||
							CancelActions[i].ActionInfo.CanUseOnFalling != EUsableInState::Not)
						{
							WillActions.Add(CancelActions[i]);
							break;
						}
					}
				} 
			} 
		}

		// 来自Preorder的动作也会被加入
		for (int i = 0; i < PreorderActionList.Num(); ++i)
			WillActions.Add(PreorderActionList[i]);
		PreorderActionList.Empty();

		// 看是不是有必要在蒙太奇的情况下把自己加入列表
		if (AddCurrentActionToCheckList == true && WillActions.Num() > 0 && IsInMontageAction())
		{
			WillActions.Add(FActionPlanInfo(*CurrAction(), FForceMoveInfo(), CurrentActiveMontage()->GetPosition()));
		}

		// 没有行动意愿高于当前动作的前提下 当前持续动作与循环动作判断
		bool bAiKeepAction = false;
		if (!OwnerCharacter->IsPlayerCharacter())
		{
			if (!WillActions.IsValidIndex(0) && !KeepAction.Id.IsEmpty())
			{
				bAiKeepAction = TickCheckAiKeepAction(DeltaTime);
				//依旧保持动作的情况下 保持行为作为一个动作意愿
				if (bAiKeepAction)
				{
					WillActions.Add(FActionPlanInfo(*CurrAction(), FForceMoveInfo(), CurrentActiveMontage() == nullptr ? NULL : CurrentActiveMontage()->GetPosition()));
				}
			}
			else
			{
				//有优先高于保持的行为
				CurActionDuration = -1;
				KeepAction = FActionInfo();
			}
		}
		
		//排序并筛选出最后一个去DoAction
		//SortingActionsByPriority(WillActions);
		WillActions.Sort([](const FActionPlanInfo& A1, const FActionPlanInfo& A2){
			//if (A1.ActionInfo == nullptr ) return false;
			//if (A2.ActionInfo == nullptr) return true;
			return A1.ActionInfo.Priority > A2.ActionInfo.Priority ;
		});
				
		if (!bAiKeepAction)
		{
			//执行播放蒙太奇
			bool GoPlayMontage = false;
			if (WillActions.Num() > 0)
			{
				for (int i = 0; i < WillActions.Num(); i++)
				{
					if (OwnerCharacter->CheckActionCost(WillActions[i].ActionInfo.Cost))
					{
						GoPlayMontage = this->DoMontage(WillActions[i]);
						if (GoPlayMontage)
							OwnerCharacter->DeductActionCost(WillActions[i].ActionInfo.Cost);
						break;
					}
					else
					{
						if (!WillActions[i].ActionInfo.Cost.ReplaceAction.IsEmpty())
						{
							OwnerCharacter->PreorderAction(WillActions[i].ActionInfo.Cost.ReplaceAction);
							break;
						}
						else
							if (!WillActions[i].ActionInfo.Cost.ReplaceCmd.IsEmpty())
							{
								OwnerCharacter->OwnerPlayerController->AddInputKeyByActionCmdId(WillActions[i].ActionInfo.Cost.ReplaceCmd);
								break;
							}
					}
				}
			}
			else
			{
				CheckCurrMontageAnim();
			}

			//如果没有蒙太奇，就播放space
			if (GoPlayMontage == false)
			{
				if (IsMontageActionPlaying() == false)
				{
					MontageAction = FActionInfo();	//在完全没有播放Montage的迹象的时候才去关闭
				
					if (WasPlayMontage == true)
					{
						OwnerCharacter->OnChangeAction(FActionInfo());	//TODO:上个动作如果真的用得到，那就真他妈麻烦了
					}
					WasPlayMontage = false;
				}
			}
		}
		this->PlayBlendSpace(false); //始终都要求play montage

		if (WillActions.Num() > 0 && !OwnerCharacter->IsPlayerCharacter()&& KeepAction.Id.IsEmpty())
		{
			CreateNewKeepAction(WillActions[0]);
		}

		//根据当前动作处理移动相关的
		if (CurrAction() != nullptr)
		{
			const int OwnerSpdLv = OwnerCharacter->GetMoveSpeedLevel();
			int SpdLv = (IsSettingToSpdLv == false || MontageAction.Id.IsEmpty()) ? OwnerSpdLv : FMath::Min(this->SettingToSpdLv, OwnerSpdLv);
			if (!KeepAction.Id.IsEmpty())
			{
				if (KeepAction.Anim.AnimPath.Num()<=0)
				{
					SpdLv = 0;
				}
			}

			// if (this->OwnerCharacter->IsPlayerCharacter())
			// {
			// 	FString Str = "";
			// 	Str.Append("   MontageAction: ");qqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqq
			// 	if (MontageAction)
			// 		Str.Append(MontageAction->Id);
			// 	else
			// 		Str.Append("Null");
			// 	Str.Append("   IsSettingToSpdLv: ");
			// 	IsSettingToSpdLv ? Str.Append("True") : Str.Append("False");
			// 	Str.Append("   SettingToSpdLv: " + FString::FromInt(SettingToSpdLv));
			// 	Str.Append("   SpeLv: " + FString::FromInt(SpdLv));
			// 	UKismetSystemLibrary::PrintString(this, Str);
			// }
			
			//const int LogicSpdLv = OwnerCharacter->GetMoveSpeedLevel();	//不能加速的动作和上面动作一样了，所以就不再用LogicSpdLv了
			OwnerCharacter->SetForwardSpeedLevelLimit(SpdLv);
			OwnerCharacter->SetXYMove(
				OwnerCharacter->GetMoveDirection(),
				SpdLv
			);
		}

		//清理一下AI的
		OwnerCharacter->ClearAICmd();
		//临时CancelTag的处理
		int TempIndex = 0;
		while (TempIndex < TemporaryCancelTags.Num())
		{
			this->TemporaryCancelTags[TempIndex].Duration -= DeltaTime;
			if (this->TemporaryCancelTags[TempIndex].Duration <= 0)
			{
				TemporaryCancelTags.RemoveAt(TempIndex);
			}else
			{
				TempIndex++;
			}
		}
	}
}

void UActionComponent::SetToSpdLv(bool On, int Lv)
{
	this->IsSettingToSpdLv = On;
	this->SettingToSpdLv = Lv;
}

void UActionComponent::SetUp(FBattleClassModel ClassInfo, FString TypeId)
{
	OwnerCharacter = Cast<AAwCharacter>(GetOwner());

	FString CurStyleId = "";
	if (const UAwRogueDataSystem* DataSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>())
		CurStyleId = DataSystem->GetBattleStyle(ClassInfo.Id);
	ResetActions(ClassInfo, CurStyleId, TypeId);

	//预加载动作信息从DataManager初始化以及挂载
	this->PreLoadActions.Empty();
	TArray<FActionInfo> PreActions = UGameplayFuncLib::GetDataManager()->GetBaseActionsById(ClassInfo.BaseActionType+"_PreLoad");
	for (FActionInfo PreAction : PreActions)
	{
		PreAction = UResourceFuncLib::ReplaceAnimPathByTypeId(PreAction, TypeId);
		this->PreLoadActions.Add(PreAction.Id,PreAction);
	}
	// 预加载动作信息原本是存放觉醒动作，现在觉醒动作全部都干掉了，现在用来存放所有法器动作。---qsl 2023-11-20 18:57:28
	// TODO：之后的优化方案：法器动作可以在装备法器的时候在加入玩家的Actions队列 --- qsl 2023-11-20 18:58:21
	this->PreLoadActions.Append(UGameplayFuncLib::GetDataManager()->GetAllItemActionInfos());
	// this->PreLoadActions.Append(UGameplayFuncLib::GetDataManager()->GetAllAwakeSkillActionInfo());
	
	this->SetupStateActions(ClassInfo.StateActions);
	this->SetupMontageActions(ClassInfo.MontageActions);
	this->ChangeCharacterState(ECharacterActionState::Ground, true);
}

void UActionComponent::ResetActions(FBattleClassModel ClassInfo, FString StyleId, FString TypeId)
{
	//动作信息从DataManager初始化以及挂载
	this->Actions.Empty();
	this->MontageAction = FActionInfo();
	for (FActionInfo ClassAction : ClassInfo.Actions)
	{
		ClassAction = UResourceFuncLib::ReplaceAnimPathByTypeId(ClassAction, TypeId);
		this->Actions.Add(ClassAction);
	}

	/*
	for (FActionInfo Action : ClassInfo.RogueBattleActions)
	{
		const UAwRogueDataSystem* RogueDataSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>();
		FRogueBattleStyle RogueBattleStyle = UGameplayFuncLib::GetDataManager()->GetRogueBattleStyle(StyleId);

		for (TTuple<ERogueAbilitySlot, FRogueBattleStyleAbility> Ability : RogueBattleStyle.Abilitys)
		{
			if (Ability.Value.DefaultActions.Contains(Action.Id))
			{
				switch (Ability.Key)
				{
				case ERogueAbilitySlot::NormalAttack:
					AddActionWithCmd(Action, "Action1"); break;
				case ERogueAbilitySlot::Ground1:
					AddActionWithCmd(Action, "Action2"); break;
				case ERogueAbilitySlot::Ground2:
					AddActionWithCmd(Action, "Action3"); break;
				case ERogueAbilitySlot::Air1:
					AddActionWithCmd(Action, "Action2"); break;
				case ERogueAbilitySlot::Air2:
					AddActionWithCmd(Action, "Action3"); break;
				default: ;
				}
			}
		}
	}
	*/

	for (FActionInfo Action : ClassInfo.RogueBaseActions)
	{
		// Action = UResourceFuncLib::ReplaceAnimPathByTypeId(Action, TypeId);
		this->Actions.Add(Action);
	}
	
	//基础动作信息从DataManager初始化以及挂载
	TArray<FActionInfo> BaseActions = UGameplayFuncLib::GetDataManager()->GetBaseActionsById(ClassInfo.BaseActionType);
	for (FActionInfo BaseAction : BaseActions)
	{
		BaseAction = UResourceFuncLib::ReplaceAnimPathByTypeId(BaseAction, TypeId);
		this->Actions.Add(BaseAction);
	}
}

void UActionComponent::RemoveActionWithCmdById(FString ActionId, FString Cmd)
{
	int i = 0;
	while (i < this->Actions.Num())
	{
		if (this->Actions[i].Id == ActionId &&
			this->Actions[i].Commands.Contains(Cmd))
		{
			this->Actions[i].Commands.Remove(Cmd);
			
			if (this->Actions[i].Commands.Num() == 0)
			{
				this->Actions.RemoveAt(i);
			}
			else
				i++;
		}
		else
			i++;
	}
}

void UActionComponent::AddActionWithCmdById(FString ActionId, FString Cmd)
{
	// GetBattleClass
	FBattleClassModel BattleClass = UGameplayFuncLib::GetAwDataManager()->GetBattleClassModelById(OwnerCharacter->CharacterObj.ClassId);
	for (FActionInfo Action : BattleClass.RogueBattleActions)
		if (Action.Id == ActionId)
			AddActionWithCmd(Action, Cmd);
}

void UActionComponent::AddActionWithCmd(FActionInfo Action, FString Cmd)
{
	bool HasAction = false;
	for (int i = 0; i < this->Actions.Num(); ++i)
	{
		if(this->Actions[i].Id == Action.Id)
		{
			this->Actions[i].Commands.Add(Cmd);
			HasAction = true;
			break;
		}
	}
	if (!HasAction)
	{
		Action.Commands.Add(Cmd);
		this->Actions.Add(Action);
	}
}

void UActionComponent::AddActionsByClassModel(FBattleClassModel ClassModel, FString TypeId)
{
	for (FActionInfo ToAddAction : ClassModel.Actions)
	{
		ToAddAction = UResourceFuncLib::ReplaceAnimPathByTypeId(ToAddAction, TypeId);
		
		if (this->Actions.Contains(ToAddAction) == false)
			Actions.Add(ToAddAction);
	} 
}

void UActionComponent::ChangeStateActionsByClassModel(FBattleClassModel ClassModel)
{
	this->SetupStateActions(ClassModel.StateActions);
	this->SetupMontageActions(ClassModel.MontageActions);
	// this->ChangeCharacterState(ECharacterActionState::Ground, true);
	// PlayBlendSpace(false, false);
	PlayBlendSpace(true, true);
}

void UActionComponent::SetUp(FMobModel MobModel)
{
	OwnerCharacter = Cast<AAwCharacter>(GetOwner());

	//动作信息初始化
	for (int i = 0; i < MobModel.Actions.Num(); i++)
	{
		this->Actions.Add(MobModel.Actions[i]);
	}
	TArray<FActionInfo> BaseActions = UGameplayFuncLib::GetDataManager()->GetBaseActionsById(MobModel.BaseActionType);
	for (FActionInfo BaseAction : BaseActions)
	{
		this->Actions.Add(BaseAction);
	}
	
	//AI专用动作细节信息初始化
	for (auto info : MobModel.AiActionDetails)
	{
		this->AiActionDetailsMap.Add(info.AiActionId, info);
	}

	this->SetupStateActions(MobModel.StateActions);
	this->SetupMontageActions(MobModel.PreorderActionKeys);
	
	this->ChangeCharacterState(ECharacterActionState::Ground, true);
}

void UActionComponent::AddActionById(FString ActionId)
{
	FBattleClassModel BattleClass = UGameplayFuncLib::GetAwDataManager()->GetBattleClassModelById(OwnerCharacter->CharacterObj.ClassId);
	for (FActionInfo Action : BattleClass.RogueBattleActions)
		if (Action.Id == ActionId)
		{
			if (!this->Actions.Contains(Action))
				this->Actions.Add(Action);
		}
}

void UActionComponent::RemoveActionById(FString ActionId)
{
	int i = 0;
	while (i < this->Actions.Num())
	{
		if (this->Actions[i].Id == ActionId)
			this->Actions.RemoveAt(i);
		else
			i++;
	}
}

void UActionComponent::SetupStateActions(TMap<FString, TMap<EArmState, FString>> Info)
{
	if (Info.Contains(FString("Ground")))
		this->StateActions.Add(ECharacterActionState::Ground, Info[FString("Ground")]);
	if (Info.Contains(FString("Falling")))
		this->StateActions.Add(ECharacterActionState::Falling, Info[FString("Falling")]);
	if (Info.Contains(FString("Flying")))
		this->StateActions.Add(ECharacterActionState::Flying, Info[FString("Flying")]);
	if (Info.Contains(FString("Attached")))
		this->StateActions.Add(ECharacterActionState::Attached, Info[FString("Attached")]);
}

void UActionComponent::SetupMontageActions(TMap<FString, TMap<EArmState, FString>> Info)
{
	if (Info.Contains(FString("Blow")))
		this->PreorderActionKeys.Add(ECharacterMontageState::Blow, Info[FString("Blow")]);
	if (Info.Contains(FString("Hurt")))
		this->PreorderActionKeys.Add(ECharacterMontageState::Hurt, Info[FString("Hurt")]);
	if (Info.Contains(FString("Dead")))
		this->PreorderActionKeys.Add(ECharacterMontageState::Dead, Info[FString("Dead")]);
	if (Info.Contains(FString("Bounced")))
		this->PreorderActionKeys.Add(ECharacterMontageState::Bounced, Info[FString("Bounced")]);
	if (Info.Contains(FString("Landing")))
		this->PreorderActionKeys.Add(ECharacterMontageState::Landing, Info[FString("Landing")]);
	if (Info.Contains(FString("SecondWind")))
		this->PreorderActionKeys.Add(ECharacterMontageState::SecondWind, Info[FString("SecondWind")]);
	if (Info.Contains(FString("GetUp")))
		this->PreorderActionKeys.Add(ECharacterMontageState::GetUp, Info[FString("GetUp")]);
	if (Info.Contains(FString("Frozen")))
		this->PreorderActionKeys.Add(ECharacterMontageState::Frozen, Info[FString("Frozen")]);
}

void UActionComponent::AddActions(TArray<FActionInfo> ToAddActions, bool InstantLearn)
{
	TArray<FString> ToLearnActionId ;
	for (int i = 0; i < ToAddActions.Num(); i++)
	{
		// this->Actions.Add(ToAddActions[i]);
		this->Actions.Add(UResourceFuncLib::ReplaceAnimPathByTypeId(ToAddActions[i], GetOwnerCharacter()->CharacterObj.TypeId));
		if (InstantLearn == true) ToLearnActionId.Add(ToAddActions[i].Id);
	}
}

bool UActionComponent::CheckCurrMontageAnim()
{
	if (MontageAction.Id.IsEmpty()) return false;
	if (!MontageAction.Anim.CheckOnTick) return false; 
	const FString MontageAnimName =  this->GetMontageAnim(&MontageAction, FActionParam(), false);
	if (MontageAnimName == LastPlayedMontageName)
	{
		return false;
	}
	if (MontageAnimName.IsEmpty())
	{
		ForceStopMontage();
		return true;
	}

	const float FromSec = CurrentActiveMontage() ? CurrentActiveMontage()->GetPosition() : 0;
	const FActionPlanInfo PlayActionInfo = FActionPlanInfo(MontageAction, FForceMoveInfo(), FromSec);
	PlayMontageAction(PlayActionInfo, MontageAnimName);

	AcceptanceRecheck();

	return true;
}

bool UActionComponent::DoMontage(FActionPlanInfo PlayActionInfo)
{
	
	FActionInfo* ToAction = GetActionById(PlayActionInfo.ActionInfo.Id);
	const bool IsNewAction = MontageAction.Id.IsEmpty() || ToAction == nullptr ||
		(PlayActionInfo.ActionInfo.Id != MontageAction.Id);

	
	// if (this->OwnerCharacter->IsPlayerCharacter() == true && IsNewAction == true)
	// {
	// 	UKismetSystemLibrary::PrintString(this->OwnerCharacter, this->OwnerCharacter->GetName().Append(" DoAction <")
	// 		.Append(ToAction ? ToAction->Id : "NoAction").Append(">"),
	// 		true, true, FLinearColor::Green);
	// }

	// if (ToAction && ToAction->Commands.Contains("Aim"))
	// {
	// 	UKismetSystemLibrary::PrintString(this, FString("Aim Action is ").Append(ToAction->OriginId),
	// 		true, true, FLinearColor::Yellow, 60);
	// }

	if (IsNewAction)
	{
		FActionInfo WasAct = FActionInfo();
		if(CurrAction()) WasAct = *CurrAction();
		MontageAction = *ToAction;
		OwnerCharacter->OnChangeAction(WasAct);
	}
	
	const FString MontageAnimName =  this->GetMontageAnim(&MontageAction, PlayActionInfo.Param, IsNewAction);
	
	if (MontageAction.Id.IsEmpty() || MontageAnimName.IsEmpty())//MontageAction->MontageAnim.IsEmpty())
	{
		ForceStopMontage();
		return false;
	}
	
	PlayMontageAction(PlayActionInfo, MontageAnimName);

	const bool PlayMontageSuccess = WasPlayMontage;
	
	// 是否播放蒙太奇成功（并别和上一个Action一样，如果和上一个蒙太奇不一样，在上面就清空过了）
	if (!IsNewAction && WasPlayMontage)
		OwnerCharacter->OnChangeAction(MontageAction);
	
	AcceptanceRecheck();
	
	return PlayMontageSuccess;
}

void UActionComponent::PlayMontageAction(FActionPlanInfo ActionPlanInfo, FString MontageAnimName)
{
	if (!MontageAction.Id.IsEmpty()) {
		LastPlayedMontageName = MontageAnimName;
		MontageLoopCount.Empty();
		WasPlayMontage = OwnerCharacter->PlayMontageAnim(MontageAnimName, ActionPlanInfo);
	}
}

void UActionComponent::PreorderActionById_Implementation(const FString& ActionId,  FActionParam ActionParam, float FromSec)
{
	if (HasPreOrdered(ActionId)) return;
	FActionInfo* ActionInfo = GetActionById(ActionId);
	if (!ActionInfo) return;
	FActionChangeInfo AInfo = FActionChangeInfo(EActionChangeMethod::ChangeActionInfo, ActionId );
	AInfo.FromSec = FromSec;
	PreorderAction(AInfo, ActionParam);
}

void UActionComponent::PreorderAction_Implementation(FActionChangeInfo Info, FActionParam ActionParam)
{
	if (HasPreOrdered(Info.ChangeToActionId) || Info.ChangeMethod == EActionChangeMethod::Keep) return;
	FActionInfo* ActionInfo = 
			GetActionById(Info.ChangeMethod == EActionChangeMethod::ChangeActionInfo ? Info.ChangeToActionId : GetPreorderActionId(Info.ToState));
	if (ActionInfo != nullptr)
	{
		FActionPlanInfo PreAct = FActionPlanInfo();
		PreAct.ActionInfo = *ActionInfo;
		PreAct.StartFrom = Info.FromSec;
		PreAct.Param = ActionParam;
		PreAct.Priority = Info.Priority;
		PreAct.FreezeTime = Info.FreezeTime;
		PreAct.DefaultPlay = true;	//TODO: 先不改编动画播放速度试试看
		PreAct.MoveAfterFrozen = Info.HitStun;
		PreorderActionList.Add(PreAct);
	}
}

bool UActionComponent::HasPreOrdered(FString ActionId)
{
	for (const FActionPlanInfo ActionList : PreorderActionList)
	{
		if ( ActionList.ActionInfo.Id == ActionId)
			return true;
	}
	return false;
}

TArray<FActionInfo*> UActionComponent::GetCancellableActions(TArray<FActionInfo*> PrevActions)
{
	TArray<FActionInfo*> Res;
	
	if (PrevActions.Num())
	{
		for (FActionInfo Action : this->Actions)
		{
			for (FActionInfo* PrevAct : PrevActions)
			{
				if (PrevAct->CanBeCancelledBy(&Action))
				{
					Res.Add(&Action);
					break;
				}
			}
		}
	}
	return Res;
}

bool UActionComponent::ActionCanCancelCurAction(FString ActionId)
{
	const FActionInfo* Act = GetActionById(ActionId);
	if (!Act) return false;
	
	if (
		(Act->CanUseOnFalling == EUsableInState::Not && CharacterActionState == ECharacterActionState::Falling) ||
		(Act->CanUseOnFalling == EUsableInState::Only && CharacterActionState != ECharacterActionState::Falling)
	) return false;
	
	for (const FCancelTag Tag : Act->Tags)
	{
		if (HasTagInMontageCancelTags(Tag.Tag) || (this->MontageAction.Id.IsEmpty() && HasTagInStateCancelTags(Tag.Tag)) || HasTagInTemporaryCancelTags(Tag.Tag))
		{
			return  true;
		}
	}

	return false;
}

TArray<FActionInfo*> UActionComponent::GetCancellableActions(TArray<FString> PrevActions)
{
	TArray<FActionInfo*> AInfo;
	for (const  FString PrevActionId : PrevActions)
	{
		AInfo.Add(GetActionById(PrevActionId));
	}
	return GetCancellableActions(AInfo);
}

bool UActionComponent::HasAction(FString ActionId)
{
	for (FActionInfo Action : this->Actions)
	{
		if (Action.Id == ActionId) return true;
	} 
	return false;
}

void UActionComponent::StopCurrentAction(bool Force)
{
	if (Force == false)
	{
		this->MontageAction = FActionInfo();
		PlayBlendSpace(false, true);
	}else
	{
		this->ForceStopMontage();
	}
	this->KeepAction = FActionInfo();
	CurActionDuration = -1;
}

void UActionComponent::ChangeCharacterState(ECharacterActionState ToState, bool Force)
{
	const bool JustChange = this->CharacterActionState != ToState;
	this->CharacterActionState = ToState;

	const bool AsJustChanged = JustChange == true || Force == true;
	if (AsJustChanged)
	{
		PlayBlendSpace(AsJustChanged, false);
	}
}

void UActionComponent::PlayBlendSpace(bool JustChange, bool MontageJustFreed)
{
	if (this->CharacterActionState == ECharacterActionState::Attached)
	{
		//就这个不是独特的……但现在成了特别独特的了
		if (OwnerCharacter->CharacterAttachment.AttachTarget == nullptr)
		{
			ChangeCharacterState(ECharacterActionState::Falling);
			return;
		}
		FActionInfo* FindAction = GetActionById(OwnerCharacter->CharacterAttachment.AttachTarget->CatcherActionId);
		if(FindAction)
			BlendSpaceAction = *FindAction;
	}else
	{
		if (StateActions.Contains(CharacterActionState) &&
			StateActions[CharacterActionState].Contains(Armed))
		{
			FActionInfo* FindAction = GetActionById(StateActions[CharacterActionState][Armed]);
			if(FindAction)
				BlendSpaceAction = *FindAction;
		}
		else
			BlendSpaceAction = FActionInfo();
	}
	
	if (BlendSpaceAction.Id.IsEmpty()) return;

	//现在完全依靠BlendSpaceAnimIndex了，不然会有矛盾
	// int AIndex = 0;
	// UFunction* Func = nullptr;
	// TArray<FString> FuncParams;
	// if(BlendSpaceAction->Anim.TickChecker != "")
	// {
	// 	const FJsonFuncData JsonFunc = UCallFuncLib::StringToJsonFuncData(BlendSpaceAction->Anim.TickChecker);
	// 	FuncParams = JsonFunc.Params;
	// 	Func = UCallFuncLib::JsonFuncToUFunc(JsonFunc);
	// }
	// if (Func)
	// {
	// 	BlendSpacePickFuncParam.Character = OwnerCharacter;
	// 	BlendSpacePickFuncParam.Params = FuncParams;
	// 	OwnerCharacter->ProcessEvent(Func, &BlendSpacePickFuncParam);
	// 	AIndex = BlendSpacePickFuncParam.Result;	
	// }
	// if (AIndex < 0 || AIndex >= BlendSpaceAction->Anim.AnimPath.Num())
	// {
	// 	AIndex = 0;
	// }

	const int AIndex = BlendSpaceAnimIndex >= BlendSpaceAction.Anim.AnimPath.Num() ? 0 : BlendSpaceAnimIndex;
	if (BlendSpaceAction.Anim.AnimPath.Num() > 0)
	{
		FString ToPlay = BlendSpaceAction.Anim.AnimPath[AIndex];
		//UKismetSystemLibrary::PrintString(this, FString("BlendSpace ").Append(FString::FromInt(AIndex)).Append(" ").Append(ToPlay));
		if (PlayingStateAction != ToPlay || JustChange == true)
		{
			OwnerCharacter->PlayMoveBlendSpace(ToPlay);
			PlayingStateAction = ToPlay;
		}
	}else
	{
		// UKismetSystemLibrary::PrintString(this, FString("BlendSpace No Anim => ").Append(BlendSpaceAction->Id));
	}
	

	AcceptanceRecheck();
}

void UActionComponent::ForceStopMontage(bool StateJustChanged)
{
	this->MontageAction = FActionInfo();
	OwnerCharacter->StopAllMontages(0.2f);
	//this->OwnerCharacter->RotateInputAcceptance = 1.f;
	//this->OwnerCharacter->SpeedInputAcceptance = FVector::OneVector;
	PlayBlendSpace(false, true);
}

void UActionComponent::SetInputAcceptance(FVector MoveAcceptance, float RotateAcceptance)
{
	//TODO will be removed
	// this->NotifyMoveAcceptance = FVector(
	// 	 MoveAcceptance.X,
	// 	MoveAcceptance.Y,
	// 	MoveAcceptance.Z
	// );
	// this->NotifyRotateAcceptance = RotateAcceptance;
	// AcceptanceRecheck();
}

void UActionComponent::AcceptanceRecheck() const
{
	//TODO will be removed
	//现在就是这样的——没有Montage就是1.f，否则是0.f。
	// OwnerCharacter->SpeedInputAcceptance = FVector(
	// 	FMath::Max(0.f, NotifyMoveAcceptance.X +  (MontageAction != nullptr ? 0.f : 1.f)),
	// 	FMath::Max(0.f, NotifyMoveAcceptance.Y +  (MontageAction != nullptr ? 0.f : 1.f)),
	// 	FMath::Max(0.f, NotifyMoveAcceptance.Z +  (MontageAction != nullptr ? 0.f : 1.f))
	// );
	// UKismetSystemLibrary::PrintString(this, FString("Acceptance Set").Append(OwnerCharacter->SpeedInputAcceptance.ToString()),
	// 	true, true, FLinearColor::Green, 30);
	// OwnerCharacter->RotateInputAcceptance =
	// 	FMath::Max(0.f,NotifyRotateAcceptance + (MontageAction != nullptr ? 0.f : 1.f));
	
}

bool UActionComponent::IsMontageActionPlaying() const
{
	//只要有哪怕一条Montage在播放，说明动画在播放，毕竟现在Montage只有MontageAction有了
	// if (IsValid(OwnerCharacter->GetCurrentActiveMontage())) 
	// 	UKismetSystemLibrary::PrintString(this, "CurrentActiveMontage:  " + OwnerCharacter->GetCurrentActiveMontage()->GetName());
	return IsValid(OwnerCharacter->GetCurrentActiveMontage());
}

FAnimMontageInstance* UActionComponent::CurrentActiveMontage() const 
{
	return OwnerCharacter->GetActiveMontageInstance();
}

FString UActionComponent::GetPreorderActionId(ECharacterMontageState MontageState)
{
	FString ActKey =
		(this->PreorderActionKeys.Contains(MontageState) &&
			this->PreorderActionKeys[MontageState].Contains(Armed) ) ?
				this->PreorderActionKeys[MontageState][Armed] : FString("");
	if (ActKey.IsEmpty() == true) return ActKey;
	return GetActionById(ActKey) == nullptr ? FString("") : ActKey;
}

void UActionComponent::PreorderActionByMontageState(ECharacterMontageState MontageState, FActionParam Param, float FromSec)
{
	const FString ActionKey = GetPreorderActionId(MontageState);
	if (ActionKey.IsEmpty() == false) PreorderActionById(ActionKey, Param, FromSec);
}

FActionInfo* UActionComponent::GetActionByMontageState(ECharacterMontageState MontageState)
{
	const FString ActionId = GetPreorderActionId(MontageState);
	if (ActionId.IsEmpty()) return nullptr;
	return GetActionById(ActionId);
}

FActionInfo* UActionComponent::GetActionByActionState(ECharacterActionState ActionState)
{
	const FString ActionId = StateActions.Contains(ActionState) ? StateActions[ActionState][Armed] : "";
	if (ActionId.IsEmpty()) return nullptr;
	return GetActionById(ActionId);
}

ECharacterActionState UActionComponent::GetCharacterActionState() const
{
	return this->CharacterActionState;
}

void UActionComponent::AddCancelTag(ECancelTagType TagType, int CancelPointIndex, float Duration, FActionInfo MAction)
{
	if (CurrAction() == nullptr || CurrAction()->CanBeCancelledTag.Num() <= CancelPointIndex) return;

	if (MAction.Id.IsEmpty())
		MAction = MontageAction;
	
	TArray<FString> TagItems;
	switch (TagType)
	{
	case ECancelTagType::Montage:
		{
			if (!MAction.Id.IsEmpty() && MAction.CanBeCancelledTag.Contains(CancelPointIndex))
			{
				TagItems.Append(MAction.CanBeCancelledTag[CancelPointIndex]);
			}
			const FCancelTagInfo TagInfo = FCancelTagInfo(CancelPointIndex, TagItems);
			if (MontageCancelTags.Contains(MAction.Id))
			{
				if (!MontageCancelTags[MAction.Id].Contains(TagInfo))
					MontageCancelTags[MAction.Id].Add(TagInfo);
			}
			else
			{
				TArray<FCancelTagInfo> TagInfos;
				TagInfos.Add(TagInfo);
				MontageCancelTags.Add(MAction.Id, TagInfos);
			}
		}break;
	case ECancelTagType::State:
		{
			if (!BlendSpaceAction.Id.IsEmpty() && BlendSpaceAction.CanBeCancelledTag.Contains(CancelPointIndex))
			{
				TagItems.Append(BlendSpaceAction.CanBeCancelledTag[CancelPointIndex]);
			}
			const FCancelTagInfo TagInfo = FCancelTagInfo(CancelPointIndex, TagItems);
			StateCancelTags.Empty();
			StateCancelTags.Add(TagInfo);

		}break;
	case ECancelTagType::Temporary:
		{
			if (!MAction.Id.IsEmpty() && MAction.CanBeCancelledTag.Contains(CancelPointIndex))
			{
				TagItems.Append(MAction.CanBeCancelledTag[CancelPointIndex]);
			}
			const FCancelTagInfo TagInfo = FCancelTagInfo(CancelPointIndex, TagItems, Duration);
			
			if (!TemporaryCancelTags.Contains(TagInfo))
				TemporaryCancelTags.Add(TagInfo);

		}break;
		default:break;
	}

}
void UActionComponent::RemoveAllCancelTags()
{
	StateCancelTags.Empty();
	MontageCancelTags.Empty();
	TemporaryCancelTags.Empty();
}

void UActionComponent::RemoveMontageCancelTag(int CancelPointIndex, FActionInfo MAction)
{
	if (MAction.Id.IsEmpty())
		MAction = this->MontageAction;

	if (MontageCancelTags.Contains(MAction.Id))
	{
		int i = 0;
		while (i < MontageCancelTags[MAction.Id].Num())
		{
			if (MontageCancelTags[MAction.Id][i].CancelPointIndex == CancelPointIndex)
			{
				MontageCancelTags[MAction.Id].RemoveAt(i);
			}else
			{
				i++;
			}
		}
	}

	// Clear Empty
	TArray<FString> WillToRemove;
	for (TTuple<FString, TArray<FCancelTagInfo>> MTag : MontageCancelTags)
		if (MTag.Value.Num() <= 0)
			WillToRemove.Add(MTag.Key);
	for (FString ToRemove : WillToRemove) 
		MontageCancelTags.Remove(ToRemove);
}
void UActionComponent::RemoveStateCancelTag(int CancelPointIndex)
{
	int i = 0;
	while (i < StateCancelTags.Num())
	{
		if (StateCancelTags[i].CancelPointIndex == CancelPointIndex)
		{
			StateCancelTags.RemoveAt(i);
		}else
		{
			i++;
		}
	}
}
void UActionComponent::RemoveTemporaryCancelTag(int CancelPointIndex)
{
	int i = 0;
	while (i < TemporaryCancelTags.Num())
	{
		if (TemporaryCancelTags[i].CancelPointIndex == CancelPointIndex)
		{
			TemporaryCancelTags.RemoveAt(i);
		}else
		{
			i++;
		}
	}
}


FString UActionComponent::GetMontageAnim(FActionInfo* Action, FActionParam Param, bool IsNewAction)
{
	if (!Action || Action->Anim.AnimPath.Num() <= 0) return FString();
	if (Action->Anim.TickChecker.IsEmpty()) return Action->Anim.AnimPath[0];

	if (IsNewAction == true || Action->Anim.Period == true)
	{
		const FJsonFuncData JsonFunc = UCallFuncLib::StringToJsonFuncData(Action->Anim.TickChecker);
	
		struct
		{
			AAwCharacter* Cha;
			FActionParam Par;
			TArray<FString> DesignerParam;

			int Result;
		} FuncParam;
		FuncParam.Cha = this->OwnerCharacter;
		FuncParam.Par = Param;
		FuncParam.DesignerParam = JsonFunc.Params;
		UFunction* Func = UCallFuncLib::JsonFuncToUFunc(JsonFunc);
		if(Func)
			this->ProcessEvent(Func, &FuncParam);
		Action->Anim.LastCheckerRes = FMath::Min(Action->Anim.AnimPath.Num() - 1, FuncParam.Result);
	}
	return Action->Anim.AnimPath[Action->Anim.LastCheckerRes];
}



bool UActionComponent::LearnAction(FActionInfo Action)
{
	FActionInfo* Act = GetActionById(Action.Id);
	if (!Act)
	{
		Actions.Add(Action);
		return true;
	}
	return false;
}

bool UActionComponent::IsHurtAction(FActionInfo ActionInfo)
{
	TArray<FString> HurtActionId ;
	HurtActionId.Add(this->GetPreorderActionId(ECharacterMontageState::Hurt));
	HurtActionId.Add(this->GetPreorderActionId(ECharacterMontageState::Blow));
	return HurtActionId.Contains(ActionInfo.Id);
}

bool UActionComponent::ChangeActionId(FString WasId, FString NewId)
{
	int WasIndex = -1;
	int NewIndex = -1;
	for (int i = 0; i < this->Actions.Num(); i++)
	{
		if (WasId == Actions[i].Id) WasIndex = i;
		if (NewId == Actions[i].Id) NewIndex = i;
	}
	if (WasIndex < 0) return false;	//没有东西可以被改成新的
	if (WasIndex == NewIndex) return false;
	if (NewIndex >= 0) Actions[NewIndex].Id = WasId;
	Actions[WasIndex].Id = NewId;
	return true;
}

bool UActionComponent::ModifyActionInfo(FString ActionOriginId, FActionInfoModifyApplication Modifer)
{
	for (int i = 0; i < this->Actions.Num(); i++)
	{
		if (Actions[i].OriginId == ActionOriginId)
		{
			bool AtLeastModified = false;
			if (Modifer.ModifyId == true)
			{
				AtLeastModified = ChangeActionId(Actions[i].Id, Modifer.IdModifyTo);
			}
			if (Modifer.ModifyTag == true)
			{
				if (Modifer.AddThisTag == false)
				{
					int n = 0;
					while (n < Actions[i].Tags.Num()){
						if (Actions[i].Tags[n].Tag == Modifer.CancelTagModifyTo.Tag)
						{
							Actions[i].Tags.RemoveAt(n);
							AtLeastModified = true;
						}else
						{
							n++;
						}
					}
				}else
				{
					bool HasOldOne = false;
					for (int n = 0; n < Actions[i].Tags.Num(); n++)
					{
						if (Actions[i].Tags[n].Tag == Modifer.CancelTagModifyTo.Tag)
						{
							Actions[i].Tags[n].FromSec = Modifer.CancelTagModifyTo.FromSec;
							HasOldOne = true;
							AtLeastModified = true;
							break;
						}
					}
					if (HasOldOne == false)
					{
						Actions[i].Tags.Add(Modifer.CancelTagModifyTo);
						AtLeastModified = true;
					}
				}
				
			}
			return AtLeastModified;
		}
	}
	return false;
}

void UActionComponent::ChangeMainAction( FActionSelection ActionSelection, FString SelectActionId, bool Save)
{
	if (ActionSelection.CandidateActionId.Contains(SelectActionId) == false) return;
	
	for (int i = 0; i < Actions.Num(); i++)
	{
		if (ActionSelection.CandidateActionId.Contains(Actions[i].OriginId) == true)
		{
			if (SelectActionId == Actions[i].OriginId)
			{
				if (Actions[i].Commands.Contains(ActionSelection.CmdAction) == false)
					Actions[i].Commands.Add(ActionSelection.CmdAction);
			}else
			{
				Actions[i].Commands.Remove(ActionSelection.CmdAction);
			}
		}
	}

	if (Save)
	{
		TArray<FString> RemoveRecordKeyList;
		for (TTuple<FString, FActionSelection> ChangeRecord : OwnerCharacter->CharacterObj.MainActionChangeRecord)
		{
			if(ChangeRecord.Value.CmdAction == ActionSelection.CmdAction)
				RemoveRecordKeyList.Add(ChangeRecord.Key);
		}
		for (FString RecordKey : RemoveRecordKeyList)
		{
			OwnerCharacter->CharacterObj.MainActionChangeRecord.Remove(RecordKey);
		}
		OwnerCharacter->CharacterObj.MainActionChangeRecord.Add(SelectActionId, ActionSelection);
	}
}

void UActionComponent::ChangeLinkedAction( FActionLink ActionLink, FString SelectedActionId, bool Save)
{
	if (ActionLink.LinkActionId.Contains(SelectedActionId) == false) return;
	if (ActionLink.Method_SetTag == false)
	{
		ChangeActionId(SelectedActionId, ActionLink.SetToActionId);
	}else
	{
		for (int i = 0; i < this->Actions.Num(); i++)
		{
			for (FString LinkActionId : ActionLink.LinkActionId)
			{
				if (this->Actions[i].OriginId == LinkActionId)
					this->Actions[i].Tags.Remove(ActionLink.LinkTag);
			}

			if (this->Actions[i].OriginId == SelectedActionId)
				this->Actions[i].Tags.Add(ActionLink.LinkTag);
		}
		
		// for (int i = 0; i < this->Actions.Num(); i++)
		// {
		// 	if (ActionLink.LinkActionId.Contains(Actions[i].OriginId))
		// 	{
		// 		if (Actions[i].OriginId == SelectedActionId)
		// 		{
		// 			FString KeyTag = ActionLink.LinkTag.Tag;
		// 			
		// 			Actions[i].Tags.RemoveAll([KeyTag](const FCancelTag& TheTag)
		// 			{
		// 				return TheTag.Tag == KeyTag;
		// 			});
		// 		}else
		// 		{
		// 			if (Actions[i].Tags.Contains(ActionLink.LinkTag) == false)
		// 			{
		// 				Actions[i].Tags.Add(ActionLink.LinkTag);
		// 			}
		// 		}
		// 	}
		// }
	}

	if (Save)
	{
		TArray<FString> RemoveRecordKeyList;
		for (TTuple<FString, FActionLink> ChangeRecord : OwnerCharacter->CharacterObj.LinkedActionChangeRecord)
		{
			if(ChangeRecord.Value.Method_SetTag == ActionLink.Method_SetTag)
			{
				if(ChangeRecord.Value.MainActionId == ActionLink.MainActionId)
					RemoveRecordKeyList.Add(ChangeRecord.Key);
			}
		}
		for (FString RecordKey : RemoveRecordKeyList)
		{
			OwnerCharacter->CharacterObj.LinkedActionChangeRecord.Remove(RecordKey);
		}
		OwnerCharacter->CharacterObj.LinkedActionChangeRecord.Add(SelectedActionId, ActionLink);
	}
}

FString UActionComponent::CurrentSelectedMainActionId(FActionSelection ActionSelection)
{
	for (const FActionInfo Act : Actions)
	{
		if (ActionSelection.CandidateActionId.Contains(Act.OriginId) && Act.Commands.Contains(ActionSelection.CmdAction))
		{
			return Act.OriginId;
		}
	}
	return FString();
}

FString UActionComponent::CurrentLinkedSelectActionId(FActionLink ActionLink)
{
	if (ActionLink.Method_SetTag == true)
	{
		for (FActionInfo Act : Actions)
		{
			for (FCancelTag Tag : Act.Tags)
				if(Tag.Tag == ActionLink.LinkTag.Tag)
					return Act.Id;
		}
	}else
	{
		for (FActionInfo Act : this->Actions)
		{
			if (Act.Id == ActionLink.SetToActionId)
				return Act.OriginId;
		}
	}
	return FString();
}

FRougeAbilityLevelInfo UActionComponent::GetCurrentActionAbilityLevelInfo() const
{
	FRougeAbilityLevelInfo DefaultInfo;

	// 获取当前动作
	FActionInfo* CurrentAction = const_cast<UActionComponent*>(this)->CurrAction();
	if (!CurrentAction || CurrentAction->OriginId.IsEmpty())
	{
		return DefaultInfo;
	}

	// 获取角色信息
	if (!OwnerCharacter)
	{
		return DefaultInfo;
	}

	const FString PawnClassId = OwnerCharacter->CharacterObj.ClassId;
	if (PawnClassId.IsEmpty())
	{
		return DefaultInfo;
	}

	// 获取数据管理器和肉鸽数据系统
	UAwDataManager* DataManager = UGameplayFuncLib::GetDataManager();
	UAwRogueDataSystem* RogueDataSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	if (!DataManager || !RogueDataSystem)
	{
		return DefaultInfo;
	}

	// 通过当前动作的OriginId查找对应的技能信息
	// 遍历所有技能槽位，找到包含当前ActionId的技能
	TArray<ERogueAbilitySlot> AllSlots = {
		ERogueAbilitySlot::NormalAttack,
		ERogueAbilitySlot::Ground1,
		ERogueAbilitySlot::Ground2,
		ERogueAbilitySlot::Air1,
		ERogueAbilitySlot::Air2
	};

	for (ERogueAbilitySlot Slot : AllSlots)
	{
		// 获取当前槽位的技能信息
		FRougeAbilityLevelInfo AbilityLevelInfo = RogueDataSystem->GetCurAbilityLevelInfo(Slot, PawnClassId);
		if (AbilityLevelInfo.AbilityInfoId.IsEmpty())
		{
			continue;
		}

		// 获取技能详细信息
		FRogueBattleAbilityInfo AbilityInfo = DataManager->GetRogueBattleAbilityInfo(PawnClassId, AbilityLevelInfo.AbilityInfoId);

		// 检查当前动作是否属于这个技能
		if (AbilityInfo.ActionIds.Contains(CurrentAction->OriginId))
		{
			return AbilityLevelInfo;
		}
	}

	return DefaultInfo;
}