// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "AnimPicker.h"
#include "TheAwakener_FO/FunctionLibrary/DataFuncLib.h"
#include "TheAwakener_FO/GamePlay/Buff/TableAddBuffInfo.h"
#include "TheAwakener_FO/GamePlay/Characters/Battle/BattleStructs.h"
#include "TheAwakener_FO/GamePlay/FX/FXPlayInfo.h"
#include "ActionInfo.generated.h"



/**
 * 动作信息
 */
USTRUCT(BlueprintType)
struct FCancelTag
{
	GENERATED_BODY()
public:
	//Cancel Tag的Tag
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString Tag = FString();

	//Cancel 成功从第几秒开始播放动画
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float FromSec = 0.f;

	bool operator ==(const FCancelTag& Other) const
	{
		return this->Tag == Other.Tag;
	}
};

/**
 * 在特殊状态的使用权限
 */
UENUM(BlueprintType)
enum class EUsableInState: uint8 
{
	//完全不行
	Not,
	//仅仅处于状态时可以
	Only,
	//无所谓是否在这个状态
	Any
};

UENUM(BlueprintType)
enum class EActionType :uint8
{
	//只执行一次
	Instant,
	//持续一段时间
	HasDuration,
	//按间隔循环
	Loop
};

USTRUCT(BlueprintType)
struct FAiActionDetail
{
	GENERATED_BODY()
public:
	//细节信息对应的AI动作ID
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		FString AiActionId;

	//设置动作持续时间的逻辑函数
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		FJsonFuncData ActionDurationPolicy;
	//动作类型 instant为只执行一次 HasDuration为持续一段时间 Loop为间隔循环 若对应的setlogic存在 则为其函数结果
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		EActionType ActionType = EActionType::Instant;
	//仅持续动作类型和循环动作类型生效 持续类型时为持续时间 循环类型为间隔时间 则为其函数结果
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		float Duration = 0;

	static FAiActionDetail FromJson(TSharedPtr<FJsonObject> JsonObj)
	{
		FAiActionDetail Result;
		Result.AiActionId = UDataFuncLib::AwGetStringField(JsonObj,"AiActionId");
		Result.ActionType = UDataFuncLib::AwGetEnumField(JsonObj, "ActionType", EActionType::Instant);
		Result.Duration = UDataFuncLib::AwGetNumberField(JsonObj, "Duration", 0);
		Result.ActionDurationPolicy = UDataFuncLib::SplitFuncNameAndParams(UDataFuncLib::AwGetStringField(JsonObj, "ActionDurationPolicy"));
		return Result;
	}
};

USTRUCT(BlueprintType)
struct FActionCost
{
	GENERATED_BODY()

public:
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int HP = 0;

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int MP = 0;

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int SP = 0;

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int AP = 0;

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int AirDodgePoint = 0;
	
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString ReplaceCmd = "";

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString ReplaceAction = "";
	
	static FActionCost FromJson(TSharedPtr<FJsonObject> JsonObj)
	{
		FActionCost Res;
		Res.HP = UDataFuncLib::AwGetNumberField(JsonObj, "HP", 0);
		Res.MP = UDataFuncLib::AwGetNumberField(JsonObj, "MP", 0);
		Res.SP = UDataFuncLib::AwGetNumberField(JsonObj, "SP", 0);
		Res.AP = UDataFuncLib::AwGetNumberField(JsonObj, "AP", 0);
		Res.AirDodgePoint = UDataFuncLib::AwGetNumberField(JsonObj, "AirDodgePoint", 0);
		Res.ReplaceCmd = UDataFuncLib::AwGetStringField(JsonObj, "ReplaceCmd", "");
		Res.ReplaceAction = UDataFuncLib::AwGetStringField(JsonObj, "ReplaceAction", "");
		return Res;
	}
};

/**
 * 动作信息，一个技能也是一个动作
 */
USTRUCT(BlueprintType)
struct FActionInfo
{
	GENERATED_BODY()

public:
	//操作指令，同时按下才可能激活
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FString> Commands;

	//技能的id
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString Id = "";
	//从表里都出来的时候的id，在一些情况下会用到，所以这个值应该只读不写（至少大多情况下如此）
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString OriginId = "";

	//我这个动作本身的CancelTags
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FCancelTag> Tags;

	//可被Cancel信息
	TMap<int, TArray<FString>> CanBeCancelledTag;
		
	// 优先级, 值越大最后越可能做这个动作
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int Priority = 0;

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	bool IsJustAttackAction = false;
	
	// 这个动作在处于Falling的时候能否做，这是一个非常特殊的处理，因为我们还有状态机
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	EUsableInState CanUseOnFalling = EUsableInState::Not;

	// 是否在第一帧就无敌（关闭所有受击框）
	// TODO:这是一个临时做法，为了临时解决受击的时候顿帧（freezeFrame），动画暂停播放了，无敌帧的NotifyState 的Begin和End都进不去
	// 所以这个目前只能用在有拉无敌帧 NotifyState 的 动作里面
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	bool IsInvincibleOnFirstFrame = false;
	
	// 使用的动画资源路径。是否是Montage取决于他被用在哪儿，如果是State的就是BlendSpace，否则就是Montage
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FAnimPicker Anim;

	// 做这个 Action 的时候是否会停止冲刺
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	bool CanStopSprint = true;
	
	// 释放动作需要的花费
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FActionCost Cost = FActionCost();

	//每个碰撞点的伤害制造器，<点id，伤害倍率（原来的动作值）>
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TMap<int, FWeaponAffectAction> Damage;

	//每个Buff点上的添加Buff的信息，<点id, 添加Buff信息>
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TMap<int, FTableAddBuffInfo> BuffPoints;

	//每个AoE点上创建AoE的信息，<点id, 创建aoe信息>
	//TODO

	//每个Bullet点创建Bullet信息，<点id，创建bullet的信息>
	//TODO

	//每个FX点上播放FX的信息，<点id, FX>
	TMap<int, FFXPlayInfo> FXPlayInfo;
	
		
	static FActionInfo FromJson(TSharedPtr<FJsonObject> JsonObj)
	{
		FActionInfo Model;
		Model.Id = UDataFuncLib::AwGetStringField(JsonObj, "Id");
		Model.OriginId = Model.Id;
		Model.Commands = UDataFuncLib::JsonValueArrayToFStringArray(UDataFuncLib::AwGetArrayField(JsonObj, "Cmds"));
		TArray<TSharedPtr<FJsonValue>> TagInfo = UDataFuncLib::AwGetArrayField(JsonObj, "Tags");
		for (const TSharedPtr<FJsonValue> TInfo : TagInfo)
		{
			FCancelTag CTag = FCancelTag();
			CTag.Tag = UDataFuncLib::AwGetStringField(TInfo->AsObject(), "Tag");
			CTag.FromSec = UDataFuncLib::AwGetNumberField(TInfo->AsObject(), "From", 0.000f);
			Model.Tags.Add(CTag);
		}

		int BCIndex = 0;
		while (JsonObj->HasField("BeCancelledTags") && JsonObj->GetObjectField("BeCancelledTags")->HasField(FString::FromInt(BCIndex)))
		{
			TArray<FString> Keys ;
			for (const TSharedPtr<FJsonValue, ESPMode::ThreadSafe> TagStr :
				UDataFuncLib::AwGetArrayField(JsonObj->GetObjectField("BeCancelledTags"), FString::FromInt(BCIndex)))
			{
				Keys.Add(TagStr->AsString());
			}
			Model.CanBeCancelledTag.Add(BCIndex, Keys);		
			BCIndex ++;
		}
		Model.Priority = UDataFuncLib::AwGetNumberField(JsonObj, "Priority", 0);
		Model.IsJustAttackAction = UDataFuncLib::AwGetBoolField(JsonObj, "IsJustAttack", false);
		
		const bool CanUseOnFall = UDataFuncLib::AwGetBoolField(JsonObj, "CanUseOnFalling", true);
		const bool CanOnlyUseOnFall = UDataFuncLib::AwGetBoolField(JsonObj, "CanOnlyUseOnFalling", false);
		if (CanOnlyUseOnFall == true)
		{
			Model.CanUseOnFalling = EUsableInState::Only;
		}else if (CanUseOnFall == true)
		{
			Model.CanUseOnFalling = EUsableInState::Any;
		}else
		{
			Model.CanUseOnFalling = EUsableInState::Not;
		}
		
		Model.IsInvincibleOnFirstFrame = UDataFuncLib::AwGetBoolField(JsonObj,"IsInvincibleOnFirstFrame",false);

		if (JsonObj->HasField("Anim"))
			Model.Anim = FAnimPicker::FromJson(JsonObj->GetObjectField("Anim"));

		Model.CanStopSprint = UDataFuncLib::AwGetBoolField(JsonObj, "CanStopSprint", true);
		
		if (JsonObj->HasField("Cost"))
			Model.Cost = FActionCost::FromJson(JsonObj->GetObjectField("Cost"));
		
		// if (JsonObj->HasField("Damage"))
		// {
		// 	int DIndex = 0;
		// 	while (JsonObj->GetObjectField("Damage")->HasField(FString::FromInt(DIndex)))
		// 	{
		// 		Model.Damage.Add(DIndex, FWeaponAffectAction::FromJson(JsonObj->GetObjectField("Damage")->GetObjectField(FString::FromInt(DIndex))));		
		// 		DIndex ++;
		// 	}
		// }
		if (JsonObj->HasField("AddBuff"))
		{
			int AIndex = 0;
			while (JsonObj->GetObjectField("AddBuff")->HasField(FString::FromInt(AIndex)))
			{
				Model.BuffPoints.Add(AIndex, FTableAddBuffInfo::FromJson(JsonObj->GetObjectField("AddBuff")->GetObjectField(FString::FromInt(AIndex))));
				AIndex ++;
			}
		}
		
		//TODO AoeCreator
		//TODO BulletFirer
		
		if (JsonObj->HasField("FXInfo"))
		{
			int FIndex = 0;
			const TSharedPtr<FJsonObject> FXInfoJsonObj = JsonObj->GetObjectField("FXInfo");
			while (FXInfoJsonObj->HasField(FString::FromInt(FIndex)))
			{
				Model.FXPlayInfo.Add(FIndex, FFXPlayInfo(
					UDataFuncLib::AwGetStringField(FXInfoJsonObj->GetObjectField(FString::FromInt(FIndex)), "VFX"),
					UDataFuncLib::AwGetStringField(FXInfoJsonObj->GetObjectField(FString::FromInt(FIndex)), "SFX")
				));
				FIndex ++;
			}
		}
		
		return Model;
	};

	/**
	 *	根据TagId获取到对应的Tag下的FromSec
	 */
	float GetFromSecByTagId(FString TagId);

	bool CanBeCancelledBy(FActionInfo* OtherAction);

	bool operator ==(const FActionInfo& Other) const;
};


/**
 * 请求改变ActionInfo的值
 */
USTRUCT(BlueprintType)
struct FActionInfoModifyApplication
{
	GENERATED_BODY()
public:
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	bool ModifyId = false;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString IdModifyTo = "";

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	bool ModifyTag = false;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FCancelTag CancelTagModifyTo = FCancelTag();
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	bool AddThisTag = true;	//是添加还是删除，如果是false就是删除CancelTagModifyTo.Tag对应的那个

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	bool ModifyCmd = false;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FString> ToAddCmd;	//要添加的Cmd
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FString> ToRemoveCmd;	//要删除的Cmd
	
	FActionInfoModifyApplication(){};
	static FActionInfoModifyApplication ModifyIdTo(FString ToId);
	static FActionInfoModifyApplication ModifyTagTo(FCancelTag ToTag, bool ToAdd = true);
	static FActionInfoModifyApplication ModifyActionCmd(TArray<FString> AddCmd, TArray<FString> RemoveCmd);
};
