// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "CharacterActionState.generated.h"

/**
 *角色状态机，用于Blend space层的状态
 */
UENUM(BlueprintType)
enum class ECharacterActionState: uint8
{
	//地面动画
	Ground,

	//下落中
	Falling,

	//飞行中
	Flying,

	//攀附、骑乘中
	Attached
};

/**
 * 一些预设的Montage Action，这些预设和“状态”其实是差不多的
 * 属于基本必定有的，规则级别的Action
 */
UENUM(BlueprintType)
enum class ECharacterMontageState: uint8
{
	//受伤
	Hurt,

	//吹飞
	Blow,

	//弹刀
	Bounced,

	//死亡
	Dead,

	//落地
	Landing,

	//濒死状态
	SecondWind,

	//倒地起身
	GetUp,

	//冻结
	Frozen,
	
};
