// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "ActionInfo.h"
#include "ActionParam.h"
#include "ActionPlanInfo.generated.h"


/**
 * 计划要播放的Action的数据
 */
USTRUCT()
struct FActionPlanInfo
{
	GENERATED_BODY()
public:
	//要切换到的动作的信息
	UPROPERTY()
	FActionInfo ActionInfo;

	//从什么时间点开始播放
	UPROPERTY()
	float StartFrom = 0;

	//开始的时候会卡帧多久
	UPROPERTY()
	float FreezeTime = 0;

	//自然播放，如果是自然播放的（这个是True）那么ScheduleTime就无效了。
	UPROPERTY()
	bool DefaultPlay = true;

	//播放的计划总时间（因为受伤等，需要这个时间来调整loop）
	UPROPERTY()
	float ScheduleTime = 0;

	//优先级，这是筛选下一个动作的时候用的，越大的越优先
	UPROPERTY()
	int Priority = 0;

	//移动计划，在FreezeTime结束后会执行的移动，如果FreezeTime是0，就直接执行了
	UPROPERTY()
	FForceMoveInfo MoveAfterFrozen = FForceMoveInfo();

	//切换动作的一些其他参数
	//TODO:之后看看能不能干掉
	UPROPERTY()
	FActionParam Param = FActionParam();
	
	FActionPlanInfo(){};
	FActionPlanInfo(FActionInfo Action, FForceMoveInfo MoveLater, float PlayFrom = 0, float Freeze = 0, bool OriginTimeLen = true, float HitStun = 0):
		ActionInfo(Action), StartFrom(PlayFrom), FreezeTime(Freeze), DefaultPlay(OriginTimeLen), ScheduleTime(HitStun), Priority(Action.Priority), MoveAfterFrozen(MoveLater){};
	
};
