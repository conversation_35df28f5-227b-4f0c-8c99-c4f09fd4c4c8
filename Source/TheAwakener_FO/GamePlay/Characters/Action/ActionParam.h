// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "ActionParam.generated.h"

USTRUCT(BlueprintType)
struct FActionParam
{
	GENERATED_BODY()
public:
	UPROPERTY(BlueprintReadWrite)
	bool Active = false;

	//伤害源的角度
	UPROPERTY(BlueprintReadWrite)
	float Degree = 0;

	//Priority差，是来自ActionChangeInfo中同一方的2个要求动作对比比出来的
	UPROPERTY(BlueprintReadWrite)
	int PriorityDistance = 0;
	
	FActionParam():Active(false),Degree(0),PriorityDistance(0){};
	
	
};
