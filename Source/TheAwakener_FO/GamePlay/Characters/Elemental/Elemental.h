// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/Object.h"
#include "Elemental.generated.h"

/**
 * 角色的元素属性
 */
UENUM(BlueprintType)
enum class EChaElemental : uint8
{
	Physical, //物理
	//以上为物理,以下为元素 
	Fire, //火
	Wind, //风
	Earth, //地
	Water, //水
	Thunder, //雷
	Ice, //冰
	Light, //光
	Darkness, //暗
};

/**
 * 角色属性值获取规则
 */
UENUM(BlueprintType)
enum class EElementalPickMethod : uint8
{
	Set,		//按照设定的值
	Element,	//向下一级（元素→武器→角色）找属性，直到最后找到角色属性
	Physical,		//如果是物理则往下一级找，找到角色那层就保持物理了
};


/**
 * 角色的属性相克管理器
 */
UCLASS(BlueprintType)
class THEAWAKENER_FO_API UElemental : public UObject
{
	GENERATED_BODY()
public:
	
	
	/**
	 * 获得属性相克提升伤害倍数
	 * @param  Attacker 攻击方
	 * @param Defender 挨揍方
	 * @return 倍率
	 */
	static float ElementalRestriction(EChaElemental Attacker, EChaElemental Defender)
	{
		constexpr float NoEffect = 1.0f;
		constexpr float Weak = 1.1f;
		constexpr float Normal = 1.2f;
		constexpr float Strong = 1.3f;
		constexpr float Restrict = 1.4f;
		
		switch (Attacker)
		{
			//普通啥也不克制
		case EChaElemental::Physical:return Normal;	
			//火焰
		case EChaElemental::Fire:
			switch (Defender)
			{
				case EChaElemental::Fire:return Weak;
				case EChaElemental::Water: return Weak;
				case EChaElemental::Ice:return Restrict;
				case EChaElemental::Darkness: return Strong;
				default:return Normal;
			}break;
			//风
		case EChaElemental::Wind:
			switch (Defender)
			{
				case EChaElemental::Thunder:return Weak;
				default:return Normal;
			}break;
		
		case EChaElemental::Earth:
			switch (Defender)
			{
				case EChaElemental::Fire:return Restrict;
				case EChaElemental::Wind: return NoEffect;
				case EChaElemental::Water: return Normal;
				case EChaElemental::Thunder:return Restrict;
				default:return Normal;
			}break;
		case EChaElemental::Water:
			switch (Defender)
			{
				case EChaElemental::Fire:return Restrict;
				case EChaElemental::Earth: return Restrict;
				default:return Normal;
			}break;
		case EChaElemental::Thunder:
			switch (Defender)
			{
				case EChaElemental::Physical:return Normal;
				case EChaElemental::Fire:return Normal;
				case EChaElemental::Wind: return Normal;
				case EChaElemental::Earth: return Normal;
				case EChaElemental::Water: return Normal;
				case EChaElemental::Thunder:return Normal;
				case EChaElemental::Ice:return Normal;
				case EChaElemental::Light:return Normal;
				case EChaElemental::Darkness: return Normal;
				default:return Normal;
			}break;
		case EChaElemental::Ice:
			switch (Defender)
			{
				case EChaElemental::Physical:return Normal;
				case EChaElemental::Fire:return Normal;
				case EChaElemental::Wind: return Normal;
				case EChaElemental::Earth: return Normal;
				case EChaElemental::Water: return Normal;
				case EChaElemental::Thunder:return Normal;
				case EChaElemental::Ice:return Normal;
				case EChaElemental::Light:return Normal;
				case EChaElemental::Darkness: return Normal;
				default:return Normal;
			}break;
		case EChaElemental::Light:
			switch (Defender)
			{
				case EChaElemental::Physical:return Normal;
				case EChaElemental::Fire:return Normal;
				case EChaElemental::Wind: return Normal;
				case EChaElemental::Earth: return Normal;
				case EChaElemental::Water: return Normal;
				case EChaElemental::Thunder:return Normal;
				case EChaElemental::Ice:return Normal;
				case EChaElemental::Light:return Normal;
				case EChaElemental::Darkness: return Normal;
				default:return Normal;
			}break;
		case EChaElemental::Darkness:
			switch (Defender)
			{
				case EChaElemental::Physical:return Normal;
				case EChaElemental::Fire:return Normal;
				case EChaElemental::Wind: return Normal;
				case EChaElemental::Earth: return Normal;
				case EChaElemental::Water: return Normal;
				case EChaElemental::Thunder:return Normal;
				case EChaElemental::Ice:return Normal;
				case EChaElemental::Light:return Normal;
				case EChaElemental::Darkness: return Normal;
				default:return Normal;
			}break;
		default:return Normal;
		}
	}
};
