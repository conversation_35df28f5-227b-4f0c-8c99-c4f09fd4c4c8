#pragma once

#include "CoreMinimal.h"
//#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "CharacterAttachLink.generated.h"

class AAwCharacter;	//Cheat IDE
class UAttachPoint;	//Cheat IDE

/**
 *两个角色之间互抓的信息
 */
USTRUCT(BlueprintType)
struct FCharacterLink
{
	GENERATED_BODY()

public:
	AAwCharacter* Catcher; //抓人的家伙
	AAwCharacter* Target;	//被抓的家伙
	UAttachPoint* CatcherPoint;	//抓人的点
	UAttachPoint* BeCaughtPoint;	//被抓的点

	/**
	 *这条信息是否还有意义
	 */
	bool AttachmentValid()const
	{
		return Catcher != nullptr && Target != nullptr && CatcherPoint != nullptr && BeCaughtPoint != nullptr;
	}
};

/**
 *角色抓取和被抓取的情况
 */
USTRUCT(BlueprintType)
struct FCharacterAttachment
{
	GENERATED_BODY()

public:
	//我被谁的某个Catch抓点抓了，这样我改变动作的时候可能把它甩下来<我的点，抓我的点>
	TArray<FCharacterLink> AttachOnMe;
	/**
	 *抓在哪个点上，抓在点上之后，自己就不能移动了，就得跟着点动了
	 *如果要在攀附点切换，那是因为某个攀附点之间跳跃的动作本身cmd就是移动
	 */
	UPROPERTY(BlueprintReadOnly)
	UAttachPoint* AttachTarget = nullptr;

	//我的哪个点去抓住别人，比如菊花，就是骑坐骑的时候抓住别人的点
	UPROPERTY()
	UAttachPoint* MyPointCatchesTarget = nullptr;

	//是否MyPoint已经抓到了AttachTarget，特指：已经经过了移动过程了，彻底贴合了
	UPROPERTY()
	bool HasTouched = false;	

	//某个点已经被抓了
	bool AttachPointHasBeenAttached(UAttachPoint* CheckPoint) const;

	//添加一个被Attach的点，返回是否添加成功
	bool AddAttachLink(UAttachPoint* CatcherPoint, UAttachPoint* MyPoint);

	//检查一次所有的抓点（AttachOnMe）看看哪些不合理了就干死
	void CheckAllAttachOnMe();

	//是否还能被Attach，TODO：已经被Attach的就不能被Attach了
	bool CanBeAttached(bool CheckAboveAll = false)
	{
		if (CheckAboveAll == true) CheckAllAttachOnMe();
		return this->AttachOnMe.Num() <= 0;
	}
	
	//删除角色身上所有的Attach的角色，并返回那些AwCharacter
	TArray<AAwCharacter*> RemoveAllAttacherOnMe();

	//删除角色身上某个部位（ChaPart）对应的所有角色，并返回
	TArray<AAwCharacter*> RemoveCharacterOnPart(FString PartId);

	TArray<AAwCharacter*> GetCharacterAttachingOnPart(FString PartId);

};