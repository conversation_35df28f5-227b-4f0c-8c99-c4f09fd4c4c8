// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/Object.h"
#include "TheAwakener_FO/GamePlay/Characters/ControlState/ControlState.h"
#include "AttachPoint.generated.h"

/**
 * 角色身上的抓点，有抓别人的，也有被别人抓的
 */
UCLASS(ClassGroup="Collision", editinlinenew, hidecategories=(Object,LOD,Lighting,TextureStreaming), meta=(DisplayName="Attach Point", BlueprintSpawnableComponent))
class THEAWAKENER_FO_API UAttachPoint : public USceneComponent{
	GENERATED_BODY()

public:
	//抓点的名称，当我们需要从多个抓点里面找到一个的时候，他就有意义了
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString Id;

	//他是否是一个抓住别人的抓点，比如角色的菊花就是一个，因为要骑在坐骑上
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool Catcher = false;

	//他是否是一个可以被别人抓住的抓点，比如坐骑的鞍座就是一个，要被人骑的嘛
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool Seat = true;

	//【仅当是Seat才会有效】抓住这个点的人，会做一个什么id对应的动作
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString CatcherActionId;

	//【仅当是Seat才会有效】抓住这个点的人的ControlState的变化
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FControlState CatcherControlState;

	//【仅当是Seat才会有效】抓住这个点的人，是否获得被抓者的操控权（坐骑应该是True）
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool CatcherTakeControl = true;
};
