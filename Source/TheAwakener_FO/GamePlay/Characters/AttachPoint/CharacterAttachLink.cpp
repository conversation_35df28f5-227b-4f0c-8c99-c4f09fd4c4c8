// Fill out your copyright notice in the Description page of Project Settings.


#include "CharacterAttachLink.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"

bool FCharacterAttachment::AttachPointHasBeenAttached(UAttachPoint* CheckPoint) const
{
	for (const FCharacterLink AttachMe : this->AttachOnMe)
	{
		if (AttachMe.BeCaughtPoint == CheckPoint)
		{
			return true;
		}
	}
	return false;
}

bool FCharacterAttachment::AddAttachLink(UAttachPoint* CatcherPoint, UAttachPoint* MyPoint)
{
	if (AttachPointHasBeenAttached(MyPoint)) return false;
	FCharacterLink ChaLink = FCharacterLink();
	ChaLink.Catcher = Cast<AAwCharacter>(CatcherPoint->GetOwner());
	ChaLink.Target = Cast<AAwCharacter>(MyPoint->GetOwner());
	ChaLink.CatcherPoint = CatcherPoint;
	ChaLink.BeCaughtPoint = MyPoint;
	if (ChaLink.AttachmentValid())
	{
		this->AttachOnMe.Add(ChaLink);
		return true;
	}
	return false;
}

void FCharacterAttachment::CheckAllAttachOnMe()
{
	int CIndex = 0;
	while (CIndex < AttachOnMe.Num())
	{
		if (AttachOnMe[CIndex].AttachmentValid() == false)
		{
			AttachOnMe.RemoveAt(CIndex);
		}else
		{
			CIndex ++;
		}
	}
}

TArray<AAwCharacter*> FCharacterAttachment::RemoveAllAttacherOnMe()
{
	TArray<AAwCharacter*> Res;
	while (AttachOnMe.Num())
	{
		if (AttachOnMe[0].AttachmentValid() == true)
		{
			Res.Add(AttachOnMe[0].Catcher);
		}
		AttachOnMe.RemoveAt(0);
	}

	return Res;
}

TArray<AAwCharacter*> FCharacterAttachment::GetCharacterAttachingOnPart(FString PartId)
{
	TArray<AAwCharacter*> Res;
	if (AttachOnMe.Num() <= 0) return Res;
	int CIndex = 0;
	const TArray<UAttachPoint*> RemoveGuyPoints = AttachOnMe[0].Target->GetAttachPointsByChaPartId(PartId);
	while (CIndex < AttachOnMe.Num())
	{
		if (AttachOnMe[CIndex].AttachmentValid() == false)
		{
			AttachOnMe.RemoveAt(CIndex);
		}else
		{
			Res.Add(AttachOnMe[CIndex].Catcher);
			CIndex ++;
		}
	}
	return Res;
}

TArray<AAwCharacter*> FCharacterAttachment::RemoveCharacterOnPart(FString PartId)
{
	TArray<AAwCharacter*> Res;
	if (AttachOnMe.Num() <= 0) return Res;
	int CIndex = 0;
	const TArray<UAttachPoint*> RemoveGuyPoints = AttachOnMe[0].Target->GetAttachPointsByChaPartId(PartId);
	while (CIndex < AttachOnMe.Num())
	{
		if (AttachOnMe[CIndex].AttachmentValid() == false)
		{
			AttachOnMe.RemoveAt(CIndex);
		}else if (RemoveGuyPoints.Contains(AttachOnMe[CIndex].BeCaughtPoint))
		{
			Res.Add(AttachOnMe[CIndex].Catcher);
			AttachOnMe.RemoveAt(CIndex);
		}else
		{
			CIndex ++;
		}
	}
	return Res;
}
