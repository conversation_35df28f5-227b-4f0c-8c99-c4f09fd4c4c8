// Fill out your copyright notice in the Description page of Project Settings.


#include "PathNodeQueue.h"

#include "TheAwakener_FO/FunctionLibrary/DataFuncLib.h"

FPathNodeQueueInfo FPathNodeQueueInfo::FromJson(TSharedPtr<FJsonObject> JsonObj)
{
	return FPathNodeQueueInfo(
		UDataFuncLib::AwGetStringField(JsonObj, "Id"),
		UDataFuncLib::AwGetStringArrayField(JsonObj, "Nodes"),
		UDataFuncLib::AwGetBoolField(JsonObj, "Loop", true)
	);
}

int FPathNodeQueue::GetNearestNodeIndex(FVector CurLocation)
{
	int BestIndex = -1;
	float BestDis = 9999999;
	for (int i =0 ; i < this->Nodes.Num(); i++)
	{
		const float CurDis = FMath::Abs((Nodes[i] - CurLocation).Size());
		if (CurDis < BestDis)
		{
			BestDis = CurDis;
			BestIndex = i;
		}
	}
	return BestIndex;
}

int FPathNodeQueue::NextNode()
{
	return SetNodeToIndex(NodeIndex + 1);
}

int FPathNodeQueue::SetNodeToIndex(int ToIndex)
{
	this->NodeIndex = ToIndex;
	if (Loop == true)
	{
		while (NodeIndex >= Nodes.Num())
		{
			NodeIndex -= Nodes.Num();
		
		}
	}else
	{
		if (NodeIndex >= Nodes.Num())
			NodeIndex = -1;
	}
	return NodeIndex;
}

