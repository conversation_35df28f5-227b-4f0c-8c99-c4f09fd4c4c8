// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "Misc/App.h"
#include "Runtime/CoreUObject/Public/UObject/UObjectGlobals.h"
#include "CoreMinimal.h"
#include "GameFramework/CharacterMovementComponent.h"
#include "ClimbCharacterMovementComponent.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UClimbCharacterMovementComponent : public UCharacterMovementComponent
{
	GENERATED_BODY()

public:
	UClimbCharacterMovementComponent();

	UFUNCTION(BlueprintCallable)
	void GetRestorePreAdditiveRootMotionVelocity() { return UCharacterMovementComponent::RestorePreAdditiveRootMotionVelocity(); }

	UFUNCTION(BlueprintCallable)
	void GetApplyRootMotionToVelocity(float deltaTime) { return UCharacterMovementComponent::ApplyRootMotionToVelocity(deltaTime); }

};
