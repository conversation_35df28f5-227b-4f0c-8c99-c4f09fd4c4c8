// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "ExtendMoveInfo.generated.h"

/**
 * 在某些动作的某些片段中，可以Loop某个片段达到延长动作效果，直到这个动作达到时间、距离或者捕获敌人的效果
 * 设计时仅针对AI设计，因此请尽量避免用于玩家动作
 * 而且从设计上来说，玩家有这样的需求也应该是按住按钮持续往前，然后松手结束才对
 */
USTRUCT()
struct FExtendMoveInfo
{
	GENERATED_BODY()
public:
	//还能Loop多少厘米
	UPROPERTY()
	float Distance = 0;	

	//还能Loop多少秒
	UPROPERTY()
	float Duration = 0;

	FExtendMoveInfo(){}
	FExtendMoveInfo(float LoopDis, float LoopTime):
		Distance(LoopDis), Duration(LoopTime){}
};
