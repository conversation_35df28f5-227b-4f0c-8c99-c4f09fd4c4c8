// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "MoveResult.generated.h"

/**
 * 每一帧移动的结果
 */
USTRUCT(BlueprintType)
struct FMoveResult
{
	GENERATED_BODY()
public:
	//最终移动的量
	UPROPERTY()
	FVector FinalMoved = FVector::ZeroVector;

	//是否碰到了墙壁
	UPROPERTY()
	bool HitWall = false;

	//是否碰到了天花板
	UPROPERTY()
	bool HitRoof = false;

	//是否碰到了地面
	UPROPERTY()
	bool HitGround = false;
};
