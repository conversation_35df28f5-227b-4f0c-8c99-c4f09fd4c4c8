// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "ActionForceRotateInfo.generated.h"

/**
 * 动作中允许发生一次立即转身的信息
 */
USTRUCT()
struct FActionForceRotateInfo
{
	GENERATED_BODY()
public:
	UPROPERTY()
	FString ActionId;

	UPROPERTY()
	uint8 Index = 0;

	UPROPERTY()
	float DegreeLimit = 0.f;

	static FActionForceRotateInfo Create(FString ActId, uint8 Idx, float DegreeMax)
	{
		FActionForceRotateInfo Res = FActionForceRotateInfo();
		Res.ActionId = ActId;
		Res.Index = Idx;
		Res.DegreeLimit = DegreeMax;
		return Res;
	}
};
