// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "FlyInfo.generated.h"

/**
 * 
 */
USTRUCT()
struct FFlyInfo
{
	GENERATED_BODY()
public:
	UPROPERTY()
	bool Flying = false;	//会飞的单位当然可以不飞
	
	UPROPERTY()
	float FavourHeight = 300.0f;	//（厘米）喜欢飞多高，仅可能会接近这个高度

	UPROPERTY()
	float BackToFavourSpeed = 80.0f;		//每秒向喜欢的高度靠拢的距离（厘米/秒）
};

