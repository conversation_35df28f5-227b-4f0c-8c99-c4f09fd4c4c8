// Fill out your copyright notice in the Description page of Project Settings.


#include "ClassObj.h"

int FBattleClassLevelAndExp::LevelingExp(int TargetLevel){
	constexpr int LevelCap = 10;
	if (TargetLevel > LevelCap) return -1;
	return FMath::CeilToInt(FMath::Pow(30+TargetLevel, TargetLevel / 10.00f + 2) / 100) * 100;	
}

int FBattleClassLevelAndExp::AddExp(int AddExpValue)
{
	int RequireExp = LevelingExp(this->Level + 1);
	this->Exp += AddExpValue;
	int LvUp = 0;
	while (RequireExp <= this->Exp && RequireExp > 0)
	{
		LvUp += 1;
		this->Level += 1;
		RequireExp = LevelingExp(this->Level + 1);
	}
	return LvUp;
}