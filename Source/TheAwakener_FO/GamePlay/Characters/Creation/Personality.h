// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/GamePlay/Buff/TableAddBuffInfo.h"
#include "UObject/Object.h"
#include "Personality.generated.h"

/**
 * 个人关系变化类型
 */
UENUM(BlueprintType)
enum class EPersonalRelationshipChangeType : uint8
{
	None,	//无变化
	LoseFriendly,	//从好友名单移除
	BecomeEnemy,	//成为仇敌
	ResolveGrievances,	//从仇敌列表移除
	BecomeFriends,	//成为好友
};

/**
 * 个人恩怨的记录
 * 这是仇恨向的，决定了是否会从Friend里面删除，甚至加入到痛恨里面
 * 但是这个跟友好度没关系，尽管他们都会改变相处关系
 */
USTRUCT(BlueprintType)
struct FPersonalGrudge
{
	GENERATED_BODY()
public:
	//对方的npcId，或者<Player>
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString NpcId;

	//对于对方的不满
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float Dissatisfaction = 0;

	//连续不满次数
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int ComboDissatisfy = 0;

	//在这个归0之前，仇恨速度会加快，但是归0之后，不满会逐渐下降
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float FeverDuration = 0;

	FPersonalGrudge(){};
	FPersonalGrudge(FString OnNpcId, float InitDissatisfaction, float FeverSec):
		NpcId(OnNpcId), Dissatisfaction(InitDissatisfaction), FeverDuration(FeverSec){};

	//每帧调一下，返回是否冷静了（FeverDuration <= 0)，冷静了就会逐渐减少仇恨
	bool Update(float DeltaTime);
};

/**
 * 角色的个人特性
 */
USTRUCT(BlueprintType)
struct FCharacterPersonality
{
	GENERATED_BODY()
public:
	//是否是种族主义者，如果是的话，会优先根据Camp来判断敌我，一般小怪都是，特殊NPC都不是
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	bool IsRacist = true;

	//对话的Id，如果是空，代表没有对话，也就没的交互了
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString DialogModelId;

	//在完成一次对话之后，自动挑选下一个对话的DialogModelId
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString DialogAutoPicker;

	//与我敌对的角色的专有Id，如果是非种族主义者，他会不顾Camp级友好关系攻击敌人
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FString> Enemy;

	//与我友好的角色专有Id，如果是种族主义者，他会不顾是否友好而攻击Camp级敌对敌人
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FString> Friend;

	//每次创建这个Npc的AwCharacter时，添加的buff
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FTableAddBuffInfo> DefaultBuffs;

	/**
	 * 容忍度，在角色受到伤害的时候，并不是立即就不爽的，这是由容忍度来决定的
	 * 每秒会降低这个值10%的仇恨度
	 * 每次受到欺负就会增加原本仇恨(记为n)的 n*n*Power(1.5,连续仇恨次数)/(Tolerance + n) 的仇恨度
	 * 仇恨值>=这个值的10倍会从友好列表移除掉，5倍会进入敌人列表
	 * Fever时间，则是10*n/(Tolerance + n)秒
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int Tolerance = 100;

	//不满的记录<对方NpcId, 仇恨信息>
	UPROPERTY(BlueprintReadOnly)
	TMap<FString, FPersonalGrudge> PersonalGrudges;

	void Update(float DeltaTime);

	/**
	 * 增加仇恨
	 * @param GuyNpcId 要憎恨的NpcId
	 * @param DissatisfyValue 通常来说憎恨度多少（会被Tolerance减免）
	 * @return 是否因此而从Friend移除或者增加到Enemy了
	 */
	bool AddDissatisfy(FString GuyNpcId, int DissatisfyValue);

	/**
	 * 友好容忍度，当不爽度(Dissatisfy)超过这个值的时候，会从好友列表(Friend)删除
	 */
	float FriendlyTolerance() const;

	/**
	 * 敌对容忍度，积累多少不爽度（Dissatisfy）超过这个值，就会被加入Enemy列表
	 */
	float HostileTolerance() const;
	
	/**
	 * 对某个NpcId的人的友好度百分比，如果本来就不是Friend，那就是0
	 * @param NpcId 检查目标的npcId
	 * @return 0-1=0%-100%
	 */
	float FriendlyPercentage(FString NpcId);

	/**
	 * 对某个NpcId的人敌意百分比，如果是Friend或者Enemy，则为0，达到100%就会进入敌对列表了
	 * @param NpcId 检查目标的NpcId
	 * @return 0-1=0%-100%
	 */
	float HostilePercentage(FString NpcId);
};
