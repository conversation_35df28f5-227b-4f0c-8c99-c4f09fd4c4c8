// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "MobModel.h"
#include "Personality.h"
#include "NpcInfo.generated.h"

/**
 * NPC的信息
 * NPC也是一种怪物，但是刷法和怪物有所不同，因为要给NPC加上这个NpcInfo。
 * 没有NpcInfo的角色，是无法交互的。
 */
USTRUCT(BlueprintType)
struct FNpcInfo
{
	GENERATED_BODY()
public:
	//一个专有的Id，在通过NPCId找人的时候，指向的是这个
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString Id = FString();

	//他是一个什么怪物模板（MobModel）
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString MobModelId = FString();
	
	//个性数据
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FCharacterPersonality Personality;

	//NPC使用的名称
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString ChaName;

	//是否被砍死了
	UPROPERTY(BlueprintReadOnly)
	bool Dead = false;

	//刷出来到地图上之后的等级（参考值，当然可以直接用）
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int InitLevel = 1;

	//刷成的Side
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int ToSide = 1;

	//战斗力是什么档次的
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	EMobRank ThisMobRank = EMobRank::Normal;
	
	//玩家的npcId，这是个漏洞，特殊处理，是的，不过你要是想给表里也配个这个Id的Npc，那后果自负
	static FString PlayerNpcId(){return "<Player>";}

	//是否是一个Npc
	bool IsNpc() const {return Id.IsEmpty() == false && MobModelId.IsEmpty() == false;}
};