// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/GamePlay/Buff/Buff.h"
#include "TheAwakener_FO/GamePlay/Characters/Action/ActionInfo.h"
#include "TheAwakener_FO/GamePlay/Characters/Action/ArmState.h"
#include "TheAwakener_FO/GamePlay/Characters/ChaPart/ChaPart.h"
#include "TheAwakener_FO/GamePlay/Characters/ChaProp/ChaProp.h"
#include "TheAwakener_FO/GamePlay/Characters/ControlState/ControlState.h"
#include "MobModel.generated.h"


UENUM(BlueprintType)
enum class EMobRank : uint8
{
	Normal,
	Elite,
	Boss
};

/**
 * 
 */
USTRUCT(BlueprintType)
struct FMobMoveProp
{
	GENERATED_BODY();
public:
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		int StopRangeWhenMoveFollow = 75.0f;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		int MinDisWhenMoveAround = 100.0f;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		int MaxDisWhenMoveAround = 200.0f;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		bool bNegativeDegreeWhenMoveAround = true;

	static FMobMoveProp FromJson(TSharedPtr<FJsonObject> JsonObj)
	{
		FMobMoveProp Model;
		Model.StopRangeWhenMoveFollow = UDataFuncLib::AwGetNumberField(JsonObj, "StopRangeWhenMoveFollow", 75.0f);
		Model.MinDisWhenMoveAround = UDataFuncLib::AwGetNumberField(JsonObj, "MinDisWhenMoveAround", 100.0f);
		Model.MaxDisWhenMoveAround = UDataFuncLib::AwGetNumberField(JsonObj, "MaxDisWhenMoveAround", 200.0f);
		Model.bNegativeDegreeWhenMoveAround = UDataFuncLib::AwGetBoolField(JsonObj, "bNegativeDegreeWhenMoveAround", true);
		return Model;
	}
};

USTRUCT(BlueprintType)
struct FMobPerceptionProp
{
	GENERATED_BODY();
public:
	//当前Character的可被看见范围
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		int ViewRadius = 500;
	//当前Character未进入战斗的视野范围
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		int RelaxSightRadius = 1500;
	//当前Character的视野范围
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		int SightRadius = 2000;
	//当前Character的视野范围的上下Z高度
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		int SightZRadius = 500;
	//当前Character的视野最大角度
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		int SightHalfAngleDregee = 45;
	//当前Character的丢失目标的视野范围
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		int LoseSightRadius = 3000;
	//当前Character的丢失目标的视野最大角度
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		int LoseSightHalfAngleDregee = 50;
	//当前Character的可被听见范围
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		int NoiseRadius = 500;
	//当前Character的听觉范围
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		int HearingRadius = 1000;

	static FMobPerceptionProp FromJson(TSharedPtr<FJsonObject> JsonObj)
	{
		FMobPerceptionProp Model = FMobPerceptionProp();
		Model.ViewRadius = UDataFuncLib::AwGetNumberField(JsonObj, "ViewRadius", 500);
		Model.RelaxSightRadius = UDataFuncLib::AwGetNumberField(JsonObj, "RelaxSightRadius", 1500);
		Model.SightRadius = UDataFuncLib::AwGetNumberField(JsonObj, "SightRadius", 2000);
		Model.SightZRadius = UDataFuncLib::AwGetNumberField(JsonObj, "SightZRadius", 500);
		Model.SightHalfAngleDregee = UDataFuncLib::AwGetNumberField(JsonObj, "SightHalfAngleDregee", 45);
		Model.LoseSightRadius = UDataFuncLib::AwGetNumberField(JsonObj, "LoseSightRadius", 3000);
		Model.LoseSightHalfAngleDregee = UDataFuncLib::AwGetNumberField(JsonObj, "LoseSightHalfAngleDregee", 50);
		Model.NoiseRadius = UDataFuncLib::AwGetNumberField(JsonObj, "NoiseRadius", 500);
		Model.HearingRadius = UDataFuncLib::AwGetNumberField(JsonObj, "HearingRadius", 1000);
		return Model;
	}
};

USTRUCT(BlueprintType)
struct FMobEquipmentInfo
{
	GENERATED_BODY();
public:
	//装备的id
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString EquipmentId;

	//装备的被刷率，1.00=100%
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float Rating = 0.5f;

	FMobEquipmentInfo():EquipmentId(FString()), Rating(0.5f){};
	FMobEquipmentInfo(FString Id, float Rate):EquipmentId(Id), Rating(Rate){};
};

//外部给AI发送的命令，会有若干个AIScript信息
USTRUCT(BlueprintType)
struct FAIOrderModel
{
	GENERATED_BODY()
public:
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString OrderId = "";
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool bReversedCheckTag = true;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString CheckTag = "";
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FString> AIScriptIdList;

	static FAIOrderModel FromJson(TSharedPtr<FJsonObject> JsonObj)
	{
		FAIOrderModel NewAIOrder = FAIOrderModel();
		NewAIOrder.OrderId = UDataFuncLib::AwGetStringField(JsonObj, "OrderId");
		NewAIOrder.bReversedCheckTag = UDataFuncLib::AwGetBoolField(JsonObj, "bReversedCheckTag");
		NewAIOrder.CheckTag = UDataFuncLib::AwGetStringField(JsonObj, "CheckTag");
		for (const TSharedPtr<FJsonValue> Value : UDataFuncLib::AwGetArrayField(JsonObj, "AIScriptIdList"))
			NewAIOrder.AIScriptIdList.Add(Value->AsString());

		return NewAIOrder;
	}
};

//Mob在死亡时对于地城阵营强度的影响
USTRUCT(BlueprintType)
struct FMobDungeonCampImpact
{
	GENERATED_BODY()
public:
	//影响的信息所属地城id
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString DungeonId;

	//影响的信息所属的CampId
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString CampId;

	//影响值
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int Value = 0;

	FMobDungeonCampImpact(){};
	FMobDungeonCampImpact(FString AffectDungeonId, FString AffectCampId, int ModifyValue):
		DungeonId(AffectDungeonId), CampId(AffectCampId), Value(ModifyValue){};
};

USTRUCT(BlueprintType)
struct FMobModel
{
	GENERATED_BODY();
public:
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString Id;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FString> Tag;
	
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString LootPackageId;
	
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FMobMoveProp MoveProp;

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FMobPerceptionProp PerceptionProp;
	//AIScript的Id
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FString> AIScript;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FAIOrderModel> AIOrder;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int StartFightingWillLevel = 1;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int MaxFightingWillLevel = 2;

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FChaProp BaseProp;

	//默认声音所在文件夹
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString SoundBaseDictionary;
	//叫声发出的骨骼
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString SoundSocket;

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FTableAddBuffInfo> InitBuff;	
	
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FChaPart> Part;

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FControlState ControlState;

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString BaseActionType;
	
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FActionInfo> Actions;

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FAiActionDetail> AiActionDetails;

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString ChaName;

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString PortraitPath;

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TMap<FString, FString> MoveAnims;
	
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString BpPath;

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	bool Flyable = false;

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	bool MountsTypeRotate = false;
	
	TMap<FString, TMap<EArmState, FString>> StateActions;

	TMap<FString, TMap<EArmState, FString>> PreorderActionKeys;

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FMobEquipmentInfo> InitEquipments;

	//初期要不显示的视觉部位，仅仅只是MeshComponent
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FString> HideSightPartsOnCreate;

	//初期要不激活的ChaPart，不是每个ChaPart都是开始时激活的
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FString> ChaPartsNotDefaultActive;

	//死亡时的地城阵营信息影响
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FMobDungeonCampImpact> DungeonCampImpacts;

	//角色死亡时候的回调，(AAwCharacter* 死的角色, TArray<FString> 参数)=>void
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FString> OnBeKilled;

	//玩家击杀这个怪，可以获得多少经验  TODO: 尚未设计
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int ExpGiven = 1;

	//Rogue:怪物默认MobRank
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	EMobRank MobRank;

	static FMobModel FromJson(TSharedPtr<FJsonObject> JsonObj);
};
