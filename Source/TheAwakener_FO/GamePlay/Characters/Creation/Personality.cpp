// Fill out your copyright notice in the Description page of Project Settings.


#include "Personality.h"

#include "Kismet/KismetSystemLibrary.h"

bool FPersonalGrudge::Update(float DeltaTime)
{
	if (this->FeverDuration > 0)
	{
		this->FeverDuration -= DeltaTime;
	}
	return FeverDuration <= 0;
}

void FCharacterPersonality::Update(float DeltaTime)
{
	TArray<FString> ToRemove;
	for (TTuple<FString, FPersonalGrudge> Per : this->PersonalGrudges)
	{
		if (PersonalGrudges[Per.Key].Update(DeltaTime))
		{
			PersonalGrudges[Per.Key].ComboDissatisfy = 0;
			if (PersonalGrudges[Per.Key].Dissatisfaction > 0)
			{
				PersonalGrudges[Per.Key].Dissatisfaction -= this->Tolerance * DeltaTime * 0.1f; 
			} else
			{
				ToRemove.Add(Per.Key);
			}
		}
	}
	for (const FString Rem : ToRemove)
	{
		PersonalGrudges.Remove(Rem);
	} 
}

bool FCharacterPersonality::AddDissatisfy(FString GuyNpcId, int DissatisfyValue)
{
	const float ModParam = DissatisfyValue * 1.000f / (DissatisfyValue + Tolerance);
	if (this->PersonalGrudges.Contains(GuyNpcId))
	{
		if (PersonalGrudges[GuyNpcId].FeverDuration > 0)
		{
			PersonalGrudges[GuyNpcId].ComboDissatisfy += 1;
			PersonalGrudges[GuyNpcId].FeverDuration = 10.000f * ModParam;
		}
		PersonalGrudges[GuyNpcId].Dissatisfaction += DissatisfyValue * FMath::Pow(1.5f, PersonalGrudges[GuyNpcId].ComboDissatisfy) * ModParam;
	}else
	{
		PersonalGrudges.Add(GuyNpcId, FPersonalGrudge(
			GuyNpcId, DissatisfyValue * ModParam, 10.000f * ModParam
		));
	}

	if (GuyNpcId == "<Player>")
	UKismetSystemLibrary::PrintString(GWorld, FString("Hate ").Append(GuyNpcId)
		.Append(" ::").Append(FString::SanitizeFloat(PersonalGrudges[GuyNpcId].Dissatisfaction).Append("||")
			.Append(FString::FromInt(DissatisfyValue)).Append("-->")
			.Append(FString::SanitizeFloat(ModParam)).Append(">>").Append(FString::SanitizeFloat(DissatisfyValue * FMath::Pow(1.5f, PersonalGrudges[GuyNpcId].ComboDissatisfy) * ModParam))),
		true, true, FLinearColor::Gray, 5);

	if (this->Enemy.Contains(GuyNpcId)) return false;
	
	if (this->Friend.Contains(GuyNpcId) && PersonalGrudges[GuyNpcId].Dissatisfaction >= FriendlyTolerance())
	{
		UKismetSystemLibrary::PrintString(GWorld, FString("Remove From Friend::").Append(GuyNpcId),
			true, true, FLinearColor::Yellow, 10);
		this->Friend.Remove(GuyNpcId);
		return true;
	}

	if (PersonalGrudges[GuyNpcId].Dissatisfaction >= HostileTolerance())
	{
		UKismetSystemLibrary::PrintString(GWorld, FString("Add To Enemy::").Append(GuyNpcId),
			true, true, FLinearColor::Red, 10);
		this->Enemy.Add(GuyNpcId);
		return true;
	}

	return false;
}

float FCharacterPersonality::FriendlyTolerance() const
{
	return 10 * Tolerance;
}

float FCharacterPersonality::HostileTolerance() const
{
	return 15 * Tolerance;	//必须得大于Friend，要不就会被先加入Enemy了，就诡异了
}

float FCharacterPersonality::FriendlyPercentage(FString NpcId)
{
	if (Friend.Contains(NpcId) == false) return 0;
	const float Tol = FriendlyTolerance();
	if (Tol <= 0) return 0;	//毫无容忍度
	const float Cur = PersonalGrudges.Contains(NpcId) ? PersonalGrudges[NpcId].Dissatisfaction : 0;
	return FMath::Min(1.000f, (Tol - Cur) / Tol);
}

float FCharacterPersonality::HostilePercentage(FString NpcId)
{
	if (Friend.Contains(NpcId) || Enemy.Contains(NpcId)) return 0;
	const float FTol = FriendlyTolerance();
	const float Tol = HostileTolerance() - FTol;
	if (Tol <= 0) return 1;
	const float Cur = PersonalGrudges.Contains(NpcId) ? (PersonalGrudges[NpcId].Dissatisfaction - FTol) : 0;
	if (Cur<=0) return 0;
	return FMath::Min(1.000f, Cur / Tol);
}