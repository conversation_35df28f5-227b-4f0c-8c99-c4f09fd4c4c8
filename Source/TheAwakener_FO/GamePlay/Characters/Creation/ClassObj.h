// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "ClassModel.h"
#include "TheAwakener_FO/GamePlay/Equipment/EquipmentSetRecord.h"
#include "ClassObj.generated.h"

/**
 * 职业经验和等级系统
 */
USTRUCT(BlueprintType)
struct FBattleClassLevelAndExp
{
	GENERATED_BODY()
public:
	UPROPERTY(BlueprintReadOnly)
	int Level = 1;

	UPROPERTY(BlueprintReadOnly)
	int Exp = 0;

	FBattleClassLevelAndExp(){}

	//升级所需要的经验值
	static int LevelingExp(int TargetLevel);

	//增加经验值，返回升了多少级
	int AddExp(int AddExpValue);
};

/**
 * 职业的数据，要储存的那些，放在RoleInfo里面
 */
USTRUCT(BlueprintType)
struct FBattleClassObj
{
	GENERATED_BODY()
public:
	//这个职业的id
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString ClassId;
	
	//我当前已经学会的动作id
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FString> LearntActionId;

	//我之前所使用的武器
	FEquipmentSetRecord EquipmentSet;

	//职业的等级和经验
	FBattleClassLevelAndExp LevelExp;

	FBattleClassObj(){};
	FBattleClassObj(FBattleClassModel Model):
		ClassId(Model.Id), LearntActionId(Model.InitActions){};
};
