// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "NewRoleItem.generated.h"

/**
 * 刚创建的角色给的东西
 */
USTRUCT(BlueprintType)
struct FNewRoleItem
{
	GENERATED_BODY()
public:
	//这条是属于那个角色Type（男、女）的Role
	UPROPERTY()
	FString CharacterType;
	
	//给到RoleInfo的ItemObj<ModelId, Count>
	UPROPERTY()
	TMap<FString, int> PackItems;

	//送进背包的武器的Model Id
	UPROPERTY()
	TArray<FString> PackWeaponModelId;

	//送进背包的装备的Model Id
	UPROPERTY()
	TArray<FString> PackEquipmentModelId;

	//穿身上的武器的Model Id
	UPROPERTY()
	TArray<FString> WearWeaponModelId;

	//穿身上的装备的Model Id
	UPROPERTY()
	TArray<FString> WearEquipmentModelId;

	//初始的货币<CurrencyType, Count>
	UPROPERTY()
	TMap<FString, int> Currency;

	static FNewRoleItem FromJson(TSharedPtr<FJsonObject> JsonObj);
};
