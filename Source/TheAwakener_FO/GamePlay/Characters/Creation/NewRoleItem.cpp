// Fill out your copyright notice in the Description page of Project Settings.


#include "NewRoleItem.h"

#include "TheAwakener_FO/FunctionLibrary/DataFuncLib.h"

FNewRoleItem FNewRoleItem::FromJson(TSharedPtr<FJsonObject> JsonObj)
{
	FNewRoleItem Res;
	Res.CharacterType = UDataFuncLib::AwGetStringField(JsonObj, "Type");
	Res.PackEquipmentModelId = UDataFuncLib::AwGetStringArrayField(JsonObj, "PackEquipments");
	Res.PackWeaponModelId = UDataFuncLib::AwGetStringArrayField(JsonObj, "PackWeapons");
	Res.WearEquipmentModelId = UDataFuncLib::AwGetStringArrayField(JsonObj, "WearEquipments");
	Res.WearWeaponModelId = UDataFuncLib::AwGetStringArrayField(JsonObj, "WearWeapons");

	if (JsonObj->HasField("PackItems"))
	{
		for (const TSharedPtr<FJsonValue, ESPMode::ThreadSafe> ItemInfo : JsonObj->GetArrayField("PackItems"))
		{
			if (ItemInfo->AsObject()->HasField("Id"))
			{
				FString ItemId = ItemInfo->AsObject()->GetStringField("Id");
				int Count = UDataFuncLib::AwGetNumberField( ItemInfo->AsObject(), "Count", 0);
				if (Count > 0) Res.PackItems.Add(ItemId, Count);
			}
		} 
	}
	
	if (JsonObj->HasField("Currency"))
	{
		for (const TSharedPtr<FJsonValue, ESPMode::ThreadSafe> CInfo : JsonObj->GetArrayField("Currency"))
		{
			if (CInfo->AsObject()->HasField("Type"))
			{
				FString CType = CInfo->AsObject()->GetStringField("Type");
				int Count = UDataFuncLib::AwGetNumberField(CInfo->AsObject(), "Count", 0);
				Res.Currency.Add(CType, Count);
			}
		} 
	}

	return Res;
}
