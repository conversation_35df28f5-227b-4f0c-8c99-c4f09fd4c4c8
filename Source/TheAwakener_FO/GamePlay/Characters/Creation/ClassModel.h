// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Kismet/KismetSystemLibrary.h"
#include "TheAwakener_FO/GamePlay/Buff/Buff.h"
#include "TheAwakener_FO/GamePlay/Characters/Action/ActionInfo.h"
#include "TheAwakener_FO/GamePlay/Characters/Action/ArmState.h"
#include "ClassModel.generated.h"


/**
 * 
 */
USTRUCT(BlueprintType)
struct FBattleClassModel
{
	GENERATED_BODY()
public:
	//职业id
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString Id = "";

	//职业Tags
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FString> Tags;

	//可转职的职业id
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FString> CanChangeClassId;

	//使用的武器类别
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	EClassWeaponType UseWeaponType = EClassWeaponType::UnArmed;

	TArray<FString> AimBlendSpace;
	//状态的动画
	TMap<FString, TMap<EArmState,FString>> StateActions;
	//约定的Montage状态动画
	TMap<FString, TMap<EArmState, FString>> MontageActions;
	//基础动作组
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString BaseActionType;
	//默认会的动作的id，并不是说我一开始有这些动作我就全会，很多是要解锁的
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FString> InitActions;
	//所有的动作的信息
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FActionInfo> Actions;
	//基础动作动作的信息
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FActionInfo> RogueBaseActions;
	//战斗动作的信息
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FActionInfo> RogueBattleActions;

	//默认声音所在文件夹
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString SoundBaseDictionary;
	
	//属性成长潜力
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FChaProp Potential;

	//职业初始Buff的信息
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FTableAddBuffInfo> StartBuffInfo;

	//角色体重，用于下落速度，越大越快
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	float FallWeight = 100.00f;
	//是否是坐骑旋转的
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	bool MountsTypeRotate = false;

	//职业默认武器，当玩家切换职业却没有对应武器的时候，会给这些武器
	//[0]=主手，[1]=副手
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FString> DefaultWeapons;

	//切换到这个职业，也许需要做一个动作，耍帅嘛
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString ActionOnChangeTo;

	static FBattleClassModel FromJson(TSharedPtr<FJsonObject> JsonObj)
	{
		FBattleClassModel Info = FBattleClassModel();
		Info.Id = UDataFuncLib::AwGetStringField(JsonObj, "Id");
		
		for (const TSharedPtr<FJsonValue> Value : UDataFuncLib::AwGetArrayField(JsonObj, "Tag"))
			Info.Tags.Add(Value->AsString());

		for (const TSharedPtr<FJsonValue> CanCC : UDataFuncLib::AwGetArrayField(JsonObj, "CanChangeClass")) 
			Info.CanChangeClassId.Add(CanCC->AsString());
		
		Info.UseWeaponType = UDataFuncLib::AwGetEnumField<EClassWeaponType>(JsonObj, "WeaponType", EClassWeaponType::BigSword);

		Info.AimBlendSpace = UDataFuncLib::AwGetStringArrayField(JsonObj, "AimBlendSpace");
		
		if (JsonObj->HasField("StateActions"))
		{
			const TSharedPtr<FJsonObject> PreorderKeys = JsonObj->GetObjectField("StateActions");
			for (TTuple<FString, TSharedPtr<FJsonValue, ESPMode::ThreadSafe>> KeyKV : PreorderKeys->Values)
			{
				TMap<EArmState, FString> UseActId;
				UseActId.Add(EArmState::Armed, UDataFuncLib::AwGetStringField(KeyKV.Value->AsObject(), "Armed", ""));
				UseActId.Add(EArmState::Unarmed, UDataFuncLib::AwGetStringField(KeyKV.Value->AsObject(), "Unarmed", ""));
				if (UseActId[EArmState::Armed].IsEmpty() || UseActId[EArmState::Unarmed].IsEmpty())
					UKismetSystemLibrary::PrintString(GWorld, FString("[BattleClass]StateActionError:").Append(Info.Id),
						true, true, FLinearColor::Red, 10);
				Info.StateActions.Add(KeyKV.Key, UseActId);
			}
		}
		
		if (JsonObj->HasField("PreorderActionKeys"))
		{
			const TSharedPtr<FJsonObject> PreorderKeys = JsonObj->GetObjectField("PreorderActionKeys");
			for (TTuple<FString, TSharedPtr<FJsonValue, ESPMode::ThreadSafe>> KeyKV : PreorderKeys->Values)
			{
				TMap<EArmState, FString> UseActId;
				UseActId.Add(EArmState::Armed, UDataFuncLib::AwGetStringField(KeyKV.Value->AsObject(), "Armed", ""));
				UseActId.Add(EArmState::Unarmed, UDataFuncLib::AwGetStringField(KeyKV.Value->AsObject(), "Unarmed", ""));
				if (UseActId[EArmState::Armed].IsEmpty() || UseActId[EArmState::Unarmed].IsEmpty())
					UKismetSystemLibrary::PrintString(GWorld, FString("[BattleClass]MontageActionError:").Append(Info.Id),
						true, true, FLinearColor::Red, 10);
				Info.MontageActions.Add(KeyKV.Key, UseActId);
			}
		}
		
		Info.BaseActionType = UDataFuncLib::AwGetStringField(JsonObj, "BaseActionType");

		Info.MountsTypeRotate =UDataFuncLib::AwGetBoolField(JsonObj, "MountsTypeRotate", false);
		
		for (const TSharedPtr<FJsonValue> Value : UDataFuncLib::AwGetArrayField(JsonObj, "ClassBuff"))
			Info.StartBuffInfo.Add(FTableAddBuffInfo::FromJson(Value->AsObject()));
		
		for (const TSharedPtr<FJsonValue> Value : UDataFuncLib::AwGetArrayField(JsonObj, "Actions"))
		{
			FActionInfo ThisAction = FActionInfo::FromJson(Value->AsObject());
			if (ThisAction.Id.IsEmpty() == false)
			{
				Info.Actions.Add(ThisAction);
				if (UDataFuncLib::AwGetBoolField(JsonObj, "InitAction", true) == true)
					Info.InitActions.Add(ThisAction.Id); 
			}
		}

		for (const TSharedPtr<FJsonValue> Value : UDataFuncLib::AwGetArrayField(JsonObj, "RogueBaseActions"))
		{
			FActionInfo ThisAction = FActionInfo::FromJson(Value->AsObject());
			if (ThisAction.Id.IsEmpty() == false)
				Info.RogueBaseActions.Add(ThisAction);
		}
		for (const TSharedPtr<FJsonValue> Value : UDataFuncLib::AwGetArrayField(JsonObj, "RogueBattleActions"))
		{
			FActionInfo ThisAction = FActionInfo::FromJson(Value->AsObject());
			if (ThisAction.Id.IsEmpty() == false)
				Info.RogueBattleActions.Add(ThisAction);
		}

		const FString BaseActionId = UDataFuncLib::AwGetStringField(JsonObj, "BaseActionType");
		// TArray<FActionInfo> BaseActions;// = UGameplayFuncLib::GetDataManager()->GetBaseActionsById(BaseActionId);
		// if (BaseActions.Num())
		// {
		// 	for (FActionInfo BaseAction : BaseActions)
		// 	{
		// 		Info.Actions.Add(BaseAction);
		// 		Info.InitActions.Add(BaseAction.Id);	//Base的总是默认学会的
		// 	} 
		// }

		Info.FallWeight = UDataFuncLib::AwGetNumberField(JsonObj, "FallWeight", 100);

		Info.SoundBaseDictionary = UDataFuncLib::AwGetStringField(JsonObj, "SoundBase");

		if (JsonObj->HasField("Potential"))
		{
			Info.Potential = FChaProp::PotentialFromJson(JsonObj->GetObjectField("Potential"));
		}

		if (JsonObj->HasField("DefaultWeapons"))
		{
			for (const TSharedPtr<FJsonValue, ESPMode::ThreadSafe> WeaponId : JsonObj->GetArrayField("DefaultWeapons"))
			{
				Info.DefaultWeapons.Add(WeaponId->AsString());
			} 
		}

		Info.ActionOnChangeTo = UDataFuncLib::AwGetStringField(JsonObj, "ActionOnChangeTo");
		
		return Info;
	};
};

