// Fill out your copyright notice in the Description page of Project Settings.


#include "AwBreakSystemComponent.h"

#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GameFramework/Manager/AwRogueDataSystem.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"

// Sets default values for this component's properties
UAwBreakSystemComponent::UAwBreakSystemComponent()
{
	// Set this component to be initialized when the game starts, and to be ticked every frame.  You can turn these features
	// off to improve performance if you don't need them.
	PrimaryComponentTick.bCanEverTick = true;

	// ...
}


// Called when the game starts
void UAwBreakSystemComponent::BeginPlay()
{
	Super::BeginPlay();

	// ...
	
}


// Called every frame
void UAwBreakSystemComponent::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
	Super::TickComponent(DeltaTime, TickType, ThisTickFunction);

	if(bInBreak)
	{
		CurBreakTime += DeltaTime;
		if(CurBreakTime >= MaxBreakTime)
		{
			bInBreak = false;
			CurBreakTime = 0;
			bInResumeValue = true;
		}
		//检测是否符合转阶段(目前先检测怪物身上用来标记阶段的Buff),如果血量符合转阶段，直接结束Break
		CheckHPState();
	}
	if(bInResumeValue)
	{
		CurrentBreakValue += MaxBreakValue * DeltaTime;
		if(CurrentBreakValue >= MaxBreakValue)
		{
			CurrentBreakValue = MaxBreakValue;
			bInResumeValue = false;
		}
	}
	// ...
}

void UAwBreakSystemComponent::SetActiveBreakSystem(bool bActiveSystem)
{
	if(bActiveSystem)
	{
		this->SetActive(true);
		AAwCharacter* Character = Cast<AAwCharacter>(GetOwner());
		if(Character)
		{
			MaxBreakValue = Character->CharacterObj.CurProperty.HP * BreakRate;
			
			//先直接用外部数据
			MaxBreakValue = UGameplayFuncLib::GetAwDataManager()->GetMobProp(Character->MobClassId,Character->CharacterObj.MobAlterId).Break;

			//暂时先把通关后系数去掉
			// UAwRogueDataSystem* RogueDataSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>();
			// if(RogueDataSystem)
			// {
			// 	if(RogueDataSystem->GetSwitch("GrailLevel") > 0)
			// 		MaxBreakValue = MaxBreakValue * ClearedGameRate;
			// }
			CurrentBreakValue = MaxBreakValue;
		}
	}
	else
	{
		this->SetActive(false);
	}
}

void UAwBreakSystemComponent::SetMaxBreakValue(float Value)
{
	if(MaxBreakValue > 0)
	{
		float Rate = Value / MaxBreakValue;
		MaxBreakValue = Value;
		CurrentBreakValue *= Rate;
	}
}

void UAwBreakSystemComponent::OnHit(FDamageInfo DamInfo)
{
	if(this->IsActive() && MaxBreakValue > 0 && CurrentBreakValue > 0 && !bInResumeValue)
	{
		AAwCharacter* Character = Cast<AAwCharacter>(GetOwner());
		int FinalDamage = DamInfo.DamagePower.TotalDamage();
		if(Character && !Character->Dead(true) &&
			FinalDamage > 0 &&
			Character->CharacterObj.CurrentRes.HP >= FinalDamage &&
			DamInfo.DamageType != EDamageType::OtherDamage &&
			DamInfo.DamageType != EDamageType::PeriodDamage)
		{
			float BreakValue = DamInfo.DamagePower.TotalBreak();
			if(DamInfo.Attacker)
				BreakValue *= DamInfo.Attacker->CharacterObj.CurProperty.PAttack;
			if(DamInfo.ValuePowerArea.Contains(EDamageArea::BreakDamagePower))
				BreakValue *= DamInfo.ValuePowerArea[EDamageArea::BreakDamagePower];
			if(DamInfo.IsCritical)
				BreakValue *= DamInfo.Attacker->CharacterObj.CurProperty.CriticalRate;
			// priority 不可控 去掉了
			// float BreakValue = FinalDamage * (FMath::Clamp(DamInfo.AttackInfoPriority, 1, 10) * FMath::Clamp((10 -DamInfo.DefendInfoPriority), 1, 10));
			// 职业系数
			// float ClassRatio = 0;
			// if(DamInfo.Attacker->IsPlayerCharacter())
			// {
			// 	ClassRatio = UGameplayFuncLib::GetAwDataManager()->GetRolePawnByClassId(DamInfo.Attacker->CharacterObj.ClassId).BreakRatio;
			// }
			// if (DamInfo.ValuePowerArea.Contains(EDamageArea::BreakDamagePower))
			// {
			// 	ClassRatio*= DamInfo.ValuePowerArea[EDamageArea::BreakDamagePower];
			// }
			// BreakValue = BreakValue * ClassRatio;

			//非直伤系数
			// if(DamInfo.DamageType != EDamageType::DirectDamage)
			// {
			// 	if(DamInfo.Elemental == EChaElemental::Physical)
			// 		BreakValue = BreakValue * 0.3;
			// 	else if(DamInfo.Elemental == EChaElemental::Thunder)
			// 		BreakValue = BreakValue * 0.4;
			// 	else
			// 		BreakValue = BreakValue * 0.2;
			// }
			//防反的Break伤害加倍
			// if(DamInfo.DamageSourceType == EAttackSource::CounterAction)
			// {
			// 	BreakValue = BreakValue * 2;
			// }

			//限制单次最大值
			//BreakValue = FMath::Clamp(BreakValue, 0 ,( MaxBreakValue * 0.2 + 200));

			//UKismetSystemLibrary::PrintString(Character, "Damage: " + FString::FromInt(DamInfo.FinalDamage()), true, true, FLinearColor::Red);
			//UKismetSystemLibrary::PrintString(Character, "BreakValue: " + FString::FromInt(BreakValue), true, true, FLinearColor::Red);

			CurrentBreakValue -= BreakValue;
			if (CurrentBreakValue <= 0)
			{
				CurrentBreakValue = 0;
				Character->PreorderAction("BreakHurt");
				bInBreak = true;
			}
		}
	}
}

void UAwBreakSystemComponent::ResumeBreakValue()
{
	if(MaxBreakValue > 0)
	{
		bInBreak = false;
		CurBreakTime = 0;
		this->SetActive(true);
		CurrentBreakValue = MaxBreakValue;
	}
}

void UAwBreakSystemComponent::CheckHPState()
{
	AAwCharacter* Character = Cast<AAwCharacter>(GetOwner());
	if(Character)
	{
		float CurHPPercent = Character->HealthPercentage(true) * 100;
		//检测一阶段BUFF
		int FirstPercent = Character->GetBuffStackTotal("Rogue_Boss_FirstStage");
		if(FirstPercent > 1)
		{
			if(CurHPPercent < FirstPercent - 1)
			{
				bInBreak = false;
				CurBreakTime = 0;
				bInResumeValue = true;
				return;
			}
		}
		//检测二阶段BUFF
		int SecondPercent = Character->GetBuffStackTotal("Rogue_Boss_SecondStage");
		if(SecondPercent > 1)
		{
			if(CurHPPercent < SecondPercent - 1)
			{
				bInBreak = false;
				CurBreakTime = 0;
				bInResumeValue = true;
				return;
			}
		}
	}
}

