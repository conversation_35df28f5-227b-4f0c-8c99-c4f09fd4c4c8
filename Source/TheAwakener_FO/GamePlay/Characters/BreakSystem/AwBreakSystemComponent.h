// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "TheAwakener_FO/GamePlay/Characters/Battle/BattleStructs.h"
#include "AwBreakSystemComponent.generated.h"


UCLASS( ClassGroup=(Custom), meta=(BlueprintSpawnableComponent) )
class THEAWAKENER_FO_API UAwBreakSystemComponent : public UActorComponent
{
	GENERATED_BODY()

public:	
	// Sets default values for this component's properties
	UAwBreakSystemComponent();

protected:
	// Called when the game starts
	virtual void BeginPlay() override;

public:
	// Called every frame
	virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

	UFUNCTION(BlueprintCallable)
	void SetActiveBreakSystem(bool bActiveSystem);

	UFUNCTION(BlueprintCallable)
	void SetMaxBreakValue(float Value);

	void OnHit(FDamageInfo DamInfo);

	UFUNCTION(BlueprintCallable)
	void ResumeBreakValue();

	UFUNCTION(BlueprintCallable)
	void CheckHPState();

	UPROPERTY(BlueprintReadWrite)
	bool bInBreak = false;

	UPROPERTY(BlueprintReadWrite)
	bool bInResumeValue = false;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	float BreakRate = 1.2;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	float ClearedGameRate = 1.3;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	float MaxBreakTime = 10;

	UPROPERTY(BlueprintReadWrite)
	float CurBreakTime = 0;

	UPROPERTY(BlueprintReadWrite)
	int MaxBreakValue = 0;

	UPROPERTY(BlueprintReadWrite)
	int CurrentBreakValue = 0;
};
