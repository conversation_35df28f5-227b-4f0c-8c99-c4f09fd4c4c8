// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Action/ActionLink.h"
#include "Creation/MobModel.h"
#include "TheAwakener_FO/GamePlay/Buff/Buff.h"
#include "TheAwakener_FO/GamePlay/Equipment/Weapon.h"
#include "TheAwakener_FO/GamePlay/Equipment/Equipment.h"
#include "AwCharacterInfo.generated.h"
DECLARE_STATS_GROUP(TEXT("TheAwakenerSurvivor"), STATGROUP_TheAwakener, STATCAT_Advanced);
DECLARE_CYCLE_STAT_EXTERN(TEXT("SurvivorTest"),
						  STAT_SurvivorTest,
						  STATGROUP_TheAwakener,
						  THEAWAKENER_FO_API);
/**
 * 性别
 */
UENUM(BlueprintType)
enum class ECharacterGender : uint8
{
	Male,
	Female,
	None
};

/**
 * 种族
 */
UENUM(BlueprintType)
enum class EAwRace: uint8
{
	Human,
	WereRat,
	Ogre
};


/**
 * 一个角色的数据，CharacterObj
 */
USTRUCT(BlueprintType)
struct FAwCharacterInfo
{
	GENERATED_BODY()
public:
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString Name = "";
	
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FString> Tag;
	
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString ClassId = "";

	//体型，包括种族、性别等等
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString TypeId = "TypeA";
	
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString MobId = "";
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString MobAlterId = "";

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	EMobRank MobRank = EMobRank::Normal;
	//似乎没有在使用 Hawk
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int CharacterLevel = 1;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FEquippedWeaponSet WeaponSet;
	
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FEquipment> Equipments;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	ECharacterGender Gender = ECharacterGender::Female;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int Level = 1;
	
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int Exp = 0;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	EAwRace Race = EAwRace::Human;

	// 当前的HP、MP、SP等
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FChaResource CurrentRes;

	// 当前的 HP、MP、ATK、DEF 等属性 MaxHP, MaxMP
	UPROPERTY(BlueprintReadOnly)
	FChaProp CurProperty;

	// 角色基础属性星级
	FChaProp BasePropStar;
	
	// 角色基础属性
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FChaProp BaseProp;

	// 装备获得的属性
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FChaProp EquipmentProp;

	// 武器获得的属性
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FChaProp WeaponProp;

	// Buff获得的属性
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FChaProp> BuffedProp;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	EChaElemental Elemental = EChaElemental::Physical;	//元素属性

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FChaPart> Part;

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FBuffObj> Buff;

	
	//主技能更换记录，符合UActionComponent::ChangeMainAction参数
	UPROPERTY()
	TMap<FString, FActionSelection> MainActionChangeRecord;
	//链接技能更换记录，符合UActionComponent::ChangeLinkedAction参数
	UPROPERTY()
	TMap<FString, FActionLink> LinkedActionChangeRecord;

	FAwCharacterInfo(){};
	FAwCharacterInfo(
		FString ChaName, FString BattleClassId, FString ChaTypeId, int Level,
		TArray<FBuffObj> WithBuffs, FEquippedWeaponSet UsingWeapon, TArray<FEquipment> UsingEquipments):
	Name(ChaName), ClassId(BattleClassId), TypeId(ChaTypeId), CharacterLevel(Level),
	WeaponSet(UsingWeapon), Equipments(UsingEquipments),Level(Level),
	Buff(WithBuffs){};
	
	static FAwCharacterInfo FromThing(const FThingObj& ThingObj);

	void AttrRecheck();
	void ResRecheck(FChaProp ThisTickOffset = FChaProp());
	void ResetBaseProperty();
	float GetActionSpeedRate();
	FAwCharacterInfo GetCharacterInfoSaveData();
	/**
	 * 获得经验值，TODO: 满级30级，无论何级需要的经验都是1000
	 * @param Value 获得了多少经验值
	 * @return 这次获得经验升了多少级
	 */
	int GainExp(int Value);

	//计算属性在Buff加权后的结果
	FChaProp CaluPropAfterBuff(FChaProp Base);

	
	FChaProp BuffPlus = FChaProp();
	FChaProp BuffTimes = FChaProp();
};
