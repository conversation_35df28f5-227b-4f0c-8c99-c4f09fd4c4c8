// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "EnumControlStateType.h"
#include "TheAwakener_FO/FunctionLibrary/DataFuncLib.h"
#include "ControlState.generated.h"

/**
 * 角色可控状态
 */
USTRUCT(BlueprintType)
struct  FControlState// : public UObject
{
	GENERATED_BODY()
public:
	/**
	 * The character CanMove state determines that if the character
	 * can move while has an move order.
	 *  0=always can
	 *  1=if cmd is from AI
	 *  2=none can order the character, but still can be FORCED move
	 *  3=nothing can make the character position changes
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	EControlStateType CanMove = EControlStateType::Normal;

	/**
	 * The character CanRotate state determines that if the character
	 * can turn right or left.
	 *  0=always can
	 *  1=only if cmd is from AI
	 *  2=cmd is useless, but still can be FORCED rotate
	 *  3=never could turn left or right.
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	EControlStateType CanRotate = EControlStateType::Normal;

	/**
	 * The character CanJump state determines if the character could
	 * jump.
	 * 0=always can
	 * 1=while AI cmd orders
	 * 2=ignore any cmd, but can be FORCED to leave the ground
	 * 3=standstill on the ground, nothing can make me up
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	EControlStateType CanJump = EControlStateType::Normal;

	/**
	 * The character CanChangeAction state determines that if the
	 * character can ChangeAction.
	 * 0=always can
	 * 1=can change to any standard action naturally, but  cmd from
	 *  none-AI dose not works.
	 * 2=does not change action by any cmd
	 * 3=keep last frame of animation while action reaches to avoid
	 * change action.
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	EControlStateType CanChangeAction = EControlStateType::Normal;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	EControlStateType CanAttack = EControlStateType::Normal;
	
	bool EqualsTo(FControlState* OtherState) const
	{
		return (
			this->CanJump == OtherState->CanJump &&
			this->CanMove == OtherState->CanMove &&
			this->CanRotate == OtherState->CanRotate &&
			this->CanChangeAction == OtherState->CanChangeAction&&
			this->CanAttack == OtherState->CanAttack
		);
	}

	static FControlState FromJson(TSharedPtr<FJsonObject> JsonObj)
	{
		FControlState ControlState;
		ControlState.CanMove = UDataFuncLib::AwGetEnumField<EControlStateType>(
			JsonObj, "CanMove", EControlStateType::Normal);
		ControlState.CanRotate =  UDataFuncLib::AwGetEnumField<EControlStateType>(
			JsonObj, "CanRotate", EControlStateType::Normal);
		ControlState.CanJump =  UDataFuncLib::AwGetEnumField<EControlStateType>(
			JsonObj, "CanJump", EControlStateType::Normal);
		ControlState.CanChangeAction =  UDataFuncLib::AwGetEnumField<EControlStateType>(
			JsonObj, "CanChangeAction", EControlStateType::Normal);
		ControlState.CanChangeAction =  UDataFuncLib::AwGetEnumField<EControlStateType>(
			JsonObj, "CanAttack", EControlStateType::Normal);
		return ControlState;
	}

	FControlState operator+(const FControlState& OtherState) const
	{
		FControlState Res;
		Res.CanMove = FMath::Max(this->CanMove, OtherState.CanMove);
		Res.CanRotate = FMath::Max(this->CanRotate, OtherState.CanRotate);
		Res.CanJump = FMath::Max(this->CanJump, OtherState.CanJump);
		Res.CanChangeAction = FMath::Max(this->CanChangeAction, OtherState.CanChangeAction);
		Res.CanAttack = FMath::Max(this->CanAttack,OtherState.CanAttack);
		return Res;
	}	
};
