// Fill out your copyright notice in the Description page of Project Settings.


#include "FightingWill.h"

#include "Kismet/KismetSystemLibrary.h"

bool FFightingWill::ModifyValue(int ModValue, int LevelRule, bool LoopLevel )
{
	this->Value += ModValue;
	if (this->Value <= 0)
	{
		this->Level += LevelRule;
		this->Value = 10000;
		if (LoopLevel)
		{
			while (this->Level < 0){this->Level = this->MaxLevel;}
			while (this->Level > this->MaxLevel){this->Level = 0;}
		}else
		{
			this->Level = FMath::Clamp(this->Level, 0, this->MaxLevel);
		}
		//UKismetSystemLibrary::PrintString(GWorld,
		//	FString("FightingWill : ").Append(FString::FromInt(this->Level)).Append(FString(" : ")).Append(FString::FromInt(this->Value)));
		return true;
	}
	//UKismetSystemLibrary::PrintString(GWorld,
	//	FString("FightingWill : ").Append(FString::FromInt(this->Level)).Append(FString(" : ")).Append(FString::FromInt(this->Value)));
	return false;
}

void FFightingWill::ModifyLevel(int ToLevel, bool ResetValueIfSameLevel)
{
	if (Level != ToLevel || ResetValueIfSameLevel)
	{
		Value = 10000;
	}
	this->Level = FMath::Clamp(ToLevel, 0, MaxLevel);
	
}

