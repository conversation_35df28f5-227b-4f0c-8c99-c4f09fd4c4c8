// Fill out your copyright notice in the Description page of Project Settings.


#include "AwAIComponent.h"

#include <string>

#include "MathUtil.h"
#include "Kismet/KismetMathLibrary.h"
#include "TheAwakener_FO/FunctionLibrary/CallFuncLib.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GameFramework/Base/AwGameInstance.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"


// Sets default values for this component's properties
UAwAIComponent::UAwAIComponent()
{
	// Set this component to be initialized when the game starts, and to be ticked every frame.  You can turn these features
	// off to improve performance if you don't need them.
	PrimaryComponentTick.bCanEverTick = true;

	
	// Init
	ViewRadius = 50;
	SightRadius = 1500;
	SightHalfAngleDregee = 45;
	LoseSightRadius = 2500;
	LoseSightHalfAngleDregee = 50;
	NoiseRadius = 300;
	HearingRadius = 500;
}

// Called when the game starts
void UAwAIComponent::BeginPlay()
{
	Super::BeginPlay();

	// ...
	bAuthority = (GetOwner()->GetLocalRole() == ROLE_Authority);
	bAutoActivate = bAuthority;
	bClockWiseInNavAvoidRot = FMath::RandBool();
}

void UAwAIComponent::SetUp(FMobModel InMobModel)
{
	if (bAuthority)
	{
		MobModel = InMobModel;
		SetUpAIScriptList(MobModel.AIScript);

		SetUpAIOrderList(MobModel.AIOrder);
		
		StopRangeWhenMoveFollow = MobModel.MoveProp.StopRangeWhenMoveFollow;
		MinDisWhenMoveAround = MobModel.MoveProp.MinDisWhenMoveAround;
		MaxDisWhenMoveAround = MobModel.MoveProp.MaxDisWhenMoveAround;
		bNegativeDegreeWhenMoveAround = MobModel.MoveProp.bNegativeDegreeWhenMoveAround;
		
		ViewRadius = MobModel.PerceptionProp.ViewRadius;
		RelaxSightRadius = MobModel.PerceptionProp.RelaxSightRadius;
		SightRadius = MobModel.PerceptionProp.SightRadius;
		SightZRadius = MobModel.PerceptionProp.SightZRadius;
		SightHalfAngleDregee = MobModel.PerceptionProp.SightHalfAngleDregee;
		LoseSightRadius = MobModel.PerceptionProp.LoseSightRadius;
		LoseSightHalfAngleDregee = MobModel.PerceptionProp.LoseSightHalfAngleDregee;
		NoiseRadius = MobModel.PerceptionProp.NoiseRadius;
		HearingRadius = MobModel.PerceptionProp.HearingRadius;
	}
}

// Called every frame
void UAwAIComponent::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
	Super::TickComponent(DeltaTime, TickType, ThisTickFunction);

	if (Paused== true) return;
	if(!Cast<AAwCharacter>(GetOwner())->GotReady) return;
	
	if (bAuthority)//权威端才运行
	{
		DurationTime += DeltaTime;
		PawnRadius = Cast<AAwCharacter>(GetOwner())->GetCapsuleComponent()->GetScaledCapsuleRadius();
		PawnHalfHeight = Cast<AAwCharacter>(GetOwner())->GetCapsuleComponent()->GetScaledCapsuleHalfHeight();

		this->JustClearStimulus = false;

		//Update刺激源
		bool AtLeastOneStimulus = false;
		int index = 0;
		//听觉
		while (index < Stimuli_Voice.Num())
		{
			if (Stimuli_Voice[index].Duration > 0)
			{
				Stimuli_Voice[index].Duration -= DeltaTime;
				index++;
			}else
			{
				Stimuli_Voice.RemoveAt(index);
				AtLeastOneStimulus = true;
			}
		}
		index = 0;
		//同伴信号
		while (index < Stimuli_AllySignal.Num())
		{
			if (Stimuli_AllySignal[index].Duration > 0)
			{
				Stimuli_AllySignal[index].Duration -= DeltaTime;
				index++;
			}else
			{
				Stimuli_AllySignal.RemoveAt(index);
				AtLeastOneStimulus = true;
			}
		}
		//视觉
		UpdateViewCharacterList(DeltaTime);

		UpdateThreatList(DeltaTime);

		//刚好全清空了，就开启标记了
		if (AtLeastOneStimulus == true && ThreatList.Num() <= 0 && Stimuli_AllySignal.Num() <= 0 && Stimuli_Voice.Num() <= 0)
		{
			this->JustClearStimulus = true;
		}
		

		if(FocusEnemy.Character)
		{
			if (!FocusEnemy.Character->Dead(true))
				FocusEnemy.DistanceXY = FVector::Dist2D(FocusEnemy.Character->GetActorLocation(), GetOwner()->GetActorLocation());
			else
				FocusEnemy = FAIFindChaInfo();
		}
	
		if (TargetEnemy.Character)
		{
			if (!TargetEnemy.Character->Dead(true))
				TargetEnemy.DistanceXY = FVector::Dist2D(TargetEnemy.Character->GetActorLocation(), GetOwner()->GetActorLocation());
			else
				TargetEnemy = FAIFindChaInfo();
		}

		//Rogue 计算各个Action的CD
		if(ActionCDList.Num() > 0)
		{
			TArray<FString> RemoveActionIds; 
			for (auto ActionCD : ActionCDList)
			{
				const float CurCD = ActionCD.Value - DeltaTime;
				ActionCDList[ActionCD.Key] = CurCD;
				if(CurCD <= 0)
					RemoveActionIds.Add(ActionCD.Key);
			}
			for (FString RemoveActionId : RemoveActionIds)
			{
				ActionCDList.Remove(RemoveActionId);
			}
		}
		

		AAwCharacter* MyCharacter = Cast<AAwCharacter>(GetOwner());
		if (bWorkAIAScript && MyCharacter->ShouldRunAI())
		{
			//检测是否在需要Wait后执行的动作
			if(bHasWaitForAction)
			{
				ActionWaitTime -= DeltaTime;
				if(ActionWaitTime <= 0)
				{
					FAICommand Cmd = CreateUseActionCommand(WaitForActionId, GetOwner()->GetActorForwardVector());
					SetInputFromAICommand(Cmd);
					bHasWaitForAction = false;
					WaitForActionId = "";
					ActionWaitTime = 0.0f;
				}
			}
			else
			{
				//AIScript
				for (int i = 0; i < AIScriptPartList.Num(); i++)
				{
					//Condition
					bool CanDoAction = true;
					for(auto Condition : AIScriptPartList[i].Condition)
					{
						UFunction* ConditionFunc = UCallFuncLib::GetUFunction(Condition.ClassPath, Condition.FunctionName);
						if (ConditionFunc)
						{
							struct
							{
								AAwCharacter* Character;
								UAwAIComponent* AIComponent;
								TArray<FString> Params;
								bool Result;
							}ConditionFuncParam;
							ConditionFuncParam.Character = MyCharacter;
							ConditionFuncParam.AIComponent = this;
							ConditionFuncParam.Params = Condition.Params;
							this->ProcessEvent(ConditionFunc, &ConditionFuncParam);
							if (!ConditionFuncParam.Result)
							{
								CanDoAction = false;
								break;
							}	
						}
					}
					if (CanDoAction)
					{
						//OnReady
						for(auto OnReady : AIScriptPartList[i].OnReady)
						{
							UFunction* OnReadyFunc = UCallFuncLib::GetUFunction(OnReady.ClassPath, OnReady.FunctionName);
							if (OnReadyFunc)
							{
								struct
								{
									AAwCharacter* Character;
									UAwAIComponent* AIComponent;
									TArray<FString> Params;
								}OnReadyFuncParam;
								OnReadyFuncParam.Character = MyCharacter;
								OnReadyFuncParam.AIComponent = this;
								OnReadyFuncParam.Params = OnReady.Params;
								this->ProcessEvent(OnReadyFunc, &OnReadyFuncParam);
							}
						}
						//Action
						this->ClearUseActionTags();
						for(auto Action : AIScriptPartList[i].Action)
						{
							UFunction* ActionFunc = UCallFuncLib::GetUFunction(Action.ClassPath, Action.FunctionName);
							if (ActionFunc)
							{
								struct
								{
									AAwCharacter* Character;
									UAwAIComponent* AIComponent;
									TArray<FString> Params;
									FAICommand Result;
								}ActionFuncParam;
								ActionFuncParam.Character = MyCharacter;
								ActionFuncParam.AIComponent = this;
								ActionFuncParam.Params = Action.Params;
								this->ProcessEvent(ActionFunc, &ActionFuncParam);
								if (ActionFuncParam.Result.AICommandType != "")
								{
									SetInputFromAICommand(ActionFuncParam.Result);
								}
							}
						}
						break;
					}
				}
			}
			
		}
		//Move
		if(ThisTickMoveCmd.AICommandType != "")
		{
			if (ThisTickMoveCmd.AICommandType == "MoveTo")
			{
				//UKismetSystemLibrary::PrintString(this,ThisTickMoveCmd.Pos.ToString(),true,false,FColor::Yellow,DeltaTime);
				//通过Nav获得实际下一帧要去的Loc
				FVector NextLoc = GetNextLocByNav(ThisTickMoveCmd.Pos, 0);
				NextLoc.Z = NextLoc.Z + PawnHalfHeight;
				FVector CurLoc = MyCharacter->GetActorLocation();
				FVector2D NextDir = FVector2D(NextLoc - CurLoc);
				NextDir.Normalize();
				
				// MyCharacter->SetMoveDirection(FVector2D(NextDir.X, NextDir.Y), true);
				// MyCharacter->SetFaceDirection(FVector2D(NextDir.X, NextDir.Y), true);

				//Avoidance
				int MoveSpeed = MyCharacter->GetMoveComponent()->GetMoveSpeed();
				// const float BaseSpeed = 50.000f + FMath::Pow(25.000f, 0.600f + (MyCharacter->CharacterObj.CurProperty.MoveSpeed * 2.000f) / 25.000f) * 10.000f;
				// if(MoveSpeed < 1)
				// 	MoveSpeed = BaseSpeed;
				
				//FVector ThisTickTargetLoc = CurLoc + FVector(NextDir, 0) * MoveSpeed * DeltaTime * 3;//为了防止抖动，计算3帧后的位置
				
				//FVector FixedLoc = UGameplayFuncLib::GetAwAvoidanceManager()->ModifyAvoidMove(MyCharacter, ThisTickTargetLoc, bClockWiseInNavAvoidRot);
				//if((FixedLoc-CurLoc).IsNearlyZero() == false)
				{
					//NextDir = FVector2D(FixedLoc - CurLoc);
					//NextDir.Normalize();

					//const float TargetDis = FVector::Distance(NextLoc, CurLoc);
					//const float FixedDis = FVector::Distance(FixedLoc, CurLoc);

					//const int SpdLv = TargetDis >= FixedDis ? ThisTickMoveCmd.SpeedLevel : 0;
					const int SpdLv = ThisTickMoveCmd.SpeedLevel;
					
					MyCharacter->SetAIMoveAndFaceDir(NextDir, NextDir, SpdLv);

					// MoveSpeed = ((SpdLv - 1) + 1.000f) * BaseSpeed;
					MoveSpeed = MyCharacter->GetMoveComponent()->GetMoveSpeedByLv(SpdLv);
					
					//UGameplayFuncLib::GetAwAvoidanceManager()->AddCharacterMove(MyCharacter, FixedLoc);
				}
			}
			else if (ThisTickMoveCmd.AICommandType == "MoveAround")
			{
				FVector NextDir;
				NextDir = GetMoveAroundDir(ThisTickMoveCmd.Pos, ThisTickMoveCmd.MinRadius, ThisTickMoveCmd.MaxRadius, ThisTickMoveCmd.bNegativeDegree);
				//用Nav检测是否可以移动
				FVector NextLoc = GetOwner()->GetActorLocation() + NextDir * PawnRadius;
				NextLoc = GetNextLocByNav(NextLoc, 0);
				NextDir = (NextLoc - GetOwner()->GetActorLocation()).GetSafeNormal();
				FVector NextFace = (ThisTickMoveCmd.Pos - GetOwner()->GetActorLocation()).GetSafeNormal(0.01f);
				//TODO: Add Avoidance
				MyCharacter->SetAIMoveAndFaceDir(
					FVector2D(NextDir.X, NextDir.Y),
					FVector2D(NextFace.X, NextFace.Y),
					2
				);
			}
			ThisTickMoveCmd = FAICommand();
		}
		//临时：AI在Action中的旋转方式修改
		else
		{
			//临时:先屏蔽玩家身上的AIComponent发出的移动指令
			if (!MyCharacter->IsPlayerCharacter())
			{
				const FVector2D TargetDir = FVector2D(GetTargetDirection());

				MyCharacter->SetAIMoveAndFaceDir(
					TargetDir, TargetDir, 0
				);
			}
		}
	}
}

FVector UAwAIComponent::GetTargetDirection()
{
	UFunction* Func = UCallFuncLib::GetUFunction(TargetDirFunc.ClassPath, TargetDirFunc.FunctionName);
	if (Func)
	{
		struct
		{
			AAwCharacter* Character;
			TArray<FString> StateParams;
			TArray<FString> Params;
			FVector Result = FVector::ZeroVector;
		}TargetDirFuncParam;
		TargetDirFuncParam.Character = Cast<AAwCharacter>(GetOwner());
		TargetDirFuncParam.Params = TargetDirFunc.Params;
		this->ProcessEvent(Func, &TargetDirFuncParam);
		return TargetDirFuncParam.Result;
	}
	return FVector::ZeroVector;
}

void UAwAIComponent::SetTargetEnemy(FAIFindChaInfo Target)
{
	AAwCharacter* character = Cast<AAwCharacter>(GetOwner());
	//从当前对象那移除自己
	if (TargetEnemy.Character)
		TargetEnemy.Character->RemoveFollowingEnemy(character);
	TargetEnemy = Target;
	//给新对象添加自己
	if (Target.Character)
		Target.Character->AddFollowingEnemy(character);
}

void UAwAIComponent::AddViewCharacter(AAwCharacter* Character)
{
	if (bAuthority)
	{
		if (Character->Dead(true))
			return;
		if (Character->CharacterObj.Tag.Contains("Simulate"))
			return;
		bool bViewed = false;
		for (int i = 0; i < ViewCharacterList.Num(); i++)
		{
			if (ViewCharacterList[i].Character == Character)
			{
				bool bInRange = CheckActualInViewRange(Character);
				ViewCharacterList[i].bInRange = bInRange;
				ViewCharacterList[i].bTraced = true;
				if (bInRange)
					ViewCharacterList[i].FadeTime = 0;
				bViewed = true;
				break;
			}

		}
		if (!bViewed)
		{
			FAIFindChaInfo NewCharacter;
			NewCharacter.Character = Character;
			NewCharacter.DistanceXY = FVector::Dist2D(Character->GetActorLocation(), GetOwner()->GetActorLocation());
			NewCharacter.DistanceZ = FMath::Abs(Character->GetActorLocation().Z - GetOwner()->GetActorLocation().Z);
			NewCharacter.FadeTime = 0;
			NewCharacter.bInRange = CheckActualInViewRange(Character);
			NewCharacter.bTraced = true;
			ViewCharacterList.Add(NewCharacter);
		}
	}
}

void UAwAIComponent::RemoveViewCharacter(AAwCharacter* Character)
{
	if (bAuthority)
	{
		for (int i = 0; i < ViewCharacterList.Num(); i++)
		{
			if (ViewCharacterList[i].Character == Character)
			{
				bool bInRange = CheckActualInViewRange(Character);
				ViewCharacterList[i].bInRange = bInRange;
				if(bInRange)
					ViewCharacterList[i].bTraced = false;
				ViewCharacterList[i].CatchTime = 0;
				break;
			}
		}
	}
}

void UAwAIComponent::OnHeardVoice(FVector SourcePos, int Range)
{
	const float CurDis = FVector::Dist(GetOwner()->GetActorLocation(), SourcePos);
	if(CurDis <= Range + HearingRadius)
		Stimuli_Voice.Add(FVoiceStimulus(SourcePos, Range));
}

void UAwAIComponent::UpdateThreatList(float DeltaTime)
{
	//把受击记录转成仇恨数据
	for (FBeTouchByOffense Offended : Stimuli_Offended)
	{
		if(!Offended.Caster)
			continue;
		if(ThreatList.Contains(Offended.Caster->GetUniqueID()))
		{
			ThreatList[Offended.Caster->GetUniqueID()].ThreatValue += Offended.Threat;
			ThreatList[Offended.Caster->GetUniqueID()].Duration = 60.0f;
		}
		else
		{
			FThreatInfo NewThreat = FThreatInfo();
			NewThreat.Target = Offended.Caster;
			NewThreat.Duration = 60.0f;
			NewThreat.ThreatValue = Offended.Threat;
			ThreatList.Add(Offended.Caster->GetUniqueID(), NewThreat);
		}
	}
	Stimuli_Offended.Empty();
	AAwCharacter* MyCharacter = Cast<AAwCharacter>(GetOwner());
	//更新视线看到的目标
	for (FAIFindChaInfo CharacterList : ViewCharacterList)
	{
		if(CharacterList.bTraced && CharacterList.bInRange && MyCharacter->IsEnemy(CharacterList.Character))
		{
			if(ThreatList.Contains(CharacterList.Character->GetUniqueID()))
			{
				if(ThreatList[CharacterList.Character->GetUniqueID()].Duration < 30.0f)
					ThreatList[CharacterList.Character->GetUniqueID()].Duration = 30.0f;
			}
			else
			{
				FThreatInfo NewThreat = FThreatInfo();
				NewThreat.Target = CharacterList.Character;
				NewThreat.Duration = 30.0f;
				NewThreat.ThreatValue = 1;
				ThreatList.Add(CharacterList.Character->GetUniqueID(), NewThreat);
			}
		}
	}
	//把对象已死亡或者超过持续时间的对象移除仇恨列表
	TArray<uint32> RemoveUid;
	for (TTuple<uint32, FThreatInfo> ThreatInfo : ThreatList)
	{
		bool bRemoveCha = false;
		if(ThreatInfo.Value.Target)
		{
			if (!IsValid(ThreatInfo.Value.Target))
			{
				bRemoveCha = true;
			}
			else if(ThreatInfo.Value.Target->Dead(true) )
				bRemoveCha = true;
			else
			{
				if(ThreatInfo.Value.Duration <= 0)
					bRemoveCha = true;
			}
		}
		else
			bRemoveCha = true;
		if(bRemoveCha)
			RemoveUid.Add(ThreatInfo.Key);
	}
	for (uint32 Uid : RemoveUid)
	{
		ThreatList.Remove(Uid);
	}
	//Duration Update
	for (TTuple<uint32, FThreatInfo> ThreatInfo : ThreatList)
	{
		ThreatList[ThreatInfo.Key].Duration -= DeltaTime;
	}
}

AAwCharacter* UAwAIComponent::GetMostThreatTarget()
{
	AAwCharacter* MostThreatTarget = nullptr;
	int MostThreat = 0;
	float MinDis = 999999;
	for (TTuple<uint32, FThreatInfo> ThreatInfo : ThreatList)
	{
		if(ThreatInfo.Value.ThreatValue > MostThreat)
		{
			MostThreat = ThreatInfo.Value.ThreatValue;
			MostThreatTarget = ThreatInfo.Value.Target;
			MinDis = FVector::Dist2D(this->GetOwner()->GetActorLocation(), MostThreatTarget->GetActorLocation());
			continue;
		}
		if(ThreatInfo.Value.ThreatValue == MostThreat)
		{
			float CurDis = FVector::Dist2D(this->GetOwner()->GetActorLocation(), ThreatInfo.Value.Target->GetActorLocation());
			if(CurDis < MinDis)
			{
				MostThreat = ThreatInfo.Value.ThreatValue;
				MostThreatTarget = ThreatInfo.Value.Target;
				MinDis = CurDis;
			}
		}
	}
	return MostThreatTarget;
}

FAICommand UAwAIComponent::CreateMoveToCommand(TArray<FVector> PosList, int StartIndex, int SpeedLevel)
{
	FAICommand AICommand;
	AICommand.AICommandType = "MoveTo";
	AICommand.Pos = PosList[StartIndex];
	AICommand.SpeedLevel = SpeedLevel;
	return AICommand;
}

FAICommand UAwAIComponent::CreateMoveAroundCommand(FVector Pos, float MinRadius, float MaxRadius, bool NegativeDegree)
{
	FAICommand AICommand;
	AICommand.AICommandType = "MoveAround";
	AICommand.Pos = Pos;
	AICommand.MinRadius = MinRadius;
	AICommand.MaxRadius = MaxRadius;
	AICommand.bNegativeDegree = NegativeDegree;
	return AICommand;
}

FAICommand UAwAIComponent::CreateUseActionCommand(FString ActionId, FVector Pos)
{
	FAICommand AICommand;
	AICommand.AICommandType = "UseAction";
	AICommand.Pos = Pos;
	AICommand.ActionId = ActionId;
	AILastActionId = ActionId;
	return AICommand;
}

void UAwAIComponent::ClearActionCommand()
{
	if (bAuthority)
	{
		ThisTickMoveCmd = FAICommand();
		auto character = Cast<AAwCharacter>(GetOwner());
		character->GetCmdComponent()->CleanPlayerInput();
	}
}

void UAwAIComponent::SetUpAIScriptList(TArray<FString> AIScriptIDList)
{
	UAwDataManager* DataManager = UGameplayFuncLib::GetAwDataManager();
	if(DataManager)
	{
		for (int i = 0; i < AIScriptIDList.Num(); i++)
		{
			FAIScriptPart AIScript = DataManager->GetAIScriptById(AIScriptIDList[i]);
			if (AIScript.Id != "")
				AIScriptPartList.Add(AIScript);
		}
	}
}

void UAwAIComponent::SetUpAIOrderList(TArray<FAIOrderModel> OrderModels)
{
	UAwDataManager* DataManager = UGameplayFuncLib::GetAwDataManager();
	if(DataManager)
	{
		for(FAIOrderModel OrderModel : OrderModels)
		{
			FAIOrder NewOrder = FAIOrder();
			NewOrder.OrderId = OrderModel.OrderId;
			NewOrder.CheckTag = OrderModel.CheckTag;
			NewOrder.bReversedCheckTag = OrderModel.bReversedCheckTag;
			for(FString ScriptId : OrderModel.AIScriptIdList)
			{
				FAIScriptPart AIScript = DataManager->GetAIScriptById(ScriptId);
				if (AIScript.Id != "")
					NewOrder.AIScriptIdList.Add(AIScript);
			}
			AIOrderList.Add(NewOrder);
		}
	}
}



FAIFindChaInfo UAwAIComponent::GetClosestDistanceEnemy(bool bViewed, bool bIncludeBlockedTarget)
{
	FAIFindChaInfo CostestTarget;
	AAwCharacter* MyCharacter = Cast<AAwCharacter>(GetOwner());
	//因为在Tick中已经给CharacterList排序了，只要拿数组最前面的就行
	FAIFindChaInfo CostestViewedTarget;
	for (int i = 0; i < ViewCharacterList.Num(); i++)
	{
		if (MyCharacter->IsEnemy(ViewCharacterList[i].Character) && !ViewCharacterList[i].Character->Dead(true))
		{
			if(!bViewed || ViewCharacterList[i].bInRange)
			{
				if(bIncludeBlockedTarget || ViewCharacterList[i].bTraced)
				{
					if(!CostestViewedTarget.Character || ViewCharacterList[i].DistanceXY < CostestViewedTarget.DistanceXY)
						CostestViewedTarget = ViewCharacterList[i];
				}
			}
		}
	}
	if(CostestViewedTarget.Character)
		CostestTarget = CostestViewedTarget;
	return CostestTarget;
}

AAwCharacter* UAwAIComponent::GetClosestDistancePlayer_Svl(bool bViewed, bool bIncludeBlockedTarget)
{
	AAwCharacter* MyCharacter = Cast<AAwCharacter>(GetOwner());
	AAwCharacter* ClosestViewedTarget = UGameplayFuncLib::GetAwGameStateSurvivor()->AthenaTower;
	
	float ClosestDistanceSqr = FMathf::MaxReal;
	if (ClosestViewedTarget && MyCharacter)
	{
		FVector::DistSquared2D(ClosestViewedTarget->GetActorLocation(),MyCharacter->GetActorLocation());
	}
	TArray<AAwCharacter*> TargetList;
	for (auto Player : UGameplayFuncLib::GetAwGameStateSurvivor()->GetPlayerCharacters())
	{
		if(!Player->Dead(true))
		{
			float DistanceSqr = FVector::DistSquared2D(Player->GetActorLocation(),MyCharacter->GetActorLocation());
			if(!ClosestViewedTarget || DistanceSqr < ClosestDistanceSqr)
			{
				ClosestViewedTarget = Player;
				ClosestDistanceSqr = DistanceSqr;
			}
		}
	}
	return ClosestViewedTarget;
}

AAwCharacter* UAwAIComponent::GetClosestDistancePlayerCharacter()
{
	AAwCharacter* MyCharacter = Cast<AAwCharacter>(GetOwner());
	AAwCharacter* ClostestViewedTarget = nullptr;
	float ClostestDistanceSqr = 99999;
	TArray<AAwCharacter*> TargetList;
	for (auto Player : UGameplayFuncLib::GetAwGameStateSurvivor()->GetPlayerCharacters())
	{
		if(!Player->Dead(true))
		{
			if(!ClostestViewedTarget || FVector::DistSquared2D(Player->GetActorLocation(),MyCharacter->GetActorLocation()) < ClostestDistanceSqr)
				ClostestViewedTarget = Player;
		}
	}
	return ClostestViewedTarget;
}

FAIFindChaInfo UAwAIComponent::GetClosestDegreeEnemy(bool bViewed, bool bIncludeBlockedTarget)
{
	FAIFindChaInfo CostestTarget;
	
	AAwCharacter* MyCharacter = Cast<AAwCharacter>(GetOwner());
	float MinViewedDegree = 180;
	FAIFindChaInfo CostestViewedTarget;
	for (int i = 0; i < ViewCharacterList.Num(); i++)
	{
		if (MyCharacter->IsEnemy(ViewCharacterList[i].Character) && !ViewCharacterList[i].Character->Dead(true))
		{
			if(!bViewed || ViewCharacterList[i].bInRange)
			{
				if(bIncludeBlockedTarget || ViewCharacterList[i].bTraced)
				{
					FVector TargetDir = ViewCharacterList[i].Character->GetActorLocation() - GetOwner()->GetActorLocation();
					TargetDir.Normalize();
					const float Degree = FMath::Abs(UKismetMathLibrary::DegAcos(FVector::DotProduct(GetOwner()->GetActorForwardVector(), TargetDir)));
					if(Degree <= MinViewedDegree)
					{
						CostestViewedTarget = ViewCharacterList[i];
						MinViewedDegree = Degree;
					}
				}
			}
		}
	}
	if(CostestViewedTarget.Character)
		CostestTarget = CostestViewedTarget;
	return CostestTarget;
}

FAIFindChaInfo UAwAIComponent::GetClosestAlly()
{
	FAIFindChaInfo CostestTarget;
	AAwCharacter* MyCharacter = Cast<AAwCharacter>(GetOwner());
	//因为在Tick中已经给CharacterList排序了，只要拿数组最前面的就行
	FAIFindChaInfo CostestViewedTarget;
	for (int i = 0; i < ViewCharacterList.Num(); i++)
	{
		if (!MyCharacter->IsEnemy(ViewCharacterList[i].Character) && ViewCharacterList[i].bInRange && ViewCharacterList[i].bTraced && !ViewCharacterList[i].Character->Dead(false))
		{
			CostestViewedTarget = ViewCharacterList[i];
			break;
		}
	}
	if(CostestViewedTarget.Character)
		CostestTarget = CostestViewedTarget;
	return CostestTarget;
}

TArray<FAIFindChaInfo> UAwAIComponent::GetAllViewedCharacter(bool bIncludeBlockedTarget, bool OnlyEnemy, bool IncludeDead)
{
	TArray<FAIFindChaInfo> FindedChaInfo;
	AAwCharacter* MyCharacter = Cast<AAwCharacter>(GetOwner());
	for (auto CurCharacterInfo : ViewCharacterList)
	{
		if (CurCharacterInfo.bInRange && CurCharacterInfo.Character)
		{
			if(IncludeDead || !CurCharacterInfo.Character->Dead(true))
			{
				if(bIncludeBlockedTarget || CurCharacterInfo.bTraced)
				{
					if(!OnlyEnemy || (OnlyEnemy && MyCharacter->IsEnemy(CurCharacterInfo.Character)))
						FindedChaInfo.Add(CurCharacterInfo);
				}
			}
		}
	}
	return FindedChaInfo;
}

FAIOrder UAwAIComponent::MakeMoveToLocationAIOrder(FVector TargetLoc)
{
	FAIOrder NewOrder = FAIOrder();
	if(TargetLoc == FVector::ZeroVector)
		return NewOrder;
	for(FAIOrder AIOrder : AIOrderList)
	{
		if(AIOrder.OrderId == "MoveToLoc")
		{
			NewOrder = AIOrder;
			for(int i = 0; i < NewOrder.AIScriptIdList.Num(); i++)
			{
				for(int j = 0; j < NewOrder.AIScriptIdList[i].Action.Num(); j++)
				{
					if(NewOrder.AIScriptIdList[i].Action[j].FunctionName == "AIMoveToLocation")
					{
						NewOrder.AIScriptIdList[i].Action[j].Params.Add(TargetLoc.ToString());
					}
					else if(NewOrder.AIScriptIdList[i].Action[j].FunctionName == "CheckAIMoveToLoc")
					{
						NewOrder.AIScriptIdList[i].Action[j].Params.Add(TargetLoc.ToString());
					}
				}
			}
			break;
		}
	}
	return NewOrder;
}

FAIOrder UAwAIComponent::MakeMoveToTargetAIOrder(AAwCharacter* Target)
{
	FAIOrder NewOrder = FAIOrder();
	if(!Target)
		return NewOrder;
	for(FAIOrder AIOrder : AIOrderList)
	{
		if(AIOrder.OrderId == "MoveToTarget")
		{
			NewOrder = AIOrder;
			FAIFindChaInfo FocusTarget = FAIFindChaInfo();
			FocusTarget.Character = Target;
			FocusTarget.bInRange = true;
			AAwCharacter* SelfCharacter = Cast<AAwCharacter>(this->GetOwner());
			if(SelfCharacter->IsEnemy(Target))
			{
				FocusEnemy = FocusTarget;
			}
			else
			{
				FocusAlly = FocusTarget;
			}
			break;
		}
	}
	return NewOrder;
}

FAIOrder UAwAIComponent::MakeAssistFirstAIOrder()
{
	FAIOrder NewOrder = FAIOrder();
	for(FAIOrder AIOrder : AIOrderList)
	{
		if(AIOrder.OrderId == "AssistFirst")
		{
			NewOrder = AIOrder;
			break;
		}
	}
	return NewOrder;
}

FAIOrder UAwAIComponent::MakeAutoAIOrder()
{
	FAIOrder NewOrder = FAIOrder();
	NewOrder.OrderId = "Auto";
	return NewOrder;
}

void UAwAIComponent::SetCurAIOrder(FAIOrder AIOrder)
{
	if(AIOrder.OrderId == "")
		return;
	if(CurAIOrder.OrderId != "")
	{
		StopCurAIOrder();
	}
	CurAIOrder = AIOrder;
	//获得插入AIScript的位置
	int InsertIndex = -1;
	if(CurAIOrder.bReversedCheckTag)
	{
		if(CurAIOrder.CheckTag != "")
		{
			for(int j = 0; j < AIScriptPartList.Num(); j++)
			{
				if(AIScriptPartList[j].Tag == CurAIOrder.CheckTag)
					InsertIndex = j;
			}
		}
		InsertIndex++;
	}
	else
	{
		if(CurAIOrder.CheckTag != "")
		{
			InsertIndex = AIScriptPartList.Num();
			for(int j = 0; j < AIScriptPartList.Num(); j++)
			{
				if(AIScriptPartList[j].Tag == CurAIOrder.CheckTag)
				{
					InsertIndex = j;
					break;
				}
			}
		}
		else
			InsertIndex = AIScriptPartList.Num();
	}
	//插入AIScript
	for(int j = CurAIOrder.AIScriptIdList.Num() - 1; j >= 0; j--)
	{
		AddAIScript(CurAIOrder.AIScriptIdList[j], InsertIndex);
	}
}

void UAwAIComponent::StopCurAIOrder()
{
	for(FAIScriptPart AIScript : CurAIOrder.AIScriptIdList)
	{
		for(int i = 0; i < AIScriptPartList.Num(); i++)
		{
			if(AIScriptPartList[i].Id == AIScript.Id && AIScriptPartList[i].Tag == "AIOrder")
			{
				AIScriptPartList.RemoveAt(i);
				break;
			}
		}
	}
}

void UAwAIComponent::AddAIScriptById(FString AIScriptId, int Index)
{
	UAwDataManager* DataManager = UGameplayFuncLib::GetAwDataManager();
	if(DataManager)
	{
		const FAIScriptPart AIScript = DataManager->GetAIScriptById(AIScriptId);
		if (AIScript.Id != "")
		{
			if(Index >= 0 && Index < AIScriptPartList.Num())
			{
				AIScriptPartList.Insert(AIScript, Index);
			}
			else
				AIScriptPartList.Add(AIScript);
		}
	}
}

void UAwAIComponent::AddAIScript(FAIScriptPart AIScript, int Index)
{
	if (AIScript.Id != "")
	{
		if(Index >= 0 && Index < AIScriptPartList.Num())
		{
			AIScriptPartList.Insert(AIScript, Index);
		}
		else
			AIScriptPartList.Add(AIScript);
	}
}

void UAwAIComponent::RemoveAIScript(FString AIScriptId)
{
	for(int i = 0; i < AIScriptPartList.Num(); i++)
	{
		if(AIScriptPartList[i].Id == AIScriptId)
		{
			AIScriptPartList.RemoveAt(i);
			break;
		}
	}
}

void UAwAIComponent::AddOffsenseInfo(FOffenseInfo NewOffenseInfo, AAwCharacter* Attacker)
{
	if(Attacker)
	{
		BeHitRecord.Add(Attacker, GWorld->GetTimeSeconds());
	}
}

void UAwAIComponent::SetInputFromAICommand(FAICommand AICommand)
{
	if (bAuthority)
	{
		AAwCharacter* MyCharacter = Cast<AAwCharacter>(GetOwner());
		if (AICommand.AICommandType == "MoveTo")
		{
			ThisTickMoveCmd = AICommand;
		}
		else if (AICommand.AICommandType == "MoveAround")
		{
			ThisTickMoveCmd = AICommand;
		}
		else if (AICommand.AICommandType == "UseAction")
		{
			FVector NextDir;
			if (AICommand.Pos != FVector(0, 0, 0))
				NextDir = AICommand.Pos;
			else
				NextDir = MyCharacter->GetActorForwardVector();
			//MyCharacter->SetFaceDirection(FVector2D(NextDir.X, NextDir.Y), true);
			MyCharacter->SetAIMoveAndFaceDir(
				FVector2D(NextDir.X, NextDir.Y),
				FVector2D(NextDir.X, NextDir.Y), 0
			);
			MyCharacter->AddAIAction(AICommand.ActionId);
			// UKismetSystemLibrary::PrintString(this,  GetOwner()->GetName().Append(FString(" UseAction: ").Append(AICommand.ActionId)));
			UE_LOG(LogTemp,Log,TEXT("%s UseAction: %s"),*GetOwner()->GetName(),*AICommand.ActionId);
		}
	}
}

FVector UAwAIComponent::GetNextLocByNav(FVector TargetPos, float StopFollowRange)
{
	AAwCharacter* MyCharacter = Cast<AAwCharacter>(GetOwner());
	AAIController* AIController = Cast<AAIController>(MyCharacter->Controller);
	if (AIController)
	{	
		//UNavigationSystemV1* NavSys = FNavigationSystem::GetCurrent<UNavigationSystemV1>(GetWorld());
		FAIMoveRequest MoveReq;
		MoveReq.SetGoalLocation(TargetPos);
		MoveReq.SetAcceptanceRadius(PawnRadius + StopFollowRange);
		MoveReq.SetReachTestIncludesAgentRadius(true);
		MoveReq.SetAllowPartialPath(true);
		MoveReq.SetUsePathfinding(true);
		MoveReq.SetProjectGoalLocation(true);
		if (NaviQueryFilter)
			MoveReq.SetNavigationFilter(NaviQueryFilter);
		else
			MoveReq.SetNavigationFilter(AIController->GetDefaultNavigationFilterClass());

		FPathFindingQuery PFQuery;
		const bool bValidQuery = AIController->BuildPathfindingQuery(MoveReq, PFQuery);
		if (bValidQuery)
		{
			FNavPathSharedPtr Path;
			AIController->FindPathForMoveRequest(MoveReq, PFQuery, Path);
			if (Path)
			{
				if (Path.Get()->GetPathPoints().Num() > 1)
				{
					FVector NavNextLoc = GetLocationByCompareWithLastPath(Path.Get()->GetPathPoints(), StopFollowRange);
					return	NavNextLoc;
				}
					
			}
		}
	}
	return MyCharacter->GetActorLocation();
}

void UAwAIComponent::ChangePathNodeQueue(FString Id, int Index)
{
	if(UGameplayFuncLib::GetAwGameState()->AwMapPathNodeQueues.Contains(Id))
	{
		PathNodeQueue = UGameplayFuncLib::GetAwGameState()->AwMapPathNodeQueues[Id];
		PathNodeQueue.NodeIndex = Index;
	}
}

FVector UAwAIComponent::GetTargetNavPathEndPoint(FVector TargetPos)
{
	AAwCharacter* MyCharacter = Cast<AAwCharacter>(GetOwner());
	AAIController* AIController = Cast<AAIController>(MyCharacter->Controller);
	if (AIController)
	{
		FAIMoveRequest MoveReq;
		MoveReq.SetGoalLocation(TargetPos);
		MoveReq.SetAcceptanceRadius(PawnRadius);
		MoveReq.SetReachTestIncludesAgentRadius(true);
		MoveReq.SetAllowPartialPath(true);
		MoveReq.SetUsePathfinding(true);
		MoveReq.SetProjectGoalLocation(true);
		MoveReq.SetNavigationFilter(AIController->GetDefaultNavigationFilterClass());

		FPathFindingQuery PFQuery;
		const bool bValidQuery = AIController->BuildPathfindingQuery(MoveReq, PFQuery);
		if (bValidQuery)
		{
			FNavPathSharedPtr Path;
			AIController->FindPathForMoveRequest(MoveReq, PFQuery, Path);
			if (Path)
			{
				 FNavigationPath* NavPath = Path.Get();
				 return NavPath->GetEndLocation();
			}
		}
	}
	return MyCharacter->GetActorLocation();
}

FVector UAwAIComponent::GetRandomTargetReachableNavPointInRadius(FVector CenterPoint, float Radius,float MinDistance)
{
	FNavLocation result;

	if (MinDistance>=Radius)
	{
		if (UNavigationSystemV1::GetNavigationSystem(GetWorld())->GetRandomReachablePointInRadius(CenterPoint, Radius, result))
		{
			return result.Location;
		}
		else
		{
			return CenterPoint;
		}
	}
	//取大于最小距离 小于半径范围内
	do
	{
		if (!UNavigationSystemV1::GetNavigationSystem(GetWorld())->GetRandomReachablePointInRadius(CenterPoint, Radius, result))
		{
			result.Location = CenterPoint;
			break;
		}	
	} while (FVector::Distance(CenterPoint, result.Location) < MinDistance);
	
	
	return result.Location;
}



FVector UAwAIComponent::GetMoveAroundDir(FVector Pos, float MinRadius, float MaxRadius, bool NegativeDegree)
{
	float CurDis = FVector::DistSquaredXY(GetOwner()->GetActorLocation(), Pos);
	if (CurDis < FMath::Square(MinRadius))
	{
		return FVector(0,0,0);
	}
	FVector CurDir = (Pos - GetOwner()->GetActorLocation()).GetSafeNormal();
	FVector NextDir;
	//向左或右90度
	if (NegativeDegree)
		NextDir = CurDir.RotateAngleAxis(-90.0f, FVector(0, 0, 1));
	else
		NextDir = CurDir.RotateAngleAxis(90.0f, FVector(0, 0, 1));
	float NextDis = ((GetOwner()->GetActorLocation() + NextDir * PawnRadius) - Pos).SizeSquared2D();
	if ((NextDis < FMath::Square(MaxRadius)) && (NextDis > FMath::Square(MinRadius)))
	{
		return NextDir;
	}

	float OnceRotateAngle = 5.0f;
	for (int i = 1; i * OnceRotateAngle < 90; i++)
	{
		//向内旋转OnceRotateAngle度
		if (NegativeDegree)
			NextDir = NextDir.RotateAngleAxis(OnceRotateAngle, FVector(0, 0, 1));
		else
			NextDir = NextDir.RotateAngleAxis(OnceRotateAngle * -1, FVector(0, 0, 1));
		NextDis = ((GetOwner()->GetActorLocation() + NextDir * PawnRadius) - Pos).SizeSquared2D();
		if ((NextDis < FMath::Square(MaxRadius)) && (NextDis > FMath::Square(MinRadius)))
		{
			return NextDir;
		}
	}
	
	return NextDir;

}

FVector UAwAIComponent::GetLocationByCompareWithLastPath(TArray<FNavPathPoint> NewPath, float StopFollowRange)
{	
	static FVector lastTarget;
	AAwCharacter* MyCharacter = dynamic_cast<AAwCharacter*>(GetOwner());
	if (!LastNavPath.Num())
	{
		CurPathIndex = 1;
		LastNavPath = NewPath;
	}
	else
	{
		bool bSimilarPath = false;
		for (int i = 1; i < NewPath.Num(); i++)
		{
			if (i < LastNavPath.Num())
			{
				if (NewPath[i].Location.Equals(LastNavPath[i].Location), 5.0f)
				{
					bSimilarPath = true;
					continue;
				}
			}
			break;
		}
		if (bSimilarPath)
		{
			int NextPathIndex = FMath::Clamp(CurPathIndex, 1, NewPath.Num() - 1);
			FVector SelfLoc = MyCharacter->GetActorLocation();
			SelfLoc.Z = SelfLoc.Z - PawnHalfHeight;
			float DisToTarget = FVector::DistSquared(SelfLoc, NewPath[NextPathIndex].Location);
			if (NextPathIndex + 1 == NewPath.Num() && DisToTarget < FMath::Square(PawnRadius + StopFollowRange))
			{
				return SelfLoc;
			}
			if (DisToTarget < FMath::Square(PawnRadius))
			{
				CurPathIndex = NextPathIndex + 1;
			}	
		}
		else
			CurPathIndex = 1;
	}
	LastNavPath = NewPath;
	CurPathIndex = FMath::Clamp(CurPathIndex, 1, LastNavPath.Num() - 1);
	return LastNavPath[CurPathIndex].Location;
}

bool UAwAIComponent::CheckActualInViewRange(AAwCharacter* Character)
{
	const float CurDis = (GetOwner()->GetActorLocation() - Character->GetActorLocation()).SizeSquared2D();
	const float CurZDis = FMath::Abs(GetOwner()->GetActorLocation().Z - Character->GetActorLocation().Z);
	
	float ActualViewRange = 0;
	if(GetAllViewedCharacter(false, true, false).Num() > 0)
		ActualViewRange = FMath::Square(SightRadius + Character->GetAIComponent()->ViewRadius);
	else
		ActualViewRange = FMath::Square(RelaxSightRadius + Character->GetAIComponent()->ViewRadius);
	
	FVector TargetDir = Character->GetActorLocation() - GetOwner()->GetActorLocation();
	TargetDir.Normalize();
	const float Degree = FMath::Abs(UKismetMathLibrary::DegAcos(FVector::DotProduct(GetOwner()->GetActorForwardVector(), TargetDir)));
	return (CurDis <= ActualViewRange && CurZDis <= SightZRadius && Degree <= SightHalfAngleDregee);
}

bool UAwAIComponent::CheckActualOutViewRange(AAwCharacter* Character)
{
	const float CurDis = (GetOwner()->GetActorLocation() - Character->GetActorLocation()).SizeSquared2D();
	const float CurZDis = FMath::Abs(GetOwner()->GetActorLocation().Z - Character->GetActorLocation().Z);
	
	float ActualViewRange = 0;
	if(GetAllViewedCharacter(false, true, false).Num() > 0)
		ActualViewRange = FMath::Square(LoseSightRadius + Character->GetAIComponent()->ViewRadius);
	else
		ActualViewRange = FMath::Square(RelaxSightRadius + Character->GetAIComponent()->ViewRadius);
	
	FVector TargetDir = Character->GetActorLocation() - GetOwner()->GetActorLocation();
	TargetDir.Normalize();
	const FVector OwnerDir = GetOwner()->GetActorForwardVector();
	const float Degree = FMath::Abs(UKismetMathLibrary::DegAcos(FVector::DotProduct(OwnerDir, TargetDir)));
	return (CurDis > ActualViewRange || CurZDis > SightZRadius || Degree > LoseSightHalfAngleDregee);
}

bool UAwAIComponent::CheckLocationInViewRange(FVector CheckLocation)
{
	const float CurDis = (GetOwner()->GetActorLocation() - CheckLocation).SizeSquared2D();
	const float CurZDis = FMath::Abs(GetOwner()->GetActorLocation().Z - CheckLocation.Z);
	float ActualViewRange = 0;
	if(GetAllViewedCharacter(false, true, false).Num() > 0)
		ActualViewRange = FMath::Square(SightRadius);
	else
		ActualViewRange = FMath::Square(RelaxSightRadius);
	FVector TargetDir = CheckLocation - GetOwner()->GetActorLocation();
	TargetDir.Normalize();
	const float Degree = FMath::Abs(UKismetMathLibrary::DegAcos(FVector::DotProduct(GetOwner()->GetActorForwardVector(), TargetDir)));
	return (CurDis <= ActualViewRange && CurZDis <= SightZRadius && Degree <= SightHalfAngleDregee);
}

void UAwAIComponent::UpdateViewCharacterList(float DeltaTime)
{
	TArray<int> RemoveViewCharacterList;
	//ViewCharacterList
	for (int i = 0; i < ViewCharacterList.Num(); i++)
	{
		if (ViewCharacterList[i].Character == nullptr)
		{
			RemoveViewCharacterList.Add(i);
			continue;
		}
		ViewCharacterList[i].DistanceXY = FVector::Dist2D(ViewCharacterList[i].Character->GetActorLocation(), GetOwner()->GetActorLocation());
		ViewCharacterList[i].DistanceZ = FMath::Abs(ViewCharacterList[i].Character->GetActorLocation().Z - GetOwner()->GetActorLocation().Z);
		//进行二次距离判断
		if(ViewCharacterList[i].bTraced)
		{
			if (!CheckActualOutViewRange(ViewCharacterList[i].Character))
			{
				ViewCharacterList[i].bInRange = true;
				ViewCharacterList[i].CatchTime += DeltaTime;
			}
			else
			{
				ViewCharacterList[i].bInRange = false;
				ViewCharacterList[i].CatchTime = 0;
			}
		}
		else
		{
			if (CheckActualInViewRange(ViewCharacterList[i].Character))
				ViewCharacterList[i].bInRange = true;
			ViewCharacterList[i].FadeTime += DeltaTime;
		}
	}
	if (RemoveViewCharacterList.Num())
	{
		for (int i = 0; i < RemoveViewCharacterList.Num(); i++)
		{
			ViewCharacterList.RemoveAt(RemoveViewCharacterList[i] - i);
		}
	}
	ViewCharacterList.Sort();
}

AController* UAwAIComponent::GetOwnerController()
{
	AAwCharacter* AwCharacter = Cast<AAwCharacter>(GetOwner());
	if (AwCharacter)
	{
		return AwCharacter->Controller;
	}
	return nullptr;
}

void UAwAIComponent::AddUseActionTag(FString Tag)
{
	if (UseActionTags.Contains(Tag) == false)
		UseActionTags.Add(Tag);
}

void UAwAIComponent::SetUseActionTags(TArray<FString> Tags)
{
	ClearUseActionTags();
	for (const FString TheTag : Tags)
		AddUseActionTag(TheTag);
}

void UAwAIComponent::ClearUseActionTags()
{
	UseActionTags.Empty();
}

void UAwAIComponent::OnBeOffended(FTransform SourcePos, AAwCharacter* HitCaster, int Threat)
{
	this->Stimuli_Offended.Add(FBeTouchByOffense(SourcePos, HitCaster, Threat));
}

void UAwAIComponent::OnAllyCall(const AActor* Ally)
{
	if (!Ally) return;
	this->Stimuli_AllySignal.Add(FAllySignal(Ally->GetActorTransform()));
}