// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/Object.h"
#include "VoiceStimulus.generated.h"

/**
 * 
 */
USTRUCT()
struct FVoiceStimulus
{
	GENERATED_BODY()
public:
	//发出声音的位置
	UPROPERTY()
	FVector Position = FVector::ZeroVector;

	//声音的影响范围
	UPROPERTY()
	int VoiceRange = 0;

	//这个刺激的持续时间
	UPROPERTY()
	float Duration = 0;

	FVoiceStimulus(){}
	FVoiceStimulus(FVector VoicePosition, int Range):
		Position(VoicePosition), VoiceRange(Range), Duration(15){}
};
