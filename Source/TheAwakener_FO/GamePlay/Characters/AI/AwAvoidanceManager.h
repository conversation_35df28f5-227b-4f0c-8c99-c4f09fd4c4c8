// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "UObject/NoExportTypes.h"
#include "AwAvoidanceManager.generated.h"


/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UAwAvoidanceManager : public UObject
{
	GENERATED_BODY()
protected:
	TMap<AAwCharacter*, FVector> ThisTickMoveCharacters;

public:
	void Tick(float DeltaTime);

	void AddCharacterMove(AAwCharacter* MoveCharacter, FVector MoveTarget);

	bool CheckCanMove(AAwCharacter* MoveCharacter, FVector MoveTarget);

	AAwCharacter* CheckMoveBlockByCharacter(AAwCharacter* MoveCharacter, FVector MoveTarget,FVector& BlockTargetMove);

	FVector ModifyAvoidMove(AAwCharacter* MoveCharacter, FVector MoveTarget, bool bClockWiseRot);
};
