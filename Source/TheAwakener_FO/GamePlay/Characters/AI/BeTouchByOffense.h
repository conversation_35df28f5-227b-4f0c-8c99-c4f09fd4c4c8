// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/Object.h"
#include "BeTouchByOffense.generated.h"

class AAwCharacter;
/**
 * 受到攻击的时候的记录
 */
USTRUCT()
struct FBeTouchByOffense
{
	GENERATED_BODY()
public:
	//打中我的物体的打中时候的Transform
	UPROPERTY()
	FTransform HitTransform;

	//打中我的物体的Caster
	UPROPERTY()
	AAwCharacter* Caster = nullptr;

	UPROPERTY()
	int Threat = 0;

	FBeTouchByOffense(){}
	FBeTouchByOffense(FTransform TransformWhileHit, AAwCharacter* HitCaster, int ThreatValue):
		HitTransform(TransformWhileHit), Caster(HitCaster), Threat(HitCaster ? ThreatValue : 0){}
};

