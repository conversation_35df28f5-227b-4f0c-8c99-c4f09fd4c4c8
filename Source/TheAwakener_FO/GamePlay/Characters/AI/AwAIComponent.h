// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "TheAwakener_FO/GamePlay/Characters/Creation/MobModel.h"
#include "AIController.h"
#include "AllySignal.h"
#include "BeTouchByOffense.h"
#include "NavigationSystem.h"
#include "VoiceStimulus.h"
#include "TheAwakener_FO/FunctionLibrary/DataFuncLib.h"
#include "TheAwakener_FO/GamePlay/Characters/Move/PathNodeQueue.h"
#include "AwAIComponent.generated.h"

class AAwCharacter;
//AI通过感知系统查找到的Character信息
USTRUCT(BlueprintType)
struct FAIFindChaInfo
{
	GENERATED_BODY()
public:
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	AAwCharacter* Character = nullptr;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool bInRange = true;
	//只在视线检测中使用，记录目标是否无阻挡
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool bTraced = true;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float DistanceXY = 0.0f;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float DistanceZ = 0.0f;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float CatchTime = 0.0f;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float FadeTime = 0.0f;

	bool operator < (const FAIFindChaInfo& NewChaInfo) const
	{
		return DistanceXY < NewChaInfo.DistanceXY;
	}
	
};

//一个AIScript的Json配置
USTRUCT(BlueprintType)
struct FAIScriptPart
{
	GENERATED_BODY()
public:
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString Id = "";
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FJsonFuncData> Condition;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FJsonFuncData> OnReady;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FJsonFuncData> Action;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString Tag = "";

	static FAIScriptPart FromJson(TSharedPtr<FJsonObject> JsonObj)
	{
		FAIScriptPart NewAIScriptPart = FAIScriptPart();
		NewAIScriptPart.Id = UDataFuncLib::AwGetStringField(JsonObj, "Id");
		for (const TSharedPtr<FJsonValue> Value : UDataFuncLib::AwGetArrayField(JsonObj, "Condition"))
			NewAIScriptPart.Condition.Add(UDataFuncLib::SplitFuncNameAndParams(Value->AsString()));
		for (const TSharedPtr<FJsonValue> Value : UDataFuncLib::AwGetArrayField(JsonObj, "OnReady"))
			NewAIScriptPart.OnReady.Add(UDataFuncLib::SplitFuncNameAndParams(Value->AsString()));
		for (const TSharedPtr<FJsonValue> Value : UDataFuncLib::AwGetArrayField(JsonObj, "Action"))
			NewAIScriptPart.Action.Add(UDataFuncLib::SplitFuncNameAndParams(Value->AsString()));
		NewAIScriptPart.Tag = UDataFuncLib::AwGetStringField(JsonObj, "Tag");

		return NewAIScriptPart;
	}
};
//AIScript的返回值，通过这个结构再去计算和构成发给ActionComponent的数据
USTRUCT(BlueprintType)
struct FAICommand
{
	GENERATED_BODY()
public:
	FString AICommandType = "";
	FString ActionId = "";
	FVector Pos = FVector();
	int Index = 0;
	float MinRadius = 0.0f;
	float MaxRadius = 0.0f;
	bool bNegativeDegree = true;
	int SpeedLevel = 0; 
};

UENUM(BlueprintType)
enum class EBuddyAIOrderType: uint8
{
	MoveToTarget,
	MoveToPlayer,
	AssistFirst,
	Auto
};

//外部给AI发送的命令，会有若干个AIScript信息
USTRUCT(BlueprintType)
struct FAIOrder
{
	GENERATED_BODY()
public:
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString OrderId = "";
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool bReversedCheckTag = false;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString CheckTag = "";
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FAIScriptPart> AIScriptIdList;
	
};

//一个仇恨对象的信息
USTRUCT(BlueprintType)
struct FThreatInfo
{
	GENERATED_BODY()
public:
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	AAwCharacter* Target = nullptr;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int ThreatValue = 0;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float Duration = 0.0f;
	
	
};

UCLASS( ClassGroup=(Custom), meta=(BlueprintSpawnableComponent))
class THEAWAKENER_FO_API UAwAIComponent : public UActorComponent
{
	GENERATED_BODY()


protected:
	virtual void BeginPlay() override;
	
	//当前Character的可被看见范围
	UPROPERTY(EditDefaultsOnly, Category = "Sight")
	float ViewRadius = 500;
	//当前Character未进入战斗的视野范围
	UPROPERTY(EditDefaultsOnly, Category = "Sight")
	float RelaxSightRadius = 2000;
	//当前Character的视野范围
	UPROPERTY(EditDefaultsOnly, Category = "Sight")
	float SightRadius = 2000;
	//当前Character的视野范围的上下Z高度
	UPROPERTY(EditDefaultsOnly, Category = "Sight")
	float SightZRadius = 500;
	UPROPERTY(EditDefaultsOnly, Category = "Sight")
	float SightHalfAngleDregee = 45;
	//当前Character的丢失目标的视野范围
	UPROPERTY(EditDefaultsOnly, Category = "Sight")
	float LoseSightRadius = 3000;
	UPROPERTY(EditDefaultsOnly, Category = "Sight")
	float LoseSightHalfAngleDregee = 50;
	//当前Character的可被听见范围
	UPROPERTY(EditDefaultsOnly, Category = "Hearing")
	float NoiseRadius = 500;
	//当前Character的听觉范围
	UPROPERTY(EditDefaultsOnly, Category = "Hearing")
	float HearingRadius = 1000;

	//该Component只在权威端运行，在构造时判断
	bool bAuthority;

	UPROPERTY(BlueprintReadOnly)
	TArray<FAIFindChaInfo> ViewCharacterList;

	//当前根据规则确定的目标敌人
	UPROPERTY(BlueprintReadOnly)
	FAIFindChaInfo TargetEnemy;
	//当前根据规则确定的目标队友
	FAIFindChaInfo TargetAlly;

	//当前锁定的敌人
	FAIFindChaInfo FocusEnemy;
	//当前锁定的队友
	FAIFindChaInfo FocusAlly;

	//保存的目标坐标位置
	FVector AITargetLocation = FVector::Zero();

private:
	AController* GetOwnerController();
	
	//记录生成时间，用于延迟开始运行逻辑
	float DurationTime;
	
	//寻路相关
	TArray<FNavPathPoint> LastNavPath;

	int CurPathIndex;
	//与寻路相关的Pawn数据
	float PawnRadius = 50.0f;
	float PawnHalfHeight = 45.0f;

	FAICommand ThisTickMoveCmd = FAICommand();

	bool bClockWiseInNavAvoidRot = true;

	

	void UpdateViewCharacterList(float DeltaTime);
	
	FVector GetNextLocByNav(FVector TargetPos, float StopFollowRange);

	FVector GetMoveAroundDir(FVector Pos, float MinRadius, float MaxRadius, bool NegativeDegree = false);

	FVector GetLocationByCompareWithLastPath(TArray<FNavPathPoint> NewPath, float StopFollowRange);

public:
	UFUNCTION(BlueprintCallable)
	AAwCharacter* GetTargetCharacter(){return TargetEnemy.Character;};
	UAwAIComponent();
	
	void SetUp(FMobModel MobModel);

	virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;
	UFUNCTION(BlueprintCallable)
	void SetInputFromAICommand(FAICommand AICommand);
	
	//AI脚本片段列表
	UPROPERTY(EditDefaultsOnly, BlueprintReadWrite, Category = "AIScript")
	TArray<FAIScriptPart> AIScriptPartList;
	UPROPERTY(EditDefaultsOnly, BlueprintReadWrite, Category = "AIScript")
	TArray<FAIOrder> AIOrderList;
	
	//实际的停止寻路范围 = Character的胶囊体半径 + StopRangeWhenMoveFollow
	UPROPERTY(EditDefaultsOnly, Category = "MoveFollow")
	float StopRangeWhenMoveFollow = 75.0f;
	UPROPERTY(EditDefaultsOnly, Category = "MoveFollow")
	float StopZDisWhenMoveFollow = 200.0f;
	UPROPERTY(EditDefaultsOnly, Category = "MoveAround")
	float MinDisWhenMoveAround = 100.0f;
	UPROPERTY(EditDefaultsOnly, Category = "MoveAround")
	float MaxDisWhenMoveAround = 200.0f;
	UPROPERTY(EditDefaultsOnly, Category = "MoveAround")
	bool bNegativeDegreeWhenMoveAround = true;


	FMobModel MobModel = FMobModel();

	//是否刚好所有的刺激源都清空
	UPROPERTY()
	bool JustClearStimulus = false;
	UPROPERTY(BlueprintReadWrite)
	TSubclassOf<UNavigationQueryFilter> NaviQueryFilter;
	//刺激源：声音
	UPROPERTY()
	TArray<FVoiceStimulus> Stimuli_Voice;
	//刺激源：受击
	UPROPERTY()
	TArray<FBeTouchByOffense> Stimuli_Offended;
	//刺激源：队友召唤
	UPROPERTY()
	TArray<FAllySignal> Stimuli_AllySignal;
	
	//一些策划用于逻辑的参数
	UPROPERTY(BlueprintReadWrite)
	TMap<FString, FString> Params;

	//AI上一次挑选的动作
	UPROPERTY()
	FString AILastActionId;

	//AI Wait后要做的动作
	UPROPERTY()
	FString WaitForActionId;

	//AI 动作的Wait时间
	UPROPERTY()
	float ActionWaitTime = 0.0f;

	//当前是否有需要wait后执行的动作
	UPROPERTY()
	bool bHasWaitForAction = false;

	UFUNCTION()
	void AddWaitForAction(FString ActionId, float WaitTime)
	{
		bHasWaitForAction = true;
		WaitForActionId = ActionId;
		ActionWaitTime = WaitTime;
	}

	/**
	 *当前巡逻的路点
	 *这是一连串的轨道，请通过AI脚本为其赋值或者改写
	 */
	UPROPERTY()
	FPathNodeQueue PathNodeQueue;

	UFUNCTION(BlueprintCallable, Category = "Path")
	void ChangePathNodeQueue(FString Id, int Index);

	UPROPERTY(BlueprintReadOnly)
	bool Paused = false;
	//用于一些需要暂停AIScript的功能（比如：冻结等）
	UPROPERTY()
	bool bWorkAIAScript = true;

	//当前正在执行的AIOrder
	FAIOrder CurAIOrder = FAIOrder();

	TMap<AAwCharacter* , float> BeHitRecord;

	//计算当前动作朝向的方法
	FJsonFuncData TargetDirFunc;
	
	//GetNavPoint
	UFUNCTION(BlueprintCallable, Category = "NavMove")
		FVector GetTargetNavPathEndPoint(FVector TargetPos);
	UFUNCTION(BlueprintCallable, Category = "NavMove")
		FVector GetRandomTargetReachableNavPointInRadius(FVector CenterPoint,float Radius,float MinDistance);
	//-------GetTarget---------//
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|AIComponent|Target")
		FAIFindChaInfo GetTargetEnemy() { return TargetEnemy; };
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|AIComponent|Target")
		FAIFindChaInfo GetTargetAlly() { return TargetAlly; };

	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|AIComponent|Target")
		FAIFindChaInfo GetFocusEnemy() { return FocusEnemy; };
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|AIComponent|Target")
		FAIFindChaInfo GetFocusAlly() { return FocusAlly; };

	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|AIComponent|Target")
		FVector GetTargetLocation() { return AITargetLocation; };

	//获得最近的敌人
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|AIComponent|Target")
		FAIFindChaInfo GetClosestDistanceEnemy(bool bViewed = false, bool bIncludeBlockedTarget = false);
		
	//获得最近的敌人 幸存者特供，只搜索塔和玩家
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|AIComponent|Target")
		AAwCharacter* GetClosestDistancePlayer_Svl(bool bViewed = false, bool bIncludeBlockedTarget = false);
	//获得最近的敌人 幸存者特供，只搜索塔和玩家
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|AIComponent|Target")
		AAwCharacter* GetClosestDistancePlayerCharacter();
	//获得最近的敌人
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|AIComponent|Target")
		FAIFindChaInfo GetClosestDegreeEnemy(bool bViewed = false, bool bIncludeBlockedTarget = false);
	//获得最近的友方
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|AIComponent|Target")
		FAIFindChaInfo GetClosestAlly();
	
	//获得所有看见的Character
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|AIComponent|Target")
		TArray<FAIFindChaInfo> GetAllViewedCharacter(bool bIncludeBlockedTarget = false, bool OnlyEnemy = true, bool IncludeDead = false);
	
	//获得当前需要的旋转朝向
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|AIComponent|Target")
		FVector GetTargetDirection();
	
	//-------GetTarget---------//

	//-------SetTarget---------//
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|AIComponent|Target")
		void SetTargetEnemy(FAIFindChaInfo Target);
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|AIComponent|Target")
		void SetTargetAlly(FAIFindChaInfo Target) { TargetAlly = Target; };

	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|AIComponent|Target")
		void SetFocusEnemy(FAIFindChaInfo Target) { FocusEnemy = Target; };
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|AIComponent|Target")
		void SetFocusAlly(FAIFindChaInfo Target) { FocusAlly = Target; };

	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|AIComponent|Target")
		void SetTargetLocation(FVector Location) { AITargetLocation = Location; };
	//-------SetTarget---------//
	
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|AIComponent|Sight")
		float GetViewRadius() { return ViewRadius; };
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|AIComponent|Sight")
		void SetViewRadius(float NewRadius) { ViewRadius = NewRadius; };

	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|AIComponent|Sight")
	float GetRelaxSightRadius() { return RelaxSightRadius; }; 
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|AIComponent|Sight")
	void SetRelaxSightRadius(float NewRadius) { RelaxSightRadius = NewRadius; };
	
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|AIComponent|Sight")
		float GetSightRadius() { return SightRadius; }; 
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|AIComponent|Sight")
		void SetSightRadius(float NewRadius) { SightRadius = NewRadius; };

	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|AIComponent|Sight")
		float GetSightZRadius() { return SightZRadius; }; 
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|AIComponent|Sight")
		void SetSightZRadius(float NewRadius) { SightZRadius = NewRadius; };

	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|AIComponent|Sight")
		float GetHalfSightDegree() { return SightHalfAngleDregee; }; 
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|AIComponent|Sight")
		void SetHalfSightDegree(float NewDegree) { SightHalfAngleDregee = NewDegree; };

	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|AIComponent|Sight")
		float GetLoseSightRadius() { return LoseSightRadius; };
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|AIComponent|Sight")
		void SetLoseSightRadius(float NewRadius) { LoseSightRadius = NewRadius; };

	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|AIComponent|Sight")
		float GetLoseHalfSightDegree() { return LoseSightHalfAngleDregee; }; 
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|AIComponent|Sight")
		void SetLoseHalfSightDegree(float NewDegree) { LoseSightHalfAngleDregee = NewDegree; };

	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|AIComponent|Hearing")
		float GetNoiseRadius() { return NoiseRadius; };
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|AIComponent|Hearing")
		void SetNoiseRadius(float NewRadius) { NoiseRadius = NewRadius; };

	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|AIComponent|Hearing")
		float GetHearingRadius() { return HearingRadius; };
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|AIComponent|Hearing")
		void SetHearingRadius(float NewRadius) { HearingRadius = NewRadius; };
	

	//因为 AIPerceptionComponent 无法在运行时修改Sight的范围，因此通过下面函数去进行二次确认
	//在看到新的目标时确认是否实际看到
	bool CheckActualInViewRange(AAwCharacter* Character);
	//已看到对象是否出了实际视野范围
	bool CheckActualOutViewRange(AAwCharacter* Character);

	//检测目标点是否在AI的视野范围内
	bool CheckLocationInViewRange(FVector CheckLocation);

	//-----------Add & Remove Character By AIPerception---------------//
	//新增已看见对象列表(从AIPerception处相应后调用)
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|AIComponent|Sight")
	void AddViewCharacter(AAwCharacter* Character);
	//把对象标记为超出视野范围(从AIPerception处相应后调用)
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|AIComponent|Sight")
	void RemoveViewCharacter(AAwCharacter* Character);

	/**
	 * 添加AI听见声音的刺激源
	 * @param SourcePos 刺激源坐标
	 */
	void OnHeardVoice(FVector SourcePos, int Range);
	/**
	 * 添加AI受击的刺激源
	 * @param SourcePos 刺激源坐标
	 */
	void OnBeOffended(FTransform SourcePos, AAwCharacter* HitCaster, int Threat);
	/**
	 * 添加AI被友人呼叫的刺激源
	 * @param Ally 呼叫者，未必是个角色对吧
	 */
	void OnAllyCall(const AActor* Ally);

	//----------- Add & Remove Character By AIPerception ---------------//

	//---------------Threat System------------------------------------//

	//暂时先只记录来源是伤害的仇恨，之后如果有技能可以单独设置仇恨值最高或者最低，也是修改这个表
	//Key = FThreatInfo.Target.Uid
	TMap<uint32, FThreatInfo> ThreatList;

	//根据Stimuli_Offended 和视觉刺激（看到一个没有仇恨的目标，仇恨值+1）更新仇恨
	void UpdateThreatList(float DeltaTime);

	//在ThreatList中选出一个最高仇恨值的
	AAwCharacter* GetMostThreatTarget();

	//---------------Threat System------------------------------------//

	//----------------- AI Script --------------------//
	//生成一个MoveTo的AICommand
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|AIComponent|AICommand")
	FAICommand CreateMoveToCommand(TArray<FVector> PosList, int StartIndex, int SpeedLevel = 2);
	//生成一个MoveAround的AICommand
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|AIComponent|AICommand")
	FAICommand CreateMoveAroundCommand(FVector Pos, float MinRadius, float MaxRadius, bool NegativeDegree = false);
	//生成一个UseAction的AICommand
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|AIComponent|AICommand")
	FAICommand CreateUseActionCommand(FString ActionId, FVector Pos = FVector(1, 0, 0));
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|AIComponent|AICommand")
	void ClearActionCommand();
	
	//添加AIScript
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|AIComponent")
	void AddAIScriptById(FString AIScriptId, int Index = -1);
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|AIComponent")
	void AddAIScript(FAIScriptPart AIScript, int Index = -1);
	
	//去除AIScript
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|AIComponent")
	void RemoveAIScript(FString AIScriptId);

	//----------------- AI Script --------------------//

	//----------------- Rogue AI Script --------------------//
	//用来记录每个动作的CD
	TMap<FString, float> ActionCDList;



	//----------------- Rogue AI Script --------------------//

	/**
	 * AI使用技能的一些标记，这些标记需要通过外部（包括AI脚本告知）
	 * 这些标记将改变AI使用技能的思路，这些思路在AIPostCommand这个AnimNotify中
	 */
	UPROPERTY()
	TArray<FString> UseActionTags;

	//---------AIOrder------------//

	void SetUpAIScriptList(TArray<FString> AIScriptIDList);

	void SetUpAIOrderList(TArray<FAIOrderModel> OrderModels);

	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|AIComponent|AIOrder")
	FAIOrder MakeMoveToLocationAIOrder(FVector TargetLoc);

	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|AIComponent|AIOrder")
	FAIOrder MakeMoveToTargetAIOrder(AAwCharacter* Target);

	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|AIComponent|AIOrder")
	FAIOrder MakeAssistFirstAIOrder();

	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|AIComponent|AIOrder")
	FAIOrder MakeAutoAIOrder();
	
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|AIComponent|AIOrder")
	void SetCurAIOrder(FAIOrder AIOrder);

	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|AIComponent|AIOrder")
	void StopCurAIOrder();

	//---------AIOrder------------//
	
	//添加对该AI的攻击记录

	void AddOffsenseInfo(FOffenseInfo NewOffenseInfo, AAwCharacter* Attacker);
	/**
	 * 添加一个UseAction的Tag，这个Tag会作用于AIPostCommand这个Notify
	 * 在这个Notify中，回调函数(AAwCharacter*, TArray<FString>, TArray<FString>)=>FString
	 * 的第二个参数就是Add过得所有的UseActionTag。
	 * @param Tag 要添加的UseActionTag
	 */
	void AddUseActionTag(FString Tag);

	/**
	 * 改写UseActionTags，
	 * 这些Tags会作用于AIPostCommand这个Notify
	 * 在这个Notify中，回调函数(AAwCharacter*, TArray<FString>, TArray<FString>)=>FString
	 * 的第二个参数就是Add过得所有的UseActionTag。
	 * @param Tag 要添加的UseActionTag
	 */
	void SetUseActionTags(TArray<FString> Tags);

	//清空UseActionTags
	void ClearUseActionTags();

	/**
	 * 获得当前的UseActionTags
	 */
	TArray<FString> CurrentUseActionTags() const {return UseActionTags;}
	
};
