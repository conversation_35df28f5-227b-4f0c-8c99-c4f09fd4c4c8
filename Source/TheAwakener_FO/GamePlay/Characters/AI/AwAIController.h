// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "AIController.h"
#include "Perception/AISenseConfig_Sight.h"
#include "AwAIController.generated.h"

/**
 * 
 */

UCLASS()
class THEAWAKENER_FO_API AAwAIController : public AAIController
{
	GENERATED_BODY()
public:
	UPROPERTY(BlueprintReadWrite, VisibleDefaultsOnly, Category = AI, meta = (AllowPrivateAccess = "true"))
	UAIPerceptionComponent* AIPerception;

	//添加声音感知
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AI|PawnSensing")
	UAISenseConfig_Sight* AiConfigSight;

	AAwAIController(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get());
	
	UFUNCTION()
	void InitBehaviorTree(FString BehaviorTreePath, FString BlackBoardPath);

	UFUNCTION()
	void AIPerceptionUpdate(AActor* Actor, FAIStimulus Stimulus);
};
