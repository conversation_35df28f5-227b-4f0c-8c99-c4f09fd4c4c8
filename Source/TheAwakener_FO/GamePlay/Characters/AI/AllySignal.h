// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/Object.h"
#include "AllySignal.generated.h"

/**
 * 同伴给的信号
 */
USTRUCT()
struct FAllySignal
{
	GENERATED_BODY()
public:
	//信息发出的位置
	UPROPERTY()
	FTransform SignalOrigin = FTransform::Identity;

	//持续时间
	UPROPERTY()
	float Duration = 0;

	FAllySignal(){}
	FAllySignal(FTransform Pos):
		SignalOrigin(Pos),Duration(60){}
};