// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/Object.h"
#include "AIPickActionInfo.generated.h"

//要挑选动作的信息
USTRUCT()
struct FAIPickActionData
{
	GENERATED_BODY()
public:
	//动作的id
	UPROPERTY()
	FString ActionId;

	//3个FightingWill会挑选他的最小范围
	UPROPERTY()
	TArray<float> MinRange;

	//3个FightingWill会挑选他的最大范围
	UPROPERTY()
	TArray<float> MaxRange;

	//3个FightingWill下选择的权重
	UPROPERTY()
	TArray<float>  PickRate;

	//敌人在正面的倍率，去乘以rate用的
	UPROPERTY()
	float EnemyInFrontRate = 1;

	//敌人在背后的倍率，乘以rate用的
	UPROPERTY()
	float EnemyInBackRate = 0;

	FAIPickActionData(){}
	
	static FAIPickActionData FromJson(TSharedPtr<FJsonObject> JsonObj);
};
//候选的动作
USTRUCT()
struct FAIPickActionCandidate
{
	GENERATED_BODY()
public:
	UPROPERTY()
	FString ActionId;
	UPROPERTY()
	float Rate = 0;
	UPROPERTY()
	bool bWaitAction = false;
	
	FAIPickActionCandidate(){}
	FAIPickActionCandidate(FString Action, float PickRate, bool bWait):
	ActionId(Action), Rate(PickRate), bWaitAction(bWait){}
};

/**
 * AI选择一个技能的信息
 */
USTRUCT()
struct FAIPickActionInfo
{
	GENERATED_BODY()
public:
	//这个AI数据组的Id是什么
	UPROPERTY()
	FString Id;

	//在不同FightingWill下选择休息的概率
	UPROPERTY()
	TArray<float> RestRate;

	//做进攻动作的信息
	UPROPERTY()
	TArray<FAIPickActionData> Actions;

	//做休息动作的信息
	UPROPERTY()
	TArray<FAIPickActionData> RestActions;

	static FAIPickActionInfo FromJson(TSharedPtr<FJsonObject> JsonObj);
};

//****************************Rogue Mob***********************************

USTRUCT()
struct FRogueAIRangeWeightInfo
{
	GENERATED_BODY()
public:
	UPROPERTY()
	float MinRange = 0;

	UPROPERTY()
	float MaxRange = 0;
	
	UPROPERTY()
	float Weight = 0;

	FRogueAIRangeWeightInfo(){}
	
	static FRogueAIRangeWeightInfo FromJson(TSharedPtr<FJsonObject> JsonObj);
};

USTRUCT()
struct FRogueAICheckBuffInfo
{
	GENERATED_BODY()
public:
	UPROPERTY()
	FString BuffId = "";

	UPROPERTY()
	int BuffStack = 0;
	
	UPROPERTY()
	float Weight = 0;

	FRogueAICheckBuffInfo(){}
	
	static FRogueAICheckBuffInfo FromJson(TSharedPtr<FJsonObject> JsonObj);
};

USTRUCT()
struct FRogueAISwitchWeightInfo
{
	GENERATED_BODY()
public:
	UPROPERTY()
	FString SwitchId = "";
	
	UPROPERTY()
	float MinValue = 0;

	UPROPERTY()
	float MaxValue = 0;
	
	UPROPERTY()
	float Weight = 0;

	FRogueAISwitchWeightInfo(){}
	
	static FRogueAISwitchWeightInfo FromJson(TSharedPtr<FJsonObject> JsonObj);
};

//要挑选动作的信息
USTRUCT()
struct FRogueAIPickActionData
{
	GENERATED_BODY()
public:
	//动作的id
	UPROPERTY()
	FString ActionId;

	//基础权重
	UPROPERTY()
	float BaseWeight = 1;

	//距离权重
	UPROPERTY()
	TArray<FRogueAIRangeWeightInfo> DistanceWeightList;

	//血量权重
	UPROPERTY()
	TArray<FRogueAIRangeWeightInfo> HPWeightList;

	//检测指定buff的层数在多少层后添加的权重
	UPROPERTY()
	TArray<FRogueAICheckBuffInfo> BuffWeightList;

	//敌人位置角度的权重
	UPROPERTY()
	TArray<FRogueAIRangeWeightInfo> TargetInDegreeWeight;

	//不同Switch的值的权重
	UPROPERTY()
	TArray<FRogueAISwitchWeightInfo> SwitchWeight;

	//敌人在地面的权重
	UPROPERTY()
	float TargetOnGroundWeight = 0;

	//敌人在空中的权重
	UPROPERTY()
	float TargetInAirWeight = 0;

	//敌人在翻滚的权重
	UPROPERTY()
	float TargetInDodgeWeight = 0;

	//敌人在受击时的权重
	UPROPERTY()
	float TargetInHurtWeight = 0;

	//当前世界难度>0时的权重
	UPROPERTY()
	float bClearedGameWeight = 0;

	//敌人在镜头外时的权重
	UPROPERTY()
	float bOutofCameraWeight = 0;

	//动作释放时是否Wait
	UPROPERTY()
	bool bWaitAction = 0;
	
	//动作CD(在Min和Max范围内随机)
	UPROPERTY()
	float MinActionCD = 0;

	UPROPERTY()
	float MaxActionCD = 0;

	FRogueAIPickActionData(){}
	
	static FRogueAIPickActionData FromJson(TSharedPtr<FJsonObject> JsonObj);
};

/**
 * AI选择一个技能的信息
 */
USTRUCT()
struct FRogueAIPickActionInfo
{
	GENERATED_BODY()
public:
	//这个Mob的MobId
	UPROPERTY()
	FString Id;

	//做进攻动作的信息
	UPROPERTY()
	TArray<FRogueAIPickActionData> Actions;
	

	static FRogueAIPickActionInfo FromJson(TSharedPtr<FJsonObject> JsonObj);
};
