// Fill out your copyright notice in the Description page of Project Settings.


#include "AwAvoidanceManager.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"


void UAwAvoidanceManager::Tick(float DeltaTime)
{
	ThisTickMoveCharacters.Empty();
}

void UAwAvoidanceManager::AddCharacterMove(AAwCharacter* MoveCharacter, FVector MoveTarget)
{
	ThisTickMoveCharacters.Add(MoveCharacter, MoveTarget);
}

bool UAwAvoidanceManager::CheckCan<PERSON>ove(AAwCharacter* MoveCharacter, FVector MoveTarget)
{
	//即使分层return 由于无法稳定获取仅周围对象 可以尝试射线抓周围不过消耗也不会很低 现在遍历对象存在过多 存在潜在危害 

	const float CurCharacterRadius = MoveCharacter->GetCapsuleComponent()->GetScaledCapsuleRadius();
	for (auto pc : UGameplayFuncLib::GetAllLocalAwPlayerControllers())
	{
		if (!pc)continue;
		const AAwCharacter* Player = pc->CurCharacter;
		if (FVector::Dist(MoveTarget, Player->GetActorLocation()) < Player->GetCapsuleComponent()->GetScaledCapsuleRadius() + CurCharacterRadius)
		{
			return false;
		}
	
		for(auto MovedCharacter : ThisTickMoveCharacters)
		{
			if(!MovedCharacter.Key) continue;
			if(!MovedCharacter.Key->Dead() && MovedCharacter.Key != MoveCharacter  && !MoveCharacter->IsEnemy(MovedCharacter.Key))
			{
				float CharacterRadius = MovedCharacter.Key->GetCapsuleComponent()->GetScaledCapsuleRadius();
				if(FVector::Dist(MoveTarget, MovedCharacter.Value) < CharacterRadius + CurCharacterRadius)
					return false;
			}
		}

		for(auto Character : UGameplayFuncLib::GetAwGameState()->AllCharacters)
		{
			if(!Character.Key) continue;
			if(ThisTickMoveCharacters.Contains(Character.Key)) continue;
			if(!Character.Key->Dead() && Character.Key != MoveCharacter && !MoveCharacter->IsEnemy(Character.Key))
			{
				float CharacterRadius = Character.Key->GetCapsuleComponent()->GetScaledCapsuleRadius();
				if(FVector::Dist(MoveTarget, Character.Key->GetActorLocation()) < CharacterRadius + CurCharacterRadius)
					return false;
			}
		}
	}
	return true;
}

AAwCharacter* UAwAvoidanceManager::CheckMoveBlockByCharacter(AAwCharacter* MoveCharacter, FVector MoveTarget,  FVector& BlockTargetMove)
{
	//即使分层return 由于无法稳定获取仅周围对象 可以尝试射线抓周围不过消耗也不会很低 现在遍历对象存在过多 存在潜在危害 
	AAwCharacter* BlockCharacter = nullptr;
	BlockTargetMove = FVector::ZeroVector;
	const float CurCharacterRadius = MoveCharacter->GetCapsuleComponent()->GetScaledCapsuleRadius();
	for (auto pc : UGameplayFuncLib::GetAllLocalAwPlayerControllers())
	{
		if (!pc)continue;
		AAwCharacter* Player = pc->CurCharacter;
		if (FVector::Dist(MoveTarget, Player->GetActorLocation()) < Player->GetCapsuleComponent()->GetScaledCapsuleRadius() + CurCharacterRadius)
		{
			BlockCharacter = Player;
			BlockTargetMove = ThisTickMoveCharacters.Find(Player)?*(ThisTickMoveCharacters.Find(Player)):FVector::ZeroVector;
			return BlockCharacter;
		}
	
		for (auto MovedCharacter : ThisTickMoveCharacters)
		{
			if (!MovedCharacter.Key) continue;
			if (!MovedCharacter.Key->Dead() && MovedCharacter.Key != MoveCharacter && !MoveCharacter->IsEnemy(MovedCharacter.Key))
			{
				float CharacterRadius = MovedCharacter.Key->GetCapsuleComponent()->GetScaledCapsuleRadius();
				if (FVector::Distance(MoveTarget, MovedCharacter.Value) < CharacterRadius + CurCharacterRadius)
				{
					BlockCharacter = MovedCharacter.Key;
					BlockTargetMove = MovedCharacter.Value-MovedCharacter.Key->GetActorLocation();
					return BlockCharacter;
				}
			}
		}

		for (auto Character : UGameplayFuncLib::GetAwGameState()->AllCharacters)
		{
			if (!Character.Key) continue;
			if (ThisTickMoveCharacters.Contains(Character.Key)) continue;
			if (!Character.Key->Dead() && Character.Key != MoveCharacter && !MoveCharacter->IsEnemy(Character.Key))
			{
				float CharacterRadius = Character.Key->GetCapsuleComponent()->GetScaledCapsuleRadius();
				if (FVector::Distance(MoveTarget,Character.Key->GetActorLocation()) < CharacterRadius + CurCharacterRadius)
				{
					BlockCharacter = Character.Key;
					BlockTargetMove = FVector::ZeroVector;
					return BlockCharacter;
				}
			}
		}
	}
	return BlockCharacter;
}

FVector UAwAvoidanceManager::ModifyAvoidMove(AAwCharacter* MoveCharacter, FVector MoveTarget, bool bClockWiseRot)
{
	//处理不够完善  如使用ue框架 建议使用CrowdAI或者RVO相关		
	if (CheckCanMove(MoveCharacter, MoveTarget))
	{	
		return MoveTarget;
	}
	
	FVector ActualLoc = MoveTarget;

	/*
	float OnceChangeAngle = 0.2;
	if (!bClockWiseRot)
		OnceChangeAngle = -0.2;
	
	for (int i = 0; i < 900; i++)
	{
		SCOPE_CYCLE_COUNTER(STAT_AvoidanceLoop1);
		FVector MoveOffset = MoveTarget - MoveCharacter->GetActorLocation();
		ActualLoc = MoveCharacter->GetActorLocation() + MoveOffset.RotateAngleAxis(OnceChangeAngle * i, FVector(0, 0, 1));
		if (CheckCanMove(MoveCharacter, ActualLoc))
		{
			return ActualLoc;
		}
	}

	for (int i = 0; i < 900; i++)
	{
		SCOPE_CYCLE_COUNTER(STAT_AvoidanceLoop2);
		FVector MoveOffset = MoveTarget - MoveCharacter->GetActorLocation();
		ActualLoc = MoveCharacter->GetActorLocation() + MoveOffset.RotateAngleAxis(-1.0f * OnceChangeAngle * i, FVector(0, 0, 1));
		if (CheckCanMove(MoveCharacter, ActualLoc))
		{
			return ActualLoc;
		}
	}
	*/
			
	//如果当前未与任何角色相撞
	//if (CheckCanMove(MoveCharacter, MoveCharacter->GetActorLocation()))

	float ChangeAngle =0.f;
	float ClockWiseAngle = 0.f;
	float AntiClockWiseAngle = 0.f;
	FVector ClockWiseLoc = ActualLoc;
	FVector AntiClockWiseLoc = ActualLoc;
	//loc坐标系下的目标偏移
	FVector MoveOffset = MoveTarget - MoveCharacter->GetActorLocation();
	//阻挡对象速度方向 如果打算尝试用总速度方向 叉乘外圆两侧切线异号 为区域圆锥外作为判断非相撞 可以使用该FVector 这里先不做复杂处理
	FVector BlockTargetMoveVector=FVector::ZeroVector ;
	//AAwCharacter* BlockCharacter = nullptr;
	AAwCharacter* BlockCharacter = CheckMoveBlockByCharacter(MoveCharacter, MoveCharacter->GetActorLocation(), BlockTargetMoveVector);
	//如果相交大于步长 则尝试远离对方
	if (BlockCharacter)
	{
		float StepLen = MoveOffset.Size();
		float Dist = FVector2D::Distance(FVector2D(MoveCharacter->GetActorLocation()), FVector2D(BlockCharacter->GetActorLocation()));
		float Radius = BlockCharacter->GetCapsuleComponent()->GetScaledCapsuleRadius() + MoveCharacter->GetCapsuleComponent()->GetScaledCapsuleRadius();
		if (Radius-Dist>StepLen)
		{
			FVector BackDir = MoveCharacter->GetActorLocation() - (BlockCharacter->GetActorLocation() + BlockTargetMoveVector);
			BackDir.Normalize();
			return BackDir * MoveOffset.Size();
		}
	}

	do
	{
		BlockCharacter = CheckMoveBlockByCharacter(MoveCharacter, ClockWiseLoc, BlockTargetMoveVector);
		if (BlockCharacter)
		{
			FVector NewOffset = ClockWiseLoc - MoveCharacter->GetActorLocation();
			//半径合 即外切圆切线垂线长度
			float R = BlockCharacter->GetCapsuleComponent()->GetScaledCapsuleRadius() + MoveCharacter->GetCapsuleComponent()->GetScaledCapsuleRadius();
			// 两圆心连线 即直角斜边长度
			float D = FVector2D::Distance(FVector2D(MoveCharacter->GetActorLocation()), FVector2D(BlockCharacter->GetActorLocation() ));
				
			float Sin = R / D;
				
			float angle = FMath::Asin(Sin)*180/PI;

			FVector TargetDirection = BlockCharacter->GetActorLocation() - MoveCharacter->GetActorLocation();
				
			//新偏移目标相对当前block对象方向是顺还是逆
			float CrossPower = FVector2D::CrossProduct(FVector2D(NewOffset).GetSafeNormal(), FVector2D(TargetDirection).GetSafeNormal())>0?1:-1;

			//目标位置与block对象方向向量偏移角度
			float OffsetAngle = FMath::Acos(FVector2D::DotProduct(FVector2D(NewOffset).GetSafeNormal(), FVector2D(TargetDirection).GetSafeNormal()))*180/PI;
			//正/负角度累积
			if (FMath::IsNearlyZero(angle + OffsetAngle * CrossPower,0.01f))
			{
				break;
			}

			ClockWiseAngle += FMath::Abs(angle + OffsetAngle*CrossPower);

			ClockWiseLoc = MoveCharacter->GetActorLocation() + MoveOffset.RotateAngleAxis(ClockWiseAngle, FVector(0, 0, 1));

		}
	} while (BlockCharacter && FMath::Abs(ClockWiseAngle) < 180);
	ChangeAngle = ClockWiseAngle;
	ActualLoc = ClockWiseLoc;
	//换个方向
	if(BlockCharacter)
	{
		do
		{
			BlockCharacter = CheckMoveBlockByCharacter(MoveCharacter, AntiClockWiseLoc, BlockTargetMoveVector);
			if (BlockCharacter)
			{
				FVector NewOffset = AntiClockWiseLoc - MoveCharacter->GetActorLocation();
				//半径合 即外切圆切线垂线长度
				float R = BlockCharacter->GetCapsuleComponent()->GetScaledCapsuleRadius() + MoveCharacter->GetCapsuleComponent()->GetScaledCapsuleRadius();
				// 两圆心连线 即直角斜边长度
				float D = FVector2D::Distance(FVector2D(MoveCharacter->GetActorLocation()), FVector2D(BlockCharacter->GetActorLocation()));

				float Sin = R / D;

				float angle = FMath::Asin(Sin) * 180 / PI;

				FVector TargetDirection = BlockCharacter->GetActorLocation() - MoveCharacter->GetActorLocation();

				//偏移相对当前block对象方向是顺还是逆
				float CrossPower = FVector2D::CrossProduct(FVector2D(NewOffset).GetSafeNormal(), FVector2D(TargetDirection).GetSafeNormal()) > 0 ? -1 : 1;

				//目标位置与block对象方向向量偏移角度
				float OffsetAngle = FMath::Acos(FVector2D::DotProduct(FVector2D(NewOffset).GetSafeNormal(), FVector2D(TargetDirection).GetSafeNormal())) * 180 / PI;

				if (FMath::IsNearlyZero(angle + OffsetAngle * CrossPower, 0.01f))
				{
					break;
				}
				//正/负角度累积
				AntiClockWiseAngle += FMath::Abs(angle + OffsetAngle * CrossPower) * (-1);


				AntiClockWiseLoc = MoveCharacter->GetActorLocation() + MoveOffset.RotateAngleAxis(AntiClockWiseAngle, FVector(0, 0, 1));

			}
		} while (BlockCharacter && FMath::Abs(AntiClockWiseAngle) < 180);
		if (!FMath::IsNearlyZero(AntiClockWiseAngle)&& (!FMath::IsNearlyZero(ClockWiseAngle)))
		{
			ChangeAngle = AntiClockWiseAngle;
			ActualLoc = AntiClockWiseLoc;
			//取最小转角 连续目标形成钩状会产生左右摇摆 暂弃用
			//ChangeAngle = FMath::Abs(ClockWiseAngle)< FMath::Abs(AntiClockWiseAngle)? ClockWiseAngle: AntiClockWiseAngle;
			//ActualLoc = FMath::Abs(ClockWiseAngle) < FMath::Abs(AntiClockWiseAngle) ? ClockWiseLoc : AntiClockWiseLoc;
		}
	}
		
	if (!BlockCharacter )
	{
		return ActualLoc;
	}
	//无路可走则原地
	return MoveCharacter->GetActorLocation();
	
	/*
	const float CurCharacterRadius = MoveCharacter->GetCapsuleComponent()->GetScaledCapsuleRadius();
	for(auto MovedCharacter : ThisTickMoveCharacters)
	{
		if(!MovedCharacter.Key) continue;
		if(!MovedCharacter.Key->Dead() && MovedCharacter.Key != MoveCharacter && !MoveCharacter->IsEnemy(MovedCharacter.Key))
		{
			float CharacterRadius = MovedCharacter.Key->GetCapsuleComponent()->GetScaledCapsuleRadius();
			if(FVector::Dist(MoveTarget, MovedCharacter.Value) < CharacterRadius + CurCharacterRadius)
			{
				FVector MoveDir = MoveCharacter->GetActorLocation() - MovedCharacter.Value;
				MoveDir.Normalize();
				return MoveCharacter->GetActorLocation() + MoveDir*100 ;
			}
				
		}
	}
	for(auto Character : UGameplayFuncLib::GetAwGameState()->AllCharacters)
	{
		if(!Character.Key) continue;
		if(ThisTickMoveCharacters.Contains(Character.Key)) continue;
		if(!Character.Key->Dead() && Character.Key != MoveCharacter && !MoveCharacter->IsEnemy(Character.Key))
		{
			float CharacterRadius = Character.Key->GetCapsuleComponent()->GetScaledCapsuleRadius();
			if(FVector::Dist(MoveTarget, Character.Key->GetActorLocation()) < CharacterRadius + CurCharacterRadius)
			{
				FVector MoveDir = MoveCharacter->GetActorLocation() - Character.Key->GetActorLocation();
				MoveDir.Normalize();
				return MoveCharacter->GetActorLocation() + MoveDir * 100;
			}
		}
	}
	return MoveCharacter->GetActorLocation();
	*/
}

