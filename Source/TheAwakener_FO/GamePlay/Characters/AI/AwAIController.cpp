// Fill out your copyright notice in the Description page of Project Settings.


#include "AwAIController.h"

#include "TheAwakener_FO/FunctionLibrary/ResourceFuncLib.h"
#include "BehaviorTree/BlackboardData.h"
#include "BehaviorTree/BehaviorTree.h"
#include "Perception/AIPerceptionComponent.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"

AAwAIController::AAwAIController(const FObjectInitializer& ObjectInitializer)
	: Super(ObjectInitializer)
{
	AIPerception = CreateDefaultSubobject<UAIPerceptionComponent>("AIPerception");
	AiConfigSight = CreateDefaultSubobject<UAISenseConfig_Sight>(TEXT("AIConfigSight"));

	if(AIPerception)
	{
		//以下对应蓝图中声音感知组件的设置
		AiConfigSight->SightRadius = 10000.0f;
		AiConfigSight->LoseSightRadius = 13000.0f;
		AiConfigSight->PeripheralVisionAngleDegrees = 180.0f;
		AiConfigSight->DetectionByAffiliation.bDetectNeutrals = true;
		AiConfigSight->DetectionByAffiliation.bDetectEnemies = true;
		AiConfigSight->DetectionByAffiliation.bDetectFriendlies = true;

		AIPerception->ConfigureSense(*AiConfigSight);
		AIPerception->SetDominantSense(UAISenseConfig_Sight::StaticClass());

		AIPerception->OnTargetPerceptionUpdated.AddDynamic(this, &AAwAIController::AIPerceptionUpdate);
	}
}

void AAwAIController::InitBehaviorTree(FString BehaviorTreePath, FString BlackBoardPath)
{
	if(!BlackBoardPath.IsEmpty())
	{
		const FString BlackBoardAssetPath = UResourceFuncLib::GetAssetPath(BlackBoardPath);
		UBlackboardData* BlackBoardAsset = LoadObject<UBlackboardData>(this,*BlackBoardAssetPath);
		//const UBlackboardComponent*& MyBlackBoard = GetBlackboardComponent();
		//UseBlackboard(BlackBoardAsset,);
	}
	if(!BehaviorTreePath.IsEmpty())
	{
		const FString BehaviorTreeAssetPath = UResourceFuncLib::GetAssetPath(BehaviorTreePath);
		UBehaviorTree* BehaviorTreeAsset = LoadObject<UBehaviorTree>(this,*BehaviorTreeAssetPath);
		RunBehaviorTree(BehaviorTreeAsset);
	}
}

void AAwAIController::AIPerceptionUpdate(AActor* Actor, FAIStimulus Stimulus)
{
	AAwCharacter* TargetCharacter = Cast<AAwCharacter>(Actor);
	if(TargetCharacter)
	{
		if(UAIPerceptionSystem::GetSenseClassForStimulus(this, Stimulus) == UAISenseConfig_Sight::StaticClass())
		{
			AAwCharacter* OwnerCharacter = Cast<AAwCharacter>(this->GetPawn());
			if(Stimulus.WasSuccessfullySensed())
			{
				OwnerCharacter->GetAIComponent()->AddViewCharacter(TargetCharacter);
			}
			else
			{
				OwnerCharacter->GetAIComponent()->RemoveViewCharacter(TargetCharacter);
			}
		}
	}
}

