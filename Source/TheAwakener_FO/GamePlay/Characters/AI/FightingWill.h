// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "FightingWill.generated.h"

/**
 * 战斗意志，也就是AI用的“状态”，这个属性不需要同步，因为主机操控了AI的行为
 */
USTRUCT(BlueprintType)
struct FFightingWill
{
	GENERATED_BODY()
public:
	/**
	 *情绪值，如果这个值上限是10000，如果这个值被降低为0，就会自然的增加Level值
	 */
	UPROPERTY(BlueprintReadOnly)
	int Value = 10000;

	//当前的情绪等级，他只是一个等级，根据不同的AI会对它有不同的解读，初始默认是1级。
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int Level = 1;

	//最大的情绪等级值
	int MaxLevel = 2;
	
	FFightingWill(){};
	FFightingWill(int InitLevel, int TotalLevel = 3):
		Level(InitLevel), MaxLevel(TotalLevel - 1){};

	/**
	 * 改变Value值
	 * @param ModValue 要改变多少
	 * @param LevelRule 如果Value经过改变小于0，就会以此变化，但是会遵从Level=[0, MaxLevel]这个规则
	 * @param LoopLevel 在等级减少到最小之后会变成最大或者到达最大之后会从0开始加
	 * @return 是否导致Value小于0了
	 */
	bool ModifyValue(int ModValue, int LevelRule = -1, bool LoopLevel = false);

	/**
	 * 直接改变Level值，但不会超出约定范围，设置后Value通常会变回10000
	 * @param ToLevel 要去的等级
	 * @param ResetValueIfSameLevel 是否同等级也重置Value到10000
	 */
	void ModifyLevel(int ToLevel, bool ResetValueIfSameLevel = true);

	/**
	 * Value值的百分比
	 */
	float ValuePercentage() const
	{
		return Value / 10000.000f;
	}
};

