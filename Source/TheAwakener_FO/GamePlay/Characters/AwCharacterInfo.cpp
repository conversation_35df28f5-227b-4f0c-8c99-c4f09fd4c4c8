// Fill out your copyright notice in the Description page of Project Settings.


#include "AwCharacterInfo.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GameData/MobProp.h"
#include "TheAwakener_FO/GameFramework/Manager/AwDataManager.h"
DEFINE_STAT(STAT_SurvivorTest);
FAwCharacterInfo FAwCharacterInfo::FromThing(const FThingObj& ThingObj)
{
	const FMobModel Model = UGameplayFuncLib::GetDataManager()->GetMobModelById(ThingObj.Id);
	
	FAwCharacterInfo Res;
	Res.Name = "";
	Res.ClassId = Model.Id;
	//TODO:
	// Res.CharacterLevel = Model.level; //TODO:
	// Res.Buffs //TODO:
	// Res.Weapon; //TODO:
	// Res.Equipments; //TODO:
	
	return Res;
}

void FAwCharacterInfo::AttrRecheck()
{
	SCOPE_CYCLE_COUNTER(STAT_SurvivorTest);
	FChaProp OldChaProp = this->CurProperty;
	this->ResetBaseProperty();
	this->CurProperty = this->BaseProp + this->EquipmentProp + this->WeaponProp;
	this->CurProperty = CaluPropAfterBuff(this->CurProperty);
	if (!CurProperty.PropResEqual(OldChaProp))
	{
		ResRecheck(CurProperty-OldChaProp);
	}
	
	CurrentRes.HP = FMath::Clamp(CurrentRes.HP,0,CurProperty.HP);
	CurrentRes.MP = FMath::Clamp(CurrentRes.MP,0,CurProperty.MP);
	CurrentRes.SP = FMath::Clamp(CurrentRes.SP,0,CurProperty.SP);
	CurrentRes.AP = FMath::Clamp(CurrentRes.AP,0,CurProperty.AP);
}

void FAwCharacterInfo::ResRecheck(FChaProp ThisTickOffset)
{
	//上限改变引起当前值改变
	
	CurrentRes.HP += ThisTickOffset.HP>0?ThisTickOffset.HP:0;
	CurrentRes.MP +=ThisTickOffset.MP>0?ThisTickOffset.MP:0;
	CurrentRes.SP +=ThisTickOffset.SP>0?ThisTickOffset.SP:0;
	CurrentRes.AP +=ThisTickOffset.AP>0?ThisTickOffset.AP:0;
	
}


void FAwCharacterInfo::ResetBaseProperty()
{
	if (UGameplayFuncLib::IsRogueMode()) 
	{
		UAwDataManager* DataManager = UGameplayFuncLib::GetDataManager();
		if(!ClassId.IsEmpty())
		{
			BaseProp.ResetToZero();
			if (DataManager)
			{
				const FRoleType Role = DataManager->GetRolePawnByClassId(ClassId);
				BaseProp.HP = Role.HP;
				BaseProp.PAttack = Role.Attack;
				BaseProp.MAttack = Role.Attack;
				FChaProp Prop = DataManager->GetBattleClassModelById(ClassId).Potential;
				BaseProp.MoveSpeed =Prop.MoveSpeed ;
				BaseProp.CriticalChance =  Prop.CriticalChance;
				BaseProp.CriticalRate =  Prop.CriticalRate;
				BaseProp.ActionSpeed = 100;
			}
			BaseProp.AP = 1000;
			WeaponSet.MainHand.Model.AttackPower.SetDamageZero();
			WeaponSet.OffHand.Model.AttackPower.SetDamageZero();
		}
		else if (!MobId.IsEmpty())
		{
			BaseProp.MoveSpeed = BasePropStar.MoveSpeed;
			if (UGameplayFuncLib::GetAwDataManager()->DebugConfig.IncreaseMobPropsWithLevel)
			{
				BaseProp.HP = FChaProp::GetRogueMobHp(BasePropStar.HP, Level);
				BaseProp.PAttack = FChaProp::GetRogueMobAtk(BasePropStar.PAttack, Level);
				BaseProp.MAttack = FChaProp::GetRogueMobAtk(BasePropStar.MAttack, Level);
			}
			else
			{
				//先用外部表格数据
				FMobProp MobProp = UGameplayFuncLib::GetAwDataManager()->GetMobProp(MobId,MobAlterId);
				BaseProp.HP = MobProp.HP;
				BaseProp.PAttack = MobProp.Atk;
				BaseProp.MAttack = MobProp.Atk;
				if(BaseProp.MoveSpeed.Num() > 1)
					BaseProp.MoveSpeed[1] = MobProp.MoveSpeed;
			}

			// if(Level <= 1)
			// {
			// 	BaseProp.HP *= 0.5;
			// }
			
			WeaponSet.MainHand.Model.AttackPower.SetDamageZero();
			WeaponSet.OffHand.Model.AttackPower.SetDamageZero();
			BaseProp.ActionSpeed = 100;
		}
	}
	else
	{
		if (this->ClassId.IsEmpty() == false)
		{
			//TODO 暂时写死
			const FBattleClassModel BattleClassModel = UGameplayFuncLib::GetAwDataManager()->GetBattleClassModelById(ClassId);
		
			FChaProp ChaProp = FChaProp();
			ChaProp.HP = 10;
			ChaProp.PAttack = 10;
			ChaProp.PDefense = 10;
			ChaProp.MoveSpeed = BattleClassModel.Potential.MoveSpeed;
			this->BaseProp = FChaProp::GetChaPropByStar(ChaProp, this->Level, EChaPotentialType::PlayerCharacter);
			this->BaseProp.AP= 1000;
			this->BaseProp.ActionSpeed = 100;
		}else if (this->MobId.IsEmpty() == false)
		{
			const FMobModel MobModel = UGameplayFuncLib::GetAwDataManager()->GetMobModelById(MobId,MobAlterId);
			EChaPotentialType ChaPotentialType = EChaPotentialType::PlayerCharacter;
			switch (MobRank)
			{
			case EMobRank::Boss : ChaPotentialType = EChaPotentialType::Boss;break;
			case EMobRank::Elite: ChaPotentialType = EChaPotentialType::EliteMob;break;
			case EMobRank::Normal: ChaPotentialType = EChaPotentialType::NormalMob;break;
			}
			this->BaseProp.ResetToZero();
			this->BaseProp = FChaProp::GetChaPropByStar(MobModel.BaseProp, this->Level, ChaPotentialType);
			//初始觉醒能量 写死1000
			this->BaseProp.AP= 1000;
			this->BaseProp.ActionSpeed = 100;
		}
	}

}

float FAwCharacterInfo::GetActionSpeedRate()
{
	int Cur = CurProperty.ActionSpeed;
	return  (float)CurProperty.ActionSpeed/BaseProp.ActionSpeed;
}

FAwCharacterInfo FAwCharacterInfo::GetCharacterInfoSaveData()
{
	FAwCharacterInfo CharacterInfoSaveData = *this;
	
	for(auto BuffIterator = CharacterInfoSaveData.Buff.CreateIterator();BuffIterator;++BuffIterator)
	{
		if (BuffIterator->Model.Tags.Contains("NotSave") )
		{
			BuffIterator.RemoveCurrent();
		}
	}
	return CharacterInfoSaveData;
}

int FAwCharacterInfo::GainExp(int Value)
{
	int LevelUp = 0;
	this->Exp += Value;
	while (this->Exp >= 100 && this->Level < 30)
	{
		this->Level += 1;
		this->Exp -= 100;
		LevelUp += 1;
	}
	return LevelUp;
}

FChaProp FAwCharacterInfo::CaluPropAfterBuff(FChaProp Base)
{
	FChaProp Res = Base;
	if (this->Buff.Num())
	{
		 BuffPlus = FChaProp();
		 BuffTimes = FChaProp();
		for (int i = 0; i < Buff.Num(); i++)
		{
			if (Buff[i].Model.CharacterPropertyModify.Num()>=2)
			{
				BuffPlus = BuffPlus + Buff[i].Model.CharacterPropertyModify[0]*Buff[i].Stack;
				BuffTimes = BuffTimes + Buff[i].Model.CharacterPropertyModify[1]*Buff[i].Stack;
			}
		}
		//乘法op重写 里是 self+self*Power
		Res = (Base+ BuffPlus) * BuffTimes;
	}
	return  Res;
}
