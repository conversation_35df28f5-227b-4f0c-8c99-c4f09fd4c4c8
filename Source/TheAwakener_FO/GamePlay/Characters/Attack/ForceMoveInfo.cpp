#include "ForceMoveInfo.h"

#include "Kismet/KismetSystemLibrary.h"
#include "TheAwakener_FO/GameFramework/Base/AwGameInstance.h"

FVector FForceMoveInfo::ThisTickVelocity(float DeltaTime,bool bOnGround)
{
	//只有在0秒内移动完毕的（当做瞬移，是不需要轨迹的）
	if (Active == false) return FVector::ZeroVector;	
	if (InSec <= 0) return this->Velocity;

	//DeltaTime是允许超过InSec的，想想在悬崖边打下去的情景。
	float ResX = 0; //Velocity.X / InSec * DeltaTime;	//因为是匀速的，Y同理
	float ResY = 0; //Velocity.Y / InSec * DeltaTime;
	float ResZ = 0; //Velocity.Z
	float StartZ = 0;
	float EndZ = 0;
	
	switch (Type)
	{
	case EForceMoveType::Fly:
		{
			const float WasPer = TimeElapsed / InSec;
			const float CurPer = (TimeElapsed + DeltaTime) / InSec;
			const float PerDis = CurPer - WasPer;
			ResX = Velocity.X * PerDis;
			ResY = Velocity.Y * PerDis;
			StartZ = Velocity.Z / InSec * TimeElapsed;
			EndZ = Velocity.Z / InSec * (TimeElapsed + DeltaTime);
			ResZ = EndZ - StartZ;
		}break;
	case EForceMoveType::Jump:
		{
			const float FuncX1 = 1.000f - TimeElapsed / InSec;	//因为前一半时间应该是负数，这才能做抛物线
			const float FuncX2 = 1.000f - (TimeElapsed + DeltaTime)  / InSec;
			StartZ = Velocity.Z * (FuncX1 == 0 ? 1.000f : (1.000f - FuncX1 * FuncX1 * FuncX1));	//尽管不可能，还是要避免分母为0
			EndZ = Velocity.Z * (FuncX2 == 0 ? 1.000f : (1.000f - FuncX2 * FuncX2 * FuncX2));
			ResZ = EndZ - StartZ;
		}break;
	case EForceMoveType::KnockOut:
		{
			//先要获得起点的Z高度，然后获得终点的Z高度，对比减出来才是结果
			const float HalfTime = InSec / 2.000f;

			if (HalfTime > 0)
			{
				this->AutoTerminateOnTimeUp = Velocity.Z <= 0;
				
				const float WasPer = TimeElapsed / InSec;
				const float CurPer = (TimeElapsed + DeltaTime) / InSec;
				const bool WasRising = WasPer <= 0.5f;
				const bool CurRising = CurPer <= 0.5f;	//预设时间一半到达顶点
				const float PerDis = CurPer - WasPer;
				
				//XY匀速移动，所以
				ResX = Velocity.X * PerDis;
				ResY = Velocity.Y * PerDis;

				//过半时落地结束（TODO：可能太晚了）
				this->TerminateOnGround =  Velocity.Z > 0;
				if (Velocity.Z > 0 && CurRising == false) this->TerminateOnGround = true;

				//Z变化曲线
				StartZ = WasRising ?
					(Velocity.Z * (1 - FMath::Pow(1 - 2 * WasPer , 3))):
					(Velocity.Z * (1 - FMath::Pow(2 * (WasPer - 0.5f) , 3)));
				EndZ = CurRising  ?
					Velocity.Z * (1 - FMath::Pow(1 - 2 * CurPer , 3)) :
					Velocity.Z * (1 - FMath::Pow(2 * (CurPer - 0.5f) , 3) ) ;
				ResZ = EndZ - StartZ;

				//UKismetSystemLibrary::PrintString(GWorld, FString("Fall Dis = ").Append(FString::SanitizeFloat(EndZ)).Append(">>").Append(FString::SanitizeFloat(EndZ - StartZ)));
			}break;
		}
	case  EForceMoveType::KnockDown:
		{
			float WasPer = TimeElapsed / InSec;
			float CurPer = (TimeElapsed + DeltaTime) / InSec;
			const float PerDis = CurPer - WasPer;
			FallingTimeElapsed+= DeltaTime;
			//XY匀速移动，所以
			ResX = Velocity.X * PerDis;
			ResY = Velocity.Y * PerDis;

			if (bOnGround|| Velocity.Z>=0)
			{
				ResZ = 0;
				FallingTimeElapsed = 0;
			}
			else
			{
				// ResZ = Velocity.Z * DeltaTime / InSec + FallingTimeElapsed * 9.8;

				// const float S = FMath::Abs(Velocity.Z);
				// const float V0 = 600;
				// float acc;
				// if (V0 * InSec >= S)
				// 	acc = 0;
				// else
				// 	acc = 2 * (S - V0 * InSec) / (InSec * InSec);
				//
				// ResZ = (V0 * (TimeElapsed + DeltaTime) + 0.5 * acc * (TimeElapsed + DeltaTime) * (TimeElapsed + DeltaTime)) -
				// 	   (V0 * TimeElapsed + 0.5 * acc * TimeElapsed * TimeElapsed);
				// ResZ *= -1;

				if (WasPer > 1) WasPer = 1;
				if (CurPer > 1) CurPer = 1;
				ResZ = (FMath::Sqrt(1-FMath::Pow(CurPer-1,2)) - FMath::Sqrt(1-FMath::Pow(WasPer-1,2))) * Velocity.Z;
				
				UKismetSystemLibrary::PrintString(UAwGameInstance::Instance, FString::SanitizeFloat(ResZ));
			}
		}break;
	}

	return FVector(ResX, ResY,ResZ);
}

void FForceMoveInfo::Jump(float MaxHeight, float ReachMaxHeightInSec, bool TerminateWhileActionChanged)
{
	this->Type = EForceMoveType::Jump;
	this->Velocity = FVector(0, 0, MaxHeight);
	this->InSec = ReachMaxHeightInSec;
	this->Active = ReachMaxHeightInSec > 0;
	this->TimeElapsed = 0;
	//this->TerminateOnState.Add(ECharacterActionState::Attached);
	this->AutoTerminateOnTimeUp = true;
	this->TerminateOnGround = false;
	this->TerminateOnActionChange = TerminateWhileActionChanged;
}

void FForceMoveInfo::KnockOut(FVector Fly, float DoneInSec)
{
	this->Type = EForceMoveType::KnockOut;
	this->Velocity = Fly;
	this->InSec = DoneInSec;
	this->Active = DoneInSec > 0;
	this->TimeElapsed = 0;
	this->AutoTerminateOnTimeUp = false;
	this->TerminateOnGround = false;
	this->TerminateOnActionChange = true;
}