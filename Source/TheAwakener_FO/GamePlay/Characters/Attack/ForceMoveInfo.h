#pragma once

#include "CoreMinimal.h"
#include "ForceMoveInfo.generated.h"


UENUM(BlueprintType)
enum class EForceMoveType: uint8
{
	/**
	 *	击飞类型轨迹
	 */
	KnockOut = 0,
	/**
	 *跳跃类型轨迹
	 */
	Jump = 1,
	/**
	 *飞行类型轨迹
	 */
	Fly = 2,
	/**
	 *	击倒类型轨迹
	 */
	KnockDown = 3,
};

/**
 * 击退方向的类型
 */
UENUM(BlueprintType)
enum class EVelocityType: uint8
{
	/**
	 * 攻击者的面向
	 */
	AttackerFace = 0,
	/**
	 * 攻击者与受击者的相对方向
	 */
	RelativeDir = 1
};

/**
 *击飞、击退等的配置信息
 */
USTRUCT(BlueprintType)
struct FForceMoveInfo
{
	GENERATED_BODY()

public:
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	bool Active = false;	//是否正在生效

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	EForceMoveType Type = EForceMoveType::KnockOut;
	
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	EVelocityType VelocityType = EVelocityType::AttackerFace;
	
	/**
	 *期望位移，最终是要乘以受击方的被击飞倍率的，单位厘米
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FVector Velocity = FVector::ZeroVector;

	/**
	 *期望完成轨迹时间，单位秒
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	float InSec = 0.f;

	/**
	 *这条信息已经运行了多少秒了
	 */
	UPROPERTY()
	float TimeElapsed = 0.f;

	//空中下坠时间 用于重力加速度计算
	UPROPERTY()
	float FallingTimeElapsed = 0.f;
	/**
	 * 当时间结束(TimeElapsed >= InSec)时，自动关闭这个ForceMove
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	bool AutoTerminateOnTimeUp = true;

	/**
	 * 碰到地面的时候是否终止这个强制移动，比如击飞是要的，但是会自动设置
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	bool TerminateOnGround = false;

	/**
	 * 当动作发生变化时自动结束掉
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	bool TerminateOnActionChange = false;
	

	/**
	 *获取某个时间段的移动增量
	 *@param DeltaTime 从这一刻起运行多久
	 */
	FVector ThisTickVelocity(float DeltaTime,bool bOnGround = false);

	FForceMoveInfo operator *(const float& Times) const
	{
		FForceMoveInfo Res = FForceMoveInfo();
		Res.Active = this->Active;
		Res.Velocity = this->Velocity * Times;
		Res.InSec = this->InSec * Times;
		Res.TimeElapsed = this->TimeElapsed;
		return Res;
	}

	bool ShouldBeDone() const
	{
		return AutoTerminateOnTimeUp == true && this->TimeElapsed >= this->InSec;
	}

	void Jump(float MaxHeight, float ReachMaxHeightInSec, bool TerminateWhileActionChanged = true);
	void KnockOut(FVector Fly, float DoneInSec);

	void AutoSetActive()
	{
		this->Active = this->Velocity.IsNearlyZero() == false && (InSec > 0 || AutoTerminateOnTimeUp == false);
	}
};

/**
 *旋转信息
 */
USTRUCT(BlueprintType)
struct FForceRotateInfo
{
	GENERATED_BODY()

public:
	UPROPERTY(BlueprintReadOnly)
	bool Active = false;	//是否正在生效

	/**
	 * @brief 目标朝向
	 */
	UPROPERTY(BlueprintReadOnly)
	FVector RotateForward = FVector::ZeroVector;
};