// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "ActionTouchInfo.generated.h"

class AAwCharacter;	//Cheat IDE

/**
 *TODO: 将被BeCaughtActorInfo彻底取代
 * 一个动作的攻击框碰触到了别人的受击框的信息
 */
// USTRUCT(BlueprintType)
// struct FActionTouchInfo
// {
// 	GENERATED_BODY()
// public:
// 	//碰触到框的Name是什么
// 	UPROPERTY(EditAnywhere, BlueprintReadWrite)
// 	FString MyHitBoxName ;
// 	
// 	//碰触到的受击框是哪个
// 	UPROPERTY(EditAnywhere, BlueprintReadWrite)
// 	UBeHitBoxBase* BeTouchedBox;
//
// 	//碰触到的是谁
// 	UPROPERTY(EditAnywhere, BlueprintReadWrite)
// 	AActor* BeTouchedActor;
//
// 	static FActionTouchInfo TouchNow(FString AttackBoxName, AActor* TouchedActor, UBeHitBoxBase* TouchedBox)
// 	{
// 		FActionTouchInfo TInfo = FActionTouchInfo();
// 		TInfo.MyHitBoxName = AttackBoxName;
// 		TInfo.BeTouchedBox = TouchedBox;
// 		TInfo.BeTouchedActor = TouchedActor;
//
// 		return TInfo;
// 	}
//
// 	bool operator ==(const FActionTouchInfo& OtherInfo) const
// 	{
// 		return (
// 			this->MyHitBoxName == OtherInfo.MyHitBoxName &&
// 			this->BeTouchedActor == OtherInfo.BeTouchedActor &&
// 			this->BeTouchedBox == OtherInfo.BeTouchedBox 
// 		);
// 	}
// };

/**
 * 一次进攻的记录
 */
USTRUCT(BlueprintType)
struct FOffenseHitRecord
{
	GENERATED_BODY()
public:
	//一次进攻的Id，他可以是Action的ActionId，可以是Bullet的ModelId等，总之只是对暗号用的
	//核心作用是验证：同一个动作、同一个子弹是否在此时又一次碰上了，命中了就要判断是否真正的命中了
	FString SourceId = FString();

	//这是几号，目前一个Action会有多段分开不同的去记录HitRecord，所以得把第几段当做几号
	UPROPERTY()
	int Index = 0;

	//下一次什么时候才能伤害
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float Duration;

	//打中了谁
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	AActor* Target;

	//还有几次就真的打不中了
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int CanHits;

	FOffenseHitRecord():Duration(0),Target(nullptr),CanHits(0){};
	FOffenseHitRecord(FString OffenseSourceId, int Index, AActor* TargetGuy, int CanHitTimesRest, float Delay):
		SourceId(OffenseSourceId), Index(Index), Duration(Delay), Target(TargetGuy), CanHits(CanHitTimesRest){};

	bool CanHitTargetNow() const
	{
		return Duration <= 0 && CanHits > 0;
	}
};



/**
 * FHitBoxOverlapResult仅仅只用于发生碰撞时，攻击一方引起的一些动作变化，他们会被Preorder到对应角色
 * 但是这并不足以满足所有的需求，因为挨揍一方可能还正好是一个当身动作等情况
 * 比如挨揍的是盾牌，就会立即发动反击动作，如果挨揍的是头盔，则会立即后空翻动作
 * 因为动作本身会发生变化才要用到这个，所以既然动作变化了，就不可能说临时有CancelPoint被激活这事儿了
 * 因此我们需要一个结构来作为信息，他其实是在Montage的AnimNotify中配置出来的
 */
// USTRUCT(BlueprintType)
// struct FCharacterHitBoxOverlapActionInfo
// {
// 	GENERATED_BODY()
// public:
// 	//角色的CharacterHitBox的名字
// 	UPROPERTY(EditAnywhere, BlueprintReadWrite)
// 	FString CharacterHitBoxId = FString();
//
// 	//对应的动作的Id，这必定是一个精确的动作Id，不然不该使用这个功能
// 	UPROPERTY(EditAnywhere, BlueprintReadWrite)
// 	FString ChangeToActionId = FString();
//
// 	FCharacterHitBoxOverlapActionInfo(){};
// 	FCharacterHitBoxOverlapActionInfo(FString HitBoxName, FString ActionId):
// 		CharacterHitBoxId(HitBoxName), ChangeToActionId(ActionId){};
//
// 	bool operator ==(const FCharacterHitBoxOverlapActionInfo& Other)const
// 	{
// 		return
// 			this->CharacterHitBoxId == Other.CharacterHitBoxId &&
// 			this->ChangeToActionId == Other.ChangeToActionId;
// 	}
// };




/**
 * 一次攻击碰撞框碰到角色碰撞框产生的信息
 */
// USTRUCT(BlueprintType)
// struct FActionHitResult
// {
// 	GENERATED_BODY()
// public:
// 	UPROPERTY(BlueprintReadWrite, EditAnywhere)
// 	FActionHitInfo HitInfo;
//
// 	UPROPERTY(BlueprintReadWrite, EditAnywhere)
// 	FActionTouchInfo TouchInfo;
//
// 	//力道来源的角度
// 	UPROPERTY(BlueprintReadWrite, EditAnywhere)
// 	FRotator SourceDirection;
//
// 	FActionHitResult():HitInfo(FActionHitInfo()), TouchInfo(FActionTouchInfo()), SourceDirection(FRotator()){};
// 	FActionHitResult(FActionHitInfo Attack, FActionTouchInfo Touch, FRotator SourceDir):HitInfo(Attack), TouchInfo(Touch), SourceDirection(SourceDir){};
//
// 	bool ActionBroken() const
// 	{
// 		return !(
// 			this->HitInfo.PushPower.Active == false ||
// 			this->HitInfo.PushPower.Velocity.IsNearlyZero() ||
// 			this->HitInfo.PushPower.ShouldBeDone()
// 		);
// 	}
// };
