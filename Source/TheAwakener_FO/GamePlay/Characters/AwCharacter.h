// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "AwCharacterInfo.h"
#include "PickUpInfo.h"
#include "Action/ActionComponent.h"
#include "Action/ActionLink.h"
#include "Action/ArmState.h"
#include "Action/InputAcceptanceModifier.h"
#include "AI/AwAIComponent.h"
#include "AI/FightingWill.h"
#include "Animation/AwAnimInstance.h"
#include "Animation/AwPhysicalBodyAnim.h"
#include "AttachPoint/AttachPoint.h"
#include "AttachPoint/CharacterAttachLink.h"
#include "Attack/ActionTouchInfo.h"
#include "Battle/MeshShock.h"
#include "Battle/OffendedObject.h"
#include "BreakSystem/AwBreakSystemComponent.h"
#include "ChaPart/ChaPart.h"
#include "ControlState/ControlState.h"
#include "Creation/MobModel.h"
#include "Creation/NewRoleItem.h"
#include "Creation/NpcInfo.h"
#include "GameFramework/Character.h"
#include "HitBox/ActorCatcher.h"
#include "HitBox/AttackHitBox.h"
#include "HitBox/Squeeze.h"
#include "Move/ActionForceRotateInfo.h"
#include "Move/ExtendMoveInfo.h"
#include "TheAwakener_FO/GameFramework/AwPlayerController.h"
#include "TheAwakener_FO/GamePlay/AssetUserData/CharacterHitBoxData.h"
#include "TheAwakener_FO/GamePlay/Camera/AwCameraComponent.h"
#include "TheAwakener_FO/GamePlay/Dialog/CloseUpShotCamera.h"
#include "TheAwakener_FO/GamePlay/Equipment/CharacterWearingAppearance.h"
#include "TheAwakener_FO/GamePlay/Equipment/Equipment.h"
#include "TheAwakener_FO/GamePlay/Equipment/Weapon.h"
#include "TheAwakener_FO/GamePlay/FX/FXPlayPoint.h"
#include "TheAwakener_FO/GamePlay/FX/PlayingFXOnCharacter.h"
#include "TheAwakener_FO/GamePlay/Interact/InteractWidgetComponent.h"
#include "TheAwakener_FO/GamePlay/Item/ItemObj.h"
#include "TheAwakener_FO/GamePlay/Move/AwMoveComponent.h"
#include "TheAwakener_FO/GamePlay/Roguelike/Map/RogueMapConfig.h"
#include "TheAwakener_FO/GamePlay/Tween/GlowExtendTweenInfo.h"
#include "AwCharacter.generated.h"


//玩家职业
UENUM(BlueprintType)
enum class EPlayerClass:uint8
{
	Warrior,
	TwinBlades,
	Swordsman,
	Spearman
};


struct FAddBuffInfo;	//Cheat IDE
struct FBattleClassModel;		//Cheat IDE
class USqueezeComp;
class UInterruptComponent;

//用在判断每一帧命中对象时
USTRUCT()
struct FActorBeHitChecker
{
	GENERATED_BODY()
public:
	UPROPERTY()
	FOffenseInfo OffenseInfo;
	UPROPERTY()
	FBeCaughtActorInfo CaughtActorInfo;
	FActorBeHitChecker(){}
	FActorBeHitChecker(FOffenseInfo OffInfo, FBeCaughtActorInfo CaughtInfo):
		OffenseInfo(OffInfo), CaughtActorInfo(CaughtInfo){};
};
// UE不支持TMap下有Array，就这么办
USTRUCT()
struct FActorBeHitCheckerArray
{
	GENERATED_BODY()
public:
	UPROPERTY()
	TArray<FActorBeHitChecker> Checkers;

	int IndexOfOffenseInfo(FOffenseInfo OInfo) const
	{
		for (int i = 0; i < this->Checkers.Num(); i++)
		{
			if (Checkers[i].OffenseInfo == OInfo)
			{
				return i;
			}
		}
		return -1;
	}
};


DECLARE_DELEGATE_OneParam(FCustomInputDelegate, const FString);
//interact成功时的动态多播委托
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnCharacterDead, AAwCharacter*, Character);
//角色受攻击的动态多播委托
DECLARE_DYNAMIC_MULTICAST_DELEGATE_EightParams(FOnCharacterBeOffened,AAwCharacter*,BeOffenedCharacter, FOffenseInfo, OffenseInfo, UAttackHitManager*, Attacker,
AAwCharacter*, AttackerInCharge, USceneComponent*, BeHitBox, USceneComponent*, FromAttackBox, bool, SameSideFriendlyFire, bool, AllyFriendlyFire);
//道具使用成功时的动态多播委托
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnCharacterUseItem,AAwCharacter*,User,FItemObj,Item);
//interac受到伤害时的动态多播委托
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnCharacterBeDamaged,AAwCharacter*,BeDamagedCharacter,FDamageInfo, DamageIndo);

UCLASS(config=Game, BlueprintType)
class THEAWAKENER_FO_API AAwCharacter : public ACharacter, public  IOffendedObject , public IAwCameraInterface
{
	GENERATED_BODY()

protected:
	virtual void BeginPlay() override;
	bool HaveSetupCharacter = false;
	UPROPERTY(BlueprintReadWrite, Category="SetupCharacter")
	bool PlayerCharacterInData = false;
private:


	UPROPERTY(Category=Character, VisibleAnywhere, BlueprintReadOnly, meta=(AllowPrivateAccess = "true"))
	UAwAnimInstance* AwAnimInstance = nullptr;
	UPROPERTY(Category=Character, VisibleAnywhere, BlueprintReadOnly, meta=(AllowPrivateAccess = "true"))
	UCmdComponent* CmdComponent = nullptr;
	UPROPERTY(Category=Character, VisibleAnywhere, BlueprintReadOnly, meta=(AllowPrivateAccess = "true"))
	UAwMoveComponent* MoveComponent = nullptr;
	UPROPERTY(Category=Character, VisibleAnywhere, BlueprintReadOnly, meta=(AllowPrivateAccess = "true"))
	UActionComponent* ActionComponent = nullptr;
	UPROPERTY(Category=Character, VisibleAnywhere, BlueprintReadOnly, meta=(AllowPrivateAccess = "true"))
	UAwAIComponent* AIComponent = nullptr;
	UPROPERTY(Category=Character, VisibleAnywhere, BlueprintReadOnly, meta=(AllowPrivateAccess = "true"))
	UAttackHitManager* AttackHitComponent = nullptr;
	UPROPERTY(Category=Character, VisibleAnywhere, BlueprintReadOnly, meta=(AllowPrivateAccess = "true"))
	UMeshShock* MeshShocker = nullptr;
	UPROPERTY(Category=Character, VisibleAnywhere, BlueprintReadOnly, meta=(AllowPrivateAccess = "true"))
	UAwCameraComponent* CameraComponent = nullptr;
	UPROPERTY(Category=Character, VisibleAnywhere, BlueprintReadOnly, meta=(AllowPrivateAccess = "true"))
	UAwBreakSystemComponent* BreakSystemComponent = nullptr;
	UPROPERTY(Category=Character, VisibleAnywhere, BlueprintReadOnly, meta=(AllowPrivateAccess = "true"))
	UAwPhysicalBodyAnim* AwPhysicalAnimComponent = nullptr;
	//等待进行处理的Damage
	UPROPERTY()
	TArray<FDamageDealer> ToBeDamaged;

	/**
	 *元素效果触发Tag，在动作的Montage的AnimNotifyState配置之后会改变这个
	 *有些Buff会依赖于这个生效
	 */
	UPROPERTY()
	TArray<FString> ElementalTriggerTag;

	// 角色死亡
	void OnDead();
	
	//记录这个Character是否被玩家所控制
	UPROPERTY(Replicated)
	bool bUnderPlayerControl;
	//角色能否飞起，这跟跳跃键的操作有关
	UPROPERTY(Replicated)
	bool Flyable = false;
	/**
	 * 单位秒，是指处于战斗状态还有多少秒
	 *是否在战斗中，造成攻击和受到伤害，都会导致InWar为True一小段时间，当然还可以追加别的情况
	 */
	UPROPERTY()
	float InWarDuration = 0;
	
	//濒死状态，是一个倒计时，满值是10秒
	UPROPERTY(Replicated)
	float SecondWind = 1.000f;
	//濒死状态时间减少倍数
	UPROPERTY()
	float SecWindLoseTimes = 1.0f;
	
	//上一帧坐标
	FVector LastLocation;

	//我身上所有蓝图里面拉的抓别人的抓点，<抓点id，抓点UAttachPoint*>
	UPROPERTY()
	TMap<FString, UAttachPoint*> CatchAttachPoints;
	//我身上所有蓝图做的被别人抓的抓点，<抓点id，抓点UAttachPoint*>
	UPROPERTY()
	TMap<FString, UAttachPoint*> SeatAttachPoints;

	//身上所有的Mesh，作为绑点用于播放视觉特效什么的<Point.GetName(), Point>
	UPROPERTY()
	TArray< UFXPlayPoint*> FXPoint;

	//正挂在身上播放的特效
	UPROPERTY()
	TArray<FPlayingFXOnCharacter> PlayingFX;

	//所有的攻击框受击框<盒子主体(SceneComponent)，盒子数据>
	TArray<TTuple<USceneComponent*, UCharacterHitBoxData*>> CharacterHitBoxes;
	
	//某个动作是否开启了立即接受转向<动作id，[<开启的ForceRotateOnce.Index, 约束最大转多少度>]>
	TMap<FString, TArray<FActionForceRotateInfo>> AllowForceRotate;
	
	//角色装备绑点<id, 绑点>
	UPROPERTY()
	TMap<FString, UEquipmentBindPoint*> EquipmentBindPoints;

	UFUNCTION()
	virtual void OnRep_Equipments();
	
	//身体上可以被装备替换的外观部位（尸块）
	UPROPERTY()
	TMap<FString, UMeshComponent*> BodySightParts;
	
	//身体上可以被装备替换的外观部位（尸块）
	UPROPERTY()
	TMap<FString, UChildActorComponent*> BodySightActorParts;

	//AI 角色吸引的怪物 拉的怪 仇恨
	TArray<AAwCharacter*> FollowingEnemys;
public:
	EFlagmarkBit ECSMark;//P1 A, P2 B
	UPROPERTY()
	float Lived = 0;
	
	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnCharacterUseItem OnCharacterUseItem;

	//锁定图标关联骨骼名字
	UPROPERTY(Category=PlayerWidget, EditAnywhere, BlueprintReadWrite)
	FName LockSignParentSocket;
	
	UFUNCTION()
	void SetupCharacter();
	//交互提示widget
	UPROPERTY()
		UInteractWidgetComponent* InteractWidget = nullptr;
	UPROPERTY(BlueprintAssignable, Category = "Events")
		FOnCharacterBeOffened  OnCharacterBeOffenedDelegate;
	UPROPERTY(BlueprintAssignable, Category = "Events")
		FOnCharacterBeDamaged  OnCharacterBeDamagedDelegate;
	
	// 怪物 死亡多久之后删除
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float MobRemoveAfterSec = 10.0f;
	// 怪物 死亡后多久显示死亡效果（溶解效果）
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float MobDeadFxAfterSec = 0.0f;

	//最近受到伤害的时间记录，每次受到伤害都是记录5秒（也就是多一个=5的值，所以数组长度==最近5秒内挨打次数）
	UPROPERTY()
	TArray<float> BeOffendedRec;
	
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FAwCharacterInfo CharacterObj;

	UPROPERTY(BlueprintReadOnly, Replicated)
	AAwPlayerController* OwnerPlayerController = nullptr;
	
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated)
	FString PlayerClassId = "";

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated)
	FString MobClassId = "";
	UFUNCTION(BlueprintCallable,BlueprintPure)
	FString GetPawnClassId()
	{
		if (PlayerClassId.IsEmpty())
			return MobClassId;
		return PlayerClassId;
	}
	UFUNCTION(BlueprintCallable)
	void GainExp(int Exp) const;
	UFUNCTION(BlueprintPure)
	float GetExpPercent() const;
	UPROPERTY(BlueprintReadOnly)
	bool TerrainCollisionEnable = true;
	
	//声音发出的点
	UPROPERTY()
	USceneComponent* MouthPoint = nullptr;
	//默认的声音文件夹和声音发出骨骼
	UPROPERTY()
	FString SoundDictionary = FString();

	//头顶泡泡的点
	UPROPERTY()
	USceneComponent* TextBubblePoint = nullptr;

	//所处阵营，这个Actor所处的阵营会在战斗逻辑中作为判断是否可以攻击对象的依据
	UPROPERTY(BlueprintReadOnly, Replicated)
	uint8 Side;
	
	//是否是坐骑类型的转身，如果不是则转身不会有轨迹
	UPROPERTY(BlueprintReadOnly, Replicated)
	bool MountsTypeRotate = false;
		
	//角色的情绪等级，AI用的“状态”
	UPROPERTY(BlueprintReadOnly)
	FFightingWill FightingWill;

	UPROPERTY(BlueprintReadOnly, Replicated)
	FPickUpInfo PickUpInfo;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated)
	UWidgetComponent* HPWidgetComponent;

	// Hurt 时候的受击手柄振动
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FAwForceFeedBackInfo HurtForceFeedBackInfo;
	// Blow 时候的受击手柄振动
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FAwForceFeedBackInfo BlowForceFeedBackInfo;
	
	/**
	 * 根据属性选择规则，找到现在的属性
	 * @param BaseElement 基础的属性，传进来的
	 * @param PickMethod 获取属性的方式
	 * @param WeaponSlot 武器的部位（主手、副手、远程、暗器）
	 */
	EChaElemental GetElemental(EChaElemental BaseElement, EElementalPickMethod PickMethod, ESlotInWeaponObj WeaponSlot) const;
	
	
	// 角色的控制状态，由 BaseControlState, ActionControlState 和 Buff 算出来。
	UPROPERTY(BlueprintReadOnly, Replicated)
	FControlState ControlState;	
	UPROPERTY(Replicated)
	FControlState BaseControlState;	// 基础配置,允许角色拥有独特的控制状态，比如根本不会移动的怪物。
	UPROPERTY(Replicated)
	FControlState AttachControlState; // 骑乘、攀附专用的ControlState
	UPROPERTY(Replicated)
	FControlState ActionControlState; // 动作中改变的角色控制状态
	
	//角色接受操控的倍数，标准动作中都应该是1.0f的，但是非标准动作都应该默认0.0f，除非AnimNotifyState改变
	FVector SpeedInputAcceptance;
	//动作中的旋转倍速，同上
	float RotateInputAcceptance;

	FVector Notify_SpeedAcceptance = FVector::ZeroVector;
	float Notify_RotateAcceptance = 0;
	//改变计划
	FInputAcceptanceModifier SpeedInputAcceptanceModifier;
	
	//角色转身速度，在体型影响的基础上再追加一个倍率
	UPROPERTY()
	float TurnAroundSpeed = 1.000f;	
	
	UPROPERTY(BlueprintReadOnly, Replicated)
	FCharacterAttachment CharacterAttachment;//抓点信息
	

	/**
	 *检测是否获得符合条件的Buff(FString  ModelId )
	*/
	bool  CheckHasBuff(FString ModelId);
	/**
	 *获得符合条件的Buff的列表(List<FBuffObj*>)
	 *@param ModelId 要查的buff的modelId
	 *@param ByCasters 来自谁释放的(BuffObj.Caster)，如果是空列表则代表不关心谁放的都算上
	 *@return 符合条件的Buff
	 */
	TArray<FBuffObj*> GetBuff(FString ModelId, TArray<AAwCharacter*> ByCasters = TArray<AAwCharacter*>());
	TArray<int> GetBuffIndexes(FString ModelId, TArray<AAwCharacter*> ByCasters = TArray<AAwCharacter*>());
	// 获取身上某种buff的层数
	UFUNCTION(BlueprintPure)
	int GetBuffStackTotal(FString ModelId);
	
	/**
	 *获取可以Cancel的动作
	 *@param PrevActions 需要检查的动作们
	 *@param LearntOnes 是否是只要已经学会的，false就会返回包含不会的
	 *@return 可以Cancel PrevActions中至少一个的Action组成的数组
	 */
	TArray<FActionInfo*> GetCancellableActions(TArray<FActionInfo*> PrevActions, bool LearntOnes = true) const;
	TArray<FActionInfo*> GetCancellableActions(TArray<FString> PrevActions, bool LearntOnes = true) const;

	/**
	 * 获取当前可以Cancel的动作
	 */
	TArray<FActionInfo*> GetCurrentCancellableActions() const;
	UPROPERTY(BlueprintReadWrite,Category="Datas")
	TArray<FSoftObjectPath> Actions;
	//最后一次受到的力，比如受到伤害之类的都得改写这个
	UPROPERTY()
	FVector LastIncomingForce = FVector::ZeroVector;
	//围绕玩家生成怪物时，
	UPROPERTY(BlueprintReadOnly)
	TArray<AActor*> localSpawnPointActors;
	UFUNCTION(BlueprintCallable)
	void AlignPlayerSpawnPointsNum(int points);
	
	void AddDefenseInfo(FDefenseInfo DefenseInfo);
	void RemoveDefenseInfo(FDefenseInfo DefenseInfo);
	void RemoveAllDefenseInfo();

	UFUNCTION()
	bool GetHitBoxInJustDefense(FString HitBoxId);

	UFUNCTION(BlueprintCallable)
	void AddFollowingEnemy(AAwCharacter* Enemy);
	UFUNCTION(BlueprintCallable)
	void RemoveFollowingEnemy(AAwCharacter* Enemy);
	UFUNCTION(BlueprintCallable)
	int GetFollowingEnemyCount(){return  FollowingEnemys.Num();};
	UFUNCTION(BlueprintCallable)
	TArray<AAwCharacter*> GetFollowingEnemy(){return  FollowingEnemys;};
	
	//这里是动作能造成的伤害计算，因为别的伤害可能是直接填写的伤害值，所以不会走这里，只有动作的按规则走这里
	FOffenseInfo GetOffenInfoWithWeaponPower(FOffenseInfo OInfo,const FVector& TargetLocation) const; 
private:
	//是否暂停了，逻辑上的暂停了
	UPROPERTY()
	bool Paused = false;
	//缓存的资产，避免意外GC
	TArray<UObject*> PreloadedObjects;
	
	//属性重置到基础状态
	void ResetBaseProperty();
	
	inline void GatherActorComponents();
	inline void GatherSubMeshes();
	inline void ConnectHitBoxToPart();

//-------Dialog-------------
	UPROPERTY()
	TMap<int, UCloseUpShotCamera*> DialogCameraPoints;
	
// ------ ACTION -----------

	//当前动作的临时的切换动作信息，即当对应的受击框被碰撞后，角色会preorder这些动作，比如格挡框挨揍要进入当身动作
	TArray<FDefenseInfo> DefendingInfo;

	// ----- DAMAGE -----
	
	

	//本帧处理伤害
	void CheckForActionHit();
	static constexpr EFlagmarkBit DyingFlag = EFlagmarkBit::D;
// ----- EQUIPMENT -----

	//TODO: 新的方法取代了这个
	// /**
	//  * 获得应该显示那些外观
	//  * @return 需要显示外观的装备
	//  */
	// TArray<FEquipmentAppearancePart> EquipmentsRequireShowAppearance();
	
	/**
	 * 在当前角色身上显示某件装备外观，这里不会删除原本某个绑点上的东西
	 **@param AppearanceEquipment 要显示外观的装备
	 */
	AActor* ShowAppearance(FEquipmentAppearancePart AppearanceEquipment);
	
	//去掉所有装备（含武器和瓶子等的）
	void HideAllAppearance();

	//设置所有装备的显示性
	void SetAppearanceVisible(bool ToVisible);
	
	//重新检测并设置身体尸块部位的可见度（更换装备后调用）
	void RecheckBodySightPartsVisible();


	
public:
	AAwCharacter();
	virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;
	virtual void Tick(float DeltaTime) override;
	virtual void Destroyed() override;
	UPROPERTY(BlueprintReadOnly)
	bool GotReady = false;
// ----- SETUP -----

	UFUNCTION(BlueprintCallable)
	void InitPlayerChaPart();
	UFUNCTION(BlueprintCallable)
	void SetupAsPlayerCharacter(FString ClassInfoId, FString TypeId, int InitLevel = 1);
	UFUNCTION(BlueprintCallable)
	void SetupAsMonster(FMobModel MobModel, int InitLevel = 1, int ToSide = 1, EMobRank ThisMobRank = EMobRank::Normal,FString AlterId="");
	UFUNCTION(BlueprintCallable)
	void SetupByCharacterInfo(FAwCharacterInfo CharacterInfo);
	UFUNCTION(BlueprintCallable)
	void SetNpcInfo(FNpcInfo Info);

	UFUNCTION(BlueprintCallable)
	void GiveNewRoleItems(FNewRoleItem Items);
	UFUNCTION(BlueprintCallable)
	void WearRogueEquipments(FString ClassId);
	UFUNCTION(BlueprintCallable)
	void WearRogueWeapon(FString RogueWeaponInfoId);
	// 根据怪物信息初始化对应组件
	void Setup(FMobModel NewMobModel) const;
	// 根据职业信息初始化对应组件
	void Setup(FBattleClassModel NewClassInfo, FString TypeId) const;
	// 通过CharacterInfo初始化Character
	UFUNCTION(BlueprintCallable)
	void InitClientByCharacterInfo(FAwCharacterInfo CharacterInfo);
	UFUNCTION(BlueprintCallable)
	void PreloadAssets();
// ----- COMPONENT -----
	UFUNCTION(BlueprintPure, Category = "Get")
	UAwAnimInstance* GetAwAnimInstance() const { return AwAnimInstance; }
	UFUNCTION(BlueprintPure, Category = "Get")
	UCmdComponent* GetCmdComponent() const {return CmdComponent;}
	UFUNCTION(BlueprintPure, Category = "Get")
	UAwMoveComponent* GetMoveComponent() const {return MoveComponent;}
	UFUNCTION(BlueprintPure, Category = "Get")
	UActionComponent* GetActionComponent() const {return ActionComponent;}
	UFUNCTION(BlueprintPure, Category = "Get")
	UAwAIComponent* GetAIComponent() const {return AIComponent;}
	UFUNCTION(BlueprintPure, Category = "Get")
	UAttackHitManager* GetAttackHitComponent() const {return AttackHitComponent;}
	UFUNCTION(BlueprintPure, Category = "Get")
	UAwCameraComponent* GetAwCameraComponent() const {return CameraComponent;}
	UFUNCTION(BlueprintPure, Category = "Get")
	UAwBreakSystemComponent* GetBreakSystemComponent() const {return BreakSystemComponent;}
	
	UFUNCTION(BlueprintPure)
	TArray<UMeshComponent*> GetAllMeshComponents();
	UFUNCTION(BlueprintPure)
	USqueezeComp* GetSqueezeComp() const;
	UFUNCTION(BlueprintPure)
	UInterruptComponent* GetInterruptComp() const;
//----------NPC---------------------------------
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FNpcInfo NpcInfo;
	
// ----- PROPERTY -----
	
	// 返回是否是接受玩家控制的角色
	UFUNCTION(BlueprintPure)
	bool UnderPlayerControl() const;
	
	// 是否是角色控制的角色
	UFUNCTION(BlueprintPure)
	bool IsPlayerCharacter() const;
	
	//判断某个角色是否为这个角色的敌人
	UFUNCTION(BlueprintPure)
	bool IsEnemy(AAwCharacter* CheckTarget);

	//判断一个角色是否可以和玩家角色交互
	UFUNCTION(BlueprintPure)
	bool CanBeInteract(AAwCharacter* WhoTalkToMe) ;

	//我是不是玩家角色的敌人
	UFUNCTION(BlueprintPure)
	bool IsPlayersEnemy();
	
	// 角色能否飞起，这跟跳跃键的操作有关
	// 移动函数需要的参数，用来判断是否是贴地移动
	UFUNCTION(BlueprintPure)
	bool CanFly() const {return Dead(true) ? false : Flyable;}

	//当前动作如有需要延长移动的话，就要看这个数据
	// TODO: 需要整合 --- by QuanShenLong
	UPROPERTY()
	FExtendMoveInfo ExtendMoveInfo;
	
	/**
	 * 重新计算角色属性
	 * 在角色buff变化、装备变化等情况下都该调用我一次
	 */
	void AttrRecheck();

	//设置装备属性
	UFUNCTION(BlueprintCallable)
	void SetEquipmentProp(FChaProp NewEquipmentProp);
	// 直接设置等级
	UFUNCTION(BlueprintCallable)
	void SetLevelTo(int ToLevel);

	// 提升等级
	UFUNCTION(BlueprintCallable)
	void LevelUp(int Times = 1);
	
	
	//变更角色当前的动画状态，同时也得变更动画
	void ChangeCharacterState(ECharacterActionState ToState) const { if (ActionComponent) ActionComponent->ChangeCharacterState(ToState); }
	
	/**
	 *扣除法力值，返回耐力值是否低于一个标准了
	 *@param Value 扣除多少，如果是负数，会被增加，但不会突破max
	 *@param Below 检查<=多少了
	 *@return 是否小于等于Below了
	 */
	bool CostMana(float Value, int Below = 0);
	
	// 获得ActionControlState
	FControlState* GetActionControlState() const;
	
// ----- HP & REVIVE & DEAD -----
	
	// 是否已经死了
	UFUNCTION(BlueprintPure)
	bool Dead(bool IncludeSecondWind = false) const;
	
	// 是否已经在濒死状态
	UFUNCTION(BlueprintPure)
	bool InSecondWind() const;
	
	// 躺地上被拉起来
	void RevivedOnSecondWind();
	/**
	 *完全恢复
	 *@param Revive 如果已经死了是否先复活，如果是false并且角色死了，就不会治疗
	 *@param Directly 是否直接回复，而不走Buff流程（旅店睡觉应该是的）
	 *@param RestoreMax 是否把MaxHP MaxMP和MaxSP也回复满，这3个玩意儿是抄袭龙信的傻屌玩意儿
	 */
	UFUNCTION(BlueprintCallable)
	void FullyRestore(bool Revive = false, bool Directly = true, bool RestoreMax = true);
	// 当前生命值百分比
	UFUNCTION(BlueprintCallable)
	float HealthPercentage(bool RecheckProp = false);
	// 当前法力值百分比
	UFUNCTION(BlueprintCallable)
	float ManaPercentage() const;
	// 当前体力值百分比
	UFUNCTION(BlueprintCallable)
	float StrengthPercentage() const;

	// 当前体力值百分比
	UFUNCTION(BlueprintCallable)
	float AwakePercentage() const;
	
	// 体力值恢复计时
	float SP_Timer = 1;
	// 体力值隔多久之后开始恢复
	float SP_FreezeDur =1.0f;
	// 魔力值恢复计时
	float MP_Timer = 1;
	// 魔力值隔多久后开始恢复
	float MP_FreezeDur = 1.0f;
	// 属性自然回复
	void Tick_RecoveryChaProp(float DeltaTime);
	
	// 获取最大生命值的百分比是具体的多少，四舍五入的
	int HealthOfPercentage(float Percentage, bool RecheckProp = false);
	
	// 是否满血
	bool HealthFull(bool RecheckProp = false);

	UFUNCTION(BlueprintCallable, Category="States")
	int CurrentHP() const;
	UFUNCTION(BlueprintCallable, Category="States")
	void SetCurrentHP(int NewHealth);

	int GetCurAirDodgePoint() const;
	// 空中闪避点数充满
	void SetCurAirDodgePointToMax();
	float OnGroundTimer = 0;
	
	// 判断当前的角色资源值是否够（HP、MP、SP）
	bool CheckActionCost(FActionCost ActionCost) const;
	// 扣除角色资源值（HP、MP、SP）
	void DeductActionCost(FActionCost ActionCost);
	 
	/**
	 *强行杀死，这个杀死是不会走进伤害流程的buff的，用于掉深渊之类的
	 *@param Directly 是否逃过濒死阶段
	 **@param IsByDamage 是否通过造成伤害进行杀死
	 */
	UFUNCTION(BlueprintCallable)
	void InstantKill(bool Directly = true, bool IsByDamage = false);

	UFUNCTION(BlueprintCallable,BlueprintImplementableEvent)
	void OnBeKilled();

	//角色死亡时的动态委托
	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnCharacterDead OnCharacterDeadDelegate;
	
// ----- BUFF -----
	
	/**
	 *改变角色的Buff（以添加的方式）
	 *@param BuffInfo 改变的信息
	 */
	FBuffObj* AddBuff(FAddBuffInfo BuffInfo);

	UFUNCTION(BlueprintCallable)
	FBuffObj AddBuffInBP(FAddBuffInfo BuffInfo)
	{
		FBuffObj* Res = AddBuff(BuffInfo);
		return Res?*Res:FBuffObj();
	}

	UFUNCTION(BlueprintImplementableEvent)
	void OnAddBuff(FBuffObj NewBuffObj);
	
	/**
	 *改变角色的Buff（以添加的方式）
	 *@param BuffId 要删除的buffObj的Model.id
	 */
	UFUNCTION(BlueprintCallable)
	void RemoveBuffById(FString BuffId);

	//移除同一Tag类的Buff
	UFUNCTION(BlueprintCallable)
	void RemoveBuffByTag(FString BuffTag);

	UFUNCTION(BlueprintCallable)
	void RemoveAllBuff();

	//过一遍肉鸽房间开始结算的Buff
	UFUNCTION(BlueprintCallable)
	void BuffEventOnRoomStart(int RoomLevel,ERogueRoomType RoomType);
	//过一遍肉鸽房间结束结算的Buff
	UFUNCTION(BlueprintCallable)
	void BuffEventOnRoomEnd(int RoomLevel,ERogueRoomType RoomType);
// ----- PART -----
	
	FChaPart* GetChaPartById(FString Id);
	int32 GetChaPartIndexById(FString Id);
	TArray<FChaPart*> GetChaPartsByType(EChaPartType PartType);
	bool HasPart(const FChaPart* CheckPart);
	// 获取身上价值最高的部位，作为Buff的伤害部位
	FChaPart* MostPriorityPart();
	
// ----- MOVE -----
	//当前是否处于卡帧的硬直中
	bool InFreezing()const;
	
	// 上一帧坐标getter
	FVector GetLastLocation()const{return LastLocation;}
	// 上一帧坐标setter
	void SetLastLocation(){LastLocation = this->GetActorLocation();}
	
	// 是否落在地上了
	UFUNCTION(BlueprintPure)
	bool OnGround() const {
		if (IsValid(MoveComponent))
			return MoveComponent->bStandOnGround;
		return true;
	}
	
	// 角色的Z变化
	float ZChange() const;
	
	// 角色XY坐标变化
	FVector2D XYChange() const;
		
	// 获得角色移动速度
	float GetMoveSpeed() const;
	
	// 获得移动速度等级
	int GetMoveSpeedLevel() const;
	
	// 获得角色当前移动方向
	FVector2D GetMoveDirection() const;
	
	// 强制位移
	UFUNCTION( NetMulticast, Reliable)
	void AddForceMove(FForceMoveInfo Move);
	
	// 设置动画重力倍数
	void SetActionGravityTimes(float Times) const;
	
	// 让角色起跳
	void AwJump(float Height, float InSec) const;
	
	// 让角色起飞或者降落，FlySpeed为负数肯定就是下落了
	void TakeOff(float FlySpeed) const;
	
	// 设置InputAcceptance
	void SetInputAcceptance(FVector MoveTimes, float RotateTimes);
	
	// 设置移动速度等级
	void SetMoveSpeedLevel(int SpeedLevel, int StateLevel) const;
	void SetForwardSpeedLevelLimit(int LimitLevel) const;

	//在动作(Action)中设置SpeedLevel的约束
	void SetActionMoveSpeedLevelLimit(bool On, int ToLevel = 0) const;
	
	// 设置角色XY方向上的移动，以及角色转向(ActorRotation而非FaceDir)
	void SetXYMove(FVector2D Direction, int MoveSpeedLevel) const;
	
	// 设置移动方向
	void SetMoveDirection(FVector2D Direction) const;
	
	// 设置面朝方向
	UFUNCTION(BlueprintCallable)
	void SetFaceDirection(FVector2D Direction) const;

	void SetAIMoveAndFaceDir(FVector2D MoveDir, FVector2D FaceDir, int SpeedLevel) const;

	FVector2D GetFaceDir() const;
	
	// 停止角色当前的强制移动，比如被击飞等
	void StopCurrentForceMove() const;
	
	// 开关保持移动
	void SetKeepMoving(bool ToActive, EMoveDirMergeRule Rule = EMoveDirMergeRule::Cover) const;

	//获取当前角色脚下的地面坐标，如角色在空中，则获取垂直向下的地面坐标
	UFUNCTION(BlueprintCallable)
	FVector GetCharacterGroundLoc();
	
// ----- ATTACK & DAMAGE -----
	
	// 当前动作中生效的ActionHitInfo
	TArray<FOffenseInfo> CurrentActionHitInfo;
	void AddActionHitInfo(FOffenseInfo ToAdd);
	void RemoveActionHitInfo(FOffenseInfo ToRemove);
	bool CheckHasAttackSourceInCurActionHitInfo(EAttackSource AttackSource);
	//UCharacterHitBox* GetCharacterHitBoxByName(FString TargetHitBoxName);	//TODO 即将干掉
	TTuple<USceneComponent*, UCharacterHitBoxData*> GetCharacterHitBoxByName(FString TargetHitBoxName);
	TArray<TTuple<USceneComponent*, UCharacterHitBoxData*>> GetCharacterHitBoxByPartType(EChaPartType PartType);
	TArray<FString> AllActiveCharacterHitBoxIds();
	USceneComponent* GetAttackHitBoxByName(FString TargetHitBoxName) const;
	void SetAllCharacterHitBoxActive(bool ToActive);
	void ResetAllCharacterHitBoxes();
	void SetJustDodge(FJustDodgeInfo DodgeInfo);

	UFUNCTION(BlueprintCallable)
	virtual FOffendedCaughtResult CanBeOffended(
		FOffenseInfo OffenseInfo,
		UAttackHitManager* Attacker,
		AAwCharacter* AttackerInCharge,
		bool IncludeAttackerActionHitBoxes,
		bool SameSideFriendlyFire = false,
		bool AllyFriendlyFire = false) override; 

	/**
	 * 我这个角色受到进攻，比如来自动作的攻击，来自子弹、来自aoe等，都得走这个，这是一次完整的受击流程
	 * @param OffenseInfo 一次进攻的完整信息信息，在调用前，请确保其AttackInfo.DamagePower已经是计算后的伤害力了（包括动作值之类都算好了），这里将和防御部位直接进行计算了
	 * @param Attacker 攻击者对象，如果是子弹或者aoe，那么就是子弹或者aoe的HitManager
	 * @param AttackerInCharge 就是一个负责人的意思，可以是nullptr代表无人对攻击负责，但是攻击依然有效
	 * @param BeHitBox 打在我的哪个碰撞盒上了
	 * @param FromAttackBox 来自哪个攻击盒子，可能是null的，这不强求，但是照理来说是都能拿到的
	 * @param SameSideFriendlyFire 同Side角色之间可否互相伤害
	 * @param AllyFriendlyFire 对于友方是否可以互相伤害
	 */
	UFUNCTION(BlueprintCallable)
	virtual void BeOffended(
		FOffenseInfo OffenseInfo, UAttackHitManager* Attacker,
		AAwCharacter* AttackerInCharge, USceneComponent* BeHitBox,
		USceneComponent* FromAttackBox = nullptr, bool SameSideFriendlyFire = false, bool AllyFriendlyFire = false)override; 
	
	/**
	 *受到伤害
	 *@param DamageInfo 经过处理的最终伤害信息
	 */
	void BeDamaged(FDamageInfo DamageInfo);
	
	UFUNCTION(BlueprintCallable, NetMulticast, Reliable)
	void PlayHitFX(bool IsDamage, int DamValue, FTransform PlayTransform, EChaPartMeatType MeatType);
	
	//当前动作是否能命中目标
	UFUNCTION(BlueprintCallable)
	bool CanHitTargetNow(AActor* TargetActor);
	
	/**
	 * 获得当前情况下，有效的命中信息，这些信息本来是AttackActive告诉我开启的，同时我判断碰撞盒碰到了才是真的有效
	 * @return <打中谁, 命中信息>筛选出来可以打中的角色，并且返回最有效的那条（价值最高的）
	 */
	TMap<AActor*, FActorBeHitCheckerArray> ThisTickValidActionHitInfo();
	
	// 添加一个激活的攻击框
	UFUNCTION(BlueprintCallable)
	void ActiveOffenseHitBox(FString BoxName);
	// 激活ECS的攻击
	UFUNCTION(BlueprintCallable)
	void ActiveECSAttackBoxs(TArray<FString> BoxNames);
	// 删除一个攻击框
	UFUNCTION(BlueprintCallable)
	void StopOffenseHitBox(FString BoxName);
	// 关闭ECS的攻击
	UFUNCTION(BlueprintCallable)
	void DeactiveECSAttackBoxs(TArray<FString> BoxNames);

	//TODO 即将干掉
	// 获取身上最有价值的受击框
	// UFUNCTION(BlueprintCallable)
	// UCharacterHitBox* MostPriorityHitBox();
	UFUNCTION(BlueprintCallable)
	USceneComponent* MostPriorityHitBox();

	//根据攻击盒的引用获取相应的OffenseInfo
	UFUNCTION(BlueprintCallable)
	FOffenseInfo GetOffenseInfoByAttackHitBox(UAttackHitBox* AttackHitBox);

// ----- CONTROL -----
	
	/**
	 *改变玩家控制权
	 *@param PlayerController 要给哪个PlayerController 操作权
	 *@param Enable 是否可以控制
	 */
	UFUNCTION(BlueprintCallable, Reliable, NetMulticast)
	void SetPlayerControllable(AAwPlayerController* PlayerController, bool Enable);
	
	// 清空玩家操作
	UFUNCTION()
	void ClearPlayerInput() const;
	
	// 清除掉所有AI的指令
	UFUNCTION()
	void ClearAICmd() const;

	//是否开启瞄准状态
	UFUNCTION()
	bool InAiming() const;

	UFUNCTION()
	void SetCmdInAiming(bool On) const;

	//获得转向的期望目标
	UFUNCTION()
	FVector2D GetRotateDir() const;

	UFUNCTION(BlueprintCallable)
	void SetDir(float Yaw);
	
// ----- ACTION -----
	
	// 当前正在做的动作
	FActionInfo* CurrentAction() const;

	/**
	 * 获取当前动作对应的FRougeAbilityLevelInfo
	 * 用于在Buff生效时确认当前技能信息如Level等属性
	 * @return 当前动作对应的FRougeAbilityLevelInfo，如果找不到则返回默认值
	 */
	UFUNCTION(BlueprintCallable)
	FRougeAbilityLevelInfo GetCurrentActionAbilityLevelInfo() const;

	//是否正确在做一个动作（播放Montage）
	bool IsInMontageAction() const;
	
	// 在变化动作的时候调用
	void OnChangeAction(FActionInfo WasAction);
	
	//设置BlendSpaceAnimIndex
	void SetBlendSpaceAnimIndex(int ToIndex) const;
	
	/**
	 *设置某个动作的ForceRotateOnce为True
	 *@param ActionId 动作Id
	 *@param Index 是第几条开关
	 *@param DegreeLimit 约束最大转多少度，仅在Open的时候有效
	 *@param Open 开还是关，开就是允许
	 */
	void SwitchActionForceRotateOnce(FString ActionId, uint8 Index, bool Open, float DegreeLimit = 0);
	
	/**
	 * 当前动作强转角度的允许量
	 * @return <ActionId, Index, 角度限制>返回允许角度的信息
	 */
	FActionForceRotateInfo* CurActionForceRotate();

	// 判断是否有某个动作
	bool HasAction(FString ActionId, bool OnlyLearnt = true) const;

	// 是否有任何Action被操作了
	bool AnyActionOccur(TArray<TTuple<FString, EAwInputState>> CheckInput) const;

	// 是否指定的动作有被操作，当调用完之后删除对应的输入
	bool IsActionOccur(FString Cmd, EAwInputState InputState = EAwInputState::Press, bool RemoveInputs = false) const;

	// 根据id获得对应的ActionInfo
	FActionInfo* GetActionById(FString ActionId) const;

	//根据MontageState获得角色学会的Action
	FActionInfo* GetActionByMontageState(ECharacterMontageState State) const;

	//根据ActionState(BlendSpace)获得角色学会的Action
	FActionInfo* GetActionByActionState(ECharacterActionState State) const;

	// 获得动画状态机的值
	ECharacterActionState CurrentActionState() const;

	// 根据MontageState来预约要播放的动作
	void PreorderActionByMontageState(ECharacterMontageState MontageState, FActionParam Params = FActionParam(), float FromSec = 0) const;

	//根据FActionChangeInfo来预约要播放的动作
	void PreorderActionByActionChangeInfo(FActionChangeInfo Info, FActionParam ActionParam) const;

	// 根据动作名字预约一个动作
	void PreorderAction(FString ActionId, FActionParam Params = FActionParam(), float FromSec = 0) const;
	void PreorderAction(FActionChangeInfo Info,  FActionParam ActionParam = FActionParam()) const;
	//尝试预约一个动作 但是不符合当前动作条件则预约失败
	void TryPreorderAction(FString ActionId, FActionParam Params = FActionParam(), float FromSec = 0) const;
	
	UFUNCTION(BlueprintCallable)
	void PreorderActionInBP(FString ActionId, FActionParam Params, float FromSec = 0)
	{
		PreorderAction(ActionId,Params,FromSec);
	}
	UPROPERTY(BlueprintReadOnly)
	FVector LastPosOnGround;
	UPROPERTY(blueprintreadwrite)
	FTransform LastCheckPoint;
	UFUNCTION(BlueprintCallable)
	void CheckOnGround();

	//根据动作名字Preorder一个动作，但是如果当前不允许Cancel预约无效
	void PreorderActionWithCancelCheck(FString ActionId, FActionParam Params = FActionParam(), float FromSec = 0) const;

	// 卡肉
	void SlowDownActionOnHit(FActionChangeInfo ActionInfo) const;

	//  添加AI的Action
	void AddAIAction(FString ActionId) const;
	
// ----- CANCEL TAG -----
	
	void AddCancelTag(ECancelTagType TagType, int CancelPointIndex, float Duration = 0, FActionInfo MAction = FActionInfo()) const;
	void RemoveAllCancelTags() const;
	void RemoveMontageCancelTag(int CancelPointIndex, FActionInfo MAction = FActionInfo()) const;
	void RemoveStateCancelTag(int CancelPointIndex) const;
	void RemoveTemporaryCancelTag(int CancelPointIndex) const;
	
// ----- MONTAGE & BLEND SPACE -----

	// 获得当前正在播放的montage instance
	FAnimMontageInstance* GetActiveMontageInstance() const;
	// 获得RootMotion中的Montage
	FAnimMontageInstance* GetRootMotionMontageInstance() const;
	// 获得当前正播放的Montage Anim
	// Returns true if this montage is active (valid and not blending out) */
	UAnimMontage* GetCurrentActiveMontage() const;
	// 停止当前动作（Montage动作）
	void StopCurrentAction(bool Forced = false) const;
	// 设置当前播放中的Montage动画的Position
	void SetActiveMontagePosition(float ToSec) const;
	// 开始播放一个Montage 动画，返回Play之前是否就已经有一个Montage在播放了
	bool PlayMontageAnim(FString MontagePath, FActionPlanInfo ActionPlanInfo) const;
	// 播放Montage动画的另一种写法，返回Montage长度
	float PlayMontageAnim(UAnimMontage* Montage, float PlayRate, float FromSec) const;
	/**
	 * 开始播放一个移动的 BlendSpace 动画
	 * @param AnimPath 播放的动画路径
	 */
	void PlayMoveBlendSpace(FString AnimPath) const;
	// 停止所有Montage
	void StopAllMontages(float BlendOut) const;

	// 播放瞄准动画
	// Index 瞄准的 BlendSpace 为 Class 表里的第几个
	void PlayAimBlendSpace(int Index) const;
	// 停止瞄准动画
	void StopAimBlendSpace() const;
	
	float GetActionSpeedRate();

	TMap<FString, float> RootMotionRate;
	float GetRootMotionRate();
	
// ----- ATTACH -----
	
	/**
	 *骑乘上某个角色的某个点，或者攀附上去
	 *@param MyNode 我自身用哪个点去Attach
	 *@param TargetNode 被攀附的点
	 *@return 是否抓成了
	 */
	bool AttachOnTarget(UAttachPoint* MyNode, UAttachPoint* TargetNode);
	// 骑乘某个角色，骑上去的瞬间进行的回调，主要是更换动作的
	void OnAttachOnTargetSuccess() const;
	// 从坐骑上或者攀附的怪物身上下来
	void StopAttaching();
	// 抓住了什么
	UAttachPoint* GetAttachingTarget()const{return this->CharacterAttachment.AttachTarget;}
	// 根据抓点名字获得抓点
	UAttachPoint* GetAttachPointByName(FString AttachPointName,	bool isCatcher);
	// 是否被任何人骑乘在任何点上
	bool HasBeenAttachedOnAnySeat() const;
	/**
	 *增加一个骑乘了我的角色
	 *@param TargetNode 抓我的点
	 *@param MyNode 抓我身上的哪个点
	 */
	void BeAttachOn(UAttachPoint* TargetNode, UAttachPoint* MyNode);
	/**
	 *身上某个点是否可以被再抓
	 */
	bool CanBeAttached(UAttachPoint* Point);
	/**
	 *根据部位Id所有对应的Seat型挂点
	 *@param PartId 部位的Id
	 */
	TArray<UAttachPoint*> GetAttachPointsByChaPartId(FString PartId);
	// 清除Attach路径
	UFUNCTION(NetMulticast, Reliable)
	void ClearAttachTargetMovementTrack();
	// 添加一个Attach 跟随路径
	UFUNCTION(NetMulticast, Reliable)
	void AddAttachTargetMovementTrack(FVector Node);
	
// ----- EQUIPMENT -----

	/**
	 * 获得某个部位的装备，如果没有则返回nullptr
	 * @param EquipPart 装备部位
	 * @return 这个部位的装备位于Equipments的下标
	 */
	int GetEquipmentIndexByPart(EEquipmentPart EquipPart);
	/**
	 * 获得某个身体部位对应的装备，如果没有则返回nullptr
	 * @param ChaPart 装备部位
	 * @return 这个部位的装备的下标
	 */
	int GetEquipmentIndexByChaPart(EChaPartType ChaPart);
	/**
	 * 穿上某件装备
	 * @param ToEquip 要穿的装备
	 */
	void WearEquipment(FEquipment ToEquip);
	void WearEquipmentById(FString EquipmentId);
	// 穿上武器，返回换下的武器的UniqueId
	TMap<ESlotInWeaponObj, FString> WearWeapon(FEquippedWeaponSet ToEquip);
	
	/**
	 * 脱掉身上某个部位对应的所有装备，并且返回
	 * @param OnPart 要脱掉装备的部位
	 * @return 脱下来的装备们
	 */
	TArray<FEquipment> UnwearEquipment(EChaPartType OnPart);
	// 脱掉武器
	FEquippedWeaponSet UnwearWeapon();
	/**
	 * 获得当前的武器类型，这个会被用于判断要做的动作
	 * @return 武器的类型，如果是UnArm就是收刀了
	 */
	UFUNCTION(BlueprintCallable)
	// 获得指定部位Equipment的指定的Index的FXKey
	EClassWeaponType CurrentWeaponType() const;
	FString EquipmentFXKey(EEquipmentPart OnPart, int FXIndex);
	// 获得身上所有的装备的值
	TArray<FEquipment> EquippedEquipments() const;
	// 刷新一下全身的装备
	void RefreshEquipmentAppearance();
	static bool CheckWearingEquipActorBindPoint(const AActor* Actor, FString BindPoint);
	FEquipment* GetEquipmentAtIndex(int Index);

	TMap<FString, UEquipmentBindPoint*> AllEquipmentBindPoints(){return this->EquipmentBindPoints;}
	TMap<FString, UMeshComponent*> AllBodySightParts(){return this->BodySightParts;}

	/**
	 * 获得应该显示那些外观
	 * @return 需要显示外观的装备
	 */
	TArray<FEquipmentAppearancePart> EquipmentsRequireShowAppearance();
	
	//道具（ItemObj）临时创建的模型等， 和装备差不多，但是不能存在道具里面
	UPROPERTY()
	TArray<AActor*> ItemsInHand;
	//清除所有的手里临时的道具
	void RemoveAllItemsInHand();
	//添加一个临时的手里道具
	void AddTempItemInHand(FEquipmentAppearancePart ItemApp);
	
	//角色身上的视觉部位记录，也就是来自装备等的，暂时挂在角色身上的东西
	UPROPERTY(BlueprintReadOnly)
	TArray<FWearingAppearanceInfo> WearingAppearance;

	//增加一个DamageDealer到ToBeDamaged以便于进行伤害管理
	void AddBeDamaged(FDamageDealer DamageDealer)
	{
		this->ToBeDamaged.Add(DamageDealer);
	}

	//判断一个Action是否是受伤的Action
	bool IsHurtAction(FActionInfo ActionInfo) const;

	/**
	 * 改变Actions中的某个Action的值
	 * @param ActionOriginId 要改的action的Id，这是指Action在json表时候的id
	 * @param Modifer 要改变的手法
	 * @return 改没改成，没找到自然就改不成了
	 */
	bool ModifyActionInfo(FString ActionOriginId, FActionInfoModifyApplication Modifer) const;

	/**
	 * 更换一个主技能
	 * @param ActionSelection 从哪一个组里面换
	 * @param SelectActionId 现在选中的这个Action的Id（OriginId）
	 */
	UFUNCTION()
	void ChangeMainAction( FActionSelection ActionSelection, FString SelectActionId) const ;

	//设置可拾取，只对玩家操作角色有效
	UFUNCTION()
	void SetPickupControl(bool On) const;

	UFUNCTION()
	void AddElementalTriggerTags(TArray<FString> ToAddTags);
	UFUNCTION()
	void RemoveElementalTriggerTags(TArray<FString> ToAddTags);
	UFUNCTION()
	void CleanElementalTriggerTag();
	UFUNCTION()
	bool HasAnyElementalTriggerTag(TArray<FString> CheckTags) const;
	UFUNCTION()
	bool HasElementalTriggerTag(FString CheckTag) const;
	UFUNCTION()
	bool HasAllTheElementalTriggerTags(TArray<FString> CheckTags) const;

	UFUNCTION()
	void AddDragMove(FDragMoveInfo Drag) const;
	UFUNCTION()
	void AddSqueeze(FVector SqueezeForce) const ;
	UFUNCTION(BlueprintCallable)
	void SetSqueezeType(ESqueezeType Type) const;
	
	/**
	 *更换职业，注意，只有玩家操作的角色可以更换（Role.MainActor）
	 *@param ToBattleClassId 要换的职业id
	 *@return 换没换成，如果不是玩家角色肯定失败
	 */
	UFUNCTION(BlueprintCallable)
	bool ChangeBattleClass(FString ToBattleClassId);

	/**
	 * Achievement接受信号
	 * 任何条件Beep了Character，通过Character来判断是否增加Achievement的进度
	 * @param SignalKey 信号
	 * @return 升级的成就id
	 */
	UFUNCTION(BlueprintCallable)
	TArray<FString> AchievementSignalBeep(FString SignalKey);

	/**
	 * 激活角色，开始走tick
	 */
	UFUNCTION(BlueprintCallable)
	void GetReady();

	/**
	 * 开启自动瞄准
	 * @param TargetYaw 目标转到多少度（-180到180）
	 * @param FixUnderDegree 与最终转向目标多少度以内，我才修正到这个角度
	 */
	UFUNCTION(BlueprintCallable)
	void SetFixRotation(float TargetYaw, float FixUnderDegree) const;
	UFUNCTION(BlueprintCallable)
	void StopFixRotation();
	
	/**
	 * 当前角色是否处于交战状态
	 */
	UFUNCTION(BlueprintCallable)
	bool InWar() const;

	/**
	 * 根据某个绑点获得坐标，绑点不存在就会返回角色现在坐标
	 * @param BindPointId 绑点的id
	 * @return 
	 */
	FTransform GetTransformByBindPointId(FString BindPointId);

	//暂停这个角色
	UFUNCTION(BlueprintCallable)
	void Pause();

	//恢复这个角色
	UFUNCTION(BlueprintCallable)
	void Resume();

// ---------- Set Glow ----------
	
	UFUNCTION(BlueprintCallable)
	void AddGlowExtendInfo(FGlowExtendTweenInfo Info);

	UFUNCTION(BlueprintCallable)
	TArray<FGlowExtendTweenInfo> GetGlowExtendInfos(){return GlowExtents;}
	UFUNCTION(BlueprintCallable)
	void EmptyGlowExtendInfos()
	{
		GlowExtents.Empty();
	}
private:
	float LastGlowExtent;
	FLinearColor LastGlowColor;
	/**
	 * @brief 不同MontageState设置的GlowExtent
	 */
	UPROPERTY()
	TArray<FGlowExtendTweenInfo> GlowExtents;

	/**
	 * 获得当前合适的Glow Extend Value
	 */
	UFUNCTION()
	float CurrentGlowExtends();

	/**
	 * 获取当前合适的外发光颜色
	 */
	UFUNCTION()
	FLinearColor CurrentGlowColor();

	/**
	 * 获得所有显示用的部位
	 */
	UFUNCTION()
	TArray<UActorComponent*> GetAllViewActorComponents();

	/**
	 * @brief 设置角色发光效果
	 * @param GlowExtent 0~1
	 * @param GlowColor 外发光颜色，正常值0~255对应0.0~1.0，因为是自发光所以值可以超过1.0以表示发光颜色强烈程度
	 */
	UFUNCTION()
	void SetGlowExtent(float GlowExtent, FLinearColor GlowColor = FLinearColor(23.0f,7.0f,0.0f));
// ---------- ---------- ----------

// ---------- Set Dissolve ----------
private:
	// 溶解程度 From
	UPROPERTY()
	float Dissolve_AmountFrom = 1.0f;
	// 溶解程度 To
	UPROPERTY()
	float Dissolve_AmountTo = 2.0f;
	// 溶解程度 变化时间
	UPROPERTY()
	float Dissolve_AmountDuration = 4;
	// 溶解效果亮光的宽度 From
	UPROPERTY()
	float Dissolve_WidthFrom = 0.0f;
	// 溶解效果亮光的宽度 To
	UPROPERTY()
	float Dissolve_WidthTo = 0.05f;
	// 溶解效果亮光的宽度 变化时间
	UPROPERTY()
	float Dissolve_WidthDuration = 0.1f;
	// 溶解效果计时器
	UPROPERTY()
	float Dissolve_Timer = 0.0f;
	UPROPERTY()
	bool Dissolve_Enable = false;
	
	/**
	 * @brief 没帧设置材质溶解效果
	 */
	UFUNCTION()
	void TickChangeDissolve(float DeltaTime);
public:
	/**
	 * @brief 开始材质溶解效果
	 */
	UFUNCTION()
	void StartDissolve() { Dissolve_Enable = true; }
	
// ---------- ---------- ----------
public:
	
	/**
	 * 头顶泡泡说话
	 * @param Text 说话得内容
	 * @param ChaName 显示的说话角色名
	 * @param InSec 显示多少秒
	 */
	void ShowTextBubble(FString Text, FString ChaName, float InSec = 10);

	/**
	 * 获得某个index的DialogCamera
	 */
	UCloseUpShotCamera* GetDialogCameraByIndex(int Index);

	/**
	 * 暂停和回复角色AI
	 */
	void PauseAI(bool DoPause = true) const;

	//-----------------Visible In Scene--------------------------------
	/**
	 * 隐藏掉这个角色，在他碍眼的情况下，最好能有个过程，不过大概率也不太容易做
	 * TODO: 遨游根
	 */
	void HideInCamera();

	/**
	 * 取消隐藏，也就是上面这个功能反过来
	 * TODO: 遨游根
	 */
	void RestoreInCamera();

	/**
	 * 获取这个角色的npcId
	 */
	FString GetNpcId();


	/**
	 * 设置进入战斗状态
	 */
	void SetInWarDuration(float Time);

	/**
	 *添加一条伤害记录
	 *@param Record 要添加的记录
	 */
	void AddHitRecord(FOffenseHitRecord Record) const;
	
	//抓取的处理
	void CatchTarget(AAwCharacter* Target, FOffenseInfo Info, USceneComponent* HitBox);

	/**
	 * 破坏掉某个部位，调用这个，是不会触发摧毁部位时候回调的，因为这不属于摧毁，只是把部位关闭了
	 * @param PartId 部位的Id
	 */
	void BreakChaPart(FString PartId);

	/**
	 * 回复一个部位
	 */
	void RestoreChaPart(FString PartId, TArray<int> MaxDurability);

	/**
	 * 是否应该执行AI？
	 */
	bool ShouldRunAI() const;

	/**
	 * 当前的AI Use Action Tags，这些Tags用于AIPostCommand中回调函数的第二个参数
	 */
	TArray<FString> CurrentUseActionTags() const;

	/**
	 * 设置当前的AI Use Action Tags，这些Tags用于AIPostCommand中回调函数的第二个参数
	 */
	void SetUseActionTags(TArray<FString> UseActionTags) const;

	/**
	 * 获得当前AIComponent中认为应当进行的转向
	 */
	FVector GetAITargetDirection() const;

	/**
	 * 告知动作系统，设置为武装或者非武装状态
	 * @param ToArms 是否要设置为武装(true)状态
	 */
	void SetToArms(EArmState ToArms) const;

	/**
	 * 获取当前武装状态
	 */
	EArmState GetArmState() const;
	
	/**
	 * 最后一次被设置的AimDirection
	 * 这东西是需要手动（外部调用）来设置的，他的设置规则是不定的，请在需要用的时候去设置它
	 * 比如需要角色向Aim方向射箭的话。
	 * 部分Montage可以调用SetAimDirection来设置这个值，以便后续Montage的一些Notify可以
	 * 使用这值的Pitch等来做计算。
	 */
	FRotator AimDirection;
	/**
	 * 角色正在瞄准的方向，通常来说只有玩家角色需要，但是也允许AI使用这个
	 */
	FRotator SetAimDirection() ;

	/**
	 * 设置角色动作倍率变化
	 */
	void SetSpeedInputAcceptanceModifier(FInputAcceptanceModifier Modifer);

	/**
	 * 返回是否是同族
	 * @param Target 检查的目标
	 * @return 是否是
	 */
	bool IsSameClan(const AAwCharacter* Target) const;

	/**
	 * 开关当前动画是否加入候选列表的设定
	 * @param Active 是否开启
	 */
	void SetCurrentActionToActionCheckList(bool Active = true) const;

	/**
	 * 在身上开启一个特效，这个特效需要手动关闭，所以如果预约的Id已存在，就不会允许开启
	 * @param EffectPath 特效路径
	 * @param Id 特效预约的Id
	 * @param BindOn 绑点类别(FXPoint的名称）
	 * @param PlayOnce 是否特效只播放一次就释放掉
	 * @return 是否开启成功了
	 */
	bool PlayAttachedVFX(FString EffectPath, FString Id, EVFXBindPointType BindOn, bool PlayOnce = true);
	
	/**
	 * 关闭身上一个播放的特效
	 * @param Id 特效预约的Id
	 */
	void StopAttachedVFX(FString Id);

	/**
	 * 显示或者隐藏对话交互提示
	 */
	void InteractWidgetVisible(bool SetTo) const;

	/**
	 * 用于类似传送功能，隐藏该角色所有模型并且关闭受击盒与其他角色的碰撞
	 **/
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable)
	void HideMeshAndCollision(bool bHide);

	/**
	 * 一些角色的出现效果实现
	 **/
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable)
	void PlaySpawnFX();

	virtual  void OnLockTargetSwitch_Implementation(bool bLeft);

	/**
	 * 开关角色头上的名字UI
	 **/
	UFUNCTION(BlueprintImplementableEvent, BlueprintCallable)
	void HideBubbleWidget(bool bHide);

	/**
	 * 进入对话时角色需要做的一些特殊处理
	 **/
	UFUNCTION(BlueprintImplementableEvent, BlueprintCallable)
	void OnStartDialog();

	/**
	 * 离开对话时角色需要做的一些特殊处理
	 **/
	UFUNCTION(BlueprintImplementableEvent, BlueprintCallable)
	void OnEndDialog();

	UFUNCTION(BlueprintImplementableEvent, BlueprintCallable)
	void SetupSkin(int SkinId);
	
	UFUNCTION(BlueprintCallable)
	void SetSkin(USkeletalMeshComponent* InMesh, TMap<int, UMaterialInterface*> Materials);
	
	UFUNCTION(BlueprintCallable)
	void SetChildSkin(UChildActorComponent* ChildActor, UMaterialInterface* Material);
	
};

