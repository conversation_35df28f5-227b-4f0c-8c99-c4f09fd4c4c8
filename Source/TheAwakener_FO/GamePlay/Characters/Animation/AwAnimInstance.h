// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "AnimFreezeInfo.h"
#include "Animation/AnimInstance.h"
#include "Animation/BlendSpace.h"
#include "TheAwakener_FO/GamePlay/Characters/Action/ActionPlanInfo.h"
#include "AwAnimInstance.generated.h"

class AAwCharacter;

USTRUCT(BlueprintType)
struct FMoveBlendSpace
{
	GENERATED_BODY()

public:
	UPROPERTY(BlueprintReadWrite)
	UBlendSpace* MoveBlendSpace = nullptr;
	UPROPERTY(BlueprintReadWrite)
	UBlendSpace* MoveBlendSpace_Start = nullptr;
	UPROPERTY(BlueprintReadWrite)
	UBlendSpace* MoveBlendSpace_End = nullptr;
	UPROPERTY(BlueprintReadWrite)
	bool HasStart = false;
	UPROPERTY(BlueprintReadWrite)
	bool HasEnd = false;
	
	bool operator == (const FMoveBlendSpace& Other) const
	{
		if (MoveBlendSpace == nullptr || MoveBlendSpace_Start == nullptr || MoveBlendSpace_End == nullptr)
			return false;

		if (MoveBlendSpace == Other.MoveBlendSpace &&
			MoveBlendSpace_Start == Other.MoveBlendSpace_Start &&
			MoveBlendSpace_End == Other.MoveBlendSpace_End)
		{
			// UKismetSystemLibrary::PrintString(GWorld, "Move BS is true !");
			return true;
		}
		
		return false;
	}
};

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UAwAnimInstance : public UAnimInstance
{
	GENERATED_BODY()
private:
	//卡帧信息，只能有一个卡帧信息，新的取代老的，不然会出事儿
	FAnimFreezeInfo FreezeInfo;
	bool bIsFreezing = false;
public:
	//是否暂停了，逻辑上的暂停了
	UPROPERTY()
	bool Paused = false;
	bool GetIsFreezing() const { return bIsFreezing; }
	//当前播放的蒙太奇GUID 用于给动画事件判断是否处于同一蒙太奇下
	UPROPERTY()
	FString CurMontageUID = "" ;
	
	UPROPERTY()
	int MoveBlendSpaceIndex = 0;

	// --- Move BlendSpace ---
	UPROPERTY(BlueprintReadWrite)
	UBlendSpace* MoveBlendSpace;
	UPROPERTY(BlueprintReadWrite)
	UBlendSpace* MoveBlendSpace_Start;
	UPROPERTY(BlueprintReadWrite)
	UBlendSpace* MoveBlendSpace_End;

	UPROPERTY(BlueprintReadWrite)
	bool IsPlayMoveBlendSpaceA = true;
	UPROPERTY(BlueprintReadWrite)
	FMoveBlendSpace MoveBs_A;
	UPROPERTY(BlueprintReadWrite)
	FMoveBlendSpace MoveBs_B;
	// --- --- ---
	
	// --- Aim BlendSpace ---
	// 瞄准的 BlendSpace
	UPROPERTY(BlueprintReadWrite)
	UBlendSpace* AimBlendSpace;
	// 是否开启瞄准的 BlendSpace
	UPROPERTY(BlueprintReadWrite)
	bool bIsPlayAimBlendSpace;
	// 瞄准的上下偏移值 -1~1
	UPROPERTY(BlueprintReadWrite)
	float AimYaw = 0;
	// 瞄准的左右偏移值 -1~1
	UPROPERTY(BlueprintReadWrite)
	float AimPitch = 0;
	// --- --- ---

	UPROPERTY(BlueprintReadWrite)
	float TargetForwardSpeed;
	UPROPERTY(BlueprintReadWrite)
	float ForwardSpeed;
	UPROPERTY(BlueprintReadWrite)
	float StopForwardSpeed;
	UPROPERTY()
	float ForwardSpeedLimit = 3;	//最高单方向速度
	UPROPERTY(BlueprintReadWrite)
	float TargetSideways;			
	UPROPERTY(BlueprintReadWrite)
	float Sideways;					//横过来的SpeedLevel

	float ValueLerpSpeed = 10;// 1秒变换10（0->1 耗时 0.1秒）

	UPROPERTY(BlueprintReadWrite)
	bool InAiming;
	
	UPROPERTY(BlueprintReadWrite)
	AAwCharacter* Character;

	// 蒙太奇里设置的动画速率
	// FString - MontageUID
	// float - AnimRate
	TMap<FString, float> AnimRateScaleModifies;
	
	FAnimFreezeInfo GetFreezeInfo() const;
	
	virtual void NativeUpdateAnimation(float DeltaSeconds) override;
	
	/**
	 * @brief 播放移动动画
	 */
	void PlayMoveBlendSpace(FString AnimationPath);


	/**
	 * @brief 播放瞄准动画
	 * @param Index 瞄准的 BlendSpace 为 Class 表里的第几个
	 */
	void PlayAimBlendSpace(int Index);
	// 停止瞄准动画
	void StopAimBlendSpace();

	
	bool PlayActionMontage(FString MontagePath, FActionPlanInfo ActionPlanInfo);
	
	// 0-stand
	// 1-walk
	// 2-run
	// 3-sprint
	void SetAnimForwardSpeed(float NewForwardSpeed);
	void SetAnimForwardSpeedLimit(int Limit);
	void SetAnimSideways(float NewSideways);

	void TickLerp(float& Value, float TargetValue, float NewValueLerpSpeed, float DeltaSeconds) const;

	void FreezeAnim(float Time, FForceMoveInfo MoveAfterDefrozen);
	bool IsInFreezingPassedThreshold();
	void TerminateFreeze();
	
	void Tick(float DeltaTime);

	bool InFreezing() const;
};
