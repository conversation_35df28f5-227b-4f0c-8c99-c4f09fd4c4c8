// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "PhysicsEngine/PhysicalAnimationComponent.h"
#include "TheAwakener_FO/GamePlay/Characters/Battle/BattleStructs.h"
#include "AwPhysicalBodyAnim.generated.h"


UCLASS(Blueprintable,meta=(BlueprintSpawnableComponent))
class THEAWAKENER_FO_API UAwPhysicalBodyAnim : public UPhysicalAnimationComponent
{
	GENERATED_BODY()

public:
	// Sets default values for this component's properties
	UAwPhysicalBodyAnim();
	// Called every frame
	virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

	void InitData();
protected:
	// Called when the game starts
	virtual void BeginPlay() override;

public:
	//混合曲线
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
		UCurveFloat* BlendCurve;
	//伤害向量转换为冲力的基本倍率  最终倍率=基础倍率/优先级差距绝对值
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
		float ImpulseBasePower = 1000;
	//单次的最大混合时间 应与曲线归0时间对齐
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
		float MaxBlendTime;
	//冲力延迟应用时间
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
		float ImpulseBlendTime;
	//Physical Anim 具体参数
	//UPROPERTY(BlueprintReadWrite,EditAnywhere)
	//	FPhysicalAnimationData PhysicsSetting;
	UFUNCTION(BlueprintCallable)
		 void ApplyPhyscialAnimBlendByDamageInfo(FDamageInfo DamageInfo,float PartBlendWeight = 1.f);
private:
		//混合骨骼
		FName BlendBone;
		//混合冲力
		FVector  ImpulseVector;
		//当前混合时间
		float CurBlendTime;
		//混合权重
		float BlendWeight;

		bool bApplyImpulse = false;

		float PriorityWeight = 1.f;

		float HitPartBlendWeight = 1.f;
};


