// Fill out your copyright notice in the Description page of Project Settings.


#include "AwPhysicalBodyAnim.h"

#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"

// Sets default values for this component's properties
UAwPhysicalBodyAnim::UAwPhysicalBodyAnim()
{
	// Set this component to be initialized when the game starts, and to be ticked every frame.  You can turn these features
	// off to improve performance if you don't need them.
	PrimaryComponentTick.bCanEverTick = true;
	bAutoActivate = true;
	// ...
}


// Called when the game starts
void UAwPhysicalBodyAnim::BeginPlay()
{
	Super::BeginPlay();
	
	InitData();
	// ...
	
}

void UAwPhysicalBodyAnim::ApplyPhyscialAnimBlendByDamageInfo(FDamageInfo DamageInfo,float PartBlendWeight)
{
	if (!IsValid(GetSkeletalMesh())||DamageInfo.BeDodged)
	{
		return;
	}
	if (!IsValid(BlendCurve))
	{
		return;
	}
	if (DamageInfo.AttackInfoPriority>=DamageInfo.DefendInfoPriority)
	{
		return;
	}
	if (IsValid(DamageInfo.HitBox)&&CurBlendTime<=0.f)
	{
		HitPartBlendWeight = PartBlendWeight;
		BlendBone = DamageInfo.HitBox->GetAttachSocketName();
		if (GetSkeletalMesh()->DoesSocketExist(BlendBone))
		{
			ImpulseVector = FVector::ZeroVector;
			ImpulseVector= DamageInfo.DamageIncomeVector;
			ImpulseVector.Normalize();
			PriorityWeight = (1.5f+1.f/FMath::Clamp(DamageInfo.DefendInfoPriority - DamageInfo.AttackInfoPriority,2,8))/10;
			float ImpulsePower = ImpulseBasePower*PriorityWeight;
			ImpulseVector*= ImpulsePower;
			CurBlendTime = MaxBlendTime;
			//UKismetSystemLibrary::PrintString(this,BlendBone.ToString(),true,true,FColor::Blue,20.f);
			//PhysicsSetting.BodyName = BlendBone;
			bApplyImpulse = false;
		}
	}
}


// Called every frame
void UAwPhysicalBodyAnim::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
	Super::TickComponent(DeltaTime, TickType, ThisTickFunction);
	if (CurBlendTime>0&&IsValid(GetSkeletalMesh())&&IsValid(BlendCurve))
	{
		float CurveKey = MaxBlendTime -CurBlendTime;
		BlendWeight = BlendCurve->GetFloatValue(CurveKey);
		float FinalBlendWeight = BlendWeight*PriorityWeight*HitPartBlendWeight;
		FinalBlendWeight = FMath::Clamp(FinalBlendWeight,0,0.9f);
		if (FinalBlendWeight>0.f)
		{
			//ApplyPhysicalAnimationSettings(BlendBone,PhysicsSetting);
			GetSkeletalMesh()->SetAllBodiesBelowSimulatePhysics(BlendBone,true,true);
			GetSkeletalMesh()->SetAllBodiesBelowPhysicsBlendWeight(BlendBone,FinalBlendWeight,false,true);
			//UKismetSystemLibrary::PrintString(this,FString::SanitizeFloat(FinalBlendWeight)+"_"+FString::SanitizeFloat(PriorityWeight),true,true,FColor::Blue,DeltaTime);
			if (CurveKey>=ImpulseBlendTime&&!bApplyImpulse)
			{
				GetSkeletalMesh()->AddImpulse(ImpulseVector,BlendBone,true);
				bApplyImpulse = true;
			}
		}
		else
		{
			GetSkeletalMesh()->SetAllBodiesPhysicsBlendWeight(0.f);
			GetSkeletalMesh()->SetAllBodiesSimulatePhysics(false);
		}
		CurBlendTime -= DeltaTime;
		if (CurBlendTime<=0.f)
		{
			GetSkeletalMesh()->SetAllBodiesPhysicsBlendWeight(0.f);
			GetSkeletalMesh()->SetAllBodiesSimulatePhysics(false);
			HitPartBlendWeight= 1.f;
		}
	}
	// ...
}

void UAwPhysicalBodyAnim::InitData()
{
	ACharacter* CharacterOwner  = Cast<ACharacter>(GetOwner());
	if (IsValid(CharacterOwner))
	{
		if (!IsValid(GetSkeletalMesh()))
		{
			SetSkeletalMeshComponent(CharacterOwner->GetMesh());
		}
	}
}


