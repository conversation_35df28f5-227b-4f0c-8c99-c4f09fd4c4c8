// Fill out your copyright notice in the Description page of Project Settings.


#include "AvatarCharacter.h"

#include "Components/SkeletalMeshComponent.h"
#include "Components/StaticMeshComponent.h"
#include "Materials/Material.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"

AAvatarCharacter::AAvatarCharacter()
{
	PrimaryActorTick.bCanEverTick = true;
	
	Root = CreateDefaultSubobject<USceneComponent>(TEXT("Root"));
	Root->SetupAttachment(GetRootComponent());
	Root->SetRelativeTransform(FTransform::Identity);

	Mesh = CreateDefaultSubobject<USkeletalMeshComponent>(TEXT("Mesh"));
	Mesh->SetupAttachment(Root);
	Mesh->SetRelativeTransform(FTransform::Identity);
	Mesh->InitAnim(true);
}

void AAvatarCharacter::DestroyMe()
{
	this->RemoveAllEquipments();
	this->Destroy();
}

void AAvatarCharacter::CloneFromAwCharacter(AAwCharacter* Target, float Duration)
{
	if (!Target) return;
	
	this->CloneFrom = Target;

	this->SetActorTransform(Target->GetActorTransform());
	
	this->Mesh->SetSkeletalMesh(Target->GetMesh()->GetSkeletalMeshAsset());
	const FString AnimPath =
		//"ArtResource/Anim/BlendSpace/Warrior/Swordsman/Swordsman_Defense";
		//"ArtResource/Anim/Montage/Fighter/BladeDancer/ChangeToBladeDancer";
		"ArtResource/Anim/Animation/Player/Warrior_Female/Move/Battle/GreatSword_Idle";
	//this->Mesh->PlayAnimation(UResourceFunctionLib::LoadAnimAsset(AnimPath), true);
	this->Mesh->SetRelativeTransform(CloneFrom->GetMesh()->GetRelativeTransform());
	this->Mesh->SetAnimationMode(EAnimationMode::AnimationSingleNode);
	
	TMap<FString, UEquipmentBindPoint*> BindPoints = Target->AllEquipmentBindPoints();
	for (const TTuple<FString, UEquipmentBindPoint*> BindPoint : BindPoints)
	{
		UEquipmentBindPoint* BindPointComp = NewObject<UEquipmentBindPoint>(this);
		BindPointComp->RegisterComponent(); 
		if (!BindPointComp) continue;
		this->AddInstanceComponent(BindPointComp);
		BindPointComp->AttachToComponent(Mesh, FAttachmentTransformRules::SnapToTargetIncludingScale, BindPoint.Value->GetAttachSocketName());
		BindPointComp->SetRelativeTransform(BindPoint.Value->GetRelativeTransform());
		this->EquipmentBindPoints.Add(BindPoint.Key, BindPointComp);
	}
		
	TMap<FString, UMeshComponent*> AllBodyParts = Target->AllBodySightParts();
	for (const TTuple<FString, UMeshComponent*> BPart : AllBodyParts)
	{
		const USkeletalMeshComponent* SkBPart = Cast<USkeletalMeshComponent>(BPart.Value);
		if (SkBPart)
		{
			USkeletalMeshComponent* SkeletalMesh = NewObject<USkeletalMeshComponent>(this);
			SkeletalMesh->RegisterComponent();
			if (SkeletalMesh)
			{
				SkeletalMesh->SetSkeletalMesh(SkBPart->GetSkeletalMeshAsset());
				SkeletalMesh->SetLeaderPoseComponent(Mesh, true);
				this->AddInstanceComponent(SkeletalMesh);
				SkeletalMesh->AttachToComponent(Mesh, FAttachmentTransformRules::SnapToTargetIncludingScale, BPart.Value->GetAttachSocketName());
				this->BodySightParts.Add(BPart.Key, SkeletalMesh);
				SkeletalMesh->SetVisibility(SkBPart->IsVisible());
			}
		}
		
		const UStaticMeshComponent* StBPart = Cast<UStaticMeshComponent>(BPart.Value);
		if (StBPart)
		{
			UStaticMeshComponent* StaticMesh = NewObject<UStaticMeshComponent>(this);
			StaticMesh->RegisterComponent();
			if (StaticMesh)
			{
				this->AddInstanceComponent(StaticMesh);
				StaticMesh->AttachToComponent(Mesh, FAttachmentTransformRules::SnapToTargetIncludingScale, BPart.Value->GetAttachSocketName());
				StaticMesh->SetStaticMesh(StBPart->GetStaticMesh());
				StaticMesh->SetRelativeTransform(StBPart->GetRelativeTransform());
				this->BodySightParts.Add(BPart.Key, StaticMesh);
				StaticMesh->SetVisibility(StBPart->IsVisible());
			}
		}
	}
	
	RefreshEquipmentsByClone();

	this->LiveForever = Duration <= 0;
	this->LifeTime = Duration;

	this->PlayBlendSpaceState(ECharacterActionState::Ground, 0, 0, true);
}

UEquipmentBindPoint* AAvatarCharacter::GetBindPointByName(FString Name)
{
	if (!EquipmentBindPoints.Contains(Name)) return nullptr;
	return EquipmentBindPoints[Name];
}

void AAvatarCharacter::RefreshEquipmentsByClone()
{
	if (!this->CloneFrom) return;
	WearEquipments(this->CloneFrom->EquipmentsRequireShowAppearance());
}

bool AAvatarCharacter::AppearanceDataEqualsTo(TArray<FEquipmentAppearancePart> Data) const
{
	return this->AppearanceData == Data;
}

void AAvatarCharacter::WearEquipments(TArray<FEquipmentAppearancePart> Equipments)
{
	//如果是完全一样的，就不弄了
	if (this->GetAppearanceData() == Equipments) return;
	
	this->RemoveAllEquipments();
	this->AppearanceData = Equipments;
	//先算出来要显示什么
	TMap<FString, FEquipmentAppearancePart> ToShow; //<PartSlot, Equipment>
	TArray<FString> HideBodyParts;
	for (int i = 0; i < Equipments.Num(); i++)	//严肃顺序
	{
		FString SlotId = Equipments[i].PartSlot;
		if (ToShow.Contains(SlotId) == false)
		{
			ToShow.Add(SlotId, Equipments[i]);
		}else if (ToShow[SlotId].Priority <=  Equipments[i].Priority)	//如果和本体不符合，那就<=改成<就完事儿了
		{
			ToShow[SlotId] = Equipments[i];
		}
	}
	//挨个显示一下
	for (TTuple<FString, FEquipmentAppearancePart> Part : ToShow)
	{
		AActor* View =  UGameplayFuncLib::CreateActorByBP( Part.Value.BluePrintPath, FTransform());
		this->WearingAppearance.Add(View);
		UEquipmentBindPoint* BindPoint = GetBindPointByName(Part.Value.BindPointIds[static_cast<int32>(CloneFrom->GetArmState())]);
		if ( View && BindPoint)
		{
			View->AttachToComponent(BindPoint, FAttachmentTransformRules::SnapToTargetIncludingScale);
			if (Part.Value.Type == EEquipmentAppearanceType::SkinnedMesh)
			{
				UActorComponent* Comp = View->GetComponentByClass(USkeletalMeshComponent::StaticClass());
				if (Comp)
				{
					USkeletalMeshComponent* MeshComp = Cast<USkeletalMeshComponent>(Comp);
					if (MeshComp)
					{
						MeshComp->SetLeaderPoseComponent(Mesh, true);
					}
				}
			}else if (Part.Value.Type == EEquipmentAppearanceType::Physical)
			{
				UActorComponent* Comp = View->GetComponentByClass(USkeletalMeshComponent::StaticClass());
				if (Comp)
				{
					USkeletalMeshComponent* MeshComp = Cast<USkeletalMeshComponent>(Comp);
					for (FString PhysicalBoneName : Part.Value.PhysicalBoneName)
						MeshComp->SetAllBodiesBelowSimulatePhysics(FName(*PhysicalBoneName), true, true);
				}
			}
		}
		for (FString HPart : Part.Value.HideBodyParts)
		{
			if (HideBodyParts.Contains(HPart) == false)
				HideBodyParts.Add(HPart);
		} 
	}
	
	for (const TTuple<FString, UMeshComponent*>& BSPart : this->BodySightParts)
	{
		if (BSPart.Value)
			BSPart.Value->SetVisibility(!HideBodyParts.Contains(BSPart.Key));
	} 
}

void AAvatarCharacter::RemoveAllEquipments()
{
	for (AActor* App : this->WearingAppearance)
	{
		if (App)
			App->Destroy();
	}
	WearingAppearance.Empty();
	AppearanceData.Empty();
}

void AAvatarCharacter::Tick(float DeltaSeconds)
{
	if (this->ToBeRemoved == true) return;
	if (this->DelayAnim.Active == true)
	{
		if (this->DelayAnim.TimeToPlay > 0)
		{
			this->DelayAnim.TimeToPlay -= DeltaSeconds;
		}else
		{
			this->Mesh->SetPosition(DelayAnim.FromSec);
			//this->Mesh->bPauseAnims = false;
			this->Mesh->Play(DelayAnim.Loop);
			this->DelayAnim.Active = false;
		}
	}
	if (this->LiveForever == false)
	{
		if (LifeTime > 0)
			this->LifeTime -= DeltaSeconds;
		else
		{
			ToBeRemoved = true;
			SetToShown(false);
			//this->DestroyMe();
		}
	}
}

void AAvatarCharacter::SetToShown(bool Show)
{
	this->SetActorHiddenInGame(!Show);
	for (AActor* App : this->WearingAppearance) App->SetActorHiddenInGame(!Show); 
}

void AAvatarCharacter::SetTransparentMaterial(FLinearColor Color)
{
	UMaterial* ColorMaterial = LoadObject<UMaterial>(
		nullptr, TEXT("/Game/MaterialLibrary/Debug/Transparent.Transparent"));
	if (ColorMaterial == nullptr)
		return;
	
	TArray<USkeletalMeshComponent*> SkeletalMeshes = TArray<USkeletalMeshComponent*>();
	TArray<UStaticMeshComponent*> StaticMeshes = TArray<UStaticMeshComponent*>();

	SkeletalMeshes.Add(Mesh);
	
	for (TTuple<FString, UMeshComponent*> Part : BodySightParts)
	{
		USkeletalMeshComponent* SkMesh = Cast<USkeletalMeshComponent>(Part.Value);
		if (SkMesh)
		{
			SkeletalMeshes.Add(SkMesh);
			continue;
		}
		UStaticMeshComponent* StMesh = Cast<UStaticMeshComponent>(Part.Value);
		if (StMesh)
		{
			StaticMeshes.Add(StMesh);
			continue;
		}
	}

	for (const AActor* Appearance : WearingAppearance)
	{
		if(!IsValid(Appearance))
			continue;
		for (UActorComponent* Component : Appearance->GetComponents())
		{
			USkeletalMeshComponent* SkeletalMesh = Cast<USkeletalMeshComponent>(Component);
			if (SkeletalMesh)
				SkeletalMeshes.Add(SkeletalMesh);

			UStaticMeshComponent* StaticMesh = Cast<UStaticMeshComponent>(Component);
			if (StaticMesh)
				StaticMeshes.Add(StaticMesh);
		}
	}

	if (SkeletalMeshes.Num() == 0 && StaticMeshes.Num() == 0)
		return;
	
	for (USkeletalMeshComponent* ThisMesh : SkeletalMeshes)
	{
		for (int i = 0; i < ThisMesh->GetMaterials().Num(); i++)
		{
			UMaterialInstanceDynamic* ColorMaterial_Inst = UMaterialInstanceDynamic::Create(ColorMaterial, ThisMesh);
			ColorMaterial_Inst->SetVectorParameterValue(FName(TEXT("Color")), Color);
			// ThisMesh->SetMaterial(i, ColorMaterial);
			if (i < ThisMesh->OverrideMaterials.Num())
				ThisMesh->OverrideMaterials[i] = ColorMaterial_Inst;	
			else
				ThisMesh->OverrideMaterials.Add(ColorMaterial_Inst);
			// ThisMesh->SetVisibility(false);
			// ThisMesh->SetVisibility(true);
			ThisMesh->MarkRenderStateDirty();
		}
	}
	
	for (UStaticMeshComponent* ThisMesh : StaticMeshes)
	{
		for (int i = 0; i < ThisMesh->GetMaterials().Num(); i++)
		{
			UMaterialInstanceDynamic* ColorMaterial_Inst = UMaterialInstanceDynamic::Create(ColorMaterial, ThisMesh);
			ColorMaterial_Inst->SetVectorParameterValue(FName(TEXT("Color")), Color);
			// ThisMesh->SetMaterial(i, ColorMaterial);
			if (i < ThisMesh->OverrideMaterials.Num())
				ThisMesh->OverrideMaterials[i] = ColorMaterial_Inst;	
			else
				ThisMesh->OverrideMaterials.Add(ColorMaterial_Inst);
		}
		// ThisMesh->SetVisibility(false);
		// ThisMesh->SetVisibility(true);
		ThisMesh->MarkRenderStateDirty();
	}
}

void AAvatarCharacter::ResetTransparentMaterial()
{
	TArray<USkeletalMeshComponent*> SkeletalMeshes = TArray<USkeletalMeshComponent*>();
	TArray<UStaticMeshComponent*> StaticMeshes = TArray<UStaticMeshComponent*>();
		
	for (TTuple<FString, UMeshComponent*> Part : BodySightParts)
	{
		USkeletalMeshComponent* SkMesh = Cast<USkeletalMeshComponent>(Part.Value);
		if (SkMesh)
		{
			SkeletalMeshes.Add(SkMesh);
			continue;
		}
		UStaticMeshComponent* StMesh = Cast<UStaticMeshComponent>(Part.Value);
		if (StMesh)
		{
			StaticMeshes.Add(StMesh);
			continue;
		}
	}

	for (const AActor* Appearance : WearingAppearance)
	{
		for (UActorComponent* Component : Appearance->GetComponents())
		{
			USkeletalMeshComponent* SkeletalMesh = Cast<USkeletalMeshComponent>(Component);
			if (SkeletalMesh)
				SkeletalMeshes.Add(SkeletalMesh);

			UStaticMeshComponent* StaticMesh = Cast<UStaticMeshComponent>(Component);
			if (StaticMesh)
				StaticMeshes.Add(StaticMesh);
		}
	}

	if (SkeletalMeshes.Num() == 0 && StaticMeshes.Num() == 0)
		return;
	
	for (USkeletalMeshComponent* ThisMesh : SkeletalMeshes)
	{
		ThisMesh->OverrideMaterials.Empty();
		// ThisMesh->SetVisibility(false);
		// ThisMesh->SetVisibility(true);
		ThisMesh->MarkRenderStateDirty();
	}

	for (UStaticMeshComponent* ThisMesh : StaticMeshes)
	{
		ThisMesh->OverrideMaterials.Empty();
		// ThisMesh->SetVisibility(false);
		// ThisMesh->SetVisibility(true);
		ThisMesh->MarkRenderStateDirty();
	}
}

AActor* AAvatarCharacter::ShowAppearance(FEquipmentAppearancePart AppearanceEquipment)
{
	if (AppearanceEquipment.BluePrintPath.IsEmpty()) return nullptr;

	const FString BindPointId = AppearanceEquipment.BindPointIds[static_cast<int32>(CloneFrom->GetArmState())];
	if (!EquipmentBindPoints.Contains(BindPointId)) return nullptr;
	UEquipmentBindPoint* BiPoint = EquipmentBindPoints[BindPointId];
	if (!BiPoint) return nullptr;
	
	AActor* Res = UGameplayFuncLib::CreateActorByBP(AppearanceEquipment.BluePrintPath, FTransform());
	if (Res)
	{
		Res->AttachToComponent(BiPoint, FAttachmentTransformRules::SnapToTargetIncludingScale);
		if (AppearanceEquipment.Type == EEquipmentAppearanceType::SkinnedMesh)
		{
			UActorComponent* Comp = Res->GetComponentByClass(USkeletalMeshComponent::StaticClass());
			if (Comp)
			{
				USkeletalMeshComponent* MeshComp = Cast<USkeletalMeshComponent>(Comp);
				if (MeshComp)
				{
					MeshComp->SetLeaderPoseComponent(Mesh, true);
				}
			}
		}else if (AppearanceEquipment.Type == EEquipmentAppearanceType::Physical)
		{
			UActorComponent* Comp = Res->GetComponentByClass(USkeletalMeshComponent::StaticClass());
			if (Comp)
			{
				USkeletalMeshComponent* MeshComp = Cast<USkeletalMeshComponent>(Comp);
				for (FString PhysicalBoneName : AppearanceEquipment.PhysicalBoneName) 
					MeshComp->SetAllBodiesBelowSimulatePhysics(FName(*PhysicalBoneName), true, true);
			}
		}
	}
	return Res;
}

void AAvatarCharacter::ClearAnimation()
{
	Mesh->SetAnimationMode(EAnimationMode::AnimationCustomMode);
}

void AAvatarCharacter::PlayAnimByActionInfo(FActionInfo Action, int Index, float DelayToPlay, bool Loop)
{
	if (Action.Anim.AnimPath.Num() <= 0) return;	//没法播放
	Index = FMath::Clamp(Index, 0, Action.Anim.AnimPath.Num() - 1);
	const FString AnimPath = Action.Anim.AnimPath[Index];
	this->Mesh->SetAnimation(UResourceFuncLib::LoadAnimAsset(AnimPath));
	this->Mesh->SetAnimationMode(EAnimationMode::AnimationSingleNode);
	this->Mesh->Play(Loop);
	if (DelayToPlay > 0)
	{
		this->DelayAnim = FCloneDelayAnimInfo(DelayToPlay, true);
		this->Mesh->bPauseAnims = true;
	}
}

void AAvatarCharacter::PlayMontageState(ECharacterMontageState MontageState , int Index, float DelayToPlay, bool Loop)
{
	if (!CloneFrom) return;
	const FActionInfo* MAction = CloneFrom->GetActionByMontageState(MontageState);
	if (!MAction) return;
	this->PlayAnimByActionInfo(*MAction, Index, DelayToPlay, Loop);
}

void AAvatarCharacter::PlayBlendSpaceState(ECharacterActionState ActionState , int Index, float DelayToPlay, bool Loop)
{
	if (!CloneFrom) return;
	const FActionInfo* MAction = CloneFrom->GetActionByActionState(ActionState);
	if (!MAction) return;
	this->PlayAnimByActionInfo(*MAction, Index, DelayToPlay, Loop);
}

void AAvatarCharacter::PlayCloneAnim(bool ContinuePlay, float DelayToPlay, bool Loop)
{
	if (!this->CloneFrom || !CloneFrom->CurrentAction()) return;
	const FActionInfo CurAct = *CloneFrom->CurrentAction();
	const FAnimMontageInstance* CurMonIns = CloneFrom->GetActiveMontageInstance();
	
	if (CurMonIns)
	{
		UAnimationAsset* ToPlay = UResourceFuncLib::LoadAnimAsset(CurMonIns->Montage->GetFullName(), false);
		const float MontPos = CurMonIns->GetPosition();
	
		this->Mesh->PlayAnimation(ToPlay, Loop);
		this->Mesh->SetPosition(MontPos);
		
		if (DelayToPlay > 0)
		{
			this->DelayAnim = FCloneDelayAnimInfo(DelayToPlay, MontPos, Loop);
		}
		if (DelayToPlay > 0 || ContinuePlay == false)
		{
			Mesh->Stop();
		}
	}else
	{
		
		this->PlayAnimByActionInfo(CurAct, 0, DelayToPlay, Loop);
	}
}

TArray<FEquipmentAppearancePart> AAvatarCharacter::GetAppearanceData()
{
	return this->AppearanceData;
}

void AAvatarCharacter::SetLifeDuration(float ToDuration, bool InfinityIfNegativeDuration)
{
	this->LifeTime = ToDuration;
	this->LiveForever = InfinityIfNegativeDuration == true && ToDuration < 0;
}