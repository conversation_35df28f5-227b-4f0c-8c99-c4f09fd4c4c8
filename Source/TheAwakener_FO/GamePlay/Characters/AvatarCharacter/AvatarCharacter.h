// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "AvatarCharacter.generated.h"

/**
 * 克隆会晚点播放动作的信息
 */
USTRUCT()
struct FCloneDelayAnimInfo
{
	GENERATED_BODY()
public:
	UPROPERTY()
	bool Active = false;	//是否有晚点播放的预约

	UPROPERTY()
	float TimeToPlay = 0;	//多久以后播放

	UPROPERTY()
	bool Loop = false;	//是否循环播放

	UPROPERTY()
	float FromSec = 0;	//从哪儿开始播放

	FCloneDelayAnimInfo(){}
	FCloneDelayAnimInfo(float DelayToPlay,float PlayFromSec, bool DoLoop = false):
		Active(true),TimeToPlay(DelayToPlay), Loop(DoLoop), FromSec(PlayFromSec){};
};



/**
 * 角色的残像，可以用于换装备的角色
 * 什么？分身还要打人？还能战斗？走这里派生出去吧，加上AttackHitBox之类的就行了
 */
UCLASS()
class THEAWAKENER_FO_API AAvatarCharacter : public APawn
{
	GENERATED_BODY()
private:
	
protected:
	
	UPROPERTY()
	USceneComponent* Root = nullptr;
	
	//被克隆的角色
	UPROPERTY()
	AAwCharacter* CloneFrom = nullptr;

	UPROPERTY(Category=Character, VisibleAnywhere, BlueprintReadOnly, meta=(AllowPrivateAccess = "true"))
	USkeletalMeshComponent* Mesh = nullptr;
	
	//角色装备绑点<id, 绑点>
	UPROPERTY()
	TMap<FString, UEquipmentBindPoint*> EquipmentBindPoints;

	//身体上可以被装备替换的外观部位（尸块）
	UPROPERTY()
	TMap<FString, UMeshComponent*> BodySightParts;

	//角色身上的视觉部位记录，也就是来自装备等
	UPROPERTY()
	TArray<AActor*> WearingAppearance;

	//用来对比的装备信息，这会在WearAppearances()的时候对比并进行赋值
	UPROPERTY()
	TArray<FEquipmentAppearancePart> AppearanceData;
	bool AppearanceDataEqualsTo(TArray<FEquipmentAppearancePart> Data) const;

	UEquipmentBindPoint* GetBindPointByName(FString Name);

	UPROPERTY()
	FCloneDelayAnimInfo DelayAnim;

	UPROPERTY()
	bool LiveForever = true;	//是否永存的
	UPROPERTY()
	float LifeTime = 0;	//非永存剩余时间
public:
	AAvatarCharacter();

	void DestroyMe();

	virtual void Tick(float DeltaSeconds) override;

	UPROPERTY(BlueprintReadOnly)
	bool ToBeRemoved = false;

	//从一个角色这里克隆而来
	UFUNCTION(BlueprintCallable)
	virtual void CloneFromAwCharacter(AAwCharacter* Target, float Duration = -1);

	//刷新装备，根据CloneFrom的角色信息来刷新
	UFUNCTION(BlueprintCallable)
	void RefreshEquipmentsByClone();

	/**
	 * 谨慎调用！！
	 * 根据传进来的装备数组，显示内容（会先removeAll）
	 * 我会绝对信任当前部位都是当前耐久度下可以装备的（所以要注意耐久度问题）
	 */
	UFUNCTION(BlueprintCallable)
	void WearEquipments(TArray<FEquipmentAppearancePart> Equipments);

	//移除掉所有的装备
	UFUNCTION(BlueprintCallable)
	void RemoveAllEquipments();

	//获得装备的数据，用来对比是不是装备换过了
	TArray<FEquipmentAppearancePart> GetAppearanceData();
	

	/**
	 *做某个ActionInfo对应的动作
	 *@param Action 需要做动作的ActionInfo
	 *@param Index 由于没有运行时，所以无法用AnimPicker来获取到播放的动画下标，只能手工给一个了
	 *@param DelayToPlay 多少秒之后才开始播放动画
	 *@param Loop 是否循环播放
	 */
	UFUNCTION(BlueprintCallable)
	void PlayAnimByActionInfo(FActionInfo Action, int Index = 0, float DelayToPlay = 0, bool Loop = false);

	/**
	 *产生一个Clone目标正在做的动作的当前动画
	 *@param ContinuePlay 是否跟着继续播放
	 *@param DelayToPlay 延迟多久（秒）播放
	 *@param Loop 是否要循环播放，如果ContinuePlay都是false，就没啥意义了
	 */
	UFUNCTION(BlueprintCallable)
	void PlayCloneAnim(bool ContinuePlay = false, float DelayToPlay = 0, bool Loop = false);

	/**
	 * 播放一个CloneFrom的MontageState动作
	 * @param MontageState 要播放的MontageState
	 * @param Index 由于没有运行时，所以无法用AnimPicker来获取到播放的动画下标，只能手工给一个了
	 * @param DelayToPlay 延迟多久（秒）去播放
	 * @param Loop 是否循环播放
	 */
	UFUNCTION(BlueprintCallable)
	void PlayMontageState(ECharacterMontageState MontageState, int Index = 0, float DelayToPlay = 0, bool Loop = false);

	/**
	 * 播放一个CloneFrom的ActionState(BlendSpace)动作
	 * @param ActionState 角色的状态
	 * @param Index 由于没有运行时，所以无法用AnimPicker来获取到播放的动画下标，只能手工给一个了
	 * @param DelayToPlay 延迟多久（秒）去播放
	 * @param Loop 是否循环播放
	 */
	UFUNCTION(BlueprintCallable)
	void PlayBlendSpaceState(ECharacterActionState ActionState , int Index = 0, float DelayToPlay = 0, bool Loop = true);

	/**
	 * 设置生命周期
	 * @param ToDuration 设置为多少秒
	 * @param InfinityIfNegativeDuration 是否当设置为负数的时候就是无限时间，如果是false，则设置为负数会杀死这个克隆
	 */
	UFUNCTION(BlueprintCallable)
	void SetLifeDuration(float ToDuration, bool InfinityIfNegativeDuration = true);

	/**
	 * 设置为可见性（用于pool暂时隐藏某个克隆）
	 * @param Show 是否显示
	 */
	void SetToShown(bool Show);

	/**
	 * 设置这个avatar为半透明的效果 
	 */
	UFUNCTION(BlueprintCallable)
	void SetTransparentMaterial(FLinearColor Color = FLinearColor(0.96,0.47,0.15,0));

	
	/**
	 * 重置这个avatar的材质球，还原成原来的样子
	 */
	UFUNCTION(BlueprintCallable)
	void ResetTransparentMaterial();

	/**
	 * @brief 在身上创建一个临时的装备以供ui上显示使用，不会删除原有的装备。**注意** 生成的临时装备不会自动删除
	 * @param AppearanceEquipment 
	 * @return 这个临时的装备
	 */
	AActor* ShowAppearance(FEquipmentAppearancePart AppearanceEquipment);

	/**
	 * @brief 清楚动画，让模型做T-Pose
	 */
	void ClearAnimation();
};

/**
 * 克隆体在池子内的数据
 */
USTRUCT()
struct FAvatarInPool
{
	GENERATED_BODY()
public:
	UPROPERTY()
	TArray<AAvatarCharacter*> Avatars;
};

