// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "AvatarCharacter.h"
#include "BreakableClone.generated.h"

UCLASS()
class THEAWAKENER_FO_API ABreakableClone : public AAvatarCharacter, public  IOffendedObject
{
	GENERATED_BODY()

private:
	//克隆体的受击框
	UPROPERTY()
	UCharacterHitBoxData* CharacterHitBoxData = nullptr;
	//克隆体的攻击管理器
	UPROPERTY()
	UAttackHitManager* AttackHitManager = nullptr;

	void BeDamaged(FDamageInfo DamageInfo);
	void BeKilled();

	void CheckForHit() const;
public:
	ABreakableClone();
	virtual void Tick(float DeltaTime) override;

	//这个克隆体的血
	UPROPERTY()
	FChaResource ChaResource = FChaResource(1);

	//这个克隆体的f防御信息
	UPROPERTY()
	FDefenseInfo CloneDefenseInfo;

	//这个克隆体的攻击信息
	UPROPERTY()
	FOffenseInfo CloneOffenseInfo;

	virtual FOffendedCaughtResult CanBeOffended(
		FOffenseInfo OffenseInfo, UAttackHitManager* Attacker, AAwCharacter* AttackerInCharge,
		bool IncludeAttackerActionHitBoxes, bool SameSideFriendlyFire, bool AllyFriendlyFire) override;
	
	virtual void BeOffended(FOffenseInfo OffenseInfo,
		UAttackHitManager* Attacker, AAwCharacter* AttackerInCharge, USceneComponent* BeHitBox, USceneComponent* FromAttackBox,
		bool SameSideFriendlyFire, bool AllyFriendlyFire) override;

	virtual void CloneFromAwCharacter(AAwCharacter* Target, float Duration) override;
	void Set(FVector Offset, bool DoOffense, FOffenseInfo OInfo, FDefenseInfo DInfo, FChaResource ChaRes);
};

/**
 * 克隆体在池子内的数据
 */
USTRUCT()
struct FBreakableCloneInPool
{
	GENERATED_BODY()
public:
	UPROPERTY()
	TArray<ABreakableClone*> Clones;
};