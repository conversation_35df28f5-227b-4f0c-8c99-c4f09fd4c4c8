// Fill out your copyright notice in the Description page of Project Settings.


#include "BreakableClone.h"

#include "Components/SkeletalMeshComponent.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GamePlay/AssetUserData/AttackHitBoxSign.h"
#include "TheAwakener_FO/GamePlay/Battle/OffenseManager.h"


ABreakableClone::ABreakableClone()
{
	PrimaryActorTick.bCanEverTick = true;
	AttackHitManager = CreateDefaultSubobject<UAttackHitManager>(TEXT("AttackHitManager"));
}

void ABreakableClone::CheckForHit() const
{
	//拿到这帧可以命中的信息
	TArray<FBeCaughtActorInfo> CaughtActorInfos = AttackHitManager->ThisTickValidTarget(false, true, true);
	for (const FBeCaughtActorInfo AInfo : CaughtActorInfos)
	{
		AActor* Target = AInfo.BeCaughtActor;
		if (AttackHitManager->TargetCanBeHitByHitRecord(Target, this->CloneOffenseInfo.SourceId, this->CloneOffenseInfo.Index))
		{
			UOffenseManager::DoOffense(AttackHitManager, CloneOffenseInfo, AInfo, this->CloneFrom, false);
		}
	}
}


void ABreakableClone::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);

	if (this->ToBeRemoved == true) return;
	if (this->ChaResource.HP <= 0)
	{
		this->ToBeRemoved = true;
	}

	AttackHitManager->Update(DeltaTime);
	CheckForHit();
}

FOffendedCaughtResult ABreakableClone::CanBeOffended(
		FOffenseInfo OffenseInfo, UAttackHitManager* Attacker, AAwCharacter* AttackerInCharge,
		bool IncludeAttackerActionHitBoxes, bool SameSideFriendlyFire, bool AllyFriendlyFire)
{
	if (this->ChaResource.HP <= 0) return FOffendedCaughtResult();
	//没有攻击判定框，就当做一次buff攻击
	if (!Attacker || !this->CloneFrom) return FOffendedCaughtResult(OffenseInfo);
		
	//阵营判断
	if (AttackerInCharge && AttackerInCharge->Side == CloneFrom->Side && SameSideFriendlyFire == false) return FOffendedCaughtResult();
	if (AttackerInCharge && AttackerInCharge->IsEnemy(CloneFrom) == false && AllyFriendlyFire == false) return FOffendedCaughtResult();

	//对方攻击框和记录信息判断
	TArray<FOffenseInfo> OInfos;
	if (AttackerInCharge && IncludeAttackerActionHitBoxes == true)
	{
		//如果有攻击负责人，把负责人所有的攻击信息加上
		OInfos.Append(AttackerInCharge->CurrentActionHitInfo);	
	}
	if (OInfos.Contains(OffenseInfo) == false) OInfos.Add(OffenseInfo);
	TArray<FOffendedCaughtResult> CaughtInfos = Attacker->GetThisTickTargetCaughtInfo(this, OInfos);

	FOffendedCaughtResult Res;
	for (const FOffendedCaughtResult CInfo : CaughtInfos)
	{
		if (CInfo.Hit == true && Res.Hit == false)
		{
			Res = CInfo;
			continue;
		}
		if (
			CInfo.BeCaughtActorInfo.CaughtHitBoxData &&
			(!Res.BeCaughtActorInfo.CaughtHitBoxData || Res.BeCaughtActorInfo.CaughtHitBoxData->Priority < CInfo.BeCaughtActorInfo.CaughtHitBoxData->Priority)
		)
		{
			Res = CInfo;
			continue;
		}
	} 
	
	return Res;
}
	
void ABreakableClone::BeOffended(FOffenseInfo OffenseInfo,
	UAttackHitManager* Attacker, AAwCharacter* AttackerInCharge, USceneComponent* BeHitBox, USceneComponent* FromAttackBox,
	bool SameSideFriendlyFire, bool AllyFriendlyFire)
{
	if (this->ChaResource.HP <= 0) return;
	//友伤避免
	if (
		AttackerInCharge && CloneFrom && (
			(SameSideFriendlyFire == false && CloneFrom->Side == AttackerInCharge->Side) ||
			(AllyFriendlyFire == false && AttackerInCharge->IsEnemy(CloneFrom) == false)
		)
	)return;

	//UCharacterHitBox* HitBox = Cast<UCharacterHitBox>(BeHitBox);
	UCharacterHitBoxData* HitBox = BeHitBox ? BeHitBox->GetAssetUserData<UCharacterHitBoxData>() : nullptr;
	
	//如果攻击信息启动，说明有攻击，就可能造成动作变化和伤害
	if (OffenseInfo.AttackInfo.Active)
	{
		FVector HitLocation = BeHitBox ? BeHitBox->GetComponentLocation() : FVector::ZeroVector;
		FVector HitLocOffset;
		FVector HitNormal = BeHitBox ? BeHitBox->GetComponentRotation().Vector() : FVector::ZeroVector;
		if (FromAttackBox && HitBox)
		{
			UGameplayFuncLib::GetHitResultBetweenSceneComponent(BeHitBox, FromAttackBox, HitLocation, HitNormal);
		}
		HitLocOffset = HitLocation;
		if (BeHitBox) HitLocOffset -= BeHitBox->GetComponentLocation();
		
		//伤害处理
		FDamageInfo DInfo = FDamageInfo::FromAttackAndDefense(
			BeHitBox, OffenseInfo.AttackInfo, this->CloneDefenseInfo, HitLocOffset
		);
		DInfo.Attacker = AttackerInCharge;
		//UDamageManager::AddDamage(AttackerInCharge, this, DInfo);

		//为攻击方开启临时的Cancel点
		if (AttackerInCharge)
		{
			for (FCancelTagInfo TempCancelPoint : DInfo.AttackerActionChange.TemporaryCancelPoints)
			{
				AttackerInCharge->AddCancelTag(ECancelTagType::Temporary, TempCancelPoint.CancelPointIndex, TempCancelPoint.Duration);
			}
		}

		//改变攻击方的动作，是否会落马等，由对应动作中的AnimNotify来执行
		if (AttackerInCharge)
		{
			if (DInfo.AttackerActionChange.HitStun.Active)
			{
				FForceMoveInfo HitStun = DInfo.AttackerActionChange.HitStun;
				HitStun.Velocity *= AttackerInCharge->CharacterObj.CurProperty.BeStrikeRate;
				HitStun.InSec *= AttackerInCharge->CharacterObj.CurProperty.BeStrikeRate;
				if (!HitStun.Velocity.IsNearlyZero() && HitStun.InSec > 0)
				{
					AttackerInCharge->LastIncomingForce = HitStun.Velocity;
				}
				DInfo.AttackerActionChange.HitStun = HitStun;
			}
			for (FString ABeep : DInfo.AttackerActionChange.AchievementBeep)
			{
				AttackerInCharge->AchievementSignalBeep(ABeep);
			}
			FActionParam AttackerAP = FActionParam();
			AttackerAP.Active = true;
			AttackerAP.PriorityDistance = OffenseInfo.AttackInfo.AttackerActionChange.Priority - CloneDefenseInfo.AttackerActionChange.Priority;
			AttackerInCharge->PreorderActionByActionChangeInfo(DInfo.AttackerActionChange, AttackerAP);
		}
		
		//震屏，只有攻击方是玩家的才会
		if (AttackerInCharge && UGameplayFuncLib::GetAwGameState() && UGameplayFuncLib::GetAwGameState()->GetMyCharacter() == AttackerInCharge)
		{
			if (OffenseInfo.AttackInfo.ShakeInfo.Shake && this->GetWorld())
			{
				//只有用了防守方的DefenseInfo，才会播放震动（因为震动是DefenseInfo的数据嘛）
				const FVector FinalPos = OffenseInfo.AttackInfo.ShakeInfo.Offset + this->GetActorLocation();
				APlayerCameraManager::PlayWorldCameraShake(
					this->GetWorld(),
					OffenseInfo.AttackInfo.ShakeInfo.Shake, FinalPos,
					OffenseInfo.AttackInfo.ShakeInfo.FullShockRange,
					OffenseInfo.AttackInfo.ShakeInfo.LoseShockRange,
					OffenseInfo.AttackInfo.ShakeInfo.FallOff,
					OffenseInfo.AttackInfo.ShakeInfo.DoRotate
				);
			}
		}
		
		//播放对应的特效（TODO：还未确定怎么做），在HitBox播放受击方的特效
		FTransform HitTrans = BeHitBox ? BeHitBox->GetComponentTransform() : this->GetActorTransform();
		HitTrans.SetLocation(HitLocation);
		HitTrans.SetRotation(HitNormal.ToOrientationQuat());
		if (UGameplayFuncLib::GetHitVFX(DInfo.AttackerActionChange) && HitBox)
			UGameplayFuncLib::CreateVFXatLocation(UGameplayFuncLib::GetHitVFX(DInfo.AttackerActionChange), HitTrans, true, true);
		if (UGameplayFuncLib::GetHitSFX(DInfo.AttackerActionChange) && HitBox)
			UGameplayFuncLib::PlaySFXatLocation(UGameplayFuncLib::GetHitSFX(DInfo.AttackerActionChange), HitLocation, HitNormal.Rotation());
		if (UGameplayFuncLib::GetHitVFX(DInfo.DefenderActionChange) && HitBox)
			UGameplayFuncLib::CreateVFXatLocation(UGameplayFuncLib::GetHitVFX(DInfo.DefenderActionChange), HitTrans, true, true);
		if (UGameplayFuncLib::GetHitSFX(DInfo.DefenderActionChange) && HitBox)
			UGameplayFuncLib::PlaySFXatLocation(UGameplayFuncLib::GetHitSFX(DInfo.DefenderActionChange), HitLocation, HitNormal.Rotation());

		BeDamaged(DInfo);	
	}
	
	if (AttackerInCharge)
	{
		AttackerInCharge->AddHitRecord(
			FOffenseHitRecord(
				OffenseInfo.SourceId, 
				OffenseInfo.Index,
				this,
				OffenseInfo.CanHitTimes - 1,
				OffenseInfo.HitSameTargetDelay
			)
		);
	}
}

void ABreakableClone::BeKilled()
{
	this->ToBeRemoved = true;
	this->SetToShown(false);
}

void ABreakableClone::BeDamaged(FDamageInfo DamageInfo)
{
	if (DamageInfo.BeDodged == true && this->ChaResource.HP <=  0)
	{
		return;	//闪避或者死亡了就do nothing了
	}
	
	const int ToDealDamage = FMath::Abs(DamageInfo.FinalDamage());

	this->ChaResource.HP -= ToDealDamage * (DamageInfo.IsHeal == false ? 1:-1);
	if (this->ChaResource.HP <= 0) this->BeKilled();

	//跳数字
	if(UGameplayFuncLib::GetAwDataManager()->DebugConfig.ShowPopText)
	{
		const FString TextId = "SceneItemDamaged";
	
		if (UGameplayFuncLib::GetAwDataManager()->DebugConfig.PopTextInfo.Contains(TextId))
		{
			FPopTextLauncher TextLauncher = UGameplayFuncLib::GetAwDataManager()->DebugConfig.PopTextInfo[TextId];
			int PopPriority = TextLauncher.Priority;
			int PopSize = TextLauncher.Size;
			FLinearColor PopColor = FLinearColor::FromSRGBColor(TextLauncher.Color);
			FVector PopPos = DamageInfo.HitBox ? DamageInfo.HitBox->GetComponentLocation() : this->GetActorLocation();
			PopPos += DamageInfo.HitLocationOffset;
			UGameplayFuncLib::GetAwGameInstance()->PopText(
				PopPos, FString::FromInt(ToDealDamage), PopPriority, PopSize, PopColor
			);
		}
	}
}

void ABreakableClone::CloneFromAwCharacter(AAwCharacter* Target, float Duration)
{
	Super::CloneFromAwCharacter(Target, Duration);
	FBox MeshBox = FBox(this->GetActorLocation(), this->GetActorLocation());
	if (this->Mesh)
	{
		Mesh->SetCollisionProfileName("BothCatherAndBeCaught");
		Mesh->SetGenerateOverlapEvents(true);
		if (this->CharacterHitBoxData == nullptr)
			this->CharacterHitBoxData = NewObject<UCharacterHitBoxData>();
		CharacterHitBoxData->Id = Mesh->GetName();
		MeshBox = Mesh->GetNavigationBounds();
		
		Mesh->AddAssetUserData(CharacterHitBoxData);
	}
	//this->ChaResource.HP = Target->CurrentHP();
}

void ABreakableClone::Set(FVector Offset, bool DoOffense, FOffenseInfo OInfo, FDefenseInfo DInfo, FChaResource ChaRes)
{
	this->SetActorLocation(this->GetActorLocation() + Offset.RotateAngleAxis(this->GetActorRotation().Yaw, FVector::ZAxisVector));
	if (DoOffense && this->Mesh)
	{
		UAttackHitBoxSign* AttackHitBoxSign = NewObject<UAttackHitBoxSign>();
		AttackHitBoxSign->Name = Mesh->GetName();
		this->Mesh->AddAssetUserData(AttackHitBoxSign);
		int32 BoxKey = FStringPool::Register(Mesh->GetName());
		AttackHitManager->AddAttackHitBox(BoxKey, Mesh);
		AttackHitManager->ActiveAllAttackHitBox();
	}
	
	CloneOffenseInfo = OInfo;
		CloneOffenseInfo.AttackHitBoxName.Add(Mesh->GetName());
	CloneDefenseInfo = DInfo;
	ChaResource = ChaRes;
}