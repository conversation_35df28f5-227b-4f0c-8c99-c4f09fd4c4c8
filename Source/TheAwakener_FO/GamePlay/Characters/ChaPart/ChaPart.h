// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "EnumChaPart.h"
#include "TheAwakener_FO/GamePlay/DamageVolume/DamageValue.h"
#include "ChaPart.generated.h"

/**
 * 角色部位
 */
USTRUCT(BlueprintType)
struct FChaPart
{
	GENERATED_BODY()
public:
	//是否激活中
	UPROPERTY()
	bool Active = true;
	
	//也就是Json表里的Part值，用来和HitBox建立关系的 
	UPROPERTY(EditDefaultsOnly, BlueprintReadWrite)
	FString Id;
	
	// 部位的逻辑类型，伤害等逻辑处理会对此有特殊效果
	UPROPERTY(EditDefaultsOnly, BlueprintReadWrite)
	EChaPartType PartType = EChaPartType::Body;
		
	// 属性肉质（读取后需要转化为怪猎的肉质才能用，即0.35f代表受到35%的伤害）
	UPROPERTY(EditDefaultsOnly, BlueprintReadWrite)
	FDamageValue Meat = FDamageValue(1);
	
	// 这个部位受到伤害的时候，DamageInfo中对于每个属性的最终伤害值，会被和这个Breakable再次相乘，计算对部位的破坏力
	UPROPERTY(EditDefaultsOnly, BlueprintReadWrite)
	FDamageValue Breakable;
	
	// 部位的当前耐久度，每次打在这个部位导致角色受伤，耐久度会降低也在Damage流程里面存在。
	// 若一个部位的Durability被降低到0以下，就会被崩坏（从Character.Part中移除，并且不会再有碰撞框，由此AI执行某些行为也会由此失败
	UPROPERTY()
	int Durability = 1;

	//部位的耐久度，因为可以被破坏多次，所以是一个数组
	UPROPERTY(EditDefaultsOnly, BlueprintReadWrite)
	TArray<int> MaxDurability;

	//是否保留最后一个MaxDurability，使得部位始终可以被击破，但是不会摧毁
	UPROPERTY(EditDefaultsOnly, BlueprintReadWrite)
	bool CanBeDestroy = false;
		
	// 部位的肉质类型，会影响视觉听觉特效的
	UPROPERTY(EditDefaultsOnly, BlueprintReadWrite)
	EChaPartMeatType MeatType = EChaPartMeatType::Meat;

	//部位被破坏之后，要关闭显示的尸块部分
	UPROPERTY(EditDefaultsOnly, BlueprintReadWrite)
	TArray<FString> HideBodySightPartsOnBroken;

	//部位破坏之后，要显示的尸块部分
	UPROPERTY(EditDefaultsOnly, BlueprintReadWrite)
	TArray<FString> ShowBodySightPartsOnBroken;

	//当每次被降低到0，会执行的回调函数 (AAwCharacter* Character, FChaPart& Part, bool RemoveAfterBroken,TArray<FString> DesignerParams)=>TimelineNode
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FString> OnBroken;

	//是否是一个弱点部位
	bool IsWeakPart() const
	{
		// TODO：之后需要定，正作需要定何为弱点
		return false;
		return this->Meat.Physical >= 0.4f;
	}

	/**
	 *根据json数据产生
	 */
	static FChaPart FromJson(TSharedPtr<FJsonObject> JsonObj);

	/**
	 *	对部位的耐久度造成伤害，调用这个
	 *	@param Value 造成了多少伤害，负数的话会被忽略
	 *	@return 返回部位遭到几次重创（未必是破坏，破坏是Durability==0了，重创是导致Durability到过0，但是又被重新赋值了）
	 */
	int ReduceDurability(int Value);

	/**
	 * 部位是否彻底完蛋了
	 */
	bool  Broken() const
	{
		return this->Durability == 0 && this->MaxDurability.Num() <= 0;
	}
};
