// Fill out your copyright notice in the Description page of Project Settings.


#include "ChaPart.h"

FChaPart FChaPart::FromJson(TSharedPtr<FJsonObject> JsonObj)
{
	FChaPart PartInfo = FChaPart();
	
	const TSharedPtr<FJsonObject> MeatInfo = JsonObj->GetObjectField("Meat");
	for (TTuple<FString, TSharedPtr<FJsonValue, ESPMode::ThreadSafe>> MeatKV : MeatInfo->Values)
	{
		float Physical = 1;
		float Fire = 1;
		if (MeatKV.Key == "Physical") Physical = MeatKV.Value->AsNumber();
		if (MeatKV.Key == "Fire") Fire = MeatKV.Value->AsNumber();
		PartInfo.Meat = FDamageValue(Physical, Fire);
	}

	PartInfo.Id = UDataFuncLib::AwGetStringField(JsonObj, "Id", UDataFuncLib::AwGetStringField(JsonObj, "Part"));	
	PartInfo.PartType = UDataFuncLib::AwGetEnumField<EChaPartType>(JsonObj, "Part", EChaPartType::Body);

	if (JsonObj->HasField("Breakable"))
	{
		const TSharedPtr<FJsonObject> BreakInfo = JsonObj->GetObjectField("Breakable");
		for (const TTuple<FString, TSharedPtr<FJsonValue>> BreakKV : BreakInfo->Values)
			PartInfo.Breakable.Add(BreakKV.Key, BreakKV.Value->AsNumber() );
	}

	for (const TSharedPtr<FJsonValue, ESPMode::ThreadSafe> Durability : UDataFuncLib::AwGetArrayField(JsonObj, "Durability"))
		PartInfo.MaxDurability.Add(Durability->AsNumber());
	if (PartInfo.MaxDurability.Num() <= 0) PartInfo.MaxDurability.Add(1);
	PartInfo.Durability = PartInfo.MaxDurability[0];
	PartInfo.CanBeDestroy = UDataFuncLib::AwGetBoolField(JsonObj, "CanBeDestroy");

	//PartInfo.StableMod = UDataFuncLib::AwGetNumberField(UDataJsonObj, "StableMod");
	PartInfo.MeatType = UDataFuncLib::AwGetEnumField<EChaPartMeatType>(JsonObj, "Type", EChaPartMeatType::Meat);

	if (JsonObj->HasField("HideSightPart"))
	{
		for (const TSharedPtr<FJsonValue, ESPMode::ThreadSafe> ArrayField : UDataFuncLib::AwGetArrayField(JsonObj, "HideSightPart"))
		{
			PartInfo.HideBodySightPartsOnBroken.Add(ArrayField->AsString());
		} 
	}
	if (JsonObj->HasField("ShowSightPart"))
	{
		for (const TSharedPtr<FJsonValue, ESPMode::ThreadSafe> ArrayField : UDataFuncLib::AwGetArrayField(JsonObj, "ShowSightPart"))
		{
			PartInfo.ShowBodySightPartsOnBroken.Add(ArrayField->AsString());
		} 
	}

	if (JsonObj->HasField("OnPartBroken"))
	{
		for (const TSharedPtr<FJsonValue, ESPMode::ThreadSafe> BFunc : JsonObj->GetArrayField("OnPartBroken"))
		{
			PartInfo.OnBroken.Add(BFunc->AsString());
		} 
	}
	
	return PartInfo;
};

int FChaPart::ReduceDurability(int Value)
{
	if (Value <= 0) return false;
	this->Durability -= Value;
	int Breaked = 0;
	int RestValue = -this->Durability;
	while (this->Durability <= 0 && RestValue > 0)
	{
		Breaked += 1;
			
		if (this->MaxDurability.Num() > 1)
		{
			this->MaxDurability.RemoveAt(0);
			this->Durability = FMath::Max(1, this->MaxDurability[0]) - RestValue;
		}else
		{
			if (this->CanBeDestroy == true && this->MaxDurability.Num() > 0)
			{
				this->MaxDurability.RemoveAt(0);
			}
			this->Durability = this->MaxDurability.Num() > 0 ? (FMath::Max(1, this->MaxDurability[0]) - RestValue) : 0;
		}
		RestValue = -Durability;
	}

	return Breaked;
}