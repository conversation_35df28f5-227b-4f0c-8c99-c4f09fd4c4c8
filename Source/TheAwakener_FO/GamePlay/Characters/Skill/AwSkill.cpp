// Fill out your copyright notice in the Description page of Project Settings.


#include "AwSkill.h"

FAwActionSkillInfo FAwActionSkillInfo::From<PERSON>son(TSharedPtr<FJsonObject> JsonObj)
{
	FAwActionSkillInfo AwSkillInfo = FAwActionSkillInfo();
	AwSkillInfo.Id = UDataFuncLib::AwGetStringField(JsonObj,"Id");
	AwSkillInfo.Desc = UDataFuncLib::AwGetStringField(JsonObj,"Desc");
	AwSkillInfo.Tags = UDataFuncLib::AwGetStringArrayField(JsonObj,"Tags");
	AwSkillInfo.SkillType = UDataFuncLib::AwGetEnumField(JsonObj,"SkillType",EAwActionSkillType::Normal);
	AwSkillInfo.MinEnergyCost = UDataFuncLib::AwGetNumberField(<PERSON><PERSON><PERSON>bj,"MinEnergyCost",0);
	AwSkillInfo.EnergyCostPerSecond =UDataFuncLib::AwGetNumberField(JsonObj,"EnergyCostPerSecond",0);
	AwSkillInfo.SkillActionId = UDataFuncLib::AwGetStringField(JsonObj,"SkillActionId","");
	AwSkillInfo.SkillCloseActionId = UDataFuncLib::AwGetStringField(JsonObj,"SkillCloseActionId","");
	AwSkillInfo.IconPath = UDataFuncLib::AwGetStringField(JsonObj,"IconPath","");
	AwSkillInfo.bUsing = false;
	AwSkillInfo.CostSourceId = UDataFuncLib::AwGetStringField(JsonObj,"CostSourceId","");
	AwSkillInfo.UnlockCostNum = UDataFuncLib::AwGetNumberField(JsonObj,"UnlockCostNum",0);
	return AwSkillInfo;
}

void FAwActionSkillInfo::ReSet()
{
	//做重置相关行为 暂时只有开关
	bUsing = false;
}
