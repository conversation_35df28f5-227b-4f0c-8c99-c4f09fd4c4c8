// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/Object.h"
#include "SkillObj.generated.h"

/**
 * 角色技能
 * 玩家角色专有的东西
 */
UCLASS()
class THEAWAKENER_FO_API USkillObj : public UObject
{
	GENERATED_BODY()
public:
	FString Id;
	FString Name;
	FString Icon;
	TArray<FString> Tag;
	// TArray<ThingObj*> Const;
	FString Description;
	FString ActionId;
	// TArray<EffectLauncher*> Effect;
	//TArray<FAddBuffInfo> AddBuffOnUse;
	
	// 是否可用
	bool Require();
};
