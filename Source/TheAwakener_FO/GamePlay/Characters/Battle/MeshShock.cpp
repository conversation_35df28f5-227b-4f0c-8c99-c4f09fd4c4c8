// Fill out your copyright notice in the Description page of Project Settings.


#include "MeshShock.h"

#include "Components/SkeletalMeshComponent.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"

UMeshShock::UMeshShock()
{
	PrimaryComponentTick.bCanEverTick = true;
}

void UMeshShock::BeginPlay()
{
	Super::BeginPlay();
	
	const AAwCharacter* Cha = Cast<AAwCharacter>(this->GetOwner());
	if (!Cha) return;
	this->Mesh = Cha->GetMesh();
	if (Mesh) this->OriginRelativeLocation = Mesh->GetRelativeLocation();
}

void UMeshShock::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
	Super::TickComponent(DeltaTime, TickType, ThisTickFunction);

	if (!Mesh || TotalFreeze <= 0 || TickDelta <= 0) return;

	if (TimeElapsed >=TotalFreeze)
	{
		TotalFreeze = -1;
		Mesh->SetRelativeLocation(OriginRelativeLocation);
	}else
	{
		const int InTick = FMath::FloorToInt(TimeElapsed / TickDelta);
		const float CurDelta = TimeElapsed - (TickDelta * InTick);
		if (FMath::FloorToInt(InTick * 1.f / 2) * 2 == InTick)
		{
			//偶数帧
			this->MoveTarget = FVector(
				CurDelta * ShockDir.X / TickDelta * ShockLen,
				CurDelta * ShockDir.Y / TickDelta * ShockLen,
				0
			) + OriginRelativeLocation; 
		}else 
		{
			//奇数帧
			this->MoveTarget = FVector(
				(TickDelta - CurDelta) / TickDelta  * ShockDir.X * ShockLen,
				(TickDelta - CurDelta) / TickDelta  * ShockDir.Y * ShockLen,
				0
			) + OriginRelativeLocation;
		}
		Mesh->SetRelativeLocation(MoveTarget);
		TimeElapsed += DeltaTime;
	}
}

void UMeshShock::SetFreeze(float FreezeTime, FVector2D Direction)
{
	if (this->TotalFreeze > 0 || FreezeTime <= 0) return;
	this->TotalFreeze = FreezeTime;
	this->TimeElapsed = 0;
	this->ShockDir = Direction.GetSafeNormal();
	this->OriginRelativeLocation = this->Mesh->GetRelativeLocation();
}