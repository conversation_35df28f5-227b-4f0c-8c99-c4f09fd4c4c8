// Fill out your copyright notice in the Description page of Project Settings.


#include "BattleStructs.h"

#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"


FDamageInfo FDamageInfo::FromAttackAndDefense(USceneComponent* TargetHitBox, FAttackInfo AttackInfo, FDefenseInfo DefenseInfo, FVector HitLocationOffset)
{
	FDamageInfo Res = FDamageInfo();
	Res.HitBox = TargetHitBox;
	Res.HitBoxData = TargetHitBox ? TargetHitBox->GetAssetUserData<UCharacterHitBoxData>() : nullptr;
	Res.HitLocationOffset = HitLocationOffset;
	Res.DamageCastItemEnergyPower = AttackInfo.DamageCastItemEnergyPower;
	/*
	根据攻防信息，计算出伤害值，这里只计算攻击力对比防御信息的伤害值
	这里会根据伤害类型判断是否进行肉质减免
	所以传到这里的时候，AttackInfo.DamagePower应该已经是攻击方所有攻击因子计算后的结果（包括武器攻击力等）
	防守一方在这里的数据，则是DefenseInfo，和TargetHitBox对应的肉质信息
	 */ 
	Res.DamagePower = AttackInfo.DamagePower ;	//伤害结果=攻击方攻击力
	Res.DamageSourceType = AttackInfo.DamageSourceType;//继承伤害来源
	Res.DamageType = AttackInfo.DamageType;//继承伤害触发类型
	Res.Elemental = AttackInfo.Elemental;//继承伤害数值类型
	//非治疗才会被减伤
	if (AttackInfo.IsHeal == false)
	{
		//目标的防御力减伤只对直接伤害有效
		if (AttackInfo.DamageType == EDamageType::DirectDamage)
		{
			const AAwCharacter* Target = TargetHitBox ? Cast<AAwCharacter>(TargetHitBox->GetOwner()) : nullptr;
			const FDamageValue DefensePropDamageTimes = Target ? FDamageValue(Target->CharacterObj.CurProperty.GetPhysicalMeatByPDefense()) : FDamageValue(1);	//防御力属性带来的减伤
			const FDamageValue MeatQualityDamageTimes = Res.HitBoxData && Res.HitBoxData->BelongsToPart ? Res.HitBoxData->BelongsToPart->Meat : FDamageValue(1);	//来自肉质减伤
			Res.DamagePower = Res.DamagePower * DefensePropDamageTimes * MeatQualityDamageTimes;
		}
		
		//不是治疗才会被防御力影响，【NOTICE】防御信息减伤恒久有效
		Res.DamagePower = Res.DamagePower * DefenseInfo.DamageModifer;
	}
	
	Res.IsHeal = AttackInfo.IsHeal;
	Res.DamageIncomeVector =
		TargetHitBox && TargetHitBox->GetOwner() ?
			TargetHitBox->GetOwner()->GetActorLocation() - HitLocationOffset :
			AttackInfo.DamageIncomeVector;

	if(AttackInfo.DamageSourceType == EAttackSource::Bullet || AttackInfo.DamageSourceType == EAttackSource::AoE
		|| AttackInfo.DamageSourceType == EAttackSource::Buff)
	{
		Res.AttackerActionUseAttackInfo = true;
		Res.AttackerActionChange = AttackInfo.AttackerActionChange;
	}
	else
	{
		Res.AttackerActionUseAttackInfo = AttackInfo.AttackerActionChange.Priority >= DefenseInfo.AttackerActionChange.Priority;
		Res.AttackerActionChange = Res.AttackerActionUseAttackInfo ?
				AttackInfo.AttackerActionChange : DefenseInfo.AttackerActionChange;
	}

	Res.DefenderActionUseAttackInfo = AttackInfo.DefenderActionChange.Priority >= DefenseInfo.DefenderActionChange.Priority;
	Res.DefenderActionChange = Res.DefenderActionUseAttackInfo ?
			AttackInfo.DefenderActionChange : DefenseInfo.DefenderActionChange;
	
	//Just Dodge时，没有伤害，且防御方动作变化必然遵循闪避方
	if (Res.HitBoxData && Res.HitBoxData->AsJustDodge.Active == true && (
			AttackInfo.DamageType != EDamageType::PeriodDamage 	//间歇性伤害不能just dodge
		)&&!Res.IsHeal
	){
		Res.DamagePower = FDamageValue(0);
		Res.DefenderActionChange = Res.HitBoxData->AsJustDodge.ActionOnJustDodge;
		Res.BeDodged = true;
	}

	Res.AttackInfoPriority = AttackInfo.DefenderActionChange.Priority;
	Res.DefendInfoPriority = DefenseInfo.DefenderActionChange.Priority;
	
	return Res;
}

bool FDamageInfo::JudgeFinalDamageCritical()
{
	/*
	bool CanCritical = true;
	if (UGameplayFuncLib::IsRogueMode())
	{
		CanCritical = CanCriticElemental.Contains(Elemental)&&CanCriticTypes.Contains(DamageType);
	}

	if (!CanCritical)
	{
		return  false;
	}
	*/
	//特定伤害类型不会暴击
	if (IsHeal)
	{
		return false;
	}

	IsCritical = CriticalChance>= FMath::RandRange(0.f,1.f);
	return IsCritical;
}

FJustDodgeInfo FJustDodgeInfo::Default()
{
	FActionChangeInfo AInfo = FActionChangeInfo(EActionChangeMethod::Keep);
	AInfo.Priority = 9;
	return FJustDodgeInfo(AInfo);
}

bool FJustDodgeInfo::operator == (const FJustDodgeInfo& Other) const
{
	return this->ActionOnJustDodge == Other.ActionOnJustDodge;
}

FDamageInfo::FDamageInfo()
{
	ValuePowerArea.Add(EDamageArea::BreakDamagePower,1.f);
	ValuePowerArea.Add(EDamageArea::CauseDamagePower,1.f);
	ValuePowerArea.Add(EDamageArea::InjuredDamagePower,1.f);
	ValuePowerArea.Add(EDamageArea::PhysicalDamagePower,1.f);
	ValuePowerArea.Add(EDamageArea::ElementalDamagePower,1.f);
}

