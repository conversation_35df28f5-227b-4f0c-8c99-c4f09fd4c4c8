// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/Object.h"
#include "MeshShock.generated.h"

/**
 * 挨打震动一下
 */
UCLASS(ClassGroup="Beat Chicken Feeling", editinlinenew, hidecategories=(Object,LOD,Lighting,TextureStreaming), meta=(DisplayName="Mesh Shocker", BlueprintSpawnableComponent))
class THEAWAKENER_FO_API UMeshShock : public UActorComponent
{
	GENERATED_BODY()
private:
	UMeshShock();
	
	UPROPERTY()
	USkeletalMeshComponent* Mesh;

	UPROPERTY()
	bool Active = false;	//是否开始抖动了

	UPROPERTY()
	FVector2D ShockDir = FVector2D::ZeroVector; //抖动方向（会被GetSafeNormal的）

	UPROPERTY()
	float TotalFreeze = 0;		//总的时间

	UPROPERTY()
	float TimeElapsed = 0;	//播放了多少时间了

	UPROPERTY()
	FVector MoveTarget;	//这一帧的移动目标

	UPROPERTY()
	FVector OriginRelativeLocation;	//震动前的Mesh位置

	virtual void BeginPlay() override;
	virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

public:
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	float ShockLen = 10;	//抖动距离多少

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	float TickDelta = 0.034f;	//抖动每个位移变化的时间段
	
	UFUNCTION(BlueprintCallable)
	void SetFreeze(float FreezeTime, FVector2D Direction);
};
