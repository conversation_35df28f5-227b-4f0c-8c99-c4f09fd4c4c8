// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
//#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "TheAwakener_FO/GamePlay/Characters/Action/CancelTagInfo.h"
#include "TheAwakener_FO/GamePlay/Characters/Action/CharacterActionState.h"
#include "TheAwakener_FO/GamePlay/Characters/Attack/ForceMoveInfo.h"
//#include "TheAwakener_FO/GamePlay/AssetUserData/CharacterHitBoxData.h"
#include "TheAwakener_FO/GamePlay/AnimNotify/PlayForceFeedBack.h"
#include "TheAwakener_FO/GamePlay/Camera/AwCameraShakeInfo.h"
#include "TheAwakener_FO/GamePlay/DamageVolume/DamageValue.h"
#include "TheAwakener_FO/GamePlay/Equipment/Weapon.h"
#include "BattleStructs.generated.h"

class AAwCharacter;
class UCharacterHitBoxData;

/**
 * 动作变化方式的类别枚举
 */
UENUM(BlueprintType)
enum class EActionChangeMethod : uint8
{
	//保持不变
	Keep,
	//变化整个ActionInfo，当然，实际上只是Preorder一个而已
	ChangeActionInfo,
	//变化指向某个ActionState，由此得到它对应的动作（因为每个角色同一个MontageState的动作未必一样，参见ECharacterMontageState）
	ToMontageState,
};

/**
 *伤害乘区
 */
UENUM(BlueprintType)
enum class EDamageArea : uint8
{
	//硬直槽伤害倍率 不入伤害的总计数值计算 小于等于该枚举的值不进入伤害数值计算
	BreakDamagePower,
	//造成伤害倍率
	CauseDamagePower,
	//受到伤害倍率
	InjuredDamagePower,
	//受到暴击伤害倍率
	InjuredCriticalPower,
	//物理伤害倍率
	PhysicalDamagePower,
	//元素伤害倍率
	ElementalDamagePower,
};

/**
 * 动作变化信息，这个决定了动作要怎么发生变化
 */
USTRUCT(BlueprintType)
struct FActionChangeInfo
{
	GENERATED_BODY()
public:
	//动作变化的方式
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	EActionChangeMethod ChangeMethod = EActionChangeMethod::Keep;

	//跳转到的ActionInfo的Id
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString ChangeToActionId = FString();

	//跳转到某个ActionState，仅ChangeMethod为ChangeMontageState时有效
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	ECharacterMontageState ToState = ECharacterMontageState::Hurt;

	//播放这个动作，先会卡帧多久（秒），这个卡帧我们就叫Freeze了
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	float FreezeTime = 0;

	//从第几秒开始播放这个动作
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	float FromSec = 0;
	
	/**
	 * 基础版——命中时候播放的特效
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	UParticleSystem* HitVFX = nullptr;

	/**
	 * 基础版——命中时候播放的音效，如果是空字符串，就代表不播放
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	USoundBase* HitSFX = nullptr;

	/**
	 * 幸存者版——命中时候播放的特效
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	UParticleSystem* HitVFX_Svl = nullptr;

	/**
	 * 幸存者版——命中时候播放的音效，如果是空字符串，就代表不播放
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	USoundBase* HitSFX_Svl = nullptr;

	/**
	 * 生效时会发出的Achievement Beep，当然一个没有就不会发出了
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FString> AchievementBeep;

	/**
	 *硬直信息
	 *Active代表了是否要硬直
	 *inSec在这里就会起到Stun时间长度的效果，所以即使坐标不变，有个Active的HitStun还是会使动作播放时长努力向这个数字靠拢的
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FForceMoveInfo HitStun = FForceMoveInfo();

	/**
	 *这个请求的优先级，优先级最高的请求最后才会被采纳。
	 *比如格挡之后自身不会动就要设置10，敌人弹刀也设置10，就基本能做到自己不动并且敌人弹刀了
	 *毕竟一次动作碰撞之后，攻防双方都会发起一次这个请求的，要对比决断最后Preorder哪一个的
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int Priority = 0;

	/**
	 * 无论是否弹刀或者霸体，反正3种情况同时只有1中发生，也就是分别只有一个FActionChangeInfo会发生在Attacker和Defender身上
	 * 这里我们还可能需要为攻击者开启一些BeCancelledTag，某些动作的可Cancel也是因此激活的，当然动作变了，开启可Cancel意义就不大了
	 * 值得注意的是，记得得有个NotifyState去关闭这个可CancelTag
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FCancelTagInfo> TemporaryCancelPoints;


	FActionChangeInfo(){};
	FActionChangeInfo(EActionChangeMethod Method, FString ChangeToId = FString(), FForceMoveInfo Stun = FForceMoveInfo()):
		ChangeMethod(Method), ChangeToActionId(ChangeToId), HitStun(Stun){};
	FActionChangeInfo(ECharacterMontageState ChangeTo,float Freeze = 0, float PlayFrom = 0, FForceMoveInfo Stun = FForceMoveInfo(), int AppPriority = 0):
		ChangeMethod(EActionChangeMethod::ToMontageState), ToState(ChangeTo), FreezeTime(Freeze), FromSec(PlayFrom), HitStun(Stun), Priority(AppPriority){};

	bool operator ==(const FActionChangeInfo& Other) const
	{
		return ChangeMethod == Other.ChangeMethod && ChangeToActionId == Other.ChangeToActionId &&
			ToState == Other.ToState && this->TemporaryCancelPoints == Other.TemporaryCancelPoints;
	}
};

/**
 * 在一次伤害中，武器和动作给出的伤害比例
 */
USTRUCT(BlueprintType)
struct FWeaponAffectAction
{
	GENERATED_BODY()
public:
	//主手影响百分比，即攻击力会被武器AttackPower影响的倍率（角色攻击力*武器攻击倍率*这个百分比）
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float MainHandRate = 1.0f;

	//副手影响百分比，即攻击力会被武器AttackPower影响的倍率（角色攻击力*武器攻击倍率*这个百分比）
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float OffHandRate = 0;

	//远程影响百分比，即攻击力会被武器AttackPower影响的倍率（角色攻击力*武器攻击倍率*这个百分比）
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float RangedRate = 0;

	//暗器影响百分比，即攻击力会被武器AttackPower影响的倍率（角色攻击力*武器攻击倍率*这个百分比）
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float ConcealedRate = 0;

	FWeaponAffectAction(){};
	FWeaponAffectAction(float MainHand, float OffHand, float Ranged, float Concealed):
		MainHandRate(MainHand), OffHandRate(OffHand), RangedRate(Ranged), ConcealedRate(Concealed){};

	static FWeaponAffectAction FromJson(TSharedPtr<FJsonObject> JsonObj)
	{
		return FWeaponAffectAction(
			UDataFuncLib::AwGetNumberField(JsonObj, "MainHand", 1),
			UDataFuncLib::AwGetNumberField(JsonObj, "OffHand", 0),
			UDataFuncLib::AwGetNumberField(JsonObj, "Ranged", 0),
			UDataFuncLib::AwGetNumberField(JsonObj, "Concealed", 0)
		);
	}
	
};

/**
 *伤害来源类别
 */
UENUM(BlueprintType)
enum class EAttackSource : uint8
{
	//无来源伤害
	None,
	//来自攻击动作
	AttackAction,
	//来自觉醒动作
	AwakeAction,
	//来自反击动作
	CounterAction,
	//蓄力攻击动作
	PowerAction,
	//来自满蓄力攻击动作
	MaxPowerAction,
	//来自瞬时攻击动作
	JustAttackAction,
	//来自闪避动作
	DodgeAction,
	//来源于Buff   肉鸽遗物算Buff
	Buff,
	//来源于飞行物
	Bullet,
	//来源于AOE
	AoE,
	//来源于道具
	Item,
	//来源为召唤物
	Summoner
};

/**
 *伤害类型
 */
UENUM(BlueprintType)
enum class EDamageType : uint8
{
	//直接伤害（只有这种类型伤害会被肉质和防御属性影响）
	DirectDamage,
	//额外伤害 附伤
	ExtraDamage,
	//间歇性伤害(DoT)
	PeriodDamage,
	//反弹伤害
	ReflectDamage,
	//特殊伤害
	SpecDamage,
	//其他伤害 不参与战斗计算 也不该显示
	OtherDamage,
	//肉鸽初级机制伤害
	RogueJuniorDamage,
	//肉鸽高级机制伤害
	RogueSeniorDamage
};




/**
 *一次攻击类型信息，只要发生了碰撞（比如OnHit）就应该产生一个这个数据
 *包括动作、aoe、bullet等只要能打人的
 *这只是一个初始信息，也就是来自一次攻击本身的数据，最终一次“碰撞”的结构不是这个
 */
USTRUCT(BlueprintType)
struct FAttackInfo
{
	GENERATED_BODY()

public:
	//是否有效，关闭之后攻击就彻底无效了，没有伤害也没有击退
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	bool Active = true;
	
	/**
	 *伤害的来源类型，默认是来自一个动作的，也可以来自buff等
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	EAttackSource DamageSourceType = EAttackSource::AttackAction;

	/**
	 * 伤害的触发类型，默认是直伤，也可以是治疗等值
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	EDamageType DamageType = EDamageType::DirectDamage;

	/**
	 * 伤害的数值元素属性
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	EChaElemental Elemental = EChaElemental::Physical;
	
	/**
	 * 伤害力，这个与武器影响相乘才会得到最终的各种伤害
	 * 角色动作中作为百分比用（1=100%），代表动作值
	 * AoE等中应该作为直接伤害（1=1点伤害）
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FDamageValue DamagePower;
	
	/*
	 *  伤害的充能转换系数
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	float DamageCastItemEnergyPower = 0.1f;
	
	/**
	 * 武器影响
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FWeaponAffectAction WeaponEffect = FWeaponAffectAction();

	/**
	 * 是否作为一次治疗，如果是治疗则最后会回复
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	bool IsHeal = false;
	
	/**
	 * 攻击来源的向量，这也是动态传值的，蓝图没法设置
	 */
	UPROPERTY()
	FVector DamageIncomeVector = FVector::ZeroVector;

	/**
	 * 攻击时期望攻击者自身发生的动作变化
	 * 可能会有弹刀发生，所以这里只能是“期望”还得看挨打一方的脸色
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FActionChangeInfo AttackerActionChange = FActionChangeInfo();

	/**
	 * 攻击后期望挨打目标的动作变化，只能是个期望，因为挨打一方可能处于霸体等情况
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FActionChangeInfo DefenderActionChange = FActionChangeInfo();

	//震屏信息，如果这个信息中的Shake为空，就代表不震了
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FAwCameraShakeInfo ShakeInfo;

	// 手柄振动信息
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FAwForceFeedBackInfo ForceFeedBackInfo;
	
	/**
	 * 根据攻击源的角度，来改变这个信息的角度
	 */
	void RotateByDegree(float AttackDegree, FVector AttackPos, FVector DefenderPos)
	{
		// AttackerActionChange
		float AtkDegree;
		switch (this->AttackerActionChange.HitStun.VelocityType)
		{
			default:
			case EVelocityType::AttackerFace:	AtkDegree = AttackDegree; break;
			case EVelocityType::RelativeDir:	AtkDegree = (AttackPos - DefenderPos).Rotation().Yaw; break;
		}
		this->AttackerActionChange.HitStun.Velocity =  this->AttackerActionChange.HitStun.Velocity.RotateAngleAxis(AtkDegree, FVector::UpVector);

		// DefenderActionChange
		float DefDegree;
		switch (this->DefenderActionChange.HitStun.VelocityType)
		{
			default:
			case EVelocityType::AttackerFace:	DefDegree = AttackDegree; break;
			case EVelocityType::RelativeDir:	DefDegree = (DefenderPos - AttackPos).Rotation().Yaw; break;
		}
		this->DefenderActionChange.HitStun.Velocity = this->DefenderActionChange.HitStun.Velocity.RotateAngleAxis(DefDegree, FVector::UpVector);

		// TODO：目前是攻击者的面向，之后需要如何修改，这个值在用吗？
		this->DamageIncomeVector = FVector(1,0,0).RotateAngleAxis(AtkDegree, FVector::UpVector);
	}

};

/**
 * 一次特殊防御的信息，通常来说角色是没有这个信息的
 * 但是在一些情况下，某些受击框会附带这个信息，比如格挡，由这个信息告诉流程，最终改变成什么动作之类的
 * 假如没有这个（这是正常的情况）那么所有的攻击、受击等动作preorder就是AttackInfo说了算了，如果有，则要合计合计了
 */
USTRUCT(BlueprintType)
struct FDefenseInfo
{
	GENERATED_BODY()
public:
	//所针对的CharacterHitBox的Id，如果一个也没有，这条数据就没什么效果了
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FString> CharacterHitBoxId;

	//攻击方变化动作的期望值，比如弹刀等，最后弹不弹刀依然是双方比对的结果，不是这里一处就能说的算的
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FActionChangeInfo AttackerActionChange = FActionChangeInfo();

	//挨打者自身的动作变化期望值
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FActionChangeInfo DefenderActionChange = FActionChangeInfo();

	//受击方此时受到的伤害倍率调整，1=100%
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FDamageValue DamageModifer = FDamageValue(1.000f);

	//是否是just防御
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool bJustDenfense = false;

	// 手柄振动信息
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FAwForceFeedBackInfo ForceFeedBackInfo;
	
	bool operator == (const FDefenseInfo& Other) const
	{
		return this->CharacterHitBoxId == Other.CharacterHitBoxId && this->DamageModifer == Other.DamageModifer &&
			this->AttackerActionChange == Other.AttackerActionChange && this->DefenderActionChange == Other.DefenderActionChange;
	}

	/**
	 * 根据攻击源的角度，来改变这个信息的角度
	 */
	void RotateByDegree(float Degree)
	{
		this->AttackerActionChange.HitStun.Velocity = FRotator(0, Degree, 0).RotateVector(this->AttackerActionChange.HitStun.Velocity);
		this->DefenderActionChange.HitStun.Velocity = FRotator(0, Degree, 0).RotateVector(this->DefenderActionChange.HitStun.Velocity);
	}

	static FDefenseInfo ProtectDefenseInfo()
	{
		FDefenseInfo Res = FDefenseInfo();
		Res.DamageModifer = FDamageValue(0.0f);
		Res.AttackerActionChange.ChangeMethod = EActionChangeMethod::ToMontageState;
		Res.AttackerActionChange.ToState = ECharacterMontageState::Bounced;
		Res.AttackerActionChange.Priority = 10;
		return Res;
	}
};

/**
 * Just Dodge的信息，Just Dodge因为没有被命中，所以才需要不同的信息
 */
USTRUCT(BlueprintType)
struct FJustDodgeInfo
{
	GENERATED_BODY()
public:
	/**
	 * 是否有效
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	bool Active = false;
	
	/**
	 *挨打方会发生的动作变化
	 *由于只有挨打方会JustDodge，且是动作游戏，不应该会导致攻击方动作变化，所以此处只有这个
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FActionChangeInfo ActionOnJustDodge;
	
	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	TArray<FJsonFuncData> OnSuccess;
	
	FJustDodgeInfo(){};
	FJustDodgeInfo(FActionChangeInfo ChangeToAction):Active(true),ActionOnJustDodge(ChangeToAction){};

	static FJustDodgeInfo Default();
	bool operator == (const FJustDodgeInfo& Other) const;
};

/**
 * 一次伤害信息，由一个AttackInfo和最多一个DefenseInfo产生出来
 */
USTRUCT(BlueprintType)
struct FDamageInfo
{
	GENERATED_BODY()
	FDamageInfo();
public:
	/**
	 * 攻击者
	 */
	UPROPERTY()
	AAwCharacter* Attacker = nullptr;
	
	/**
	 *打击到的碰撞盒子
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	USceneComponent* HitBox = nullptr;

	/**
	 * 打中的盒子的信息
	 */
	UPROPERTY()
	UCharacterHitBoxData* HitBoxData = nullptr;

	/**
	 * 造成伤害的点和HitBox坐标的偏移（HitBox.GetActorLocation + HitLocation = 受击世界坐标）
	 * 虽然这么做，代码里很多算出碰撞的地方要减去HitBox.GetActorLocation看起来很吃屎
	 * 但是在Buff之类没有Location的地方不传值的时候就知道这个妙处了
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FVector HitLocationOffset = FVector::ZeroVector;
	
	/**
	 *伤害的来源类型
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	EAttackSource DamageSourceType = EAttackSource::AttackAction;
	/**
	 *伤害的数值类型
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	EChaElemental Elemental = EChaElemental::Physical;
	/**
	 * 伤害触发类型
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	EDamageType DamageType = EDamageType::DirectDamage;
	/**
	 * 是否是一次治疗
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	bool IsHeal = false;
	/**
	 * 伤害值 或者 治疗值
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FDamageValue DamagePower = FDamageValue();

	/*
	 *  伤害的充能转换系数
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	float DamageCastItemEnergyPower = 1.f;
	/**
	 * 是否是一次暴击
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	bool IsCritical  = false;

	//
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<EChaElemental> CanCriticElemental = {EChaElemental::Physical};

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<EDamageType> CanCriticTypes = {EDamageType::DirectDamage,EDamageType::ExtraDamage,EDamageType::ReflectDamage};
	/**
	 * 此次伤害的暴击概率
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	float CriticalChance  = 0.f;
	
	//伤害乘算倍率集合
	UPROPERTY()
	TMap<EDamageArea,float> ValuePowerArea;
	
	/**
	 * 攻击来源的向量，这也是动态传值的，蓝图没法设置
	 */
	UPROPERTY()
	FVector DamageIncomeVector = FVector();

	/**
	 * 伤害是否被闪避了，闪避了就不会跳数字，也不可能破坏部位了，算是小优化
	 */
	UPROPERTY()
	bool BeDodged = false;

	/**
	 * 最终攻击方动作变化
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FActionChangeInfo AttackerActionChange = FActionChangeInfo();

	//攻击方动作取得是不是attackInfo的，如果不是，自然是defenseInfo的
	UPROPERTY()
	bool AttackerActionUseAttackInfo = true;

	/**
	 * 最终防守方动作变化
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FActionChangeInfo DefenderActionChange = FActionChangeInfo(ECharacterMontageState::Hurt);

	//防御方动作取得是不是attackInfo的，如果不是，自然是defenseInfo的
	UPROPERTY()
	bool DefenderActionUseAttackInfo = true;

	//攻击者的动作优先级（作为计算Break值的参数使用）
	int AttackInfoPriority = 0;

	//受击者的动作优先级（作为计算Break值的参数使用）
	int DefendInfoPriority = 0;

	/**
	 * 根据攻击源的角度，来改变这个信息的角度
	 */
	void RotateByDegree(float Degree)
	{
		this->DamageIncomeVector = FRotator(0, Degree, 0).RotateVector(DamageIncomeVector);
	}

	/**
	 *只有AttackInfo而产生的的一个伤害信息
	 *@param  TargetHitBox 打中了那个CharacterHitBox
	 *@param AttackInfo 产生这个伤害的攻击信息
	 *@param  DefenseInfo 防御信息
	 *@param HitLocationOffset 命中点相对于TargetHitBox的ComponentLocation的偏移
	 */
	static FDamageInfo FromAttackAndDefense(USceneComponent* TargetHitBox, FAttackInfo AttackInfo, FDefenseInfo DefenseInfo, FVector HitLocationOffset = FVector::ZeroVector);

	//总伤害 = (物理+元素)*伤害倍率区间1*伤害倍率区间2*伤害倍率区间3 ...
	int FinalDamage() 
	{
		int TotalDamage = this->DamagePower.TotalDamage();
		for (auto Power:ValuePowerArea)
		{
			if (Power.Key<=EDamageArea::BreakDamagePower)
			{
				continue;
			}
			if (!IsCritical&&Power.Key == EDamageArea::InjuredCriticalPower)
			{
				continue;
			}
			Power.Value = FMath::Clamp(Power.Value,0,Power.Value);
			TotalDamage = FMath::RoundToInt(Power.Value*TotalDamage);
		}
		return TotalDamage;
	}

	//计算此次伤害最终是否是暴击
	bool JudgeFinalDamageCritical();
	
	void CheckMinDamagePowerArea()
	{
		for (auto Power:ValuePowerArea)
		{
			ValuePowerArea[Power.Key] = FMath::Clamp(ValuePowerArea[Power.Key],0,ValuePowerArea[Power.Key]);
		}
	}
};

/**
 * 其实是是用于DamageManager处理的一个Info
 */
USTRUCT()
struct FDamageDealer
{
	GENERATED_BODY()
public:
	//攻击者只可能是一个角色（AwCharacter）或者空，如果不是角色，就通过这个找到角色，找不到就给空
	UPROPERTY()
	AAwCharacter* Attacker;

	//TODO 干掉我，同时换成一个Actor，因为任何Actor都会受击
	UPROPERTY()
	AAwCharacter* Defender;

	UPROPERTY()
	FDamageInfo DamageInfo;

	FDamageDealer():Attacker(nullptr), Defender(nullptr), DamageInfo(FDamageInfo()){};
	FDamageDealer(AAwCharacter* Atker, AAwCharacter* Defer, FDamageInfo DInfo):
		Attacker(Atker), Defender(Defer), DamageInfo(DInfo){};
};

/**
 * 泽格组啊的信息，和AttackInfo是对应的，原本有AttackHitBox和CatchHitbox
 *  现在AttackHitBox足够了，Catch的删除了，所以效果放在HitInfo里
 */
USTRUCT(BlueprintType)
struct FCatchInfo
{
	GENERATED_BODY()
public:
	/**
	 *是否激活中，激活了之后才会有效
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool Active = false;

	/**
	 *抓点的（我抓别人用的抓点）的Id
	 *如果TargetAttachMe==true，那么这个点的作用就是我被别人Attach的点了
	 *确实有二义性，但是就这么地了
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString CatchPointId;

	/**
	 * 如果是True，就是被抓目标攀附到我身上，逻辑是反过来的
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool TargetAttachMe = false;
	
	/**
	 * 别人抓我的点，因为现在未必是只有我抓别人，比如龙咬住我，实际上我攀附到龙身上了（我受限）
	 * 如果对方已经攀附到别的人身上，或者没有对应的抓点，就不可能“被我咬住”了
	 * 当然，一切大前提还是，TargetAttachMe=true，否则这个值白填
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString TargetCaughtPointId = FString("Body");
};

/**
 * 一个进攻信息，所有要对角色发起进攻的，都得最终变成一个进攻信息
 * 一个进攻信息配合命中部位(HitBox)带来的DefenseInfo（优先取特殊的）得到一次
 * 进攻流程，进攻流程中有包括双方角色动作变化、造成伤害等等标准事件
 */
USTRUCT(BlueprintType)
struct FOffenseInfo
{
	GENERATED_BODY()
public:
	/**
	 * 一个攻击的SourceId，这个Id的赋值规则是：
	 * 1. 来自动作的，应该给动作Id
	 * 2. 来自Aoe和Bullet的，给对应的GetUniqueId()转字符串
	 * 3. 来自Buff的，随便给一个，毕竟Buff不应该受到“同目标攻击次数和间隔”规则限制
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString SourceId = FString();
	
	/**
	 * 这是有效的AttackHitBox的GetName()，可以同时关联多个AttackHitBox
	 * 这东西只有Buff要进攻的时候是空的，因为非buff都应该有个捕捉框才行。
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FString> AttackHitBoxName;
	
	/**
	 * 这是几号动作命中点，这个点会指向所在ActionInfo里面的Damage[这个Index]
	 * 这也代表了一个动作中的第几个分段攻击，同一个分段攻击会共享命中次数和延迟
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int Index = 0;

	//这是命中点的偏移向量 有时候的信息弹出是围绕命中点 而不是就是命中点
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FVector OffensePosOffset = FVector::ZeroVector;
	
	//能碰到同一个目标的次数
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int CanHitTimes = 1;

	//若是碰撞次数超过1次，那么每2次之间的间隔是多久（秒）
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	float HitSameTargetDelay = 0.200f;

	//这个HitBox碰撞优先级，当有多个HitBox命中时，我们这帧算是哪个碰到了依赖于这个，这个越大越优先
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int Priority = 1;

	//这个Hit信息下的攻击信息
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FAttackInfo AttackInfo;

	//这个Hit信息下的抓取信息
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FCatchInfo CatchInfo;

	//如果是玩家角色，则在这次攻击到下次攻击间隔不超过这个值（秒）的时候算是连击+1
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float ComboOffenseTime = 2.0f;

	//当Index相同时，认为相等
	bool operator ==(const FOffenseInfo& Other) const
	{
		return this->SourceId == Other.SourceId && this->Index == Other.Index;
	}
};