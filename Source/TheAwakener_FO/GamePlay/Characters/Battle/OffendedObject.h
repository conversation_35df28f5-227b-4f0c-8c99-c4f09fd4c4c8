// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "BattleStructs.h"
#include "TheAwakener_FO/GamePlay/Characters/HitBox/ActorCatcher.h"
#include "UObject/Interface.h"
#include "OffendedObject.generated.h"

class AAwCharacter;

// This class does not need to be modified.
UINTERFACE(meta=(CannotImplementInterfaceInBlueprint))
class UOffendedObject : public UInterface
{
	GENERATED_BODY()
};

/**
 * 可以受到攻击的对象的接口
 */
class THEAWAKENER_FO_API IOffendedObject
{
	GENERATED_BODY()

	// Add interface functions to this class. This is the class that will be inherited to implement this interface.
public:
	/**
	 * 能否被这个Attacker进行侵犯
	 * @param OffenseInfo 一次进攻的完整信息信息，以判断这次信息是否有效
	* @param Attacker 攻击者对象，如果是子弹或者aoe，那么就是子弹或者aoe的HitManager
	 * @param AttackerInCharge 就是一个负责人的意思，可以是nullptr代表无人对攻击负责，但是攻击依然有效
	 * @param IncludeAttackerActionHitBoxes 是否把AttackerInCharge的动作攻击盒一起算上，如果AttackerInCharge是空，true也没用；【强烈建议】动作攻击大多为true，非角色动作（如Aoe bullet等）通常为false
	*  @param SameSideFriendlyFire 同Side角色之间可否互相伤害
	 * @param AllyFriendlyFire 对于友方是否可以互相伤害
	 */
	virtual FOffendedCaughtResult CanBeOffended(
		FOffenseInfo OffenseInfo,
		UAttackHitManager* Attacker,
		AAwCharacter* AttackerInCharge,
		bool IncludeAttackerActionHitBoxes,
		bool SameSideFriendlyFire = false,
		bool AllyFriendlyFire = false
	){return FOffendedCaughtResult();}
	
	/**
	 * 这个Actor受到进攻，比如来自动作的攻击，来自子弹、来自aoe等，都得走这个，这是一次完整的受击流程
	 * @param OffenseInfo 一次进攻的完整信息信息，在调用前，请确保其AttackInfo.DamagePower已经是计算后的伤害力了（包括动作值之类都算好了），这里将和防御部位直接进行计算了
	 * @param Attacker 攻击者对象，如果是子弹或者aoe，那么就是子弹或者aoe的HitManager
	 * @param AttackerInCharge 就是一个负责人的意思，可以是nullptr代表无人对攻击负责，但是攻击依然有效
	 * @param BeHitBox 打在我的哪个碰撞盒上了
	 * @param FromAttackBox 来自哪个攻击盒子，可能是null的，这不强求，但是照理来说是都能拿到的
	 * @param SameSideFriendlyFire 同Side角色之间可否互相伤害
	 * @param AllyFriendlyFire 对于友方是否可以互相伤害
	 */
	virtual void BeOffended(
		FOffenseInfo OffenseInfo,
		UAttackHitManager* Attacker,
		AAwCharacter* AttackerInCharge,
		USceneComponent* BeHitBox,
		USceneComponent* FromAttackBox,
		bool SameSideFriendlyFire = false, bool AllyFriendlyFire = false
	){};
};
