// Fill out your copyright notice in the Description page of Project Settings.


#include "BGMManager.h"

#include "Kismet/GameplayStatics.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/FunctionLibrary/ResourceFuncLib.h"

int UBGMManager::AddBgmByBgmList(TMap<FString, FString> BgmList)
{
	int Count = 0;
	for (const TTuple<FString, FString> Bgm : BgmList)
	{
		if (AddBGM(Bgm.Key, Bgm.Value))
			Count ++;
	}
	return Count;
}

bool UBGMManager::AddBGM(FString Key, FString Path)
{
	if (this->BGM.Contains(Key)) return false;
	
	USoundBase* Sound = LoadObject<USoundBase>(nullptr, *UResourceFuncLib::GetAssetPath(Path));
	if (!Sound) return false;

	
	BGM.Add(Key, Sound);
	
	//UAudioComponent* Audio = UGameplayStatics::CreateSound2D(this->GetWorld(), Sound);
	return true;
}

void UBGMManager::PlayBGM(FString Key, float FadeInSec)
{
	//找不到音乐或者是正在播放的音乐就不运行这个
	//if (BGM.Contains(Key) == false || (this->PlayedBGMList.Num() > 0 && this->PlayedBGMList[0] == Key)) return;
	
	if (BGM.Contains(Key) == false) return;
	
	if(this->PlayedBGMList.Num() > 0 && this->PlayedBGMList[0] == Key)
	{
		if(this->BGMPlayer)
		{
			if(this->BGMPlayer->bIsPaused)
			{
				this->BGMPlayer->Play();
				this->BGMPlayer->FadeIn(FadeInSec);
			}
			return;
		}
	}
	
	if (!this->BGMPlayer)
	{
		// if (!this->PlayerCha) this->PlayerCha = UGameplayFuncLib::GetAwGameState()->MyCharacter;
		// if (!this->PlayerCha || !this->PlayerCha->GetWorld()) return;
		if(UAwGameInstance::Instance)
			this->BGMPlayer = UGameplayStatics::CreateSound2D(UAwGameInstance::Instance->GetWorld(), BGM[Key]);
	}
	if (!this->BGMPlayer) return;

	//开始播放
	if (BGMPlayer->IsPlaying())
	{
		BGMPlayer->FadeOut(FadeInSec, 0);
		BGMPlayer->SetSound(BGM[Key]);
		BGMPlayer->FadeIn(FadeInSec);
	}else
	{
		BGMPlayer->SetSound(BGM[Key]);
		BGMPlayer->Play();
	}

	//记录一下
	PlayedBGMList.Insert(Key, 0);
	while (PlayedBGMList.Num() > 5) PlayedBGMList.RemoveAt(5);
}

void UBGMManager::StopCurrentBGM(float FadeOutSec)
{
	if(this->BGMPlayer)
	{
		BGMPlayer->FadeOut(FadeOutSec, 0);
		BGMPlayer->Stop();
	}
}

void UBGMManager::ResumeToPrevBGM(float FadeInSec)
{
	if (PlayedBGMList.Num() <= 0) return;
	const FString Key = PlayedBGMList.Num() > 1 ? PlayedBGMList[1] : PlayedBGMList[0];
	PlayBGM(Key, FadeInSec);
}

USoundBase* UBGMManager::GetBgm(FString Key)
{
	if (BGM.Contains(Key))
		return BGM[Key];
	
	return nullptr;
}
