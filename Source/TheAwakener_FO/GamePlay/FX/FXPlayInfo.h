// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "FXPlayInfo.generated.h"

/**
 *  播放视觉或者听觉特效的信息
 */
USTRUCT(BlueprintType)
struct FFXPlayInfo
{
	GENERATED_BODY()
public:
	//要播放的视觉特效
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString VFX = "";

	//要播放的听觉特效
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString SFX = "";


	FFXPlayInfo(){};
	FFXPlayInfo(FString SightEffect, FString SoundEffect):VFX(SightEffect), SFX(SoundEffect){};

	bool Valid() const {return VFX.IsEmpty() == false || SFX.IsEmpty() == false;}
};
