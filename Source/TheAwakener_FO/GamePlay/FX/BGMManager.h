// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "UObject/Object.h"
#include "BGMManager.generated.h"

/**
 * 背景音乐管理器
 */
UCLASS()
class THEAWAKENER_FO_API UBGMManager : public UObject
{
	GENERATED_BODY()
private:
	//歌单，所有的音乐
	UPROPERTY()
	TMap<FString, USoundBase*> BGM;

	//播放的BGM的队列，其实是为了返回上一个用的，当前的肯定是0，上一个是1，类推，记录最多5个
	UPROPERTY()
	TArray<FString> PlayedBGMList;
	
	//播放器
	UPROPERTY()
	UAudioComponent* BGMPlayer = nullptr;

	//玩家角色
	UPROPERTY()
	AAwCharacter* PlayerCha = nullptr;

public:
	// 根据 BgmList 添加歌单，返回添加成功几首歌
	int AddBgmByBgmList(TMap<FString, FString> BgmList);
	
	//添加歌单，返回是否添加成功（如果对应音乐不存在或者已经存在对应的key就会失败）
	bool AddBGM(FString Key, FString Path);

	//播放歌单的某个Key对应的音乐，此时，会FadeOut之前的一个
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|BGM")
	void PlayBGM(FString Key, float FadeInSec = 1.5f);

	//暂停当前的BGM
	UFUNCTION(BlueprintCallable, Category = "AW|Gameplay|BGM")
	void StopCurrentBGM(float FadeOutSec = 0.5f);

	//返回上一个音乐播放
	void ResumeToPrevBGM(float FadeInSec = 1.5f);

	UFUNCTION(BlueprintPure, Category = "AW|Gameplay|BGM")
	USoundBase* GetBgm(FString Key);
};
