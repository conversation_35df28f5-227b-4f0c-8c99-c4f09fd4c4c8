// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/GamePlay/Roguelike/Map/RogueMapConfig.h"
#include "Subsystems/GameInstanceSubsystem.h"
#include "TheAwakener_FO/Gameframework/Timeline/TimelineNode.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "AwRelicSubSystem.generated.h"
/**
 * Relic Subsystem
 * by Mu<PERSON><PERSON>
 */
UENUM(BlueprintType)
enum class ERelicRarity:uint8
{
	// 普通
	Normal ,
	// 稀有
	Rarely ,
	// 史诗
	Epic ,
	// 传说
	Legend
};

UENUM(BlueprintType)
enum class ERelicType:uint8
{
	// 进攻型遗物
	Attack ,
	// 生存型遗物
	Survive ,
	// 资源型遗物
	Resources,
	// 其他型 或称作 敏捷型遗物
	Other
};


USTRUCT(BlueprintType)
struct FAwRelicInfo
{
	GENERATED_BODY()
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Relic")
			FString Id = "";
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Relic")
			FString Desc = "";
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Relic")
			FString RecordId = "";
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Relic")
			ERelicType RelicType;
	//不同等级但相同效果的卡的Id（用来标记互斥的Id）
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Relic")
			FString RelicGroupId = "";
	//分组Tag
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Relic")
			TArray<FString> Tags;
	//前置节点  暂时弃用
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Relic")
			TArray<FString> PreNodeIds;
	
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Relic")
			ERelicRarity  RelicRarity = ERelicRarity::Normal;
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Relic")
			int  RelicLevel = 1;
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Relic")
			int  MaxNum = 1;
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Relic")
			float Value = 1;
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Relic")
			TArray<FString> IconPath ;
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Relic")
			TArray<FString> IconKeys;
	UPROPERTY(EditAnywhere,BlueprintReadWrite, Category = "Relic")
			TArray<FString> RelicEffectBuffs;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
			TArray<FJsonFuncData> OnRelicPreview;
public:
	static FAwRelicInfo FromJson(TSharedPtr<FJsonObject> JsonObj);
};

/**
 * Buff在非Damage流程中的返回值
 */
USTRUCT(BlueprintType)
struct FRelicPreviewResult
{
	GENERATED_BODY()
public:
	//不记录实际结构 减少允许计算时的内存占用 实际有需求可以通过id去 subsystem里找
	UPROPERTY(BlueprintReadOnly)
	FString PreViewRelicId;
	
	UPROPERTY(BlueprintReadWrite)
	TMap<FString,FString> PreViewResult;

	UPROPERTY()
	UTimelineNode* TimelineNode = nullptr;
	
	 bool IsResultValid() const{return !PreViewResult.IsEmpty();}
	
	FRelicPreviewResult(){};
	FRelicPreviewResult(FString PreViewId, UTimelineNode* Node = nullptr):
		PreViewRelicId(PreViewId), TimelineNode(Node){};
};


//房间中一种遗物稀有度的随机概率
USTRUCT(BlueprintType)
struct FAwRoomRelicRarityDrop
{
	GENERATED_BODY()
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "RelicDrop")
	ERelicRarity Rarity = ERelicRarity::Normal;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "RelicDrop")
	int Level = 1;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "RelicDrop")
	float Rate = 0.f;
public:
	static FAwRoomRelicRarityDrop FromJson(TSharedPtr<FJsonObject> JsonObj)
	{
		FAwRoomRelicRarityDrop TargetMetaData = FAwRoomRelicRarityDrop();
		TargetMetaData.Rarity = UDataFuncLib::AwGetEnumField(JsonObj,"RelicRarity",ERelicRarity::Normal);
		TargetMetaData.Level = UDataFuncLib::AwGetNumberField(JsonObj,"Level",1);
		TargetMetaData.Rate = UDataFuncLib::AwGetNumberField(JsonObj,"Rate",0.f);
		return  TargetMetaData;
	}
};
//一种房间不同等级范围中遗物稀有度的掉落分布
USTRUCT(BlueprintType)
struct FAwRoomRelicDrop
{
	GENERATED_BODY()
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "RelicDrop")
	FString Id = "";
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "RelicDrop")
	ERogueRoomType RoomType = ERogueRoomType::Normal;
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "RelicDrop")
	int MinLevel = 0;
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "RelicDrop")
	int MaxLevel = 0;
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "RelicDrop")
	TArray<FAwRoomRelicRarityDrop> DropsRate;
public:
	static FAwRoomRelicDrop FromJson(TSharedPtr<FJsonObject> JsonObj)
	{
		FAwRoomRelicDrop TargetMetaData = FAwRoomRelicDrop();
		TargetMetaData.RoomType = UDataFuncLib::AwGetEnumField(JsonObj,"RoomType",ERogueRoomType::Normal); 
		TargetMetaData.MinLevel = UDataFuncLib::AwGetNumberField(JsonObj,"MinLevel",0);
		TargetMetaData.MaxLevel = UDataFuncLib::AwGetNumberField(JsonObj,"MaxLevel",0);
		TargetMetaData.Id =  UDataFuncLib::EnumToFString(TargetMetaData.RoomType) + "_"+FString::FromInt(TargetMetaData.MinLevel)+"_"+FString::FromInt(TargetMetaData.MaxLevel );
		for (const TSharedPtr<FJsonValue, ESPMode::ThreadSafe> AppearInfo : UDataFuncLib::AwGetArrayField(JsonObj, "RarityDrop"))
		{
			TargetMetaData.DropsRate.Add(FAwRoomRelicRarityDrop::FromJson(AppearInfo->AsObject()));
		}
		return  TargetMetaData;
	}
};


DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FRelicChangeDelegate,FAwRelicInfo,RelicInfo);
DECLARE_DYNAMIC_MULTICAST_DELEGATE(FRelicDelegate);
UCLASS()
class THEAWAKENER_FO_API UAwRelicSubSystem : public UGameInstanceSubsystem
{
	GENERATED_BODY()
public:
	static bool IsReGet;
	void InitSubSystem();
	//存读档
	void SaveData();
	void LoadSaveData();

	//正式遗物的获取,用于特定计算计算,有一些非正式的其他用途遗物
	TMap<FString,int> RelicHasGotInFormalPool;
public:
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Relic")
	TMap<FString,FAwRelicInfo> AllRelic;
	//当前能获取的遗物
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Relic")
	TArray<FString> GetableRelic;
	//已经获取的遗物 包括正式与非正式
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Relic")
	TMap<FString,int> RelicHasGot;
	UFUNCTION(BlueprintCallable, Category = "Relic")
	bool FindGotRelicInGroup(FAwRelicInfo& RelicInfo, FString GroupId);
	//未获取的遗物
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Relic")
	TArray<FString> RelicNotGet;
	
	UPROPERTY(BlueprintAssignable)
	FRelicChangeDelegate OnPlayerGetRelic;
	UPROPERTY(BlueprintAssignable)
	FRelicChangeDelegate OnPlayerLoseRelic;
	UPROPERTY(BlueprintAssignable)
	FRelicDelegate OnPlayerClearRelic;
	
	//升级遗物 (待考虑)
	UPROPERTY(BlueprintAssignable)
	FRelicChangeDelegate OnRelicUpgradeRelic;
	//-------随机获取遗物---------
	//通过额外参数和加权计算获取目标数随机遗物
	UFUNCTION(BlueprintCallable)
	TMap<FString,int> GetRandomItemInRoom(int num, int RoomLevel,ERogueRoomType RoomType);
	//随机遗物(遗物修改为等级制后的版本)
	UFUNCTION(BlueprintCallable)
	TMap<FString,int> GetRandomItemInRoom_New(int num, int RoomLevel,ERogueRoomType RoomType);
	//根据等级权重随机出需要的等级
	int GetRandomLevelByWeight(TMap<int,float> LevelWeight);
	//根据权重随机出RelicId(圣遗物等级制版本)
	//Level = 0时，不指定目标等级
	FString GetRandomRelicByLevel(int Level, TArray<FString> Pool,TMap<int,float> LevelWeight, bool RandGotRelic = true, bool RandNotGotRelic = true);
	//只随机出现在已有的GroupId高一级的RelicId(圣遗物等级制版本)
	FString GetRandomRelicByGotRelic(TArray<FString> Pool, TMap<int,float> LevelWeight);
	//获取拥有指定tag的遗物
	UFUNCTION(BlueprintCallable)
	TArray<FString> GetRandomItemByTag(int Num,TArray<FString>Tags);
	//通过稀有度权重加权计算获取目标稀有度
	ERelicRarity GetRandomRarityByRarityWeight(TMap<ERelicRarity,float> RarityWeight);
	//通过特定逻辑暗骰随机
	UFUNCTION(BlueprintCallable)
	FString GetRandomRelicByTagWeight(ERelicRarity TargetRarity,TArray<FString>Pool,bool bTagLeader);
	//获取拥有指定tag的遗物
	UFUNCTION(BlueprintCallable)
	TArray<FString> CheckRelicTagsGetable(TArray<FString>Tags);
	UFUNCTION(BlueprintCallable)
	TArray<FString> GetRelicTagsHasGot();
	//全池获取目标随机数的遗物
	UFUNCTION(BlueprintCallable)
	TArray<FString> GetRandomItem(int Number);
	//------随机获取遗物结束-----
	//获取目标遗物的具体信息
	UFUNCTION(BlueprintCallable)
	FAwRelicInfo GetRelicInfo(FString RelicId);
	//从已获取的遗物中移除目标1个
	UFUNCTION(BlueprintCallable)
	void RemoveRelic(FString RelicId, AAwPlayerController* Controller);
	//从已获取的遗物中移除目标id的所有遗物
	UFUNCTION(BlueprintCallable)
	void RemoveAllSameIdRelic(FString RelicId, AAwPlayerController* Controller);
	//清除当前遗物
	UFUNCTION(BlueprintCallable)
	void ClearRelic();
	//清除当前遗物效果
	UFUNCTION(BlueprintCallable)
	void ClearRelicEffect();
	//给玩家遗物
	UFUNCTION(BlueprintCallable)
	void GivePlayerRelic(FString RelicId, AAwPlayerController* Controller, bool InPool = true);
	//给多个玩家遗物
	UFUNCTION(BlueprintCallable)
	void GivePlayerRelic_Multi(FString RelicId, bool InPool = true);
	// 根据当前的遗物重新获得一遍
	UFUNCTION(BlueprintCallable)
	void ReGetRelic();
	
	//根据RelicGroupId 获取符合的RelicId
	TArray<FString> GetRelicByGroupId(FString GroupId);
	//获取当前已获得的GroupId和相应等级
	//<GroupId, Level>
	TMap<FString,int> GetHasGotRelicGroup();
	//获取当前可获得但没有获得的GroupId
	//<Group, Level>
	TMap<FString,int> GetNotGotRelicGroup();

	UFUNCTION(BlueprintPure)
	TMap<FString,int> GetRelicHasGotOnUI();
	
	UFUNCTION(BlueprintCallable)
	TArray<FString> GetAllRelicByGroup(FString GroupTag,bool InPool);
	
	UFUNCTION(BlueprintCallable)
	TMap<FString,int> GetRelicHasGotAfterSortByGroup(FString GroupTag,bool InPool = true);

	UFUNCTION(BlueprintCallable)
	TArray<FString> GetAllRelicAfterSortByGroup(FString GroupTag,bool InPool);
	
	UFUNCTION(BlueprintCallable)
	TArray<FString> GetAllRelicAfterIdSortByGroup(FString GroupTag,bool InPool);
	
	UFUNCTION(BlueprintCallable)
	int GetRelicNumInRecordByGroup(FString GroupTag,bool InPool);

	//从完成解锁的遗物类别中随机n个奖励
	UFUNCTION(BlueprintCallable)
	TArray<FString> GetRelicUnlockRewards(TArray<FString>PoolGroups,int RewardsNum);

	UFUNCTION(BlueprintPure)
	bool	CheckRelicGroupAllUnlock(FString Group);
	
	//给玩家初始遗物
	UFUNCTION(BlueprintCallable)
	void GivePlayerInitialRelic(AAwPlayerController* Controller, bool InPool = true);

	UFUNCTION(BlueprintCallable)
	void SetInitialRelics(TArray<FString> Relics);

	UFUNCTION(BlueprintPure)
	TArray<FString> GetInitialRelics();

	UFUNCTION(BlueprintCallable)
	TArray<FRelicPreviewResult> OnRelicPreview(FString RelicId,AAwPlayerController* PlayerController);
private:
	//检查GetableRelic
	void CheckGetableRelic();
	//检查目标稀有度是否存在于目标池子中
	bool CheckRarityVaild(ERelicRarity& TargetRarity, TArray<FString> Pool);
	
	//检查目标稀有度是否存在于目标池子中
	bool CheckLevelVaild(int& TargetLevel, TArray<FString> Pool);

	//处理遗物Buff效果的通用函数
	void ProcessRelicBuffs(const TArray<FString>& RelicEffectBuffs, AAwCharacter* PlayerCharacter, bool IsAdd = true);

	//随机时使用
	TMap<FString,int> RelicTagHasGot;
	//战斗初始携带遗物
	TSet<FString>InitialRelics ;
};



