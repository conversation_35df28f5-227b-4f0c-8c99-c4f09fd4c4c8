// Fill out your copyright notice in the Description page of Project Settings.


#include "AwRelicSubSystem.h"

#include "Algo/Accumulate.h"
#include "Kismet/KismetArrayLibrary.h"
#include "Kismet/KismetMathLibrary.h"
#include "TheAwakener_FO/FunctionLibrary/DataFuncLib.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/DesignerScript/Trigger/TriggerScript.h"
#include "TheAwakener_FO/FunctionLibrary/CommonFuncLib.h"
#include "TheAwakener_FO/GameFramework/Manager/AwRogueDataSystem.h"
#include "TheAwakener_FO/GamePlay/Buff/BuffManager.h"
#include "TheAwakener_FO/GamePlay/Roguelike/BattleStyle/RogueBattleStyleSubSystem.h"
#include "TheAwakener_FO/GamePlay/Roguelike/BattleUpgrade/RogueBattleUpgradeSubSystem.h"

bool UAwRelicSubSystem::IsReGet = false;
FAwRelicInfo FAwRelicInfo::FromJson(TSharedPtr<FJsonObject> JsonObj)
{
	FAwRelicInfo TargetMetaData = FAwRelicInfo();
	TargetMetaData.Id = UDataFuncLib::AwGetStringField(JsonObj,"Id");
	TargetMetaData.RecordId = UDataFuncLib::AwGetStringField(JsonObj,"RecordId");
	TargetMetaData.Desc = UDataFuncLib::AwGetStringField(JsonObj,"Desc");
	TargetMetaData.RelicType = UDataFuncLib::AwGetEnumField(JsonObj,"RelicType",ERelicType::Other);
	TargetMetaData.RelicGroupId = UDataFuncLib::AwGetStringField(JsonObj,"GroupId");
	TargetMetaData.Tags = UDataFuncLib::AwGetStringArrayField(JsonObj,"Tags");
	TargetMetaData.PreNodeIds = UDataFuncLib::AwGetStringArrayField(JsonObj,"PreNodeIds");
	TargetMetaData.RelicRarity = UDataFuncLib::AwGetEnumField(JsonObj,"RelicRarity",ERelicRarity::Normal);
	TargetMetaData.RelicLevel = UDataFuncLib::AwGetNumberField(JsonObj,"RelicLevel",1);
	TargetMetaData.MaxNum = UDataFuncLib::AwGetNumberField(JsonObj,"MaxNum",1);
	TargetMetaData.Value = UDataFuncLib::AwGetNumberField(JsonObj,"Value",1);
	
	TargetMetaData.IconPath = UDataFuncLib::AwGetStringArrayField(JsonObj,"IconPath");
	TargetMetaData.IconKeys= UDataFuncLib::AwGetStringArrayField(JsonObj,"IconKeys");
	TargetMetaData.RelicEffectBuffs = UDataFuncLib::AwGetStringArrayField(JsonObj,"EffectBuff");

	for (const TSharedPtr<FJsonValue> Value : UDataFuncLib::AwGetArrayField(JsonObj, "OnRelicPreview"))
	{
		TargetMetaData.OnRelicPreview.Add(UDataFuncLib::SplitFuncNameAndParams(Value->AsString()));
	}
	
	return  TargetMetaData;
}

void UAwRelicSubSystem::InitSubSystem()
{
	AllRelic = UGameplayFuncLib::GetAwDataManager()->GetAllMetaRelics();
	AllRelic.GenerateKeyArray(RelicNotGet);
	CheckGetableRelic();
}

void UAwRelicSubSystem::SaveData()
{
	if (!GetGameInstance())
	{
		return;
	}
	UAwRogueDataSystem* SubSystem = GetGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	if (!SubSystem)
	{
		return;
	}
	for (auto RelicInfo :RelicHasGot)
	{
		SubSystem->SetRelicHasGet_CurBattle(RelicInfo.Key,RelicInfo.Value);
		//设置已获得过的Record
		SubSystem->SetRelicHasGetRecord(RelicInfo.Key);
	}
}

void UAwRelicSubSystem::LoadSaveData()
{
	if (!GetGameInstance())
	{
		return;
	}
	UAwRogueDataSystem* SubSystem = GetGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	if (!SubSystem)
	if (!SubSystem)
	{
		return;
	}
	TMap<FString, int> RelicHasGotData = SubSystem->GetAllRelicHasGet_CurBattle();
	RelicHasGot = RelicHasGotData;

	for (auto Relic:RelicHasGot)
	{
		if (AllRelic.Contains(Relic.Key))
		{
			if (!AllRelic[Relic.Key].Tags.Contains("NotInFormal"))
			{
				RelicHasGotInFormalPool.Add(Relic);
			}
		}
	}
}

bool UAwRelicSubSystem::FindGotRelicInGroup(FAwRelicInfo& RelicInfo, FString GroupId)
{
	RelicInfo= FAwRelicInfo();
	for (auto KV :RelicHasGot)
	{
		auto Relic = GetRelicInfo(KV.Key);
		if (Relic.RelicGroupId==GroupId)
		{
			RelicInfo = Relic;
			return true;
		}
	}
	return false;
}

TMap<FString,int> UAwRelicSubSystem::GetRandomItemInRoom(int num,  int RoomLevel,ERogueRoomType RoomType)
{
	TMap<FString,int> Res;
	TArray<FString>Pool = GetableRelic;

	TMap<ERelicRarity,float> RarityWeight;
	//权重判断
	{
		UAwDataManager* DataManager= UGameplayFuncLib::GetAwDataManager();
		if (!DataManager)
		{
			//UE_LOG(LogTemp, Error, TEXT("DataManager is Not Vaild In RelicRandom"));
			return  Res;
		}
		TMap<FString, FAwRoomRelicDrop> RelicDropInfo = DataManager->GetRogueRoomRelicDrop();
			
		for (auto DropInfo : RelicDropInfo)
		{
			if (RoomType == DropInfo.Value.RoomType&& UKismetMathLibrary::InRange_FloatFloat(RoomLevel,DropInfo.Value.MinLevel,DropInfo.Value.MaxLevel))
			{
				for (auto Drop:DropInfo.Value.DropsRate)
				{
					RarityWeight.Add(Drop.Rarity,Drop.Rate);
				}
				break;
			}
		}
	}
	
	for (int i =0;i<num&&!Pool.IsEmpty();++i)
	{
		TArray<FString>CurPool = Pool; 
		//稀有度权重 
		ERelicRarity TargetRarity = GetRandomRarityByRarityWeight(RarityWeight);
		CheckRarityVaild(TargetRarity,CurPool);
		for(TArray<FString>::TIterator Iterator =CurPool.CreateIterator();Iterator;++Iterator)
		{
			FAwRelicInfo* TargetRelic = AllRelic.Find(*Iterator);
			if (TargetRelic&&TargetRelic->RelicRarity!=TargetRarity)
			{
				Iterator.RemoveCurrent();
			}
		}
		//应确保最低稀有度永远可靠
		FString RelicId = "";
		switch (i)
		{
			case 0:
					//第一个遗物趋同
					//RelicId = CurPool[rand()%CurPool.Num()];
					RelicId = GetRandomRelicByTagWeight(TargetRarity,CurPool,true);
				break;
			case 1:
					//第二个遗物排同
					//RelicId = CurPool[rand()%CurPool.Num()];
					RelicId = GetRandomRelicByTagWeight(TargetRarity,CurPool,false);
				break;
		default:
					//第三个之后的遗物纯随机
					RelicId = CurPool[rand()%CurPool.Num()];
				break;
		}

		if (RelicId.IsEmpty())
		{
			UE_LOG(LogTemp, Log, TEXT("Relic ResultId is Empty。Please Check the RelicPool。-GetablePoolNum:%n"),GetableRelic.Num());
		}
		
		Res.Add(RelicId,(uint8)AllRelic.Find(RelicId)->RelicRarity);
		Pool.Remove(RelicId);
	}
	
	return  Res;
}

TMap<FString, int> UAwRelicSubSystem::GetRandomItemInRoom_New(int num, int RoomLevel, ERogueRoomType RoomType)
{
	TMap<FString,int> Res;
	TArray<FString>Pool = GetableRelic;

	TMap<int,float> LevelWeight;
	//权重判断
	{
		UAwDataManager* DataManager= UGameplayFuncLib::GetAwDataManager();
		if (!DataManager)
		{
			//UE_LOG(LogTemp, Error, TEXT("DataManager is Not Vaild In RelicRandom"));
			return  Res;
		}
		TMap<FString, FAwRoomRelicDrop> RelicDropInfo = DataManager->GetRogueRoomRelicDrop();
			
		for (auto DropInfo : RelicDropInfo)
		{
			if (RoomType == DropInfo.Value.RoomType&& UKismetMathLibrary::InRange_FloatFloat(RoomLevel,DropInfo.Value.MinLevel,DropInfo.Value.MaxLevel))
			{
				for (auto Drop:DropInfo.Value.DropsRate)
				{
					LevelWeight.Add(Drop.Level,Drop.Rate);
				}
				break;
			}
		}
	}
	
	for (int i =0;i<num&&!Pool.IsEmpty();++i)
	{
		TArray<FString>CurPool = Pool;
		// TMap<int,float> CurLevelWeight = LevelWeight;

		int TargetLevel = GetRandomLevelByWeight(LevelWeight);
		//UKismetSystemLibrary::PrintString(this,"LV:"+FString::FromInt(TargetLevel));
		CheckLevelVaild(TargetLevel,Pool);
		FString RelicId = GetRandomRelicByLevel(TargetLevel,Pool,LevelWeight);
		
		if (RelicId.IsEmpty())
		{
			UE_LOG(LogTemp, Log, TEXT("Relic ResultId is Empty。Please Check the RelicPool。-GetablePoolNum:%d"),GetableRelic.Num());
			continue;
		}
		
		Res.Add(RelicId,TargetLevel);
		Pool.Remove(RelicId);
		
	}
	
	return  Res;
}

int UAwRelicSubSystem::GetRandomLevelByWeight(TMap<int, float> LevelWeight)
{
	int TargetLevel = 1;
	float TotalWeight = 0;

	LevelWeight.ValueSort([](float A,float B)
		{
			return A<B;
		}
	);
	
	for (TTuple<int, float> WeightInfo : LevelWeight)
	{
		TotalWeight += WeightInfo.Value;
	}
	
	float RandWeight = FMath::RandRange(0.0f,TotalWeight);
	
	float CurLimitValue = 0;
	for (auto It:LevelWeight)
	{
		CurLimitValue += It.Value;
		if (RandWeight<=CurLimitValue)
		{
			TargetLevel = It.Key;
			break;
		}
	}
	return TargetLevel;
}

FString UAwRelicSubSystem::GetRandomRelicByLevel(int Level, TArray<FString> Pool,TMap<int,float> LevelWeight, bool RandGotRelic, bool RandNotGotRelic)
{
	FString Result = "";
	TMap<FString,float>WeightMap;
	TMap<FString,int> GotGroupList = GetHasGotRelicGroup();

	float WeightPower =  UGameplayFuncLib::GetAwDataManager()->DebugConfig.RelicDiceWeight;
	float NewWeightPower =  UGameplayFuncLib::GetAwDataManager()->DebugConfig.NewRelicDiceWeight;
	
	for (auto PoolItem:Pool)
	{
		float TargetWeight = 1.0f;
		FAwRelicInfo* RelicInfo = AllRelic.Find(PoolItem);
		if(RelicInfo)
		{
			FString GroupId = RelicInfo->RelicGroupId;
			if (RandGotRelic&&GotGroupList.Contains(GroupId))
			{
				TargetWeight = WeightPower;
			}
			else if (RandNotGotRelic&&!GotGroupList.Contains(GroupId))
			{
				TargetWeight = NewWeightPower;
			}
		
			//不出现等级低的已有道具
			if(GotGroupList.Contains(GroupId))
			{
				TargetWeight = GotGroupList[GroupId] <= Level-1?TargetWeight:0;
			}
			auto Relic = AllRelic.Find(PoolItem);
			if (Relic->RelicLevel==Level)
			{
				WeightMap.Add(PoolItem,TargetWeight*100);
			}
			else
			{
				WeightMap.Add(PoolItem,LevelWeight[Relic->RelicLevel]);
			}
		}
	}
	//获取可以用的GroupId
	TArray<FString> Keys;
	TArray<float> Values;
	WeightMap.GenerateKeyArray(Keys);
	WeightMap.GenerateValueArray(Values);
	//权重随机
	int ResultIndex = UCommonFuncLib::AliasMethodRandom(Values,true);
	
	Result = Keys.IsValidIndex(ResultIndex)?Keys[ResultIndex]:"";
	return Result;
}

FString UAwRelicSubSystem::GetRandomRelicByGotRelic(TArray<FString> Pool,TMap<int,float> LevelWeight)
{
	FString RelicId = "";
	TMap<FString,int> GotGroupList = GetHasGotRelicGroup();
	TArray<FString> CanUseLevel02Relic;
	TArray<FString> CanUseLevel03Relic;
	for (FString RelicIdInPool : Pool)
	{
		FAwRelicInfo* RelicInfo = AllRelic.Find(RelicIdInPool);
		if(RelicInfo)
		{
			if(GotGroupList.Contains(RelicInfo->RelicGroupId))
			{
				if(RelicInfo->RelicLevel == GotGroupList[RelicInfo->RelicGroupId] + 1)
				{
					if(RelicInfo->RelicLevel == 2)
						CanUseLevel02Relic.Add(RelicIdInPool);
					else if(RelicInfo->RelicLevel == 3)
						CanUseLevel03Relic.Add(RelicIdInPool);
				}
					
			}
		}
	}
	TMap<int,float> FixedWeight = LevelWeight;
	if(FixedWeight.Contains(1))
		FixedWeight[1] = 0;
	if(FixedWeight.Contains(2) && CanUseLevel02Relic.Num() <= 0)
		FixedWeight[2] = 0;
	if(FixedWeight.Contains(3) && CanUseLevel03Relic.Num() <= 0)
		FixedWeight[3] = 0;
	int TargetLevel =  GetRandomLevelByWeight(FixedWeight);
	
	if(TargetLevel == 2 && CanUseLevel02Relic.Num())
		RelicId = CanUseLevel02Relic[FMath::RandRange(0, CanUseLevel02Relic.Num() - 1)];
	if(TargetLevel == 3 && CanUseLevel03Relic.Num())
		RelicId = CanUseLevel03Relic[FMath::RandRange(0, CanUseLevel03Relic.Num() - 1)];
	
	return RelicId;
}

TArray<FString> UAwRelicSubSystem::GetRandomItemByTag(int Num, TArray<FString> Tags)
{
	TArray<FString> Res;
	TArray<FString>Pool;
	for (auto RelicId:GetableRelic)
	{
		FAwRelicInfo* TargetRelic = AllRelic.Find(RelicId);
		if (TargetRelic)
		{
			if (UCommonFuncLib::ArrayContainsArray(TargetRelic->Tags,Tags))
			{
				Pool.Add(TargetRelic->Id);
			}
		}
	}

	for (int i =0;i<Num&&!Pool.IsEmpty();++i)
	{
		int RandomIndex = FMath::RandRange(0,Pool.Num()-1);
		if (Pool.IsValidIndex(RandomIndex))
		{
			Res.Add(Pool[RandomIndex]);
			Pool.RemoveAt(RandomIndex);
		}
	}
	return  Res;
}

ERelicRarity UAwRelicSubSystem::GetRandomRarityByRarityWeight(TMap<ERelicRarity, float> RarityWeight)
{
	//O(n) 线性权重随机
	ERelicRarity Result = ERelicRarity::Normal;
	RarityWeight.KeySort([](ERelicRarity A,ERelicRarity B)
		{
			return A<B;
		}
	);
	TArray<float> WeightArray;
	RarityWeight.GenerateValueArray(WeightArray);
	float ResultSeed = FMath::RandRange(0.f,Algo::Accumulate(WeightArray,0.f,TPlus<>()));
	
	float CurLimitValue = 0;
	for (auto It:RarityWeight)
	{
		CurLimitValue += It.Value;
		if (ResultSeed<=CurLimitValue)
		{
			Result = It.Key;
			break;
		}
	}

	
	return  Result;
}

FString UAwRelicSubSystem::GetRandomRelicByTagWeight(ERelicRarity TargetRarity,TArray<FString>Pool,bool bTagLeader)
{
	FString Result = "";
	TMap<FString,float>WeightMap;
	
	RelicTagHasGot.ValueSort(
		[=](float A,float B)
		{return A>B;}
		);

	FString HighestTag = RelicTagHasGot.IsEmpty()?"":RelicHasGot.begin()->Key;
	
	TArray<FString> VaildGroupTags = {"Group_Igni","Group_Ilm","Group_Azem","Group_Zantia","Group_Eminendanis","Group_Poltick"}; 

	float WeightPower =  UGameplayFuncLib::GetAwDataManager()->DebugConfig.RelicDiceWeight;
	float NewWeightPower =  UGameplayFuncLib::GetAwDataManager()->DebugConfig.NewRelicDiceWeight;
	bool AntiRelicDice =  UGameplayFuncLib::GetAwDataManager()->DebugConfig.AntiRelicDice;
	float AntiRelicDicePower =  UGameplayFuncLib::GetAwDataManager()->DebugConfig.AntiRelicDiceWeight;
	
	for (auto RelicId:Pool)
	{
		FAwRelicInfo* TargetRelic = AllRelic.Find(RelicId);
		float Weight = 1.f;
		if (TargetRelic&&TargetRelic->RelicRarity==TargetRarity)
		{
			bool bGroupFit = bGroupFit = UCommonFuncLib::ArrayContainsArray( TargetRelic->Tags,VaildGroupTags);  
			for (auto Tag:TargetRelic->Tags)
			{
				if (bTagLeader)
				{
					if (bGroupFit)
					{
						if (Tag !=HighestTag&&AntiRelicDice&&(!HighestTag.IsEmpty()))
						{
							Weight = AntiRelicDicePower;
						}
						else
						{
							//优先拿过的
							Weight += RelicTagHasGot.Contains(Tag)?FMath::Clamp(RelicTagHasGot[Tag]*WeightPower,0,9):0.f;
						}
					}
				}
				else
				{
					if (bGroupFit)
					{
						//优先没拿过的
						Weight += RelicTagHasGot.Contains(Tag)?0.f:NewWeightPower;
					}
				}
			}
			WeightMap.Add(TargetRelic->Id,Weight);
		}
	}


	TArray<FString> Keys;
	TArray<float> Values;
	WeightMap.GenerateKeyArray(Keys);
	WeightMap.GenerateValueArray(Values);
	//权重随机
	int ResultIndex = UCommonFuncLib::AliasMethodRandom(Values,true);

	Result = Keys.IsValidIndex(ResultIndex)?Keys[ResultIndex]:"";
	return Result;
}

TArray<FString> UAwRelicSubSystem::CheckRelicTagsGetable(TArray<FString> Tags)
{
	TSet<FString> Res;
	for (auto RelicId:GetableRelic)
	{
		FAwRelicInfo* RelicInfo = AllRelic.Find(RelicId);
		if (RelicInfo&&UCommonFuncLib::ArrayContainsArray(RelicInfo->Tags,Tags))
		{
			for (auto Tag:Tags)
			{
				if(RelicInfo->Tags.Contains(Tag))
				{
					Res.Add(Tag);
				}
			}
		}
	}
	return  Res.Array();
}

TArray<FString> UAwRelicSubSystem::GetRelicTagsHasGot()
{
	TArray<FString>Res;
	RelicTagHasGot.GenerateKeyArray(Res);
	return  Res;
}

TArray<FString> UAwRelicSubSystem::GetRandomItem(int Number)
{
	TArray<FString> Res;
	TArray<FString>Pool = GetableRelic;
	for (int i =0;i<Number&&!Pool.IsEmpty();++i)
	{
		int RandomIndex = FMath::RandRange(0,Pool.Num()-1);
		if (Pool.IsValidIndex(RandomIndex))
		{
			Res.Add(Pool[RandomIndex]);
			Pool.RemoveAt(RandomIndex);
		}
	}
	return  Res;
}

FAwRelicInfo UAwRelicSubSystem::GetRelicInfo(FString RelicId)
{
	FAwRelicInfo Result = FAwRelicInfo();
	Result = *(AllRelic.Find(RelicId));
	return  Result;
}

void UAwRelicSubSystem::RemoveRelic(FString RelicId, AAwPlayerController* Controller)
{
	FAwRelicInfo* RelicInfo = AllRelic.Find(RelicId);
	if (!RelicInfo||!RelicHasGot.Contains(RelicId))
	{
		return;
	}
	//对应Buff加负层
	AAwCharacter* PlayerCharacter = Controller->CurCharacter;
	ProcessRelicBuffs(RelicInfo->RelicEffectBuffs, PlayerCharacter, false);

	int num = *RelicHasGot.Find(RelicId);
	num--;
	//Tag获取信息变更
	for (auto RelicTag:RelicInfo->Tags)
	{
		if (RelicTagHasGot.Contains(RelicTag))
		{
			RelicTagHasGot[RelicTag] = FMath::Clamp(RelicTagHasGot[RelicTag]-1,0,RelicTagHasGot[RelicTag]-1);
		}
	}
	//已获取信息变更
	if (num<=0)
	{
		RelicHasGot.Remove(RelicId);
		if (RelicHasGotInFormalPool.Contains(RelicId))
		{
			RelicHasGotInFormalPool.Remove(RelicId);
		}
	}
	else
	{
		if (RelicHasGotInFormalPool.Contains(RelicId))
		{
			RelicHasGotInFormalPool.Add(RelicId,num);
		}
		RelicHasGot.Add(RelicId,num);
	}
	if (OnPlayerLoseRelic.IsBound())
	{
		OnPlayerLoseRelic.Broadcast(*AllRelic.Find(RelicId));
	}
}

void UAwRelicSubSystem::RemoveAllSameIdRelic(FString RelicId, AAwPlayerController* Controller)
{
	FAwRelicInfo* RelicInfo = AllRelic.Find(RelicId);
	if (!RelicInfo||!RelicHasGot.Contains(RelicId))
	{
		return;
	}
	int num = *RelicHasGot.Find(RelicId);
	for (int i =0;i<num;i++)
	{
		RemoveRelic(RelicId, Controller);
	}
}

void UAwRelicSubSystem::ClearRelic()
{
	//清空已获取遗物
	RelicHasGot.Empty();
	//清空已获取的正式遗物
	RelicHasGotInFormalPool.Empty();
	//清空获取的Tag分组记录
	RelicTagHasGot.Empty();
	//重置未获取遗物
	AllRelic.GenerateKeyArray(RelicNotGet);
	//重置GetableRelic
	CheckGetableRelic();
	//移除实际效果Buff
	for (auto PC : UGameplayFuncLib::GetAllLocalAwPlayerControllers())
	{
		if (!PC || !PC->CurCharacter)
		{
			return;
		}
		PC->CurCharacter->RemoveBuffByTag("Relic");
	}
	if (OnPlayerClearRelic.IsBound())
	{
		OnPlayerClearRelic.Broadcast();
	}
}

void UAwRelicSubSystem::ClearRelicEffect()
{
	for (auto PC : UGameplayFuncLib::GetAllLocalAwPlayerControllers())
	{
		if (!PC || !PC->CurCharacter)
		{
			return;
		}
		PC->CurCharacter->RemoveBuffByTag("Relic");
	}
}

void UAwRelicSubSystem::GivePlayerRelic(FString RelicId, AAwPlayerController* Controller,bool InPool)
{
	AAwCharacter* PlayerCharacter = Controller->CurCharacter;
	if (!IsValid(PlayerCharacter))
	{
		return;
	}
	FAwRelicInfo* RelicInfo = nullptr;
	if ((GetableRelic.Contains(RelicId) ||InPool==false) &&AllRelic.Contains(RelicId))
	{
		int num  = 1;
		RelicInfo = AllRelic.Find(RelicId);
		//移除玩家身上同效果低等级的圣遗物
		for (FString CurGroupRelicId : GetRelicByGroupId(RelicInfo->RelicGroupId))
		{
			if(CurGroupRelicId != RelicId && RelicHasGot.Contains(CurGroupRelicId))
			{
				RemoveRelic(CurGroupRelicId,Controller);
				//UKismetSystemLibrary::PrintString(this,CurGroupRelicId,true,true,FColor::Red);
			}
		}
		//已经获取的遗物信息修改
		if (RelicHasGot.Contains(RelicId))
		{
			if (num>=RelicInfo->MaxNum)
			{
				GetableRelic.Remove(RelicId);
				return;
			}
			num = *RelicHasGot.Find(RelicId)+1;
			RelicHasGot.Add(RelicId,num);
			if (!RelicInfo->Tags.Contains("NotInFormal"))
			{
				RelicHasGotInFormalPool.Add(RelicId,num);
			}
		}
		else
		{
			RelicHasGot.Add(RelicId,1);
			if (!RelicInfo->Tags.Contains("NotInFormal"))
			{
				RelicHasGotInFormalPool.Add(RelicId,1);
			}
		}
		
		//检查能获取的遗物是否要发生改变
		CheckGetableRelic();
		
		for (auto RelicTag:RelicInfo->Tags)
		{
			if (!RelicTag.Contains("Group_"))
			{
				continue;
			}
			if (RelicTagHasGot.Contains(RelicTag))
			{
				RelicTagHasGot.Add(RelicTag,RelicTagHasGot[RelicTag]+1);
			}
			else
			{
				RelicTagHasGot.Add(RelicTag,1);
			}
		}
		//获取配置的Buff效果信息
		ProcessRelicBuffs(RelicInfo->RelicEffectBuffs, PlayerCharacter, true);


		RelicNotGet.Remove(RelicId);
		if (AllRelic.Contains(RelicId))
		{
			if (OnPlayerGetRelic.IsBound())
			{
				OnPlayerGetRelic.Broadcast(AllRelic[RelicId]);
			}
		}
	}
	
}

void UAwRelicSubSystem::GivePlayerRelic_Multi(FString RelicId, bool InPool)
{
	auto PCs = UGameplayFuncLib::GetAllLocalAwPlayerControllers();
	for (auto PC:PCs)
	{
		if (PC && PC->CurCharacter)
		{
			if (PC->GetLocalPCIndex()==0)
			{
				GivePlayerRelic(RelicId,PC,InPool);
			}
			else
			{
				auto RelicInfo = AllRelic.Find(RelicId);
				ProcessRelicBuffs(RelicInfo->RelicEffectBuffs, PC->CurCharacter, true);
			}
		}
	}
}

void UAwRelicSubSystem::ReGetRelic()
{
	TMap<FString,int> OldRelics = RelicHasGot;
	ClearRelic();
	IsReGet = true;
	for (auto Relic:OldRelics)
	{
		 for (int i=0;i<Relic.Value;i++)
		 {
			 // GivePlayerRelic(Relic.Key,Controller,false);
			 GivePlayerRelic_Multi(Relic.Key,false);
		 }
	}
	IsReGet = false;
}

TArray<FString> UAwRelicSubSystem::GetRelicByGroupId(FString GroupId)
{
	TArray<FString> Res;
	for (TTuple<FString, FAwRelicInfo> Relic : AllRelic)
	{
		if(Relic.Value.RelicGroupId == GroupId)
			Res.Add(Relic.Key);
	}
	return Res;
}

TMap<FString, int> UAwRelicSubSystem::GetHasGotRelicGroup()
{
	TMap<FString, int> Res;
	for (TTuple<FString, int> HasGot : RelicHasGot)
	{
		FAwRelicInfo RelicInfo = GetRelicInfo(HasGot.Key);
		if(RelicInfo.RelicGroupId != "")
		{
			if (Res.Contains(RelicInfo.RelicGroupId))
			{
				Res.Add(RelicInfo.RelicGroupId, FMath::Max(RelicInfo.RelicLevel,Res[RelicInfo.RelicGroupId]));
			}
			else
			{
				Res.Add(RelicInfo.RelicGroupId, RelicInfo.RelicLevel);
			}
		}
	}
	return Res;
}

TMap<FString,int> UAwRelicSubSystem::GetNotGotRelicGroup()
{
	TMap<FString, int> Res;
	TMap<FString, int> HasGotGroups = GetHasGotRelicGroup();
	for (FString RelicId : GetableRelic)
	{
		FAwRelicInfo RelicInfo = GetRelicInfo(RelicId);
		if(!HasGotGroups.Contains(RelicInfo.RelicGroupId))
		{
			if(Res.Contains(RelicInfo.RelicGroupId))
			{
				if(Res[RelicInfo.RelicGroupId] < RelicInfo.RelicLevel)
					Res.Add(RelicInfo.RelicGroupId, RelicInfo.RelicLevel);
			}
			else
				Res.Add(RelicInfo.RelicGroupId, RelicInfo.RelicLevel);
		}
	}
	return Res;
}

TMap<FString, int> UAwRelicSubSystem::GetRelicHasGotOnUI()
{
	TMap<FString, int> Res;
	for (TTuple<FString, int> Relic : this->RelicHasGotInFormalPool)
	{
		if(!Relic.Key.Contains("Apple") && !Relic.Key.Contains("Pray"))
			Res.Add(Relic);
	}
	return Res;
}

TMap<FString,int> UAwRelicSubSystem::GetRelicHasGotAfterSortByGroup(FString GroupTag,bool InPool)
{
	TMap<FString,int> Res;
	for (auto Relic:RelicHasGot)
	{
		if (!AllRelic.Contains(Relic.Key))
		{
			//保险 理论上永远不进
			continue;
		}
		FAwRelicInfo RelicInfo = AllRelic[Relic.Key];
		bool PoolCheck =InPool?GetRelicHasGotOnUI().Contains(RelicInfo.Id):true;
		if (RelicInfo.Tags.Contains("NotInPool")&&InPool)
		{
			PoolCheck = false;
		}
		if (RelicInfo.Tags.Contains(GroupTag)&&PoolCheck)
		{
			Res.Add(Relic.Key,Relic.Value);
		}
	}
	//稀有度从高到低  排序
	Res.KeySort(
	[=](FString RelicA,FString RelicB)
		{
			return AllRelic.Find(RelicA)->RelicRarity>AllRelic.Find(RelicB)->RelicRarity;
		}
	);
	return Res;
}

TArray<FString> UAwRelicSubSystem::GetAllRelicAfterSortByGroup(FString GroupTag,bool InPool)
{
	TArray<FString>Res = GetAllRelicByGroup(GroupTag,InPool);
	
	//稀有度从高到低  排序
	Res.Sort(
	[=](FString RelicA,FString RelicB)
		{
			return AllRelic.Find(RelicA)->RelicRarity>AllRelic.Find(RelicB)->RelicRarity;
		}
	);
	return Res;
}

TArray<FString> UAwRelicSubSystem::GetAllRelicAfterIdSortByGroup(FString GroupTag, bool InPool)
{
	TArray<FString>Res = GetAllRelicByGroup(GroupTag,InPool);
	//稀有度从高到低  排序
	Res.Sort(
	[=](FString RelicA,FString RelicB)
		{
			FString Id_A = AllRelic.Find(RelicA)->RecordId;
			FString Id_B = AllRelic.Find(RelicB)->RecordId;
			int A =Id_A.IsNumeric()?FCString::Atoi(*Id_A):0;
			int B = Id_B.IsNumeric()?FCString::Atoi(*Id_B):0;
			return A<B;
		}
	);
	return Res;
}

TArray<FString> UAwRelicSubSystem::GetAllRelicByGroup(FString GroupTag, bool InPool)
{
	TArray<FString>Res;

	for (auto Relic:AllRelic)
	{
		FAwRelicInfo RelicInfo = AllRelic[Relic.Key];
		if (InPool)
		{
			if(Relic.Value.Tags.Contains("NotInPool"))
			{
				continue;
			}
		}
		
		if (RelicInfo.Tags.Contains(GroupTag))
		{
			Res.Add(Relic.Key);
		}
	}
	return Res;
}

int UAwRelicSubSystem::GetRelicNumInRecordByGroup(FString GroupTag, bool InPool)
{
	int Res =0;
	UAwRogueDataSystem* System = GetGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	TArray<FString> Relics = GetAllRelicByGroup(GroupTag,InPool);
	if (System)
	{
		for (auto Relic:Relics)
		{
			if (System->GetRelicHasGetRecord(Relic)>0)
			{
				++Res;
			}
		}
	}
	return Res;
}

TArray<FString> UAwRelicSubSystem::GetRelicUnlockRewards(TArray<FString> PoolGroups, int RewardsNum)
{
	TArray<FString>Res = TArray<FString>();
	TArray<FString>RewardPool = TArray<FString>();
	for (auto Temp:PoolGroups)
	{
		bool bFit = CheckRelicGroupAllUnlock(Temp);
		if (bFit)
		{
			RewardPool.Add(Temp);
		}
	}
	int RandomNum = FMath::Clamp(RewardsNum,0,RewardPool.Num());

	for (int i =0;i<RandomNum;++i)
	{
		int index =rand()%RewardPool.Num();
		Res.Add(RewardPool[index]);
		RewardPool.RemoveAt(index);
	}
	return Res;
}

bool UAwRelicSubSystem::CheckRelicGroupAllUnlock(FString Group)
{
	bool bFit = GetAllRelicByGroup(Group,true).Num() >0?GetAllRelicByGroup(Group,true).Num() == GetRelicNumInRecordByGroup(Group,true):false;
	return bFit;
}

void UAwRelicSubSystem::GivePlayerInitialRelic(AAwPlayerController* Controller, bool InPool)
{
	for (auto RelicId:InitialRelics)
	{
		GivePlayerRelic(RelicId,Controller,InPool);
	}
}

void UAwRelicSubSystem::SetInitialRelics(TArray<FString> Relics)
{
	InitialRelics = TSet<FString>(Relics);
}

TArray<FString> UAwRelicSubSystem::GetInitialRelics()
{
	return InitialRelics.Array();
}

TArray<FRelicPreviewResult> UAwRelicSubSystem::OnRelicPreview(FString RelicId,AAwPlayerController* PlayerController)
{
	TArray<FRelicPreviewResult>Result;
	if (AllRelic.Contains(RelicId))
	{
		FAwRelicInfo RelicInfo = AllRelic[RelicId];
		//通过脚本函数确定 遗物效果预览最终影响  结果key值Cur 为当前 After 为预览后
		for (auto Event:RelicInfo.OnRelicPreview)
		{
			UFunction* Func;
			Func = UCallFuncLib::GetUFunction(Event.ClassPath, Event.FunctionName);
			if (Func)
			{
				struct
				{
					FAwRelicInfo RelicInfo;
					TArray<FString> Params;
					AAwPlayerController* Controller;
					FRelicPreviewResult Result;
				}FuncParam;
				FuncParam.RelicInfo = RelicInfo;
				FuncParam.Params = Event.Params;
				FuncParam.Controller = PlayerController;
				this->ProcessEvent(Func, &FuncParam);

				if (FuncParam.Result.IsResultValid())
				{
					Result.Add(FuncParam.Result);
				}
				
				if (FuncParam.Result.TimelineNode&&UGameplayFuncLib::GetAwGameInstance())
				{
					UGameplayFuncLib::GetAwGameInstance() ->TimelineManager->AddNode(FuncParam.Result.TimelineNode);
				}
			}
		}
	}
	
	return  Result;
}

void UAwRelicSubSystem::CheckGetableRelic()
{
	GetableRelic.Empty();
	TArray<FString> HasGotIds;
	RelicHasGot.GenerateKeyArray(HasGotIds);

	TMap<FString,int> HasGotGroup = GetHasGotRelicGroup();
	
	//肉鸽动作系统
	URogueBattleUpgradeSubSystem* BattleSubSystem = nullptr;
	if (GetGameInstance())
	{
		BattleSubSystem = GetGameInstance()->GetSubsystem<URogueBattleUpgradeSubSystem>();
	}
	TArray<FString>BattleTag;
	if (BattleSubSystem)
	{
		for (auto Tag:URogueBattleUpgradeSubSystem::GetCurClassTags())
		{
			BattleTag.Add(UDataFuncLib::EnumToFString(Tag.Key));
		}
	}
	
	
	//遗物过滤
	for(auto Relic:AllRelic)
	{
		bool bFitPreNode = true ;
		//bool bFitPreNode = UCommonFuncLib::ArrayContainsArray(Relic.Value.PreNodeIds,HasGotIds,Relic.Value.PreNodeLogic);
		int HasGotNum = RelicHasGot.Contains(Relic.Value.Id)?RelicHasGot[Relic.Value.Id]:0;
		bool bGotMax = HasGotNum>= Relic.Value.MaxNum;

		//限定区分
		if (!bFitPreNode||bGotMax||Relic.Value.Tags.Contains("NotInPool"))
		{
			continue;
		}
		//动作词条过滤
		
		bool bFitBattleTag = true;
		for (auto RelicTag:Relic.Value.Tags)
		{
			if(RelicTag.Contains("BattleState_"))
			{
				FString CurBattleTag;
				RelicTag.Split("BattleState_",nullptr,&CurBattleTag);
				bFitBattleTag =BattleTag.Contains(CurBattleTag);
				//有一个战斗tag不满足 那么这组Tag就不满足 退出内循环
				if (!bFitBattleTag)
				{
					break;
				}
			}
		}
		//有战斗tag 且不满足当前 激活的战斗tag则跳过 加入可获取列表
		if (!bFitBattleTag)
		{
			continue;
		}
		//同GroupId但Level更低的去除
		if(HasGotGroup.Contains(Relic.Value.RelicGroupId))
		{
			if(Relic.Value.RelicLevel <= HasGotGroup[Relic.Value.RelicGroupId])
				continue;
		}
		
		GetableRelic.Add(Relic.Value.Id);
	}
}

bool UAwRelicSubSystem::CheckRarityVaild(ERelicRarity& TargetRarity, TArray<FString> Pool)
{
	bool result = false;
	bool bFinish = false;

	if (Pool.IsEmpty())
	{
		return  false;
	}
	
	while (!bFinish&&TargetRarity!=ERelicRarity::Normal)
	{
		for (auto Target:Pool)
		{
			FAwRelicInfo* RelicInfo = AllRelic.Find(Target);
			if (RelicInfo)
			{
				if(RelicInfo->RelicRarity == TargetRarity)
				{
					bFinish = true;
					result = true;
					break;
				}
			}
		}
		if (!result)
		{
			switch (TargetRarity)
			{
			case ERelicRarity::Legend:
				TargetRarity = ERelicRarity::Epic;
				break;
			case ERelicRarity::Epic:
				TargetRarity = ERelicRarity::Rarely;
				break;
			default:
					TargetRarity = ERelicRarity::Normal;
				break;
			}
		}
		if (TargetRarity == ERelicRarity::Normal)
		{
			break;
		}
	}
	return  result;
}

bool UAwRelicSubSystem::CheckLevelVaild(int& TargetLevel, TArray<FString> Pool)
{
	bool result = false;
	bool bFinish = false;

	if (Pool.IsEmpty())
	{
		return  false;
	}
	
	while (!bFinish&&TargetLevel!=1)
	{
		for (auto Target:Pool)
		{
			FAwRelicInfo* RelicInfo = AllRelic.Find(Target);
			if (RelicInfo)
			{
				if(RelicInfo->RelicLevel == TargetLevel)
				{
					bFinish = true;
					result = true;
					break;
				}
			}
		}
		if (!result)
		{
			TargetLevel--;
		}
		if (TargetLevel == 1)
		{
			break;
		}
	}
	return  result;
}

void UAwRelicSubSystem::ProcessRelicBuffs(const TArray<FString>& RelicEffectBuffs, AAwCharacter* PlayerCharacter, bool IsAdd)
{
	UBuffManager::ProcessBuffs(RelicEffectBuffs,PlayerCharacter,"Relic",IsAdd);
}


