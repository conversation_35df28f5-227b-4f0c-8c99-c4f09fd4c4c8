// Fill out your copyright notice in the Description page of Project Settings.


#include "AwRogueTalentSubSystem.h"

#include "Algo/Accumulate.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GameFramework/Manager/AwRogueDataSystem.h"


FAwRogueTalentInfo FAwRogueTalentInfo::FromJson(TSharedPtr<FJsonObject> JsonObj)
{
	FAwRogueTalentInfo TargetMetaData = FAwRogueTalentInfo();
	TargetMetaData.Id = UDataFuncLib::AwGetStringField(JsonObj,"Id");
	TargetMetaData.Desc = UDataFuncLib::AwGetStringField(JsonObj,"Desc");
	TargetMetaData.Tags = UDataFuncLib::AwGetStringArrayField(JsonObj,"Tags");
	TargetMetaData.CostSourceId = UDataFuncLib::AwGetStringField(JsonObj,"CostSourceId","");
	TargetMetaData.ShowAfterSwitch = UDataFuncLib::AwGetBoolField(JsonObj,"ShowAfterSwitch",false);
	for (auto CostString: UDataFuncLib::AwGetStringArrayField(JsonObj,"Costs"))
	{
		TargetMetaData.Costs.Add(CostString.IsNumeric()?FCString::Atoi(*CostString):0);
	}
	TargetMetaData.CurLevel = 0;
	TargetMetaData.MaxLevel = TargetMetaData.Costs.Num();
	
	TargetMetaData.TalentEffectBuffs  = UDataFuncLib::AwGetStringArrayField(JsonObj,"EffectBuff");

	return  TargetMetaData;
}

void UAwRogueTalentSubSystem::InitSubSystem()
{
	AllTalent = UGameplayFuncLib::GetAwDataManager()->GetAllMetaRogueTalents();
	 CheckTalentShow();
}

void UAwRogueTalentSubSystem::SaveData()
{
	if (!GetGameInstance())
	{
		return;
	}
	UAwRogueDataSystem* SubSystem = GetGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	if (!SubSystem)
	{
		return;
	}
	for (auto UnlockTalentData:UnlockTalent)
	{
		SubSystem->SetUnlockTalent(UnlockTalentData.Key,UnlockTalentData.Value);
	}
}

void UAwRogueTalentSubSystem::LoadSaveData()
{
	if (!GetGameInstance())
	{
		return;
	}
	UAwRogueDataSystem* SubSystem = GetGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	if (!SubSystem)
	{
		return;
	}
	InitSubSystem();
	ClearALlPlayerTalent();
	for (auto SaveData:SubSystem->GetAllUnlockTalent())
	{
		for (int i =0;i<SaveData.Value;i++)
		{
			ReUnlockTalentNoCost(SaveData.Key);
		}
	}
}

void UAwRogueTalentSubSystem::CheckTalentShow()
{
	ShowTalents.Empty();
	UAwRogueDataSystem* SubSystem = GetGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	for (auto Talent:AllTalent)
	{
		if (SubSystem&&Talent.Value.ShowAfterSwitch)
		{
			if (SubSystem->GetIsClearedGame())
			{
				ShowTalents.Add(Talent.Key);
			}
		}
		else 
		{
			ShowTalents.Add(Talent.Key);
		}
	}
}

void UAwRogueTalentSubSystem::TalentBuffOnGameStart(AAwPlayerController* Controller)
{
	AAwCharacter* PlayerCharacter = Controller->CurCharacter;
	if (!PlayerCharacter)
	{
		return;
	}
	for (int bi = 0; bi < PlayerCharacter->CharacterObj.Buff.Num(); bi++)
	{
		if (PlayerCharacter->CharacterObj.Buff[bi].Model.OnRogueGameStart.Num())
		{
			for (int i = 0; i <PlayerCharacter->CharacterObj.Buff[bi].Model.OnRogueGameStart.Num(); i++)
			{
				UFunction* Func = UCallFuncLib::JsonFuncToUFunc(PlayerCharacter->CharacterObj.Buff[bi].Model.OnRogueGameStart[i]);
				if (IsValid(Func) == false) continue;;
						
				struct {
					FBuffObj BuffObj;
					int32 WasStack;
					TArray<FString> Params;
					FBuffRunResult Result;
				} FuncParam;
						
				FuncParam.BuffObj =PlayerCharacter->CharacterObj.Buff[bi];
				FuncParam.WasStack = PlayerCharacter->CharacterObj.Buff[bi].Stack;
				FuncParam.Params = PlayerCharacter->CharacterObj.Buff[bi].Model.OnRogueGameStart[i].Params;
			
				PlayerCharacter->ProcessEvent(Func, &FuncParam);
				PlayerCharacter->CharacterObj.Buff[bi] = FuncParam.Result.BuffObj;
				if (FuncParam.Result.TimelineNode)
					UGameplayFuncLib::GetTimelineManager()->AddNode(FuncParam.Result.TimelineNode);
			}
		}
	}
}

void UAwRogueTalentSubSystem::SetChooseTalent(TArray<FString> ChossedIds)
{
	ChoosedTalents = ChossedIds;
}

void UAwRogueTalentSubSystem::ClearChooseTalent()
{
	ChoosedTalents.Empty();
}

void UAwRogueTalentSubSystem::ReGetTalentEffect(AAwPlayerController* Controller)
{
	AAwCharacter* PlayerCharacter = Controller->CurCharacter;
	if (!PlayerCharacter)
	{
		return;
	}
	PlayerCharacter->RemoveBuffByTag("Talent");

	for (auto TargetID : UnlockTalent)
	{
		FAwRogueTalentInfo* TargetInfo =AllTalent.Find(TargetID.Key);
		if (!TargetInfo)
		{
			continue;
		}
		//实际添加效果
		for (auto BuffId : TargetInfo->TalentEffectBuffs)
		{
			int BuffStack = 1;
			if (BuffId.Contains("(")&&BuffId.Contains(")"))
			{
				FString Left,Right;
				FString StackString = UDataFuncLib::SplitParamBetweenSplitChar(BuffId,"(",")",Left,Right);
				BuffStack = StackString.IsNumeric()?FCString::Atoi(*StackString)*TargetInfo->CurLevel:BuffStack*TargetInfo->CurLevel;
				FString RightString;
				BuffId.Split("(",&BuffId ,&RightString);
			}
			FBuffModel BuffModel =UGameplayFuncLib::GetAwDataManager()->GetBuffModelById(BuffId);
			if (!BuffModel.Tags.Contains("Talent"))
			{
				BuffModel.Tags.Add("Talent");
			}
			if (BuffModel.ValidBuffModel() == false) return ;
			{
				PlayerCharacter->AddBuff(FAddBuffInfo(PlayerCharacter, PlayerCharacter, BuffModel, BuffStack, 0, false, true));
			}
		}
	}
}

bool UAwRogueTalentSubSystem::TryUnlockTalent(FString Id)
{
	if (!AllTalent.Contains(Id))
	{
		return  false;
	}
	
	FAwRogueTalentInfo* TargetInfo = AllTalent.Find(Id);
	if (TargetInfo->CurLevel +1 >TargetInfo->MaxLevel)
	{
		return  false;
	}

	int Cost = TargetInfo->Costs.IsValidIndex(TargetInfo->CurLevel)?TargetInfo->Costs[TargetInfo->CurLevel]:0;
	
	if (!CheckCost(TargetInfo->CostSourceId,Cost))
	{
		return  false;
	}

	// To do 扣除对应cost
	ReduceSourceByCost(TargetInfo->CostSourceId,Cost);

	TargetInfo->CurLevel = TargetInfo->CurLevel +1;
	if (!UnlockTalent.Contains(Id))
	{
		UnlockTalent.Add(Id,1);
	}
	else
	{
		UnlockTalent[Id]+=1;
	}
	return  true;
}

bool UAwRogueTalentSubSystem::TryUnlockTalentAndGetEffect(FString Id,AAwPlayerController* Controller)
{
	if (!AllTalent.Contains(Id))
	{
		return  false;
	}
	
	FAwRogueTalentInfo* TargetInfo = AllTalent.Find(Id);
	if (TargetInfo->CurLevel +1 >TargetInfo->MaxLevel)
	{
		return  false;
	}

	int Cost = TargetInfo->Costs.IsValidIndex(TargetInfo->CurLevel)?TargetInfo->Costs[TargetInfo->CurLevel]:0;
	
	if (!CheckCost(TargetInfo->CostSourceId,Cost))
	{
		return  false;
	}

	// To do 扣除对应cost
	ReduceSourceByCost(TargetInfo->CostSourceId, Cost);
	//实际添加效果
	for (auto BuffId : TargetInfo->TalentEffectBuffs)
	{
		int BuffStack = 1;
		if (BuffId.Contains("(")&&BuffId.Contains(")"))
		{
			FString Left,Right;
			FString StackString = UDataFuncLib::SplitParamBetweenSplitChar(BuffId,"(",")",Left,Right);
			BuffStack = StackString.IsNumeric()?FCString::Atoi(*StackString):BuffStack;
			FString RightString;
			BuffId.Split("(",&BuffId ,&RightString);
		}
		FBuffModel BuffModel =UGameplayFuncLib::GetAwDataManager()->GetBuffModelById(BuffId);
		if (!BuffModel.Tags.Contains("Talent"))
		{
			BuffModel.Tags.Add("Talent");
		}
		if (BuffModel.ValidBuffModel() == false) return false ;
		{
			AAwCharacter* PlayerCharacter = Controller->CurCharacter;
			PlayerCharacter->AddBuff(FAddBuffInfo(PlayerCharacter, PlayerCharacter, BuffModel, BuffStack, 0, false, true));
		}
	}
	TargetInfo->CurLevel = TargetInfo->CurLevel +1;
	if (!UnlockTalent.Contains(Id))
	{
		UnlockTalent.Add(Id,1);
	}
	else
	{
		UnlockTalent[Id]+=1;
	}
	return  true;
}

void UAwRogueTalentSubSystem::ResetTalent(AAwPlayerController* Controller)
{
	AAwCharacter* PlayerCharacter = Controller->CurCharacter;
	PlayerCharacter->RemoveBuffByTag("Talent");
	ClearChooseTalent();
	UAwRogueDataSystem* SubSystem = GetGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	if (!SubSystem)
	{
		return ;
	}

	FString SourceId = "";
	for (auto TalentId:UnlockTalent)
	{
		int Cost = 0;
		 FAwRogueTalentInfo* Info =  AllTalent.Find(TalentId.Key);
		 if (Info)
		 {
		 	SourceId = Info->CostSourceId;
		    if (Info->Costs.IsValidIndex(Info->CurLevel - 1))
		    {
			    for (int i =0;i<Info->CurLevel;++i)
			    {
			    	Cost+= Info->Costs[i] ;
			    }
		    	int OldNum = SubSystem->GetCurrencyCount(SourceId);
		    	SubSystem->SetCurrencyCount(OldNum+Cost,SourceId);
		    }
		 }
		Info->CurLevel = 0;
	}

	UnlockTalent.Empty();
}

bool UAwRogueTalentSubSystem::CheckCost(FString SourceId, int Cost)
{
	UAwGameInstance* GameInstance =  UGameplayFuncLib::GetAwGameInstance();
	if (!GameInstance)
	{
		return  false;
	}
	UAwRogueDataSystem* SubSystem = GetGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	if (!SubSystem)
	{
		return false;
	}
	
	//匹配玩家 对应资源持有量>
	return  Cost<=SubSystem->GetCurrencyCount(SourceId, false);
}

void UAwRogueTalentSubSystem::ReduceSourceByCost(FString SourceId, int Cost)
{
	UAwRogueDataSystem* SubSystem = GetGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	if (!SubSystem)
	{
		return ;
	}
	SubSystem->AddCurrencyCount(-1 * Cost, SourceId, false);
}

void UAwRogueTalentSubSystem::ClearALlPlayerTalent()
{
	for (auto PC : UGameplayFuncLib::GetAllLocalAwPlayerControllers())
	{
		if (!PC)continue;
		ClearTalentEffect(PC);
	}
	ClearChooseTalent();
	UnlockTalent.Empty();
}

void UAwRogueTalentSubSystem::ClearTalentEffect(AAwPlayerController* Controller)
{
	AAwCharacter* PlayerCharacter = nullptr;
	if (Controller)
	{
		PlayerCharacter = Controller->CurCharacter;
	}
	if (PlayerCharacter)
	{
		PlayerCharacter->RemoveBuffByTag("Talent");
	}
}

void UAwRogueTalentSubSystem::ReUnlockTalentNoCost(FString Id)
{
	if (!AllTalent.Contains(Id))
	{
		return  ;
	}
	
	FAwRogueTalentInfo* TargetInfo = AllTalent.Find(Id);
	if (TargetInfo->CurLevel +1 >TargetInfo->MaxLevel)
	{
		return  ;
	}
	
	TargetInfo->CurLevel = TargetInfo->CurLevel +1;
	if (!UnlockTalent.Contains(Id))
	{
		UnlockTalent.Add(Id,1);
	}
	else
	{
		UnlockTalent[Id]+=1;
	}
	return  ;
}

