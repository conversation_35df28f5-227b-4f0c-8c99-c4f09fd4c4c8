// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Subsystems/GameInstanceSubsystem.h"
#include "AwRogueTalentSubSystem.generated.h"

USTRUCT(BlueprintType)
struct FAwRogueTalentInfo
{
	GENERATED_BODY()
public:
	UPROPERTY(BlueprintReadOnly,EditAnywhere,Category="RogueTalent")
		FString Id;
	UPROPERTY(BlueprintReadOnly,EditAnywhere,Category="RogueTalent")
		FString Desc;
	UPROPERTY(BlueprintReadOnly,EditAnywhere,Category="RogueTalent")
		TArray<FString> Tags;
	UPROPERTY(BlueprintReadOnly,EditAnywhere,Category="RogueTalent")
		FString CostSourceId;
	UPROPERTY(BlueprintReadOnly,EditAnywhere,Category="RogueTalent")
		TArray<int> Costs;
	UPROPERTY(BlueprintReadOnly,EditAnywhere,Category="RogueTalent")
		int CurLevel = 0;
	UPROPERTY(BlueprintReadOnly,EditAnywhere,Category="RogueTalent")
		int MaxLevel = 0;
	UPROPERTY(EditAnywhere,BlueprintReadWrite, Category = "RogueTalent")
		bool ShowAfterSwitch = false;
	UPROPERTY(EditAnywhere,BlueprintReadWrite, Category = "RogueTalent")
		TArray<FString> TalentEffectBuffs;

public:
	static FAwRogueTalentInfo FromJson(TSharedPtr<FJsonObject> JsonObj);
};

UCLASS()
class THEAWAKENER_FO_API UAwRogueTalentSubSystem: public UGameInstanceSubsystem
{
	GENERATED_BODY()
public:
	void InitSubSystem();
	//存读档
	void SaveData();
	void LoadSaveData();
	void TalentBuffOnGameStart(AAwPlayerController* Controller);
public:

	//全部的天赋
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Talent")
	TMap<FString,FAwRogueTalentInfo> AllTalent;
	//显示可以解锁的天赋
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Talent")
	TArray<FString> ShowTalents;
	//解锁的天赋
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Talent")
	TMap<FString,int>UnlockTalent;
	//解锁并选择携带的天赋 暂时弃用
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Talent")
	TArray<FString> ChoosedTalents;
	//最大携带数量 暂时弃用
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Talent")
	int MaxNum = 3;

	//设置当前选择
	UFUNCTION(BlueprintCallable)
	void SetChooseTalent(TArray<FString> ChossedIds );
	//清空选择
	UFUNCTION(BlueprintCallable)
	void ClearChooseTalent( );
	
	//根据当前的选择天赋重新给一遍天赋效果
	UFUNCTION(BlueprintCallable)
	void ReGetTalentEffect(AAwPlayerController* Controller);
	//尝试解锁天赋
	UFUNCTION(BlueprintCallable)
	bool TryUnlockTalent(FString Id);

	//尝试解锁天赋并获取效果
	UFUNCTION(BlueprintCallable)
	bool TryUnlockTalentAndGetEffect(FString Id,AAwPlayerController* Controller);
	
	//重置天赋
	UFUNCTION(BlueprintCallable)
	void ResetTalent(AAwPlayerController* Controller);
	//检查天赋cost条件
	UFUNCTION(BlueprintCallable)
	bool CheckCost(FString SourceId,int Cost);

	//检测哪些要显示 那些先隐藏
	UFUNCTION(BlueprintCallable)
	void CheckTalentShow();
	//消耗天赋cost
	UFUNCTION(BlueprintCallable)
	void ReduceSourceByCost(FString SourceId,int Cost);

	UFUNCTION(BlueprintCallable)
	void ClearALlPlayerTalent();
	UFUNCTION(BlueprintCallable)
	void ClearTalentEffect(AAwPlayerController* Controller);
private:
	void ReUnlockTalentNoCost(FString Id);
	
};



