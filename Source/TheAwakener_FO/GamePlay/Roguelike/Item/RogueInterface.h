// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "UObject/Interface.h"
#include "RogueInterface.generated.h"

/**
 * 
 */


UINTERFACE(MinimalAPI)
class URogueInterface : public UInterface
{
	GENERATED_BODY()
};

class THEAWAKENER_FO_API IRogueInterface
{
	GENERATED_BODY()

		// Add interface functions to this class. This is the class that will be inherited to implement this interface.
public:
	UFUNCTION(BlueprintCallable,BlueprintNativeEvent)
	void GiveInfoMap(const TMap<FString,FString>& InfoMap);

	UFUNCTION(BlueprintCallable,BlueprintNativeEvent)
	void SetRogueActive(const bool Active);
};

UINTERFACE(MinimalAPI)
class URogueCreaterControllerInterface : public UInterface
{
	GENERATED_BODY()
};

class THEAWAKENER_FO_API IRogueCreaterControllerInterface
{
	GENERATED_BODY()

	// Add interface functions to this class. This is the class that will be inherited to implement this interface.
	public:
	UFUNCTION(BlueprintCallable,BlueprintNativeEvent)
	TArray<AActor*>  GetRealCreatersByInfos (const TMap<FString,FString>& InfoMap);

	UFUNCTION(BlueprintCallable,BlueprintNativeEvent)
	void  SetControllerInfo ( AAwCharacter* CreaterOwner,const TMap<FString,FString>& InfoMap);
};