// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/Interface.h"
#include "RogueItemInterface.generated.h"

/**
 * 
 */

//暂时弃用 用RogueInterface 替代
UINTERFACE(MinimalAPI)
class URogueItemInterface : public UInterface
{
	GENERATED_BODY()
};
//暂时弃用 用RogueInterface 替代
class THEAWAKENER_FO_API IRogueItemInterface
{
	GENERATED_BODY()

		// Add interface functions to this class. This is the class that will be inherited to implement this interface.
public:
	UFUNCTION(BlueprintCallable,BlueprintNativeEvent)
	void GiveInfoMap(const TMap<FString,FString>& InfoMap);
	
};

