// Fill out your copyright notice in the Description page of Project Settings.


#include "AwRogueItemSubSystem.h"

#include "RogueInterface.h"
#include "Algo/Accumulate.h"
#include "Kismet/KismetArrayLibrary.h"
#include "Kismet/KismetMathLibrary.h"
#include "TheAwakener_FO/FunctionLibrary/CommonFuncLib.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GameFramework/Manager/AwRogueDataSystem.h"




FAwRogueItemPropModify FAwRogueItemPropModify::Normalize()
{
	FAwRogueItemPropModify Res = FAwRogueItemPropModify();
	Res.MaxEnergy = 1;
	Res.CostEnergy = 1;
	Res.EnergyRecovery = 1;
	Res.CurEffectLevel = 1;
	Res.MaxEffectLevel = 1;
	Res.CurEffectValue = 1.f;
	return  Res;
}

FAwRogueItemPropModify FAwRogueItemPropModify::Zero()
{
	FAwRogueItemPropModify Res = FAwRogueItemPropModify();
	Res.MaxEnergy = 0;
	Res.CostEnergy = 0;
	Res.EnergyRecovery = 0;
	Res.CurEffectLevel = 0;
	Res.MaxEffectLevel = 0;
	Res.CurEffectValue = 0.f;
	return  Res;
}

FAwRogueItemEffect FAwRogueItemEffect::FromJson(TSharedPtr<FJsonObject> JsonObj)
{
	FAwRogueItemEffect TargetMetaData = FAwRogueItemEffect(); 
	TargetMetaData.EffectTags = UDataFuncLib::AwGetStringArrayField(JsonObj,"EffectTags");
	TargetMetaData.EffectMinLevel = UDataFuncLib::AwGetNumberField(JsonObj,"EffectMinLevel",1);
	TargetMetaData.EffectMaxLevel = UDataFuncLib::AwGetNumberField(JsonObj,"EffectMaxLevel",99);
	TargetMetaData.Effect = UDataFuncLib::AwGetStringField(JsonObj,"Effect");
	TargetMetaData.LoseEffect = UDataFuncLib::AwGetStringField(JsonObj,"LoseEffect");

	TSharedPtr<FJsonObject> Field;
	if (JsonObj->TryGetField("SpecialParams").IsValid())
	{
		 Field = JsonObj->TryGetField("SpecialParams")->AsObject();
	}
	for (auto Key:UDataFuncLib::AwGetJsonKeyField(Field))
	{
		TargetMetaData.SpecialParams.Add(Key, UDataFuncLib::AwGetStringField(Field,Key));
	}


	return  TargetMetaData;
}

FAwRogueItemAttributeUpgrade FAwRogueItemAttributeUpgrade::FromJson(TSharedPtr<FJsonObject> JsonObj)
{
	FAwRogueItemAttributeUpgrade TargetMetaData = FAwRogueItemAttributeUpgrade(); ;
	TargetMetaData.Level = UDataFuncLib::AwGetNumberField(JsonObj,"Level",1);
	TargetMetaData.MaxEnergyLevelUp = UDataFuncLib::AwGetNumberField(JsonObj,"MaxEnergyLevelUp",0);
	return TargetMetaData;
}

FAwRogueItemInfo::FAwRogueItemInfo()
{
	ModifyPower = FAwRogueItemPropModify::Normalize();
	ModifyValue = FAwRogueItemPropModify::Zero();
}

FAwRogueItemInfo FAwRogueItemInfo::FromJson(TSharedPtr<FJsonObject> JsonObj)
{
	FAwRogueItemInfo TargetMetaData = FAwRogueItemInfo();
	TargetMetaData.Item= UGameplayFuncLib::GetDataManager()->GetItemById(UDataFuncLib::AwGetStringField(JsonObj,"ItemId"));

	TargetMetaData.UID= UDataFuncLib::AwGetStringField(JsonObj,"UID").IsEmpty()?TargetMetaData.Item.Model.Id:UDataFuncLib::AwGetStringField(JsonObj,"UID");
	TargetMetaData.Tags = UDataFuncLib::AwGetStringArrayField(JsonObj,"Tags");
	TargetMetaData.CreaterMaxNum = UDataFuncLib::AwGetNumberField(JsonObj,"CreaterMaxNum",99);
	TargetMetaData.OrgEffectValue = UDataFuncLib::AwGetNumberField(JsonObj,"OrgEffectValue",1);
	TargetMetaData.CurEffectValue = TargetMetaData.OrgEffectValue;
	TargetMetaData.OrgEffectLevel = UDataFuncLib::AwGetNumberField(JsonObj,"OrgEffectLevel",1);
	TargetMetaData.CurEffectLevel = TargetMetaData.OrgEffectLevel;
	TargetMetaData.MaxEffectLevel = UDataFuncLib::AwGetNumberField(JsonObj,"MaxEffectLevel",1);
	TargetMetaData.MaxEnergy = UDataFuncLib::AwGetNumberField(JsonObj,"MaxEnergy",1);
	TargetMetaData.CurEnergy = TargetMetaData.MaxEnergy ;
	TargetMetaData.CostEnergy = UDataFuncLib::AwGetNumberField(JsonObj,"CostEnergy",1);
	TargetMetaData.EnergyRecovery =UDataFuncLib::AwGetNumberField(JsonObj,"EnergyRecovery",1);
	TargetMetaData.Value = UDataFuncLib::AwGetNumberField(JsonObj,"Value",1);

	for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "LevelUpAttributes"))
	{
		FAwRogueItemAttributeUpgrade NewUpgrade = FAwRogueItemAttributeUpgrade::FromJson(JsonValue->AsObject());
		TargetMetaData.LevelUpAttributes.Add(NewUpgrade.Level,NewUpgrade);
	}
	
	//效果读取
	for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "OnUseEffects"))
	{
		 FAwRogueItemEffect UseEffect = FAwRogueItemEffect::FromJson(JsonValue->AsObject());
		for (auto KeyTag:UseEffect.EffectTags)
		{
			if (TargetMetaData.OnUseEffects.Contains(KeyTag))
			{
				TargetMetaData.OnUseEffects[KeyTag].Effects.Add(UseEffect);
			}
			else
			{
				FAwRogueItemEffects NewEffects;
				NewEffects.Effects.Add(UseEffect);
				TargetMetaData.OnUseEffects.Add(KeyTag,NewEffects);
			}
		}
	}
	//持续效果读取
	for (TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "OnUsingEffects"))
	{
		FAwRogueItemEffect UsingEffect = FAwRogueItemEffect::FromJson(JsonValue->AsObject());
		for (auto KeyTag:UsingEffect.EffectTags)
		{
			if (TargetMetaData.OnUsingEffects.Contains(KeyTag))
			{
				TargetMetaData.OnUsingEffects[KeyTag].Effects.Add(UsingEffect);
			}
			else
			{
				FAwRogueItemEffects NewEffects;
				NewEffects.Effects.Add(UsingEffect);
				TargetMetaData.OnUsingEffects.Add(KeyTag,NewEffects);
			}
		}
	}

	
	TargetMetaData.ModifyPower = FAwRogueItemPropModify::Normalize();
	TargetMetaData.ModifyValue = FAwRogueItemPropModify::Zero();
	return  TargetMetaData;
}

FAwRogueItemInfo FAwRogueItemInfo::GetSelfAfterModify()
{
	//此处若要优化 参照Buff的 CharacterPropModify
	FAwRogueItemInfo Res = *this;
	
	//计算累加
	Res.MaxEnergy += ModifyValue.MaxEnergy;
	Res.CostEnergy += ModifyValue.CostEnergy;
	Res.EnergyRecovery += ModifyValue.EnergyRecovery;
	Res.CurEffectLevel += ModifyValue.CurEffectLevel;
	Res.MaxEffectLevel += ModifyValue.MaxEffectLevel;
	Res.CurEffectValue += ModifyValue.CurEffectValue;
	
          	//计算累乘
	Res.MaxEnergy *= ModifyPower.MaxEnergy;
	Res.CostEnergy *= ModifyPower.CostEnergy;
	Res.EnergyRecovery *= ModifyPower.EnergyRecovery;
	Res.CurEffectLevel *= ModifyPower.CurEffectLevel;
	Res.MaxEffectLevel *= ModifyPower.MaxEffectLevel;
	Res.CurEffectValue *= ModifyPower.CurEffectValue;
	//临界
	Res.MaxEnergy = FMath::Clamp(Res.MaxEnergy,1,Res.MaxEnergy);
	Res.CostEnergy = FMath::Clamp(Res.CostEnergy,1,Res.CostEnergy);
	Res.EnergyRecovery = FMath::Clamp(Res.EnergyRecovery,1,Res.EnergyRecovery);
	Res.CurEffectLevel = FMath::Clamp(Res.CurEffectLevel,1,Res.CurEffectLevel);
	Res.CurEffectValue = FMath::Clamp(Res.CurEffectValue,0,Res.CurEffectValue);
	
	//等级提升充能计算
	if (Res.LevelUpAttributes.Contains(Res.CurEffectLevel))
	{
		Res.MaxEnergy += Res.LevelUpAttributes[Res.CurEffectLevel].MaxEnergyLevelUp;
	}
	return Res;
}

void FAwRogueItemInfo::ResetModify()
{
	ModifyPower = FAwRogueItemPropModify::Normalize();
	ModifyValue = FAwRogueItemPropModify::Zero();
}

 bool FAwRogueItemInfo::CanBeUse()
{
	return  CurEnergy>=CostEnergy&&CurEnergy>=0;
}


void UAwRogueItemSubSystem::Initialize(FSubsystemCollectionBase& Collection)
{
	Super::Initialize(Collection);
	TickHandle = FTSTicker::GetCoreTicker().AddTicker(FTickerDelegate::CreateUObject(this,&UAwRogueItemSubSystem::Tick),0.0f);
}

void UAwRogueItemSubSystem::InitSubSystem()
{
	//初始化抓取所有肉鸽道具并将血瓶单独提出来
	AllRogueItemMap = UGameplayFuncLib::GetAwDataManager()->GetAllMetaRogueItems();
	if(AllRogueItemMap.Contains("HealingPotion_Rogue"))
	{
		CurHealingPotion = *AllRogueItemMap.Find("HealingPotion_Rogue");
		AllRogueItemMap.Remove("HealingPotion_Rogue");
	}
	if (AllRogueItemMap.Contains("Global_Setting"))
	{
		MaxGlobalItemEnergy = AllRogueItemMap["Global_Setting"].MaxEnergy;
		CurGlobalItemEnergy = MaxGlobalItemEnergy;
		AllRogueItemMap.Remove("Global_Setting");
	}
	
	AllRogueItemMap.GenerateKeyArray(GetableRogueItems);
}

void UAwRogueItemSubSystem::SaveData()
{
	if (!GetGameInstance())
	{
		return;
	}
	UAwRogueDataSystem* SubSystem = GetGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	if (!SubSystem)
	{
		return;
	}
	
	//存主动
	FAwRogueItemInfo TempItemInfo = FAwRogueItemInfo();
	if (!MainItem.UID.IsEmpty())
	{
		TempItemInfo = MainItem;
		TempItemInfo.Creaters.Empty();
		
		SubSystem->SetCurItem_CurBattle(TempItemInfo);
	}
	if (!SecondItem.UID.IsEmpty())
	{
		TempItemInfo = SecondItem;
		TempItemInfo.Creaters.Empty();

		SubSystem->SetCurItem_CurBattle(TempItemInfo,false);
	}
	
	//存血瓶
	FAwRogueItemInfo TempHealingPotion = CurHealingPotion;
	SubSystem->SetHealingPotion_CurBattle(TempHealingPotion);
	SubSystem->SetItemEnergy_CurBattle(CurGlobalItemEnergy);
}

void UAwRogueItemSubSystem::LoadSaveData()
{
	if (!GetGameInstance())
	{
		return;
	}
	ResetAllItemToMeta();
	UAwRogueDataSystem* SubSystem = GetGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	if (!SubSystem)
	{
		return;
	}
	FAwRogueItemInfo MainItemData = SubSystem->GetCurItem_CurBattle(true);
	FAwRogueItemInfo SecondItemData = SubSystem->GetCurItem_CurBattle(false);
	FAwRogueItemInfo HealPotionData = SubSystem->GetHealingPotion_CurBattle();

	//当前道具读取存档
	if (!MainItemData.UID.IsEmpty()&&AllRogueItemMap.Contains(MainItemData.UID))
	{
		SetCurRogueItem(MainItemData.UID,MainItemData.GetSelfAfterModify().CurEffectLevel,true);
	}
	if(!SecondItemData.UID.IsEmpty()&&AllRogueItemMap.Contains(SecondItemData.UID))
	{
		SetCurRogueItem(SecondItemData.UID,SecondItemData.GetSelfAfterModify().CurEffectLevel,false);
	}
	
	//血瓶读取存档
	if (HealPotionData.Item.Model.Id.Equals("HealingPotion_Rogue"))
	{
		CurHealingPotion= HealPotionData;
		CurHealingPotion.ResetModify();
	}

	CurGlobalItemEnergy =	SubSystem->GetItemEnergy_CurBattle();
}

void UAwRogueItemSubSystem::ReApplySaveDataModify()
{
	if (!GetGameInstance())
	{
		return;
	}
	UAwRogueDataSystem* SubSystem = GetGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	if (!SubSystem)
	{
		return;
	}
	FAwRogueItemInfo MainItemData = SubSystem->GetCurItem_CurBattle(true);
	FAwRogueItemInfo SecondItemData = SubSystem->GetCurItem_CurBattle(false);
	FAwRogueItemInfo HealPotionData = SubSystem->GetHealingPotion_CurBattle();

	//当前道具读取存档
	if (!MainItemData.UID.IsEmpty()&&AllRogueItemMap.Contains(MainItemData.UID))
	{
		MainItem = MainItemData;
	}
	 if(!SecondItemData.UID.IsEmpty()&&AllRogueItemMap.Contains(SecondItemData.UID))
	{
		SecondItem = SecondItemData;
	}
	
	//血瓶读取存档
	if (HealPotionData.UID.Equals("HealingPotion_Rogue"))
	{
		//旧存档没有效果结构 因此空时 覆盖
		if (HealPotionData.OnUseEffects.IsEmpty())
		{
			HealPotionData.OnUseEffects = CurHealingPotion.OnUseEffects;
		}
		if (HealPotionData.OnUsingEffects.IsEmpty())
		{
			HealPotionData.OnUsingEffects = CurHealingPotion.OnUsingEffects;
		}
		CurHealingPotion= HealPotionData;
	}
	
	CurGlobalItemEnergy =	SubSystem->GetItemEnergy_CurBattle();
}

TArray<FAwRogueItemInfo> UAwRogueItemSubSystem::GetAllRogueActiveItem()
{
	TArray<FAwRogueItemInfo> Res;
	for (auto Item:AllRogueItemMap)
	{
		if (Item.Value.Tags.Contains("NotInPool"))
		{
			continue;
		}
		Res.Add(Item.Value);
	}
	return Res;
}

void UAwRogueItemSubSystem::ApplyItemModifyToAllItem(FString ItemId)
{
	FAwRogueItemInfo* Target=  AllRogueItemMap.Find(ItemId);
	if (Target)
	{
		for (auto Item:AllRogueItemMap)
		{
			AllRogueItemMap[Item.Key].ModifyValue = Target->ModifyValue;
			AllRogueItemMap[Item.Key].ModifyPower = Target->ModifyPower;
		}
	}
	
}

bool UAwRogueItemSubSystem::UpgradeAllItem(int Level)
{
	if (AllRogueItemMap.Num())
	{
		GlobalItemModifyValue.CurEffectLevel += Level;
		
		if(OnItemUpgrade.IsBound())
		{
			OnItemUpgrade.Broadcast(AllRogueItemMap.begin().Value(),Level);
		}
		return  true;
	}
	return  false;
}

bool UAwRogueItemSubSystem::UpgradeItem(int Level, bool bMainItem)
{
	FAwRogueItemInfo* TargetItem = bMainItem? &MainItem: &SecondItem;
	if (!TargetItem)
	{
		return false;
	}
	if (TargetItem->UID.IsEmpty())
	{
		return false;
	}
	else
	{
		int TargetLevel = TargetItem->CurEffectLevel+Level;
		if (TargetLevel>TargetItem->GetSelfAfterModify().MaxEffectLevel||TargetLevel<1)
		{
			return  false;
		}
		TargetItem->CurEffectLevel = FMath::Clamp(TargetLevel,1,TargetItem->GetSelfAfterModify().MaxEffectLevel);
	}

	if(OnItemUpgrade.IsBound())
	{
		OnItemUpgrade.Broadcast(AllRogueItemMap.begin().Value(),AllRogueItemMap.begin().Value().GetSelfAfterModify().CurEffectLevel);
	}
	
	return  true;
}

void UAwRogueItemSubSystem::GetCurModify(FAwRogueItemPropModify& ValueModify, FAwRogueItemPropModify& PowerModify)
{
	ValueModify = GlobalItemModifyValue;
	PowerModify = GlobalItemModifyPower;
}

FAwRogueItemInfo UAwRogueItemSubSystem::GetRogueItemAfterGlobalModify(FAwRogueItemInfo SourceItem)
{
	if (SourceItem.UID.IsEmpty())
	{
		return FAwRogueItemInfo();
	}
	FAwRogueItemInfo Result = SourceItem;
	Result.ModifyValue += GlobalItemModifyValue;
	Result.ModifyPower = Result.ModifyPower + GlobalItemModifyPower - FAwRogueItemPropModify::Normalize();

	return Result.GetSelfAfterModify();
}

void UAwRogueItemSubSystem::ResetAllItemToMeta()
{
	//SetCurRogueItem("");
	SetCurRogueItem("",1,true);
	SetCurRogueItem("",1,false);
	AllRogueItemMap = UGameplayFuncLib::GetAwDataManager()->GetAllMetaRogueItems();
	if(AllRogueItemMap.Contains("HealingPotion_Rogue"))
	{
		CurHealingPotion = *AllRogueItemMap.Find("HealingPotion_Rogue");
		AllRogueItemMap.Remove("HealingPotion_Rogue");
	}
	if (AllRogueItemMap.Contains("Global_Setting"))
	{
		MaxGlobalItemEnergy = AllRogueItemMap["Global_Setting"].MaxEnergy;
		CurGlobalItemEnergy = MaxGlobalItemEnergy;
		AllRogueItemMap.Remove("Global_Setting");
	}
	AllRogueItemMap.GenerateKeyArray(GetableRogueItems);
}

bool UAwRogueItemSubSystem::ModifyRogueItem(FString Id, FAwRogueItemInfo NewInfo,bool ApplyGlobal )
{
	//应用于全局
	if (ApplyGlobal)
	{
		GlobalItemModifyPower = NewInfo.ModifyPower;
		GlobalItemModifyValue = NewInfo.ModifyValue;
		return true;
	}
	
	//覆盖
	if (AllRogueItemMap.Contains(Id))
	{
		AllRogueItemMap.Add(Id,NewInfo);
		return  true;
	}
	else if (CurHealingPotion.Item.Model.Id==Id)
	{
		CurHealingPotion = NewInfo;
		return  true;
	}
	
	return  false;
}

TMap<FString,int> UAwRogueItemSubSystem::GetRandomItem(int Number, int RoomLevel,ERogueRoomType RoomType)
{
	TMap<FString,int> Res;
	TArray<FString>Pool = GetableRogueItems;
	//如果已有最高等级的道具 则同道具不再会在池子中出现
	if (!MainItem.UID.IsEmpty()&&MainItem.GetSelfAfterModify().CurEffectLevel>=MainItem.GetSelfAfterModify().MaxEffectLevel)
	{
		Pool.Remove(MainItem.UID);
	}
	if (!SecondItem.UID.IsEmpty()&&SecondItem.GetSelfAfterModify().CurEffectLevel>=SecondItem.GetSelfAfterModify().MaxEffectLevel)
	{
		Pool.Remove(SecondItem.UID);
	}
	//第一关不会随出已有的法器
	if(RoomLevel == 1)
	{
		if(!MainItem.UID.IsEmpty())
		{
			Pool.Remove(MainItem.UID);
		}
		if(!SecondItem.UID.IsEmpty())
		{
			Pool.Remove(SecondItem.UID);
		}
	}

	int CurLevel =FMath::Max(GetRogueItemAfterGlobalModify(MainItem).CurEffectLevel,GetRogueItemAfterGlobalModify(MainItem).CurEffectLevel);
	CurLevel = FMath::Clamp(CurLevel,1,CurLevel+1);
	
	//随机
	for (int i =0;i<Number&&!Pool.IsEmpty();++i)
	{
		int RandomIndex = FMath::RandRange(0,Pool.Num()-1);
		if (Pool.IsValidIndex(RandomIndex))
		{
			int Level = GeItemRandomLevel(Pool[RandomIndex],RoomLevel,RoomType);
			Res.Add(Pool[RandomIndex],Level);
			Pool.RemoveAt(RandomIndex);
		}
	}
	
	return  Res;
}

int UAwRogueItemSubSystem::GeItemRandomLevel(FString ItemId, int RoomLevel,ERogueRoomType RoomType)
{
	int ResultLevel = 1;
	
	int MinLevel = 1;
	int MaxLevel = 99;

	if (!MainItem.UID.IsEmpty())
	{
		MaxLevel = FMath::Clamp(MaxLevel,1,FMath::Min(MainItem.GetSelfAfterModify().MaxEffectLevel,MainItem.GetSelfAfterModify().CurEffectLevel+1));
	}
	else if (!SecondItem.UID.IsEmpty())
	{
		MaxLevel = FMath::Clamp(MaxLevel,1,FMath::Min(SecondItem.GetSelfAfterModify().MaxEffectLevel,SecondItem.GetSelfAfterModify().CurEffectLevel+1));
	}
	else
	{
		MaxLevel = 1;
	}
	
	if (MainItem.UID.Equals(ItemId))
	{
		MinLevel =  FMath::Clamp(MinLevel,MainItem.GetSelfAfterModify().CurEffectLevel+1,MainItem.GetSelfAfterModify().MaxEffectLevel);
		MaxLevel = FMath::Clamp(MainItem.GetSelfAfterModify().CurEffectLevel+1,MainItem.GetSelfAfterModify().CurEffectLevel+1,MainItem.GetSelfAfterModify().MaxEffectLevel);
	}
	else if (SecondItem.UID.Equals(ItemId))
	{
		MinLevel =  FMath::Clamp(MinLevel,SecondItem.GetSelfAfterModify().CurEffectLevel+1,SecondItem.GetSelfAfterModify().MaxEffectLevel);
		MaxLevel = FMath::Clamp(SecondItem.GetSelfAfterModify().CurEffectLevel+1,SecondItem.GetSelfAfterModify().CurEffectLevel+1,SecondItem.GetSelfAfterModify().MaxEffectLevel);
	}
	
	//获取配置数据
	UAwDataManager* DataManager= UGameplayFuncLib::GetAwDataManager();

	TMap<FString, FAwRoomRelicDrop> RelicDropInfo = DataManager->GetRogueRoomRelicDrop();

	TMap<int,float> LevelWeight;
	
	for (auto DropInfo : RelicDropInfo)
	{
		if (RoomType == DropInfo.Value.RoomType&& UKismetMathLibrary::InRange_FloatFloat(RoomLevel,DropInfo.Value.MinLevel,DropInfo.Value.MaxLevel))
		{
			for (auto Drop:DropInfo.Value.DropsRate)
			{
				if (Drop.Level<=MaxLevel&&Drop.Level>=MinLevel)
				{
					LevelWeight.Add(Drop.Level,Drop.Rate);
				}
			}
			break;
		}
	}
	
	TArray<float> Values;
	LevelWeight.GenerateValueArray(Values);
	
	float TotalWeight = Algo::Accumulate(Values,0.f,TPlus<>());

	float RandWeight = FMath::RandRange(0.0f,TotalWeight);

	//线性随机  堆栈随机
	LevelWeight.KeySort([](int A,int B)
		{
			return A<B;
		}
	);
	
	float CurLimitValue = 0;
	for (auto It:LevelWeight)
	{
		CurLimitValue += It.Value;
		if (RandWeight<=CurLimitValue)
		{
			ResultLevel = It.Key;
			break;
		}
	}

	/*
	if (MainItem.UID == ItemId||SecondItem.UID == ItemId)
	{
		UKismetSystemLibrary::PrintString(this,FString::SanitizeFloat(ResultLevel));
	}
	*/
	
	//保险措施 理论上不必要
	ResultLevel = FMath::Clamp(ResultLevel,MinLevel,MaxLevel);
	
	return  ResultLevel;
}

void UAwRogueItemSubSystem::FullyRestorePotion()
{
	CurHealingPotion.CurEnergy = CurHealingPotion.GetSelfAfterModify().MaxEnergy;
}

void UAwRogueItemSubSystem::ReSetCurItemCreater(bool bMainItem )
{
	//获取目标地址
	FAwRogueItemInfo* TargetItem = bMainItem? &MainItem: &SecondItem;
	
	//重置生成物
	if (TargetItem->UID.IsEmpty())
	{
		return;
	}
	for(int i =0; i< TargetItem->Creaters.Num();i++)
	{
		if (IsValid(TargetItem->Creaters[i].Get())&&TargetItem->Creaters[i].Get()->GetWorld())
		{
			TargetItem->Creaters[i].Get()->Destroy();
		}
	}
	TargetItem->Creaters.Empty();
	
	/*
	for(TArray<AActor*>::TIterator Iterator = MainItem->Creaters.CreateIterator();Iterator;++Iterator)
	{
		AActor* Creater =  nullptr;
		Creater = Iterator?*Iterator:nullptr;
		if (IsValidChecked(Creater))
		{
			(*Iterator)->Destroy();
			Iterator.RemoveCurrent();
		}
		else
		{
			Iterator.RemoveCurrent();
		}
	}
	*/
}

void UAwRogueItemSubSystem::ReSetCurItem(bool bMainItem)
{
	if (bMainItem)
	{
		if (!MainItem.UID.IsEmpty())
		{
			MainItem.CurEnergy = MainItem.GetSelfAfterModify().MaxEnergy;
			ReSetCurItemCreater(true);
		}
	}
	else
	{
		if (!SecondItem.UID.IsEmpty())
		{
			SecondItem.CurEnergy = SecondItem.GetSelfAfterModify().MaxEnergy;
			ReSetCurItemCreater(false);
		}
	}
	
}

void UAwRogueItemSubSystem::SetCurRogueItem(FString ItemId,int level,bool bMainItem )
{
	if (ItemId.IsEmpty())
	{
		if (bMainItem)
		{
			ReSetCurItem(true);
			MainItem = FAwRogueItemInfo();
		}
		else
		{
			ReSetCurItem(false);
			SecondItem = FAwRogueItemInfo();
		}
		return;
	}
	else if (!AllRogueItemMap.Contains(ItemId))
	{
		return;
	}

	FAwRogueItemInfo NewInfo = AllRogueItemMap[ItemId];
	//具体设置到那个道具位置
	if (bMainItem)
	{
		if (MainItem.UID!=ItemId || level!=MainItem.GetSelfAfterModify().CurEffectLevel)
		{
			ReSetCurItem(true);
			//GetableRogueItems.Add(MainItem->Item.Model.Id);
		}
		NewInfo = AllRogueItemMap[ItemId];
		NewInfo.CurEffectLevel = FMath::Clamp(level,1,MainItem.GetSelfAfterModify().MaxEffectLevel);
		MainItem = NewInfo;
	}
	else
	{
		if (SecondItem.UID!=ItemId || level!=SecondItem.GetSelfAfterModify().CurEffectLevel)
		{
			ReSetCurItem(false);
			//GetableRogueItems.Add(MainItem->Item.Model.Id);
		}
		SecondItem = AllRogueItemMap[ItemId];
		NewInfo.CurEffectLevel = FMath::Clamp(level,1,SecondItem.GetSelfAfterModify().MaxEffectLevel);
		SecondItem= NewInfo;
	}

	UAwRogueDataSystem* DataSystem = GetGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	if (DataSystem)
	{
		DataSystem->SetMagicItemHasGetRecord(ItemId);
		PlayGetUseRogueItem.Broadcast(NewInfo);
	}
}

FAwRogueItemInfo UAwRogueItemSubSystem::GetRogueItemById(FString Id)
{
	FAwRogueItemInfo Res = FAwRogueItemInfo();
	if (AllRogueItemMap.Contains(Id))
	{
		Res = *AllRogueItemMap.Find(Id);
	}
	return  Res;
}

void UAwRogueItemSubSystem::UseRogueItemAction(const int PlayerIndex,bool bUseMain)
{
	FAwRogueItemInfo*  Target = bUseMain? &MainItem: &SecondItem;
	if (!Target)
	{
		return;
	}
	if (Target->UID.IsEmpty())
	{
		return;
	}
	/*
	 //旧使用判断
	if (!Target->GetSelfAfterModify().CanBeUse())
	{
		return;
	}
	*/
	if (CurGlobalItemEnergy<Target->GetSelfAfterModify().CostEnergy)
	{
		return;
	}
	
	AAwCharacter* Character = UGameplayFuncLib::GetLocalAwPlayerCharacter(PlayerIndex) ;
	
	if (!IsValid(Character))
	{
		return;
	}

	UActionComponent*  ActionComponent = Character->GetActionComponent();

	if (!IsValid(ActionComponent))
	{
		return;
	}

	if(!ActionComponent->ActionCanCancelCurAction(Target->Item.Model.OnUse.UseActionId))
	{
		return;
	}
	LastOnUsingItem = Target;
	UGameplayFuncLib::GetLocalAwPlayerCharacter(PlayerIndex)->PreorderActionWithCancelCheck(Target->Item.Model.OnUse.UseActionId);
}

bool UAwRogueItemSubSystem::UseHealingPotionAction(const int PlayerIndex)
{
	if (CurHealingPotion.Item.Model.Id.IsEmpty())
	{
		return false;
	}

	if (!CurHealingPotion.GetSelfAfterModify().CanBeUse())
	{
		return false;
	}

	AAwCharacter* Character = UGameplayFuncLib::GetLocalAwPlayerCharacter(PlayerIndex) ;
	
	if (!IsValid(Character))
	{
		return false;
	}
	
	UActionComponent*  ActionComponent = Character->GetActionComponent();

	if (!IsValid(ActionComponent))
	{
		return false;
	}

	if(!ActionComponent->ActionCanCancelCurAction(CurHealingPotion.Item.Model.OnUse.UseActionId))
	{
		return false;
	}
	
	LastOnUsingItem = &CurHealingPotion;
	UGameplayFuncLib::GetLocalAwPlayerController(PlayerIndex)->CurCharacter->PreorderActionWithCancelCheck(CurHealingPotion.Item.Model.OnUse.UseActionId);

	return  true;
}

bool UAwRogueItemSubSystem::UseCurRogueItem(TArray<FString> UseMethodTags,const int PlayerIndex,bool bCost )
{
	if (!LastOnUsingItem)
	{
		return false;
	}
	if (LastOnUsingItem->UID.IsEmpty())
	{
		return  false;
	}
	AAwCharacter* PlayerCharacter = UGameplayFuncLib::GetLocalAwPlayerCharacter(PlayerIndex);
	if (!PlayerCharacter)
	{
		return false;
	}
	
	//扣除消耗
	if (bCost)
	{
		if (LastOnUsingItem->Item.Model.Id ==CurHealingPotion.Item.Model.Id)
		{
			LastOnUsingItem->CurEnergy -= LastOnUsingItem->GetSelfAfterModify().CostEnergy;
		}
		else
		{
			CurGlobalItemEnergy -= LastOnUsingItem->GetSelfAfterModify().CostEnergy;
		}
	}
	
	//触发目标使用组对应的效果
	TArray<FItemUseResult> UseResults;
	for (auto UseMethod:UseMethodTags)
	{
		if (LastOnUsingItem->OnUseEffects.Contains(UseMethod))
		{
			//解析实际效果
			for (auto UseEffect:LastOnUsingItem->OnUseEffects[UseMethod].Effects)
			{
				//非对应等级效果不触发
				int CurLevel = LastOnUsingItem->GetSelfAfterModify().CurEffectLevel;
				if (!UKismetMathLibrary::InRange_IntInt(CurLevel,UseEffect.EffectMinLevel,UseEffect.EffectMaxLevel))
				{
					continue;
				}
				
				const FJsonFuncData JsonFunc = UCallFuncLib::StringToJsonFuncData(UseEffect.Effect);
				UFunction* Func = UCallFuncLib::JsonFuncToUFunc(JsonFunc);
				if (IsValid(Func) == false) continue;
				struct {
					FAwRogueItemInfo Item;
					AAwCharacter* User;
					TMap<FString,FString>SpecialParams ; 
					TArray<FString> Params;
					FItemUseResult Result;
				} FuncParam;
				FuncParam.Item = *LastOnUsingItem;
				FuncParam.User = PlayerCharacter;
				FuncParam.SpecialParams = UseEffect.SpecialParams;
				FuncParam.Params = JsonFunc.Params;
						
				GWorld->ProcessEvent(Func, &FuncParam);
				UseResults.Add(FuncParam.Result);
				//生成控制器
				TArray<AActor*>  NewCreaters;
				for (auto Creater : FuncParam.Result.Creaters)
				{
					if (Creater&&IsValid(Creater))
					{
						if (Creater->Implements<URogueCreaterControllerInterface>())
						{
							TMap<FString,FString>InterfaceParams = UseEffect.SpecialParams;
							InterfaceParams.Add("EffectLevel",FString::SanitizeFloat(LastOnUsingItem->GetSelfAfterModify().CurEffectLevel));
							InterfaceParams.Add("EffectValue",FString::SanitizeFloat(LastOnUsingItem->GetSelfAfterModify().CurEffectValue));
							
						    TArray<AActor*> ControlCreaters =	IRogueCreaterControllerInterface::Execute_GetRealCreatersByInfos(Creater,InterfaceParams);
							if (!ControlCreaters.IsEmpty())
							{
								NewCreaters=ControlCreaters;
							}
							else
							{
								NewCreaters.Add(Creater);
							}
						}
						if (Creater->Implements<URogueInterface>())
						{
							TMap<FString,FString>InterfaceParams = UseEffect.SpecialParams;;
							InterfaceParams.Add("EffectLevel",FString::SanitizeFloat(LastOnUsingItem->GetSelfAfterModify().CurEffectLevel));
							InterfaceParams.Add("EffectValue",FString::SanitizeFloat(LastOnUsingItem->GetSelfAfterModify().CurEffectValue));
							IRogueInterface::Execute_GiveInfoMap(Creater,InterfaceParams);
						}
					}
				}
				
				int CreaterNum = LastOnUsingItem->Creaters.Num();
				int CreaterMaxNum = LastOnUsingItem->GetSelfAfterModify().CreaterMaxNum;
				
				//实际生成物传参
				for (auto Creater : NewCreaters)
				{
					if (Creater&&IsValid(Creater))
					{
						LastOnUsingItem->Creaters.Add(Creater);
						//接口传参
						if (Creater->Implements<URogueInterface>())
						{
							TMap<FString,FString>InterfaceParams = UseEffect.SpecialParams;;
							InterfaceParams.Add("EffectLevel",FString::SanitizeFloat(LastOnUsingItem->GetSelfAfterModify().CurEffectLevel));
							InterfaceParams.Add("EffectValue",FString::SanitizeFloat(LastOnUsingItem->GetSelfAfterModify().CurEffectValue));
							IRogueInterface::Execute_GiveInfoMap(Creater,InterfaceParams);
						}
					}
				}
				
				//超出对象池的生成物 被替代
				int RepleaceNum = CreaterNum + NewCreaters.Num() - CreaterMaxNum;
				for (int i =0;i<RepleaceNum;++i)
				{
					if(LastOnUsingItem->Creaters.IsValidIndex(i))
					{
						if(IsValid(LastOnUsingItem->Creaters[i].Get()))
						{
							LastOnUsingItem->Creaters[i].Get()->Destroy();
						}
					}
				}			
			}
		}
	}
	FItemUseResult Res = FItemUseResult::Merge(UseResults);
	if (Res.UseSuccessful == false)
	{
		return false;
	}
	PlayerCharacter->OnCharacterUseItem.Broadcast(PlayerCharacter,LastOnUsingItem->Item);
	OnUseRogueItem.Broadcast(*LastOnUsingItem);
	
	return true;
}

bool UAwRogueItemSubSystem::UseRogueItemById(TArray<FString> UseMethodTags, FString Id,const int PlayerIndex,bool bCost)
{
	AAwCharacter* PlayerCharacter = UGameplayFuncLib::GetLocalAwPlayerCharacter(PlayerIndex);
	if (!PlayerCharacter)
	{
		return false;
	}

	FAwRogueItemInfo* Target = AllRogueItemMap.Find(Id);
	if (CurHealingPotion.Item.Model.Id == Id)
	{
		Target = &CurHealingPotion;
	}

	if (!Target)
	{
		return false;
	}
	
	//扣除消耗
	if (bCost)
	{
		if (LastOnUsingItem->Item.Model.Id ==CurHealingPotion.Item.Model.Id)
		{
			LastOnUsingItem->CurEnergy -= LastOnUsingItem->GetSelfAfterModify().CostEnergy;
		}
		else
		{
			CurGlobalItemEnergy -= LastOnUsingItem->GetSelfAfterModify().CostEnergy;
		}
	}

		//触发目标使用组对应的效果
	TArray<FItemUseResult> UseResults;
	for (auto UseMethod:UseMethodTags)
	{
		if (Target->OnUseEffects.Contains(UseMethod))
		{
			//解析实际效果
			for (auto UseEffect:Target->OnUseEffects[UseMethod].Effects)
			{
				//非对应等级效果不触发
				int CurLevel = Target->GetSelfAfterModify().CurEffectLevel;
				if (!UKismetMathLibrary::InRange_IntInt(CurLevel,UseEffect.EffectMinLevel,UseEffect.EffectMaxLevel))
				{
					continue;
				}
				
				const FJsonFuncData JsonFunc = UCallFuncLib::StringToJsonFuncData(UseEffect.Effect);
				UFunction* Func = UCallFuncLib::JsonFuncToUFunc(JsonFunc);
				if (IsValid(Func) == false) continue;
				struct {
					FAwRogueItemInfo Item;
					AAwCharacter* User;
					TMap<FString,FString>SpecialParams ; 
					TArray<FString> Params;
					FItemUseResult Result;
				} FuncParam;
				FuncParam.Item = *Target;
				FuncParam.User = PlayerCharacter;
				FuncParam.SpecialParams = UseEffect.SpecialParams;
				FuncParam.Params = JsonFunc.Params;
						
				GWorld->ProcessEvent(Func, &FuncParam);
				UseResults.Add(FuncParam.Result);
				//生成控制器
				TArray<AActor*>  NewCreaters;
				for (auto Creater : FuncParam.Result.Creaters)
				{
					if (Creater&&IsValid(Creater))
					{
						if (Creater->Implements<URogueCreaterControllerInterface>())
						{
							TMap<FString,FString>InterfaceParams = UseEffect.SpecialParams;;
							InterfaceParams.Add("EffectLevel",FString::SanitizeFloat(Target->GetSelfAfterModify().CurEffectLevel));
							InterfaceParams.Add("EffectValue",FString::SanitizeFloat(Target->GetSelfAfterModify().CurEffectValue));
							
						    TArray<AActor*> ControlCreaters =	IRogueCreaterControllerInterface::Execute_GetRealCreatersByInfos(Creater,InterfaceParams);
							if (!ControlCreaters.IsEmpty())
							{
								NewCreaters.Add(Creater);
							}
						}
						if (Creater->Implements<URogueInterface>())
						{
							TMap<FString,FString>InterfaceParams = UseEffect.SpecialParams;;
							InterfaceParams.Add("EffectLevel",FString::SanitizeFloat(LastOnUsingItem->GetSelfAfterModify().CurEffectLevel));
							InterfaceParams.Add("EffectValue",FString::SanitizeFloat(LastOnUsingItem->GetSelfAfterModify().CurEffectValue));
							IRogueInterface::Execute_GiveInfoMap(Creater,InterfaceParams);
						}
					}
				}
				
				int CreaterNum = Target->Creaters.Num();
				int CreaterMaxNum = Target->GetSelfAfterModify().CreaterMaxNum;
				
				//实际生成物传参
				for (auto Creater : NewCreaters)
				{
					if (Creater&&IsValid(Creater))
					{
						Target->Creaters.Add(Creater);
						//接口传参
						if (Creater->Implements<URogueInterface>())
						{
							TMap<FString,FString>InterfaceParams = UseEffect.SpecialParams;;
							InterfaceParams.Add("EffectLevel",FString::SanitizeFloat(Target->GetSelfAfterModify().CurEffectLevel));
							InterfaceParams.Add("EffectValue",FString::SanitizeFloat(Target->GetSelfAfterModify().CurEffectValue));
							IRogueInterface::Execute_GiveInfoMap(Creater,InterfaceParams);
						}
					}
				}
				
				//超出对象池的生成物 被替代
				int RepleaceNum = CreaterNum + NewCreaters.Num() - CreaterMaxNum;
				for (int i =0;i<RepleaceNum;++i)
				{
					if(Target->Creaters.IsValidIndex(i))
					{
						if(IsValid(Target->Creaters[i].Get()))
						{
							Target->Creaters[i].Get()->Destroy();
						}
					}
				}			
			}
		}
	}
	FItemUseResult Res = FItemUseResult::Merge(UseResults);
	if (Res.UseSuccessful == false)
	{
		return false;
	}
	PlayerCharacter->OnCharacterUseItem.Broadcast(PlayerCharacter,Target->Item);
	OnUseRogueItem.Broadcast(*Target);
	
	return true;
}

bool UAwRogueItemSubSystem::OnUsingCurRogueItem(TArray<FString> UseMethodTags,const int PlayerIndex)
{
	if (!LastOnUsingItem)
	{
		return false;
	}
	if (LastOnUsingItem->UID.IsEmpty())
	{
		return  false;
	}
	AAwCharacter* PlayerCharacter = UGameplayFuncLib::GetLocalAwPlayerCharacter(PlayerIndex);
	if (!PlayerCharacter)
	{
		return false;
	}

	//触发目标使用组对应的效果
	TArray<FItemUseResult> UseResults;
	for (auto UseMethod:UseMethodTags)
	{
		if (LastOnUsingItem->OnUsingEffects.Contains(UseMethod))
		{
			//解析实际效果
			for (auto UseEffect:LastOnUsingItem->OnUsingEffects[UseMethod].Effects)
			{
				//非对应等级效果不触发
				int CurLevel = LastOnUsingItem->GetSelfAfterModify().CurEffectLevel;
				if (!UKismetMathLibrary::InRange_IntInt(CurLevel,UseEffect.EffectMinLevel,UseEffect.EffectMaxLevel))
				{
					continue;
				}
				
				const FJsonFuncData JsonFunc = UCallFuncLib::StringToJsonFuncData(UseEffect.Effect);
				UFunction* Func = UCallFuncLib::JsonFuncToUFunc(JsonFunc);
				if (IsValid(Func) == false) continue;
				struct {
					FAwRogueItemInfo Item;
					AAwCharacter* User;
					TMap<FString,FString>SpecialParams ; 
					TArray<FString> Params;
					FItemUseResult Result;
				} FuncParam;
				FuncParam.Item =  *LastOnUsingItem;
				FuncParam.User = PlayerCharacter;
				FuncParam.SpecialParams = UseEffect.SpecialParams;
				FuncParam.Params = JsonFunc.Params;
						
				GWorld->ProcessEvent(Func, &FuncParam);
				UseResults.Add(FuncParam.Result);
				//生成控制器
				TArray<AActor*>  NewCreaters;
				for (auto Creater : FuncParam.Result.Creaters)
				{
					if (Creater&&IsValid(Creater))
					{
						if (Creater->Implements<URogueCreaterControllerInterface>())
						{
							TMap<FString,FString>InterfaceParams = UseEffect.SpecialParams;;
							InterfaceParams.Add("EffectLevel",FString::SanitizeFloat(LastOnUsingItem->GetSelfAfterModify().CurEffectLevel));
							InterfaceParams.Add("EffectValue",FString::SanitizeFloat(LastOnUsingItem->GetSelfAfterModify().CurEffectValue));
							
						    TArray<AActor*> ControlCreaters =	IRogueCreaterControllerInterface::Execute_GetRealCreatersByInfos(Creater,InterfaceParams);
							if (!ControlCreaters.IsEmpty())
							{
								NewCreaters.Add(Creater);
							}
						}
					}
					if (Creater->Implements<URogueInterface>())
					{
						TMap<FString,FString>InterfaceParams = UseEffect.SpecialParams;;
						InterfaceParams.Add("EffectLevel",FString::SanitizeFloat(LastOnUsingItem->GetSelfAfterModify().CurEffectLevel));
						InterfaceParams.Add("EffectValue",FString::SanitizeFloat(LastOnUsingItem->GetSelfAfterModify().CurEffectValue));
						IRogueInterface::Execute_GiveInfoMap(Creater,InterfaceParams);
					}
				}
				
				int CreaterNum = LastOnUsingItem->Creaters.Num();
				int CreaterMaxNum = LastOnUsingItem->GetSelfAfterModify().CreaterMaxNum;
				
				//实际生成物传参
				for (auto Creater : NewCreaters)
				{
					if (Creater&&IsValid(Creater))
					{
						LastOnUsingItem->Creaters.Add(Creater);
						//接口传参
						if (Creater->Implements<URogueInterface>())
						{
							TMap<FString,FString>InterfaceParams = UseEffect.SpecialParams;;
							InterfaceParams.Add("EffectLevel",FString::SanitizeFloat(LastOnUsingItem->GetSelfAfterModify().CurEffectLevel));
							InterfaceParams.Add("EffectValue",FString::SanitizeFloat(LastOnUsingItem->GetSelfAfterModify().CurEffectValue));
							IRogueInterface::Execute_GiveInfoMap(Creater,InterfaceParams);
						}
					}
				}
				
				//超出对象池的生成物 被替代
				int RepleaceNum = CreaterNum + NewCreaters.Num() - CreaterMaxNum;
				for (int i =0;i<RepleaceNum;++i)
				{
					if(LastOnUsingItem->Creaters.IsValidIndex(i))
					{
						if(IsValid(LastOnUsingItem->Creaters[i].Get()))
						{
							LastOnUsingItem->Creaters[i].Get()->Destroy();
						}
					}
				}			
			}
		}
	}
	FItemUseResult Res = FItemUseResult::Merge(UseResults);
	if (Res.UseSuccessful == false)
	{
		return false;
	}
	PlayerCharacter->OnCharacterUseItem.Broadcast(PlayerCharacter,LastOnUsingItem->Item);
	OnUseRogueItem.Broadcast(*LastOnUsingItem);
	return true;
	
}

bool UAwRogueItemSubSystem::OnUsingCurRogueItemEnd(TArray<FString> UseMethodTags,const int PlayerIndex)
{
	if (!LastOnUsingItem)
	{
		return false;
	}
	if (LastOnUsingItem->UID.IsEmpty())
	{
		return  false;
	}
	AAwCharacter* PlayerCharacter = UGameplayFuncLib::GetLocalAwPlayerCharacter(PlayerIndex);
	if (!PlayerCharacter)
	{
		return false;
	}
	
	//触发目标使用组对应的效果
	TArray<FItemUseResult> UseResults;
	for (auto UseMethod:UseMethodTags)
	{
		if (LastOnUsingItem->OnUsingEffects.Contains(UseMethod))
		{
			//解析实际效果
			for (auto UseEffect:LastOnUsingItem->OnUsingEffects[UseMethod].Effects)
			{
				//非对应等级效果不触发
				int CurLevel = LastOnUsingItem->GetSelfAfterModify().CurEffectLevel;
				if (!UKismetMathLibrary::InRange_IntInt(CurLevel,UseEffect.EffectMinLevel,UseEffect.EffectMaxLevel))
				{
					continue;
				}
				
				const FJsonFuncData JsonFunc = UCallFuncLib::StringToJsonFuncData(UseEffect.LoseEffect);
				UFunction* Func = UCallFuncLib::JsonFuncToUFunc(JsonFunc);
				if (IsValid(Func) == false) continue;
				struct {
					FAwRogueItemInfo Item;
					AAwCharacter* User;
					TMap<FString,FString>SpecialParams ; 
					TArray<FString> Params;
					FItemUseResult Result;
				} FuncParam;
				FuncParam.Item = *LastOnUsingItem;
				FuncParam.User = PlayerCharacter;
				FuncParam.SpecialParams = UseEffect.SpecialParams;
				FuncParam.Params = JsonFunc.Params;
						
				GWorld->ProcessEvent(Func, &FuncParam);
				UseResults.Add(FuncParam.Result);
				//生成控制器
				TArray<AActor*>  NewCreaters;
				for (auto Creater : FuncParam.Result.Creaters)
				{
					if (Creater&&IsValid(Creater))
					{
						if (Creater->Implements<URogueCreaterControllerInterface>())
						{
							TMap<FString,FString>InterfaceParams = UseEffect.SpecialParams;;
							InterfaceParams.Add("EffectLevel",FString::SanitizeFloat(LastOnUsingItem->GetSelfAfterModify().CurEffectLevel));
							InterfaceParams.Add("EffectValue",FString::SanitizeFloat(LastOnUsingItem->GetSelfAfterModify().CurEffectValue));
							
						    TArray<AActor*> ControlCreaters =	IRogueCreaterControllerInterface::Execute_GetRealCreatersByInfos(Creater,InterfaceParams);
							if (!ControlCreaters.IsEmpty())
							{
								NewCreaters.Add(Creater);
							}
						}
						if (Creater->Implements<URogueInterface>())
						{
							TMap<FString,FString>InterfaceParams = UseEffect.SpecialParams;;
							InterfaceParams.Add("EffectLevel",FString::SanitizeFloat(LastOnUsingItem->GetSelfAfterModify().CurEffectLevel));
							InterfaceParams.Add("EffectValue",FString::SanitizeFloat(LastOnUsingItem->GetSelfAfterModify().CurEffectValue));
							IRogueInterface::Execute_GiveInfoMap(Creater,InterfaceParams);
						}
					}
				}
				
				int CreaterNum = LastOnUsingItem->Creaters.Num();
				int CreaterMaxNum = LastOnUsingItem->GetSelfAfterModify().CreaterMaxNum;
				
				//实际生成物传参
				for (auto Creater : NewCreaters)
				{
					if (Creater&&IsValid(Creater))
					{
						LastOnUsingItem->Creaters.Add(Creater);
						//接口传参
						if (Creater->Implements<URogueInterface>())
						{
							TMap<FString,FString>InterfaceParams = UseEffect.SpecialParams;;
							InterfaceParams.Add("EffectLevel",FString::SanitizeFloat(LastOnUsingItem->GetSelfAfterModify().CurEffectLevel));
							InterfaceParams.Add("EffectValue",FString::SanitizeFloat(LastOnUsingItem->GetSelfAfterModify().CurEffectValue));
							IRogueInterface::Execute_GiveInfoMap(Creater,InterfaceParams);
						}
					}
				}
				
				//超出对象池的生成物 被替代
				int RepleaceNum = CreaterNum + NewCreaters.Num() - CreaterMaxNum;
				for (int i =0;i<RepleaceNum;++i)
				{
					if(LastOnUsingItem->Creaters.IsValidIndex(i))
					{
						if(IsValid(LastOnUsingItem->Creaters[i].Get()))
						{
							LastOnUsingItem->Creaters[i].Get()->Destroy();
						}
					}
				}			
			}
		}
	}
	FItemUseResult Res = FItemUseResult::Merge(UseResults);
	if (Res.UseSuccessful == false)
	{
		return false;
	}
	PlayerCharacter->OnCharacterUseItem.Broadcast(PlayerCharacter,LastOnUsingItem->Item);
	OnUseRogueItem.Broadcast(*LastOnUsingItem);
	return true;
	
}

void UAwRogueItemSubSystem::AddGlobalItemRecoverByDamageInfo(FDamageInfo NewDamageInfo)
{
	if(NewDamageInfo.DamageSourceType <EAttackSource::AttackAction||NewDamageInfo.DamageSourceType>EAttackSource::DodgeAction)
	{
		return;
	}
	float BaseValue =  UGameplayFuncLib::GetAwDataManager()->DebugConfig.RogueItemDamageRecoverBase;
	float MaxValue =  UGameplayFuncLib::GetAwDataManager()->DebugConfig.RogueItemDamageRecoverOnceMax;
	
	float RecoverValue = BaseValue + NewDamageInfo.DamageCastItemEnergyPower*NewDamageInfo.DamagePower.Physical;
	RecoverValue = FMath::Clamp(RecoverValue,0,MaxValue);
	
	CurGlobalItemEnergy += RecoverValue;
	CurGlobalItemEnergy = FMath::Clamp(CurGlobalItemEnergy,0, MaxGlobalItemEnergy);
}


bool UAwRogueItemSubSystem::Tick(float DeltaTime)
{
	if(UGameplayStatics::IsGamePaused(this))
	{
		return true;
	}
	
	//旧独立回复计算
	/*
	if (!MainItem.UID.IsEmpty())
	{
		int Stack =  FMath::Floor(GetRogueItemAfterGlobalModify(MainItem).CurEnergy/GetRogueItemAfterGlobalModify(MainItem).CostEnergy);
		MainItem.CurEnergy+= GetRogueItemAfterGlobalModify(MainItem).EnergyRecovery*DeltaTime;
		MainItem.CurEnergy = FMath::Clamp(MainItem.CurEnergy,0, GetRogueItemAfterGlobalModify(MainItem).MaxEnergy);
		int NewStack =  FMath::Floor(GetRogueItemAfterGlobalModify(MainItem).CurEnergy/GetRogueItemAfterGlobalModify(MainItem).CostEnergy);
		if (NewStack>Stack)
		{
			//UKismetSystemLibrary::PrintString(nullptr,FString::SanitizeFloat(Stack)+"----"+FString::SanitizeFloat(NewStack));
			if(OnItemRecoverStack.IsBound())
			{
				OnItemRecoverStack.Broadcast(MainItem);
			}
		}
	}
	//旧独立回复计算
	if (!SecondItem.UID.IsEmpty())
	{
		int Stack = FMath::Floor(GetRogueItemAfterGlobalModify(SecondItem).CurEnergy/GetRogueItemAfterGlobalModify(SecondItem).CostEnergy);
		SecondItem.CurEnergy+= GetRogueItemAfterGlobalModify(SecondItem).EnergyRecovery*DeltaTime;
		SecondItem.CurEnergy = FMath::Clamp(SecondItem.CurEnergy,0, GetRogueItemAfterGlobalModify(SecondItem).MaxEnergy);
		int NewStack =  FMath::Floor(GetRogueItemAfterGlobalModify(SecondItem).CurEnergy/GetRogueItemAfterGlobalModify(SecondItem).CostEnergy);
		if (NewStack>Stack)
		{
			if(OnItemRecoverStack.IsBound())
			{
				OnItemRecoverStack.Broadcast(SecondItem);
			}
		}
	}
	*/

	//2.1全局回复
	if (GlobalItemModifyValue.EnergyRecovery!=0)
	{
		float RecoverValue = GlobalItemModifyValue.EnergyRecovery*GlobalItemModifyPower.EnergyRecovery;

		int Stack1 = 0;
		int Stack2 = 0;
		bool LastAllFill = CurGlobalItemEnergy>= (GetRogueItemAfterGlobalModify(MainItem).CostEnergy+GetRogueItemAfterGlobalModify(SecondItem).CostEnergy);
		if (!MainItem.UID.IsEmpty())
		{
			 Stack1 =  FMath::Floor(CurGlobalItemEnergy/GetRogueItemAfterGlobalModify(MainItem).CostEnergy);
		}
		else if (!SecondItem.UID.IsEmpty())
		{
			Stack2 = FMath::Floor(CurGlobalItemEnergy/GetRogueItemAfterGlobalModify(SecondItem).CostEnergy);
		}
		
		CurGlobalItemEnergy += RecoverValue*DeltaTime;
		CurGlobalItemEnergy = FMath::Clamp(CurGlobalItemEnergy,0, MaxGlobalItemEnergy);
		
		bool MainFill = FMath::Floor(CurGlobalItemEnergy/GetRogueItemAfterGlobalModify(MainItem).CostEnergy)>Stack1;
		bool SecondFill = FMath::Floor(CurGlobalItemEnergy/GetRogueItemAfterGlobalModify(SecondItem).CostEnergy)>Stack2;
		bool AllFill =LastAllFill&&CurGlobalItemEnergy>= (GetRogueItemAfterGlobalModify(MainItem).CostEnergy+GetRogueItemAfterGlobalModify(SecondItem).CostEnergy);
		if (MainFill||SecondFill||AllFill)
		{
			if(OnItemRecoverStack.IsBound())
			{
				OnItemRecoverStack.Broadcast(SecondItem);
			}
		}
	}

	//UKismetSystemLibrary::PrintString(nullptr,"ItemEnergy:"+FString::SanitizeFloat(CurGlobalItemEnergy),true,true,FColor::Green,DeltaTime);
	
	return  true;
}
