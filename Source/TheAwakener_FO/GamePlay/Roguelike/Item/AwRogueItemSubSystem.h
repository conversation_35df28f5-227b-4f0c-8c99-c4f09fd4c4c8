// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Engine/DataTable.h"
#include "TheAwakener_FO/GamePlay/Item/ItemObj.h"
#include "Subsystems/GameInstanceSubsystem.h"
#include "TheAwakener_FO/GamePlay/Characters/Battle/BattleStructs.h"
#include "TheAwakener_FO/GamePlay/Roguelike/Map/RogueMapConfig.h"
#include "Containers/Ticker.h"
#include "AwRogueItemSubSystem.generated.h"

USTRUCT(BlueprintType)
struct FAwRogueItemPropModify
{
	GENERATED_BODY()
	// Max
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float MaxEnergy = 0;
	// 用一次消耗多少
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float CostEnergy = 0;
	// 每秒回多少
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float EnergyRecovery = 0;
	// 当前效果等级
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int CurEffectLevel = 0.f;
	// 最大效果等级
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int MaxEffectLevel = 0;
	// 当前效果值
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float CurEffectValue = 0.f;
public:
	static  FAwRogueItemPropModify  Normalize();
	static  FAwRogueItemPropModify  Zero();

	//重写运算符
	FAwRogueItemPropModify operator+(const FAwRogueItemPropModify& Other) const
	{
		FAwRogueItemPropModify Res = FAwRogueItemPropModify();
		Res.MaxEnergy = this->MaxEnergy + Other.MaxEnergy;
		Res.CostEnergy = this->CostEnergy + Other.CostEnergy;
		Res.EnergyRecovery = this->EnergyRecovery + Other.EnergyRecovery;
		Res.CurEffectLevel = this->CurEffectLevel + Other.CurEffectLevel;
		Res.MaxEffectLevel = this->MaxEffectLevel + Other.MaxEffectLevel;
		Res.CurEffectValue = this->CurEffectValue + Other.CurEffectValue;
		return Res;
	}
	//重写运算符
	FAwRogueItemPropModify operator - (const FAwRogueItemPropModify& Other) const
	{
		FAwRogueItemPropModify Res = FAwRogueItemPropModify();
		Res.MaxEnergy = this->MaxEnergy - Other.MaxEnergy;
		Res.CostEnergy = this->CostEnergy - Other.CostEnergy;
		Res.EnergyRecovery = this->EnergyRecovery - Other.EnergyRecovery;
		Res.CurEffectLevel = this->CurEffectLevel - Other.CurEffectLevel;
		Res.MaxEffectLevel = this->MaxEffectLevel - Other.MaxEffectLevel;
		Res.CurEffectValue = this->CurEffectValue - Other.CurEffectValue;
		return Res;
	}
	
	//重写运算符
	FAwRogueItemPropModify& operator+=(const FAwRogueItemPropModify& Other) 
	{
		this->MaxEnergy += Other.MaxEnergy;
		this->CostEnergy += Other.CostEnergy;
		this->EnergyRecovery += Other.EnergyRecovery;
		this->CurEffectLevel += Other.CurEffectLevel;
		this->MaxEffectLevel += Other.MaxEffectLevel;
		this->CurEffectValue += Other.CurEffectValue;
		return *this;
	}
};


USTRUCT(BlueprintType)
struct FAwRogueItemEffect
{
	GENERATED_BODY()
public:
	//效果Tag 用于分组触发
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FString> EffectTags;
	//生效最小等级
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int EffectMinLevel = 1;
	//生效最大等级
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int EffectMaxLevel = 99;
	
	//效果脚本 包括触发性效果和持续性效果
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString Effect;

	//失效脚本 用于持续性效果的移除回收
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString LoseEffect;
	
	//额外传参 效果对象 (它具体怎么解析 由各个目标组成)
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TMap<FString,FString> SpecialParams;

	static FAwRogueItemEffect FromJson(TSharedPtr<FJsonObject> JsonObj);
};


USTRUCT(BlueprintType)
struct FAwRogueItemEffects
{
	GENERATED_BODY()
public:
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FAwRogueItemEffect> Effects;
};


USTRUCT(BlueprintType)
struct FAwRogueItemAttributeUpgrade
{
	GENERATED_BODY()
public:
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int Level = 0;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int MaxEnergyLevelUp = 0;

	static FAwRogueItemAttributeUpgrade FromJson(TSharedPtr<FJsonObject> JsonObj);
};


USTRUCT(BlueprintType)
struct FAwRogueItemInfo : public FTableRowBase
{
	GENERATED_USTRUCT_BODY()
	FAwRogueItemInfo();
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
		FItemObj  Item;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
		FString  UID = "";
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
		TArray<FString> Tags;
	// Max
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
		int MaxEnergy = 1;
	// Current
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
		float CurEnergy = 1;
	// 用一次消耗多少
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
		int CostEnergy = 1;
	// 每秒回多少
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
		int EnergyRecovery = 1;
	// 召唤物最大数量 超过最大数量则替代最早的召唤物
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
		int CreaterMaxNum = 1;
	// 初始效果等级
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
		int OrgEffectLevel = 1;
	// 当前效果等级
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
		int CurEffectLevel = 1;
	// 最大效果等级
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
		int MaxEffectLevel = 1;
	// 初始效果值
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
		int OrgEffectValue = 1;
	// 当前效果值
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
		int CurEffectValue = 1;
	// 价值
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
		float Value = 1;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TMap<int,FAwRogueItemAttributeUpgrade>LevelUpAttributes;
	
	UPROPERTY()
	TMap<FString,FAwRogueItemEffects> OnUseEffects;

	UPROPERTY()
	TMap<FString,FAwRogueItemEffects> OnUsingEffects;
	
	//以上是当前基于元数据的活动值
	//修改的值变化
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FAwRogueItemPropModify ModifyValue;
	//修改的倍率变化
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FAwRogueItemPropModify ModifyPower;
	//生成物子集
		TArray<TWeakObjectPtr<AActor>> Creaters;
public:
	int GetCanUseNum(){return CurEnergy/CostEnergy;}
	static FAwRogueItemInfo FromJson(TSharedPtr<FJsonObject> JsonObj);
	FAwRogueItemInfo GetSelfAfterModify();
	void ResetModify();
	bool CanBeUse();
};

DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FUseRogueItemDelegate,FAwRogueItemInfo,ItemInfo);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FRogueItemUpgradeDelegate,FAwRogueItemInfo,ItemInfo,int,LevelChange);

UCLASS()
class THEAWAKENER_FO_API UAwRogueItemSubSystem : public UGameInstanceSubsystem
{
	GENERATED_BODY()
public:
	
	virtual  void Initialize(FSubsystemCollectionBase& Collection) override;
	void InitSubSystem();
	//存读档
	void SaveData();
	void LoadSaveData();
	void ReApplySaveDataModify();
	
	UPROPERTY(BlueprintReadOnly,EditAnywhere)
	FAwRogueItemInfo CurHealingPotion;

	FAwRogueItemInfo MainItem = FAwRogueItemInfo();
	FAwRogueItemInfo SecondItem = FAwRogueItemInfo();
	
	//获取当前可用的道具
	UFUNCTION(BlueprintPure)
		FAwRogueItemInfo GetCurRogueItem(bool bMainItem = true){  return bMainItem?MainItem:SecondItem;}

	UPROPERTY(BlueprintReadOnly,EditAnywhere)
	float MaxGlobalItemEnergy = 0;

	UPROPERTY(BlueprintReadOnly,EditAnywhere)
	float CurGlobalItemEnergy = 0;
	
	//全局修改的值变化  例如所有道具充能加快XXX 所有道具效果增加XXX
	FAwRogueItemPropModify GlobalItemModifyValue = FAwRogueItemPropModify::Zero();
	//全局修改的倍率变化 例如所有道具充能加快XXX 所有道具效果增加XXX
	FAwRogueItemPropModify GlobalItemModifyPower = FAwRogueItemPropModify::Normalize();
	
	TMap<FString,FAwRogueItemInfo> AllRogueItemMap;
	TArray<FString> GetableRogueItems;

	//获取所有Rogue的主动道具
	UFUNCTION(BlueprintCallable)
	TArray<FAwRogueItemInfo> GetAllRogueActiveItem();
	//同步道具的属性修改 如果每个道具Buff等属性独立则不要调这个函数
		void ApplyItemModifyToAllItem(FString ItemId);
	//升级或降级所有道具槽位等级
	UFUNCTION(BlueprintCallable)
		bool UpgradeAllItem(int Level);
	// 升级或者降级携带目标道具等级	
	UFUNCTION(BlueprintCallable)
		bool UpgradeItem(int Level,bool bMainItem = true);
	//获取同步下的道具属性修改
	UFUNCTION(BlueprintPure)
		void GetCurModify(FAwRogueItemPropModify& ValueModify,FAwRogueItemPropModify& PowerModify );
	//获取是是否全局修改后的道具属性
	UFUNCTION(BlueprintPure)
		FAwRogueItemInfo GetRogueItemAfterGlobalModify(FAwRogueItemInfo SourceItem);
	//还原一切道具至元数据
	UFUNCTION(BlueprintCallable)
		void ResetAllItemToMeta();
	//修改道具属性
	UFUNCTION(BlueprintCallable)
		bool ModifyRogueItem(FString Id,FAwRogueItemInfo NewInfo,bool ApplyGlobal = false);
	//获取随机数量的道具
	UFUNCTION(BlueprintCallable)
		TMap<FString,int> GetRandomItem(int Number, int RoomLevel = 1,ERogueRoomType RoomType = ERogueRoomType::Normal);
	//获取某个道具的随机等级
	UFUNCTION(BlueprintCallable)
		int GeItemRandomLevel(FString ItemId,int RoomLevel,ERogueRoomType RoomType);
	UFUNCTION(BlueprintCallable)
		void FullyRestorePotion();
	//重置当前道具召唤物
	UFUNCTION(BlueprintCallable)
		void ReSetCurItemCreater(bool bMainItem = true);
	//重置当前道具
	UFUNCTION(BlueprintCallable)
		void ReSetCurItem(bool bMainItem = true);
	//Get And Set
	//设置玩家主动道具
	UFUNCTION(BlueprintCallable)
		void SetCurRogueItem(FString ItemId,int level = 1,bool bMainItem = true);

	UFUNCTION(BlueprintCallable)
		FAwRogueItemInfo GetRogueItemById(FString Id);
	//获取修正后的道具信息
	UFUNCTION(BlueprintCallable,BlueprintPure)
		FAwRogueItemInfo GetItemInfoAfterModify(FAwRogueItemInfo Source){return  Source.GetSelfAfterModify();}
	//Use
	//使用肉鸽主动道具的动作 如果Main为true 则为使用主槽位
	UFUNCTION(BlueprintCallable)
		void UseRogueItemAction(const int PlayerIndex,bool bUseMain = true);
	UFUNCTION(BlueprintCallable)
		bool UseHealingPotionAction(const int PlayerIndex);
	UFUNCTION(BlueprintCallable)
		bool UseCurRogueItem(TArray<FString> UseMethodTags,const int PlayerIndex,bool bCost =true);
	UFUNCTION(BlueprintCallable)
		bool UseRogueItemById(TArray<FString> UseMethodTags,FString Id,const int PlayerIndex,bool bCost = true);
	
	UFUNCTION(BlueprintCallable)
		bool OnUsingCurRogueItem(TArray<FString> UseMethodTags,const int PlayerIndex);
	UFUNCTION(BlueprintCallable)
		bool OnUsingCurRogueItemEnd(TArray<FString> UseMethodTags,const int PlayerIndex);

	void AddGlobalItemRecoverByDamageInfo(FDamageInfo NewDamageInfo);
	
	//Event
	UPROPERTY(BlueprintAssignable)
		FUseRogueItemDelegate OnUseRogueItem;
	UPROPERTY(BlueprintAssignable)
		FRogueItemUpgradeDelegate OnItemUpgrade;
	UPROPERTY(BlueprintAssignable)
		FUseRogueItemDelegate PlayGetUseRogueItem;
	UPROPERTY(BlueprintAssignable)
	FUseRogueItemDelegate OnItemRecoverStack;
private:
		bool Tick(float DeltaTime);
	
		FAwRogueItemInfo* LastOnUsingItem = nullptr;
	
	FTSTicker::FDelegateHandle TickHandle;
};



