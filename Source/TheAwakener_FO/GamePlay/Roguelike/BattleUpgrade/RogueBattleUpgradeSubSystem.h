// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "RogueBattleUpgrade.h"
#include "TheAwakener_FO/GameFramework/Manager/AwDataManager.h"
#include "TheAwakener_FO/GameFramework/Manager/AwRogueDataSystem.h"
#include "Subsystems/GameInstanceSubsystem.h"
#include "RogueBattleUpgradeSubSystem.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API URogueBattleUpgradeSubSystem : public UGameInstanceSubsystem
{
	GENERATED_BODY()

private:
	UPROPERTY()
	UAwGameInstance* GameInstance;
	UPROPERTY()
	UAwRogueDataSystem* DataSystem;
	UPROPERTY()
	UAwDataManager* DataManager;

	static void AddActions(FRogueBattleAbilityInfo Info, FString Cmd,const int PlayerIndex);
	static void RemoveActions(FRogueBattleAbilityInfo Info, FString Cmd,const int PlayerIndex);
	FString SetAbilityAction(ERogueAbilitySlot AbilitySlot, FString AbilityId,const int PlayerIndex) const;

	// 筛选出列表中符合某些 AbilitySlot 技能插槽 的技能Id
	static TArray<FString> GetAbilitiesBySlot(TArray<FRogueBattleAbilityInfo> Infos, ERogueAbilitySlot AbilitySlot);
	
	// BattleUpgrade - 表数据
	// ERogueAbilitySlot - 技能插槽
	// ExclusionList - 剔除列表
	// Count - 随机几个？
	static TArray<FString> GetRandomAbilitiesBySlot(
		FRogueBattleUpgrade BattleUpgrade, ERogueAbilitySlot AbilitySlot,
		TArray<FString> ExclusionList, int Count);

	static TMap<FString, int> GetRandomPool(TArray<FRougeAbilityLevelInfo> Infos, FAbilityLevelWeight WeightInfo);
	
	UFUNCTION(BlueprintPure)
	TArray<FRougeAbilityLevelInfo> GetCanRollAbilityInfos_LevelUp(const FString& PawnClassId);
	
	UFUNCTION(BlueprintPure)
	TArray<FRougeAbilityLevelInfo> GetCanRollAbilityInfos_All(const FString& PawnClassId);
	
	UFUNCTION(BlueprintPure)
	TArray<FRougeAbilityLevelInfo> GetCanRollAbilityInfo_ById(const FString& AbilityId,const FString& PawnClassId);

public:
	void InitSubSystem();
	
	UFUNCTION(BlueprintCallable)
	TArray<FRougeAbilityLevelInfo> GetThreeRandomAbilityLevelInfo(int RoomStep, ERogueRoomType RoomType,const FString& PawnClassId);

	UFUNCTION(BlueprintPure)
	FRougeAbilityLevelInfo GetCurAbilityLevelInfo(ERogueAbilitySlot AbilityType,const FString& PawnClassId) const;
	UFUNCTION(BlueprintPure)
	FString GetCurAbilityId(ERogueAbilitySlot AbilitySlot,const FString& PawnClassId) const;
	
	UFUNCTION(BlueprintPure)
	TArray<FString> GetAbilityIdsByAbilitySlot(const FString& PawnClassId,ERogueAbilitySlot AbilitySlot = ERogueAbilitySlot::None);
	
	// 设置所有技能 --- 弃用
	UFUNCTION(BlueprintCallable)
	void SetAbilities(const int PlayerIndex,FBattleAbilityIds AbilityIds, bool DoSave = true);
	// 设置单个技能 --- 弃用
	UFUNCTION(BlueprintCallable)
	FString SetAbility(const int PlayerIndex,ERogueAbilitySlot AbilitySlot, FString AbilityId, bool DoSave = true);
	
	// 根据 LeveL 信息
	UFUNCTION(BlueprintCallable)
	FString SetAbilityInfo(int PlayerIndex,FRougeAbilityLevelInfo AbilityLevelInfo, bool DoSave = true);
	UFUNCTION(BlueprintCallable)
	void SetAbilityInfos(int PlayerIndex,TMap<ERogueAbilitySlot, FRougeAbilityLevelInfo> AbilityLevelInfos, bool DoSave = true);

	UFUNCTION(BlueprintCallable)
	FString SetPreviewAbilityInfo(const int PlayerIndex,FRougeAbilityLevelInfo AbilityLevelInfo, bool DoSave = true);
	UFUNCTION(BlueprintCallable)
	void SettPreviewAbilityInfos(const int PlayerIndex,TMap<ERogueAbilitySlot, FRougeAbilityLevelInfo> AbilityLevelInfos, bool DoSave = true);
	
	void RefreshCurActionLv3Buffs(const int PlayerIndex);

	static void RemoveActionLv3Buff(const int PlayerIndex);
	void AddActionLv3BuffBySlot(ERogueAbilitySlot Slot,const int& PlayerIndex) const;
	
	UFUNCTION(BlueprintCallable)
	void OnClearBattle() const;

	// *** 已弃用，返回map为空 ***
	// 获取当角色所有的技能的Tag
	// Return-Key：战斗Tag
	// Return-Int：有几个技能有个这个Tag
	UFUNCTION(BlueprintPure, Category="RogueBattleStyle")
	static TMap<ERogueBattleTag, int> GetCurClassTags();
};
