// Fill out your copyright notice in the Description page of Project Settings.


#include "RogueBattleUpgrade.h"

#include "TheAwakener_FO/FunctionLibrary/DataFuncLib.h"

FRogueBattleAbilityInfo FRogueBattleAbilityInfo::FromJson(TSharedPtr<FJsonObject> JsonObj)
{
	FRogueBattleAbilityInfo Res;

	Res.Id = UDataFuncLib::AwGetStringField(JsonObj, "Id", "");
	Res.Name = UDataFuncLib::AwGetStringField(JsonObj, "Name", "");
	Res.AbilitySlot = UDataFuncLib::AwGetEnumField<ERogueAbilitySlot>(JsonObj, "AbilitySlot", ERogueAbilitySlot::None);
	Res.VideoPath = UDataFuncLib::AwGetStringField(JsonObj, "VideoPath", "");
	Res.ActionIds = UDataFuncLib::AwGetStringArrayField(JsonObj, "ActionIds");
	Res.CoreTag = UDataFuncLib::AwGetEnumField<ERogueBattleTag>(JsonObj, "CoreTag", ERogueBattleTag::None);
	Res.Desc = UDataFuncLib::AwGetStringField(JsonObj, "Desc", "");
	for (TSharedPtr<FJsonValue> ArrayField : UDataFuncLib::AwGetArrayField(JsonObj, "AtkScale"))
		Res.AtkScale.Add(ArrayField.Get()->AsNumber());
	for (TSharedPtr<FJsonValue> ArrayField : UDataFuncLib::AwGetArrayField(JsonObj, "BreakScale"))
		Res.BreakScale.Add(ArrayField.Get()->AsNumber());
	Res.ActionLv3Buffs = UDataFuncLib::AwGetStringField(JsonObj, "ActionLv3Buffs", "ActionLv3Buff_1");
	
	return Res;
}

FRogueBattleUpgradeInfo FRogueBattleUpgradeInfo::FromJson(TSharedPtr<FJsonObject> JsonObj)
{
	FRogueBattleUpgradeInfo Res;

	Res.Index = UDataFuncLib::AwGetNumberField(JsonObj, "Index", 0);
	Res.Id = UDataFuncLib::AwGetStringField(JsonObj, "Id", "");
	Res.PreId = UDataFuncLib::AwGetStringField(JsonObj, "PreId", "");
	Res.Desc = UDataFuncLib::AwGetStringField(JsonObj, "Desc", "");
	Res.Icon = UDataFuncLib::AwGetStringField(JsonObj, "Icon", "");
	Res.UpgradeType = UDataFuncLib::AwGetEnumField<EUpgradeType>(JsonObj, "UpgradeType", EUpgradeType::ObtainAction);
	Res.bOnlyforCareer = UDataFuncLib::AwGetBoolField(JsonObj, "OnlyForCareer", false);
	Res.Weight = UDataFuncLib::AwGetNumberField(JsonObj, "Weight", 1);
	Res.MaxLevel = UDataFuncLib::AwGetNumberField(JsonObj, "MaxLevel", 1);
	for (const TSharedPtr<FJsonValue> Value : UDataFuncLib::AwGetArrayField(JsonObj, "Func"))
		Res.Func.Add(UDataFuncLib::SplitFuncNameAndParams(Value->AsString()));

	Res.BuffId = UDataFuncLib::AwGetStringField(JsonObj, "BuffId", "");
	for (const TSharedPtr<FJsonValue> Value : UDataFuncLib::AwGetArrayField(JsonObj, "BuffStack"))
		Res.BuffStack.Add(Value->AsNumber());
	
	return Res;
}

bool FRogueBattleUpgrade::HasUpgradeId(FString UpgradeId)
{
	for (FRogueBattleUpgradeInfo Info : UpgradeInfos)
		if (Info.Id == UpgradeId)
			return true;

	return false;
}

FRogueBattleAbilityInfo FRogueBattleUpgrade::GetAbilityInfo(FString AbilityId)
{
	for (FRogueBattleAbilityInfo Info : AbilityInfos)
		if (Info.Id == AbilityId)
			return Info;
	return FRogueBattleAbilityInfo();
}

FRogueBattleUpgradeInfo FRogueBattleUpgrade::GetUpgradeInfo(FString UpgradeId)
{
	for (FRogueBattleUpgradeInfo Info : UpgradeInfos)
		if (Info.Id == UpgradeId)
			return Info;
	return FRogueBattleUpgradeInfo();
}

FRogueBattleUpgrade FRogueBattleUpgrade::FromJson(TSharedPtr<FJsonObject> JsonObj)
{
	FRogueBattleUpgrade Res;

	Res.Id = UDataFuncLib::AwGetStringField(JsonObj, "Id", "");
	Res.DefaultNormalAttack = UDataFuncLib::AwGetStringField(JsonObj, "DefaultNormalAttack", "");
	Res.DefaultGround1 = UDataFuncLib::AwGetStringField(JsonObj, "DefaultGround1", "");
	Res.DefaultGround2 = UDataFuncLib::AwGetStringField(JsonObj, "DefaultGround2", "");
	Res.DefaultAir1 = UDataFuncLib::AwGetStringField(JsonObj, "DefaultAir1", "");
	Res.DefaultAir2 = UDataFuncLib::AwGetStringField(JsonObj, "DefaultAir2", "");

	if (JsonObj->HasField("AbilityInfos")) 
		for (const TSharedPtr<FJsonValue, ESPMode::ThreadSafe> JsonValue : JsonObj->GetArrayField("AbilityInfos"))
			Res.AbilityInfos.Add(FRogueBattleAbilityInfo::FromJson(JsonValue->AsObject()));

	if (JsonObj->HasField("UpgradeInfos")) 
		for (const TSharedPtr<FJsonValue, ESPMode::ThreadSafe> JsonValue : JsonObj->GetArrayField("UpgradeInfos"))
			Res.UpgradeInfos.Add(FRogueBattleUpgradeInfo::FromJson(JsonValue->AsObject()));
	
	return Res;
}
