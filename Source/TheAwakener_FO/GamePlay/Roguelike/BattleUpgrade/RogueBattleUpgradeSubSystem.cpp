// Fill out your copyright notice in the Description page of Project Settings.


#include "RogueBattleUpgradeSubSystem.h"

#include "Elements/Framework/TypedElementSelectionSet.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"

void URogueBattleUpgradeSubSystem::AddActions(FRogueBattleAbilityInfo Info, FString Cmd,const int PlayerIndex)
{
	const AAwCharacter* Me = UGameplayFuncLib::GetLocalAwPlayerCharacter(PlayerIndex);
	if (!Me) return;
	UActionComponent* ActionComp = Me->GetActionComponent();
	if (!ActionComp) return;

	for (const FString ActionId : Info.ActionIds)
		ActionComp->AddActionWithCmdById(ActionId, Cmd);
}

void URogueBattleUpgradeSubSystem::RemoveActions(FRogueBattleAbilityInfo Info, FString Cmd, const int PlayerIndex)
{
	const AAwCharacter* Me = UGameplayFuncLib::GetLocalAwPlayerCharacter(PlayerIndex);
	if (!Me) return;
	UActionComponent* ActionComp = Me->GetActionComponent();
	if (!ActionComp) return;

	for (const FString ActionId : Info.ActionIds)
		ActionComp->RemoveActionWithCmdById(ActionId, Cmd);
}

FString URogueBattleUpgradeSubSystem::SetAbilityAction(ERogueAbilitySlot AbilitySlot, FString AbilityId,const int PlayerIndex) const
{
	FString LastAbilityId;
	const FString CurPawnClassId = DataSystem->GetCurPawnClassId(PlayerIndex);
	LastAbilityId = GetCurAbilityId(AbilitySlot,CurPawnClassId);
	
	if (AbilitySlot == ERogueAbilitySlot::None)
	{
		UKismetSystemLibrary::PrintString(this, "这个Slot不能切换");
		return LastAbilityId;
	}
	
	
	const FRogueBattleAbilityInfo LastInfo = DataManager->GetRogueBattleAbilityInfo(CurPawnClassId, LastAbilityId);
	const FRogueBattleAbilityInfo NewInfo = DataManager->GetRogueBattleAbilityInfo(CurPawnClassId, AbilityId);

	FString ActionCmd = "";
	switch (AbilitySlot)
	{
	case ERogueAbilitySlot::NormalAttack: ActionCmd = "NormalAttack"; break;
	case ERogueAbilitySlot::Ground1: ActionCmd = "Ability1"; break;
	case ERogueAbilitySlot::Ground2: ActionCmd = "Ability2"; break;
	case ERogueAbilitySlot::Air1: ActionCmd = "Ability1"; break;
	case ERogueAbilitySlot::Air2: ActionCmd = "Ability2"; break;
	default: break;
	}
	
	RemoveActions(LastInfo, ActionCmd, PlayerIndex);
	AddActions(NewInfo, ActionCmd, PlayerIndex);

	return LastAbilityId;
}

TArray<FString> URogueBattleUpgradeSubSystem::GetAbilitiesBySlot(TArray<FRogueBattleAbilityInfo> Infos,
                                                                 ERogueAbilitySlot AbilitySlot)
{
	TArray<FString> Res;
	for (FRogueBattleAbilityInfo Info :Infos)
		if (Info.AbilitySlot == AbilitySlot)
			Res.Add(Info.Id);
	
	return Res;
}

TArray<FString> URogueBattleUpgradeSubSystem::GetRandomAbilitiesBySlot(FRogueBattleUpgrade BattleUpgrade,
	ERogueAbilitySlot AbilitySlot, TArray<FString> ExclusionList, int Count)
{
	TMap<FString, int> RandomPool;
	for (FString Id : GetAbilitiesBySlot(BattleUpgrade.AbilityInfos, AbilitySlot))
	{
		if (AbilitySlot == ERogueAbilitySlot::NormalAttack)
			RandomPool.Add(Id, 1);
		else
			if (BattleUpgrade.HasUpgradeId(Id) && !ExclusionList.Contains(Id))
				RandomPool.Add(Id, 1);
	}
	return UMathFuncLib::GetRandomIdInPool(RandomPool, Count);
}

TMap<FString, int> URogueBattleUpgradeSubSystem::GetRandomPool(TArray<FRougeAbilityLevelInfo> Infos,
	FAbilityLevelWeight WeightInfo)
{
	TMap<FString, int> RandomPool;
	for (int i = 0; i < Infos.Num(); ++i)
	{
		const int Level = Infos[i].Level;
		int Weight = 1;
		if (Level == 1) Weight = WeightInfo.Lv1;
		if (Level == 2) Weight = WeightInfo.Lv2;
		if (Level == 3) Weight = WeightInfo.Lv3;
		
		RandomPool.Add(FString::FromInt(i), Weight);
	}
	return RandomPool;
}

TArray<FRougeAbilityLevelInfo> URogueBattleUpgradeSubSystem::GetThreeRandomAbilityLevelInfo(int RoomStep, ERogueRoomType RoomType,const FString& PawnClassId)
{
	TArray<FRougeAbilityLevelInfo> Res;
	
	FAbilityLevelWeight WeightInfo = FAbilityLevelWeight();
	TArray<FAbilityLevelWeight> Weights = DataManager->GetAbilityLevelWeight();
	for (FAbilityLevelWeight Weight : Weights)
	{
		if(RoomStep >= Weight.MinLevel && RoomStep <= Weight.MaxLevel && RoomType == Weight.RoomType)
		{
			WeightInfo = Weight;
			break;
		}
	}
	// if (RoomStep <= 10 && Weights.Num() >= 1)
	// 	WeightInfo = Weights[0];
	// else if (RoomStep <= 20 && Weights.Num() >= 2)
	// 	WeightInfo = Weights[1];
	// else if (Weights.Num() >= 3)
	// 	WeightInfo = Weights[2];

	// 随升级
	//TArray<FRougeAbilityLevelInfo> CanRollInfos = GetCanRollAbilityInfos_LevelUp();
	//全随 1
	TArray<FRougeAbilityLevelInfo> CanRollInfos = GetCanRollAbilityInfos_All(PawnClassId);
	TArray<FString> RandomIndex = UMathFuncLib::GetRandomIdInPool(GetRandomPool(CanRollInfos, WeightInfo), 1);
	if (RandomIndex.Num() > 0)
	{
		const int i = FCString::Atoi(*RandomIndex[0]);
		Res.Add(CanRollInfos[i]);
		if (CanRollInfos[i].Level == 3)
			WeightInfo.Lv3 = FMath::CeilToInt(WeightInfo.Lv3 * 0.5);
	}

	// 全随 2
	CanRollInfos = GetCanRollAbilityInfos_All(PawnClassId);
	for (FRougeAbilityLevelInfo Info : Res)
		if (CanRollInfos.Contains(Info))
			CanRollInfos.Remove(Info);
	RandomIndex = UMathFuncLib::GetRandomIdInPool(GetRandomPool(CanRollInfos, WeightInfo), 1);
	if (RandomIndex.Num() > 0)
	{
		const int i = FCString::Atoi(*RandomIndex[0]);
		Res.Add(CanRollInfos[i]);
		if (CanRollInfos[i].Level == 3)
			WeightInfo.Lv3 = FMath::CeilToInt(WeightInfo.Lv3 * 0.2);
	}

	// 全随 3
	for (FRougeAbilityLevelInfo Info : Res)
		if (CanRollInfos.Contains(Info))
			CanRollInfos.Remove(Info);
	RandomIndex = UMathFuncLib::GetRandomIdInPool(GetRandomPool(CanRollInfos, WeightInfo), 1);
	if (RandomIndex.Num() > 0)
	{
		const int i = FCString::Atoi(*RandomIndex[0]);
		Res.Add(CanRollInfos[i]);
	}
	
	return Res;
}

TArray<FRougeAbilityLevelInfo> URogueBattleUpgradeSubSystem::GetCanRollAbilityInfos_LevelUp(const FString& PawnClassId)
{
	TArray<FRougeAbilityLevelInfo> Res;
	
	for (TTuple<ERogueAbilitySlot, FRougeAbilityLevelInfo> CurInfo : DataSystem->GetCurAbilityLevelInfos(PawnClassId))
		Res.Append(GetCanRollAbilityInfo_ById(CurInfo.Value.AbilityInfoId,PawnClassId));

	return Res;
}

TArray<FRougeAbilityLevelInfo> URogueBattleUpgradeSubSystem::GetCanRollAbilityInfos_All(const FString& PawnClassId)
{
	TArray<FRougeAbilityLevelInfo> Res;
	
	const FRogueBattleUpgrade BattleUpgrade = DataManager->GetRogueBattleUpgrade(PawnClassId);
	for (FRogueBattleAbilityInfo AbilityInfo : BattleUpgrade.AbilityInfos)
		Res.Append(GetCanRollAbilityInfo_ById(AbilityInfo.Id,PawnClassId));

	return Res;
}

// 添加所有元素变体的辅助函数
auto AddAllElements = [&](FRougeAbilityLevelInfo& Info, TArray<FRougeAbilityLevelInfo>& ResultArray)
{
	const TArray<EAbilityElement> Elements = {
		EAbilityElement::Fire,
		EAbilityElement::Ice,
		EAbilityElement::Wind,
		EAbilityElement::Thunder,
		EAbilityElement::Light,
		EAbilityElement::Darkness
	};

	for (EAbilityElement Element : Elements)
	{
		Info.Element = Element;
		ResultArray.Add(Info);
	}
};

// 添加除当前元素外的其他元素变体的辅助函数
auto AddElementsExcludingCurrent = [&](FRougeAbilityLevelInfo& Info, TArray<FRougeAbilityLevelInfo>& ResultArray, EAbilityElement CurrentElement)
{
	const TArray<EAbilityElement> Elements = {
		EAbilityElement::Fire,
		EAbilityElement::Ice,
		EAbilityElement::Wind,
		EAbilityElement::Thunder,
		EAbilityElement::Light,
		EAbilityElement::Darkness
	};

	for (EAbilityElement Element : Elements)
	{
		if (Element != CurrentElement)
		{
			Info.Element = Element;
			ResultArray.Add(Info);
		}
	}
};

TArray<FRougeAbilityLevelInfo> URogueBattleUpgradeSubSystem::GetCanRollAbilityInfo_ById(const FString& AbilityId,const FString& PawnClassId)
{
	TArray<FRougeAbilityLevelInfo> Res;
	FRougeAbilityLevelInfo TempInfo;
	TempInfo.AbilityInfoId = AbilityId;

	bool HasLevelInfo = false;
	for (TTuple<ERogueAbilitySlot, FRougeAbilityLevelInfo> CurInfo : DataSystem->GetCurAbilityLevelInfos(PawnClassId))
		if (CurInfo.Value.AbilityInfoId == AbilityId)
		{
			HasLevelInfo = true;
			TempInfo = CurInfo.Value;
			break;
		}

	const ERogueAbilitySlot Slot = DataManager->GetRogueBattleAbilityInfo(PawnClassId, AbilityId).AbilitySlot;
	if (Slot == ERogueAbilitySlot::None)
		return Res;
	if (HasLevelInfo)
	{
		if (TempInfo.Level == 0)
		{
			TempInfo.Level = 1;
			AddAllElements(TempInfo, Res);
		}
		if (TempInfo.Level <= 1)
		{
			TempInfo.Level = 2;
			AddAllElements(TempInfo, Res);
		}
		if (TempInfo.Level <= 2)
		{
			TempInfo.Level = 3;
			AddAllElements(TempInfo, Res);
		}
		else if (TempInfo.Level == 3)
		{
			// Level 3 -> 其他元素版本（排除当前元素）
			const EAbilityElement OldElement = TempInfo.Element;
			AddElementsExcludingCurrent(TempInfo, Res, OldElement);
		}
	}
	else
	{
		const int Level = DataSystem->GetCurAbilityLevelInfo(Slot,PawnClassId).Level;
		for (int i = FMath::Clamp(Level, 1, 3); i <= 3; i++)
		{
			TempInfo.Level = i;

			if (TempInfo.Level == 0 || TempInfo.Level == 1 || TempInfo.Level == 2)
				Res.Add(TempInfo);
			else if (TempInfo.Level == 3)
			{
				AddAllElements(TempInfo, Res);
			}
		}
	}
	
	return Res;
}

void URogueBattleUpgradeSubSystem::InitSubSystem()
{
	GameInstance = UGameplayFuncLib::GetAwGameInstance();
	DataSystem = GameInstance->GetSubsystem<UAwRogueDataSystem>();
	DataManager = UGameplayFuncLib::GetDataManager();
}

FRougeAbilityLevelInfo URogueBattleUpgradeSubSystem::GetCurAbilityLevelInfo(ERogueAbilitySlot AbilityType,const FString& PawnClassId) const
{
	return DataSystem->GetCurAbilityLevelInfo(AbilityType,PawnClassId);
}

FString URogueBattleUpgradeSubSystem::GetCurAbilityId(ERogueAbilitySlot AbilitySlot,const FString& PawnClassId) const
{
	return GetCurAbilityLevelInfo(AbilitySlot,PawnClassId).AbilityInfoId;
}

TArray<FString> URogueBattleUpgradeSubSystem::GetAbilityIdsByAbilitySlot(const FString& PawnClassId,ERogueAbilitySlot AbilitySlot)
{
	const FRogueBattleUpgrade BattleUpgrade = DataManager->GetRogueBattleUpgrade(PawnClassId);
	return GetAbilitiesBySlot(BattleUpgrade.AbilityInfos, AbilitySlot);
}

void URogueBattleUpgradeSubSystem::SetAbilities(const int PlayerIndex,FBattleAbilityIds AbilityIds, bool DoSave)
{
	FBattleAbilityIds LastAbilityIds;
	LastAbilityIds.NormalAttack = SetAbility(PlayerIndex,ERogueAbilitySlot::NormalAttack, AbilityIds.NormalAttack, false);
	LastAbilityIds.Ground1 = SetAbility(PlayerIndex,ERogueAbilitySlot::Ground1, AbilityIds.Ground1, false);
	LastAbilityIds.Ground2 = SetAbility(PlayerIndex,ERogueAbilitySlot::Ground2, AbilityIds.Ground2, false);
	LastAbilityIds.Air1 = SetAbility(PlayerIndex,ERogueAbilitySlot::Air1, AbilityIds.Air1, false);
	LastAbilityIds.Air2 = SetAbility(PlayerIndex,ERogueAbilitySlot::Air2, AbilityIds.Air2, false);

	if (DoSave)
		UGameplayFuncLib::SaveGame();
}

FString URogueBattleUpgradeSubSystem::SetAbility(const int PlayerIndex,ERogueAbilitySlot AbilitySlot, FString AbilityId, bool DoSave)
{
	FString LastAbilityId = SetAbilityAction(AbilitySlot, AbilityId,PlayerIndex);
	
	DataSystem->SetCurBattleAbilityId(AbilitySlot, AbilityId, PlayerIndex);
	
	if (DoSave)
		DataSystem->SaveData();

	return LastAbilityId;
}

FString URogueBattleUpgradeSubSystem::SetAbilityInfo(int PlayerIndex,FRougeAbilityLevelInfo AbilityLevelInfo, bool DoSave)
{
	const FString PawnClassId = DataSystem->GetCurPawnClassId(PlayerIndex);
	const FRogueBattleAbilityInfo AbilityInfo = DataManager->GetRogueBattleAbilityInfo(PawnClassId, AbilityLevelInfo.AbilityInfoId);
	
	FString LastAbilityId = SetAbilityAction(AbilityInfo.AbilitySlot, AbilityInfo.Id, PlayerIndex);

	DataSystem->SetCurAbilityLevelInfo(AbilityInfo.AbilitySlot, AbilityLevelInfo);
	
	if (DoSave)
	{
		DataSystem->SaveData();
		RefreshCurActionLv3Buffs(PlayerIndex);
	}
	
	return LastAbilityId;
}

void URogueBattleUpgradeSubSystem::SetAbilityInfos(int PlayerIndex,TMap<ERogueAbilitySlot, FRougeAbilityLevelInfo> AbilityLevelInfos, bool DoSave)
{
	const FString PawnClassId = DataSystem->GetCurPawnClassId(PlayerIndex);
	for (const TTuple<ERogueAbilitySlot, FRougeAbilityLevelInfo> Info : AbilityLevelInfos)
		SetAbilityInfo(PlayerIndex,Info.Value, false);

	RefreshCurActionLv3Buffs(PlayerIndex);

	if (DoSave)
		DataSystem->SaveData();
}

FString URogueBattleUpgradeSubSystem::SetPreviewAbilityInfo(const int PlayerIndex,FRougeAbilityLevelInfo AbilityLevelInfo, bool DoSave)
{
	const FString PawnClassId = DataSystem->GetCurPawnClassId(PlayerIndex);
	const FRogueBattleAbilityInfo AbilityInfo = DataManager->GetRogueBattleAbilityInfo(PawnClassId, AbilityLevelInfo.AbilityInfoId);
	
	TMap<ERogueAbilitySlot, FRougeAbilityLevelInfo> PreviewAbilityInfos = DataSystem->GetPreviewAbilityLevelInfos(PawnClassId);
	
	FString LastAbilityId = "";
	if(PreviewAbilityInfos.Contains(AbilityInfo.AbilitySlot))
		LastAbilityId = PreviewAbilityInfos[AbilityInfo.AbilitySlot].AbilityInfoId;

	DataSystem->SetPreviewAbilityLevelInfo(PlayerIndex,AbilityInfo.AbilitySlot, AbilityLevelInfo);
	
	if (DoSave)
	{
		DataSystem->SaveData();
	}
	
	return LastAbilityId;
}

void URogueBattleUpgradeSubSystem::SettPreviewAbilityInfos(const int PlayerIndex,
	TMap<ERogueAbilitySlot, FRougeAbilityLevelInfo> AbilityLevelInfos, bool DoSave)
{
	for (const TTuple<ERogueAbilitySlot, FRougeAbilityLevelInfo> Info : AbilityLevelInfos)
		SetPreviewAbilityInfo(PlayerIndex,Info.Value, false);

	if (DoSave)
		DataSystem->SaveData();
}

void URogueBattleUpgradeSubSystem::RefreshCurActionLv3Buffs(const int PlayerIndex)
{
	// remove old buff
	RemoveActionLv3Buff(PlayerIndex);
	// add new buff
	AddActionLv3BuffBySlot(ERogueAbilitySlot::NormalAttack,PlayerIndex);
	AddActionLv3BuffBySlot(ERogueAbilitySlot::Ground1,PlayerIndex);
	AddActionLv3BuffBySlot(ERogueAbilitySlot::Ground2,PlayerIndex);
	AddActionLv3BuffBySlot(ERogueAbilitySlot::Air1,PlayerIndex);
	AddActionLv3BuffBySlot(ERogueAbilitySlot::Air2,PlayerIndex);
}

void URogueBattleUpgradeSubSystem::RemoveActionLv3Buff(const int PlayerIndex)
{
	AAwCharacter* Me = UGameplayFuncLib::GetLocalAwPlayerCharacter(PlayerIndex);
	if (!Me) return;
	
	TArray<FString> WillToRemoveBuffs;
	for (FBuffObj Buff : Me->CharacterObj.Buff)
	{
		if (Buff.Model.Tags.Contains("ActionLv3"))
			WillToRemoveBuffs.Add(Buff.Model.Id);
	}
	for (const FString BuffId : WillToRemoveBuffs)
		Me->RemoveBuffById(BuffId);
}

void URogueBattleUpgradeSubSystem::AddActionLv3BuffBySlot(ERogueAbilitySlot Slot,const int& PlayerIndex) const
{
	AAwCharacter* Me = UGameplayFuncLib::GetAwPlayerController(PlayerIndex)->CurCharacter;
	if (!Me) return;
	const FString PawnClassId = Me->CharacterObj.ClassId;
	const FRougeAbilityLevelInfo AbilityLevelInfo = GetCurAbilityLevelInfo(Slot,PawnClassId);
	const FRogueBattleAbilityInfo AbilityInfo = DataManager->GetRogueBattleAbilityInfo(PawnClassId, AbilityLevelInfo.AbilityInfoId);
	if (AbilityLevelInfo.Level >= 1 && AbilityLevelInfo.Element != EAbilityElement::None)
	{
		FActionLv3Buffs ActionLv3Buff = DataManager->ActionLv3Buffs[AbilityInfo.ActionLv3Buffs];
		const FString BuffId = ActionLv3Buff.ElementBuff[AbilityLevelInfo.Element];
		FBuffModel BuffModel = DataManager->GetBuffModelById(BuffId);
		BuffModel.Tags.Add(AbilityInfo.Id);
		Me->AddBuff(FAddBuffInfo(Me, Me, BuffModel, 1, 1, false, true));
	}
}

void URogueBattleUpgradeSubSystem::OnClearBattle() const
{
	DataSystem->ClearGiveUpBattleAbilityIds();
	DataSystem->ClearBattleUpgradeIds();
	// DataSystem->EmptyCurBattleAbilityId();
	DataSystem->EmptyCurAbilityLevelInfos();

	for (auto PC :UGameplayFuncLib::GetAllLocalAwPlayerControllers())
	{
		if (PC)RemoveActionLv3Buff(PC->GetLocalPCIndex());
	}
}

TMap<ERogueBattleTag, int> URogueBattleUpgradeSubSystem::GetCurClassTags()
{
	TMap<ERogueBattleTag, int> Res;

	return Res;
}