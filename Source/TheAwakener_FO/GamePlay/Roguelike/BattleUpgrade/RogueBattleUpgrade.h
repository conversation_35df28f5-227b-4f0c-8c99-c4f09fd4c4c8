// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/FunctionLibrary/DataFuncLib.h"
#include "TheAwakener_FO/GamePlay/Roguelike/BattleStyle/RogueBattleTag.h"
#include "TheAwakener_FO/GamePlay/Roguelike/Map/RogueMapConfig.h"
#include "RogueBattleUpgrade.generated.h"

UENUM(BlueprintType)
enum class ERogueBattleAbilityType: uint8
{
	// 普攻
	NormalAttack,
	// 地面技能
	Ground,
	// 空中技能
	Air,
	// 其他
	Other
};

// 肉鸽技能类型，或者叫做肉鸽技能槽位
UENUM(BlueprintType)
enum class ERogueAbilitySlot: uint8
{
	// 没插上
	None,
	// 普攻。 ActionCmd:"NormalAttack"
	NormalAttack,
	// 地面技能1。 ActionCmd:"Ability1"
	Ground1,
	// 地面技能2。 ActionCmd:"Ability2"
	Ground2,
	// 空中技能1。 ActionCmd:"Ability1"
	Air1,
	// 空中技能2。 ActionCmd:"Ability2"
	Air2
};

// 技能的元素类型
UENUM(BlueprintType)
enum class EAbilityElement: uint8
{
	// 无
	None,
	
	// 火
	Fire,
	// 冰
	Ice,
	// 风
	Wind,
	// 雷
	Thunder,
	// 光
	Light,
	// 暗
	Darkness
};

// 随机算法里用的各个等级的权重分布
USTRUCT(BlueprintType)
struct FAbilityLevelWeight
{
	GENERATED_BODY()

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString Id = "";

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	ERogueRoomType RoomType = ERogueRoomType::Normal;

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int MinLevel = 0;
	
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int MaxLevel = 0;
	
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int Lv1 = 1;

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int Lv2 = 1;

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int Lv3 = 1;
};

// 3级动作添加的效果buff
USTRUCT(BlueprintType)
struct FActionLv3Buffs
{
	GENERATED_BODY()

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString Id = "";

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TMap<EAbilityElement, FString> ElementBuff;
};

// 带等级的肉鸽技能信息
USTRUCT(BlueprintType)
struct FRougeAbilityLevelInfo
{
	GENERATED_BODY()

	// 肉鸽角色技能信息Id
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString AbilityInfoId = "";

	// 技能等级 0 1 2 3
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int Level = 0;

	// 技能元素，在等级3的时候起效
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	EAbilityElement Element = EAbilityElement::None;

	bool operator==(const FRougeAbilityLevelInfo& InInfo) const
	{
		return InInfo.AbilityInfoId == AbilityInfoId &&
			InInfo.Level == Level &&
			InInfo.Element == Element;
	}
};

// 一条肉鸽角色技能信息
USTRUCT(BlueprintType)
struct FRogueBattleAbilityInfo
{
	GENERATED_BODY()

	// 技能Id
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString Id = "";

	// 技能名称
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString Name = "";

	// 技能插槽
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	ERogueAbilitySlot AbilitySlot = ERogueAbilitySlot::None;

	// 视频地址Id（表VideoPath.json）
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString VideoPath;
	
	// 这个技能的ActionId列表
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FString> ActionIds;

	// 核心Tag
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	ERogueBattleTag CoreTag = ERogueBattleTag::None;

	// 描述
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString Desc;

	// 攻击力倍率
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<float> AtkScale;

	// 破坏值倍率
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<float> BreakScale;

	// 技能等级3的buff组
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString ActionLv3Buffs = "ActionLv3Buff_1";
	
	static FRogueBattleAbilityInfo FromJson(TSharedPtr<FJsonObject> JsonObj);
};

// 强化类型
UENUM(BlueprintType)
enum class EUpgradeType : uint8
{
	// 动作获得
	ObtainAction,
	// 动作强化
	StrengthenAction,
	// 动作精通
	MasterAction
};

// 一条肉鸽角色战斗强化信息
USTRUCT(BlueprintType)
struct FRogueBattleUpgradeInfo
{
	GENERATED_BODY()

public:
	// Index
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int Index = 0;

	// 获得动作的id与前面的技能AbilityId一一对应
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString Id = "";

	// 前置Id
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString PreId = "";
	
	// 描述
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString Desc = "";

	// 用来显示这个升级的IconPath
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString Icon = "";

	// 强化类型
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	EUpgradeType UpgradeType = EUpgradeType::ObtainAction;

	// 是否是职业专精强化
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	bool bOnlyforCareer = false;

	// 随机权重
	// 参照值：被替换下过去的技能为1
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int Weight = 1;

	// 等级上限
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int MaxLevel = 1;
	
	// 添加上的时候执行的脚本
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FJsonFuncData> Func;

	// 添加上的时候，添加的 BuffId
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString BuffId = "";

	// 添加上的时候，添加的Buff的目标层数
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<int> BuffStack;
	
	static FRogueBattleUpgradeInfo FromJson(TSharedPtr<FJsonObject> JsonObj);
};

// 肉鸽角色战斗强化
USTRUCT(BlueprintType)
struct FRogueBattleUpgrade
{
	GENERATED_BODY()

public:
	// Id 对应角色职业Id
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString Id = "";
	
	// 普通攻击的默认Id
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString DefaultNormalAttack = "";
	
	// 地面技能1的默认Id
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString DefaultGround1 = "";
	
	// 地面技能2的默认Id
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString DefaultGround2 = "";
	
	// 空中技能1的默认Id
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString DefaultAir1 = "";
	
	// 空中技能2的默认Id
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString DefaultAir2 = "";

	// 技能信息列表
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FRogueBattleAbilityInfo> AbilityInfos;

	// 升级信息列表
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FRogueBattleUpgradeInfo> UpgradeInfos;

	bool HasUpgradeId(FString Id);
	FRogueBattleAbilityInfo GetAbilityInfo(FString AbilityId);
	FRogueBattleUpgradeInfo GetUpgradeInfo(FString UpgradeId);
	
	static FRogueBattleUpgrade FromJson(TSharedPtr<FJsonObject> JsonObj);
};