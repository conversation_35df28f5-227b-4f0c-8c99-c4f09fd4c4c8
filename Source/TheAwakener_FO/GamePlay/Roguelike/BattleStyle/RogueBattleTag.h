// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "RogueBattleTag.generated.h"

UENUM(BlueprintType)
enum class ERogueBattleTag: uint8
{
	// None
	None,
	// 钝击
	Bludgeon,
	// 斩击
	Slash,
	// 突刺
	Pierce,
	// 击飞
	Below,
	// 下砸
	Smash,
	// 位移
	Dash,
	// 蓄力
	Power,

	// 完美闪避
	JustDodge,
	// 完美防御
	JustDefense,
	// 瞬时攻击
	JustAttack,
	// 防御反击
	DefenseCounter,
	// 闪避反击
	DodgeCounter,
	// 跃升攻击
	LeapAttack,
	// 血瓶恢复
	PotionNumRecover,
	// 血瓶数量增加
	PotionNum,
	// 血瓶质量
	PotionQuality,
	// 金币
	Coin,
	// 经验
	Exp,
	// 魂之残响 Key
	Key,
	// 神之碎片 Shard
	Shard,
	// 虚无结晶 Soul
	Soul,
	// 法器
	MagicItem,
	// 圣遗物
	Relic,
	// 技能
	Action
};

// 判断战斗风格强化
USTRUCT(BlueprintType)
struct FBattleStyleUpgradeCheckInfos
{
	GENERATED_BODY()

	// 不填为 True
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FString> CheckInfos;
};
