// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "RogueBattleStyleAbility.h"
#include "TheAwakener_FO/GamePlay/Roguelike/BattleUpgrade/RogueBattleUpgrade.h"
#include "RogueBattleStyle.generated.h"

// 技能类型
UENUM(BlueprintType)
enum class ERogueAbilityType : uint8
{
	// 普攻
	NormalAttack,
	// 地面技能
	Ground,
	// 空中技能
	Air,
	// 其他
	Other
};

// 战斗风格
USTRUCT(BlueprintType)
struct FRogueBattleStyle
{
	GENERATED_BODY()
	
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString Id = "";
	// 属于哪个角色（职业）
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString Class = "";
	// 风格名称
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString Name = "";
	// 风格描述
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString Desc = "";
	// 风格核心 体现 Tag 倾向
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString StyleCore = "";
	// 普攻 动作的 Ids

	// 5个技能槽位的战斗风格技能
	// NormalAttack	普攻	ActionCmd:"Action1"
	// Ground1	地面技能1	ActionCmd:"Action2"
	// Ground2	地面技能2	ActionCmd:"Action3"
	// Air1	空中技能1	ActionCmd:"Action2"
	// Air2	空中技能2	ActionCmd:"Action3"
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TMap<ERogueAbilitySlot, FRogueBattleStyleAbility> Abilitys;
	
	static FRogueBattleStyle FromJson(TSharedPtr<FJsonObject> JsonObj);
};
