// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "RogueBattleStyle.h"
#include "RogueBattleStyleUpgrade.h"
#include "TheAwakener_FO/GameFramework/Base/AwGameInstance.h"
#include "Subsystems/GameInstanceSubsystem.h"
#include "RogueBattleStyleSubSystem.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API URogueBattleStyleSubSystem : public UGameInstanceSubsystem
{
	GENERATED_BODY()

private:
	UPROPERTY()
	UAwGameInstance* GameInstance;
	
	// 战斗风格强化信息（在大厅里面用的）
	// FString - 战斗风格Id BattleStyleId
	// TArray<FString> - 该战斗风格强化了那些？ Id的集合
	TMap<FString, TArray<FString>> BattleStyleUpgradeInfo;
	
	// 执行一次强化
	bool DoOneUpgrade(FString UpgradeId);
	// 撤回一次强化
	bool UndoOneUpgrade(FString UpgradeId);

	bool CheckPreId(FRogueBattleStyleUpgrade UpgradeInfo);
	
	TMap<FString, FRogueBattleStyleUpgrade> GetCurrentAllUpgradeIds() const;

	// 获取当前的战斗风格
	FString GetCurrentStyleId() const;

	int TargetPlayerIndex = 0;
public:
	// 在大厅里面是否开启了所有强化
	bool IsUpgradeAllInHall = false;
	
	void InitSubSystem();

	void ClearTempUpgradeInfo();
	//对于多人情况，减少代码修改，使用前声明对谁修改
	UFUNCTION(BlueprintCallable)
	void SetModifyingTarget(int PlayerIndex){TargetPlayerIndex = PlayerIndex;}
	// 判断是否在大厅
	UFUNCTION(BlueprintCallable, Category="RogueBattleStyle")
	bool IsInHall() const;

	// 战斗风格默认强化
	UFUNCTION(BlueprintCallable, Category="RogueBattleStyle")
	void UpgradeDefault();
	
	// 在大厅中强化某个，测试用
	UFUNCTION(BlueprintCallable, Category="RogueBattleStyle")
	void UpgradeInHall(FString UpgradeId);

	// 在大厅中去除某个强化，测试用
	UFUNCTION(BlueprintCallable, Category="RogueBattleStyle")
	void UndoUpgradeInHall(FString UpgradeId);
	
	// 在大厅中强化当前风格的强化
	UFUNCTION(BlueprintCallable, Category="RogueBattleStyle")
	void UpgradeAllInHall();
	
	// 在大厅中移除当前风格的所有强化
	UFUNCTION(BlueprintCallable, Category="RogueBattleStyle")
	void RemoveAllUpgradeInHall();
	
	// 在战斗中强化某个
	UFUNCTION(BlueprintCallable, Category="RogueBattleStyle")
	void UpgradeInBattle(FString UpgradeId);

	UFUNCTION(BlueprintCallable, Category="RogueBattleStyle")
	void UndoUpgradeInBattle(FString UpgradeId);
	
	// 在战斗中强化好几个
	UFUNCTION(BlueprintCallable, Category="RogueBattleStyle")
	void UpgradeAnyInBattle(TArray<FString> UpgradeIds);
	
	// 在战斗中再次强化
	UFUNCTION(BlueprintCallable, Category="RogueBattleStyle")
	void ReUpgradeInBattle(const TArray<FString> UpgradeIds);
	
	// 获取当前战斗风格已经强化了那些？
	TArray<FString> GetCurrentUpgradeIds();
	
	// 能否随机强化
	UFUNCTION(BlueprintPure, Category="RogueBattleStyle")
	bool CanRandomUpgrade();
	
	/**
	 * @brief 根据当前的战斗风格，随机获取需要的强化
	 * @param Count 需要随到的数量
	 * @return 
	 */
	UFUNCTION(BlueprintCallable, Category="RogueBattleStyle")
	TArray<FRogueBattleStyleUpgrade> GetUpgradesByRandom(int Count);


	static int GetRateByRank(int Rank);
	// 确认当前战斗风格是否有某个强化
	UFUNCTION(BlueprintPure, Category="RogueBattleStyle")
	bool CheckHasUpgrade(FString UpgradeId);

	/**
	 * @brief 根据强化信息，确认当前战斗风格是否有某个强化
	 * @param Character
	 * @param CheckInfo BattleStyleId（战斗风格Id） + RogueBattleStyleUpgradeId（战斗风格强化Id），通过 “.” 连接，方便填写
	 */
	UFUNCTION(BlueprintPure, Category="RogueBattleStyle")
	static bool CheckHasUpgradeByCheckInfo(const AAwCharacter* Character, const FString CheckInfo);
	
	// 获取某个战斗风格可以强化的所有Id集合
	static TArray<FString> AllShouldUpgradeIds(FString StyleId);
	
	// 确认能否再强化
	// 强化完了就不能再强化了
	bool CheckCanUpgrade(FString StyleId);
	
	UFUNCTION(BlueprintPure, Category="RogueBattleStyle")
	FRogueBattleStyleAbilityInfo GetCurAbilityInfo(ERogueAbilitySlot AbilityType);

	// 获取当前能释放的技能的Tag
	// Return-Key：战斗Tag
	// Return-Int：有几个技能有个这个Tag （0~5个）
	UFUNCTION(BlueprintPure, Category="RogueBattleStyle")
	TMap<ERogueBattleTag, int> GetCurActiveTags();
};
