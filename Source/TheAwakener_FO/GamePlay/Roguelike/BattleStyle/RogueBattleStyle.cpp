// Fill out your copyright notice in the Description page of Project Settings.


#include "RogueBattleStyle.h"

#include "TheAwakener_FO/FunctionLibrary/DataFuncLib.h"

FRogueBattleStyle FRogueBattleStyle::FromJson(TSharedPtr<FJsonObject> JsonObj)
{
	FRogueBattleStyle Res;
	
	Res.Id = UDataFuncLib::AwGetStringField(JsonObj, "Id", "");
	Res.Class = UDataFuncLib::AwGetStringField(JsonObj, "Class", "");
	Res.Name = UDataFuncLib::AwGetStringField(JsonObj, "Name", "");
	Res.Desc = UDataFuncLib::AwGetStringField(JsonObj, "Desc", "");
	Res.StyleCore = UDataFuncLib::AwGetStringField(JsonObj, "StyleCore", "");

	if (JsonObj-><PERSON><PERSON>ield("Abilitys"))
	{
		const TSharedPtr<FJsonObject> AbilityJsonObj = JsonObj->GetObjectField("Abilitys");
		
		if (AbilityJsonObj->HasField("NormalAttack"))
			Res.Abilitys.Add(ERogueAbilitySlot::NormalAttack,
				FRogueBattleStyleAbility::FromJson(AbilityJsonObj->GetObjectField("NormalAttack")));
	
		if (AbilityJsonObj->HasField("Ground1"))
			Res.Abilitys.Add(ERogueAbilitySlot::Ground1,
				FRogueBattleStyleAbility::FromJson(AbilityJsonObj->GetObjectField("Ground1")));
	
		if (AbilityJsonObj->HasField("Ground2"))
			Res.Abilitys.Add(ERogueAbilitySlot::Ground2,
				FRogueBattleStyleAbility::FromJson(AbilityJsonObj->GetObjectField("Ground2")));
	
		if (AbilityJsonObj->HasField("Air1"))
			Res.Abilitys.Add(ERogueAbilitySlot::Air1,
				FRogueBattleStyleAbility::FromJson(AbilityJsonObj->GetObjectField("Air1")));
	
		if (AbilityJsonObj->HasField("Air2"))
			Res.Abilitys.Add(ERogueAbilitySlot::Air2,
				FRogueBattleStyleAbility::FromJson(AbilityJsonObj->GetObjectField("Air2")));
	}
	
	return Res;
}
