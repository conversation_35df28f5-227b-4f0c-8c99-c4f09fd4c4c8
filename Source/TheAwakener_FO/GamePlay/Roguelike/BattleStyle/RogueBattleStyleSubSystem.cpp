// Fill out your copyright notice in the Description page of Project Settings.


#include "RogueBattleStyleSubSystem.h"

#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GameFramework/Manager/AwRogueDataSystem.h"

bool URogueBattleStyleSubSystem::DoOneUpgrade(FString UpgradeId)
{
	if (!GameInstance->GetSubsystem<UAwRogueDataSystem>()) return false;
	
	const FString CurStyleId = GetCurrentStyleId();

	FRogueBattleStyleUpgrade Upgrade = UGameplayFuncLib::GetAwDataManager()->GetRogueBattleStyleUpgrade(CurStyleId, UpgradeId);

	if (!CheckPreId(Upgrade))
		return false;
	
	if (!UGameplayFuncLib::GetLocalAwPlayerCharacter(TargetPlayerIndex))
		return false;
	
	UActionComponent* ActionComponent = UGameplayFuncLib::GetLocalAwPlayerCharacter(TargetPlayerIndex)->GetActionComponent();
	// Remove RemoveActions
	for (TTuple<FString, TArray<FString>> RemoveAction : Upgrade.RemoveActions)
		for (FString ActionId : RemoveAction.Value)
			ActionComponent->RemoveActionWithCmdById(ActionId, RemoveAction.Key);
	// Add AddActions
	for (TTuple<FString, TArray<FString>> AddAction : Upgrade.AddActions)
		for (FString ActionId : AddAction.Value)
			ActionComponent->AddActionWithCmdById(ActionId, AddAction.Key);
	return true;
}

bool URogueBattleStyleSubSystem::UndoOneUpgrade(FString UpgradeId)
{
	if (!GameInstance->GetSubsystem<UAwRogueDataSystem>()) return false;
	
	const FString CurStyleId = GetCurrentStyleId();

	FRogueBattleStyleUpgrade Upgrade = UGameplayFuncLib::GetAwDataManager()->GetRogueBattleStyleUpgrade(CurStyleId, UpgradeId);
	UActionComponent* ActionComponent = UGameplayFuncLib::GetLocalAwPlayerCharacter(TargetPlayerIndex)->GetActionComponent();
	// Remove AddActions
	for (TTuple<FString, TArray<FString>> Action : Upgrade.AddActions)
		for (FString ActionId : Action.Value)
			ActionComponent->RemoveActionWithCmdById(ActionId, Action.Key);
	// Add RemoveActions
	for (TTuple<FString, TArray<FString>> Action : Upgrade.RemoveActions)
		for (FString ActionId : Action.Value)
			ActionComponent->AddActionWithCmdById(ActionId, Action.Key);

	return true;
}

bool URogueBattleStyleSubSystem::CheckPreId(FRogueBattleStyleUpgrade UpgradeInfo)
{
	if (UpgradeInfo.PerId.IsEmpty())
		return true;
	if (CheckHasUpgrade(UpgradeInfo.PerId))
		return true;
	
	UKismetSystemLibrary::PrintString(this, "[Error!] PreId (PerID: "+UpgradeInfo.PerId+") is not upgraded!!! (ID: "+UpgradeInfo.Id+")");
	return false;
}

TMap<FString, FRogueBattleStyleUpgrade> URogueBattleStyleSubSystem::GetCurrentAllUpgradeIds() const
{
	const FString CurStyleId = GetCurrentStyleId();
	return UGameplayFuncLib::GetAwDataManager()->GetRogueBattleStyleUpgrades(CurStyleId);
}

FString URogueBattleStyleSubSystem::GetCurrentStyleId() const
{
	const FString ClassId = GameInstance->GetSubsystem<UAwRogueDataSystem>()->GetCurPawnClassId(TargetPlayerIndex);
	return GameInstance->GetSubsystem<UAwRogueDataSystem>()->GetBattleStyle(ClassId);
}

TArray<FString> URogueBattleStyleSubSystem::GetCurrentUpgradeIds()
{
	const FString StyleId = GetCurrentStyleId();
	TArray<FString> UpgradeIds;
	if (IsInHall())
	{
		if (this->BattleStyleUpgradeInfo.Contains(StyleId)) 
			UpgradeIds = this->BattleStyleUpgradeInfo[StyleId];
	}
	else
	{
		if (GameInstance->GetSubsystem<UAwRogueDataSystem>()) 
			UpgradeIds = GameInstance->GetSubsystem<UAwRogueDataSystem>()->GetUpgrades(StyleId);
	}
	return UpgradeIds;
}

void URogueBattleStyleSubSystem::InitSubSystem()
{
	GameInstance = UGameplayFuncLib::GetAwGameInstance();

	IsUpgradeAllInHall = false;
}

void URogueBattleStyleSubSystem::ClearTempUpgradeInfo()
{
	IsUpgradeAllInHall = false;
	this->BattleStyleUpgradeInfo.Empty();
}

bool URogueBattleStyleSubSystem::IsInHall() const
{
	if (GameInstance->GetSubsystem<UAwRogueDataSystem>())
		return !GameInstance->GetSubsystem<UAwRogueDataSystem>()->GetCurBattleDataIsActive();
	return true;
}

void URogueBattleStyleSubSystem::UpgradeDefault()
{
	return;
	// TODO:
	
	if (IsInHall())
	{
		UpgradeInHall(GetCurAbilityInfo(ERogueAbilitySlot::Ground1).Model.UnlockId);
		UpgradeInHall(GetCurAbilityInfo(ERogueAbilitySlot::Ground2).Model.UnlockId);
		UpgradeInHall(GetCurAbilityInfo(ERogueAbilitySlot::Air1).Model.UnlockId);
		UpgradeInHall(GetCurAbilityInfo(ERogueAbilitySlot::Air2).Model.UnlockId);
	}
	else
	{
		UpgradeInBattle(GetCurAbilityInfo(ERogueAbilitySlot::Ground1).Model.UnlockId);
		UpgradeInBattle(GetCurAbilityInfo(ERogueAbilitySlot::Ground2).Model.UnlockId);
		UpgradeInBattle(GetCurAbilityInfo(ERogueAbilitySlot::Air1).Model.UnlockId);
		UpgradeInBattle(GetCurAbilityInfo(ERogueAbilitySlot::Air2).Model.UnlockId);
	}
}

void URogueBattleStyleSubSystem::UpgradeInHall(FString UpgradeId)
{
	if (CheckHasUpgrade(UpgradeId))
		return;
	
	if (DoOneUpgrade(UpgradeId))
	{
		const FString StyleId = GetCurrentStyleId();
		if (!this->BattleStyleUpgradeInfo.Contains(StyleId))
			this->BattleStyleUpgradeInfo.Add(StyleId, TArray<FString>());
		this->BattleStyleUpgradeInfo[StyleId].Add(UpgradeId);
	}
}

void URogueBattleStyleSubSystem::UndoUpgradeInHall(FString UpgradeId)
{
	if (UndoOneUpgrade(UpgradeId))
	{
		const FString StyleId = GetCurrentStyleId();
		if (this->BattleStyleUpgradeInfo.Contains(StyleId))
			this->BattleStyleUpgradeInfo[StyleId].Remove(UpgradeId);
	}
}

void URogueBattleStyleSubSystem::UpgradeAllInHall()
{
	for (const TTuple<FString, FRogueBattleStyleUpgrade> UpgradeInfo : GetCurrentAllUpgradeIds()) 
		UpgradeInHall(UpgradeInfo.Key);
	
	IsUpgradeAllInHall = true;
}

void URogueBattleStyleSubSystem::RemoveAllUpgradeInHall()
{
	for (const TTuple<FString, FRogueBattleStyleUpgrade> UpgradeInfo : GetCurrentAllUpgradeIds())
		UndoUpgradeInHall(UpgradeInfo.Key);

	UpgradeDefault();
	
	IsUpgradeAllInHall = false;
}

void URogueBattleStyleSubSystem::UpgradeInBattle(FString UpgradeId)
{
	if (CheckHasUpgrade(UpgradeId))
		return;
	
	if (DoOneUpgrade(UpgradeId))
	{
		const FString StyleId = GetCurrentStyleId();
		UAwRogueDataSystem* DataSystem = GameInstance->GetSubsystem<UAwRogueDataSystem>();
		DataSystem->AddUpgrade(StyleId, UpgradeId);

		DataSystem->SetBattleStyleUpgradeRandomReward(TArray<FString>());
		DataSystem->SaveData();
	}
}

void URogueBattleStyleSubSystem::UndoUpgradeInBattle(FString UpgradeId)
{
	if (UndoOneUpgrade(UpgradeId))
	{
		const FString StyleId = GetCurrentStyleId();
		UAwRogueDataSystem* DataSystem = GameInstance->GetSubsystem<UAwRogueDataSystem>();
		DataSystem->RemoveUpgrade(StyleId, UpgradeId);
		
		DataSystem->SaveData();
	}
}

void URogueBattleStyleSubSystem::UpgradeAnyInBattle(TArray<FString> UpgradeIds)
{
	for (const FString UpgradeId : UpgradeIds)
		UpgradeInBattle(UpgradeId);
}

void URogueBattleStyleSubSystem::ReUpgradeInBattle(const TArray<FString> UpgradeIds)
{
	for (int i = 0; i < UpgradeIds.Num(); ++i)
	{
		const FString Id = UpgradeIds[i];
		DoOneUpgrade(Id);
	}
}

bool URogueBattleStyleSubSystem::CanRandomUpgrade()
{
	// 所有的
	const TMap<FString, FRogueBattleStyleUpgrade> AllUpgrades = GetCurrentAllUpgradeIds();

	// 已经强化的
	const TArray<FString> UpgradeIds = GetCurrentUpgradeIds();

	return UpgradeIds.Num() < AllUpgrades.Num();
}

TArray<FRogueBattleStyleUpgrade> URogueBattleStyleSubSystem::GetUpgradesByRandom(int Count)
{
	TArray<FRogueBattleStyleUpgrade> Res;

	// 所有的
	TMap<FString, FRogueBattleStyleUpgrade> AllUpgrades = GetCurrentAllUpgradeIds();

	// 已经强化的
	TArray<FString> UpgradeIds = GetCurrentUpgradeIds();
	
	// 获得随机池子
	TMap<FString, FRogueBattleStyleUpgrade> Pool;
	for (TTuple<FString, FRogueBattleStyleUpgrade> Upgrade : AllUpgrades)
		if (!UpgradeIds.Contains(Upgrade.Key) &&
			(Upgrade.Value.PerId.IsEmpty() || UpgradeIds.Contains(Upgrade.Value.PerId))
			)
			Pool.Add(Upgrade);
	
	// 随机出强化牌
	for (int i = 0; i < Count; ++i)
	{
		int TotalRate = 0;
		for (TTuple<FString, FRogueBattleStyleUpgrade> Each : Pool)
			TotalRate += GetRateByRank(Each.Value.Rank);
		
		if (TotalRate <= 0)
			break;

		int Rand = FMath::RandRange(0, TotalRate);
		FString NewId;
		for (TTuple<FString, FRogueBattleStyleUpgrade> Each : Pool)
		{
			Rand -= GetRateByRank(Each.Value.Rank);
			if (Rand <= 0)
			{
				Res.Add(Each.Value);
				NewId = Each.Key;
				break;
			}
		}
		
		Pool.Remove(NewId);
	}
	
	return Res;
}

int URogueBattleStyleSubSystem::GetRateByRank(int Rank)
{
	if (Rank == 3)
		return 10;
	if (Rank == 2)
		return 40;
	if (Rank == 1)
		return 80;
	return 10;
}

bool URogueBattleStyleSubSystem::CheckHasUpgrade(FString UpgradeId)
{
	return GetCurrentUpgradeIds().Contains(UpgradeId);
}

bool URogueBattleStyleSubSystem::CheckHasUpgradeByCheckInfo(const AAwCharacter* Character, const FString CheckInfo)
{
	const FString ClassId = Character->CharacterObj.ClassId;
	const FString CurrStyleId =
		Character->GetGameInstance()->GetSubsystem<UAwRogueDataSystem>()->GetBattleStyle(ClassId);
			
	URogueBattleStyleSubSystem* BattleStyleSubSystem =
		Character->GetGameInstance()->GetSubsystem<URogueBattleStyleSubSystem>();
	
	FString StyleId;
	FString UpgradeId;
	CheckInfo.Split(".", &StyleId, &UpgradeId);
	if (CurrStyleId == StyleId)
		return BattleStyleSubSystem->CheckHasUpgrade(UpgradeId);
	
	return false;
}

TArray<FString> URogueBattleStyleSubSystem::AllShouldUpgradeIds(FString StyleId)
{
	TArray<FString> Res;
	for (TTuple<ERogueAbilitySlot, FRogueBattleStyleAbility> Ability : UGameplayFuncLib::GetDataManager()->GetRogueBattleStyle(StyleId).Abilitys)
		for (FString Id : Ability.Value.UpgradeIds)
			Res.Add(Id);

	return Res;
}

bool URogueBattleStyleSubSystem::CheckCanUpgrade(FString StyleId)
{
	bool Can = false;
	for (FString Id : AllShouldUpgradeIds(StyleId))
		if (!GetCurrentUpgradeIds().Contains(Id))
			Can = true;
	return Can;
}

FRogueBattleStyleAbilityInfo URogueBattleStyleSubSystem::GetCurAbilityInfo(ERogueAbilitySlot AbilityType)
{
	FRogueBattleStyleAbilityInfo AbilityInfo;
	
	const FString StyleId = GetCurrentStyleId();
	TMap<ERogueAbilitySlot, FRogueBattleStyleAbility>  Abilities = UGameplayFuncLib::GetDataManager()->GetRogueBattleStyle(StyleId).Abilitys;
	if (!Abilities.Contains(AbilityType))
		return AbilityInfo;
	
	FRogueBattleStyleAbility Ability = Abilities[AbilityType];

	AbilityInfo.Model = Ability;

	int Index = FRogueBattleStyleAbility::GetShowIndex(Ability.IndexIds, GetCurrentUpgradeIds());

	AbilityInfo.IsUnlock = Ability.UnlockId.IsEmpty() ? true : CheckHasUpgrade(Ability.UnlockId);
	if (Ability.Name.Num() >= 1)
		AbilityInfo.Name = Ability.Name[FMath::Min(Ability.Name.Num()-1, Index)];
	if (Ability.CoreTag.Num() >= 1)
		AbilityInfo.CoreTag = Ability.CoreTag[FMath::Min(Ability.CoreTag.Num()-1, Index)];
	if (Ability.AllTags.Num() >= 1)
		AbilityInfo.AllTags = Ability.AllTags[FMath::Min(Ability.AllTags.Num()-1, Index)].Tags;
	
	int Count = 0;
	for (const FString UpgradeId : Ability.UpgradeIds)
		if (CheckHasUpgrade(UpgradeId))
			Count ++;
	AbilityInfo.UpgradeCount = Count;

	return AbilityInfo;
}

TMap<ERogueBattleTag, int> URogueBattleStyleSubSystem::GetCurActiveTags()
{
	TMap<ERogueBattleTag, int> Res;

	TArray<FRogueBattleStyleAbilityInfo> Abilities;

	return Res;
	// TODO: 还需要吗？
	
	Abilities.Add(GetCurAbilityInfo(ERogueAbilitySlot::NormalAttack));
	Abilities.Add(GetCurAbilityInfo(ERogueAbilitySlot::Ground1));
	Abilities.Add(GetCurAbilityInfo(ERogueAbilitySlot::Ground2));
	Abilities.Add(GetCurAbilityInfo(ERogueAbilitySlot::Air1));
	Abilities.Add(GetCurAbilityInfo(ERogueAbilitySlot::Air2));

	for (FRogueBattleStyleAbilityInfo Ability : Abilities)
	{
		if (Ability.IsUnlock)
		{
		for (ERogueBattleTag Tag : Ability.AllTags)
			if (Res.Contains(Tag))
				Res[Tag] += 1;
			else
				Res.Add(Tag, 1);
		}
	}

	return Res;
}
