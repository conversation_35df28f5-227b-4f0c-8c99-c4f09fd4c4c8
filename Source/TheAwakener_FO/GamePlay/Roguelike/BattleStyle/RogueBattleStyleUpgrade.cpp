// Fill out your copyright notice in the Description page of Project Settings.


#include "RogueBattleStyleUpgrade.h"

#include "TheAwakener_FO/FunctionLibrary/DataFuncLib.h"

FRogueBattleStyleUpgrade FRogueBattleStyleUpgrade::From<PERSON>son(TSharedPtr<FJsonObject> JsonObj)
{
	FRogueBattleStyleUpgrade Res = FRogueBattleStyleUpgrade();

	Res.Index = UDataFuncLib::AwGetNumberField(JsonObj, "Index", 0);
	Res.Id = UDataFuncLib::AwGetStringField(JsonObj, "Id", "");
	Res.PerId = UDataFuncLib::AwGetStringField(JsonObj, "PreId", "");
	Res.Rank = UDataFuncLib::AwGetNumberField(JsonObj, "Rank", 1);
	Res.Desc = UDataFuncLib::AwGetStringField(<PERSON>sonObj, "Desc", "");
	Res.UpgradeType = UDataFuncLib::AwGetNumberField(JsonObj, "UpgradeType", 2);
	
	if (JsonObj->HasField("AddActions"))
	{
		const TSharedPtr<FJsonObject> AddObj = JsonObj->GetObjectField("AddActions");
		Res.AddActions["Action1"] = UDataFuncLib::AwGetStringArrayField(AddObj, "Action1");
		Res.AddActions["Action2"] = UDataFuncLib::AwGetStringArrayField(AddObj, "Action2");
		Res.AddActions["Action3"] = UDataFuncLib::AwGetStringArrayField(AddObj, "Action3");
	}

	if (JsonObj->HasField("RemoveActions"))
	{
		const TSharedPtr<FJsonObject> RemoveObj = JsonObj->GetObjectField("RemoveActions");
		Res.RemoveActions["Action1"] = UDataFuncLib::AwGetStringArrayField(RemoveObj, "Action1");
		Res.RemoveActions["Action2"] = UDataFuncLib::AwGetStringArrayField(RemoveObj, "Action2");
		Res.RemoveActions["Action3"] = UDataFuncLib::AwGetStringArrayField(RemoveObj, "Action3");
	}
	
	return Res;
}
