// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "RogueBattleTag.h"
#include "RogueBattleStyleAbility.generated.h"

// All Tags 组
USTRUCT(BlueprintType)
struct FAllRogueBattleTags
{
	GENERATED_BODY()

	// All Tags
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<ERogueBattleTag> Tags;
};

// 战斗风格 下的技能。分为：地面技能1，地面技能2，空中技能1，空中技能2
USTRUCT(BlueprintType)
struct FRogueBattleStyleAbility
{
	GENERATED_BODY()

	// Id
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString Id;
	
	// 技能名称
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FString> Name;
	
	// 默认 Actions
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FString> DefaultActions;

	// 核心Tags
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<ERogueBattleTag> CoreTag;

	// All Tags组
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FAllRogueBattleTags> AllTags;

	// 相关的强化Id，解锁不算
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FString> UpgradeIds;

	// 用来判断是否解锁的id，为空则默认解锁
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString UnlockId;

	// 根据强化id来返回Index，从后往前判断，比如有第三个就返回3，有第二个就返回2，空为0
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FString> IndexIds;

	static int GetShowIndex(TArray<FString> IndexIds, TArray<FString> UpgradeIds);
	
	static FRogueBattleStyleAbility FromJson(TSharedPtr<FJsonObject> JsonObj);	
};

USTRUCT(BlueprintType)
struct FRogueBattleStyleAbilityInfo
{
	GENERATED_BODY()
	
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FRogueBattleStyleAbility Model;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool IsUnlock = false;
	
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString Name = "";
	
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	ERogueBattleTag CoreTag = ERogueBattleTag::Slash;
	
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<ERogueBattleTag> AllTags;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int UpgradeCount = 0;
};
