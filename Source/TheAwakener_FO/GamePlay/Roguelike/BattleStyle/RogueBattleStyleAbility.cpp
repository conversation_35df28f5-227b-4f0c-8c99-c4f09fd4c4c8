// Fill out your copyright notice in the Description page of Project Settings.


#include "RogueBattleStyleAbility.h"

#include "TheAwakener_FO/FunctionLibrary/CallFuncLib.h"
#include "TheAwakener_FO/FunctionLibrary/DataFuncLib.h"

int FRogueBattleStyleAbility::GetShowIndex(TArray<FString> IndexIds, TArray<FString> UpgradeIds)
{
	int Index = 0;
	for (int i = IndexIds.Num() - 1; i >= 0; --i)
	{
		if (UpgradeIds.Contains(IndexIds[i]))
		{
			Index = i+1;
			break;
		}
	}
	return Index;
}

FRogueBattleStyleAbility FRogueBattleStyleAbility::FromJson(TSharedPtr<FJsonObject> JsonObj)
{
	FRogueBattleStyleAbility Res;
	
	Res.Id = UDataFuncLib::AwGetStringField(JsonObj, "Id");
	Res.Name = UDataFuncLib::AwGetStringArrayField(JsonObj, "Name");
	Res.DefaultActions = UDataFuncLib::AwGetStringArrayField(JsonObj, "DefaultActionId");
	
	Res.CoreTag = UDataFuncLib::AwGetEnumArrayField<ERogueBattleTag>(JsonObj, "CoreTag");
	
	if (JsonObj->HasField("AllTags"))
	{
		for (const TSharedPtr<FJsonValue, ESPMode::ThreadSafe> JsonValue : JsonObj->GetArrayField("AllTags"))
		{
			FAllRogueBattleTags AllTags;
			for (const TSharedPtr<FJsonValue, ESPMode::ThreadSafe> Array : JsonValue->AsArray())
				AllTags.Tags.Add(UDataFuncLib::FStringToEnum<ERogueBattleTag>(Array->AsString()));

			Res.AllTags.Add(AllTags);
		}
	}

	Res.UpgradeIds = UDataFuncLib::AwGetStringArrayField(JsonObj, "UpgradeIds");
	Res.UnlockId = UDataFuncLib::AwGetStringField(JsonObj, "UnlockId");
	Res.IndexIds = UDataFuncLib::AwGetStringArrayField(JsonObj, "IndexIds");
	
	return Res;
};