// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "RogueBattleStyleUpgrade.generated.h"

USTRUCT(BlueprintType)
struct FRogueBattleStyleUpgrade
{
	GENERATED_BODY()
	
	// 就看看用的
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int Index = 0;

	// id
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString Id = "";

	// 前置id
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString PerId = "";

	// 稀有度1~3
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int Rank = 1;

	// 强化类型
	// 0 - ObtainAction 技能解锁
	// 1 - StrengthenAction 技能强化
	// 2 - MasterAction 技能精通
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int UpgradeType = 2;
	
	// 描述
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString Desc = "";

	// 需要添加的ActionID
	TMap<FString, TArray<FString>> AddActions;

	// 需要删除的ActionID
	TMap<FString, TArray<FString>> RemoveActions;
	
	FRogueBattleStyleUpgrade()
	{
		AddActions.Empty();
		AddActions.Add("Action1", TArray<FString>());
		AddActions.Add("Action2", TArray<FString>());
		AddActions.Add("Action3", TArray<FString>());
		
		RemoveActions.Empty();
		RemoveActions.Add("Action1", TArray<FString>());
		RemoveActions.Add("Action2", TArray<FString>());
		RemoveActions.Add("Action3", TArray<FString>());
	}
	
	static FRogueBattleStyleUpgrade FromJson(TSharedPtr<FJsonObject> JsonObj);
};
