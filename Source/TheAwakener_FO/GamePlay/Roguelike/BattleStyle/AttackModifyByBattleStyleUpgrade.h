// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/GamePlay/Characters/Attack/ForceMoveInfo.h"
#include "AttackModifyByBattleStyleUpgrade.generated.h"

USTRUCT(BlueprintType)
struct FModifyInfo
{
	GENERATED_BODY()

	// 修改的数值
	// bool: 0-False 1-True other-False
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString Value;

	// CheckInfo
	// 不填为 True
	// BattleStyleId + BattleStyleUpgradeId 通过 “.” 连接
	// 用.连接是为了方便填写
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString CheckInfo;
};

USTRUCT(BlueprintType)
struct FModifyInfos
{
	GENERATED_BODY()

	// 修改的数值
	// bool: 0-False 1-True other-False
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FString> Values;

	// CheckInfo
	// 不填为 True
	// BattleStyleId + BattleStyleUpgradeId 通过 “.” 连接
	// 用.连接是为了方便填写
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString CheckInfo;
};

USTRUCT(BlueprintType)
struct FModifyInfo_HitStun
{
	GENERATED_BODY()

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FForceMoveInfo HitStun = FForceMoveInfo();
	
	// CheckInfo
	// 不填为 True
	// BattleStyleId + BattleStyleUpgradeId 通过 “.” 连接
	// 用.连接是为了方便填写
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString CheckInfo;
};

USTRUCT(BlueprintType)
struct FAttackModifyByBattleStyleUpgrade
{
	GENERATED_BODY()

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FModifyInfo> DamagePowerRate_Phy;
};


USTRUCT(BlueprintType)
struct FDefenseModifyByBattleStyleUpgrade
{
	GENERATED_BODY()

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FModifyInfo> DamagePhyModify;
};

USTRUCT(BlueprintType)
struct FAnimRateModifyByBattleStyleUpgrade
{
	GENERATED_BODY()

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FModifyInfo> AnimRate;
};

USTRUCT(BlueprintType)
struct FLoopNumModifyByBattleStyleUpgrade
{
	GENERATED_BODY()

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FModifyInfo> LoopNum;
};