// 

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/GamePlay/Buff/TableAddBuffInfo.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "UObject/Object.h"
#include "RogueWeapon.generated.h"

USTRUCT(BlueprintType)
struct FTableAddBuffInfos
{
	GENERATED_BODY()
public:
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FTableAddBuffInfo> AddBuffInfos;
};

USTRUCT(BlueprintType)
struct FRogueWeaponInfo
{
	GENERATED_BODY()

public:
	// Id
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString Id;

	// Chinese表Id,武器名称
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString Name;
	
	// ItemIcon表Id
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString Icon;

	// 主手武器模型
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString MainHand;

	// 副手武器模型
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString OffHand;

	// 设计思路(注释)
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString DesignIdeas;

	// Chinese表Id,武器描述1
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString Desc1;

	// Chinese表Id,武器描述2
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString Desc2;

	// "Chinese表Id","初始描述","等级+1","等级+2","等级+3","..."
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FString> LevelDesc;

	// 各级需要添加的武器 buffs
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FTableAddBuffInfos> Buffs;

	// "解锁需要消耗的神之碎片","+1所消耗神之碎片","+2所消耗神之碎片","+3所消耗神之碎片"
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<int> Cost;

	static FRogueWeaponInfo FromJson(const TSharedPtr<FJsonObject>& JsonObj)
	{
		FRogueWeaponInfo Info;

		Info.Id = UDataFuncLib::AwGetStringField(JsonObj, "Id", "");
		Info.Name = UDataFuncLib::AwGetStringField(JsonObj, "Name", "");
		Info.Icon = UDataFuncLib::AwGetStringField(JsonObj, "Icon", "");
		Info.MainHand = UDataFuncLib::AwGetStringField(JsonObj, "MainHand", "");
		Info.OffHand = UDataFuncLib::AwGetStringField(JsonObj, "OffHand", "");
		Info.DesignIdeas = UDataFuncLib::AwGetStringField(JsonObj, "DesignIdeas", "");
		Info.Desc1 = UDataFuncLib::AwGetStringField(JsonObj, "Desc1", "");
		Info.Desc2 = UDataFuncLib::AwGetStringField(JsonObj, "Desc2", "");
		Info.LevelDesc = UDataFuncLib::AwGetStringArrayField(JsonObj, "LevelDesc");

		if (JsonObj->HasField("Buffs"))
		{
			for (const TSharedPtr<FJsonValue> JsonValue : JsonObj->GetArrayField("Buffs"))
			{
				FTableAddBuffInfos TableAddBuffInfos;
				for (const TSharedPtr<FJsonValue> AddBuffValue : JsonValue->AsArray())
					TableAddBuffInfos.AddBuffInfos.Add(FTableAddBuffInfo::FromJson(AddBuffValue->AsObject()));
				Info.Buffs.Add(TableAddBuffInfos);
			}
		}

		if (JsonObj->HasField("Cost"))
			for (const TSharedPtr<FJsonValue> Value : JsonObj->GetArrayField("Cost"))
				Info.Cost.Add(Value->AsNumber());
		
		return Info;
	}
};

USTRUCT(BlueprintType)
struct FWeaponAtkBox
{
	GENERATED_BODY()

public:
	// Id
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString Id;

	// 这个武器 目标 AttackHitBox - ChangeTo AttackHitBox
	TMap<FString, FString> ChangeToAttackBox;
};

USTRUCT(BlueprintType)
struct FClassWeapons
{
	GENERATED_BODY()

public:
	// Id
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString Id;
	
	// WeaponIds
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FString> Weapons;
};

// 武器默认信息
USTRUCT(BlueprintType)
struct FWeaponDefaultInfo
{
	GENERATED_BODY()

	// WeaponId - WeaponLevel
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TMap<FString, int> UnlockWeapons;

	// ClassId - WeaponId
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TMap<FString, FString> CurrWeapon;
};

/**
 * 
 */
UCLASS(Blueprintable)
class THEAWAKENER_FO_API URogueWeapon : public UObject
{
	GENERATED_BODY()

public:
	
	/**
	 * @brief 根据填入的AtkHitBox的Id，当前角色，当前武器 得到对应的攻击和名称
	 * @param FilledId 填入的Id
	 * @param PlayerIndex 多人
	 * @return 攻击盒名称
	 */
	UFUNCTION(BlueprintPure)
	static FString GetCurWeaponAtkBox(FString FilledId,int PlayerIndex);

	/**
	 * @brief 切换武器
	 * @param WeaponId 武器Id
	 * @param PlayerIndex 多人
	 * @return 切换是否成功？
	 */
	UFUNCTION(BlueprintCallable)
	static bool ChangeWeapon(const FString& WeaponId,int PlayerIndex);

	/**
	 * @brief 解锁武器，并扣取货币
	 * @return 成功与否
	 */
	UFUNCTION(BlueprintCallable)
	static bool UnlockWeapon(FString WeaponId);

	/**
	 * @brief 升级武器，并扣取货币
	 * @return 成功与否
	 */
	UFUNCTION(BlueprintCallable)
	static bool LevelUpWeapon(FString WeaponId);

	// 判断武器等级是否已经是满级
	UFUNCTION(BlueprintCallable)
	static bool CheckWeaponLevelIsMax(FString WeaponId);

	UFUNCTION(BlueprintCallable)
	static void RemoveWeaponBuffs(AAwCharacter* Character, FRogueWeaponInfo WeaponInfo, int Level);

	UFUNCTION(BlueprintCallable)
	static void AddWeaponBuffs(AAwCharacter* Character, FRogueWeaponInfo WeaponInfo, int Level);

	UFUNCTION(BlueprintCallable)
	static FOffenseInfo ChangeAttackHitBoxByWeapon(AAwCharacter* Character, FOffenseInfo OffenseInfo);
};
