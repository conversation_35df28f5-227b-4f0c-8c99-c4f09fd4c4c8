// 


#include "RogueWeapon.h"

#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GameFramework/Manager/AwDataManager.h"
#include "TheAwakener_FO/GameFramework/Manager/AwRogueDataSystem.h"

FString URogueWeapon::GetCurWeaponAtkBox(FString FilledId,int PlayerIndex)
{
	FString WeaponAtkBoxId;
	if (FilledId.Contains("WeaponAtkBox_Main"))
		WeaponAtkBoxId = "WeaponAtkBox_Main";
	else if(FilledId.Contains("WeaponAtkBox_Off"))
		WeaponAtkBoxId = "WeaponAtkBox_Main";
	else
		return FilledId;
	
	UAwDataManager* DataManager = UGameplayFuncLib::GetDataManager();
	const UAwRogueDataSystem* RogueDataSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	FString WeaponId = RogueDataSystem->GetCurrWeaponId(RogueDataSystem->GetCurPawnClassId(PlayerIndex));
	WeaponAtkBoxId.Append("_").Append(WeaponId);
	
	DataManager->GetWeaponModelById(WeaponAtkBoxId);

	FString AttackHitBoxName;
	return AttackHitBoxName;
}

bool URogueWeapon::ChangeWeapon(const FString& WeaponId,int PlayerIndex)
{
	UAwRogueDataSystem* RogueDataSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	
	if (!RogueDataSystem->CheckWeaponUnlock(WeaponId))
		return false;

	const FString CurrClassId = RogueDataSystem->GetCurPawnClassId(PlayerIndex);
	const FString LastWeaponId = RogueDataSystem->GetCurrWeaponId(CurrClassId);
	if (LastWeaponId == WeaponId)
		return false;
	
	UAwDataManager* DataManager = UGameplayFuncLib::GetDataManager();
	
	if (!DataManager->GetRogueClassWeapons(CurrClassId).Weapons.Contains(WeaponId))
		return false;

	AAwCharacter* Me = UGameplayFuncLib::GetLocalAwPlayerCharacter(PlayerIndex);
	
	// Remove Weapon Buff
	const FRogueWeaponInfo LastWeaponInfo = DataManager->GetRogueWeaponInfo(LastWeaponId);
	const int LastLevel = RogueDataSystem->GetWeaponLevel(LastWeaponId);
	RemoveWeaponBuffs(Me, LastWeaponInfo, LastLevel);
	
	// Wear new Weapon and add Weapon Buff
	Me->WearRogueWeapon(WeaponId);

	RogueDataSystem->SetCurrWeaponId(CurrClassId, WeaponId);
	RogueDataSystem->SaveData();
	
	return true;
}

bool URogueWeapon::UnlockWeapon(FString WeaponId)
{
	UAwRogueDataSystem* RogueDataSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	UAwDataManager* DataManager = UGameplayFuncLib::GetDataManager();
	
	if (RogueDataSystem->CheckWeaponUnlock(WeaponId))
		return false;

	FRogueWeaponInfo WeaponInfo = DataManager->GetRogueWeaponInfo(WeaponId);
	
	if (WeaponInfo.Cost.Num() <= 0)
		return false;

	if (RogueDataSystem->AddCurrency_Shard(WeaponInfo.Cost[0] * -1))
	{
		RogueDataSystem->SetWeaponLevel(WeaponId, 0);
		RogueDataSystem->SaveData();
		return true;
	}
	
	return false;
}

bool URogueWeapon::LevelUpWeapon(FString WeaponId)
{
	UAwRogueDataSystem* RogueDataSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	UAwDataManager* DataManager = UGameplayFuncLib::GetDataManager();

	const int Level = RogueDataSystem->GetWeaponLevel(WeaponId);
	const FRogueWeaponInfo WeaponInfo = DataManager->GetRogueWeaponInfo(WeaponId);

	if (WeaponInfo.Cost.Num() <= Level + 1)
		return false;

	const int Cost = WeaponInfo.Cost[Level+1];
	
	if (RogueDataSystem->AddCurrency_Shard(Cost * -1))
	{
		const int NewLevel = Level + 1;
		RogueDataSystem->SetWeaponLevel(WeaponId, NewLevel);
		
		AAwCharacter* Me = UGameplayFuncLib::GetCharacterEquippedWeaponId(WeaponId);
		if (Me){
			// 升级整备着的武器的时候，需要check Buffs
			// Remove Weapon Buff
			RemoveWeaponBuffs(Me, WeaponInfo, Level);
			// Add Weapon Buff
			AddWeaponBuffs(Me, WeaponInfo, NewLevel);
		}
		
		RogueDataSystem->SaveData();
		return true;
	}
	
	return false;
}

bool URogueWeapon::CheckWeaponLevelIsMax(FString WeaponId)
{
	const UAwRogueDataSystem* RogueDataSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	UAwDataManager* DataManager = UGameplayFuncLib::GetDataManager();

	const int Level = RogueDataSystem->GetWeaponLevel(WeaponId);
	const FRogueWeaponInfo WeaponInfo = DataManager->GetRogueWeaponInfo(WeaponId);
	
	return WeaponInfo.Cost.Num() <= Level + 1;
}

void URogueWeapon::RemoveWeaponBuffs(AAwCharacter* Character, FRogueWeaponInfo WeaponInfo, const int Level)
{
	if(WeaponInfo.Buffs.IsEmpty())
		return;
	for (const FTableAddBuffInfo BuffInfo : WeaponInfo.Buffs[Level].AddBuffInfos)
	{
		FBuffModel BuffModel = UGameplayFuncLib::GetAwDataManager()->GetBuffModelById(BuffInfo.Id);
		if (BuffModel.ValidBuffModel() == false) continue;
		Character->AddBuff(FAddBuffInfo(Character,Character,BuffModel,BuffInfo.Stack*-1, BuffInfo.Time, false, BuffInfo.Infinity));
	}
}

void URogueWeapon::AddWeaponBuffs(AAwCharacter* Character, FRogueWeaponInfo WeaponInfo, const int Level)
{
	if(WeaponInfo.Buffs.IsEmpty())
		return;
	for (const FTableAddBuffInfo BuffInfo : WeaponInfo.Buffs[Level].AddBuffInfos)
	{
		FBuffModel BuffModel = UGameplayFuncLib::GetAwDataManager()->GetBuffModelById(BuffInfo.Id);
		if (BuffModel.ValidBuffModel() == false) continue;
		Character->AddBuff(FAddBuffInfo(Character,Character,BuffModel,BuffInfo.Stack, BuffInfo.Time, false, BuffInfo.Infinity));
	}
}

FOffenseInfo URogueWeapon::ChangeAttackHitBoxByWeapon(AAwCharacter* Character, FOffenseInfo OffenseInfo)
{
	if (Character->IsPlayerCharacter())
	{
		UAwDataManager* DataManager = UGameplayFuncLib::GetDataManager();
		const UAwRogueDataSystem* DataSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>();
		const FString CurrWeaponId = DataSystem->GetCurrWeaponId(Character->GetPawnClassId());
		FWeaponAtkBox WeaponAtkBox = DataManager->GetWeaponAtkBoxes(CurrWeaponId);

		TArray<FString> NewBoxNames;
		for (FString BoxName : OffenseInfo.AttackHitBoxName)
		{
			if (!WeaponAtkBox.ChangeToAttackBox.IsEmpty() && WeaponAtkBox.ChangeToAttackBox.Contains(BoxName))
				NewBoxNames.Add(WeaponAtkBox.ChangeToAttackBox[BoxName]);
			else
				NewBoxNames.Add(BoxName);
		}
		OffenseInfo.AttackHitBoxName = NewBoxNames;
	}

	return OffenseInfo;
}
