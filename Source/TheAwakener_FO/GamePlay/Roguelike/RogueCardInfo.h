// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "BattleStyle/RogueBattleTag.h"
#include "BattleUpgrade/RogueBattleUpgrade.h"
#include "Map/RogueMapConfig.h"
#include "TheAwakener_FO/GamePlay/Characters/Elemental/Elemental.h"
#include "TheAwakener_FO/GamePlay/Roguelike/Relic/AwRelicSubSystem.h"
#include "RogueCardInfo.generated.h"

// 战斗强化卡面信息
USTRUCT(BlueprintType)
struct FRogueCardInfo_BattleStyleUpgrade
{
	GENERATED_BODY()

	// Id
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString Id;

	// 名称
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString Name;

	// 描述
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString Desc;

	// Icon path
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString IconPath = "";
	
	// Tag，用来显示Icon
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	ERogueBattleTag CoreTag = ERogueBattleTag::None;
	
	// 强化等级
	// 0 - 解锁
	// 1 - level1
	// 2 - level2
	// 3 - level3
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int UpgradeLevel = 0;

	// 升级类型来控制卡面图片
	// 0 - 技能解锁
	// 1 - 技能强化
	// 2 - 技能精通
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int SkillCardType = 0;

	// 是否是职业专精强化
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool bOnlyforCareer = false;
};

// 圣遗物卡面信息
USTRUCT(BlueprintType)
struct FRogueCardInfo_Relic
{
	GENERATED_BODY()

	// Id
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString Id;

	// 名称
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString Name;

	// 圣遗物类型
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	ERelicType RelicType;

	// 序号
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int RecordId;

	// 描述
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString Desc;

	// 品质
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	ERelicRarity Rarity = ERelicRarity::Normal;

	// 等级
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int Level = 1;

	// 遗物图标 PathId
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString IconPathId;
	
	// 属性图标 PathId
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString ElementPathId;

	// 数字图标 PathId
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString NumberPathId;
	
	// 持有数量
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int GotCount = 0;
};

// 法器卡面信息
USTRUCT(BlueprintType)
struct FRogueCardInfo_MagicItem
{
	GENERATED_BODY()

	// Id
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString Id;

	// 名称
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString Name;

	// 描述
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString Desc;

	// 元素类型
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	EChaElemental Elemental;
	
	// 法器图标 PathId
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString IconPathId;

	// 法器等级
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int MagicItemLevel = 0;
};

// 动作卡面信息
USTRUCT(BlueprintType)
struct FRogueCardInfo_Action
{
	GENERATED_BODY()

	// Id
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString Id;

	// 名称
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString Name;

	// 动作类型
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	ERogueAbilitySlot Slot;

	// 描述
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString Desc;

	// 描述_2
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString Desc_2;

	// 品质
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int Level = 0;

	// 遗物图标 PathId
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString IconPathId;
	
	// 属性图标 PathId
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	EAbilityElement Element = EAbilityElement::None;
	
	// 视频 PathId
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString VideoPathId;
};

// 房间卡面信息
USTRUCT(BlueprintType)
struct FRogueCardInfo_Room
{
	GENERATED_BODY()

	// RoomInfo
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FRogueRoomInfo RoomInfo;
	
	// 名称
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString Name;

	// 描述
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString Desc;

	// 房间类型
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	ERogueRoomType RoomType;

	// 战斗意志数量
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int ActionPointNumber = 0;

	// 当前房间奖励列表
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FString> Rewards;
};
