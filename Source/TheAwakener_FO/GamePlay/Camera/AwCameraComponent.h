// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "AwCameraPosInfo.h"
#include "GameFramework/SpringArmComponent.h"
#include "Camera/CameraComponent.h"
#include "TheAwakener_FO/GamePlay/AnimNotifyState/AimRotateLimitInfo.h"
#include "AwCameraComponent.generated.h"

UINTERFACE(MinimalAPI)
class UAwCameraInterface : public UInterface
{
	GENERATED_BODY()
};

class THEAWAKENER_FO_API IAwCameraInterface
{
	GENERATED_BODY()

	// Add interface functions to this class. This is the class that will be inherited to implement this interface.
	public:
	UFUNCTION(BlueprintCallable,BlueprintNativeEvent)
	 void OnLock();
	UFUNCTION(BlueprintCallable,BlueprintNativeEvent)
	void OnUnLock();
	UFUNCTION(BlueprintCallable,BlueprintNativeEvent)
	 void OnLockTargetSwitch(bool bSwitchLeft );
};


class AAwCharacter;

UCLASS( ClassGroup=(Custom), meta=(BlueprintSpawnableComponent) )
class THEAWAKENER_FO_API UAwCameraComponent : public UActorComponent
{
	GENERATED_BODY()
	
private:
	UPROPERTY()
	USpringArmComponent* SpringArm;
	UCameraComponent* Camera;
	UPROPERTY()
	AAwCharacter* Character;
	
	UPROPERTY()
	FAwCameraPosInfo TickArmInfo;
	
	UPROPERTY()
	bool bIsReset;
	// 目标还原速度，以为内还原的时候 ArmModifer 列表为空了，所以还原速度就记录一下
	float TargetResetSpeed;
	// 还原速度太突然了，加了lerp过度一下
	float ResetSpeed;
	// 还原速度太突然了，加了lerp过度一下
	void TickResetSpeed(float DeltaTime);
	
	void ThisTickPos(float DeltaTime);

	void ThisTickLock(float DeltaTime);

	float RenderKeepLockDuration = 0.f;
	
	float CurLockSwitchKeepTime = 0.f;
	
	//FRotator AnimCameraFix = FRotator::ZeroRotator;
public:
	UAwCameraComponent();
	
protected:
	virtual void BeginPlay() override;

	UPROPERTY()
	FAwCameraPosInfo DefaultArmInfo;

public:
	virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

	//限制锁定区域
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	FRotator FreeLockRange = FRotator(45.f,30.f,0.f);
	
	//缓动锁定区域 大于该值 小于限制锁定区域 才会生效
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	FRotator FixLockRange = FRotator(0.f,180.f,0.f);
	
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	float MaxRenderKeepLockDuration = 5.0f;

	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	float MinRenderLength = 75;
	
	UPROPERTY()
	TMap<FString, FAwCameraPosInfo> ArmModifers;
	UFUNCTION()
	void RemoveArmModiferById(FString UniqueId);

	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	float YawLockFixSpeed = 1.15;
	
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	float PitchLockFixSpeed = 1.15;

	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	float ReLockRangeOnDead = 500;

	// 最大俯视角
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	float LockPitchMin = -35;
	//最大仰视角
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	float LockPitchMax = 35;

	// 越小 上下锁定幅度影响越大 越大 上下锁定幅度影响越小
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	float LockPitchZStandardDistance = 200;

	//此误差Z内 强制向平视靠拢
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	float LockPitchSafeZ = 50;


	// 误差Z内 最大俯视角
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	float LockSafePitchMin = -10;
	//误差Z内 最大仰视角
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	float LockSafePitchMax = 25;
	
	// 瞄准旋转限制的信息
	UPROPERTY()
	FAimRotateLimitInfo AimRotateLimitInfo;
	
	UFUNCTION(BlueprintImplementableEvent)
	void SetViewTarget(AActor* Target);
	
	UFUNCTION(BlueprintCallable)
	USpringArmComponent* GetSpring();

	UFUNCTION(BlueprintCallable)
	FRotator GetCameraRotate();

	UFUNCTION(BlueprintPure, Category = "Get")
	UCameraComponent* GetCamera() const {return Camera;}

	UFUNCTION(BlueprintCallable)
	FVector2D GetRotateDir();
	
	UFUNCTION(BlueprintCallable)
	void SetCameraRotationByForward(FVector Forward);
	UFUNCTION(BlueprintCallable)
	void SetCameraRotation(FRotator NewRotator);

	//向目标偏转一次
	UFUNCTION(BlueprintCallable)
	void FindTarget();
	
	//锁定目标
	UFUNCTION(BlueprintCallable)
	void LockTarget();
	
	//目标死亡切换最近锁定目标
	bool LockNewTargetInRangeOnLastDead(AAwCharacter* DeadCharacter);
	
	/*
	//目标死亡切换最近锁定目标  用于事件bind
	UFUNCTION()
	void LockNewTargetInRangeOnLastDead(AAwCharacter* DeadCharacter);
	*/
	
	//切换目标
	UFUNCTION(BlueprintCallable)
	void SwitchLockTarget(bool bLeft);
	// up
	void AddPitchInput(float Val) const;
	// Right
	void AddYawInput(float Val) const;

	//综合计算锁定后的最终旋转
	FRotator CaluFinalLockRotator();
	//void SetAnimCameraFix(FRotator FixOffset ){AnimCameraFix = FixOffset;}
	
	//FRotator GetAnimCameraFix( ){return AnimCameraFix;}

	UFUNCTION(BlueprintPure)
	AActor* GetCurLockTarget();
	
	TWeakObjectPtr<AActor> GetOnLockTarget(){return OnLockTarget;}
	
	// 根据瞄准对象，获得摇臂的修改值
	float GetModifyArmLengthByLockTarget();
	
private:
	void SortLockTargetsByComplexWeight(TArray<AAwCharacter*>& Source,bool bDirectionOnly = false,bool bLeft = true);
	
	bool LockCurTarget();
	bool UnlockCurTarget();

	bool CameraTargetInRenderDistance = true;
	
	TWeakObjectPtr<AActor> OnLockTarget;
};
