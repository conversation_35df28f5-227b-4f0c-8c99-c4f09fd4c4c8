// Fill out your copyright notice in the Description page of Project Settings.


#include "AwCameraComponent.h"

#include "CameraLockPointComponent.h"
#include "Kismet/KismetMathLibrary.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/FunctionLibrary/MathFuncLib.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"

void UAwCameraComponent::TickResetSpeed(float DeltaTime)
{
	if (bIsReset && ResetSpeed != TargetResetSpeed)
	{
		ResetSpeed += DeltaTime * 1.5;
		if (ResetSpeed > TargetResetSpeed)
			ResetSpeed = TargetResetSpeed;
	}
}

void UAwCameraComponent::ThisTickPos(float DeltaTime)
{
	if (ArmModifers.Num() <= 0)
	{
		TickArmInfo = FAwCameraPosInfo();
		return;
	}
	
	// 没有修改信息就是还原
	bIsReset = ArmModifers.Num() == 0;
	
	// 设置这一帧的 ArmInfo
	TickArmInfo = FAwCameraPosInfo();
	int i = 0;
	for (const TTuple<FString, FAwCameraPosInfo> Modifer : ArmModifers)
	{
		if (i == 0)
		{
			TickArmInfo.ArmLengthModify = Modifer.Value.ArmLengthModify;
			TickArmInfo.SocketCameraOffsetModify = Modifer.Value.SocketCameraOffsetModify;
			TickArmInfo.ChangeSpeed = Modifer.Value.ChangeSpeed;
			// TickArmInfo.ResetSpeed = Modifer.Value.ResetSpeed;
		}
		else
		{
			TickArmInfo.ArmLengthModify = FMath::Max(TickArmInfo.ArmLengthModify, Modifer.Value.ArmLengthModify);
			TickArmInfo.SocketCameraOffsetModify.X = FMath::Max(TickArmInfo.SocketCameraOffsetModify.X, Modifer.Value.SocketCameraOffsetModify.X);
			TickArmInfo.SocketCameraOffsetModify.Y = FMath::Max(TickArmInfo.SocketCameraOffsetModify.Y, Modifer.Value.SocketCameraOffsetModify.Y);
			TickArmInfo.SocketCameraOffsetModify.Z = FMath::Max(TickArmInfo.SocketCameraOffsetModify.Z, Modifer.Value.SocketCameraOffsetModify.Z);
			TickArmInfo.ChangeSpeed = FMath::Max(TickArmInfo.ChangeSpeed, Modifer.Value.ChangeSpeed);
			// TickArmInfo.ResetSpeed = FMath::Max(TickArmInfo.ResetSpeed, Modifer.Value.ResetSpeed);
		}
		i++;
	}
}

void UAwCameraComponent::ThisTickLock(float DeltaTime)
{
	USceneComponent* LockRenderComponent = Cast<USceneComponent>(OnLockTarget->GetComponentByClass(UCameraLockPointComponent::StaticClass()));
	if (!IsValid(LockRenderComponent))
	{
		UnlockCurTarget();
		return;
	}
	FVector LockPoint = LockRenderComponent->GetComponentLocation();
	FVector2D ProjectPoint;
	auto pc = UGameplayFuncLib::GetPlayerControllerByComp(this);
	//是否丢失渲染目标
	 bool bInRender = OnLockTarget->WasRecentlyRendered()&&UWidgetLayoutLibrary::ProjectWorldLocationToWidgetPosition(pc,LockPoint,ProjectPoint,true);
	
	if (bInRender)
	{
		RenderKeepLockDuration = MaxRenderKeepLockDuration;
	}
	else 
	{
		RenderKeepLockDuration-=DeltaTime;
		bInRender = RenderKeepLockDuration>0||bInRender;
	}

	//目标是否依旧有效
	bool VaildCharacter = true;

	if (OnLockTarget->GetClass()->IsChildOf(AAwCharacter::StaticClass()))
	{
		AAwCharacter* LockCharacter = Cast<AAwCharacter>(OnLockTarget);
		if (LockCharacter->Dead())
		{
			VaildCharacter= LockNewTargetInRangeOnLastDead(LockCharacter);
		}
	}
	
	if (IsValid(LockRenderComponent)&&VaildCharacter&&bInRender)
	{
		FRotator Rotator = CaluFinalLockRotator();
		FRotator RotatorDiff = UMathFuncLib::Aw_RotatorOffsetNormalize(GetCameraRotate(),Rotator);
		/*
		UKismetSystemLibrary::PrintString(this,"TRotate_"+Rotator.ToString()+"_CRotate_"+GetCameraRotate().ToString()+"_Diff_"+RotatorDiff.ToString(),
			true,true,FColor::Red,DeltaTime);
		*/
		//float A = UMathFuncLib::NormalizeAngle(Rotator.Yaw,0);
		//SetCameraRotation(RotatorDiff+GetCameraRotate());
		//float YawOffset=;

		FVector CamToLock = LockRenderComponent->GetComponentLocation() - pc->PlayerCameraManager->GetCameraLocation();
		
		FRotator CurRotator = GetCameraRotate();

		bool bNeedFixLock = false;
		
		CurRotator.Roll = 0;

		//Yaw轴临界判断 超出临界则锁死回到临界
		/*
		FVector CamToLock2D = FVector(CamToLock.X,CamToLock.Y,0.f);
		FVector CurYaw = FVector(GetCameraRotate().Vector().X,GetCameraRotate().Vector().Y,0.f);
		float YawDisDegree = UMathFuncLib::GetDegreeBetweenTwoVector(CurYaw,CamToLock2D);
		*/

		
		if (FMath::Abs(RotatorDiff.Yaw) > FreeLockRange.Yaw)
		{
			float WillChangeYaw = RotatorDiff.Yaw > 0 ? (RotatorDiff.Yaw - FreeLockRange.Yaw) : (RotatorDiff.Yaw + FreeLockRange.Yaw);
			CurRotator.Yaw = GetCameraRotate().Yaw + WillChangeYaw * 1;
			bNeedFixLock = true;
		}
		
		
		/*
		else if (FMath::Abs(RotatorDiff.Yaw) > FixLockRange.Yaw)
		{
			if (FMath::IsNearlyEqual(GetCameraRotate().Yaw,Rotator.Yaw,10*DeltaTime))
			{
				CurRotator.Yaw = GetCameraRotate().Yaw;
			}
			else
			{
				CurRotator.Yaw = UKismetMathLibrary::RLerp( GetCameraRotate(),Rotator,DeltaTime*YawLockFixSpeed,true).Yaw;
			}
			bNeedFixLock=true;
		}
		*/
		/*
		//Pitch轴临界判断 超出临界则锁死回到临界
		float PitchDisDegree =GetCameraRotate().Pitch>180?GetCameraRotate().Pitch-360 - Rotator.Pitch:GetCameraRotate().Pitch-Rotator.Pitch;
		*/

		/*
		UKismetSystemLibrary::PrintString(this,"PitchFix"+FString::SanitizeFloat(RotatorDiff.Pitch)+"_OPitch_"+FString::SanitizeFloat(GetCameraRotate().Pitch)+"_TPitch_"+FString::SanitizeFloat(Rotator.Pitch),
	true,true,FColor::Red,DeltaTime);
		*/
		if (FMath::Abs(RotatorDiff.Pitch) > FreeLockRange.Pitch)
		{
			float WillChangePitch = RotatorDiff.Pitch>0 ? (RotatorDiff.Pitch - FreeLockRange.Pitch) : (RotatorDiff.Pitch + FreeLockRange.Pitch);
			CurRotator.Pitch = GetCameraRotate().Pitch + WillChangePitch * -1;
			bNeedFixLock=true;
		}//临界与自由区域中间为缓动区域 逐渐将目标拉至视角中心自由区域
		else if (FMath::Abs(RotatorDiff.Pitch) > FixLockRange.Pitch)
		{
			 if (FMath::IsNearlyEqual(GetCameraRotate().Pitch,Rotator.Pitch,10*DeltaTime))
			{
				CurRotator.Pitch = GetCameraRotate().Pitch;
			}
			else
			{
				float FinalPitchLockFixSpeed = PitchLockFixSpeed*FMath::Abs(RotatorDiff.Pitch);
				CurRotator.Pitch = UKismetMathLibrary::RLerp( GetCameraRotate(),Rotator,DeltaTime*PitchLockFixSpeed,true).Pitch;
			}
			bNeedFixLock=true;
		}
		
		if (bNeedFixLock)
		{
			SetCameraRotation(CurRotator);
		}

	}
	else
	{
		//脱离视野超时解锁
		 UnlockCurTarget();
	}
}

UAwCameraComponent::UAwCameraComponent()
{
	PrimaryComponentTick.bCanEverTick = true;
}

void UAwCameraComponent::BeginPlay()
{
	Super::BeginPlay();

	Character = Cast<AAwCharacter>(GetOwner());
	SpringArm = Cast<USpringArmComponent>(Character->GetComponentByClass(USpringArmComponent::StaticClass()));
	Camera = Cast<UCameraComponent>(SpringArm->GetChildComponent(0));
}

void UAwCameraComponent::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
	Super::TickComponent(DeltaTime, TickType, ThisTickFunction);
	if (!Character->OwnerPlayerController)return;
	ThisTickPos(DeltaTime);
	TickResetSpeed(DeltaTime);

	float TargetArmLength = 0;
	//摇臂尺寸
	if (Character->CharacterAttachment.AttachTarget == nullptr)
	{
		TargetArmLength = FAwCameraPosInfo::DefaultArmLength() + GetModifyArmLengthByLockTarget() + TickArmInfo.ArmLengthModify;
	}
	else
	{
		const AAwCharacter* AttachTarget = Cast<AAwCharacter>(Character->CharacterAttachment.AttachTarget->GetOwner());
		if (AttachTarget)
		{
			const float Fat = FMath::Max(0.00f, AttachTarget->GetCapsuleComponent()->GetScaledCapsuleRadius() - 35.00f) * 5.00f;
			TargetArmLength = FAwCameraPosInfo::DefaultArmLength() + GetModifyArmLengthByLockTarget() + TickArmInfo.ArmLengthModify + Fat;
		}
	}

	const float InterpSpeed = bIsReset ? ResetSpeed : TickArmInfo.ChangeSpeed;
	
	if (this->GetSpring()->TargetArmLength != TargetArmLength)
	{
		if (FMath::Abs(this->GetSpring()->TargetArmLength - TargetArmLength) > 0.1f)
			this->GetSpring()->TargetArmLength = UMathFuncLib::FInterpTo(
				this->GetSpring()->TargetArmLength,
				TargetArmLength, DeltaTime, InterpSpeed, 0.1f);
		else
			this->GetSpring()->TargetArmLength = TargetArmLength;
	}

	// UKismetSystemLibrary::PrintString(this, FString::SanitizeFloat(bIsReset ? ResetSpeed : TickArmInfo.ChangeSpeed));
	// UKismetSystemLibrary::PrintString(this, (TickArmInfo.DefaultSocketCameraOffset + TickArmInfo.SocketCameraOffsetModify).ToString());
	
	if (this->GetSpring()->SocketOffset != FAwCameraPosInfo::DefaultSocketCameraOffset() + TickArmInfo.SocketCameraOffsetModify)
	{
		if (FVector::DistSquared(this->GetSpring()->SocketOffset,
			FAwCameraPosInfo::DefaultSocketCameraOffset() + TickArmInfo.SocketCameraOffsetModify) > 0.01f)
			this->GetSpring()->SocketOffset = UMathFuncLib::VInterpTo(
				this->GetSpring()->SocketOffset,
				FAwCameraPosInfo::DefaultSocketCameraOffset() + TickArmInfo.SocketCameraOffsetModify,
				DeltaTime, InterpSpeed, 0.01f);
		else
			this->GetSpring()->SocketOffset = FAwCameraPosInfo::DefaultSocketCameraOffset() + TickArmInfo.SocketCameraOffsetModify;
	}

	//玩家视角判断 理论上这个组件旧不应该是外挂逻辑 理应替换PlayerCameraManager 或 CameraComponent 
	if (Character->IsPlayerCharacter())
	{
		if (IsValid(OnLockTarget.Get()))
		{
			ThisTickLock(DeltaTime);
		}
		else
		{
			UAwAnimInstance* AnimInstance = Character->GetAwAnimInstance();
			if (AimRotateLimitInfo.IsActive)
			{
				// UKismetSystemLibrary::PrintString(Character, "----------");
				// UKismetSystemLibrary::PrintString(Character, "CameraRotate: " + GetCameraRotate().ToString());
				// UKismetSystemLibrary::PrintString(Character, "OriCameraRotate" + AimRotateLimitInfo.OriCameraRotate.ToString());
			
				const float CameraYaw = UMathFuncLib::NormalizeAngle(GetCameraRotate().Yaw, 2);
				const float CameraPitch = UMathFuncLib::NormalizeAngle(GetCameraRotate().Pitch, 2);

				const float YawDis = CameraYaw - UMathFuncLib::NormalizeAngle(Character->GetActorRotation().Yaw, 2);
				const float PitchDis = CameraPitch - 0;
				
				// UKismetSystemLibrary::PrintString(Character, " - Yaw:   " + FString::SanitizeFloat(YawDis));
				// UKismetSystemLibrary::PrintString(Character, " - Pitch: " + FString::SanitizeFloat(PitchDis));
				
				if (AnimInstance)
				{
					AnimInstance->AimYaw = FMath::Clamp(YawDis / AimRotateLimitInfo.RotateLimit_Yaw, -1, 1);
					// UKismetSystemLibrary::PrintString(Character, "AnimInstance->AimYaw:     " + FString::SanitizeFloat(AnimInstance->AimYaw));
					AnimInstance->AimPitch = FMath::Clamp(PitchDis / AimRotateLimitInfo.RotateLimit_Pitch, -1, 1);
					// UKismetSystemLibrary::PrintString(Character, "AnimInstance->AimPitch:   " + FString::SanitizeFloat(AnimInstance->AimPitch));
				}

				const FRotator NowRotator = GetCameraRotate();
				const float NowPitch = UMathFuncLib::NormalizeAngle(GetCameraRotate().Pitch,2);
				const float MaxPitch = AimRotateLimitInfo.RotateLimit_Pitch;
				const float MinPitch = -AimRotateLimitInfo.RotateLimit_Pitch;

				const FVector NowForward = NowRotator.Vector();
				const FVector MaxForward = FRotator(
								NowRotator.Pitch,
								AimRotateLimitInfo.OriCameraRotate.Yaw + AimRotateLimitInfo.RotateLimit_Yaw,
								NowRotator.Roll).Vector();
				const FVector MinForward = FRotator(
								NowRotator.Pitch,
								AimRotateLimitInfo.OriCameraRotate.Yaw - AimRotateLimitInfo.RotateLimit_Yaw,
								NowRotator.Roll).Vector();
				
				FRotator NewRotator = NowRotator;
				if (NowPitch > MaxPitch) NewRotator.Pitch = MaxPitch;
				if (NowPitch < MinPitch) NewRotator.Pitch = MinPitch;
				if (UMathFuncLib::GetDegreeBetweenTwoVector(NowForward, MaxForward) < 0)
				{
					if (AimRotateLimitInfo.CanYawReset)
						AimRotateLimitInfo.OriCameraRotate.Yaw = NowRotator.Yaw;
					else
						NewRotator.Yaw = AimRotateLimitInfo.OriCameraRotate.Yaw + AimRotateLimitInfo.RotateLimit_Yaw;	
				}
				if (UMathFuncLib::GetDegreeBetweenTwoVector(NowForward, MinForward) > 0)
				{
					if (AimRotateLimitInfo.CanYawReset)
						AimRotateLimitInfo.OriCameraRotate.Yaw = NowRotator.Yaw;
					else
						NewRotator.Yaw = AimRotateLimitInfo.OriCameraRotate.Yaw - AimRotateLimitInfo.RotateLimit_Yaw;
				}

				if (NewRotator != NowRotator)
					SetCameraRotation(NewRotator);
			}
			else
			{
				if (AnimInstance)
				{
					AnimInstance->AimYaw = 0;
					AnimInstance->AimPitch = 0;
				}
			}	
		}
	}

	//最小渲染距离相关逻辑  处理墙角视角
	if( Character->IsPlayerCharacter() &&
		Character->OwnerPlayerController->GameControlState != EGameControlState::Sequence )
	{
		AAwPlayerController* pc = Cast<AAwPlayerController>(Character->GetController());
		const float FixedLen =
		 	GetSpring()->TargetArmLength -
		 	FVector::Distance(GetSpring()->GetUnfixedCameraPosition(),
		 					  pc->PlayerCameraManager->GetCameraLocation());

		const bool FitRenderDistance = FixedLen >= MinRenderLength;
		
		if (FitRenderDistance != CameraTargetInRenderDistance)
		{
			CameraTargetInRenderDistance = FitRenderDistance;
			if (!FitRenderDistance)
			{
				// for (auto Cmp:Character->GetAllMeshComponents())
				// {
				// 	Cmp->SetVisibility(false);
				// }
				Character->HideInCamera();
			}
			else
			{
				// for (auto Cmp:Character->GetAllMeshComponents())
				// {
				// 	Cmp->SetVisibility(true);
				// }
				Character->RestoreInCamera();
			}
		}
	}
}

void UAwCameraComponent::RemoveArmModiferById(FString UniqueId)
{
	if (ArmModifers.Contains(UniqueId))
	{
		TargetResetSpeed = ArmModifers[UniqueId].ResetSpeed * 3;
		ResetSpeed = 0;
		ArmModifers.Remove(UniqueId);
	}
}

USpringArmComponent* UAwCameraComponent::GetSpring()
{
	return SpringArm;
}

FRotator UAwCameraComponent::GetCameraRotate()
{
	if (Character)
		return Character->GetControlRotation();
	return FRotator::ZeroRotator;
}

FVector2D UAwCameraComponent::GetRotateDir()
{
	if (AimRotateLimitInfo.IsActive)
		return FVector2D(AimRotateLimitInfo.OriCameraRotate.Vector());
	else
		return FVector2D(GetCameraRotate().Vector());
}

void UAwCameraComponent::SetCameraRotationByForward(const FVector Forward)
{
	if (Character && Character->OwnerPlayerController)
	{
		Character->OwnerPlayerController->SetControlRotation(Forward.Rotation());
		if (AimRotateLimitInfo.IsActive && AimRotateLimitInfo.CanYawReset)
			AimRotateLimitInfo.OriCameraRotate.Yaw = Forward.Rotation().Yaw; 
	}
}

void UAwCameraComponent::SetCameraRotation(FRotator NewRotator)
{
	if (Character && Character->OwnerPlayerController)
	{
		Character->OwnerPlayerController->SetControlRotation(NewRotator);
		if (AimRotateLimitInfo.IsActive && AimRotateLimitInfo.CanYawReset)
			AimRotateLimitInfo.OriCameraRotate.Yaw = NewRotator.Yaw;
	}
}

void UAwCameraComponent::FindTarget()
{
	AAwGameState* GameState = UGameplayFuncLib::GetAwGameState();

	FVector ToForward = Character->GetActorForwardVector();
	
	if (GameState)
	{
		FVector ViewTarget = Character->OwnerPlayerController->PlayerCameraManager->GetViewTarget()->GetActorLocation();
		
		AAwCharacter* Boss = GameState->GetBoss();
		if (Boss)
			ToForward = Boss->GetActorLocation() - ViewTarget;
		else
		{
			TArray<AAwCharacter*> Elites = GameState->GetElites();
			if (Elites.Num() > 0)
			{
				for (AAwCharacter* Elite : Elites)
				{
					ToForward = Elite->GetActorLocation() - ViewTarget;
					break;
				}
			}
			else
			{
				TArray<AAwCharacter*> NormalMobs = GameState->GetNormalEnemy();
				if (NormalMobs.Num() > 0)
				{
					AAwCharacter* NearlyMob =UGameplayFuncLib::FindNearlyAngleCharacter(Character,UGameplayFuncLib::FindNearlyAngleCharacterInSector(180, Character,NormalMobs));
					if (NearlyMob)
						ToForward = NearlyMob->GetActorLocation() - ViewTarget;
				}
			}
		}
	}

	// UKismetSystemLibrary::PrintString(this, ToForward.Rotation().ToString());
	// SetCameraRotationByForward(ToForward);

	FRotator ToRotator = ToForward.Rotation();
	ToRotator.Pitch = -10;
	SetCameraRotation(ToRotator);
}

void UAwCameraComponent::LockTarget()
{
	if (IsValid(OnLockTarget.Get()))
	{
		//如果此次是解锁 且成功则关闭后续逻辑
		if (UnlockCurTarget())
		{
			return;
		}
	}
	else
	{
		AAwGameState* GameState = UGameplayFuncLib::GetAwGameState();
		//获取最近渲染在屏幕内的敌人单位 
		TArray<AAwCharacter*>Targets = GameState->GetAllEnemy(true,0.01);
		
		//角度范围二次筛选
		//Targets = UGameplayFuncLib::FindNearlyAngleCharacterInSector(180,Targets);
		SortLockTargetsByComplexWeight(Targets);
		//排序完 第一个就是最优先锁定目标
		if (!Targets.IsEmpty())
		{
			//锁定优先级排序
			OnLockTarget = Targets[0];
			//锁定目标执行 被锁后的事情 如 显示锁定UI
			LockCurTarget();
			//角色的话绑定死亡
			if (OnLockTarget->GetClass()->IsChildOf(AAwCharacter::StaticClass()))
			{
				AAwCharacter* NewTarget = Cast<AAwCharacter>(OnLockTarget.Get());
				if (IsValid(NewTarget))
				{
					//NewTarget->OnCharacterDeadDelegate.AddUniqueDynamic(this,&UAwCameraComponent::LockNewTargetInRangeOnLastDead);
				}
			}
		}
		else
		{
			//旧逻辑 单次视角修正
			FindTarget();
		}
	}
}

bool UAwCameraComponent::LockNewTargetInRangeOnLastDead(AAwCharacter* DeadCharacter)
{
	AAwGameState* GameState = UGameplayFuncLib::GetAwGameState();
	//获取最近渲染在屏幕内的敌人单位 
	TArray<AAwCharacter*>Targets = GameState->GetAllEnemy(true,0.01);
	for (TArray<AAwCharacter*>::TIterator It(Targets); It; ++It)
	{
		 bool InRange = FVector::Distance(DeadCharacter->GetActorLocation(),(*It)->GetActorLocation())<=ReLockRangeOnDead;
		FVector  TargetVec =(*It)->GetActorLocation() -UGameplayFuncLib::GetPlayerControllerByComp(this)->PlayerCameraManager->GetCameraLocation();
		 bool InForward = GetCameraRotate().Vector().Dot(TargetVec)>0;
		bool InLive = !(*It)->Dead();
		if (!InRange||!InForward||!InLive)
		{
			It.RemoveCurrent();
		}
	}
	auto PC = UGameplayFuncLib::GetPlayerControllerByComp(this);
	
	if (!Targets.IsEmpty())
	{
		UnlockCurTarget();
		//锁定优先级排序
		OnLockTarget = Targets[0];
		//锁定目标执行 被锁后的事情 如 显示锁定UI
		LockCurTarget();
		if (PC)
			PC->CheckLockSign();
		return  true;
	}
	else
	{
		if (PC)
			PC->HideLockSign();
	}
	return  false;
}
/*
void UAwCameraComponent::LockNewTargetInRangeOnLastDead(AAwCharacter* DeadCharacter)
{
	AAwGameState* GameState = UGameplayFuncLib::GetAwGameState();
	//获取最近渲染在屏幕内的敌人单位 
	TArray<AAwCharacter*>Targets = GameState->GetAllEnemy(true,0.01);
	for (TArray<AAwCharacter*>::TIterator It(Targets); It; ++It)
	{
		bool InRange = FVector::Distance(DeadCharacter->GetActorLocation(),(*It)->GetActorLocation())<=ReLockRangeOnDead;
		FVector  TargetVec =(*It)->GetActorLocation() -UGameplayFuncLib::GetMyAwPlayerController()->PlayerCameraManager->GetCameraLocation();
		bool InForward = GetCameraRotate().Vector().Dot(TargetVec)>0;
		bool InLive = !(*It)->Dead();
		if (!InRange||!InForward||!InLive)
		{
			It.RemoveCurrent();
		}
	}
	
	if (!Targets.IsEmpty())
	{
		UnlockCurTarget();
		//锁定优先级排序
		OnLockTarget = Targets[0];
		//锁定目标执行 被锁后的事情 如 显示锁定UI
		LockCurTarget();
	}
}
*/


void UAwCameraComponent::SwitchLockTarget(bool bLeft)
{
	if (IsValid(OnLockTarget.Get()))
	{
		AAwGameState* GameState = UGameplayFuncLib::GetAwGameState();
		//获取最近渲染在屏幕内的敌人单位 
		TArray<AAwCharacter*>Targets = GameState->GetAllEnemy(true,0.01);
		Targets.Remove(Cast<AAwCharacter>(OnLockTarget.Get()));
		SortLockTargetsByComplexWeight(Targets,true,bLeft);
		
		//排序完 第一个就是最优先锁定目标
		if (!Targets.IsEmpty())
		{
			UnlockCurTarget();
			//锁定优先级排序
			OnLockTarget = Targets[0];
			//锁定目标执行 被锁后的事情 如 显示锁定UI
			LockCurTarget();
		}
	}
	else
	{
		return;	
	}
}


void UAwCameraComponent::AddPitchInput(const float Val) const
{
	if (!Character || Val == 0)
		return;
	
	Character->AddControllerPitchInput(Val);
}

void UAwCameraComponent::AddYawInput(const float Val) const
{
	if (!Character || Val == 0)
		return;
	
	Character->AddControllerYawInput(Val);
}

FRotator UAwCameraComponent::CaluFinalLockRotator()
{
	USceneComponent* LockRenderComponent = Cast<USceneComponent>(OnLockTarget->GetComponentByClass(UCameraLockPointComponent::StaticClass()));
	
	FRotator Rotator = FRotator::ZeroRotator;
	/*
	FVector CameraSpringOffset = SpringArm->SocketOffset + FVector(SpringArm->TargetArmLength*-1,0,0);
	
	FRotator OrgRotator  = FRotator::ZeroRotator;
	OrgRotator.Yaw = GetCameraRotate().Yaw;
	//避免过度俯视 或 仰视
	//OrgRotator.Pitch = -10;
	//OrgRotator.Pitch = FMath::Clamp(GetCameraRotate().Pitch,-15,15);
	
	//反推预想位置
	CameraSpringOffset = CameraSpringOffset.RotateAngleAxis(-1*OrgRotator.Pitch,FVector(0,1,0));
	CameraSpringOffset = CameraSpringOffset.RotateAngleAxis(	OrgRotator.Yaw,FVector(0,0,1));

	FTransform Martix;
	Martix.SetLocation(Character->GetActorLocation());
	//Martix.SetRotation(GetCameraRotate().Quaternion());
	Martix.SetRotation(FRotator::ZeroRotator.Quaternion());
	Martix.SetScale3D(FVector(1,1,1));
	
	FVector StartPoint = UKismetMathLibrary::TransformLocation(Martix,CameraSpringOffset);
	*/
	auto pc = UGameplayFuncLib::GetPlayerControllerByComp(this);
	FVector StartPoint = pc->PlayerCameraManager->GetCameraLocation();
	//反推位置上的看向旋转
	if (IsValid(LockRenderComponent))
	{
		FRotator	DefaultRotator = UKismetMathLibrary::FindLookAtRotation(StartPoint,LockRenderComponent->GetComponentLocation());
		float MinPitch =LockPitchMin;
		float MaxPitch = LockPitchMax;
		
		if (IsValid(pc->CurCharacter))
		{
			float ZDistance = (pc->CurCharacter->GetActorLocation()-LockRenderComponent->GetComponentLocation()).Z;
			if (FMath::Abs(ZDistance)<LockPitchSafeZ)
			{
				//以min为主
				 MinPitch =LockSafePitchMin;
				 MaxPitch = LockSafePitchMax;
			}
			else
			{
				if (ZDistance>0)
				{
					MaxPitch  =  LockPitchMax* FMath::Abs(FMath::Clamp(ZDistance,0,ZDistance))/LockPitchZStandardDistance;
					MaxPitch =     FMath::Clamp(MaxPitch,LockPitchMin,LockPitchMax);
				}
				else
				{
					MinPitch  =  LockPitchMin* FMath::Abs(FMath::Clamp(ZDistance,ZDistance,0))/LockPitchZStandardDistance;
					MinPitch =     FMath::Clamp(MinPitch,LockPitchMin,LockPitchMax);
				}
			}

		}

		DefaultRotator.Pitch = FMath::Clamp(DefaultRotator.Pitch,MinPitch,MaxPitch);
		Rotator = DefaultRotator;
	}
	return  Rotator;
}

AActor* UAwCameraComponent::GetCurLockTarget()
{
	if (GetOnLockTarget().Get())
		return GetOnLockTarget().Get();
	
	return nullptr;
}

float UAwCameraComponent::GetModifyArmLengthByLockTarget()
{
	if (!Character)
		return 0;
	
	if (AActor* CurLockTarget = GetCurLockTarget())
	{
		if (const AAwCharacter* Target = Cast<AAwCharacter>(CurLockTarget))
		{
			// 瞄准对象比我高了多少
			float Taller = (Target->GetCapsuleComponent()->GetScaledCapsuleHalfHeight() - 
			Character->GetCapsuleComponent()->GetScaledCapsuleHalfHeight()) * 2;
			if (Taller < 0) Taller = 0;
			
			// 瞄准对象与我的距离的平方（忽略高度）
			float Distance = FVector2D::Distance(FVector2D(Character->GetActorLocation()), FVector2D(Target->GetActorLocation()));
			const UCapsuleComponent* MyCapsule = Cast<UCapsuleComponent>(Character->GetSqueezeComp()->ShapeCollision);
			const UCapsuleComponent* TargetCapsule =Target->GetSqueezeComp()?Cast<UCapsuleComponent>(Target->GetSqueezeComp()->ShapeCollision):nullptr;
			Distance -= MyCapsule ? MyCapsule->GetScaledCapsuleRadius() : 0;
			Distance -= TargetCapsule ? TargetCapsule->GetScaledCapsuleRadius() : 0;
			if (Distance < 0) Distance = 0;
			
			const float Rate = 1 - FMath::Clamp(Distance / Taller / 4, 0, 1);

			// FString Str = " ";
			// Str.Append(" Distance: ").Append(FString::SanitizeFloat(Distance));
			// Str.Append(" Rate: ").Append(FString::SanitizeFloat(Rate));
			// Str.Append(" Value: ").Append(FString::SanitizeFloat(Taller * Rate * 1.5));
			// UKismetSystemLibrary::PrintString(this, Str);
			
			return Taller * Rate * 1.5;
		}
	}

	return 0;
}

void UAwCameraComponent::SortLockTargetsByComplexWeight(TArray<AAwCharacter*>& Source,bool bDirectionOnly,bool bLeft )
{
	
	TMap<AAwCharacter*,float>SortSorces;
	auto pc = UGameplayFuncLib::GetPlayerControllerByComp(this);
	for (auto Target:Source)
	{
		//float Weight = 0.f;
		FVector2D ScreenCenter = UWidgetLayoutLibrary::GetViewportSize(this)/2;
		
		FVector CameraPos;
		FVector ForwardVec;
		UGameplayStatics::DeprojectScreenToWorld(pc,ScreenCenter,CameraPos,ForwardVec);

		//转屏幕坐标系
		FVector2D ScreenPos;
		bool bInView = UGameplayStatics::ProjectWorldToScreen(pc,Target->GetActorLocation(),ScreenPos);
		if (ScreenPos.X<0||ScreenPos.X>ScreenCenter.X*2||ScreenPos.Y<0||ScreenPos.Y>ScreenCenter.Y*2)
		{
			bInView = false;
		}
		if (!bInView)
		{
			continue;
		}
		float Distance = FVector::Distance(CameraPos,Target->GetActorLocation());
		//初次锁定极限距离
		if (Distance>4000.f)
		{
			continue;
		}
		float ScreenDistance = FVector2d::Distance(ScreenPos,ScreenCenter);
		FVector2D CurTargetScreenPos;
		if (bDirectionOnly&&IsValid(OnLockTarget.Get()))
		{
			UGameplayStatics::ProjectWorldToScreen(pc,OnLockTarget->GetActorLocation(),CurTargetScreenPos);
			ScreenDistance = FVector2d::Distance(ScreenPos,CurTargetScreenPos);
		}
		
		//特定方向筛选 常态不启用
		if (bDirectionOnly)
		{
			bool bFit =  (bLeft&&ScreenPos.X<=CurTargetScreenPos.X)||(!bLeft&&ScreenPos.X>CurTargetScreenPos.X);
			if (!bFit)
			{
				continue;
			}
		}
		
		float Weight = 0;
		//权重计算
		Weight +=  1- ScreenDistance/ScreenCenter.Length();
		Weight +=  1-Distance*0.5/4000;
		Weight += Target->CharacterObj.MobRank == EMobRank::Boss?2:Target->CharacterObj.MobRank==EMobRank::Elite?1:0;
		
		SortSorces.Add(Target,Weight );
	}
		SortSorces.ValueSort([](float A,float B)
			{
				return A>B;
			}
		);

	/*
	for (auto A:SortSorces)
	{
		FString P = A.Key->GetName()+"_Weight:"+FString::SanitizeFloat(A.Value.Distance1);
		UKismetSystemLibrary::PrintString(this,P,true,true,FColor::Red,30.f);
	}
	*/
		
	SortSorces.GenerateKeyArray(Source);
}


bool UAwCameraComponent::LockCurTarget()
{
	//锁定目标执行 被锁后的事情 如 显示锁定UI
	if ( IsValid(OnLockTarget.Get())&&OnLockTarget->Implements<UAwCameraInterface>())
	{
		IAwCameraInterface::Execute_OnLock(OnLockTarget.Get());
		return true;
	}
	return false;
}

bool UAwCameraComponent::UnlockCurTarget()
{
	//解除锁定目标执行 
	if (IsValid(OnLockTarget.Get()))
	{
		if (OnLockTarget->Implements<UAwCameraInterface>())
		{
			IAwCameraInterface::Execute_OnUnLock(OnLockTarget.Get());
		}
		OnLockTarget = nullptr;
		
		//还原锁定渲染丢失保留时间
		RenderKeepLockDuration=MaxRenderKeepLockDuration;
		return true;
	}
	return false;
}
