// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "AwCameraPosInfo.h"
#include "GameFramework/SpringArmComponent.h"
#include "AwCameraPawn.generated.h"

class AAwCharacter;

UCLASS()
class THEAWAKENER_FO_API AAwCameraPawn : public APawn
{
	GENERATED_BODY()

public:
	// Sets default values for this actor's properties
	AAwCameraPawn();

protected:
	// Called when the game starts or when spawned
	virtual void BeginPlay() override;

	UPROPERTY()
	FAwCameraPosInfo DefaultArmInfo = FAwCameraPosInfo();

	//摇臂到要跟随的角色的距离
	UPROPERTY()
	FVector SpringDistanceToTarget = FVector::ZeroVector;
	
public:
	// Called every frame
	virtual void Tick(float DeltaTime) override;

	UPROPERTY()
	TMap<FString, FAwCameraPosInfo> ArmModifers;
	UFUNCTION()
	void RemoveArmModiferById(FString UniqueId);

	UFUNCTION(BlueprintImplementableEvent)
	void SetViewTarget(AActor* Target);
	
	UFUNCTION(BlueprintCallable)
	USpringArmComponent* GetSpring();

	UFUNCTION(BlueprintCallable)
	FRotator GetCameraRotate();

	// up
	void AddPitchInput(float Val);
	// Right
	void AddYawInput(float Val);

	void SetCameraRotation(FVector Forward);
	
	UPROPERTY(BlueprintReadWrite)
	AAwCharacter* TargetCharacter;
	
private:
	UPROPERTY()
	USpringArmComponent* Spring;

	UPROPERTY()
	FAwCameraPosInfo TickArmInfo;

	UPROPERTY()
	bool bIsReset;
	// 目标还原速度，以为内还原的时候 ArmModifer 列表为空了，所以还原速度就记录一下
	float TargetResetSpeed;
	// 还原速度太突然了，加了lerp过度一下
	float ResetSpeed;
	// 还原速度太突然了，加了lerp过度一下
	void TickResetSpeed(float DeltaTime);
	
	void ThisTickPos(float DeltaTime);
};
