// Fill out your copyright notice in the Description page of Project Settings.


#include "AwCameraPawn.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/FunctionLibrary/MathFuncLib.h"


// Sets default values
AAwCameraPawn::AAwCameraPawn()
{
	// Set this actor to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryActorTick.bCanEverTick = true;
}

// Called when the game starts or when spawned
void AAwCameraPawn::BeginPlay()
{
	Super::BeginPlay();
}

// Called every frame
void AAwCameraPawn::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);
	
	ThisTickPos(DeltaTime);
	TickResetSpeed(DeltaTime);
	
	if (IsValid(TargetCharacter))
	{
		float TargetArmLength = 0;
		//摇臂尺寸
		if (TargetCharacter->CharacterAttachment.AttachTarget == nullptr)
		{
			TargetArmLength = FAwCameraPosInfo::DefaultArmLength() + TickArmInfo.ArmLengthModify;
		}else
		{
			const AAwCharacter* AttachTarget = Cast<AAwCharacter>(TargetCharacter->CharacterAttachment.AttachTarget->GetOwner());
			if (AttachTarget)
			{
				const float Fat = FMath::Max(0.00f, AttachTarget->GetCapsuleComponent()->GetScaledCapsuleRadius() - 35.00f) * 5.00f;
				TargetArmLength = FAwCameraPosInfo::DefaultArmLength() + TickArmInfo.ArmLengthModify + Fat;
			}
		}

		const float InterpSpeed = bIsReset ? ResetSpeed : TickArmInfo.ChangeSpeed;
		
		if (this->GetSpring()->TargetArmLength != TargetArmLength)
		{	
			if (FMath::Abs(this->GetSpring()->TargetArmLength - TargetArmLength) > 0.1f)
				this->GetSpring()->TargetArmLength = UMathFuncLib::FInterpTo(
					this->GetSpring()->TargetArmLength,
					TargetArmLength, DeltaTime, InterpSpeed, 0.1f);
			else
				this->GetSpring()->TargetArmLength = TargetArmLength;
		}

		// UKismetSystemLibrary::PrintString(this, FString::SanitizeFloat(bIsReset ? ResetSpeed : TickArmInfo.ChangeSpeed));
		// UKismetSystemLibrary::PrintString(this, (TickArmInfo.DefaultSocketCameraOffset + TickArmInfo.SocketCameraOffsetModify).ToString());
		
		if (this->GetSpring()->SocketOffset != FAwCameraPosInfo::DefaultSocketCameraOffset() + TickArmInfo.SocketCameraOffsetModify)
		{
			if (FVector::DistSquared(this->GetSpring()->SocketOffset,
				FAwCameraPosInfo::DefaultSocketCameraOffset() + TickArmInfo.SocketCameraOffsetModify) > 0.01f)
				this->GetSpring()->SocketOffset = UMathFuncLib::VInterpTo(
					this->GetSpring()->SocketOffset,
					FAwCameraPosInfo::DefaultSocketCameraOffset() + TickArmInfo.SocketCameraOffsetModify,
					DeltaTime, InterpSpeed, 0.01f);
			else
				this->GetSpring()->SocketOffset = FAwCameraPosInfo::DefaultSocketCameraOffset() + TickArmInfo.SocketCameraOffsetModify;
		}
		
		//相机跟随人
		this->SetActorLocation(TargetCharacter->GetActorLocation() + SpringDistanceToTarget);
	}
}

void AAwCameraPawn::RemoveArmModiferById(FString UniqueId)
{
	if (ArmModifers.Contains(UniqueId))
	{
		TargetResetSpeed = ArmModifers[UniqueId].ResetSpeed * 3;
		ResetSpeed = 0;
		ArmModifers.Remove(UniqueId);
	}
}

USpringArmComponent* AAwCameraPawn::GetSpring()
{
	if (IsValid(Spring))
		return Spring;

	Spring = Cast<USpringArmComponent>(GetComponentByClass(USpringArmComponent::StaticClass()));
	return Spring;
}

FRotator AAwCameraPawn::GetCameraRotate()
{
	return this->GetActorRotation();
}

void AAwCameraPawn::AddPitchInput(float Val)
{
	FRotator Rotator = GetActorRotation();
	Rotator.Pitch += Val;
	// if (Rotator.Pitch >= TickArmInfo.PitchMax)
	// 	Rotator.Pitch = TickArmInfo.PitchMax;
	// if (Rotator.Pitch <= TickArmInfo.PitchMin)
	// 	Rotator.Pitch = TickArmInfo.PitchMin;
	SetActorRotation(Rotator);
}

void AAwCameraPawn::AddYawInput(float Val)
{
	FRotator Rotator = GetActorRotation();
	Rotator.Yaw += Val;
	SetActorRotation(Rotator);
}

void AAwCameraPawn::SetCameraRotation(FVector Forward)
{
	SetActorRotation(Forward.Rotation());
}

void AAwCameraPawn::ThisTickPos(float DeltaTime)
{
	if (ArmModifers.Num() <= 0)
	{
		TickArmInfo = DefaultArmInfo;
		return;
	}
	
	// 没有修改信息就是还原
	bIsReset = ArmModifers.Num() == 0;
	
	// 设置这一帧的ArmInfo
	TickArmInfo = FAwCameraPosInfo();
	int i = 0;
	for (const TTuple<FString, FAwCameraPosInfo> Modifer : ArmModifers)
	{
		if (i == 0)
		{
			TickArmInfo.ArmLengthModify = Modifer.Value.ArmLengthModify;
			TickArmInfo.SocketCameraOffsetModify = Modifer.Value.SocketCameraOffsetModify;
			TickArmInfo.ChangeSpeed = Modifer.Value.ChangeSpeed;
			// TickArmInfo.ResetSpeed = Modifer.Value.ResetSpeed;
		}
		else
		{
			TickArmInfo.ArmLengthModify = FMath::Max(TickArmInfo.ArmLengthModify, Modifer.Value.ArmLengthModify);
			TickArmInfo.SocketCameraOffsetModify.X = FMath::Max(TickArmInfo.SocketCameraOffsetModify.X, Modifer.Value.SocketCameraOffsetModify.X);
			TickArmInfo.SocketCameraOffsetModify.Y = FMath::Max(TickArmInfo.SocketCameraOffsetModify.Y, Modifer.Value.SocketCameraOffsetModify.Y);
			TickArmInfo.SocketCameraOffsetModify.Z = FMath::Max(TickArmInfo.SocketCameraOffsetModify.Z, Modifer.Value.SocketCameraOffsetModify.Z);
			TickArmInfo.ChangeSpeed = FMath::Max(TickArmInfo.ChangeSpeed, Modifer.Value.ChangeSpeed);
			// TickArmInfo.ResetSpeed = FMath::Max(TickArmInfo.ResetSpeed, Modifer.Value.ResetSpeed);
		}
		i++;
	}
}

void AAwCameraPawn::TickResetSpeed(float DeltaTime)
{
	if (bIsReset && ResetSpeed != TargetResetSpeed)
	{
		ResetSpeed += DeltaTime * 1.5;
		if (ResetSpeed > TargetResetSpeed)
			ResetSpeed = TargetResetSpeed;
	}
}