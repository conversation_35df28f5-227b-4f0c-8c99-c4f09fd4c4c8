// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Camera/CameraShakeBase.h"
#include "AwCameraShakeInfo.generated.h"

/**
 * 要求相机震动的数据
 */
USTRUCT(BlueprintType)
struct FAwCameraShakeInfo
{
	GENERATED_BODY()
public:
	//要播放的shake的蓝图
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TSubclassOf<UCameraShakeBase> Shake = nullptr;

	//播放的偏移位置，基于当前角色的Root点所在的位置做出的偏移
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FVector Offset = FVector::ZeroVector;

	//多少距离（厘米）半径内能感受到完整的振幅
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	float FullShockRange = 1000.00f;

	//多少距离（厘米）半径之外完全无法感受振幅
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	float LoseShockRange = 2500.00f;

	//衰减：事实上我也不知道咋填写，所以要不就别填写了
	UPROPERTY()
	float FallOff = 1.000f;

	//是否把Offset做为向量，向这个方向释放震源？用了以后不知道什么屌事儿，还是自动设置false，留个余地就好
	UPROPERTY()
	bool DoRotate = false;

	FAwCameraShakeInfo(){};
	FAwCameraShakeInfo(TSubclassOf<UCameraShakeBase> UseShake):Shake(UseShake){};
	FAwCameraShakeInfo(TSubclassOf<UCameraShakeBase> UseShake, float FullRange, float OutOfRange, FVector OffsetPos = FVector::ZeroVector):
		Shake(UseShake), Offset(OffsetPos), FullShockRange(FullRange), LoseShockRange(OutOfRange){};
	
};
