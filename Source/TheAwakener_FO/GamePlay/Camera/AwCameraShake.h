// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "LegacyCameraShake.h"
#include "AwCameraShake.generated.h"

/**
 * 
 */
UCLASS(Blueprintable, HideCategories = (CameraShakePattern))
class THEAWAKENER_FO_API UAwCameraShake : public ULegacyCameraShake
{
	GENERATED_BODY()

		UFUNCTION(BlueprintCallable, Category = Oscillation)
		void setOscillation(float TOscillationDuration, float TOscillationBlendInTime, float TOscillationBlendOutTime);
};
