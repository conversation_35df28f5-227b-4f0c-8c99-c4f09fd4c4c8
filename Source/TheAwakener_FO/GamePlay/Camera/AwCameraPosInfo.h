// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "AwCameraPosInfo.generated.h"

/**
 * 控制镜头移动的数据信息
 */
USTRUCT(BlueprintType)
struct FAwCameraPosInfo
{
	GENERATED_BODY()
public:
	//摇臂拉的速度（差值的速度）
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float ChangeSpeed = 1;

	//摇臂归位的速度（差值的速度）
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float ResetSpeed = 1;

	// 摇臂距离偏移（厘米）在默认距离上修改，所以拉近为负值
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float ArmLengthModify = 0;
	
	// 相机偏移（厘米）在默认offset上修改，所以可以为负值
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FVector SocketCameraOffsetModify = FVector(0, 0, 0);
	
	FAwCameraPosInfo();
	FAwCameraPosInfo(float NewChangeSpeed, float NewResetSpeed, float NewArmLengthModify, FVector NewSocketCameraOffsetModify);

	static float DefaultArmLength();
	static FVector DefaultSocketCameraOffset();
};
