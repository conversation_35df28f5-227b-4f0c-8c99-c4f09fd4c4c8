// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/BlendSpace.h"
#include "Camera/CameraActor.h"
#include "Camera/CameraComponent.h"
#include "GameFramework/Actor.h"
#include "AwUICamera.generated.h"

UCLASS()
class THEAWAKENER_FO_API AAwUICamera : public ACameraActor
{
	GENERATED_BODY()

public:
	// Sets default values for this actor's properties
	AAwUICamera();

protected:
	// Called when the game starts or when spawned
	virtual void BeginPlay() override;
	
public:
	// Called every frame
	virtual void Tick(float DeltaTime) override;
};
