// Fill out your copyright notice in the Description page of Project Settings.


#include "AwCameraPosInfo.h"

#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GameFramework/Manager/AwDataManager.h"

FAwCameraPosInfo::FAwCameraPosInfo()
{
	
}

FAwCameraPosInfo::FAwCameraPosInfo(float NewChangeSpeed, float NewResetSpeed, float NewArmLengthModify, FVector NewSocketCameraOffsetModify)
{
	ChangeSpeed = NewChangeSpeed;
	ResetSpeed = NewResetSpeed;
	ArmLengthModify = NewArmLengthModify;
	SocketCameraOffsetModify = NewSocketCameraOffsetModify;
}

float FAwCameraPosInfo::DefaultArmLength()
{
	if (UGameplayFuncLib::GetDataManager())
		return UGameplayFuncLib::GetDataManager()->DebugConfig.CameraArmLength;
	return 400;
}

FVector FAwCameraPosInfo::DefaultSocketCameraOffset()
{
	if (UGameplayFuncLib::GetDataManager())
		return UGameplayFuncLib::GetDataManager()->DebugConfig.CameraOffset;
	return FVector(20, 90, 90);
}
