// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Equipment.h"
#include "CharacterWearingAppearance.generated.h"

/**
 * 一个装备部位的具体信息，这记录了 他的Actor和创建Actor的蓝图信息
 */
USTRUCT(BlueprintType)
struct FWearingAppearanceInfo
{
	GENERATED_BODY()
public:
	//这个外观部位使用的蓝图
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString BPPath = "";

	//外观部位的Actor指针
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	AActor* Actor = nullptr;

	//这个部件替换掉身上某些块，仅适用于Type == SkinnedMesh的部位
	//取值范围：Head Body LeftArm RightArm LeftLeg RightLeg
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FString> HideBodyParts;

	//所属的装备部位，这个是便于从装备部位倒过来找到这个部位
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	EEquipmentPart EquipmentPart = EEquipmentPart::Invalid;

	//所属的Slot，这会影响到Conflict的结果，因为如果别的装备和这个部位Conflict，就要对比一下决定留谁了
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString Slot = "";

	//优先级，如果Conflict，就会判断谁更高级
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int Priority = 0;

	

	FWearingAppearanceInfo(){};
	FWearingAppearanceInfo(FString BluePrintPath, AActor* CreatedActor):
		BPPath(BluePrintPath), Actor(CreatedActor) {};

	/**
	 * 从一个外观获得一个外观信息
	 * @param CreatedActor 根据外观信息已经创建完的，在角色身上的东西
	 * @param AppearancePart 外观信息
	 * @param EquipmentPart 所属装备部位
	 */
	static FWearingAppearanceInfo FromEquipmentAppearance(AActor* CreatedActor, FEquipmentAppearancePart* AppearancePart, EEquipmentPart EquipmentPart);
	
	bool operator ==(const FWearingAppearanceInfo& Other) const
	{
		return Other.BPPath == this->BPPath && Other.Actor == this->Actor && Other.HideBodyParts == this->HideBodyParts && Other.Slot == this->Slot;
	}

	//与==不同，这个只判断BPPath
	bool SameBP(const FWearingAppearanceInfo& Other) const
	{
		return Other.BPPath == this->BPPath;
	}
};


