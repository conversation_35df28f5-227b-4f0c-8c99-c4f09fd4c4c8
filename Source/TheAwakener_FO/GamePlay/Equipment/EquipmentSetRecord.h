// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/Object.h"
#include "EquipmentSetRecord.generated.h"

/**
 * 装备套组记录，可以快速替换装备用的东西
 */
USTRUCT(BlueprintType)
struct FEquipmentSetRecord
{
	GENERATED_BODY()
public:
	//曾经装备的主武器的UniqueId
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString MainWeaponUniqueId;

	//曾经装备的副武器的UniqueId，Empty代表没有装备
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString OffWeaponUniqueId;
};
