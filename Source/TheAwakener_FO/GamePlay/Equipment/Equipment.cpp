// Fill out your copyright notice in the Description page of Project Settings.


#include "Equipment.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GameFramework/Manager/AwDataManager.h"

TArray<FEquipmentAppearancePart> FEquipment::PartsToShow(FString BodyType, int DurabilityLevel)
{
	TArray<FEquipmentAppearancePart> Res;
	for (int i = 0; i < this->AppearanceParts.Num(); i++)
	{
		//不是这个CharacterObj.Type能穿的
		if (AppearanceParts[i].ShowOnCharacterType.Num() > 0 && !AppearanceParts[i].ShowOnCharacterType.Contains(BodyType)) continue;
		//不在这个耐久度范围显示
		if (AppearanceParts[i].ShowAboveDurability <= DurabilityLevel && AppearanceParts[i].ShowBelowDurability >= DurabilityLevel)
		{
			Res.Add(AppearanceParts[i]);
		}
	}
	return Res;
}

FEquipmentDurabilityModifyResult FEquipment::ReduceDurability(FString BodyType, int Value)
{
	//TODO 部位变化写在这里迟早是个祸害，要找个机会重构掉，因为AwCharacter已经有类似内容了
	const TArray<FEquipmentAppearancePart> WasShow = PartsToShow(BodyType, this->Durability);
	this->Durability -= Value;
	this->Durability = FMath::Clamp(this->Durability, 0, this->DurabilityMax);
	const TArray<FEquipmentAppearancePart> NewToShow = PartsToShow(BodyType, this->Durability);
	TArray<FEquipmentAppearancePart> RemoveRes;
	for (FEquipmentAppearancePart Show : WasShow)
	{
		if (NewToShow.Contains(Show) == false)
		{
			RemoveRes.Add(Show);
		}
	} 
	
	TArray<FEquipmentAppearancePart> AddRes;
	for (FEquipmentAppearancePart ToShow : NewToShow)
	{
		if (WasShow.Contains(ToShow) == false)
		{
			AddRes.Add(ToShow);
		}
	}

	return FEquipmentDurabilityModifyResult(
		RemoveRes,
		AddRes
	);
}

FEquipment FEquipment::FromThing(const FThingObj& ThingObj)
{
	FEquipment Res = UGameplayFuncLib::GetDataManager()->GetEquipmentById(ThingObj.Id);
	return Res;
}

FEquipmentAppearancePart FEquipmentAppearancePart::FromJson(TSharedPtr<FJsonObject> JsonObj)
{
	FEquipmentAppearancePart AppPart = FEquipmentAppearancePart();
	AppPart.BluePrintPath = UDataFuncLib::AwGetStringField(JsonObj, "BluePrintPath");
	AppPart.BindPointIds = UDataFuncLib::AwGetStringArrayField(JsonObj, "BindPointId");
	AppPart.Type = UDataFuncLib::AwGetEnumField<EEquipmentAppearanceType>(
		JsonObj, "AppearanceType", EEquipmentAppearanceType::Normal);
	AppPart.PhysicalBoneName = UDataFuncLib::AwGetStringArrayField(JsonObj, "PhysicalBoneName");
	AppPart.PartSlot = UDataFuncLib::AwGetStringField(JsonObj, "PartSlot");
	if (JsonObj->HasField("HidePart"))
	{
		for (const TSharedPtr<FJsonValue, ESPMode::ThreadSafe> Value :
			UDataFuncLib::AwGetArrayField(JsonObj, "HidePart"))
		{
			AppPart.HideBodyParts.Add(Value->AsString());
		}
	}
	if (JsonObj->HasField("Show"))
	{
		const TSharedPtr<FJsonObject> ShowJsonObj = JsonObj->GetObjectField("Show");
		AppPart.ShowAboveDurability = UDataFuncLib::AwGetNumberField(ShowJsonObj, "Min", 1);
		AppPart.ShowBelowDurability = UDataFuncLib::AwGetNumberField(ShowJsonObj, "Max", 1);
	}
	AppPart.Priority = JsonObj->HasField("Priority") ? JsonObj->GetIntegerField("Priority") : 0;
	AppPart.ConflictPartSlots = UDataFuncLib::AwGetStringArrayField(JsonObj, "ConflictSlots");
	AppPart.ConflictPartSlots.Add(AppPart.PartSlot);
			
	if (JsonObj->HasField("ShowOnType"))
	{
		for (const TSharedPtr<FJsonValue, ESPMode::ThreadSafe> ShowOn : JsonObj->GetArrayField("ShowOnType"))
		{
			AppPart.ShowOnCharacterType.Add(ShowOn->AsString());
			UKismetSystemLibrary::PrintString(GWorld, FString("ShowOnType:").Append(ShowOn->AsString()).Append("/").Append(AppPart.ShowOnCharacterType.Last()),
				true, true, FLinearColor::Green, 10);
		} 
	}

	return AppPart;
}

FEquipment FEquipment::FromJson(TSharedPtr<FJsonObject> JsonObj)
{
	FEquipment Equipment = FEquipment();
	Equipment.Id = UDataFuncLib::AwGetStringField(JsonObj, "Id");
	
	if (JsonObj->HasField("Meat"))
	{
		const TSharedPtr<FJsonObject> MeatInfo = JsonObj->GetObjectField("Meat");
		for (TTuple<FString, TSharedPtr<FJsonValue>> MeatKV : MeatInfo->Values)
		{
			Equipment.Meat.Add(MeatKV.Key, MeatKV.Value->AsNumber() );
		}
	}

	if (JsonObj->HasField("Property"))
	{
		//Equipment.Property = FChaProp::FromJson(JsonObj->GetObjectField("Property"), 1, EChaPotentialType::Armor);
		Equipment.Property = FChaProp::PotentialFromJson(JsonObj->GetObjectField("Property"));
	}

	if (JsonObj->HasField("MeatType"))
		Equipment.MeatType = UDataFuncLib::AwGetEnumField<EChaPartMeatType>(JsonObj, "MeatType", EChaPartMeatType::Meat);
	if (JsonObj->HasField("CharacterPart"))
		Equipment.AffectPart = UDataFuncLib::AwGetEnumField<EChaPartType>(JsonObj, "CharacterPart", EChaPartType::Body);
	if (JsonObj->HasField("EquipmentPart"))
		Equipment.PartType = UDataFuncLib::AwGetEnumField<EEquipmentPart>(JsonObj, "EquipmentPart", EEquipmentPart::Invalid);

	//Equipment.Priority = UDataFuncLib::AwGetNumberField(JsonObj, "Priority", 0);

	Equipment.DurabilityMax = UDataFuncLib::AwGetNumberField(JsonObj, "Durability", 1);
	Equipment.Durability = Equipment.DurabilityMax;

	if (JsonObj->HasField("Appearance"))
	{
		for (const TSharedPtr<FJsonValue, ESPMode::ThreadSafe> AppearInfo : UDataFuncLib::AwGetArrayField(JsonObj, "Appearance"))
		{
			FEquipmentAppearancePart Part = FEquipmentAppearancePart::FromJson(AppearInfo->AsObject());
			Part.PartType = Equipment.PartType;
			Equipment.AppearanceParts.Add(Part);
		} 
	}

	if (JsonObj->HasField("FXKey"))
	{
		for (TSharedPtr<FJsonValue, ESPMode::ThreadSafe> FXK : UDataFuncLib::AwGetArrayField(JsonObj, "FXKey"))
		{
			Equipment.FXKey.Add(FXK->AsString());
		}
	}

	Equipment.ClipLevel.Init(0, 4);
	if (JsonObj->HasField("Clip"))
	{
		for (int i = 0; i < 4; i++)
		{
			FString IKey = FString::FromInt(i);
			Equipment.ClipLevel[i] =  UDataFuncLib::AwGetNumberField(JsonObj->GetObjectField("Clip"), IKey, 0);
		}
	}

	return Equipment;
}

//根据ClipLevel重新计算得出装备的肉质
void FEquipment::ResetDataByClips()
{
	float MeatValue = 0;
	for (const int CLv : this->ClipLevel)
	{
		//TODO 得有个算法，计算出材质和肉质的关系，目前都当等级/100……
		MeatValue += CLv / 100.000f;
	}
	this->Meat.SetAllTo(MeatValue);
}

bool FEquipment::HasAppearancePartInSlot(FString SlotName)
{
	for (FEquipmentAppearancePart AppPart : this->AppearanceParts)
	{
		if (AppPart.PartSlot == SlotName) return true;
	}
	return false;
}

FString FEquipment::GetPartTypeText(FEquipment Equipment)
{
	switch (Equipment.PartType)
	{
	case EEquipmentPart::Arm:return UGameplayFuncLib::GetAwDataManager()->GetTextByKey("ArmorPart_Hand");
	case EEquipmentPart::Body:return UGameplayFuncLib::GetAwDataManager()->GetTextByKey("ArmorPart_Body");
	case EEquipmentPart::Head:return UGameplayFuncLib::GetAwDataManager()->GetTextByKey("ArmorPart_Helmet");
	case EEquipmentPart::Leg:return UGameplayFuncLib::GetAwDataManager()->GetTextByKey("ArmorPart_Pant");
		default:return "";
	}
}