// Fill out your copyright notice in the Description page of Project Settings.


#include "ArmorSynthesis.h"

TTuple<bool, FEquipment> UArmorSynthesis::SyntheticArmor(FEquipment Base, FEquipment AddOn)
{
	if (Base.PartType != AddOn.PartType) return TTuple<bool, FEquipment>(false, FEquipment());
	FEquipment Res = Base;
	int ClipCount = 4;	//Clip有4个，这是写死的
	int SyntheticClips = 0;
	for (int i = 0; i < ClipCount; i++)
	{
		if (Res.ClipLevel.Num() <= i || Res.ClipLevel[i] <= 0)
		{
			//等级为0或者不存在，代表可以融入后来者
			if (AddOn.ClipLevel.Num() > i && AddOn.ClipLevel[i] > 0)
			{
				Res.ClipLevel[i] = AddOn.ClipLevel[i];
				SyntheticClips += 1;
			}
		}
	}
	if (SyntheticClips <= 0) return TTuple<bool, FEquipment>(false, FEquipment());
	//重新计算一件防具的属性
	Res.ResetDataByClips();
	//视觉部位合并
	for (FEquipmentAppearancePart AppPart : AddOn.AppearanceParts)
	{
		if (Res.HasAppearancePartInSlot(AppPart.PartSlot) == false)
		{
			Res.AppearanceParts.Add(AppPart);
		}
	} 

	return TTuple<bool, FEquipment>(true, Res);
}