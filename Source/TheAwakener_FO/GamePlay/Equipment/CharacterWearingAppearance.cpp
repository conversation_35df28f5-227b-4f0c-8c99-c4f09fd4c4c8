// Fill out your copyright notice in the Description page of Project Settings.


#include "CharacterWearingAppearance.h"

FWearingAppearanceInfo FWearingAppearanceInfo::FromEquipmentAppearance(AActor* CreatedActor, FEquipmentAppearancePart* AppearancePart, EEquipmentPart EquipmentPart)
{
	FWearingAppearanceInfo Res = FWearingAppearanceInfo(AppearancePart->BluePrintPath, CreatedActor);

	if (AppearancePart)
	{
		Res.Priority = AppearancePart->Priority;
		Res.Slot = AppearancePart->PartSlot;
		Res.HideBodyParts = AppearancePart->HideBodyParts;
	}
	
	Res.EquipmentPart = EquipmentPart;
	
	return Res;
}

