// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Equipment.h"
#include "TheAwakener_FO/GamePlay/Characters/Elemental/Elemental.h"
#include "Weapon.generated.h"

/**
 * 在武器内的位置，是作为主武器还是副武器
 */
UENUM(BlueprintType)
enum class ESlotInWeaponObj:uint8
{
	MainWeapon,
	OffWeapon
};

/**
 * 武器本身的类别
 */
UENUM(BlueprintType)
enum class EWeaponType : uint8
{
	UnArmed,		//徒手武器，包括没有武器、拳套等
	BigSword,		//大剑，比如Claymore GreatSword
	OneHandSword,	//片手剑，比如Saber, Sword
	PoleArm,		//长柄武器，如Spear Lance
	Shield,		//盾牌，盾牌当然是武器（激战2不也是吗）
};

/**
 * 武器的模板数据
 */
USTRUCT(BlueprintType)
struct FWeaponModel
{
	GENERATED_BODY()
public:
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString Id;

	//攻击力
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FDamageValue AttackPower;

	//武器本身的属性
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	EChaElemental Elemental = EChaElemental::Physical;

	//提供的属性
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FChaProp Property;

	//武器类别
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	EWeaponType WeaponType = EWeaponType::UnArmed;

	//当前的耐久度档次，这个档次会影响装备部位的显示性
	// UPROPERTY(EditAnywhere, BlueprintReadWrite)
	// int Durability = 1;

	//最大耐久度档次
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int DurabilityMax = 1;

	//要播放的视觉特效相关的Key
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FString> FXKey;

	//装备的部件，这些部件是显示用的信息
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FEquipmentAppearancePart> AppearanceParts;

	//装备的部件，这是当用于副手武器时候的信息
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FEquipmentAppearancePart> AsOffWeaponAppearanceParts;

	//显示优先级，用来和显示部位冲突的，但是呢，他却不和防具的冲突
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int Priority;

	//TODO 其他属性再议

	//初始化为肉搏武器
	FWeaponModel():
		Id(FString()),
		AttackPower(1),
		Property(FChaProp()), FXKey(TArray<FString>()),
		AppearanceParts(TArray<FEquipmentAppearancePart>()), Priority(0){};

	static FWeaponModel FromThing(const FThingObj& ThingObj);

	static FWeaponModel FromJson(TSharedPtr<FJsonObject> JsonObj);

	bool IsNull() const{return this->Id.IsEmpty();}

	bool operator ==(const FWeaponModel& Other)const
	{
		if (this->IsNull() && Other.IsNull()) return true;	//都是空就是一样的
		if (this->IsNull() || Other.IsNull()) return false;		//只有一个空就不一样
		return this->Id == Other.Id && this->WeaponType == Other.WeaponType ;	//目前我们就认为这俩一样就一样了
	}
};

/**
 * 武器的实体
 */
USTRUCT(BlueprintType)
struct FWeaponObj
{
	GENERATED_BODY()
public:
	//唯一id
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString UniqueId;
	
	//模板
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FWeaponModel Model;

	//是否临时性的，即在很多流程中，用完就直接销毁而不会放入背包的。职业默认武器都是临时性的
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	bool IsTemporary = false;

	//耐久度
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int Durability = 1;

	// 词条
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FString> EquipSets;
	
	FWeaponObj(){};
	FWeaponObj(FWeaponModel FromModel, bool IsTempWeapon = false):
		UniqueId(FDateTime::Now().ToString().Append("_").Append(FString::FromInt(FMath::RandRange(0, 999999)))),
		Model(FromModel), IsTemporary(IsTempWeapon), Durability(FromModel.DurabilityMax){};

	bool operator ==(const FWeaponObj& Other) const
	{
		return this->Model == Other.Model &&
			this->UniqueId == Other.UniqueId;
	}

	/**
	 * 现在有哪些部位是要显示的
	 */
	//TArray<FEquipmentAppearancePart> CurrentToShow();

	/**
	 * 在某个耐久度等级，有哪些部位是要显示的
	 * @param DurabilityLevel 要检查的耐久度等级
	 * @param InMainHand 当做主手武器来显示
	 * @return 要显示的装备的指针
	 */
	TArray<FEquipmentAppearancePart> PartsToShow(int DurabilityLevel, bool InMainHand);

	/**
	 * 降低装备的耐久度等级
	 * @param Value 要降低的量，如果是负数则会增加
	 * @param InMainHand 当做主手武器来显示
	 * @return 等级产生的变化是否值的重新刷一下显示
	 */
	FEquipmentDurabilityModifyResult ReduceDurability(int Value, bool InMainHand);

	bool IsNull() const
	{
		return this->Model.IsNull();
	}
};


/**
 * 实际使用中的武器套组
 * 角色身上的武器装备是走这个，记录角色装备信息的也走这个
 */
USTRUCT(BlueprintType)
struct FEquippedWeaponSet
{
	GENERATED_BODY()
public:
	//模板
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FWeaponObj MainHand;

	//副武器（特指近战）
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FWeaponObj OffHand;

	//当合并之后，这个武器是个什么类型了
	UPROPERTY(BlueprintReadOnly)
	EClassWeaponType WeaponType;

	//要显示的部位，这个可以理解为偷懒用的，但是他的确降低了运算频率了
	UPROPERTY(BlueprintReadOnly)
	TArray<FEquipmentAppearancePart> ShowPartNow;

	FEquippedWeaponSet():
		WeaponType(EClassWeaponType::UnArmed), ShowPartNow(TArray<FEquipmentAppearancePart>()){};

	FEquippedWeaponSet(FString BodyType, FWeaponObj Main, FWeaponObj Off = FWeaponObj());

	/**
	 * 重新计算要显示的部位
	 * @param BodyType 角色的体型，CharacterObj.TypeId
	 */
	void RecheckShowParts(FString BodyType);

	//是否是空武器
	bool IsNull() const
	{
		return this->MainHand.Model.IsNull() && this->OffHand.Model.IsNull();
	}

	//某个slot是否为空
	bool IsSlotEmpty(ESlotInWeaponObj Slot) const
	{
		switch (Slot)
		{
			case ESlotInWeaponObj::MainWeapon: return this->MainHand.Model.IsNull();
			case ESlotInWeaponObj::OffWeapon: return this->OffHand.Model.IsNull();
		}
		return true;
	}

	FWeaponObj WeaponInSlot(ESlotInWeaponObj Slot) const
	{
		switch (Slot)
		{
		case ESlotInWeaponObj::MainWeapon: return this->MainHand;
		case ESlotInWeaponObj::OffWeapon: return this->OffHand;
		}
		return this->MainHand;
	}

	//获得武器类型称号
	static FString GetWeaponTypeText(FEquippedWeaponSet Weapon);

	//获得唯一的Model，第二个参数确保是否真的是唯一的，如果本就不是唯一的，比如是双刀，第2个返回值就是false了
	TTuple<FWeaponModel, bool> GetUniqueWeaponModel() const;

	bool operator ==(const FEquippedWeaponSet& Other) const
	{
		if (this->IsNull() || Other.IsNull()) return false;
		return this->MainHand == Other.MainHand &&
			this->OffHand == Other.OffHand &&
			this->WeaponType == Other.WeaponType;
	}
};
