// Fill out your copyright notice in the Description page of Project Settings.


#include "Weapon.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GameFramework/Manager/AwDataManager.h"



TArray<FEquipmentAppearancePart> FWeaponObj::PartsToShow(int DurabilityLevel, bool InMainHand)
{
	TArray<FEquipmentAppearancePart> Res;
	if (InMainHand)
	{
		for (int i = 0; i < this->Model.AppearanceParts.Num(); i++)
		{
			if (Model.AppearanceParts[i].ShowAboveDurability <= DurabilityLevel && Model.AppearanceParts[i].ShowBelowDurability >= DurabilityLevel)
			{
				Res.Add(Model.AppearanceParts[i]);
			}
		}
	}else
	{
		for (int i = 0; i < this->Model.AsOffWeaponAppearanceParts.Num(); i++)
		{
			if (Model.AsOffWeaponAppearanceParts[i].ShowAboveDurability <= DurabilityLevel && Model.AsOffWeaponAppearanceParts[i].ShowBelowDurability >= DurabilityLevel)
			{
				Res.Add(Model.AsOffWeaponAppearanceParts[i]);
			}
		}
	}
	
	return Res;
}

FEquipmentDurabilityModifyResult FWeaponObj::ReduceDurability(int Value, bool InMainHand)
{
	const TArray<FEquipmentAppearancePart> WasShow = PartsToShow(this->Durability, InMainHand);
	this->Durability -= Value;
	this->Durability = FMath::Clamp<int>(this->Durability, 1, this->Model.DurabilityMax);
	const TArray<FEquipmentAppearancePart> NewToShow = PartsToShow(this->Durability, InMainHand);
	TArray<FEquipmentAppearancePart> RemoveRes;
	for (FEquipmentAppearancePart Show : WasShow)
	{
		if (NewToShow.Contains(Show) == false)
		{
			RemoveRes.Add(Show);
		}
	} 
	
	TArray<FEquipmentAppearancePart> AddRes;
	for (FEquipmentAppearancePart ToShow : NewToShow)
	{
		if (WasShow.Contains(ToShow) == false)
		{
			AddRes.Add(ToShow);
		}
	}

	return FEquipmentDurabilityModifyResult(
		RemoveRes,
		AddRes
	);
}

FWeaponModel FWeaponModel::FromThing(const FThingObj& ThingObj)
{
	FWeaponModel Weapon = UGameplayFuncLib::GetDataManager()->GetWeaponModelById(ThingObj.Id);
	return Weapon;
}

FWeaponModel FWeaponModel::FromJson(TSharedPtr<FJsonObject> JsonObj)
{
	FWeaponModel Weapon = FWeaponModel();
	Weapon.Id = UDataFuncLib::AwGetStringField(JsonObj, "Id");
	
	if (JsonObj->HasField("Attack"))
	{
		const TSharedPtr<FJsonObject> MeatInfo = JsonObj->GetObjectField("Attack");
		for (TTuple<FString, TSharedPtr<FJsonValue>> MeatKV : MeatInfo->Values)
		{
			Weapon.AttackPower.Add(MeatKV.Key, MeatKV.Value->AsNumber() );
		}
	}

	if (JsonObj->HasField("Elemental"))
		Weapon.Elemental = UDataFuncLib::FStringToEnum<EChaElemental>(JsonObj->GetStringField("Elemental"));

	if (JsonObj->HasField("Property"))
	{
		Weapon.Property = FChaProp::FromJson(JsonObj->GetObjectField("Property"),1, EChaPotentialType::Weapon);
	}

	Weapon.Priority = UDataFuncLib::AwGetNumberField(JsonObj, "Priority", 0);

	Weapon.DurabilityMax = UDataFuncLib::AwGetNumberField(JsonObj, "Durability", 0);
	//Weapon.Durability = Weapon.DurabilityMax;

	if (JsonObj->HasField("Appearance"))
	{
		for (const TSharedPtr<FJsonValue, ESPMode::ThreadSafe> AppearInfo : UDataFuncLib::AwGetArrayField(JsonObj, "Appearance"))
		{
			Weapon.AppearanceParts.Add(FEquipmentAppearancePart::FromJson(AppearInfo->AsObject()));
		} 
	}

	if (JsonObj->HasField("OffAppearance"))
	{
		for (const TSharedPtr<FJsonValue, ESPMode::ThreadSafe> AppearInfo : UDataFuncLib::AwGetArrayField(JsonObj, "OffAppearance"))
		{
			FEquipmentAppearancePart AppPart = FEquipmentAppearancePart();
			AppPart.BluePrintPath = UDataFuncLib::AwGetStringField(AppearInfo->AsObject(), "BluePrintPath");
			AppPart.BindPointIds = UDataFuncLib::AwGetStringArrayField(AppearInfo->AsObject(), "BindPointId");
			AppPart.Type = UDataFuncLib::AwGetEnumField<EEquipmentAppearanceType>(
				AppearInfo->AsObject(), "AppearanceType", EEquipmentAppearanceType::Normal);
			AppPart.PhysicalBoneName = UDataFuncLib::AwGetStringArrayField(AppearInfo->AsObject(), "PhysicalBoneName");
			AppPart.PartSlot = UDataFuncLib::AwGetStringField(AppearInfo->AsObject(), "PartSlot");
			if (AppearInfo->AsObject()->HasField("HidePart"))
			{
				for (const TSharedPtr<FJsonValue, ESPMode::ThreadSafe> Value : UDataFuncLib::AwGetArrayField(AppearInfo->AsObject(), "HidePart"))
				{
					AppPart.HideBodyParts.Add(Value->AsString());
				}
			}
			if (AppearInfo->AsObject()->HasField("Show"))
			{
				AppPart.ShowAboveDurability = UDataFuncLib::AwGetNumberField(AppearInfo->AsObject()->GetObjectField("Show"), "Min", 0);
				AppPart.ShowBelowDurability = UDataFuncLib::AwGetNumberField(AppearInfo->AsObject()->GetObjectField("Show"), "Max", 99999);
			}
			AppPart.Priority = AppearInfo->AsObject()->HasField("Priority") ? AppearInfo->AsObject()->GetIntegerField("Priority") : 0;
			AppPart.ConflictPartSlots = UDataFuncLib::AwGetStringArrayField(AppearInfo->AsObject(), "ConflictSlots");
			AppPart.ConflictPartSlots.Add(AppPart.PartSlot);
			Weapon.AsOffWeaponAppearanceParts.Add(AppPart);
		} 
	}else
	{
		Weapon.AsOffWeaponAppearanceParts = Weapon.AppearanceParts;
	}

	if (JsonObj->HasField("FXKey"))
	{
		for (TSharedPtr<FJsonValue, ESPMode::ThreadSafe> FXK : UDataFuncLib::AwGetArrayField(JsonObj, "FXKey"))
		{
			Weapon.FXKey.Add(FXK->AsString());
		}
	}

	if (JsonObj->HasField("WeaponType"))
		Weapon.WeaponType = UDataFuncLib::AwGetEnumField<EWeaponType>(JsonObj, "WeaponType", EWeaponType::UnArmed);

	return Weapon;
}

FEquippedWeaponSet::FEquippedWeaponSet(FString BodyType, FWeaponObj Main, FWeaponObj Off)
{
	this->MainHand = Main;
	this->OffHand = Off;
	this->WeaponType = EClassWeaponType::UnArmed;	//TODO，武器类型要获得
	this->RecheckShowParts(BodyType);
}

//重新计算要显示的部位
void FEquippedWeaponSet::RecheckShowParts(FString BodyType)
{
	this->ShowPartNow = TArray<FEquipmentAppearancePart>();

	TMap<FString, FEquipmentAppearancePart> MainHandShow;
	if (MainHand.Model.IsNull() == false)
	{
		TArray<FEquipmentAppearancePart> ToShow = MainHand.PartsToShow(MainHand.Durability, true);
		for (FEquipmentAppearancePart AppPart : ToShow)
		{
			//不符合显示标准的continue掉
			if (AppPart.ShowOnCharacterType.Num() > 0 && AppPart.ShowOnCharacterType.Contains(BodyType) == false) continue;
			if (AppPart.ShowAboveDurability > MainHand.Durability || AppPart.ShowBelowDurability < MainHand.Durability) continue;
			MainHandShow.Add(AppPart.PartSlot, AppPart);
		} 
	}

	TMap<FString, FEquipmentAppearancePart> OffHandShow;
	if (OffHand.Model.IsNull() == false)
	{
		TArray<FEquipmentAppearancePart> ToShow = OffHand.PartsToShow(OffHand.Durability, false);
		for (FEquipmentAppearancePart AppPart :ToShow)
		{
			if (AppPart.ShowOnCharacterType.Num() > 0 && AppPart.ShowOnCharacterType.Contains(BodyType) == false) continue;
			if (AppPart.ShowAboveDurability > OffHand.Durability || AppPart.ShowBelowDurability < OffHand.Durability) continue;
			OffHandShow.Add(AppPart.PartSlot, AppPart);
		} 
	}

	//TODO 还没想清楚，暂不处理武器显示冲突问题
	
	for (TTuple<FString, FEquipmentAppearancePart> ToShow : MainHandShow)
	{
		this->ShowPartNow.Add(ToShow.Get<1>());
	}
	for (TTuple<FString, FEquipmentAppearancePart> ToShow : OffHandShow)
	{
		this->ShowPartNow.Add(ToShow.Get<1>());
	}
}

FString FEquippedWeaponSet::GetWeaponTypeText(FEquippedWeaponSet Weapon)
{
	switch (Weapon.WeaponType)
	{
	case EClassWeaponType::Archery: return UGameplayFuncLib::GetAwDataManager()->GetTextByKey("WeaponType_Archery");
	case EClassWeaponType::BigSword: return UGameplayFuncLib::GetAwDataManager()->GetTextByKey("WeaponType_BigSword");
	case EClassWeaponType::PoleArm: return UGameplayFuncLib::GetAwDataManager()->GetTextByKey("WeaponType_PoleArm");
	case EClassWeaponType::TwinSword: return UGameplayFuncLib::GetAwDataManager()->GetTextByKey("WeaponType_TwinSword");
	case EClassWeaponType::UnArmed: return UGameplayFuncLib::GetAwDataManager()->GetTextByKey("WeaponType_UnArmed");
		default:return "";
	}
}

TTuple<FWeaponModel, bool> FEquippedWeaponSet::GetUniqueWeaponModel() const
{
	int FoundCount = 0;
	FWeaponModel Res;
	if (this->MainHand.Model.IsNull() == false)
	{
		if (FoundCount <= 0) Res = this->MainHand.Model;
		FoundCount += 1;
	}
	if (this->OffHand.Model.IsNull() == false)
	{
		if (FoundCount <= 0) Res = this->OffHand.Model;
		FoundCount += 1;
	}
	return TTuple<FWeaponModel, bool>(Res, FoundCount == 1);
}