// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Equipment.h"
#include "UObject/Object.h"
#include "ArmorSynthesis.generated.h"

/**
 * 护甲合成，和武器合成玩法不太一样
 */
UCLASS()
class THEAWAKENER_FO_API UArmorSynthesis : public UObject
{
	GENERATED_BODY()
public:
	/**
	 * 合成两件护甲
	 * @param Base 基础装备，作为底料
	 * @param AddOn 添加的装备
	 * @returns bool代表是否能合成，如果不能则第二返回值无意义，否则返回合成后装备
	 */
	static TTuple<bool, FEquipment> SyntheticArmor(FEquipment Base, FEquipment AddOn);
};
