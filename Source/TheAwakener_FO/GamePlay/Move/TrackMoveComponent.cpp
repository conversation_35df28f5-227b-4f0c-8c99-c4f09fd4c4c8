// Fill out your copyright notice in the Description page of Project Settings.


#include "TrackMoveComponent.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "Kismet/KismetMathLibrary.h"
#include "Kismet/KismetSystemLibrary.h"

UTrackMoveComponent::UTrackMoveComponent()
{
	PrimaryComponentTick.bCanEverTick = true;
	baseMoveAcceleration = AutoMoveAcceleration;
	LastRotation = FRotator::ZeroRotator;
	SpinPercent = 0;
	HasTargetRotator = false;
}

void UTrackMoveComponent::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
	Super::TickComponent(DeltaTime, TickType, ThisTickFunction);

	if (Paused == true) return;

	if (TrackType == ETrackType::Passive)
	{
		//被动位移计算
		PassiveMoveTick(DeltaTime);
	}
	else
	{
		//主动位移计算
		InitativeMoveTick(DeltaTime);
	}
		//旋转相关计算 先计算位移 因为swing状态下 抵达终点这帧开始会自动转头
		RotationTick(DeltaTime);


	if (OnTrackTickEvent.IsBound())
	{
		OnTrackTickEvent.Broadcast(DeltaTime, this, GetOwner());
	}

}

void UTrackMoveComponent::GetTrackFromActor(AActor* Actor)
{
	if (Actor)
	{
		UActorComponent* AC = Actor->GetComponentByClass(USplineComponent::StaticClass());
		this->Track = Cast<USplineComponent>(AC);

		float SplineLen = Track->GetSplineLength();
		if (SplineLen <= 0) return;
		this->GetOwner()->SetActorLocation(Track->GetWorldLocationAtDistanceAlongSpline(SplineLen * StartPercentageOnTrack));
		this->GetOwner()->SetActorRotation(Track->GetWorldRotationAtDistanceAlongSpline(SplineLen * StartPercentageOnTrack));
		CurTrackPercent = StartPercentageOnTrack;
		if (OnTrackBeginEvent.IsBound())
		{
			OnTrackBeginEvent.Broadcast(this, GetOwner());
		}
	}
}

void UTrackMoveComponent::Push(FVector Vec)
{
	if (bOutTrack) return;
	const FVector MoveVec = Vec.ProjectOnTo(this->GetOwner()->GetActorForwardVector());
	const bool IsNegative = FVector::DotProduct(this->GetOwner()->GetActorForwardVector(), Vec) < 0;
	this->MoveForce += (IsNegative ? -1:1) * MoveVec.Size();
}

void UTrackMoveComponent::Stop()
{
	MoveForce = 0;
	AutoMoveSpeed = 0;
	AutoMoveAcceleration = 0;
	SpinPercent = 0;
	HasTargetRotator = false;
	LastRotation = GetOwner()->GetActorRotation();
	if (OnTrackStopEvent.IsBound())
	{
		OnTrackStopEvent.Broadcast(this, GetOwner());
	}
}

bool UTrackMoveComponent::IsStop()
{
	bool result = false;

	if (TrackType==ETrackType::Initiative && FMath::IsNearlyZero(AutoMoveSpeed)&& FMath::IsNearlyZero(AutoMoveAcceleration))
	{
		result = true;
	}
	else if(TrackType == ETrackType::Passive&& (FMath::IsNearlyZero(MoveForce) || bOutTrack == true || !GWorld->GetAuthGameMode()))
	{
		result = true;
	}
	if (HasTargetRotator)
	{
		result = false;
	}

	return result;
}

void UTrackMoveComponent::StopMove()
{
	MoveForce = 0;
	AutoMoveSpeed = 0;
	AutoMoveAcceleration = 0;
}

bool UTrackMoveComponent::IsMoveStop()
{
	bool result = false;

	if (TrackType == ETrackType::Initiative && FMath::IsNearlyZero(AutoMoveSpeed) && FMath::IsNearlyZero(AutoMoveAcceleration))
	{
		result = true;
	}
	else if (TrackType == ETrackType::Passive && (FMath::IsNearlyZero(MoveForce) || bOutTrack == true || !GWorld->GetAuthGameMode()))
	{
		result = true;
	}
	return result;
}

void UTrackMoveComponent::ReStartAutoMove()
{
	AutoMoveAcceleration = baseMoveAcceleration;
}

void UTrackMoveComponent::BlockWall(const UShapeComponent* NewBlockShapeComp)
{
	if (NewBlockShapeComp != this->BlockShapeComp)
	{
		// UKismetSystemLibrary::PrintString(this, "Block Wall!!!", true, true, FLinearColor::Red, 5);
		MoveForce = MoveForce/2*-1;
	}
}

float UTrackMoveComponent::GetOwnerDistanceAlongSpline()
{
	if (!Track)
	{
		return 0;
	}
	float key = Track->FindInputKeyClosestToWorldLocation(this->GetOwner()->GetActorLocation());
	float CurDistance = Track->GetDistanceAlongSplineAtSplineInputKey(key) ;

	return CurDistance;
}

FRotator UTrackMoveComponent::GetRotationAlongSpline()
{
	if (!Track)
	{
		return FRotator::ZeroRotator;
	}

	FRotator NewRotator;
	if (FaceForward)
	{
		NewRotator = Track->GetWorldRotationAtDistanceAlongSpline(CurTrackPercent * Track->GetSplineLength());
	}
	else
	{
		NewRotator = UKismetMathLibrary::MakeRotFromXY(
			-1 * Track->GetDirectionAtDistanceAlongSpline(CurTrackPercent * Track->GetSplineLength(), ESplineCoordinateSpace::World),
			-1 * Track->GetRightVectorAtDistanceAlongSpline(CurTrackPercent * Track->GetSplineLength(), ESplineCoordinateSpace::World));
	}

	return NewRotator;
}

FVector UTrackMoveComponent::GetOwnerCurTrackDirection()
{
	if (!Track)
	{
		return FVector::ZeroVector;
	}
	return Track->GetDirectionAtDistanceAlongSpline(GetOwnerDistanceAlongSpline(), ESplineCoordinateSpace::World);
}

FVector UTrackMoveComponent::FindOwnerClosestLocationOnSpline()
{
	FVector result;
	result = GetOwner()->GetActorLocation();

	if (Track)
	{
		result = Track->FindLocationClosestToWorldLocation(GetOwner()->GetActorLocation(), ESplineCoordinateSpace::World);
		if (Cast<AAwCharacter>(GetOwner()))
		{
			result.Z += Cast<AAwCharacter>(GetOwner())->GetCapsuleComponent()->GetScaledCapsuleHalfHeight();
		}
		return result;
	}

	return result;
}

bool UTrackMoveComponent::IsOwnerOnTrack(float MinDistance)
{	
	if (!Track)
	{
		return false;
	}

	FVector NearestLocation = Track->FindLocationClosestToWorldLocation(GetOwner()->GetActorLocation(), ESplineCoordinateSpace::World);
	FVector CharacterLocation = GetOwner()->GetActorLocation();
	if (Cast<AAwCharacter>(GetOwner()))
	{
		NearestLocation.Z += Cast<AAwCharacter>(GetOwner())->GetCapsuleComponent()->GetScaledCapsuleHalfHeight();
	}

	float distance = FVector::Dist(NearestLocation, GetOwner()->GetActorLocation());

	return FVector::Dist(NearestLocation, GetOwner()->GetActorLocation())<=MinDistance;
}

float UTrackMoveComponent::AngleBetweenTargetRotatorAndLastRotation()
{
	if (!HasTargetRotator)
	{
		return 0.f;
	}
	FVector Target = TargetRotator.Vector();
	FVector Last = LastRotation.Vector();
	FVector cross = FVector::CrossProduct(Last,Target);

	float angle = FMath::Acos(FVector::DotProduct(Last,Target));

	if (cross.Z<0)
	{
		angle = 360 - angle;
	}

	return angle;
}

void UTrackMoveComponent::SetTargetRotator(FRotator Target)
{
	if (TargetRotator.Equals(Target))
	{
		return;
	}
	HasTargetRotator = true;
	TargetRotator = Target;
	LastRotation = FRotator::ZeroRotator;
	SpinPercent = 0;
}

void UTrackMoveComponent::SetOwnerLocationAndRotationByPercent(float NewPercent, bool ForceStop, bool IsFaceForward)
{
	
	if (!Track)
	{
		return;
	}
	if (Track->GetSplineLength()<=0)
	{
		return;
	}

	if (ForceStop)
	{
		Stop();
	}
	NewPercent = FMath::Clamp(NewPercent,0.f,1.f);
	CurTrackPercent = NewPercent;
	FaceForward = IsFaceForward;

	GetOwner()->SetActorLocation(Track->GetWorldLocationAtDistanceAlongSpline(CurTrackPercent * Track->GetSplineLength()));
	FRotator NewRotator;
	if (FaceForward)
	{
		NewRotator = Track->GetWorldRotationAtDistanceAlongSpline(CurTrackPercent * Track->GetSplineLength());
	}
	else
	{
		NewRotator = UKismetMathLibrary::MakeRotFromXY(
			-1 * Track->GetDirectionAtDistanceAlongSpline(CurTrackPercent * Track->GetSplineLength(), ESplineCoordinateSpace::World),
			-1 * Track->GetRightVectorAtDistanceAlongSpline(CurTrackPercent * Track->GetSplineLength(), ESplineCoordinateSpace::World));
	}
	GetOwner()->SetActorRotation(NewRotator);
	
}

void UTrackMoveComponent::PassiveMoveTick(float DeltaTime)
{
	if (!Track || FMath::IsNearlyZero(MoveForce) || bOutTrack == true || !GWorld->GetAuthGameMode()) return;
	const float SplineLen = Track->GetSplineLength();
	if (SplineLen <= 0) return;

	const float MovePer = this->MoveForce * DeltaTime / SplineLen;
	CurTrackPercent = Track->GetDistanceAlongSplineAtSplineInputKey(Track->FindInputKeyClosestToWorldLocation(this->GetOwner()->GetActorLocation())) / SplineLen;
	CurTrackPercent = MovePer + CurTrackPercent;
	if (CurTrackPercent <= 0 || CurTrackPercent >= 1) bOutTrack = true;
	CurTrackPercent = FMath::Clamp(CurTrackPercent, 0.000f, 1.000f);

	const FVector TargetLocation = Track->GetWorldLocationAtDistanceAlongSpline(SplineLen * CurTrackPercent);
	if ((TargetLocation - GetOwner()->GetActorLocation()).Size() > 10000)
	{
		bOutTrack = true;	//距离轨道太远了也会导致被摧毁
		return;
	}
	this->GetOwner()->SetActorLocation(TargetLocation);

	//计算ForceReduction
	ForceReduction = FMath::Clamp<float>(MoveForce / 5, 100, 300);

	if (MoveForce > 0)
	{
		MoveForce -= ForceReduction * DeltaTime;
		if (MoveForce > MaxMoveForce) MoveForce = MaxMoveForce;
		if (MoveForce <= 0) MoveForce = 0;
	}
	else if (MoveForce < 0)
	{
		MoveForce += ForceReduction * DeltaTime;
		if (MoveForce < (-1 * MaxMoveForce)) MoveForce = (-1 * MaxMoveForce);
		if (MoveForce >= 0) MoveForce = 0;
	}

	if (MoveForce == 0)
		this->BlockShapeComp = nullptr;

}

void UTrackMoveComponent::InitativeMoveTick(float DeltaTime)
{
	if (!Track ||(FMath::IsNearlyZero(AutoMoveSpeed))&& FMath::IsNearlyZero(AutoMoveAcceleration))return;
	float CurPercentEnd = GoForward  ? 1.f:0.f;

	//到终点
	if (CurTrackPercent == CurPercentEnd)
	{
		//单向
		if (AutoMoveType == EAutoTrackMoveType::Once)
		{
			Stop();
		}
		//转向
		else
		{
			FaceForward = !FaceForward;
			float RotatorPower = FaceForward ? 1 : -1;
			FRotator NewTargetRotator;
			NewTargetRotator = UKismetMathLibrary::MakeRotFromXY(
				RotatorPower * Track->GetDirectionAtDistanceAlongSpline(CurTrackPercent * Track->GetSplineLength(), ESplineCoordinateSpace::World),
				RotatorPower * Track->GetRightVectorAtDistanceAlongSpline(CurTrackPercent * Track->GetSplineLength(), ESplineCoordinateSpace::World));
			SetTargetRotator(NewTargetRotator);

			//终点自动转向时停止移动 后可以增加变量暴露控制
			StopMove();
			return;
		}
	}

	float Power = GoForward ? 1 : -1;

	AutoMoveAcceleration = FMath::Abs(AutoMoveAcceleration) * Power;

	AutoMoveSpeed += AutoMoveAcceleration * DeltaTime;
	AutoMoveSpeed = FMath::Clamp(AutoMoveSpeed, AutoMoveSpeedMax*-1,AutoMoveSpeedMax);

	float NextPercent = (AutoMoveSpeed * DeltaTime) / Track->GetSplineLength() + CurTrackPercent;

	CurTrackPercent = FMath::Clamp(NextPercent, 0.0f, 1.0f);

	GetOwner()->SetActorLocation(Track->GetWorldLocationAtDistanceAlongSpline(CurTrackPercent*Track->GetSplineLength()));

}

void UTrackMoveComponent::RotationTick(float DeltaTime)
{
	if (IsStop())
	{
		return;
	}
	if (!HasTargetRotator)
	{
		if (!Track)
		{
			return;
		}
		//没有趋向目标,按轨道每帧设置旋转
		FRotator NewRotator;
		if (FaceForward)
		{
			NewRotator = Track->GetWorldRotationAtDistanceAlongSpline(CurTrackPercent * Track->GetSplineLength());
		}
		else
		{
			NewRotator = UKismetMathLibrary::MakeRotFromXY(
			-1 * Track->GetDirectionAtDistanceAlongSpline(CurTrackPercent * Track->GetSplineLength(), ESplineCoordinateSpace::World),
			-1 * Track->GetRightVectorAtDistanceAlongSpline(CurTrackPercent * Track->GetSplineLength(), ESplineCoordinateSpace::World));
		}
		GetOwner()->SetActorRotation(NewRotator);
	}
	else
	{
		if (LastRotation.IsZero())
		{
			LastRotation = GetOwner()->GetActorRotation();
		}
		FRotator CurRotator = GetOwner()->GetActorRotation();

		if (!CurRotator.Equals(TargetRotator))
		{
			SpinPercent += DeltaTime * AutoSpinSpeed / 180;
			SpinPercent = FMath::Clamp(SpinPercent ,0.f,1.f);

			FRotator NewRotator = FMath::Lerp(LastRotation, TargetRotator,SpinPercent);
			GetOwner()->SetActorRotation(NewRotator);
			return;
		}
		else
		{
			LastRotation = FRotator::ZeroRotator;
			SpinPercent = 0;
			HasTargetRotator = false;
		}
	}

}

void UTrackMoveComponent::BeginPlay()
{
	Super::BeginPlay();
	GetTrackFromActor(TrackActor);
}
