// Fill out your copyright notice in the Description page of Project Settings.


#include "AwMoveComponent.h"

#include "Components/SkeletalMeshComponent.h"
#include "Kismet/KismetMathLibrary.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "Net/UnrealNetwork.h"

// Sets default values for this component's properties
UAwMoveComponent::UAwMoveComponent()
{
	PrimaryComponentTick.bCanEverTick = true;

	ProfileName = FName("Terrain");
}

void UAwMoveComponent::BeginPlay()
{
	Super::BeginPlay();
	
	Character = Cast<AAwCharacter>(this->GetOwner());

	Radius = Character->GetCapsuleComponent()->GetScaledCapsuleRadius();
	HalfHeight = Character->GetCapsuleComponent()->GetScaledCapsuleHalfHeight();

	TargetDegree = GetOwner()->GetActorRotation().Yaw;
}


void UAwMoveComponent::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
	if (Paused == true ) return;
	
	if (!Character || Character->GotReady == false) return;
	
	Super::TickComponent(DeltaTime, TickType, ThisTickFunction);

	// init
	ThisTickMove.Set(0,0,0);
	RootMotionMove.Set(0,0,0);
	float PitchTarget = 0;
	Character->SetLastLocation();
	
	// 是否Attaching
	const bool InAttaching = Character->CharacterAttachment.AttachTarget != nullptr;

	FFindFloorResult FindFloorResult = Character->GetCharacterMovement()->CurrentFloor ;
	//FindFloor 消耗较高 不建议重复调用 并且CharacterMovement 自身内部每帧逻辑已存在调用
	/*
	Character->GetCharacterMovement()->FindFloor(Character->GetCapsuleComponent()->GetComponentLocation(),
						FindFloorResult, ThisTickMove.IsZero(), nullptr);
	bStandOnGround = FindFloorResult.IsWalkableFloor();
	*/
	bStandOnGround = FindFloorResult.IsWalkableFloor();
	if (InAttaching == false)
	{
		float YawChanged = 0;	//ActorRotation最终转了多少度
		
		const int Speed = GetMoveSpeed();
		
		//没有拽住别人，所以是自然的移动
		const FVector ForceMoveVelocity = ForceMove.ThisTickVelocity(DeltaTime,bStandOnGround);
		
		//正常走路

		//来自RootMotion的移动，只有当没有ForceMove的时候才有效
		if (ForceMoveVelocity.IsNearlyZero())
		{
			FAnimMontageInstance* Montage = Character->GetRootMotionMontageInstance();
			if (Montage)
			{
				FRootMotionMovementParams RootMotionParam;
				float InOutPos = Montage->GetPosition();
				Montage->SimulateAdvance(DeltaTime, InOutPos, RootMotionParam);
				//RootMotion 速率与动作动画速率同步
				float Power = Character->GetActionSpeedRate();
				RootMotionMove = CaluStandardRootMotionMove(RootMotionParam) * Power *
					Character->GetRootMotionRate();
				
				if(UseRootMotionRotation)
				{
					RootMotionRotation = RootMotionParam.GetRootMotionTransform().Rotator();
					this->Character->AddActorWorldRotation(RootMotionRotation);
				}
			}
		}
		
		const FVector2D MoveDirNormal = MoveDir.GetSafeNormal();
		
		if (Character->ControlState.CanRotate != EControlStateType::NoControl &&
			Character->ControlState.CanRotate != EControlStateType::OutOfControl)
		{
			if (Character->IsPlayerCharacter() && BeOffendedForceRotate.Active)
			{
				GetOwner()->SetActorRotation(FRotator(0, BeOffendedForceRotate.ToYaw, 0));
				BeOffendedForceRotate.Active = false;
			}
			else
			{
				RotateDir = Character->GetRotateDir();
				const float Fat = FMath::Max(0.000f, Character->GetCapsuleComponent()->GetScaledCapsuleRadius() - 35.000f);
				const float RotatePerSec = 900 / (5.000f * (Fat*1.000f / (120.000f + Fat * 1.000f)) + 1.000f) * Character->TurnAroundSpeed;
			
				FVector RotateDirNormal = FVector(RotateDir.GetSafeNormal().X, RotateDir.GetSafeNormal().Y, 0);
				//TODO：现在面向绝对和转向吻合了
				if (ForceRotate.Active)
				{
					RotateDir.X = ForceRotate.RotateForward.X;
					RotateDir.Y = ForceRotate.RotateForward.Y;
				}
				ForceRotate.Active = false;
				RotateDirNormal.X = RotateDir.X;
				RotateDirNormal.Y = RotateDir.Y;
				RotateDirNormal.Z = 0;
	
				const FActionForceRotateInfo* ForceRotateInfo = Character->CurActionForceRotate();
				const bool CanForceRotate = ForceRotateInfo != nullptr && (RotateDirNormal.IsNearlyZero() == false);

				if (RotateDirNormal.IsNearlyZero() == false)
				{
					TargetDegree = RotateDirNormal.Rotation().Yaw;
				}else if (Character->RotateInputAcceptance <= 0 && CanForceRotate == false)	//如果ForceRotate要改变TargetDegree就这里干
				{
					TargetDegree = GetOwner()->GetActorRotation().Yaw;
				}
			
				const float WasYaw = GetOwner()->GetActorRotation().Yaw; //我们游戏中角色的角度
	
				float WishYaw = TargetDegree;
			
				const float DegreeDis = DegreeDistance(WasYaw, WishYaw);
				const float CanRotateDegree = CanForceRotate == false ? (Character->RotateInputAcceptance * RotatePerSec * DeltaTime) : ForceRotateInfo->DegreeLimit;	//每秒转这么多度
	
				if (CanRotateDegree < DegreeDis)
				{
					//需要修正
					WishYaw = (WasYaw + (RotateClockwise(WasYaw, TargetDegree) ? 1.00f : -1.00f) * CanRotateDegree * Character->RotateInputAcceptance);
					WishYaw = FixDegree(WishYaw);
				}
	
				if (CanForceRotate == true)
					Character->SwitchActionForceRotateOnce(ForceRotateInfo->ActionId, ForceRotateInfo->Index, false, ForceRotateInfo->DegreeLimit);

				if (FixRotation.Active == true && (FMath::Abs(WishYaw - FixRotation.TargetYaw) <= FixRotation.FixUnderDegree))
				{
					WishYaw = FixRotation.TargetYaw;
				}
			
				//在这里设置了转向 	此处SetRotation 消耗较高  非必要不要Set  必要下tick只最终调用一次 最终计算 不要存在多次set
				YawChanged = WishYaw - WasYaw;
				if (!FMath::IsNearlyZero(YawChanged))
				{
					GetOwner()->SetActorRotation(FRotator(0, WishYaw, 0));
				}
				
				if (FMath::IsNearlyZero(TargetDegree - WishYaw)) TargetDegree = GetOwner()->GetActorRotation().Yaw;
			}
		}
		else
		{
			TargetDegree = this->GetOwner()->GetActorRotation().Yaw;
		}
		
		//TODO: 现在面向==转向
		// SetFaceDir(this->GetOwner()->GetActorForwardVector());

		//输入的力
		//只有在没有ForceMove和RootMotion的时候，才可能有重力影响
		// const float HasFallPlus =  50.000f * DeltaTime + HasFall;// 调整这个50就能调整下降速率
		// const float WeightMove = ( ForceMoveVelocity.IsNearlyZero() && FMath::IsNearlyZero(RootMotionMove.Z) ?  ActionGravityTimes : 0) * 
		// 	(-FallWeight * FMath::Pow(HasFallPlus, 2 ) / 10.000f);

		int GoTimes = 0;
		if (!Character->InFreezing())
			DeltaGone += DeltaTime;
		while (DeltaGone >= DeltaValve)
		{
			GoTimes += 1;
			DeltaGone -= DeltaValve;
		}
		bool GoFall = ForceMoveVelocity.IsNearlyZero() && FMath::IsNearlyZero(RootMotionMove.Z, 0.01f) && !bStandOnGround;
		if (GoFall == false)
			GoTimes = 0;
		// UKismetSystemLibrary::PrintString(this, FString("GoTimes = ").Append(FString::FromInt(GoTimes)), true, true,
		// 	GoTimes > 0 ? FLinearColor::Green:FLinearColor::Red, 10);
		float DeltaAdded = 0;
		for (int i = 1; i <= GoTimes; i++) DeltaAdded += i * DeltaValve;
		const float ThisTickFall = 36.000f * (DeltaAdded + HasFall);
		float WeightMove =  ActionGravityTimes * -ThisTickFall;

		// UKismetSystemLibrary::PrintString(Character, " --------- ");
		// UKismetSystemLibrary::PrintString(Character, " GoTimes: " + FString::FromInt(GoTimes));
		// UKismetSystemLibrary::PrintString(Character, " DeltaAdded: " + FString::SanitizeFloat(DeltaAdded));
		// UKismetSystemLibrary::PrintString(Character, " HasFall: " + FString::SanitizeFloat(HasFall));
		// UKismetSystemLibrary::PrintString(Character, " ActionGravityTimes: " + FString::SanitizeFloat(ActionGravityTimes));
		// UKismetSystemLibrary::PrintString(Character, " ThisTickFall: " + FString::SanitizeFloat(ThisTickFall));
		// UKismetSystemLibrary::PrintString(Character, " WeightMove: " + FString::SanitizeFloat(WeightMove));
		
		FVector2D ThisTickMoveDir = Character->MountsTypeRotate ? FVector2D(Character->GetActorForwardVector().GetSafeNormal()) : MoveDirNormal;
		
		if (KeepMovingDir.Active == true)
		{
			//在已经生效的时候，就开始不再记录当前输入的方向了，而是要试着改变这个方向
			if (
				KeepMovingDir.MergeRule == EMoveDirMergeRule::Cover ||
				(KeepMovingDir.MergeRule == EMoveDirMergeRule::IgnoreWhileNotZero && ThisTickMoveDir.IsNearlyZero() == true)
			){
				ThisTickMoveDir = KeepMovingDir.Direction;
			}else if (KeepMovingDir.MergeRule == EMoveDirMergeRule::Plus)
			{
				ThisTickMoveDir.X = (ThisTickMoveDir.X + KeepMovingDir.Direction.X) / 2.000f;
				ThisTickMoveDir.Y = (ThisTickMoveDir.Y + KeepMovingDir.Direction.Y) / 2.000f;
			}
			ThisTickMoveDir *= KeepMovingDir.Speed;
		}else
		{
			//尚未生效的时候，不断记录当前的方向
			KeepMovingDir.Direction = ThisTickMoveDir;
			KeepMovingDir.Speed = Speed;
			ThisTickMoveDir.X *= Character->SpeedInputAcceptance.X * Speed;
			ThisTickMoveDir.Y *= Character->SpeedInputAcceptance.Y * Speed;
			//UKismetSystemLibrary::PrintString(this, FString("Moving Accept ").Append(Character->SpeedInputAcceptance.ToString()));
		}
		
		FVector InputMove = FVector(
			ThisTickMoveDir.X  * DeltaTime,
			ThisTickMoveDir.Y  * DeltaTime,
			(Character->CanFly() == true ? 0 : WeightMove)
		);
		
		if (Character->ControlState.CanMove == EControlStateType::OutOfControl)
		{
			InputMove.X = 0;
			InputMove.Y = 0;
		}
		if (Character->ControlState.CanJump == EControlStateType::OutOfControl)
		{
			InputMove.Z = Character->CanFly() == true ? 0 : WeightMove;
		}

		if (FMath::IsNearlyZero(InputMove.X) == true && Character->UnderPlayerControl() == true)
		{
			PitchTarget = Character->OwnerPlayerController->GetInputCameraDir().Pitch;
		}

		//首先是获得这一帧全部的力
		ThisTickMove = InputMove + ForceMoveVelocity + RootMotionMove;
		
		// --- Fix ThisTickMove ---
		if (Character->ControlState.CanMove == EControlStateType::NoControl)
		{
			ThisTickMove.X = 0;
			ThisTickMove.Y = 0;
		}
		if (Character->ControlState.CanJump == EControlStateType::NoControl)
			ThisTickMove.Z = 0;
		if (Character->InFreezing())
			ThisTickMove = FVector::ZeroVector; //卡帧时0位移
		// --- --- ---
		

		// --- Add Squeeze & Drag
		//用来计算动画的东西，必须在被挤开前运行计算
		FVector2D MoveVec = ThisTickMoveDir.GetSafeNormal();
		FVector2D DirVec = FVector2D(Character->GetActorForwardVector().X, Character->GetActorForwardVector().Y).GetSafeNormal();
		float DirDegree = UKismetMathLibrary::DegAcos(FVector2D::DotProduct(MoveVec, DirVec));
		bool IsGoRight = FVector2D::CrossProduct(DirVec, MoveVec) >= 0 ;
		//被挤开，必须运行在最终移动前
		ThisTickMove += SqueezeThisTick();
		if (this->DragMoveInfo.Active)
			ThisTickMove += this->DragMoveInfo.Velocity;
		// --- --- ---
		

		// --- Fix Z ---
		// Character->GetCharacterMovement()->FindFloor(Character->GetCapsuleComponent()->GetComponentLocation(),
  //       					FindFloorResult, ThisTickMove.IsZero(), nullptr);
  //       if (FindFloorResult.bWalkableFloor && ThisTickMove.Z < 0)
  //       		ThisTickMove.Z = 0;
		
		if (FMath::IsNearlyZero(ThisTickMove.Z, 0.01f)) 
			ThisTickMove.Z = 0;
		
		// TODO:之后得考虑是 正常移动状态 还是 骑乘状态 还是 飞行状态。--- by QuanShenLong
		if (ThisTickMove.Z > 0)
			Character->GetCharacterMovement()->SetMovementMode(MOVE_Falling);
		// --- --- ---
		
		// --- MoveSmooth ---
		FStepDownResult StepDownResult;
		//MoveSmooth 消耗相当高 不建议直接调用 并且CharacterMovement 自身内部每帧逻辑已存在调用 如可以 建议继承CharacterMovement 重写相关逻辑
		//Character->GetCharacterMovement()->Velocity =  ThisTickMove / DeltaTime;
		Character->GetCharacterMovement()->MoveSmooth(ThisTickMove / DeltaTime, DeltaTime, &StepDownResult);
		if (StepDownResult.bComputedFloor)
			FindFloorResult = StepDownResult.FloorResult;
		else if (ThisTickMove.Z <= 0.f)
		{
			//FindFloor 消耗较高 不建议重复调用 并且CharacterMovement 自身内部每帧逻辑 已存在调用
			Character->GetCharacterMovement()->FindFloor(Character->GetCapsuleComponent()->GetComponentLocation(),
					FindFloorResult, ThisTickMove.IsZero(), nullptr);
		}
		else
			FindFloorResult.Clear();
		
		AAwCharacter* FindFloorHitCha = Cast<AAwCharacter>(FindFloorResult.HitResult.GetActor());
		bool bFindFloorHitCha = IsValid(FindFloorHitCha);
		bStandOnGround = FindFloorResult.bWalkableFloor && !bFindFloorHitCha;
		if (bStandOnGround)
			Character->GetCharacterMovement()->SetMovementMode(MOVE_Walking);

		// if (Cast<AAwCharacter>(this->Character)->IsPlayerCharacter())
		// {
		// 	AActor* HitActor = FindFloorResult.HitResult.GetActor();
		// 	if (bStandOnGround)
		// 	{
		// 		UKismetSystemLibrary::PrintString(Character, "IsStandOnGround: True", true, true, FLinearColor::Green);
		// 		if (HitActor)
		// 			UKismetSystemLibrary::PrintString(HitActor, HitActor->GetName(), true, true, FLinearColor::Green);
		// 	}
		// 	else
		// 	{
		// 		UKismetSystemLibrary::PrintString(Character, "IsStandOnGround: False", true, true, FLinearColor::Red);
		// 		if (HitActor)
		// 			UKismetSystemLibrary::PrintString(HitActor, HitActor->GetName(), true, true, FLinearColor::Red);
		// 	}
		// }
		
		ThisTickFinalMove = Character->GetActorLocation() - Character->GetLastLocation();
		// --- --- ---

		// if (Cast<AAwCharacter>(this->Character)->IsPlayerCharacter())
		// {
		// 	switch (Character->GetActionComponent()->CurrentActionState())
		// 	{
		// 	case ECharacterActionState::Ground:
		// 		UKismetSystemLibrary::PrintString(Character, "ECharacterActionState::Ground");
		// 		break;
		// 	case ECharacterActionState::Falling:
		// 		UKismetSystemLibrary::PrintString(Character, "ECharacterActionState::Falling");
		// 		break;
		// 	case ECharacterActionState::Flying:
		// 		UKismetSystemLibrary::PrintString(Character, "ECharacterActionState::Flying");
		// 		break;
		// 	case ECharacterActionState::Attached:
		// 		UKismetSystemLibrary::PrintString(Character,"ECharacterActionState::Attached");
		// 		break;
		// 	default: ;
		// 	}
		// }
		
		// if (Cast<AAwCharacter>(this->Character)->IsPlayerCharacter())
		// {
		// 	switch (Character->GetCharacterMovement()->MovementMode)
		// 	{
		// 		case MOVE_None: UKismetSystemLibrary::PrintString(Character, "MOVE_None");break;
		// 		case MOVE_Walking: UKismetSystemLibrary::PrintString(Character, "MOVE_Walking");break;
		// 		case MOVE_NavWalking: UKismetSystemLibrary::PrintString(Character, "MOVE_NavWalking");break;
		// 		case MOVE_Falling: UKismetSystemLibrary::PrintString(Character, "MOVE_Falling");break;
		// 		case MOVE_Swimming: UKismetSystemLibrary::PrintString(Character, "MOVE_Swimming");break;
		// 		case MOVE_Flying: UKismetSystemLibrary::PrintString(Character, "MOVE_Flying");break;
		// 		case MOVE_Custom: UKismetSystemLibrary::PrintString(Character, "MOVE_Custom");break;
		// 		case MOVE_MAX: UKismetSystemLibrary::PrintString(Character, "MOVE_MAX");break;
		// 		default: ;
		// 	}
		// }

		//连续2帧都在下方接触面近距离 则认为 已保持在接触面上 而非下落
		if (!Character->InFreezing())
		{
			if (bFindFloorHitCha &&
				FindFloorResult.GetDistanceToFloor()<Character->GetCharacterMovement()->MIN_FLOOR_DIST
				&&FindFloorHitCha->OnGround()
				)
					FallingZeroTickCount ++;
			else
				FallingZeroTickCount = 0;
		
			if (FallingZeroTickCount >= 10)
			{
				Character->ChangeCharacterState(ECharacterActionState::Ground);
				bStandOnGround = true;
				Character->GetCharacterMovement()->SetMovementMode(MOVE_Walking);
			}
		}
		
		const int OwnerSpdLv = Character->GetMoveSpeedLevel();
		int ForwardLv = (DirDegree < 77.5f ? 1 : (DirDegree > 112.5f ? -1 : 0)) * OwnerSpdLv;
		int SideSpeed = (DirDegree < 22.5f || DirDegree > 158.5f) ? 0 : (IsGoRight == true ? 1 : -1) * OwnerSpdLv;
		//转身也会做出对应走路动作
		//UKismetSystemLibrary::PrintString(this, FString("SideSpeed:").Append(FString::FromInt(SideSpeed)).Append(", YawChanged:").Append(FString::SanitizeFloat(YawChanged)));
		if (SideSpeed == 0 && ForwardLv == 0 && FMath::IsNearlyZero(YawChanged, 0.0001f) == false)
		{
			SideSpeed = (YawChanged > 0 ? 1:-1);
			ForwardLv = 1;
		}
		Character->SetMoveSpeedLevel(ForwardLv, SideSpeed);
		
		// --- Change State ---
		if (bStandOnGround == true)
		{
			if (HasFall >= 0.5f || Character->CanFly() == true)
			{
				if (Character->IsInMontageAction() == false)
					Character->PreorderActionByMontageState(
						ECharacterMontageState::Landing, FActionParam(), 0
					);
			}
			HasFall = 0;
			ChangeState(ECharacterActionState::Ground);
			if (ForceMove.TerminateOnGround) ForceMove.Active = false;
			PitchTarget = 0;
		}else
		{
			if (Character->CanFly())
			{
				HasFall = 0;
				ChangeState(ECharacterActionState::Flying);
			}else
			{
				if (ForceMove.Active)
				{
					HasFall = 0;
				}else
				{
					HasFall += DeltaAdded;//DeltaTime * ActionGravityTimes;
				}

				const float StartHasFall = UGameplayFuncLib::GetAwDataManager()->DebugConfig.StartHasFall;
				const float JumpHasFall = UGameplayFuncLib::GetAwDataManager()->DebugConfig.JumpHasFall;
				const bool ShouldFall =
					(ThisTickMove.Z <= 0 && HasFall > StartHasFall) || //如果是下落，需要这么多才能算Fall
						(ThisTickMove.Z > 0 /*&& HasFall > JumpHasFall*/);	//如果是上升，需要这么多才能算Fall
				// if (this->Character->IsPlayerCharacter())
				// 	UKismetSystemLibrary::PrintString(this, FString("Has Fall:").Append(FString::SanitizeFloat(HasFall)),
				// 		true,true, HasFall > StartHasFall ? FLinearColor::Yellow : FLinearColor::Green, 30
				// );
				if (ShouldFall == true)	
					ChangeState(ECharacterActionState::Falling);
			}
		}
		// --- --- ---

		
	}
	// --- Attaching 计算 ---
	else
	{
		//拽住了别人，所以被牵着走，此时引力等都会无效化
		HasFall = 0;

		ForceMove.Active = false;	//吸附的时候forceMove恒等于0 （TODO：不行再说）
		
		FVector ToPos = Character->CharacterAttachment.MyPointCatchesTarget->GetComponentLocation();
		const FVector PosOffset = ToPos - this->Character->GetActorLocation();
		
		//被我拽住的角色拽着走了
		if (Character->CharacterAttachment.HasTouched == false)
		{
			float FollowDis = 0.f;
	 		const float CanFollowDis = BeDraggedDistancePerSec * DeltaTime;
			
			while (this->AttachTargetMovementTrack.Num() && FollowDis < CanFollowDis)
			{
				const float Dis = FMath::Abs((AttachTargetMovementTrack[0] - ToPos).Size());
				const float CanMove = CanFollowDis - FollowDis;
				if (Dis <= CanMove)
				{
					//长度不如我，所以看下一个点了
					FollowDis += Dis;
					ToPos = AttachTargetMovementTrack[0];
					AttachTargetMovementTrack.RemoveAt(0);
				}else
				{
					//比我能走得远，得弄个新的点出来
					ToPos = ToPos + (AttachTargetMovementTrack[0] - ToPos).GetSafeNormal() * CanMove;
					FollowDis = CanFollowDis;
				}
			}
			if (!FMath::IsNearlyZero((ToPos - Character->GetLastLocation()).Size()))
			{
				this->Character->SetActorLocation(ToPos - PosOffset, false);
			}
			ChangeState(ECharacterActionState::Falling);	//抓到了没有吸住也是falling
			if (!AttachTargetMovementTrack.Num())
			{
				//设置为：已经移动到了，抓牢了
				Character->CharacterAttachment.HasTouched = true;
				if (Character->GetLocalRole() == ENetRole::ROLE_Authority)
					Character->OnAttachOnTargetSuccess();
				ChangeState(ECharacterActionState::Attached);
			}
		}
		else
		{
			//吸住了
			AttachTargetMovementTrack.Empty();
			this->Character->SetReplicateMovement(false);
			this->Character->SetActorLocation(Character->CharacterAttachment.AttachTarget->GetComponentLocation() - PosOffset);
			this->SetFaceDir(Character->CharacterAttachment.AttachTarget->GetComponentRotation().Vector());
			ChangeState(ECharacterActionState::Attached);
		}
		TargetDegree = GetOwner()->GetActorRotation().Yaw;
	}
	// --- --- ---

	
	// 同步给 Attaching 在我身上的角色，或者把角色从我身上移除
	int TracerIndex = 0;
	while (TracerIndex < Character->CharacterAttachment.AttachOnMe.Num())
	{
		if (Character->CharacterAttachment.AttachOnMe[TracerIndex].AttachmentValid() == false)
		{
			Character->CharacterAttachment.AttachOnMe.RemoveAt(TracerIndex);
			continue;
		}
		AAwCharacter* GuyOnMe = Cast<AAwCharacter>(Character->CharacterAttachment.AttachOnMe[TracerIndex].Catcher);
		if (!GuyOnMe || GuyOnMe->Dead() == true)
		{
			Character->CharacterAttachment.AttachOnMe.RemoveAt(TracerIndex);
			continue;
		}

		GuyOnMe->AddAttachTargetMovementTrack(
			GuyOnMe->GetAttachingTarget()->GetComponentLocation()
		);
		TracerIndex++;
	}
	
	if (InAttaching == false && Character->CanFly() == true)
	{
		float Dis = (PitchTarget - GetOwner()->GetActorRotation().Pitch);
		if (Dis > 180)  Dis -= 360;
		if (Dis < -180) Dis += 360;
		
		Character->AddActorLocalRotation(FRotator(Dis * 0.5f, 0, 0));
	}

	ThisTickXYMoved = ThisTickFinalMove.Size2D();

	ForceMove.TimeElapsed += DeltaTime;
	if (ForceMove.Active == true && ForceMove.ShouldBeDone() == true)
	{
		ForceMove.Active = false;
	}
	MoveDir.Set(0, 0);

	SqueezeVector.Empty();	//清空挤开信息
	DragMoveInfo.Active = false;
	//this->FixRotation.Active = false;	//试试看并不每帧关闭
	
	if (this->Character->GetActorLocation().Z <= this->DeadlyZ) Character->InstantKill();
}

void UAwMoveComponent::SetFaceDir(FVector Dir) const
{
	if (Dir.IsNearlyZero())
		return;
	//TODO FaceDir才是要设置的
	//GetOwner()->SetActorRotation(Dir.Rotation());
}

void UAwMoveComponent::SetActorDir(float Yaw)
{
	this->TargetDegree = Yaw;
	Character->SetActorRotation(FRotator(0,Yaw,0));
}

FForceMoveInfo UAwMoveComponent::GetForceMove() const
{
	return this->ForceMove;
}

void UAwMoveComponent::AddForceMove(FForceMoveInfo Move)
{
	this->ForceMove = Move;
}

void UAwMoveComponent::StopForceMove()
{
	this->ForceMove.Active = false;
}
void UAwMoveComponent::CheckForStopForceMoveOnActionChange()
{
	if (ForceMove.TerminateOnActionChange == true) ForceMove.Active = false;
}

void UAwMoveComponent::SetForceRotate(FVector2D FaceDir)
{
	// UKismetSystemLibrary::PrintString(this, FaceDir.ToString());
	ForceRotate.Active = true;
	ForceRotate.RotateForward = FVector(FaceDir.X, FaceDir.Y, 0);
}

void UAwMoveComponent::SetXYMove(FVector2D MoveDirection, int MoveSpeedLevel)
{
	this->MoveDir = MoveDirection;
	this->SpeedLevel = MoveSpeedLevel;
}

void UAwMoveComponent::Jump(float Height, float InSec)
{
	this->ForceMove.Jump(Height, InSec);
}

void UAwMoveComponent::TakeOff(float FlySpeed)
{
	//this->ForceMove.TakeOff(FlySpeed);
}

int UAwMoveComponent::GetMoveSpeed() const
{
	//根据档位算，10档是300 ：：50+POWER(25,0.6+A10/25)*10
	// const float BaseSpeed = 50.000f + FMath::Pow(25.000f, 0.600f + (Character->CharacterObj.CurProperty.MoveSpeed * 2.000f) / 25.000f) * 10.000f;
	// constexpr float SpeedLevelTimes = 1;
	// return ((SpeedLevel - 1) * SpeedLevelTimes + 1.000f) * BaseSpeed * 0.875f;
	return GetMoveSpeedByLv(SpeedLevel);
}

int UAwMoveComponent::GetMoveSpeedByLv(int SpdLv) const
{
	if (SpdLv <= 0)
		return 0;
	
	if (SpdLv == 1)
	{
		if (Character->CharacterObj.CurProperty.MoveSpeed.Num() >= 1)
			return Character->CharacterObj.CurProperty.MoveSpeed[0];
	}
	else if (SpdLv == 2)
	{
		if (Character->CharacterObj.CurProperty.MoveSpeed.Num() >= 2)
			return Character->CharacterObj.CurProperty.MoveSpeed[1];
		if (Character->CharacterObj.CurProperty.MoveSpeed.Num() >= 1)
			return Character->CharacterObj.CurProperty.MoveSpeed[0];
	}
	else if (SpdLv >= 3)
	{
		if (Character->CharacterObj.CurProperty.MoveSpeed.Num() >= 3)
			return Character->CharacterObj.CurProperty.MoveSpeed[2];
		if (Character->CharacterObj.CurProperty.MoveSpeed.Num() >= 2)
			return Character->CharacterObj.CurProperty.MoveSpeed[1];
		if (Character->CharacterObj.CurProperty.MoveSpeed.Num() >= 1)
			return Character->CharacterObj.CurProperty.MoveSpeed[0];
	}
	return 0;
}

FVector UAwMoveComponent::CaluStandardRootMotionMove(FRootMotionMovementParams RootMotionParam)
{
	FVector Result = FVector::ZeroVector;
	if (Character->ControlState.CanMove>=EControlStateType::OutOfControl)
	{
		return  Result;
	}
	Result = RootMotionParam.GetRootMotionTransform().GetLocation();
	Result *= Character->GetMesh()->GetComponentScale().X; 
	Result = Character->GetActorRotation().RotateVector(Result);
	Result = FRotator(0,-90,0).RotateVector(Result);
	return  Result;
}

void  UAwMoveComponent::ChangeState(ECharacterActionState ToState)
{
	Character->ChangeCharacterState(ToState);
	//ForceMove不会随着状态变化而变化
}

FVector UAwMoveComponent::SqueezeThisTick()
{
	FVector Res = FVector();

	bool XSameDir = true;
	float MostX = 0;
	bool YSameDir = true;
	float MostY = 0;
	bool ZSameDir = true;
	float MostZ = 0;
	
	for (const FVector ThisSqueeze : this->SqueezeVector)
	{
		if (XSameDir == true)
		{
			if (FMath::IsNearlyZero(MostX))
			{
				MostX = ThisSqueeze.X;
			}else
			{
				if (MostX * ThisSqueeze.X < 0)
				{
					XSameDir = false;
				}else
				{
					
					MostX = FMath::Abs(ThisSqueeze.X) > FMath::Abs(MostX) ? ThisSqueeze.X : MostX;
				}
			}
		}
		if (YSameDir == true)
		{
			if (FMath::IsNearlyZero(MostY))
			{
				MostY = ThisSqueeze.Y;
			}else
			{
				if (MostY * ThisSqueeze.Y < 0)
				{
					YSameDir = false;
				}else
				{
					
					MostY = FMath::Abs(ThisSqueeze.Y) > FMath::Abs(MostY) ? ThisSqueeze.Y : MostY;
				}
			}
		}
		if (ZSameDir == true)
		{
			if (FMath::IsNearlyZero(MostZ))
			{
				MostZ = ThisSqueeze.Z;
			}else
			{
				if (MostZ * ThisSqueeze.Z < 0)
				{
					ZSameDir = false;
				}else
				{
					
					MostZ = FMath::Abs(ThisSqueeze.Z) > FMath::Abs(MostZ) ? ThisSqueeze.Z : MostZ;
				}
			}
		}
	}
	Res.X = XSameDir == true ? MostX : 0;
	Res.Y = YSameDir == true ? MostY : 0;
	Res.Z = ZSameDir == true ? MostZ : 0;
	
	return Res;
}

void UAwMoveComponent::AddSqueeze(FVector SqueezeForce)
{
	this->SqueezeVector.Add(SqueezeForce);
}

void UAwMoveComponent::AddDragForce(FDragMoveInfo Drag)
{
	if (Drag.Active == false) return;
	if (this->DragMoveInfo.Active == false || this->DragMoveInfo.Priority <= Drag.Priority)
	{
		this->DragMoveInfo = Drag;
	}
}

void UAwMoveComponent::SetFixRotation(float TargetYaw, float FixUnderDegree)
{
	this->FixRotation.Active = true;
	this->FixRotation.TargetYaw = TargetYaw;
	this->FixRotation.FixUnderDegree = FixUnderDegree;
}

void UAwMoveComponent::StopFixRotation()
{
	this->FixRotation.Active = false;
}