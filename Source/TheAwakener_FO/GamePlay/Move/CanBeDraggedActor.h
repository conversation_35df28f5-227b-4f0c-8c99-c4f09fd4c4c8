// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Components/CapsuleComponent.h"
#include "UObject/Object.h"
#include "CanBeDraggedActor.generated.h"

/**
 * 可被拖拽的角色类型，根据这个类型，拖拽时执行方式不同
 */
UENUM()
enum class EBeDraggedActorType : uint8
{
	Actor,	//只是一个Actor，直接改变其坐标
	Character,	//AwCharacter，调用其AddDragMove接口
};

/**
 * 有这个的Actor可以被拖拽
 */
UCLASS(ClassGroup="Movement", editinlinenew, hidecategories=(Object,LOD,Lighting,TextureStreaming), meta=(DisplayName="Character Can Be Dragged Move", BlueprintSpawnableComponent))
class THEAWAKENER_FO_API UCanBeDraggedActor : public UCapsuleComponent
{
	GENERATED_BODY()
public:
	//类型
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	EBeDraggedActorType Type = EBeDraggedActorType::Character;
	
	//当前是否激活，可以临时关闭
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool Active = true;
};
