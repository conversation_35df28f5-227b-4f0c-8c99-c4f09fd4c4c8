// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "AutoFixRotation.h"
#include "DragMoveInfo.h"
#include "KeepMoveDir.h"
#include "Components/ActorComponent.h"
#include "GameFramework/CharacterMovementComponent.h"
#include "TheAwakener_FO/GamePlay/Characters/Action/CharacterActionState.h"
#include "TheAwakener_FO/GamePlay/Characters/Attack/ForceMoveInfo.h"
#include "AwMoveComponent.generated.h"

class AAwCharacter;


/**
 * 临时凑效果的一个强行转身信息
 */
USTRUCT(BlueprintType)
struct FBeOffendedForceRotate
{
	GENERATED_BODY()
public:
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	bool Active = false;

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	float ToYaw = 0;

	FBeOffendedForceRotate(){}
	FBeOffendedForceRotate(float RotateToYaw): Active(true), ToYaw(RotateToYaw){}
};

/**
 *	移动模式
 */
UENUM(BlueprintType)
enum class EMovementType: uint8
{
	Ground,	//地面
	Fall,			//下落
	Jump,		//起跳
	Flying,	//飞行
	Attaching,	//正在抓向，而不是已经抓到了
};
//DECLARE_STATS_GROUP(TEXT("STATGROUP_AwMove"),STATGROUP_AwMove,STATCAT_Test);
//DECLARE_CYCLE_STAT(TEXT("AwMoveTick"),STAT_AwMoveTick,STATGROUP_AwMove);

UCLASS(Blueprintable, meta=(BlueprintSpawnableComponent), Category="AW|Gameplay|Component")
class THEAWAKENER_FO_API UAwMoveComponent : public UActorComponent
{
	GENERATED_BODY()

public:
	// Sets default values for this component's properties
	UAwMoveComponent();

protected:
	// Called when the game starts
	virtual void BeginPlay() override;
private:
	const float BeDraggedDistancePerSec = 2000.0f;	//被拽着飞的距离（厘米/秒）

	float TargetDegree = 0;
	
	float Radius;
	float HalfHeight;
	int SpeedLevel;

	float HasFall = 0;	//下落了多少秒
	float DeltaGone = 0;	//DeltaTime累积了多少
	float DeltaValve = 0.005;	//这么久下落一次
	
	FVector2D MoveDir;	//本帧移动方向
	FVector2D RotateDir;	//本帧角色期待的面向
	FVector FaceDirVector;	//面向专用
	FVector RootMotionMove;	//来自RootMotion的Move
	FRotator RootMotionRotation;	//来自RootMotion的Rotation
	
	FForceMoveInfo ForceMove;	//本帧受到的最后一个强制移动，强制移动是指受击的那个移动
	FForceRotateInfo ForceRotate;
	FVector ThisTickMove;			//这一帧需要的移动
	FVector ThisTickFinalMove;		//最终这一帧的移动
	//FInertiaMoveInfo InertiaMove;	//惯性的移动
	FAutoFixRotation FixRotation;	//自动瞄准的辅助转角

	// 下落的时候z轴移动为0的帧数
	// TODO：这是一个补丁 --- by QuanShenLong
	int FallingZeroTickCount = 0;
	
	UPROPERTY()
	AAwCharacter* Character;

	UPROPERTY()
	float DeadlyZ = -50000; //如果Z高度低于这个，将当场毙命

	virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

	//当前动作吃重力的比例，这个值每个tick都可能变化，这仅仅作用于有RootMotion或者ForceMove的时候，所以通常应该是0.f
	UPROPERTY()
	float ActionGravityTimes = 1.f;

	//是否使用当前动作的RootMotion中的旋转值
	UPROPERTY()
	bool UseRootMotionRotation = false;

	void ChangeState(ECharacterActionState ToState);

	static float DegreeDistance(float Degree1, float Degree2)
	{
		float Dis = FMath::Abs(Degree1 - Degree2);
		while (Dis < 0) Dis += 360.00f;
		return Dis > 180 ? (360.00f - Dis) : Dis;
	}
	static bool RotateClockwise(FVector Vector1, FVector Vector2)
	{
		return FVector2D::CrossProduct(
			FVector2D(Vector1.X, Vector1.Y).GetSafeNormal(),
			FVector2D(Vector2.X, Vector2.Y).GetSafeNormal()) >= 0;
	}
	static bool RotateClockwise(float StartDegree, float EndDegree)
	{
		float Deg = EndDegree - StartDegree;
		while (Deg > 360) Deg -= 360.00f;
		while (Deg < 0) Deg += 360.00f;
		return Deg >= 179.9f ? false : true;
	}
	static float FixDegree(float Degree)
	{
		while (Degree < -180) Degree += 360.f;
		while (Degree > 180) Degree -= 360.f;
		return Degree;
	}

	//当前记录的移动方向
	UPROPERTY()
	FKeepMoveDir KeepMovingDir = FKeepMoveDir();

	//这帧收到的所有的挤开速度
	UPROPERTY()
	TArray<FVector> SqueezeVector;
	//算出这一帧的挤开力
	FVector SqueezeThisTick();

	//这帧被人拽着走的信息
	FDragMoveInfo DragMoveInfo;
public:
	//是否暂停了，逻辑上的暂停了
	UPROPERTY()
	bool Paused = false;

	//这帧x,y距离上移动了多少厘米，暴露给外部的数据
	UPROPERTY()
	float ThisTickXYMoved = 0;
	UFUNCTION(BlueprintPure)
	int GetMoveSpeed() const;
	int GetMoveSpeedByLv(int SpdLv) const;

	FVector CaluStandardRootMotionMove(FRootMotionMovementParams RootMotionParam);
	
	UPROPERTY()
	float FallWeight = 50.00f;	//每秒下降自然这么多

	//我所Attach的目标的移动轨迹
	UPROPERTY()
	TArray<FVector> AttachTargetMovementTrack;

	UFUNCTION()
	FVector GetThisTickMove() const { return ThisTickMove; }
	
	void SetFaceDir(FVector Dir) const;
	UFUNCTION()
	void SetActorDir(float Yaw);

	FForceMoveInfo GetForceMove() const;
	void AddForceMove(FForceMoveInfo Move);
	void StopForceMove();
	void CheckForStopForceMoveOnActionChange();

	UPROPERTY(BlueprintReadOnly)
	bool bStandOnGround = false;
	/**
	 * 设置面向（来自AI）
	 * @param FaceDir 
	 */
	void SetForceRotate(FVector2D FaceDir);
	/**
	 *来自CMD的走路
	 *@param MoveDirection 走的方向
	 *@param MoveSpeedLevel
	 */
	void SetXYMove(FVector2D MoveDirection, int MoveSpeedLevel);
	/**
	 *来自Cmd的跳跃，请在外部确保角色能跳了才调用这个，用在Notify的CharacterJump里面
	 *@param Height 能跳多高（厘米）
	 *@param InSec 几秒跳到最高点，因为Jump是个特殊处理的东西
	 */
	void Jump(float Height, float InSec);
	/**
	 *来自Cmd的飞行，用在NotifyState里面的CharacterTakeOff里面
	 *这当然是直上直下的。
	 *@param FlySpeed 速度的倍率，用来乘以角色本来的speed；如果是负数，就是Landing了
	 */
	void TakeOff(float FlySpeed);

	/**
	 * 添加一个挤开
	 */
	void AddSqueeze(FVector SqueezeForce);

	bool IsFalling() const
	{
		return !bStandOnGround && ThisTickFinalMove.Z < 0 && !FMath::IsNearlyZero(ThisTickFinalMove.Z);
	}
	
	UPROPERTY(VisibleAnywhere, BlueprintReadWrite)
	FName ProfileName;

	void SetActionGravityTimes(float ToTimes = 0.f)
	{
		this->ActionGravityTimes = ToTimes;
	}

	void StopCurrentForceMove()
	{
		this->ForceMove.Active = false;
	}

	void SetKeepMovingActive(bool ToActive, EMoveDirMergeRule Rule = EMoveDirMergeRule::Cover)
	{
		this->KeepMovingDir.Active = ToActive;
		this->KeepMovingDir.MergeRule = Rule;
	}

	/**
	 * 添加一个被拽走的力
	 */
	void AddDragForce(FDragMoveInfo Drag);

	/**
	 * 开启自动瞄准
	 * @param TargetYaw 目标转到多少度（-180到180）
	 * @param FixUnderDegree 与最终转向目标多少度以内，我才修正到这个角度
	 */
	void SetFixRotation(float TargetYaw, float FixUnderDegree);

	void StopFixRotation();

	//TODO 强行凑效果吧，挨打转向
	FBeOffendedForceRotate BeOffendedForceRotate;

	void SetUseRootMotionRotation(bool bUse)
	{
		UseRootMotionRotation = bUse;
	}

	UFUNCTION(BlueprintCallable)
	void SetMoveComponentPause(bool Pause)
	{
		Paused = Pause;
	}
};
