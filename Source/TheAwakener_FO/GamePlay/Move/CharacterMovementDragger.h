// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "CanBeDraggedActor.h"
#include "Components/BoxComponent.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "UObject/Object.h"
#include "CharacterMovementDragger.generated.h"

/**
 * 带着范围内的所有角色发生移动
 * 将我自己的移动距离赋值给他们的DragMoveInfo
 */
UCLASS(ClassGroup="Movement", editinlinenew, hidecategories=(Object,LOD,Lighting,TextureStreaming), meta=(DisplayName="[Capsule] Drag Characters Inside Move", BlueprintSpawnableComponent))
class THEAWAKENER_FO_API UCharacterMovementDragger : public UCapsuleComponent
{
	GENERATED_BODY()
private:
	//范围内的人
	UPROPERTY()
	TMap<AActor*, UCanBeDraggedActor*> ActorInside;

	//我自己上一帧的坐标
	UPROPERTY()
	FVector MyLastLocation = FVector();

	//上一帧我的旋转
	UPROPERTY()
	float LastYaw = 0;
public:
	UCharacterMovementDragger();
	virtual void BeginPlay() override;

	UFUNCTION(BlueprintCallable)
	void OnOverlapBegin(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp,
		int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult);

	UFUNCTION(BlueprintCallable)
	void OnOverlapEnd(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex);

	virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

	//这个带动力的优先级，如果有多个带动同一个角色，就会取优先级最高的那个
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int Priority = 0;

	UFUNCTION(BlueprintCallable)
	TArray<AActor*> CaughtActors();
};

/**
 * 带着范围内的所有角色发生移动
 * 将我自己的移动距离赋值给他们的DragMoveInfo
 */
UCLASS(ClassGroup="Movement", editinlinenew, hidecategories=(Object,LOD,Lighting,TextureStreaming), meta=(DisplayName="[Box] Drag Characters Inside Move", BlueprintSpawnableComponent))
class THEAWAKENER_FO_API UCharacterMovementDragger_Box : public UBoxComponent
{
	GENERATED_BODY()
private:
	//范围内的人
	UPROPERTY()
	TMap<AActor*, UCanBeDraggedActor*> ActorInside;

	//我自己上一帧的坐标
	UPROPERTY()
	FVector MyLastLocation = FVector();

	UPROPERTY()
	float LastYaw = 0;
public:
	UCharacterMovementDragger_Box();
	virtual void BeginPlay() override;

	UFUNCTION(BlueprintCallable)
	void OnOverlapBegin(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp,
		int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult);

	UFUNCTION(BlueprintCallable)
	void OnOverlapEnd(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex);

	virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

	//这个带动力的优先级，如果有多个带动同一个角色，就会取优先级最高的那个
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int Priority = 0;

	UFUNCTION(BlueprintCallable)
	TArray<AActor*> CaughtActors();
};