// Fill out your copyright notice in the Description page of Project Settings.


#include "CharacterMovementDragger.h"
UCharacterMovementDragger::UCharacterMovementDragger()
{
	PrimaryComponentTick.bCanEverTick = true;
}

void UCharacterMovementDragger::BeginPlay()
{
	Super::BeginPlay();

	this->MyLastLocation = GetOwner() ? GetOwner()->GetActorLocation() : FVector();
	this->LastYaw = GetComponentRotation().Yaw ;
	OnComponentBeginOverlap.AddDynamic(this, &UCharacterMovementDragger::OnOverlapBegin);
	OnComponentEndOverlap.AddDynamic(this, &UCharacterMovementDragger::OnOverlapEnd);
}
	
void UCharacterMovementDragger::OnOverlapBegin(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp,
	int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult)
{
	this->ActorInside.Add(OtherActor, Cast<UCanBeDraggedActor>(OtherComp));

}

void UCharacterMovementDragger::OnOverlapEnd(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex)
{
	this->ActorInside.Remove(OtherActor);
}

void UCharacterMovementDragger::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
	Super::TickComponent(DeltaTime, TickType, ThisTickFunction);
	const FVector Moved = GetOwner()->GetActorLocation() - this->MyLastLocation;

	for (const TTuple<AActor*, UCanBeDraggedActor*> AInside : this->ActorInside)
	{
		if (!AInside.Value || AInside.Value->Active == false) continue;

		FVector YawMoved =  (AInside.Key->GetActorLocation() - GetOwner()->GetActorLocation()).RotateAngleAxis(GetOwner()->GetActorRotation().Yaw - LastYaw, FVector::UpVector)
						+ GetOwner()->GetActorLocation() - AInside.Key->GetActorLocation();
		YawMoved.Z = 0;
		//UKismetSystemLibrary::PrintString(this, FString("YawMoved ").Append(YawMoved.ToString()));
		
		switch (AInside.Value->Type)
		{
		case EBeDraggedActorType::Character:
			{
				//是一个角色
				const AAwCharacter* Cha = Cast<AAwCharacter>(AInside.Key);
				if (Cha)
				{
					Cha->AddDragMove(FDragMoveInfo( Moved + YawMoved, this->Priority));
				}
			}break;
		case EBeDraggedActorType::Actor:
			{
				//是一个Actor
				AInside.Key->SetActorLocation(AInside.Key->GetActorLocation() + Moved + YawMoved);
			}break;
		default:break;
		}
	}
	
	this->MyLastLocation = GetOwner()->GetActorLocation();
	this->LastYaw = GetComponentRotation().Yaw;
}

UCharacterMovementDragger_Box::UCharacterMovementDragger_Box()
{
	PrimaryComponentTick.bCanEverTick = true;
}

void UCharacterMovementDragger_Box::BeginPlay()
{
	Super::BeginPlay();

	this->MyLastLocation = GetOwner() ? GetOwner()->GetActorLocation() : FVector();
	this->LastYaw = GetComponentRotation().Yaw ;
	OnComponentBeginOverlap.AddDynamic(this, &UCharacterMovementDragger_Box::OnOverlapBegin);
	OnComponentEndOverlap.AddDynamic(this, &UCharacterMovementDragger_Box::OnOverlapEnd);
}
	
void UCharacterMovementDragger_Box::OnOverlapBegin(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp,
	int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult)
{	
	this->ActorInside.Add(OtherActor, Cast<UCanBeDraggedActor>(OtherComp));
}

void UCharacterMovementDragger_Box::OnOverlapEnd(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex)
{
	this->ActorInside.Remove(OtherActor);
}

void UCharacterMovementDragger_Box::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
	Super::TickComponent(DeltaTime, TickType, ThisTickFunction);
	const FVector Moved = GetOwner()->GetActorLocation() - this->MyLastLocation;

	for (const TTuple<AActor*, UCanBeDraggedActor*> AInside : this->ActorInside)
	{
		if (!AInside.Value || AInside.Value->Active == false) continue;

		FVector YawMoved =  (AInside.Key->GetActorLocation() - GetOwner()->GetActorLocation()).RotateAngleAxis(GetOwner()->GetActorRotation().Yaw - LastYaw, FVector::UpVector)
						+ GetOwner()->GetActorLocation() - AInside.Key->GetActorLocation();
		YawMoved.Z = 0;
		//UKismetSystemLibrary::PrintString(this, FString("YawMoved ").Append(YawMoved.ToString()));
		
		switch (AInside.Value->Type)
		{
		case EBeDraggedActorType::Character:
			{
				//是一个角色
				const AAwCharacter* Cha = Cast<AAwCharacter>(AInside.Key);
				if (Cha)
				{
					Cha->AddDragMove(FDragMoveInfo( Moved + YawMoved, this->Priority));
				}
			}break;
		case EBeDraggedActorType::Actor:
			{
				//是一个Actor
				AInside.Key->SetActorLocation(AInside.Key->GetActorLocation() + Moved + YawMoved);
			}break;
			default:break;
		}
	}
	
	this->MyLastLocation = GetOwner()->GetActorLocation();
	this->LastYaw = GetComponentRotation().Yaw;
}

TArray<AActor*> UCharacterMovementDragger_Box::CaughtActors()
{
	TArray<AActor*> Res;
	for (const TTuple<AActor*, UCanBeDraggedActor*> AInside : this->ActorInside)
	{
		if (AInside.Key)
			Res.Add(AInside.Key);
	}
	return Res;
}
TArray<AActor*> UCharacterMovementDragger::CaughtActors()
{
	TArray<AActor*> Res;
	for (const TTuple<AActor*, UCanBeDraggedActor*> AInside : this->ActorInside)
	{
		if (AInside.Key)
			Res.Add(AInside.Key);
	}
	return Res;
}
