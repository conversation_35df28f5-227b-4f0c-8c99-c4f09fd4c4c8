// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Components/SplineComponent.h"
#include "TrackMoveComponent.generated.h"

/**
 * 轨道式移动：固定在某条轨道上移动（需要给轨道Spline所在的Actor）
 */
UENUM(BlueprintType)
enum class ETrackType :uint8
{
	//被动移动,如矿车等 
	Passive,
	//主动移动,如有人驾驶的车等
	Initiative

};

UENUM(BlueprintType)
enum class EAutoTrackMoveType :uint8
{
	//单向 
	Once,
	//来回
	Swing
};

UCLASS(Blueprintable, meta=(BlueprintSpawnableComponent), Category="AW|Gameplay|Component")
class THEAWAKENER_FO_API UTrackMoveComponent : public UActorComponent
{
	GENERATED_BODY()

private:

	UPROPERTY()
	USplineComponent* Track = nullptr;

protected:
	virtual void BeginPlay() override;
	virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;
public:


	DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnTrackBegin,UTrackMoveComponent*, TrackMoveComponent, AActor*, ComponentOwner);
	DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FOnTrackTick,float,DeltaTime ,UTrackMoveComponent*, TrackMoveComponent, AActor*, ComponentOwner);
	DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnTrackStop, UTrackMoveComponent*, TrackMoveComponent, AActor*, ComponentOwner);
	DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnTrackMoveStop, UTrackMoveComponent*, TrackMoveComponent, AActor*, ComponentOwner);
	DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnTrackEnd, UTrackMoveComponent*, TrackMoveComponent, AActor*, ComponentOwner);

	UPROPERTY(BlueprintAssignable)
		FOnTrackBegin OnTrackBeginEvent;
	UPROPERTY(BlueprintAssignable)
		FOnTrackTick OnTrackTickEvent;
	UPROPERTY(BlueprintAssignable)
		FOnTrackStop OnTrackStopEvent;
	UPROPERTY(BlueprintAssignable)
		FOnTrackEnd OnTrackEndEvent;

	//是否暂停了，逻辑上的暂停了
	UPROPERTY(BlueprintReadWrite, Category = "TrackMoveComponent")
	bool Paused = false;

	UTrackMoveComponent();
	//是否沿着spline正向
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "TrackMoveComponent")
		bool GoForward = true;
	//是否面向spline正向
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "TrackMoveComponent")
		bool FaceForward = true;

	//自动移动方式 单次或来回
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "TrackMoveComponent")
		EAutoTrackMoveType AutoMoveType = EAutoTrackMoveType::Once;

	//移动方式 自动或被动计算
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "TrackMoveComponent")
		ETrackType TrackType = ETrackType::Passive;
	/**
	 * 移动力（厘米/秒）
	 * 这个力是向前的移动速度，若是负数，则会向后
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float MoveForce = 0;

	//当前自动移动速率 不包含方向
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float AutoMoveSpeed = 0;
	//自动移动速率最大值 不包含方向
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float AutoMoveSpeedMax = 300;
	//自动移动加速度 不包含方向
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float AutoMoveAcceleration = 100;
	//转身速率
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float AutoSpinSpeed= 30;

	//是否存在要转向目标方向
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool HasTargetRotator;
	/**
	 * 最大移动力（厘米/秒）
	 * 这个力是最大向前的移动速度
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float MaxMoveForce = 1500;

	/**
	 * 力衰减（厘米/秒）
	 * 这个力是MoveForce向0接近的标准
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float ForceReduction = 100;

	/**
	 * Spline拥有者
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	AActor* TrackActor = nullptr;

	/**
	 * 是否会被摧毁了，如果原本在轨道上，现在脱离了轨道就会被标记为要摧毁了
	 */


	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool bOutTrack = false;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float StartPercentageOnTrack = 0.1;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float CurTrackPercent;

	UFUNCTION(BlueprintCallable)
	void GetTrackFromActor(AActor* Actor);

	/**
	 * 给一个推力
	 */
	UFUNCTION(BlueprintCallable)
	void Push(FVector Vec);
	
	/**
	 * 停止小车的移动以及旋转
	 */
	UFUNCTION(BlueprintCallable)
	void Stop();
	
	/**
	* 移动以及旋转是否都停止
	*/
	UFUNCTION(BlueprintCallable,BlueprintPure)
	bool IsStop();

	/**
	* 停止移动
	*/
	UFUNCTION(BlueprintCallable)
	void StopMove();
	/**
	* 停止移动
	*/
	UFUNCTION(BlueprintCallable,BlueprintPure)
	bool IsMoveStop();


	//重新开始自动移动
	UFUNCTION(BlueprintCallable)
	void ReStartAutoMove();

	/**
	 * 小车撞到墙，MoveForce方向改为反向，大小改为一半
	 */
	UFUNCTION(BlueprintCallable)
	void BlockWall(const UShapeComponent* NewBlockShapeComp);

	UFUNCTION(BlueprintCallable, BlueprintPure)
	float GetOwnerDistanceAlongSpline();

	UFUNCTION(BlueprintCallable, BlueprintPure)
	FRotator GetRotationAlongSpline();

	UFUNCTION(BlueprintCallable, BlueprintPure)
	FVector GetOwnerCurTrackDirection();

	UFUNCTION(BlueprintCallable, BlueprintPure)
	FVector FindOwnerClosestLocationOnSpline();

	UFUNCTION(BlueprintCallable, BlueprintPure)
	bool IsOwnerOnTrack(float MinDistance = 0.0f);

	UFUNCTION(BlueprintCallable, BlueprintPure)
	float AngleBetweenTargetRotatorAndLastRotation();
	//设置目标特殊旋转方向 逐帧按照SpinSpeed趋向 会覆盖spline旋转
	UFUNCTION(BlueprintCallable)
	void SetTargetRotator(FRotator Target);

	//设置拥有者到spine固定百分比位置 第二参数为是否清空拥有者当前移动和旋转计算
	UFUNCTION(BlueprintCallable)
	void SetOwnerLocationAndRotationByPercent(float NewPercent,bool ForceStop = false,bool IsFaceForward = true);

	UPROPERTY()
	UShapeComponent* BlockShapeComp;

protected:
	void PassiveMoveTick(float DeltaTime);

	void InitativeMoveTick(float DeltaTime);

	void RotationTick(float DeltaTime);
private:
	//初始加速度 以便还原移动
	float baseMoveAcceleration;

	//目标旋转方向
	FRotator TargetRotator;

	FRotator LastRotation;

	float SpinPercent;
};
