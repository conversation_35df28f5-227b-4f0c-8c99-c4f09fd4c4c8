// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "KeepMoveDir.generated.h"

/**
 * 保持角度向量和输入向量之间的关系
 */
UENUM()
enum class EMoveDirMergeRule : uint8
{
	//以KeepMoveDir为准
	Cover,
	//两个向量平均值（相加/2）
	Plus,
	//若输入非0则无视KeepMoveDir
	IgnoreWhileNotZero
};

/**
 * 保持某个角度移动的信息
 */
USTRUCT()
struct FKeepMoveDir
{
	GENERATED_BODY()
public:
	//是否激活中
	UPROPERTY()
	bool Active = false;
	
	//要保持的角度的向量
	UPROPERTY()
	FVector2D Direction = FVector2D::ZeroVector;

	//保持的速度
	UPROPERTY()
	float Speed = 0;

	//与输入向量之间的关系
	UPROPERTY()
	EMoveDirMergeRule MergeRule = EMoveDirMergeRule::Cover;
	
	FKeepMoveDir(){};
	FKeepMoveDir(bool ActiveImmediately, EMoveDirMergeRule Rule = EMoveDirMergeRule::Cover):
		Active(ActiveImmediately), MergeRule(Rule){};
};