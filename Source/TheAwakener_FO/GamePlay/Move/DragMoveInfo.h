// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/Object.h"
#include "DragMoveInfo.generated.h"

/**
 * 被拽着走的信息
 */
USTRUCT(BlueprintType)
struct FDragMoveInfo
{
	GENERATED_BODY()
public:
	//是否被激活
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	bool Active = false;

	//这帧拽着走多少
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FVector Velocity = FVector::ZeroVector;

	//这个拽着走的优先级，执行优先级最高的被拽着走
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int Priority = 0;

	FDragMoveInfo(){};
	FDragMoveInfo(FVector Velo, int ThisPriority = 1):
		Active(!Velo.IsNearlyZero()), Velocity(Velo), Priority(ThisPriority){};
};
