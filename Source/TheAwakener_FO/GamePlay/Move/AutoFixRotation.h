// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/Object.h"
#include "AutoFixRotation.generated.h"

/**
 * 辅助自动瞄准
 */
USTRUCT(BlueprintType)
struct FAutoFixRotation
{
	GENERATED_BODY()
public:
	//是否启用了
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	bool Active = false;

	//目标角度(Yaw)，请确保在-180到180之间，不然…………
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	float TargetYaw = 0;

	//修正多少度，也就是我本来的目标度数和这个度数差距小于等于这个范围才启用这个
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	float FixUnderDegree = 15;
};
