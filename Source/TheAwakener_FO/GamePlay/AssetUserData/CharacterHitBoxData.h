// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "BeCaughtHitBox.h"
#include "TheAwakener_FO/GamePlay/Characters/HitBox/CharacterHitBox.h"
#include "CharacterHitBoxData.generated.h"

/**
 * 加上这个，代表这个是个角色受击框，但是别忘了碰撞关系还是要调调的
 */
UCLASS()
class THEAWAKENER_FO_API UCharacterHitBoxData : public UBeCaughtHitBox
{
	GENERATED_BODY()
public:
	UPROPERTY(BlueprintReadOnly)
	FString Id;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	ECharacterHitBoxType Type;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	EChaPartType PartType;

	//挂向了哪个Seat的AttachPoint，如果这不是空，就能抓了
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString SeatPointId;

	//所属的部位
	FChaPart* BelongsToPart = nullptr;

	//被视为Just Dodge，通常都是false的，只有被Montage AnimState开启了才会变成true
	UPROPERTY(BlueprintReadOnly)
	FJustDodgeInfo AsJustDodge = FJustDodgeInfo();

	/**
	 * 默认情况下是否是启用的
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool DefaultActive = Type == ECharacterHitBoxType::Normal;
	
	//这个盒子的默认防御信息，在没有特殊的情况下，就会启用这个防御信息作为受攻击的时候的防御信息
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FDefenseInfo DefaultDefenseInfo;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float HitPhysicalAnimBlendRate = 1.0f;
	/**
	 * 对于老的CharacterHitBox的一个兼容
	 * @param Box 传来一个CharacterHitBox
	 * @return 返回一个CharacterHitBoxData
	 */
	static UCharacterHitBoxData* FromCharacterHitBox(UCharacterHitBox* Box);
};
