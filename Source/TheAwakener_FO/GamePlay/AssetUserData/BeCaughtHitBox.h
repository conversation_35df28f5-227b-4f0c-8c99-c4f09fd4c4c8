// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Engine/AssetUserData.h"
#include "TheAwakener_FO/GamePlay/Characters/Battle/BattleStructs.h"
#include "BeCaughtHitBox.generated.h"

/**
 * 加上这个，那么这个将被视为一个被抓取框
 */
UCLASS(BlueprintType)
class THEAWAKENER_FO_API UBeCaughtHitBox : public UAssetUserData
{
	GENERATED_BODY()
public:
	/**
	 *通常来说，Active应该都是True的，但是有些例外，就是格挡框
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite) 
	bool Active = true;

	//这个盒子的被碰撞优先级，越高越容易算被命中的
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int Priority = 1;

	/**
	 * 在被攻击框命中的时候会执行的事情，他是一个JsonFunc
	 * (UAttackHitBox* Box, AAwCharacter* Attacker, FOffenseInfo OffenseInfo)=>bool
	 * @param Box 攻击框的指针
	 * @param Attacker 攻击者
	 * @param OffenseInfo 攻击信息
	 * @param Defender 受击者，其实就是自己，因为脚本没发获得，得传一下
	 * @param Params 是这个String里面()的参数
	 * @return 是否算是命中，如果是true就是命中了，若是这个为空，或者函数找不到，都会按照True处理
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString OnAttackHit;
	
	UFUNCTION(BlueprintCallable)
	bool RunOnAttackHit(USceneComponent* Box, AActor* Attacker, FOffenseInfo OffenseInfo, AActor* Defender);
};
