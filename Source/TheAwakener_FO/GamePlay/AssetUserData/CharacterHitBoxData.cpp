// Fill out your copyright notice in the Description page of Project Settings.


#include "CharacterHitBoxData.h"

UCharacterHitBoxData* UCharacterHitBoxData::FromCharacterHitBox(UCharacterHitBox* Box)
{
	if (!Box) return nullptr;
	UCharacterHitBoxData* Res = NewObject<UCharacterHitBoxData>();
	Res->Id = Box->Id;
	Res->Type = Box->Type;
	Res->DefaultActive = Box->DefaultActive;
	Res->PartType = Box->PartType;
	Res->AsJustDodge = Box->AsJustDodge;
	Res->BelongsToPart = Box->BelongsToPart;
	Res->DefaultDefenseInfo = Box->DefaultDefenseInfo;
	Res->SeatPointId = Box->SeatPointId;
	Res->HitPhysicalAnimBlendRate = Box->HitPhysicalAnimBlendRate;
	Res->Priority = Box->Priority;
	return Res;
}
