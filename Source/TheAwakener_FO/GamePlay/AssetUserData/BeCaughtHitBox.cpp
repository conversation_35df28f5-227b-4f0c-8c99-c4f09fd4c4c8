// Fill out your copyright notice in the Description page of Project Settings.


#include "BeCaughtHitBox.h"

#include "TheAwakener_FO/FunctionLibrary/CallFuncLib.h"

bool UBeCaughtHitBox::RunOnAttackHit(USceneComponent* Box, AActor* Attacker, FOffenseInfo OffenseInfo, AActor* Defender)
{
	if (this->OnAttackHit.IsEmpty()) return true;
	const FJsonFuncData FuncData = UCallFuncLib::StringToJsonFuncData(this->OnAttackHit);
	UFunction* Func = UCallFuncLib::JsonFuncToUFunc(FuncData);
	if (IsValid(Func) == false) return true;
				
	struct {
		USceneComponent* HitBox;
		AActor* AttackerPointer;
		FOffenseInfo Offense;
		AActor* Defender;
		TArray<FString> Params;

		bool Result;
	} FuncParam;
				
	FuncParam.HitBox = Box;
	FuncParam.AttackerPointer = Attacker;
	FuncParam.Offense = OffenseInfo;
	FuncParam.Defender = Defender;
	FuncParam.Params = FuncData.Params;
				
	this->ProcessEvent(Func, &FuncParam);
	return FuncParam.Result;
}
