// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Engine/AssetUserData.h"
#include "UObject/Object.h"
#include "AttackHitBoxSign.generated.h"

/**
 *  代表这玩意儿是个攻击盒
 */
UCLASS()
class THEAWAKENER_FO_API UAttackHitBoxSign : public UAssetUserData
{
	GENERATED_BODY()
public:
	UAttackHitBoxSign();
	UAttackHitBoxSign(const FObjectInitializer& ObjectInitializer);
	//名称
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString Name;

	//这个碰撞盒的碰撞信息能在Leaving的时候Delay多少秒移除
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float LeavingDelay = 0;
};
