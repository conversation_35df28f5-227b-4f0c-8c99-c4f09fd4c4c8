#include "Buff.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GameFramework/Manager/AwDataManager.h"

FBuffModel FBuffModel::FromJson(TSharedPtr<FJsonObject> JsonObj)
{
	FBuffModel Model = FBuffModel();
	Model.Id = UDataFuncLib::AwGetStringField(JsonObj, "Id");
	
	for (const TSharedPtr<FJsonValue> Value : UDataFuncLib::AwGetArrayField(JsonObj, "Tag"))
		Model.Tags.Add(Value->AsString());

	Model.Priority = UDataFuncLib::AwGetNumberField(JsonObj, "Priority", 255);

	Model.TickTime = UDataFuncLib::AwGetNumberField(JsonObj, "TickTime", 0.f);

	Model.MaxStack = UDataFuncLib::AwGetNumberField(JsonObj, "MaxStack", 1);

	Model.IgnoreDifferentCaster =  UDataFuncLib::AwGetBoolField(JsonObj, "IgnoreDifferentCaster", false);
	
	if (Model.MaxStack < 1) Model.MaxStack = 1;

	for (const TSharedPtr<FJsonValue> Value : UDataFuncLib::AwGetArrayField(JsonObj, "Property"))
	{
		Model.CharacterPropertyModify.Add(FChaProp::FromJson(Value->AsObject(), 1, EChaPotentialType::Buff));
		Model.FindPropertyId(Value->AsObject());
	}
	
	if (JsonObj->HasField("ControlState"))
		Model.ControlState = FControlState::FromJson(JsonObj->GetObjectField("ControlState"));

	for (const TSharedPtr<FJsonValue> Value : UDataFuncLib::AwGetArrayField(JsonObj, "OnOccur"))
		Model.OnOccur.Add(UDataFuncLib::SplitFuncNameAndParams(Value->AsString()));

	for (const TSharedPtr<FJsonValue> Value : UDataFuncLib::AwGetArrayField(JsonObj, "OnTick"))
		Model.OnTick.Add(UDataFuncLib::SplitFuncNameAndParams(Value->AsString()));

	for (const TSharedPtr<FJsonValue> Value : UDataFuncLib::AwGetArrayField(JsonObj, "OnRemoved"))
		Model.OnRemoved.Add(UDataFuncLib::SplitFuncNameAndParams(Value->AsString()));

	for (const TSharedPtr<FJsonValue> Value : UDataFuncLib::AwGetArrayField(JsonObj, "OnHit"))
		Model.OnHit.Add(UDataFuncLib::SplitFuncNameAndParams(Value->AsString()));

	for (const TSharedPtr<FJsonValue> Value : UDataFuncLib::AwGetArrayField(JsonObj, "OnCrit"))
		Model.OnCrit.Add(UDataFuncLib::SplitFuncNameAndParams(Value->AsString()));
	
	for (const TSharedPtr<FJsonValue> Value : UDataFuncLib::AwGetArrayField(JsonObj, "OnBeHurt"))
		Model.OnBeHurt.Add(UDataFuncLib::SplitFuncNameAndParams(Value->AsString()));

	for (const TSharedPtr<FJsonValue> Value : UDataFuncLib::AwGetArrayField(JsonObj, "OnKill"))
		Model.OnKill.Add(UDataFuncLib::SplitFuncNameAndParams(Value->AsString()));

	for (const TSharedPtr<FJsonValue> Value : UDataFuncLib::AwGetArrayField(JsonObj, "OnBeKilled"))
		Model.OnBeKilled.Add(UDataFuncLib::SplitFuncNameAndParams(Value->AsString()));

	for (const TSharedPtr<FJsonValue> Value : UDataFuncLib::AwGetArrayField(JsonObj, "OnChangeAction"))
		Model.OnChangeAction.Add(UDataFuncLib::SplitFuncNameAndParams(Value->AsString()));

	for (const TSharedPtr<FJsonValue> Value : UDataFuncLib::AwGetArrayField(JsonObj, "OnOffense"))
		Model.OnOffense.Add(UDataFuncLib::SplitFuncNameAndParams(Value->AsString()));

	for (const TSharedPtr<FJsonValue> Value : UDataFuncLib::AwGetArrayField(JsonObj, "OnAnimNotfiy"))
		Model.OnAnimNotfiy.Add(UDataFuncLib::SplitFuncNameAndParams(Value->AsString()));

	for (const TSharedPtr<FJsonValue> Value : UDataFuncLib::AwGetArrayField(JsonObj, "OnAnimNotfiyBegin"))
		Model.OnAnimNotfiyBegin.Add(UDataFuncLib::SplitFuncNameAndParams(Value->AsString()));

	for (const TSharedPtr<FJsonValue> Value : UDataFuncLib::AwGetArrayField(JsonObj, "OnAnimNotfiyEnd"))
		Model.OnAnimNotfiyEnd.Add(UDataFuncLib::SplitFuncNameAndParams(Value->AsString()));
	// 	for (const TSharedPtr<FJsonValue> Value : UDataFuncLib::AwGetArrayField(JsonObj, "OnActionHit"))
	// 		Model.OnActionHit.Add(UDataFuncLib::SplitFuncNameAndParams(Value->AsString()));
	//
	// 	for (const TSharedPtr<FJsonValue> Value : UDataFuncLib::AwGetArrayField(JsonObj, "OnBeHitByAction"))
	// 		Model.OnBeHitByAction.Add(UDataFuncLib::SplitFuncNameAndParams(Value->AsString()));
	
	//------Rogue----------
	for (const TSharedPtr<FJsonValue> Value : UDataFuncLib::AwGetArrayField(JsonObj, "OnRogueGameStart"))
		Model.OnRogueGameStart.Add(UDataFuncLib::SplitFuncNameAndParams(Value->AsString()));
	
	for (const TSharedPtr<FJsonValue> Value : UDataFuncLib::AwGetArrayField(JsonObj, "OnRogueRoomStart"))
		Model.OnRogueRoomStart.Add(UDataFuncLib::SplitFuncNameAndParams(Value->AsString()));
	
	for (const TSharedPtr<FJsonValue> Value : UDataFuncLib::AwGetArrayField(JsonObj, "OnRogueRoomEnd"))
		Model.OnRogueRoomEnd.Add(UDataFuncLib::SplitFuncNameAndParams(Value->AsString()));
	
	for (const TSharedPtr<FJsonValue> Value : UDataFuncLib::AwGetArrayField(JsonObj, "AfterRogueLoadSaveData"))
		Model.AfterRogueLoadSaveData.Add(UDataFuncLib::SplitFuncNameAndParams(Value->AsString()));
	//------RogueEnd------
	
	return Model;
}

void FBuffModel::FindPropertyId(TSharedPtr<FJsonObject> JsonObj)
{
	if(JsonObj->HasField("HP"))
		PropertyID = "HP";
	if(JsonObj->HasField("Mp"))
		PropertyID = "Mp";
	if(JsonObj->HasField("Sp"))
		PropertyID = "Sp";
	if(JsonObj->HasField("Ap"))
		PropertyID = "Ap";
	if(JsonObj->HasField("AirDodgePoint"))
		PropertyID = "AirDodgePoint";
	if(JsonObj->HasField("PAtk"))
		PropertyID = "ATK";
	if(JsonObj->HasField("MAtk"))
		PropertyID = "ElementRate";
	if(JsonObj->HasField("PDef"))
		PropertyID = "DEF";
	if(JsonObj->HasField("MDef"))
		PropertyID = "ElementDEF";
	if(JsonObj->HasField("BeStrikeRate"))
		PropertyID = "BeStrikeRate";
	if(JsonObj->HasField("ActionSpeed"))
		PropertyID = "ActionSpeed";
	if(JsonObj->HasField("CriticalChance"))
		PropertyID = "CriticalChance";
	if(JsonObj->HasField("CriticalRate"))
		PropertyID = "CirticalPower";
};

TTuple<FBuffObj, TArray<UTimelineNode*>> FBuffObj::Init(FAddBuffInfo BuffInfo)
{
	this->ToBeRemoved = false;
	this->Model = BuffInfo.Model;
	this->Caster = BuffInfo.Caster;
	this->Carrier = BuffInfo.Target;
	this->Time = 0;
	this->Infinity = BuffInfo.Infinity;
	return this->Modify(BuffInfo);
}

TTuple<FBuffObj, TArray<UTimelineNode*>> FBuffObj::Modify(FAddBuffInfo AddBuffInfo){
	TTuple<FBuffObj, TArray<UTimelineNode*>> Res(*this, TArray<UTimelineNode*>());
	if (this->ToBeRemoved == true)
		return Res;
	const int WasStack = this->Stack;
	this->Stack += AddBuffInfo.AddStack;
	this->Stack = FMath::Clamp(this->Stack, 0, (this->Model.MaxStack));
	this->Duration = AddBuffInfo.SetToDuration == true ? AddBuffInfo.Duration :
		(this->Duration + AddBuffInfo.Duration);
	if (this->Stack <= 0 || (this->Duration <= 0 && this->Infinity == false))
	{
		return this->BeforeRemoved(false);
	}else
	{
		Res.Key = *this;
		if (this->Carrier && this->Model.OnOccur.Num())
		{	
			for (int i = 0; i < this->Model.OnOccur.Num(); i++)
			{
				UFunction* Func = UCallFuncLib::JsonFuncToUFunc(this->Model.OnOccur[i]);
				if (IsValid(Func) == false) continue;;
				
				struct {
					FBuffObj BuffObj;
					int32 WasStack;
					TArray<FString> Params;

					FBuffRunResult Result;
				} FuncParam;
				
				FuncParam.BuffObj = *this;
				FuncParam.WasStack = WasStack;
				FuncParam.Params = this->Model.OnOccur[i].Params;
				
				this->Carrier->ProcessEvent(Func, &FuncParam);
				//Buff = &FuncParam.Result.BuffObj;
				Res.Get<0>() = FuncParam.Result.BuffObj;
				if (FuncParam.Result.TimelineNode)
					Res.Get<1>().Add(FuncParam.Result.TimelineNode);
			}
		}
	}
	return Res;
}

TTuple<FBuffObj, TArray<UTimelineNode*>> FBuffObj::BeforeRemoved(bool Dispelled)
{
	this->ToBeRemoved = true;
	TTuple<FBuffObj, TArray<UTimelineNode*>> Res(*this, TArray<UTimelineNode*>());
	if (this->Carrier && this->Model.OnRemoved.Num())
	{
		for (int i = 0; i < this->Model.OnRemoved.Num(); i++)
		{
			UFunction* Func = UCallFuncLib::JsonFuncToUFunc(this->Model.OnRemoved[i]);
			if (IsValid(Func) == false) continue;
			
			struct {
				FBuffObj BuffObj;
				bool IsDispelled;
				TArray<FString> Params;

				FBuffRunResult Result;
			} FuncParam;
			
			FuncParam.BuffObj = *this;
			FuncParam.IsDispelled = Dispelled;
			FuncParam.Params = this->Model.OnRemoved[i].Params;
				
			this->Carrier->ProcessEvent(Func, &FuncParam);
			//Buff = &FuncParam.Result.BuffObj;
			Res.Get<0>() = FuncParam.Result.BuffObj;
			if (FuncParam.Result.TimelineNode)
				Res.Get<1>().Add(FuncParam.Result.TimelineNode);
		}
	}
	return Res;
}

TTuple<FBuffObj, TArray<UTimelineNode*>> FBuffObj::OnTick(float DeltaTime, bool ForceUse)
{
	this->Time += DeltaTime;
	if (this->Infinity == false) this->Duration -= DeltaTime;
	if (this->Duration <= 0 && this->Infinity == false)
	{
		return this->BeforeRemoved(false);
	}

	const int ShouldRunTimes = FMath::FloorToInt(this->Time / this->Model.TickTime);
	const bool CanRun = (
		ForceUse == true ||
		(
			this->Model.TickTime > 0 &&
			this->Model.OnTick.Num() &&
			ShouldRunTimes >= this->Ticked
		)
	) && this->Carrier != nullptr;

	TTuple<FBuffObj, TArray<UTimelineNode*>> Res(*this, TArray<UTimelineNode*>());

	if (CanRun == false) return Res;
	
	//最大追帧次数
	int CanRunTimes = FMath::Clamp(ShouldRunTimes - this->Ticked,0,2);
	
	while (CanRunTimes > 0)
	{
		FBuffObj ThisBuff = *this;
		for (int i = 0; i < this->Model.OnTick.Num(); i++)
		{
			UFunction* Func = UCallFuncLib::JsonFuncToUFunc(this->Model.OnTick[i]);
			if (IsValid(Func) == false) continue;;
			
			struct {
				FBuffObj BuffObj;
				int32 Ticked;
				TArray<FString> Params;
				
				FBuffRunResult Result;
			} FuncParam;
			
			FuncParam.BuffObj = ThisBuff;
			FuncParam.Ticked = ThisBuff.Ticked;
			FuncParam.Params = ThisBuff.Model.OnTick[i].Params;
				
			this->Carrier->ProcessEvent(Func, &FuncParam);
			Res.Get<0>() = FuncParam.Result.BuffObj;
			ThisBuff = FuncParam.Result.BuffObj;
			if (FuncParam.Result.TimelineNode)
				Res.Get<1>().Add(FuncParam.Result.TimelineNode);
		}
		CanRunTimes -= 1;
	}
	//一个是最大步长限制内不停再追 一个是追一下 追不上就放弃直接相等 以免连续n帧都在追帧重复执行某些逻辑
	//Res.Get<0>().Ticked+= CanRunTimes;
	Res.Get<0>().Ticked = ShouldRunTimes;
	
	return Res;
}

TTuple<FBuffObj, TArray<UTimelineNode*>> FBuffObj::OnActionChange(FActionInfo WasAction)
{
	TTuple<FBuffObj, TArray<UTimelineNode*>> Res(*this, TArray<UTimelineNode*>());
	if (this->Carrier && this->Model.OnChangeAction.Num())
	{
			
		for (int i = 0; i < this->Model.OnChangeAction.Num(); i++)
		{
			UFunction* Func = UCallFuncLib::JsonFuncToUFunc(this->Model.OnChangeAction[i]);
			if (IsValid(Func) == false) continue;
			
			struct {
				FBuffObj BuffObj;
				FActionInfo WasAction;
				FActionInfo CurrentAction;
				TArray<FString> Params;

				FBuffRunResult Result;
			} FuncParam;
			
			FuncParam.BuffObj = *this;
			FuncParam.WasAction = WasAction;
			FuncParam.CurrentAction = *this->Carrier->CurrentAction();
			FuncParam.Params = this->Model.OnChangeAction[i].Params;
				
			this->Carrier->ProcessEvent(Func, &FuncParam);
			//Buff = &FuncParam.Result.BuffObj;
			Res.Get<0>() = FuncParam.Result.BuffObj;
			if (FuncParam.Result.TimelineNode)
				Res.Get<1>().Add(FuncParam.Result.TimelineNode);
		}
	}
	return Res;
}

FAddBuffInfo::FAddBuffInfo(AAwCharacter* Caster, AAwCharacter* Target, FBuffModel Model, int AddStack, float Duration, bool SetToDuration , bool Infinity ){
	this->Caster = Caster;
	this->Target = Target;
	this->Model = Model;
	this->AddStack = AddStack;
	this->Duration = Duration;
	this->SetToDuration = SetToDuration;
	this->PropertyOnAdd = Caster ? Caster->CharacterObj.CurProperty : FChaProp();
	this->Infinity = Infinity;
}

FAddBuffInfo FAddBuffInfo::FromThing(const FThingObj& ThingObj)
{
	FAddBuffInfo Res = FAddBuffInfo();
	FBuffModel BuffModel = UGameplayFuncLib::GetDataManager()->GetBuffModelById(ThingObj.Id);
	Res.Model = BuffModel;
	Res.AddStack = ThingObj.Count;
	// Res.Duration; //TODO:
	// Res.SetToDuration; //TODO:
	// Res.Caster; //TODO:
	// Res.Target; //TODO:
	// Res.PropertyOnAdd; //TODO:
	// Res.Infinity; //TODO:
		
	return Res;
}