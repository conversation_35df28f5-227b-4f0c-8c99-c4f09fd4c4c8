#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/FunctionLibrary/DataFuncLib.h"
#include "TheAwakener_FO/Gameframework/Timeline/TimelineNode.h"
#include "TheAwakener_FO/GamePlay/Characters/Action/ActionInfo.h"
#include "TheAwakener_FO/GamePlay/Characters/ChaProp/ChaProp.h"
#include "TheAwakener_FO/GamePlay/Characters/ControlState/ControlState.h"
#include "TheAwakener_FO/GamePlay/Thing/ThingObj.h"
#include "Buff.generated.h"

class AAwCharacter;

/** @brief buff 静态表数据 */
USTRUCT(BlueprintType)
struct FBuffModel
{
	GENERATED_BODY()
	
public:
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString Id;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FString> Tags;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	uint8 Priority = 255;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float TickTime = 0.f;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int MaxStack = 1;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FChaProp> CharacterPropertyModify;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FControlState ControlState;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool IgnoreDifferentCaster = false;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString PropertyID;
	
	TArray<FJsonFuncData> OnOccur;  //(FBuffObj BuffObj, int32 WasStack, TArray<FString> Params)=>FBuffRunResult
	TArray<FJsonFuncData> OnTick;	//(FBuffObj BuffObj, int32 Ticked, TArray<FString> Params)=>FBuffRunResult
	TArray<FJsonFuncData> OnRemoved; //(FBuffObj BuffObj, bool IsDispelled, TArray<FString> Params)=>FBuffRunResult
	TArray<FJsonFuncData> OnHit;  //(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params)=>FBuffDamageResult
	TArray<FJsonFuncData> OnBeHurt; //(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params)=>FBuffDamageResult
	TArray<FJsonFuncData> OnKill; //(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params)=>FBuffDamageResult
	TArray<FJsonFuncData> OnBeKilled; //(FBuffObj BuffObj, FDamageInfo* DamInfo, AAwCharacter* Target, TArray<FString> Params)=>FBuffDamageResult
	TArray<FJsonFuncData> OnChangeAction;	//(FBuffObj BuffObj, FActionInfo WasAction, TArray<FString> Params)=>FBuffRunResult;
	TArray<FJsonFuncData> OnOffense;		//(FBuffObj BuffObj, AActor* Target, FOffenseInfo UsingOffenseHitTheTarget, TArray<FString> Params)=>FBuffOffenseResult;
	TArray<FJsonFuncData> OnAnimNotfiy; // (FBuffObj BuffObj, int32 WasStack, TArray<FString> Params)=>FBuffRunResult
	TArray<FJsonFuncData> OnAnimNotfiyBegin; // (FBuffObj BuffObj, int32 WasStack, TArray<FString> Params)=>FBuffRunResult
	TArray<FJsonFuncData> OnAnimNotfiyEnd; // (FBuffObj BuffObj, int32 WasStack, TArray<FString> Params)=>FBuffRunResult
	TArray<FJsonFuncData> OnCrit;  //(FBuffObj BuffObj, FDamageInfo DamInfo, AAwCharacter* Target, TArray<FString> Params)=>FBuffDamageResult
	//-------Rogue--------
	TArray<FJsonFuncData> OnRogueGameStart;  // (FBuffObj BuffObj, int32 WasStack, TArray<FString> Params)=>FBuffRunResult
	TArray<FJsonFuncData> OnRogueRoomStart; // (FBuffObj BuffObj, int RoomLevel,ERogueRoomType RoomType, TArray<FString> Params)=>FBuffRunResult
	TArray<FJsonFuncData> OnRogueRoomEnd; // (FBuffObj BuffObj, int RoomLevel,ERogueRoomType RoomType, TArray<FString> Params)=>FBuffRunResult
	TArray<FJsonFuncData> AfterRogueLoadSaveData;//(FBuffObj BuffObj, int32 Ticked, TArray<FString> Params)=>FBuffRunResult
	//------RogueEnd-----
public:
	FBuffModel()
	{
		//[0] is plus [1] is times
		//CharacterPropertyModify.Init(FChaProp(), 2);
		ControlState = FControlState();
	}
	FBuffModel(FString BuffId, TArray<FString> BuffTags, int StackMax = 1, int BuffPriority = 0, float EachTickTime = 0):
		Id(BuffId), Tags(BuffTags), Priority(BuffPriority), TickTime(EachTickTime), MaxStack(StackMax)
	{
		//CharacterPropertyModify.Init(FChaProp(), 2);
		ControlState = FControlState();
	};
	
	static FBuffModel FromJson(TSharedPtr<FJsonObject> JsonObj);
	
	bool ValidBuffModel() const
	{
		return this->Id.IsEmpty() == false;
	}
private:
	void FindPropertyId(TSharedPtr<FJsonObject> JsonObj);
};


/**
 * BuffObj
 */
//class THEAWAKENER_FO_API UBuff : public UAwObject
USTRUCT(BlueprintType)
struct FBuffObj
{
	GENERATED_BODY()
private:
	UPROPERTY()
	int32 Ticked = 0;
	
public:
	// buff静态表数据
	UPROPERTY(BlueprintReadOnly)
	FBuffModel Model = FBuffModel();
		
	// buff的计时当前时间
	UPROPERTY(BlueprintReadWrite)
	float Time = 0;

	// 释放者
	UPROPERTY(BlueprintReadOnly)
	AAwCharacter* Caster = nullptr;
	
	// 携带者
	UPROPERTY(BlueprintReadOnly)
	AAwCharacter* Carrier = nullptr;
	
	// 当前层数
	UPROPERTY(BlueprintReadWrite);
	int Stack = 0;

	//剩余时间
	UPROPERTY(BlueprintReadWrite)
	float Duration = 0;

	//是否无限时间
	UPROPERTY(BlueprintReadWrite)
	bool Infinity = false;

	UPROPERTY()
	FChaProp PropOnCast;

	//一些策划用于逻辑的参数
	UPROPERTY(BlueprintReadWrite)
	TMap<FString, FString> Param;

	//If I should be removed from character buff list this tick
	UPROPERTY(BlueprintReadOnly)
	bool ToBeRemoved = false;

	void ResetTicked()
	{Ticked = 0;};
	/**
	 *初始化一个BuffObj
	 *@param BuffInfo Buff的信息
	 */
	TTuple<FBuffObj, TArray<UTimelineNode*>> Init(FAddBuffInfo BuffInfo);

	/**
	 * 当Buff发生变化时调用
	 * @param AddBuffInfo 改变Buff的信息
	 */
	TTuple<FBuffObj, TArray<UTimelineNode*>> Modify(FAddBuffInfo AddBuffInfo);

	/**
	 * 如果Buff即将被移除，比如剩余时间或者层数小于等于0
	 *  @Param 是否是被魔法之类的玩意儿驱散掉的
	 */
	TTuple<FBuffObj, TArray<UTimelineNode*>> BeforeRemoved(bool Dispelled = false);

	/**
	 * 每个Tick
	 * @param DeltaTime 增加的时间
	 * @param ForceUse 是否强行执行OnTick回调
	 */
	TTuple<FBuffObj, TArray<UTimelineNode*>> OnTick(float DeltaTime, bool ForceUse = false);

	/**
	 *更换动作的时候
	 *@param WasAction  发生动作更换之前的那个动作
	 */
	TTuple<FBuffObj, TArray<UTimelineNode*>> OnActionChange(FActionInfo WasAction);


	bool operator ==(const FBuffObj& Other) const
	{
		return this->Carrier == Other.Carrier &&
			this->Caster == Other.Caster &&
				this->Model.Id == Other.Model.Id &&
					this->Stack == Other.Stack &&
						this->Duration == Other.Duration &&
							this->Time == Other.Time&&
								this->Model.Tags == Other.Model.Tags;
	}

	const bool IsBuffVaild()
	{return Stack>0&&!ToBeRemoved&&(Duration>0||Infinity);}
};

USTRUCT(BlueprintType)
struct FAddBuffInfo
{
	GENERATED_BODY()
public:
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FBuffModel Model;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int AddStack;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float Duration;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool SetToDuration;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	AAwCharacter* Caster;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	AAwCharacter* Target;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FChaProp PropertyOnAdd;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool Infinity = false;
	
	FAddBuffInfo(): AddStack(0), Duration(0), SetToDuration(false), Caster(nullptr), Target(nullptr), Infinity(false){}

	FAddBuffInfo(AAwCharacter* Caster, AAwCharacter* Target, FBuffModel Model, int AddStack, float Duration, bool SetToDuration = false, bool Infinity = false);
	
	static FAddBuffInfo FromThing(const FThingObj& ThingObj);
};



/**
 * Buff在非Damage流程中的返回值
 */
USTRUCT()
struct FBuffRunResult
{
	GENERATED_BODY()
public:
	UPROPERTY()
	FBuffObj BuffObj;

	UPROPERTY()
	UTimelineNode* TimelineNode = nullptr;

	FBuffRunResult(){};
	FBuffRunResult(FBuffObj Buff, UTimelineNode* Node = nullptr):
		BuffObj(Buff), TimelineNode(Node){};
};

/**
 * Buff在Offense流程中的返回值
 */
USTRUCT()
struct FBuffOffenseResult
{
	GENERATED_BODY()
public:
	UPROPERTY()
	FBuffObj BuffObj;		//运行的BuffObj，会被这个改写

	UPROPERTY()
	FOffenseInfo OffenseInfo;		//BeOffense流程中的OffenseInfo，会被这个改写

	UPROPERTY()
	UTimelineNode* TimelineNode = nullptr;		//要播放的TimelineNode

	FBuffOffenseResult(){}
	FBuffOffenseResult(FBuffObj Buff, FOffenseInfo OInfo, UTimelineNode* Node = nullptr):
		BuffObj(Buff), OffenseInfo(OInfo), TimelineNode(Node){};
};

/**
 * 
 */
USTRUCT(BlueprintType)
struct FBuffChecker
{
	GENERATED_BODY()
public:
	
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString Id;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int Stack;

	FBuffChecker():Id(""),Stack(0){};
	FBuffChecker(FString BuffId, int BuffStack)
	{
		Id = BuffId;
		Stack = BuffStack;
	};
};

/**
 * Buff在Damage流程中返回的东西
 * 因为循环引用的傻逼问题，只能放在这里，不能放在Buff.h
 */
USTRUCT()
struct FBuffDamageResult
{
	GENERATED_BODY();
public:
	UPROPERTY()
	FBuffObj BuffObj;

	UPROPERTY()
	FDamageInfo DamageInfo;

	UPROPERTY()
	UTimelineNode* TimelineNode;

	FBuffDamageResult():BuffObj(FBuffObj()), DamageInfo(FDamageInfo()), TimelineNode(nullptr){};
	FBuffDamageResult(FBuffObj Buff, FDamageInfo DamInfo, UTimelineNode* Timeline):
		BuffObj(Buff), DamageInfo(DamInfo), TimelineNode(Timeline){};
};
