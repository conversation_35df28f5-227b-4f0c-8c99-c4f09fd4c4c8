// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Buff.h"
#include "UObject/Object.h"
#include "BuffManager.generated.h"

/**
 * Buff的一些静态函数库
 */
UCLASS()
class THEAWAKENER_FO_API UBuffManager : public UObject
{
	GENERATED_BODY()
public:
	/**
	 * 给角色添加一个Buff
	 * @param BuffInfo 添加buff的信息
	 * @return 添加在Buff携带者CharacterInfo的Buff的下标
	 */ 
	UFUNCTION(BlueprintCallable)
	static int AddBuff(FAddBuffInfo BuffInfo);
	UFUNCTION(BlueprintCallable)
	static void ProcessBuffs(const TArray<FString>& RelicEffectBuffs, AAwCharacter* PlayerCharacter,FString ForceTag, bool IsAdd);
	/**
	 * 改变角色buff的层数
	 * @param Buff 指向Buff的指针
	 * @param Stack 要改变的层数
	 * @param SetTo 是否是设置为，如果不是，就是加上
	 */
	static void ModifyBuffStack(FBuffObj* Buff, int Stack, bool SetTo = false);

	/**
	 * 改变角色buff的持续时间
	*/
	static void RefreshBuff(FBuffObj* Buff,float Duration, bool SetTo = true);
	
	static bool CheckRemoveBuff(FBuffObj& Buff);

	static void RemoveBuff(FBuffObj& Buff,bool IsDispelled = false);


};
