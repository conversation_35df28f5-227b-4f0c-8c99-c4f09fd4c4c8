// Fill out your copyright notice in the Description page of Project Settings.


#include "BuffManager.h"

#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"

int UBuffManager::AddBuff(FAddBuffInfo BuffInfo)
{
	int BuffIndex = -1;
	if (!BuffInfo.Target)
		return BuffIndex;
	
	for (int i = 0; i < BuffInfo.Target->CharacterObj.Buff.Num(); i++)
	{
		bool SameCaster =BuffInfo.Model.IgnoreDifferentCaster?true:BuffInfo.Target->CharacterObj.Buff[i].Caster == BuffInfo.Caster; 
		if (
			SameCaster&&
			BuffInfo.Target->CharacterObj.Buff[i].Model.Id == BuffInfo.Model.Id &&
			BuffInfo.Target->CharacterObj.Buff[i].Model.Tags == BuffInfo.Model.Tags&&
			BuffInfo.Target->CharacterObj.Buff[i].ToBeRemoved == false
		)
		{
			BuffIndex = i;
			break;
		}
	}
	
	if (BuffIndex < 0)
	{
		//没有老的
		FBuffObj NewBuff = FBuffObj();
		NewBuff.Model = BuffInfo.Model;
		NewBuff.Carrier = BuffInfo.Target;
		NewBuff.Caster = BuffInfo.Caster;
		NewBuff.Duration = BuffInfo.Duration;
		NewBuff.Infinity = BuffInfo.Infinity;
		NewBuff.Stack = BuffInfo.AddStack;
		NewBuff.PropOnCast = BuffInfo.Caster ? BuffInfo.Caster->CharacterObj.CurProperty : BuffInfo.PropertyOnAdd;

		//无效新Buff不应成功添加
		if (!NewBuff.IsBuffVaild())
		{
			return  -1;
		}
		
		for (int i = 0; i < BuffInfo.Model.OnOccur.Num(); i++)
		{
			UFunction* Func = UCallFuncLib::JsonFuncToUFunc(BuffInfo.Model.OnOccur[i]);
			if (IsValid(Func) == false) continue;;
				
			struct {
				FBuffObj BuffObj;
				int32 WasStack;
				TArray<FString> Params;

				FBuffRunResult Result;
			} FuncParam;
				
			FuncParam.BuffObj = NewBuff;
			FuncParam.WasStack = 0;
			FuncParam.Params = BuffInfo.Model.OnOccur[i].Params;
				
			BuffInfo.Target->ProcessEvent(Func, &FuncParam);
			NewBuff = FuncParam.Result.BuffObj;
			if (FuncParam.Result.TimelineNode)
				UGameplayFuncLib::GetTimelineManager()->AddNode(FuncParam.Result.TimelineNode);
		}

		BuffInfo.Target->CharacterObj.Buff.Add(NewBuff);
		return BuffInfo.Target->CharacterObj.Buff.Find(NewBuff);
	}else
	{
		//存在老的
		//层数夹取叠加
		const int WasStack = BuffInfo.Target->CharacterObj.Buff[BuffIndex].Stack;
		BuffInfo.Target->CharacterObj.Buff[BuffIndex].Stack += BuffInfo.AddStack;
		BuffInfo.Target->CharacterObj.Buff[BuffIndex].Stack = FMath::Min(
			BuffInfo.Target->CharacterObj.Buff[BuffIndex].Stack,
			BuffInfo.Target->CharacterObj.Buff[BuffIndex].Model.MaxStack
			);
		//持续时间覆盖或叠加 
		BuffInfo.Target->CharacterObj.Buff[BuffIndex].Infinity = BuffInfo.Infinity;
		BuffInfo.Target->CharacterObj.Buff[BuffIndex].Duration =
			BuffInfo.SetToDuration ? BuffInfo.Duration : (BuffInfo.Target->CharacterObj.Buff[BuffIndex].Duration + BuffInfo.Duration);

		if(!CheckRemoveBuff(BuffInfo.Target->CharacterObj.Buff[BuffIndex]))
		{
			//还有时间或者层数
			for (int i = 0; i < BuffInfo.Target->CharacterObj.Buff[BuffIndex].Model.OnOccur.Num(); i++)
			{
				UFunction* Func = UCallFuncLib::JsonFuncToUFunc(BuffInfo.Target->CharacterObj.Buff[BuffIndex].Model.OnOccur[i]);
				if (IsValid(Func) == false) continue;;
				
				struct {
					FBuffObj BuffObj;
					int32 WasStack;
					TArray<FString> Params;

					FBuffRunResult Result;
				} FuncParam;
				
				FuncParam.BuffObj = BuffInfo.Target->CharacterObj.Buff[BuffIndex];
				FuncParam.WasStack = WasStack;
				FuncParam.Params = BuffInfo.Target->CharacterObj.Buff[BuffIndex].Model.OnOccur[i].Params;
				
				BuffInfo.Target->ProcessEvent(Func, &FuncParam);
				BuffInfo.Target->CharacterObj.Buff[BuffIndex] = FuncParam.Result.BuffObj;
				if (FuncParam.Result.TimelineNode)
					UGameplayFuncLib::GetTimelineManager()->AddNode(FuncParam.Result.TimelineNode);
			}
		}else
		{
			//准备删除了
			RemoveBuff(BuffInfo.Target->CharacterObj.Buff[BuffIndex]);
		}
		return BuffIndex;
	}

	return -1;
	
	if (!BuffInfo.Target) return -1;
	TArray<AAwCharacter*> BuffCasters;
	if (BuffInfo.Caster != nullptr) BuffCasters.Add(BuffInfo.Caster);
	TArray<int> HasBuffIndex = BuffInfo.Target->GetBuffIndexes(BuffInfo.Model.Id, BuffCasters);
	
	if (HasBuffIndex.Num() > 0)
	{
		const int BIndex = HasBuffIndex[0];
		//modify old one
		TTuple<FBuffObj, TArray<UTimelineNode*>> BuffRunResult = BuffInfo.Target->CharacterObj.Buff[BIndex].Modify(BuffInfo) ;//   HasBuff[0]->Modify(BuffInfo);
		BuffInfo.Target->CharacterObj.Buff[BIndex] = BuffRunResult.Get<0>();
		if (BuffRunResult.Get<1>().Num())
		{
			for (int i = 0; i < BuffRunResult.Get<1>().Num(); i++)
			{
				UGameplayFuncLib::GetTimelineManager()->AddNode(BuffRunResult.Get<1>()[i]);
			}
		}
		BuffInfo.Target->AttrRecheck();
		return BIndex;
	}

	if (BuffInfo.AddStack > 0)
	{
		//create new one
		FBuffObj NewBuff = FBuffObj();
		TTuple<FBuffObj, TArray<UTimelineNode*>> BuffRunResult = NewBuff.Init(BuffInfo);
		
		if (BuffRunResult.Get<1>().Num())
		{
			for (int i = 0; i < BuffRunResult.Get<1>().Num(); i++)
			{
				UGameplayFuncLib::GetTimelineManager()->AddNode(BuffRunResult.Get<1>()[i]);
			}
		}
		BuffInfo.Target->CharacterObj.Buff.Add(NewBuff);
		BuffInfo.Target->CharacterObj.Buff.Sort([](const FBuffObj BuffA, const FBuffObj BuffB){
			return BuffA.Model.Priority < BuffB.Model.Priority;
		});
		BuffInfo.Target->AttrRecheck();
		return BuffInfo.Target->CharacterObj.Buff.Num() - 1;
	}
	UKismetSystemLibrary::PrintString(BuffInfo.Target, FString("Can't add buff cuz AddStack <= 0 or Duration <= 0 while no old ones found."),
		true,true, FLinearColor::Red, 30);
	return -1;
}

void UBuffManager::ProcessBuffs(const TArray<FString>& RelicEffectBuffs, AAwCharacter* PlayerCharacter,
	FString ForceTag, bool IsAdd)
{
	int StackMultiplier = IsAdd ? 1 : -1;
	for (auto BuffId : RelicEffectBuffs)
	{
		int BuffStack = 1;
		if (BuffId.Contains("(") && BuffId.Contains(")"))
		{
			FString Left, Right;
			FString StackString = UDataFuncLib::SplitParamBetweenSplitChar(BuffId, "(", ")", Left, Right);
			BuffStack = StackString.IsNumeric() ? FCString::Atoi(*StackString) : BuffStack;
			BuffId = Left;
		}

		FBuffModel BuffModel = UGameplayFuncLib::GetAwDataManager()->GetBuffModelById(BuffId);
		//Buff的Tag加入Relic标识
		if (!ForceTag.IsEmpty() && !BuffModel.Tags.Contains(ForceTag))
		{
			BuffModel.Tags.Add(ForceTag);
		}

		if (BuffModel.ValidBuffModel() == false)
		{
			return;
		}

		PlayerCharacter->AddBuff(FAddBuffInfo(PlayerCharacter, PlayerCharacter, BuffModel, BuffStack * StackMultiplier, 0, false, true));
	}
}

void UBuffManager::ModifyBuffStack(FBuffObj* Buff, int Stack, bool SetTo)
{
	if (!Buff || !Buff->Carrier) return;

	const int AddStack = SetTo ? (Stack - Buff->Stack) : Stack;
	const FAddBuffInfo BInfo = FAddBuffInfo(
		Buff->Caster, Buff->Carrier, Buff->Model, AddStack, Buff->Duration, true, Buff->Infinity 
	);
	
	TTuple<FBuffObj, TArray<UTimelineNode*>> BuffRunResult = Buff->Modify(BInfo) ;//   HasBuff[0]->Modify(BuffInfo);
	
	if (BuffRunResult.Get<1>().Num())
	{
		for (int i = 0; i < BuffRunResult.Get<1>().Num(); i++)
		{
			UGameplayFuncLib::GetTimelineManager()->AddNode(BuffRunResult.Get<1>()[i]);
		}
	}
	Buff->Carrier->AttrRecheck();
	if (CheckRemoveBuff(*Buff))
	{
		RemoveBuff(*Buff);
	}
}

void UBuffManager::RefreshBuff(FBuffObj* Buff, float Duration, bool SetTo)
{
	if (!Buff || !Buff->Carrier) return;

	const float NewDuration = SetTo ? Duration : (Buff->Duration + Duration);
	
	const FAddBuffInfo BInfo = FAddBuffInfo(
		Buff->Caster, Buff->Carrier, Buff->Model, 0, NewDuration, true, Buff->Infinity 
	);
	
	TTuple<FBuffObj, TArray<UTimelineNode*>> BuffRunResult = Buff->Modify(BInfo) ;//   HasBuff[0]->Modify(BuffInfo);
	Buff->Carrier->AttrRecheck();
	if (CheckRemoveBuff(*Buff))
	{
		RemoveBuff(*Buff);
	}
}

bool UBuffManager::CheckRemoveBuff(FBuffObj& Buff)
{
	if (Buff.Stack > 0 &&
		(Buff.Duration > 0 || Buff.Infinity == true))
		{
			return false;
		}
	Buff.ToBeRemoved = true;
	return  true;
}

void UBuffManager::RemoveBuff(FBuffObj &Buff,bool IsDispelled)
{
	if (Buff.ToBeRemoved)
	{
		return;
	}
	Buff.ToBeRemoved = true;
	TTuple<FBuffObj, TArray<UTimelineNode*>> Res;
	Res = Buff.BeforeRemoved(IsDispelled);
	if (!Res.Value.IsEmpty())
	{
		for (auto TimelineNode :Res.Value)
		{
			UGameplayFuncLib::GetTimelineManager()->AddNode(TimelineNode);	
		}
	}
}



