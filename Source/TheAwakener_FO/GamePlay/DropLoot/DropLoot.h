#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/FunctionLibrary/DataFuncLib.h"
#include "DropLoot.generated.h"


UENUM(BlueprintType)
enum class EDropLootType : uint8
{
	PotionNumRecover,
	PotionNum,
	PotionQuality,
	Coin,
	Exp,
	Key,
	Shard,
	Soul,
	Magicltem,
	Relic,
	Action,
	ItemInstanceBox,
};

USTRUCT(BlueprintType)
struct FDropLootInfo
{
	GENERATED_BODY()
public:
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	EDropLootType Type;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float DropRate;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int NumberMin;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int NumberMax;

	static FDropLootInfo FromJson(TSharedPtr<FJsonObject> JsonObj)
	{
		FDropLootInfo Res = FDropLootInfo();

		Res.Type = UDataFuncLib::AwGetEnumField<EDropLootType>(JsonObj, "Type", EDropLootType::Coin);
		Res.DropRate = UDataFuncLib::AwGetNumberField(JsonObj, "DropRate", 0.0);
		Res.NumberMin = UDataFuncLib::AwGetNumberField(JsonObj, "NumberMin", 1);
		Res.NumberMax = UDataFuncLib::AwGetNumberField(JsonObj, "NumberMax", 1);

		return Res;
	}
};

USTRUCT(BlueprintType)
struct FMobDropLootInfo
{
	GENERATED_BODY()
public:
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString MobId;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float BaseIIR;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FDropLootInfo> Drops;

	static FMobDropLootInfo FromJson(TSharedPtr<FJsonObject> JsonObj)
	{
		FMobDropLootInfo Res = FMobDropLootInfo();

		Res.MobId = UDataFuncLib::AwGetStringField(JsonObj, "MobId");
		for (const TSharedPtr<FJsonValue, ESPMode::ThreadSafe> Loot : UDataFuncLib::AwGetArrayField(JsonObj, "Drops"))
			Res.Drops.Add(FDropLootInfo::FromJson(Loot->AsObject()));
		Res.BaseIIR = UDataFuncLib::AwGetNumberField(JsonObj, "BaseIIR", 1);
		return Res;
	}
};