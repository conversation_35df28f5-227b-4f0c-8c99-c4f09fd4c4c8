// The copyright of this project belongs to Shanghai Minghuan Software Technology Co., Ltd

#pragma once

#include "CoreMinimal.h"
#include "MechanicalActor.h"
#include "TheAwakener_FO/DesignerScript/Bullet/BulletScript.h"
#include "TheAwakener_FO/GamePlay/Characters/Battle/BattleStructs.h"
#include "Traits/TrCommand.h"
#include "MechanicRunner.generated.h"


UCLASS()
class THEAWAKENER_FO_API AMechanicRunner : public AMechanicalActor
{
	GENERATED_BODY()
			/**
	 * The steady update time interval.
	 */
	// UPROPERTY(EditAnywhere, Category = Mechanism,
	// 		  Meta = (AllowPrivateAccess = "true"))
	// float SteadyDeltaTime = IMechanical_DefaultSteadyDeltaTime;

	friend class UDetail;
	friend class UBelt;
	friend class ISubjective;
	FHitLogPlayer HitlogP1;
	FHitLogPlayer HitlogP2;
public:	
	// Sets default values for this actor's properties
	AMechanicRunner();
	static int ThreadsCount;
	/**
	 * The number of entities to batch as part of a separate thread.
	 */
	static int32 BaseBatchSize;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	float TimeMultiplier = 30;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	float InAirTolerance = 20;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	float InAirTimeTolerance = 0.5f;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TMap<FString,EDamageCalculationType> DamageCalculationTypeMap;
	/**
	 * Get the time of the last processed steady frame.
	 */
	OPTIONAL_FORCEINLINE float
	GetProcessedSteadyTime() const
	{
		return IMechanical::GetProcessedSteadyTime_Implementation();
	}

	/**
	 * The current ratio within the steady frame.
	 * 
	 * This is in relation between the previous steady
	 * frame and the current next one.
	 * Should be used for interframe interpolation.
	 */
	OPTIONAL_FORCEINLINE float
	CalcSteadyFrameRatio() const
	{
		return IMechanical::CalcSteadyFrameRatio_Implementation();
	}

	/**
	 * The current steady frame.
	 */
	OPTIONAL_FORCEINLINE int64
	GetSteadyFrame() const
	{
		return SteadyFrame;
	}

	/**
	 * The total steady time elapsed.
	 */
	OPTIONAL_FORCEINLINE float
	GetSteadyTime() const
	{
		return IMechanical::GetSteadyTime_Implementation();
	}

	/**
	 * The current steady future factor.
	 * 
	 * This is in relation between the previous change time
	 * delta to the next steady frame change delta time.
	 */
	OPTIONAL_FORCEINLINE float
	CalcSteadyFutureFactor() const
	{
		return IMechanical::CalcSteadyFutureFactor_Implementation();
	}
	 protected:

	/**
	 * Begin executing the mechanism.
	 */
	virtual void BeginPlay() override;

	/**
	 * End executing the mechanism.
	 */
	virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;

  public:
	virtual float
	GetOwnTime() const override
	{
		return GetGameTimeSinceCreation();
	}

	virtual void Tick(float DeltaTime) override;
	float GetDistanceToGround(const FVector& StartLocation, float TraceDistance,float &GroundZ,bool &IsHit) const;

	UFUNCTION(BlueprintCallable)
	void CleanAllECSMonster();

	UFUNCTION()
	int LogPlayerHit(AAwCharacter* Character,FActionChangeInfo ActionChangeInfo,FAwForceFeedBackInfo FeedBackInfo,bool IsBullet);
	UFUNCTION()
	//处理命中动作切换和顿帧
	void DealPlayerHit(const FHitLogPlayer& Hitlog,const int& SafeHit);
	void DealAllPlayerHit(const int& SafeHitP1,const int& SafeHitP2,const int& SafeKill);

	// 获取单例实例
	static AMechanicRunner* GetInstance() { return Instance; }

private:
	static AMechanicRunner* Instance;
};

OPTIONAL_FORCEINLINE AMechanicRunner::AMechanicRunner()
{
	PrimaryActorTick.bCanEverTick = true;
}

OPTIONAL_FORCEINLINE void AMechanicRunner::BeginPlay()
{
	Super::BeginPlay();
	DoRegister();
	Instance = this;
}

OPTIONAL_FORCEINLINE void AMechanicRunner::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
	DoUnregister();
	Super::EndPlay(EndPlayReason);
}
