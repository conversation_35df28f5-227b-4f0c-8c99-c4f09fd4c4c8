// The copyright of this project belongs to Shanghai Minghuan Software Technology Co., Ltd

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "ECSStructs.generated.h"

USTRUCT(BlueprintType)
struct THEAWAKENER_FO_API FMonsterLog
{
	GENERATED_BODY()
 
  public:

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FString GroupName;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    TArray<AAwCharacter*> Monsters;
};
