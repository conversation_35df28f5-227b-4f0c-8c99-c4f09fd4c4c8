// The copyright of this project belongs to Shanghai Minghuan Software Technology Co., Ltd

#include "MechanicRunner.h"

#include "CoreMinimal.h"
#include "BubbleCage.h"
#include "ECSDamageLib.h"
#include "EnemySpawner.h"
#include "MonsterReplaceMgr.h"
#include "GameFramework/Actor.h"
#include "TheAwakener_FO/DesignerScript/Bullet/BulletScript.h"
#include "TheAwakener_FO/Optimization/StatsDeclare.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "Traits/Attacks.h"
#include "Traits/Enemy.h"
#include "Traits/Hit.h"
#include "Traits/Speed.h"
#include "Traits/TrAnimation.h"
#include "Traits/TrCommand.h"

int AMechanicRunner::ThreadsCount = 4;
int32 AMechanicRunner::BaseBatchSize = 128;
AMechanicRunner* AMechanicRunner::Instance = nullptr; 
FVector MoveTowards(FVector Current, FVector Target, float MaxDelta)
{
    FVector Delta = Target - Current;
    float Distance = Delta.Size();

    if (Distance <= MaxDelta || Distance < KINDA_SMALL_NUMBER)
        return Target;

    return Current + Delta / Distance * MaxDelta;
}
void AMechanicRunner::Tick(float DeltaTime)
{
	DoTick(GetGameTimeSinceCreation(),
		DeltaTime,
		GetSteadyDeltaTime());
	const auto Mechanism = GetMechanism();
	const double WorldTimeSeconds = GetWorld()->GetTimeSeconds();
	// const auto EnemySpawner = AEnemySpawner::GetInstance();
        		// Super::Tick(DeltaTime);
        		// {
        		// 	SCOPE_CYCLE_COUNTER(STAT_SurvivorRenderState);
        		// 	EnemySpawner->UpdateRenderState([&]()
        		// 	// General after-spawn initialization.
        		// 	{
        		// 		SCOPE_CYCLE_COUNTER(STAT_SurvivorAfterSpawn);
        		// 		static TArray<float> ResetData({0, 0, 1, 1});
        		// 		Mechanism->Operate<FUnsafeChain>(
        		// 		[=](FUnsafeSubjectHandle Subject, const FJustSpawned&, const FRendering& Rendering, const FBubbleSphere BubbleSphere)
        		// 		{
        		// 			// ResetData[MaterialUvScaleDataIndex] = BubbleSphere.Radius / 200;
        		// 			// Rendering.Owner->SetCustomData(Rendering.InstanceId, ResetData);
        		// 			Subject.RemoveTraitDeferred<FJustSpawned>();
        		// 			Subject.SetTraitDeferred(FAppearing{});
        		// 			Subject.SetTraitDeferred(FMove{});
        		// 		});
        		// 	});
        		// }
	// if( UAwDataManager::ConfigValue("GameConfig","TestConfig","bShowECSAttackBox")==FString("True"))
	// {
	// 	Mechanism->OperateConcurrently(
	// 		[=](FSolidSubjectHandle Subject,FBubbleSphere& Sphere,FEnemy& Enemy,FLocated& Located)
	// 		{
	// 			const auto Hit = Subject.GetTraitPtr<FAC_Hit, EParadigm::Unsafe>();
	// 			DrawDebugSphere(GetWorld(),Located.Location + FVector(0,0,Sphere.Radius),Sphere.Radius,10,Hit != nullptr ? FColor::White : FColor::Red);
	// 		},ThreadsCount,BaseBatchSize);
	// }
	// Decouple
	{
		SCOPE_CYCLE_COUNTER(STAT_SurvivorDecouple);
		ABubbleCage::Evaluate();
	}
	// //计算连杀
	// {
	// 	auto ScoreManager = UGameplayFuncLib::GetScoreManager();
	// 	// ScoreManager->AddCombo()
	// //计算连杀
	// 	// ScoreManager->AddCombo()
	// 	// machenics
	// 	std::atomic<int32> SafeHit{0};
	// 	std::atomic<int32> SafeKill{0};
	// 	Mechanism->OperateConcurrently(
	// 		[&](FSolidSubjectHandle Subject,FEnemy& Enemy,FAC_Hit* Hitted,FJustDead* Dead)
	// 		{
	// 			if (Hitted)
	// 			{
	// 				SafeHit.fetch_add(1, std::memory_order_relaxed);
	// 			}
	// 			if (Dead)
	// 			{
	// 				SafeKill.fetch_add(1, std::memory_order_relaxed);
	// 			}
	// 		},ThreadsCount,BaseBatchSize);
	// 	if (SafeHit>0)
	// 		ScoreManager->AddCombo(SafeHit,3);
	// 	if (SafeKill>0)
	// 		ScoreManager->AddKillCombo(SafeKill,3);
		
	// }
		// if (IsDirt)
	// 	{
	// 		IsDirt = false;
	// 		SCOPE_CYCLE_COUNTER(STAT_SurvivorRenderState);
	// 		const auto EnemySpawner = AEnemySpawner::GetInstance();
	// 		EnemySpawner->UpdateRenderState([&]()
	// 		// General after-spawn initialization.
	// 		{
	// 			SCOPE_CYCLE_COUNTER(STAT_SurvivorAfterSpawn);
	// 			// static TArray<float> ResetData({0, 0, 1, 1});
	// 			// Mechanism->Operate<FUnsafeChain>(
	// 			// [=](FUnsafeSubjectHandle Subject, const FJustSpawned&, const FRendering& Rendering, const FBubbleSphere BubbleSphere)
	// 			// {
	// 			// 	ResetData[MaterialUvScaleDataIndex] = BubbleSphere.Radius / 200;
	// 			// 	Rendering.Owner->SetCustomData(Rendering.InstanceId, ResetData);
	// 			// 	Subject.RemoveTrait<FJustSpawned>();
	// 			// 	Subject.SetTrait(FAppearing{});
	// 			// 	Subject.SetTrait(FMove{});
	// 			// });
	// 		});
	// }
	// float g;
	// bool b;
	// GetDistanceToGround(FVector{-65.97,0.21,-17.47},300,g,b);
	//Attack 攻击
	{
		std::atomic<int32> SafeHitP1{0};
		std::atomic<int32> SafeKillP1{0};
		std::atomic<int32> SafeHitP2{0};
		HitlogP1.Reset();
		HitlogP2.Reset();
		Mechanism->Operate([&](FTrAttackBox& ECSAttackBox,FLocated& Located,FTrAttackEnable& Enable)
		{
			SCOPE_CYCLE_COUNTER(STAT_SurvivorHit);
			if (!ECSAttackBox.bIsECSPositionSynced)return;
			ECSAttackBox.bIsECSPositionSynced = false;
			//绑定时处理
			// if (!ECSAttackBox.Attacker->IsPlayerCharacter())return;
			FOffenseInfo OffenseInfo;
			FDamageInfo DInfo;

			const auto EnemyFilter = FFilter::Make<FEnemy>();
			TArray<FSubjectHandle> Overlappers;
			// if (ECSAttackBox.BoxType== EAttackBoxType::Bullet)
			// {
			// 	Overlappers= ABubbleCage::GetOverlapping(Located.Location, ECSAttackBox.Radius, EnemyFilter);
			// }
			// else
			if (ECSAttackBox.HitBox)
			{
				float Radius = FMath::Max(ECSAttackBox.HitBox->GetScaledCapsuleRadius(),ECSAttackBox.HitBox->GetScaledCapsuleHalfHeight());
				Overlappers= ABubbleCage::GetOverlapping(ECSAttackBox.HitBox->GetComponentLocation(), Radius, EnemyFilter);
			}
			if( UAwDataManager::ConfigValue("GameConfig","TestConfig","bShowECSAttackBox")==FString("True"))
			{
				DrawDebugSphere(GetWorld(),Located.Location,ECSAttackBox.Radius,10,FColor::White);
			}
			for (auto& Overlapper : Overlappers)
			{
				bool IsHit = false;
				bool IsDead = false;
				bool IsBullet = false;
				auto TargetLocation = Overlapper.GetTraitPtr<FLocated, EParadigm::Unsafe>()->Location;
				if (ECSAttackBox.BoxType == EAttackBoxType::Melee)
				{
					auto CurrentActionHitInfo = ECSAttackBox.Attacker->CurrentActionHitInfo.Num()>0?ECSAttackBox.Attacker->CurrentActionHitInfo[0]:FOffenseInfo();
					OffenseInfo = ECSAttackBox.Attacker->GetOffenInfoWithWeaponPower(CurrentActionHitInfo,TargetLocation);
					UECSDamageLib::TryHitEnemy(Overlapper,ECSAttackBox.Attacker,ECSAttackBox.AttackFlag,OffenseInfo,DInfo,TargetLocation,IsHit,IsDead);
				}
				// else if(ECSAttackBox.BoxType == EAttackBoxType::Bullet)
				// {
				// 	IsBullet = true;
				// 	auto Model = FBulletModelPool::Get(ECSAttackBox.BulletModelKey);
				// 	for (auto OnHitItem : Model.OnHit)
				// 	{
				// 		EDamageCalculationType DamageType = DamageCalculationTypeMap.FindRef(OnHitItem.FunctionName);
				// 		FBeCaughtActorInfo CaughtInfo;
				// 		if (OnHitItem.FunctionName.Equals("DealDamageOnHit"))
				// 		{
				// 			CaughtInfo.BeCaughtSubject = Overlapper;
				// 			OffenseInfo = UBulletScript::GetOffenseInfo(ECSAttackBox.Bullet,CaughtInfo,DamageType,OnHitItem.Params);
				// 		}
				// 	}
				// 	UECSDamageLib::TryHitEnemy(Overlapper,ECSAttackBox,OffenseInfo,DInfo,TargetLocation,WorldTimeSeconds,IsHit,IsDead);
				// }

				auto Sphere = Overlapper.GetTraitPtr<FBubbleSphere, EParadigm::Unsafe>();
				if( UAwDataManager::ConfigValue("GameConfig","TestConfig","bShowECSAttackBox")==FString("True"))
				{
					DrawDebugSphere(GetWorld(),TargetLocation + FVector(0,0,Sphere->Radius),Sphere->Radius,10,IsHit ? FColor::Red : FColor::White);
				}
				if (IsHit)
				{
					UECSDamageLib::ProcessECSHitLogic(ECSAttackBox.Attacker,DInfo,OffenseInfo,IsBullet,SafeHitP1,SafeHitP2);
					UE_LOG(LogTemp,Log,TEXT("Hitted %i"),Overlapper.GetId());
				}
				if (IsDead)
				{
					UECSDamageLib::ProcessECSKillLogic(ECSAttackBox.Attacker,DInfo,SafeKillP1);
				}
			}
		});
		DealAllPlayerHit(SafeHitP1,SafeHitP2,SafeKillP1);
	}
	//避免卡顿时造成大量唯一和多余计算
	float ECSDeltaTimeMove = FMath::Min(DeltaTime,0.0333f);
	
	//Movement
	{
		SCOPE_CYCLE_COUNTER(STAT_SurvivorMovement);
		Mechanism->OperateConcurrently([&](FSolidSubjectHandle Subject,FLocated& Locate,FForceMoveInfo& ForceMovement,FTrSpeed& Speed,FTrGravity& Gravity,FTrGroundCheck& GroundCheck,FTrAnimationState& AnimState)
		{
			FVector DeltaLocation = FVector::ZeroVector;
			if (Subject.HasTrait<FAC_AirLag>())
			{
				return;
			}
			//Gravity
			if (Subject.HasTrait<FAS_InAir>()&&!ForceMovement.Active)
			{
				// keep vertical speed larger than G * 1s,prevent pass 6 meter in 3 frame
				// 6 meter (Ground check length)
				// 3 frame (Ground check rate)  
				Speed.Value.Z = FMath::Max(Speed.Value.Z - Gravity.Value * ECSDeltaTimeMove, -Gravity.Value);
				DeltaLocation += Speed.Value * ECSDeltaTimeMove;
			}
			//Force Move
			if (ForceMovement.Active)
			{
				SCOPE_CYCLE_COUNTER(STAT_SurvivorForceMovement);
				FVector Velocity = ForceMovement.ThisTickVelocity(ECSDeltaTimeMove);
				DeltaLocation += Velocity;
				ForceMovement.TimeElapsed += ECSDeltaTimeMove;
				if (ForceMovement.TimeElapsed >= ForceMovement.InSec)
				{
					ForceMovement.Active = false;
				}
			}
			bool Islanded = false;
			float GroundZ = 0.0f;
			bool IsHit = false;
			GroundCheck.cooldown--;
			if (GroundCheck.cooldown<=0)
			{
				SCOPE_CYCLE_COUNTER(STAT_SurvivorGroundCheck);
				GroundCheck.cooldown = FTrGroundCheck::CooldownMax;
				float Distance = GetDistanceToGround(Locate.Location,300,GroundZ,IsHit);
				//离地时
				if (!IsHit || FMath::Abs(Distance)> InAirTolerance)
				{
					if (!Subject.HasTrait<FAS_InAir>())
					{
						//如果原本有循环动画，则给与结束时间，以便触发切换
						if (AnimState.EndTime<0){
							AnimState.EndTime = WorldTimeSeconds;
						}
						Subject.SetTraitDeferred(FAS_InAir());
						Subject.RemoveTraitDeferred<FAS_OnGround>();
						UE_LOG(LogTemp,Log,TEXT("Air at %f"),WorldTimeSeconds);
					}
				}
				//落地或穿到地下
				if (IsHit
					&& DeltaLocation.Z<=0
					&&(FMath::Abs(Distance) < InAirTolerance	//几乎没有离地
						|| Distance > DeltaLocation.Z))// 这个瞬间从地上传到地下
				{
					Speed.Value.Z = 0;
					//避免瞬间进入空中也触发落地
					FAS_InAir* InAir = Subject.GetTraitPtr<FAS_InAir, EParadigm::Unsafe>();
					if (InAir && InAir->Duration > InAirTimeTolerance)
					{
						UE_LOG(LogTemp,Log,TEXT("Landed at %f"),WorldTimeSeconds);
						Subject.SetTraitDeferred(FAC_Landed());
						Subject.RemoveTraitDeferred<FAS_InAir>();
					}
					Islanded = true;
					// if (!Subject.HasTrait<FAS_OnGround>())
					// {
					// 	AnimState.EndTime = WorldTimeSeconds;
					// }
					Subject.SetTraitDeferred(FAS_OnGround{GroundZ});
				}
			}
			//因为deferredSetting，不会马上拿到落地状态
			if (Islanded && !ForceMovement.Active)
			{
				Locate = Locate.Location+DeltaLocation;
				Locate.Location.Z = GroundZ;
			}else
			{
				Locate = Locate.Location+DeltaLocation;
			}
		},ThreadsCount,BaseBatchSize);
	}
	Mechanism->OperateConcurrently([&](FSolidSubjectHandle Subject,FAC_AirLag& AirLag)
	{
		AirLag.Duration -= ECSDeltaTimeMove;
		if (AirLag.Duration <= 0)
		{
			Subject.RemoveTraitDeferred<FAC_AirLag>();
		}
	},ThreadsCount,BaseBatchSize);
	Mechanism->OperateConcurrently([&](FSolidSubjectHandle Subject,FAS_InAir& Air)
	{
		Air.Duration += ECSDeltaTimeMove;
	},ThreadsCount,BaseBatchSize);
	Mechanism->OperateConcurrently([&](FSolidSubjectHandle Subject,FHitStun& HitStun)
	{
		HitStun.Time -= ECSDeltaTimeMove;
		if (HitStun.Time <= 0)
		{
			Subject.RemoveTraitDeferred<FHitStun>();
		}
	},ThreadsCount,BaseBatchSize);
	Mechanism->OperateConcurrently([&](FTr_HitLog& HitLog)
	{
		for (auto KV : HitLog.Pool)
		{
			if(HitLog.Pool[KV.Key].Enabled)
			{
				HitLog.Pool[KV.Key].Duration -= DeltaTime;
			}
		}
	},ThreadsCount,BaseBatchSize);
	// auto Filter = FFilter::Make<FEnemy>(EFlagmarkBit::C);
	// Mechanism->Operate(Filter,[&](FSolidSubjectHandle Subject)
	// {
	// 	AEnemySpawner::Instance->OnEnemyDead(Subject);
	// });
	//
	// AEnemySpawner::Instance
	// TArray<FVector> Locations;
	// for (auto FindCharacter : UGameplayFuncLib::GetAwGameState()->GetPlayerCharacters())
	// {
	// 	if(FindCharacter == nullptr) continue;
	// 	Locations.Add(FindCharacter->GetActorLocation());
	// }
	// if (auto GS = UGameplayFuncLib::GetAwGameStateSurvivor())
	// {
	// 	if (GS->AthenaTower)
	// 	{
	// 		Locations.Add(GS->AthenaTower->GetActorLocation());
	// 	}
	// }
	// auto Records = UMonsterReplaceMgrLib::GetSubjectInRange(Locations,FFilter::Make<FEnemy>(),AEnemySpawner::Instance,Approach);
	// for (auto Record : Records)
	// {
	// 	int Speed = UGameplayFuncLib::GetDataManager()->GetMobProp(Record.MobProp.ID).MoveSpeed * DeltaTime;
	// 	UMonsterReplaceMgrLib::MoveSubjectTo(Record.SubjectHandle,Record.TargetLocation,Speed);
	// }
	Mechanism->OperateConcurrently([&](FSolidSubjectHandle Subject,FAC_Approaching& Approaching,FLocated& Located)
	{
		// UMonsterReplaceMgrLib::MoveSubjectTo(Subject,Approaching.TargetLocation,Approaching.Speed);
		if (!Subject.HasTrait<FAS_Run>())
		{
			Subject.SetTraitDeferred(FAC_Run());
			return;
		}
		
		FVector TargetLocation = Approaching.TargetLocation;
		TargetLocation.Z = Located.Location.Z;
		float Dist = FVector::Distance(TargetLocation,Located.Location);
		if (Dist<Approaching.TargetRadius)
		{
			Subject.RemoveTraitDeferred<FAC_Approaching>();
			Subject.SetTraitDeferred(FAC_Idle());
		}
		else 
		{
			Located.Location = MoveTowards(Located.Location, TargetLocation, ECSDeltaTimeMove*Approaching.Speed);
		}
	},ThreadsCount,BaseBatchSize);
	
}
float AMechanicRunner::GetDistanceToGround(const FVector& StartLocation, float TraceDistance,float &GroundZ,bool &IsHit) const
{
    FHitResult Hit;
    FVector Start = StartLocation + FVector(0, 0, TraceDistance);
    FVector End = StartLocation - FVector(0, 0, TraceDistance); // 向下 trace

    FCollisionQueryParams Params(SCENE_QUERY_STAT(DistanceToGround), false, GetOwner());
	FCollisionObjectQueryParams ObjectParams(ECollisionChannel::ECC_WorldStatic);

    bool bHit = GetWorld()->LineTraceSingleByObjectType(
        Hit,
        Start,
        End,
        ObjectParams,
        Params
    );

    if (bHit)
	{
    	// UPrimitiveComponent* Comp = Hit.GetComponent();
    	// if (Comp && Comp->GetCollisionEnabled() != ECollisionEnabled::NoCollision)
    	{
    		GroundZ = Hit.Location.Z;
    		IsHit = true;
    		return Hit.Location.Z - StartLocation.Z;
    	}
    }
	else
	{
		GroundZ = 0;
	}

    return TraceDistance; // 未命中地面，默认最大距离
}

void AMechanicRunner::CleanAllECSMonster()
{
	const auto Mechanism = GetMechanism();
	const auto EnemyFilter = FFilter::Make<FEnemy>();
	EnemyFilter.Excludes(EFlagmarkBit::C);
	Mechanism->OperateConcurrently([&](FSolidSubjectHandle Subject,FEnemy& Enemy)
	{
		// Subject.DespawnDeferred()
		Subject.SetFlag(EFlagmarkBit::C);
	},ThreadsCount,BaseBatchSize);
}

int AMechanicRunner::LogPlayerHit(AAwCharacter* Character, FActionChangeInfo ActionChangeInfo,
	FAwForceFeedBackInfo FeedBackInfo,bool IsBullet)
{
	if (Character->OwnerPlayerController)
	{
		int index = Character->OwnerPlayerController->GetLocalPCIndex();
		if (index==0)
		{
			HitlogP1.SetInfo(Character,ActionChangeInfo,FeedBackInfo,IsBullet);
		}
		else if (index==1)
		{
			HitlogP2.SetInfo(Character,ActionChangeInfo,FeedBackInfo,IsBullet);
		}
		return index;
	}
	return -1;
}

void AMechanicRunner::DealPlayerHit(const FHitLogPlayer& Hitlog,const int& SafeHit)
{
		if (Hitlog.bIsHit)
		{
			if (!Hitlog.bIsBullet)
			{
				UPlayForceFeedBack::Play(Hitlog.ForceFeedBackInfo, Hitlog.Player->GetMesh());
			}
			if (Hitlog.ActionChangeInfo.ChangeMethod == EActionChangeMethod::Keep)
			{
				UECSDamageLib::HitFreeze(Hitlog.ActionChangeInfo,Hitlog.Player->GetAwAnimInstance(),SafeHit);
			}
			else
			{
				FActionParam ActionParam;
				ActionParam.Active = true;
				Hitlog.Player->PreorderAction(Hitlog.ActionChangeInfo, ActionParam);
			}
		}
	
}

void AMechanicRunner::DealAllPlayerHit(const int& SafeHitP1,const int& SafeHitP2,const int& SafeKill)
{
		auto ScoreManager = UGameplayFuncLib::GetScoreManager();
		if (SafeHitP1>0)
		{
			ScoreManager->AddCombo(SafeHitP1,3);
		}
		// if (SafeKill>0)
		// {
		// 	ScoreManager->AddKillCombo(SafeKill);
		// }
		DealPlayerHit(HitlogP1,SafeHitP1);
		DealPlayerHit(HitlogP2,SafeHitP2);
}
