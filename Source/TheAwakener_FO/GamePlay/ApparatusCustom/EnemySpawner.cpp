// The copyright of this project belongs to Shanghai Minghuan Software Technology Co., Ltd


#include "EnemySpawner.h"
#include "BubbleSphere.h"
#include "BubbleCage.h"
#include "Directed.h"
#include "MathUtil.h"
#include "MechanicRunner.h"
#include "MonsterReplaceMgr.h"
#include "TraitRendererComponent.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "Traits/Enemy.h"
#include "Traits/Health.h"
#include "Traits/JustSpawned.h"
#include "Traits/RenderBatches.h"
AEnemySpawner* AEnemySpawner::Instance = nullptr;
// Sets default values
AEnemySpawner::AEnemySpawner()
{
	// Set this actor to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryActorTick.bCanEverTick = true;
}

void AEnemySpawner::TestSpawn()
{
	const auto Mechanism = UMachine::ObtainMechanism(GetWorld());
	const auto BubbleCage = ABubbleCage::GetInstance();
	if (BubbleCage == nullptr) return;
	const auto BubbleCageComponent = BubbleCage->GetComponent();
	FVector location = GetActorLocation();
	for (int32 j = 0; j < SpawnTestNum; ++j,--SpawnTestNum)
	{
		SpawnEnemyComplex(0,location,SpawnRadius,FString());
	}
}

FVector2D AEnemySpawner::VRandomCircle()
{
	float radian = FMath::FRand() * FMathf::Pi * 2;
	float x = FMath::Cos(radian);
	float y = FMath::Sin(radian);
	return FVector2D(x, y) * FMath::FRand();
}

void AEnemySpawner::UpdateRenderState(std::function<void()> UpdatedCallback)
{
	for (int32 BatchedSoFar = 0, TotalHandled = 0; TotalHandled < EnemyMobProps.Num(); CurrentBatchIndex += 1,
	     TotalHandled += 1)
	{
		if (CurrentBatchIndex >= EnemyMobProps.Num())
		{
			CurrentBatchIndex = 0;
		}
		const auto BatchType = EnemyMobProps[CurrentBatchIndex].Trait;
		const auto Renderer = UTraitRendererComponent::GetInstance(BatchType);
		Renderer->Update();
		UpdatedCallback();
		Renderer->UpdateRenderState();
		BatchedSoFar += Renderer->GetNumRenderInstances();
	}
}

void AEnemySpawner::OnEnemyDead(FSolidSubjectHandle& Handle)
{
	if (Handle.HasTrait<FEnemy>())
	{
		const auto& Enemy = Handle.GetTraitRef<FEnemy>();
		const auto MobId = GetMobIdByTrait(EnemyMobProps[Enemy.KindId].Trait);
		FString Alter;
		if (Handle.HasTrait<FMonsterAlter>())
		{
			Alter = FStringPool::Get(Handle.GetTraitRef<FMonsterAlter>().AlterId);
		}
		auto ScoreManager = UGameplayFuncLib::GetScoreManager();
		ScoreManager->AddKillCombo(1);
		CallBPOnEnemyDead(MobId, Handle.GetTraitRef<FLocated>().Location,Alter);

		int Exp = UGameplayFuncLib::GetAwDataManager()->GetMobExp(MobId,Alter);
		//玩家获得经验
		if (UGameplayFuncLib::GetWorkingAwPlayerController()->CurCharacter)
		{
			UGameplayFuncLib::GetWorkingAwPlayerController()->CurCharacter->GainExp(Exp);
		}
	}
}

FString AEnemySpawner::GetMobIdByTrait(UScriptStruct* Trait)
{
	if (!Trait)
	{
		UE_LOG(LogTemp, Warning, TEXT("EnemySpawner: Trait is null"));
		return FString();
	}

	// 遍历EnemyMobProps查找匹配的Trait
	for (const auto& MobProp : EnemyMobProps)
	{
		if (MobProp.Trait == Trait)
		{
			return MobProp.ID;
		}
	}

	UE_LOG(LogTemp, Warning, TEXT("EnemySpawner: No Mob ID found for trait type: %s"),
		Trait ? *Trait->GetName() : TEXT("Unknown"));
	return FString();
}

FEnemyMobProp AEnemySpawner::GetMobPropById(FString MobId)
{
	for (const auto& MobProp : EnemyMobProps)
	{
		if (MobProp.ID == MobId)
		{
			return MobProp;
		}
	}
	return FEnemyMobProp();
}

int AEnemySpawner::GetMobIndexById(FString MobId)
{
	for (int i = 0; i < EnemyMobProps.Num(); i++)
	{
		if (EnemyMobProps[i].ID == MobId)
		{
			return i;
		}
	}
	return -1;	
}

// Called every frame
void AEnemySpawner::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);
}

void AEnemySpawner::SpawnEnemy(int Index)
{
	SpawnEnemyComplex(Index, FVector(), SpawnRadius,FString());
}


void AEnemySpawner::SpawnEnemyComplex(int Index, FVector SpawnPoint2D, float Radius,FString AlterId)
{
	if (EnemyMobProps.Num() <= Index ||
		EnemyConfigs.Num() <= Index)
	{
		return;
	}
	const auto Mechanism = UMachine::ObtainMechanism(GetWorld());
	const auto& Config = EnemyConfigs[Index];
	const auto& Sphere = Config.EnemyRecord.GetTraitRef<FBubbleSphere>();
	UAwDataManager* DataManager = UGameplayFuncLib::GetDataManager();
	
	FMobProp MobProp;
	MobProp = DataManager->GetMobProp(EnemyMobProps[Index].ID,AlterId);
	int32 _SpawnPointIterationsMax = SpawnPointIterationsMax;
	const auto BubbleCage = ABubbleCage::GetInstance();
	if (BubbleCage == nullptr) return;
	const auto BubbleCageComponent = BubbleCage->GetComponent();
	// Make sure the spawn point is within a valid range:
	float rangePower = 1;
	float rangeMulti = 1.1;
	do
	{
		SpawnPoint2D += FVector(VRandomCircle() * (Radius + Sphere.Radius) /* FMath::Pow(rangeMulti,++rangePower)*/,
		                        Sphere.Radius);
		if (--_SpawnPointIterationsMax == 0)
		{
			// Too many tries to find the point, so just exit for now:
			return;
		}
	}
	while (!BubbleCageComponent->IsInside(SpawnPoint2D));
	// SpawnPoint2D = FVector(VRand2D() * (Radius + Sphere.Radius) * FMath::FRand(), 0);
	const auto Enemy = Mechanism->SpawnSubject(Config.EnemyRecord);
	if (!Enemy.IsValid())return;
	Enemy.GetTraitRef<EParadigm::Unsafe, FEnemy>().KindId = Index;
	Enemy.SetTrait(FLocated(SpawnPoint2D));
	Enemy.SetTrait(FJustSpawned());
	Enemy.SetTrait(FHealth{MobProp.HP, MobProp.HP});
	if (!AlterId.IsEmpty())
	{
		Enemy.SetTrait(FMonsterAlter{FStringPool::Register(AlterId)});
	}
	int groupId = FStringPool::Register(UMonsterReplaceMgr::MakeGroupName(EnemyMobProps[Index].ID,AlterId));
	Enemy.SetTrait(FMonsterGroup{groupId});
	// Random rotation for variation sake:
	// Enemy.SetTrait(FDirected{FMath::VRand()});
	if (Enemy.HasTrait<FRenderBatched>())
	{
		Enemy.ObtainTrait(EnemyMobProps[Index].Trait);
	}
}
