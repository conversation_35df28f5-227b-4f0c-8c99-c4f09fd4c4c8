// The copyright of this project belongs to Shanghai Minghuan Software Technology Co., Ltd


#include "TheAwakener_FO/GamePlay/ApparatusCustom/MonsterReplaceMgr.h"

#include "Located.h"

#include "BubbleCage.h"
#include "MechanicRunner.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "Traits/Enemy.h"
#include "Traits/Health.h"
#include "TheAwakener_FO/Optimization/StatsDeclare.h"
#include "Traits/TrCommand.h"
#include "HAL/PlatformMisc.h"
#include "Misc/CommandLine.h"
#include "Misc/Parse.h"

// 静态成员变量定义
UMonsterReplaceMgr* UMonsterReplaceMgr::Instance = nullptr;
bool UMonsterReplaceMgr::IsGroupNameContainAlterId = true;

void UMonsterReplaceMgr::BeginPlay()
{
	Super::BeginPlay();
	Instance = this;
}

void UMonsterReplaceMgr::ClearPendingNewActor()
{
	PendingNewActor.Empty();
}

void UMonsterReplaceMgr::AddPendingNewActor(FString GroupName)
{
	int key = FStringPool::Register(GroupName);
	if(!PendingNewActor.Contains(key))
	{
		PendingNewActor.Add(key,0);
	}
	PendingNewActor[key]++;
}

int UMonsterReplaceMgr::GetPendingActors(FString GroupName)
{
	int key = FStringPool::Register(GroupName);
	if (PendingNewActor.Contains(key))
	{
		return PendingNewActor[key];
	}
	return 0;
}

void UMonsterReplaceMgr::AddMonsterAmount(FString GroupId, FMonsterLog Log)
{
	MonstersAmount.Add(FStringPool::Register(GroupId),Log);
}

void UMonsterReplaceMgr::RemoveMonsterAmount(FString GroupId)
{
	MonstersAmount.Remove(FStringPool::Register(GroupId));
}

void UMonsterReplaceMgr::OverrideActorLimit(FString MobId, FString AlterId, int Limit)
{
	FString GroupName = MakeGroupName(MobId,AlterId);
	int GroupId = FStringPool::Register(GroupName);
	check (ECSMonsterRangeData.Contains(GroupId));
	ECSMonsterRangeData[GroupId].MaxActorNum = Limit;
}

void UMonsterReplaceMgr::InitECSMonsterRangeData(FString MobId, FString AlterId)
{
	FString GroupName = MakeGroupName(MobId,AlterId);
	int GroupId = FStringPool::Register(GroupName);
	if (!ECSMonsterRangeData.Contains(GroupId))
	{
		auto MobProp = EnemySpawner->EnemyMobProps.FindByPredicate([&](FEnemyMobProp Data)
		{
			return Data.ID == MobId && Data.AlterId == AlterId;
		});
		if (!MobProp)
		{
			MobProp = EnemySpawner->EnemyMobProps.FindByPredicate([&](FEnemyMobProp Data)
			{
				return Data.ID == MobId;
			});
		}
		if (MobProp)
		{
			FECSMonsterRangeData NewData;
			NewData.GroupId = GroupId;
			NewData.ReplaceRange = MobProp->ReplaceRange;
			NewData.AimRange = MobProp->AimingRange;
			NewData.ApprouchRangeMax = MobProp->ApproachRange;
			NewData.ApprouchRangeMin = MobProp->ApproachRangeMin;
			NewData.HeightOffset = MobProp->HeightOffset;
			NewData.RotOffset = MobProp->RotOffset;
			NewData.ApprouchRangeStopRadius = MobProp->ApproachRangeStopRadius;
			NewData.MoveSpeed = UGameplayFuncLib::GetDataManager()->GetMobProp(MobId,AlterId).MoveSpeed;
			NewData.Trait = MobProp->Trait;
			NewData.MobId = FStringPool::Register(MobId);
			NewData.AlterId = AlterId.IsEmpty() ? -1:FStringPool::Register(AlterId);
			NewData.MaxActorNum = MobProp->MaxActorNum;
			ECSMonsterRangeData.Add(GroupId,NewData);
		}
		else
		{
			FECSMonsterRangeData NewData;
			NewData.GroupId = GroupId;
			NewData.MoveSpeed = UGameplayFuncLib::GetDataManager()->GetMobProp(MobId,AlterId).MoveSpeed;
			NewData.MobId = FStringPool::Register(MobId);
			NewData.AlterId = AlterId.IsEmpty() ? -1:FStringPool::Register(AlterId);
			ECSMonsterRangeData.Add(GroupId,NewData);
			UE_LOG(LogTemp,Warning,TEXT("Can not find the MobProp: %s Alter:%s"), *MobId,*AlterId);
		}
	}
}

FString UMonsterReplaceMgr::MakeGroupName(FString MobId, FString AlterId)
{
	if (IsGroupNameContainAlterId)
	{
		return MobId + AlterId;
	}
	return MobId;
}

void UMonsterReplaceMgr::UpdatePlayerLocation()
{
	PlayerLocations.Empty();
	AAwCharacter* Tower = nullptr;
	if (UGameplayFuncLib::GetAwGameStateSurvivor())
	{
		Tower = UGameplayFuncLib::GetAwGameStateSurvivor()->AthenaTower;
	}
	if (Tower)
	{
		PlayerLocations.Add(Tower->GetActorLocation());
	}
	for (auto FindCharacter : UGameplayFuncLib::GetAwGameState()->GetPlayerCharacters())
	{
		if(FindCharacter == nullptr) continue;
		FVector PlayerLocation = FindCharacter->GetActorLocation();
		float Speed = FindCharacter->GetMoveComponent()->GetMoveSpeed();
		Speed = FMath::Clamp(Speed,0,PositionPredictionLimit);
		PlayerLocation += FindCharacter->GetActorForwardVector() * Speed;
		PlayerLocations.Add(PlayerLocation);
	}
}

void UMonsterReplaceMgr::CheckActive()
{
	SCOPE_CYCLE_COUNTER(STAT_Survivor_Active);
	const auto Mechanism = UMachine::ObtainMechanism(this->GetWorld());
	FFilter ActiveFilter = FFilter::Make<FEnemy>();
	ActiveFilter.Exclude(EFlagmarkBit::C);
	ActiveFilter.Exclude(EFlagmarkBit::D);
	Mechanism->OperateConcurrently(ActiveFilter,[&](FSolidSubjectHandle Subject,FLocated &Located,FMonsterGroup& GroupData)
	{
		// Subject.GetTrait()
		// Record.SubjectHandle.SetTraitDeferred(FAC_FaceTo{Record.TargetLocation});
		if(ECSMonsterRangeData.Contains(GroupData.Id))
		{
			float Range = FMath::Square(ECSMonsterRangeData[GroupData.Id].AimRange);
			float MinDist = TNumericLimits<float>::Max();
			FVector MinLocation;
			bool Found = false;
			for (auto PlayerLocation : PlayerLocations)
			{
				float Dist = FVector::DistSquared2D(Located.Location,PlayerLocation);
				if (Dist<Range && Dist<MinDist)
				{
					MinDist = Dist;
					MinLocation = PlayerLocation;
					Found = true;
				}
			}
			if (Found)
			{
				Subject.SetTraitDeferred(FAC_FaceTo{MinLocation});
			}
		}
	},AMechanicRunner::ThreadsCount,AMechanicRunner::BaseBatchSize);
}

void UMonsterReplaceMgr::CheckApproach()
{
	SCOPE_CYCLE_COUNTER(STAT_Survivor_Approach);
	const auto Mechanism = UMachine::ObtainMechanism(this->GetWorld());
	FFilter AppFilter = FFilter::Make<FEnemy>();
	AppFilter.Exclude(EFlagmarkBit::C);
	AppFilter.Exclude(EFlagmarkBit::D);
	Mechanism->OperateConcurrently(AppFilter,[&](FSolidSubjectHandle Subject,FLocated &Located,FMonsterGroup& GroupData)
	{
		// Subject.GetTrait()
		// Record.SubjectHandle.SetTraitDeferred(FAC_FaceTo{Record.TargetLocation});
		if(ECSMonsterRangeData.Contains(GroupData.Id))
		{
			float StopRadius = ECSMonsterRangeData[GroupData.Id].ApprouchRangeStopRadius;
			float RangeMin = FMath::Square(ECSMonsterRangeData[GroupData.Id].ApprouchRangeMin);
			float RangeMax = FMath::Square(ECSMonsterRangeData[GroupData.Id].ApprouchRangeMax);
			float MinDist = TNumericLimits<float>::Max();
			FVector MinLocation = Located.Location;
			bool Found = false;
			for (auto PlayerLocation : PlayerLocations)
			{
				float Dist = FVector::DistSquared2D(Located.Location,PlayerLocation);
				if (Dist<RangeMax && Dist<MinDist)
				{
					MinDist = Dist;
					MinLocation = PlayerLocation;
					Found = true;
				}
			}
			if (Found)
			{
				MoveSubjectTo(Subject,MinLocation,StopRadius,RangeMin,ECSMonsterRangeData[GroupData.Id].MoveSpeed);
			}
		}
	},AMechanicRunner::ThreadsCount,AMechanicRunner::BaseBatchSize);
}

void UMonsterReplaceMgr::CheckReplace()
{
	SCOPE_CYCLE_COUNTER(STAT_SurvivorReplace);

	for (const auto& RangeKV : ECSMonsterRangeData)
	{
		auto RangeData = RangeKV.Value;
		CheckMobInRange();
		if (MapMobInRange[RangeKV.Key]<RangeData.MaxActorNum)
		{
			auto Res = GetSubjectInRange(RangeData,Replace);
			FString AlterId = RangeData.AlterId==-1?"":FStringPool::Get(RangeData.AlterId);
			NativeEventReplaceMonster(Res,FStringPool::Get(RangeData.MobId),AlterId,FStringPool::Get(RangeData.GroupId),RangeData.HeightOffset,RangeData.RotOffset);
		}
	}

}

void UMonsterReplaceMgr::NativeEventReplaceMonster_Implementation(const TArray<FReplacementCheckingResult>& Res, const FString& MobId, const FString& AlterId, const FString& GroupId, float HeightOffset, float RotOffset)
{
	// 默认的C++实现，可以在蓝图中重写
	// 这里可以添加默认的怪物替换逻辑
}

void UMonsterReplaceMgr::CheckMobInRange()
{
	for (auto RangeDataKV : ECSMonsterRangeData)
	{
		// GetSubjectInRange()
		auto RangeData = RangeDataKV.Value;
		float DistSquare = FMath::Square(RangeData.ReplaceRange);
		if (!MapMobInRange.Contains(RangeDataKV.Key))
		{
			MapMobInRange.Add(RangeDataKV.Key,0);
		}
		else
		{
			MapMobInRange[RangeDataKV.Key] = 0;
		}
		if (MonstersAmount.Contains(RangeDataKV.Key))
		{
			for (const auto& Mob : MonstersAmount[RangeDataKV.Key].Monsters)
			{
				bool Found = false;
				for (auto PlayerLocation:PlayerLocations)
				{
					float Dist = FVector::DistSquared2D(Mob->GetActorLocation(),PlayerLocation);
					if(Dist<DistSquare)
					{
						Found = true;
						break;
					}
				}
				if (Found)
				{
					MapMobInRange[RangeDataKV.Key]++;
				}
			}
		}
	}
}

void UMonsterReplaceMgr::OnTick(float DeltaTime)
{
	CheckTimeCount += DeltaTime;
	if (CheckTimeCount >= CheckInterval)
	{
		UpdatePlayerLocation();
		CheckActive();
		CheckApproach();
		if (EnableReplace)
		{
			CheckReplace();
		}
		CheckTimeCount = 0.0f;
	}
}

void UMonsterReplaceMgr::GenerateDistance(FReplacementCheckingResult& Record)
{
	auto located = Record.SubjectHandle.GetTraitPtr<FLocated, EParadigm::Unsafe>();
	if (located)
	{
		Record.Distance = FVector::DistSquared(located->Location, Record.TargetLocation);
	}
}

void UMonsterReplaceMgr::SortByDistance(TArray<FReplacementCheckingResult>& Records, int keep)
{
	//generate distance for records
	for (auto& Record : Records)
	{
		GenerateDistance(Record);
	}

	Records.Sort([](const FReplacementCheckingResult& A, const FReplacementCheckingResult& B)
	{
		return A.Distance < B.Distance;
	});
	if (keep > 0 && Records.Num() > keep)
	{
		Records.RemoveAt(keep, Records.Num() - keep);
	}
}

// TArray<FReplacementCheckingResult> UMonsterReplaceMgr::GetSubjectInRange_Old(
// 	const TArray<FVector> PlayerLocations, FFilter Filter, AEnemySpawner* Spawner,EECSRangeType RangeType , int KeepPerMobId)
// {
// 	SCOPE_CYCLE_COUNTER(STAT_SurvivorReplace_Search);
// 	TArray<FReplacementCheckingResult> Result;
// 	if (!Spawner)return Result;
//
// 	for (const auto& MobProp : Spawner->EnemyMobProps)
// 	{
// 		// Get all subjects of this MobProp
// 		FFilter MobFilter = Filter;
// 		MobFilter += MobProp.Trait;
// 		bool IsAlter = !MobProp.AlterId.IsEmpty();
// 		int StrAlter = -1;
// 		if (IsAlter)
// 		{
// 			StrAlter = FStringPool::Register(MobProp.AlterId);
// 		}
// 		TArray<FReplacementCheckingResult> Group;
// 		float Range = 0;
// 		switch(RangeType)
// 		{
// 			case Aiming:
// 				Range = MobProp.AimingRange;
// 				break;
// 			case Approach:
// 				Range = MobProp.ApproachRange;
// 				break;
// 			case Replace:
// 				Range = MobProp.ReplaceRange;
// 				break;
// 		}
// 		for (const auto& PlayerLocation : PlayerLocations)
// 		{
// 			auto SubjectsAtLocation = ABubbleCage::GetOverlapping(PlayerLocation, Range, MobFilter);
// 			for (const auto& Subject : SubjectsAtLocation)
// 			{
// 				auto AlterData = Subject.GetTraitPtr<FMonsterAlter, EParadigm::Unsafe>();
// 				// 如果MobProp有AlterId，严格匹配相同AlterId的怪物
// 				if (IsAlter)
// 				{
// 					if (!AlterData || AlterData->AlterId != StrAlter)
// 					{
// 						continue; // MobProp有AlterId但怪物没有或AlterId不匹配
// 					}
// 				}
// 				else
// 				{
// 					// 如果MobProp没有AlterId，但怪物有AlterId，检查是否存在对应的AlterId MobProp
// 					if (AlterData)
// 					{
// 						// 检查是否存在匹配该怪物AlterId的MobProp
// 						bool HasMatchingAlterMobProp = false;
// 						for (const auto& CheckMobProp : Spawner->EnemyMobProps)
// 						{
// 							if (!CheckMobProp.AlterId.IsEmpty() &&
// 								CheckMobProp.Trait == MobProp.Trait &&
// 								FStringPool::Register(CheckMobProp.AlterId) == AlterData->AlterId)
// 							{
// 								HasMatchingAlterMobProp = true;
// 								break;
// 							}
// 						}
// 						// 如果存在对应的AlterId MobProp，跳过此怪物（让有AlterId的MobProp处理）
// 						if (HasMatchingAlterMobProp)
// 						{
// 							continue;
// 						}
// 					}
// 				}
// 				//记录搜索条目
// 				auto Located = Subject.GetTraitPtr<FLocated, EParadigm::Unsafe>();
// 				auto Health = Subject.GetTraitPtr<FHealth, EParadigm::Unsafe>();
// 				if (Located && Subject.IsValid() && Health)
// 				{
// 					FReplacementCheckingResult Record;
// 					Record.SubjectHandle = Subject;
// 					Record.TargetLocation = PlayerLocation;
// 					Record.HP = Health->Value;
// 					Record.HPMax = Health->Maximum;
// 					// Record.RangeData = MobProp;
// 					Record.Distance = FVector::DistSquared(Located->Location, PlayerLocation);
// 					Group.Add(Record);
// 				}
// 			}
// 		}
// 		SortByDistance(Group, KeepPerMobId);
// 		Result.Append(Group);
// 	}
// 	return Result;
// }

TArray<FSubjectHandle> UMonsterReplaceMgr::OverlapGradually(const UE::Math::TVector<double>& Vector, const float MaxRange,
	const int KeepPerMobId, const FFilter& MobFilter, const int StartRangeMulti)
{
	float Range = MaxRange / StartRangeMulti;
	int LoopTimes = 1;
	TArray<FSubjectHandle> Result;
	bool Continue = true;

	// 计算阈值，避免整数除法问题
	float IncrementThreshold = FMath::Max(1.0f, (float)KeepPerMobId / StartRangeMulti);
	int MaxLoopTimes = FMath::Max(1, StartRangeMulti / 2);
	float Multiplier = 2;
	while (Continue)
	{
		Result = ABubbleCage::GetOverlapping(Vector, Range, MobFilter);
		int Num = Result.Num();
		if (Num > IncrementThreshold)
		{
			// 如果有捕捉到对象 则慢慢增加范围
			Multiplier = FMath::LogX<float>(Num,KeepPerMobId);
		}
		else
		{
			// 快速增加范围
			Multiplier = 2.0f;
		}
		UE_LOG(LogTemp,Log,TEXT("HawkLog: Replace Checking RangeMultiplier %f"),Multiplier);
		Range *= Multiplier;
		if (Range >= MaxRange)
		{
			Continue = false;
		}

		LoopTimes++;

		if (LoopTimes > MaxLoopTimes || Num >= KeepPerMobId)
		{
			break;
		}
	}
	return Result;
}

TArray<FReplacementCheckingResult> UMonsterReplaceMgr::GetSubjectInRange(const FECSMonsterRangeData& RangeData, const EECSRangeType RangeType)
{
	SCOPE_CYCLE_COUNTER(STAT_SurvivorReplace_Search);
	// Get all subjects of this MobProp
	FFilter MobFilter = Filter;
	int KeepPerMobId = RangeData.MaxActorNum;
	MobFilter += RangeData.Trait;
	TArray<FReplacementCheckingResult> Group;
	float Range = 0;
	switch (RangeType)
	{
		case Aiming:
			Range = RangeData.AimRange;
			break;
		case Approach:
			Range = RangeData.ApprouchRangeMax;
			break;
		case Replace:
			Range = RangeData.ReplaceRange;
			break;
	}
	for (const auto& PlayerLocation : PlayerLocations)
	{
		TArray<FSubjectHandle> SubjectsAtLocation = OverlapGradually(PlayerLocation, Range, KeepPerMobId, MobFilter, 4);
		for (const FSubjectHandle& Subject : SubjectsAtLocation)
		{
			SCOPE_CYCLE_COUNTER(STAT_SurvivorReplace_CreateSearchResult);
			auto GroupData = Subject.GetTraitPtr<FMonsterGroup, EParadigm::Unsafe>();
			if (!GroupData || GroupData->Id!=RangeData.GroupId)
			{
				continue;
			}
			auto Located = Subject.GetTraitPtr<FLocated, EParadigm::Unsafe>();
			auto Health = Subject.GetTraitPtr<FHealth, EParadigm::Unsafe>();
			if (Located && Subject.IsValid() && Health)
			{
				FReplacementCheckingResult Record;
				Record.SubjectHandle = Subject;
				Record.TargetLocation = PlayerLocation;
				Record.HP = Health->Value;
				Record.HPMax = Health->Maximum;
				Record.RangeData = RangeData;
				Record.Distance = FVector::DistSquared(Located->Location, PlayerLocation);
				Group.Add(Record);
			}
		}
	}
	SortByDistance(Group,KeepPerMobId);
	return Group;
}

void UMonsterReplaceMgr::MoveSubjectTo(FSolidSubjectHandle& Subject, FVector TargetLocation,float StopRadius, float IngoreDist,float Speed)
{
	auto Located = Subject.GetTraitPtr<FLocated, EParadigm::Unsafe>();
	
	if (Located)
	{
		TargetLocation.Z = Located->Location.Z;
		float Dist = FVector::DistSquared2D(TargetLocation,Located->Location);
		if (Dist<IngoreDist) return;
	}
	auto ap = FAC_Approaching();
	ap.Speed = Speed;
	ap.TargetLocation = TargetLocation;
	ap.TargetRadius = StopRadius;
	Subject.SetTraitDeferred(ap);
}

// void UMonsterReplaceMgr::CheckMonsterToActive_Old(const TArray<FVector> PlayerLocations, FFilter Filter,
// 	AEnemySpawner* Spawner, EECSRangeType RangeType)
// {
// 	SCOPE_CYCLE_COUNTER(STAT_Survivor_Active);
// 	auto Res = GetSubjectInRange_Old(PlayerLocations,Filter,Spawner,RangeType,9999);
// 	for (auto Record : Res)
// 	{
// 		Record.SubjectHandle.SetTraitDeferred(FAC_FaceTo{Record.TargetLocation});
// 	}
// }
