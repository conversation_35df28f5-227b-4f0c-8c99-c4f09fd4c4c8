// The copyright of this project belongs to Shanghai Minghuan Software Technology Co., Ltd

#pragma once

#include "CoreMinimal.h"
#include "ECSStructs.h"
#include "EnemySpawner.h"
#include "Filter.h"
#include "Components/ActorComponent.h"
#include "MonsterReplaceMgr.generated.h"

USTRUCT(BlueprintType)
struct FECSMonsterRangeData
{
	GENERATED_BODY()
	int GroupId;
	int MobId;
	int AlterId;
	UScriptStruct* Trait;
	float AimRange;
	float ApprouchRangeMin;
	float ApprouchRangeMax;
	float HeightOffset;
	float RotOffset;
	float ReplaceRange;
	float MoveSpeed;
	float ApprouchRangeStopRadius;
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	int MaxActorNum = 5;
};
USTRUCT(BlueprintType)
struct FReplacementCheckingResult
{
	GENERATED_BODY()
	
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	FSubjectHandle SubjectHandle;
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	FVector TargetLocation;
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	int HP;
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	int HPMax;
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	FECSMonsterRangeData RangeData;
	UPROPERTY(BlueprintReadOnly,EditAnywhere)
	float Distance;
};

UENUM(BlueprintType)
enum EECSRangeType
{
	Aiming,
	Approach,
	Replace,
};
UCLASS(Blueprintable, ClassGroup=(Custom), meta=(BlueprintSpawnableComponent) )
class THEAWAKENER_FO_API UMonsterReplaceMgr : public UActorComponent
{
	GENERATED_BODY()
	public:
	static UMonsterReplaceMgr* Instance;
	virtual void BeginPlay() override;
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	float CheckInterval = 0.3f;
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	float CheckTimeCount = 0.0f;
	
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	AEnemySpawner* EnemySpawner;

	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	int MonsterReplaceLimit = 5;
	
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	FFilter Filter;
	
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	bool EnableReplace = true;
	//记录actor怪物信息
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	TMap<int,FMonsterLog> MonstersAmount;
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	TMap<int,int> PendingNewActor;
	UFUNCTION(BlueprintCallable)
	void ClearPendingNewActor();
	UFUNCTION(BlueprintCallable)
	void AddPendingNewActor(FString GroupName);
	UFUNCTION(BlueprintCallable,BlueprintPure)
	int GetPendingActors(FString GroupName);
	UFUNCTION(BlueprintCallable)
	void AddMonsterAmount(FString GroupId,FMonsterLog Log);
	UFUNCTION(BlueprintCallable)
	void RemoveMonsterAmount(FString GroupId);
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	TArray<FReplacementCheckingResult> ReplacementCheckResult;
	//临时确定范围内怪物数量
	TMap<int,int> MapMobInRange;


	UPROPERTY(BlueprintReadOnly)
	TArray<FVector> PlayerLocations;
	
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	TMap<int,FECSMonsterRangeData> ECSMonsterRangeData;
	static bool IsGroupNameContainAlterId;
	UFUNCTION(BlueprintCallable)
	void InitECSMonsterRangeData(FString MobId,FString AlterId);
	UFUNCTION(BlueprintCallable,BlueprintPure)
	static FString MakeGroupName(FString MobId,FString AlterId);
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	float PositionPredictionLimit = 300;

	void UpdatePlayerLocation();
	void CheckActive();
	void CheckApproach();
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable)
	void NativeEventReplaceMonster(const TArray<FReplacementCheckingResult>& Res, const FString& MobId, const FString& AlterId, const FString& GroupId, float HeightOffset, float RotOffset);
	virtual void NativeEventReplaceMonster_Implementation(const TArray<FReplacementCheckingResult>& Res, const FString& MobId, const FString& AlterId, const FString& GroupId, float HeightOffset, float RotOffset);
	void CheckMobInRange();
	void CheckReplace();
	UFUNCTION(BlueprintCallable)
	void OnTick(float DeltaTime);
	UFUNCTION(BlueprintCallable)
	static void GenerateDistance(FReplacementCheckingResult& Record);
	// Sorts the records by distance from the target location.
	UFUNCTION(BlueprintCallable)
	static void SortByDistance(TArray<FReplacementCheckingResult>& Records,int keep = 5);

	// UFUNCTION(BlueprintCallable)
	// static TArray<FReplacementCheckingResult> GetSubjectInRange_Old(const TArray<FVector> PlayerLocations,FFilter Filter,AEnemySpawner* Spawner,EECSRangeType RangeType,int KeepPerMobId = 5);

	static TArray<FSubjectHandle> OverlapGradually(const UE::Math::TVector<double>& Vector, const float MaxRange, const int KeepPerMobId, const FFilter& MobFilter, const int StartRangeMulti = 4);
	UFUNCTION(BlueprintCallable)
	TArray<FReplacementCheckingResult> GetSubjectInRange(const FECSMonsterRangeData& RangeData,EECSRangeType RangeType);
	
	UFUNCTION()
	static void MoveSubjectTo(FSolidSubjectHandle& Subject,FVector TargetLocation,float StopRadius, float IngoreDist,float Speed);

	// UFUNCTION(BlueprintCallable)
	// static void CheckMonsterToActive_Old(const TArray<FVector> PlayerLocations,FFilter Filter,AEnemySpawner* Spawner,EECSRangeType RangeType);
};
