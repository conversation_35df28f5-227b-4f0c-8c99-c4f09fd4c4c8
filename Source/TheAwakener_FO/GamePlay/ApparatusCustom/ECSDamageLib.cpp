#include "ECSDamageLib.h"

#include "BubbleSphere.h"
#include "Located.h"
#include "MechanicRunner.h"
#include "Rotated.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GameFramework/Base/AwGameInstance.h"
#include "TheAwakener_FO/GamePlay/Battle/OffenseManager.h"
#include "TheAwakener_FO/GamePlay/Characters/Attack/ForceMoveInfo.h"
#include "TheAwakener_FO/GamePlay/Characters/Battle/BattleStructs.h"
#include "TheAwakener_FO/GamePlay/DamageVolume/DamageManager.h"
#include "Traits/Attacks.h"
#include "Traits/Dying.h"
#include "Traits/Enemy.h"
#include "Traits/Health.h"
#include "Traits/TrAnimation.h"
#include "Traits/TrCommand.h"

void UECSDamageLib::PlayFX(const FSubjectHandle& Subject, AAwCharacter* Attacker, const FAttackInfo& AttackInfo)
{
	const auto Locate = Subject.GetTraitPtr<FLocated, EParadigm::Unsafe>();
	const auto Rotate = Subject.GetTraitPtr<FRotated, EParadigm::Unsafe>();
	const auto Sphere = Subject.GetTraitPtr<FBubbleSphere, EParadigm::Unsafe>();
	FVector Offset{0,0,Sphere->Radius};
	//命中被闪避时不播放特效和音效
	if (UGameplayFuncLib::GetHitVFX(AttackInfo.AttackerActionChange))
		UGameplayFuncLib::CreateVFXatLocation(UGameplayFuncLib::GetHitVFX(AttackInfo.AttackerActionChange), FTransform{Rotate->Rotation,Locate->Location + Offset}, true, true);
	if (UGameplayFuncLib::GetHitSFX(AttackInfo.AttackerActionChange))
		UGameplayFuncLib::PlaySFXatLocation(UGameplayFuncLib::GetHitSFX(AttackInfo.AttackerActionChange), Locate->Location + Offset, Rotate->GetRotator());
	if (UGameplayFuncLib::GetHitVFX(AttackInfo.DefenderActionChange))
		UGameplayFuncLib::CreateVFXatLocation(UGameplayFuncLib::GetHitVFX(AttackInfo.DefenderActionChange), FTransform{Rotate->Rotation,Locate->Location + Offset}, true, true);
	if (UGameplayFuncLib::GetHitSFX(AttackInfo.DefenderActionChange))
		UGameplayFuncLib::PlaySFXatLocation(UGameplayFuncLib::GetHitSFX(AttackInfo.DefenderActionChange), Locate->Location + Offset, Rotate->GetRotator());
}

void UECSDamageLib::AddHitLog(const FSubjectHandle& Subject,const EFlagmarkBit& Flag,double WorldSecond)
{
	auto HitLog = Subject.GetTraitPtr<FTr_HitLog, EParadigm::Unsafe>();
	HitLog->Pool[Flag].HitTimes ++;
	HitLog->Pool[Flag].HitTime = WorldSecond;
}

void UECSDamageLib::CleanActionHittedECS(EFlagmarkBit Flag,UWorld* World)
{
	if (Flag==EFlagmarkBit::E)
	{
		UE_LOG(LogTemp,Error,TEXT("CleanActionHittedECS: Wrong Flag E"));
		return;
	}
	const auto Mechanism = UMachine::ObtainMechanism(World);
	Mechanism->OperateConcurrently([&](FTr_HitLog& HitLog)
	{
		HitLog.CleanHitLogItem(Flag);
	},AMechanicRunner::ThreadsCount,AMechanicRunner::BaseBatchSize);
}
void UECSDamageLib::TryHitEnemy(FSubjectHandle& Subject,FTrAttackBox& EcsAttackBox,FOffenseInfo OffenseInfo,FDamageInfo& DInfo, const FVector& TargetLocation,const double& WorldSecond,bool& isHit,bool& isDead)
{
	FAttackInfo AttackInfo = OffenseInfo.AttackInfo;
	AAwCharacter* Attacker = EcsAttackBox.Attacker;
	EFlagmarkBit& Flag = EcsAttackBox.AttackFlag;
	int totalDamage;
	if (HasHitProtection(Subject,OffenseInfo,Flag,WorldSecond))return;
	// if (!IsDmgInfoGenerated)
	{
		// IsDmgInfoGenerated = true;
		UOffenseManager::DealBuffECS(Attacker,OffenseInfo);
		DInfo = FDamageInfo::FromAttackAndDefense(nullptr, OffenseInfo.AttackInfo, FDefenseInfo());
		DInfo.Attacker = Attacker;
		UDamageManager::ApplayCritcalAndBuffECS(Attacker,DInfo);
		DInfo.DamagePower.Physical = DInfo.FinalDamage(); //原逻辑里导致元素伤害翻倍，先保留
		
		OffenseInfo.AttackInfo.DefenderActionChange.HitStun.Velocity *= Attacker->CharacterObj.CurProperty.BeStrikeRate;
		OffenseInfo.AttackInfo.DefenderActionChange.HitStun.InSec *= Attacker->CharacterObj.CurProperty.BeStrikeRate;
		
		totalDamage = FMath::Abs(DInfo.DamagePower.TotalDamage());
	}
	
	OffenseInfo.AttackInfo.RotateByDegree(Attacker->GetActorRotation().Yaw, Attacker->GetActorLocation(), TargetLocation);	//动作的在这里进行方向旋转
	FForceMoveInfo ForceMoveInfo = AttackInfo.DefenderActionChange.HitStun;
	const auto Health = Subject.GetTraitPtr<FHealth, EParadigm::Unsafe>();
	
	Subject.SetTraitDeferred(FAC_AirLag{AttackInfo.DefenderActionChange.FreezeTime});//攻击冻结
	
	PlayFX(Subject,Attacker,AttackInfo);
	AddHitLog(Subject,Flag,WorldSecond);
	// Subject.SetFlag(EcsAttackBox.Attacker->ECSMark);
	if (Subject.HasTrait<FAS_InAir>())
	{
		Subject.SetTraitDeferred(FAC_Hit());
		Subject.SetTraitDeferred(FAS_InAir_Hitted());
	}	
	if (ForceMoveInfo.Velocity.Z>0)
	{
		Subject.SetTraitDeferred(FAC_HitUp());
		Subject.SetTraitDeferred(FAS_InAir_Hitted());
	}
	else
	{
		Subject.SetTraitDeferred(FAC_Hit());
	}
	isHit = true;
	if (Health)
	{
#if UE_EDITOR
		UE_LOG(LogTemp,Log,TEXT("Deal Damage:%i %i/%i -> %i/%i time:%f"),totalDamage,Health->Value,Health->Maximum,Health->Value-totalDamage,Health->Maximum,WorldSecond);
#endif
		Health->Value -= totalDamage * (DInfo.IsHeal == false ? 1 : -1);
		Health->Value = FMath::Clamp(Health->Value, 0, Health->Maximum);
		UGameplayFuncLib::ProcessAwakeningAndRogueRecovery(Attacker, Health->Value, DInfo);
	}
	//击退
	TryKnockback(Subject,ForceMoveInfo);
	//转向攻击者，而不是速度来源
	Subject.SetTraitDeferred(FAC_FaceTo{Attacker->GetActorLocation()});
	// UE_LOG(LogTemp, Display, TEXT("#%i Health value is %d"),Subject.GetId(), Health->Value);
	if (Health && Health->Value <= 0)
	{
		isDead = true;
		Subject.SetFlag(DyingFlag);
		Subject.RemoveTraitDeferred<FHealth>();
		Subject.SetTraitDeferred(FDying{});
		auto& Enemy = Subject.GetTraitRef<FEnemy, EParadigm::Unsafe>();
	}
}

void UECSDamageLib::TryBeepAchievementByHit(AAwCharacter* Character, TArray<FString> Beep)
{
	for (FString ABeep : Beep)
	{
		Character->AchievementSignalBeep(ABeep);
	}
}

void UECSDamageLib::TryKnockback(FSubjectHandle& Subject, FForceMoveInfo& ForceMoveInfo)
{
	if (ForceMoveInfo.Active)
	{
		Subject.SetTraitDeferred(ForceMoveInfo);
	}
}

void UECSDamageLib::HitFreeze(const FActionChangeInfo& ActionChangeInfo, UAwAnimInstance* AwAnimInstance, const int HitNum)
{	
	if (AwAnimInstance)
	{
		if (AwAnimInstance->IsInFreezingPassedThreshold()&&HitNum<UGameplayFuncLib::GetAwDataManager()->DebugConfig.FreezingMinEnemyCount)return;
		float FreezeTime = ActionChangeInfo.FreezeTime;
		FreezeTime = (AwAnimInstance->IsInFreezingPassedThreshold()||HitNum>=2) ?
			FMath::Max(FreezeTime * UGameplayFuncLib::GetAwDataManager()->DebugConfig.FreezingRate,UGameplayFuncLib::GetAwDataManager()->DebugConfig.MinFreezingTime)
			: FreezeTime;
		AwAnimInstance->FreezeAnim(FreezeTime, ActionChangeInfo.HitStun);
	}
}

bool UECSDamageLib::HasHitProtection(const FSubjectHandle& Subject, const FOffenseInfo& OInfo,EFlagmarkBit Flag,double WorldSecond)
{
	const auto HitLog = Subject.GetTraitPtr<FTr_HitLog,EParadigm::Unsafe>();
	if (HitLog)
	{
		return HitLog->Pool[Flag].HitTimes >= OInfo.CanHitTimes
			|| HitLog->Pool[Flag].HitTime > WorldSecond - OInfo.HitSameTargetDelay;
	}
	return false;
}

int UECSDamageLib::ProcessECSHitLogic(AAwCharacter* Attacker, const FDamageInfo& DInfo, const FOffenseInfo& OffenseInfo, bool IsBullet,std::atomic<int32> &SafeHitP1,std::atomic<int32> &SafeHitP2)
{
	if (!Attacker)
		return -1;

	// 记录玩家命中信息
	AMechanicRunner* MechanicRunner = AMechanicRunner::GetInstance();
	if (!MechanicRunner)
		return -1;

	int PlayerIndex = MechanicRunner->LogPlayerHit(Attacker, DInfo.AttackerActionChange, OffenseInfo.AttackInfo.ForceFeedBackInfo, IsBullet);

	// 为攻击者添加临时Cancel点
	for (const FCancelTagInfo& TempCancelPoint : DInfo.AttackerActionChange.TemporaryCancelPoints)
	{
		Attacker->AddCancelTag(ECancelTagType::Temporary, TempCancelPoint.CancelPointIndex, TempCancelPoint.Duration);
	}
	if(PlayerIndex==0)
	{
		SafeHitP1.fetch_add(1, std::memory_order_relaxed);
	}
	else if(PlayerIndex==1)
	{
		SafeHitP2.fetch_add(1, std::memory_order_relaxed);
	}

	// 触发成就信号
	TryBeepAchievementByHit(Attacker, DInfo.AttackerActionChange.AchievementBeep);
	TryBeepAchievementByHit(Attacker, DInfo.DefenderActionChange.AchievementBeep);

	return PlayerIndex;
}

void UECSDamageLib::ProcessECSKillLogic(AAwCharacter* Attacker,FDamageInfo& DInfo,std::atomic<int32> &SafeKillP1)
{
	SafeKillP1.fetch_add(1, std::memory_order_relaxed);
	// 处理攻击者的OnKill Buff效果
	for (int bi = 0; bi < Attacker->CharacterObj.Buff.Num(); bi++)
	{
		UDamageManager::DealBuff(Attacker, DInfo, Attacker->CharacterObj.Buff[bi], Attacker->CharacterObj.Buff[bi].Model.OnKill);
	}
}
