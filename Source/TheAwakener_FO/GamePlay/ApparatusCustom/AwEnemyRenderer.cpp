// The copyright of this project belongs to Shanghai Minghuan Software Technology Co., Ltd


#include "AwEnemyRenderer.h"

#include "BubbleSphere.h"
#include "EnemySpawner.h"
#include "Located.h"
#include "Rendering.h"
#include "Rotated.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/Optimization/StatsDeclare.h"
#include "Traits/Health.h"
#include "Traits/JustSpawned.h"
#include "Traits/TrAnimation.h"
#include "Traits/TrCommand.h"
#include "WorldPartition/ContentBundle/ContentBundleLog.h"

DEFINE_STAT(STAT_SurvivorAnimateStateHit);
DEFINE_STAT(STAT_SurvivorAnimateStateHitBack);
DEFINE_STAT(STAT_SurvivorAnimateState);

void AAwEnemyRenderer::ProcessTimeCount()
{
	RunFaceToTimeCount++;
	if (RunFaceToTimeCount>=RunFaceToInterval)
	{
		TimeToRunFaceTo = true;
		RunFaceToTimeCount = 0;
	}
}

void AAwEnemyRenderer::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);
	ProcessTimeCount();
	SCOPE_CYCLE_COUNTER(STAT_SurvivorAnimateState);
	const auto Mechanism = GetMechanism();
	FFilter Filter = FFilter::Make<FAC_Hit>();
	Filter += TraitRenderer->TraitType;
	WorldtTimeSecond = GetWorld()->GetTimeSeconds();
	//初始化
	{
		Mechanism->Operate([&](FSolidSubjectHandle Subject, const FJustSpawned&, const FRendering& Rendering, FHealth* Health)
		{
			// ResetData[MaterialUvScaleDataIndex] = BubbleSphere.Radius / 200;
			// Rendering.Owner->SetCustomData(Rendering.InstanceId, ResetData);
		if (Health)
		{
			UE_LOG(LogTemp,Log,TEXT("Subject id:%i hp:%d"),Subject.GetId(),Health->Value);
		}
		Subject.RemoveTraitDeferred<FJustSpawned>();
		TraitRenderer->SetCustomDataValue(Rendering.InstanceId, 0,A_Idle);
		TraitRenderer->SetCustomDataValue(Rendering.InstanceId, 1,WorldtTimeSecond);
		});
	}
	//受击
	{
		Filter = FFilter::Make<FAC_Hit>();
		Filter += TraitRenderer->TraitType;
		Filter.Exclude<FAC_HitUp,FJustSpawned>();
		FTrAnimationState StateHit = FTrAnimationState{A_Hit,WorldtTimeSecond,WorldtTimeSecond + AD_Hit};
		FTrAnimationState StateHitAir = FTrAnimationState{A_HitAir,WorldtTimeSecond,WorldtTimeSecond + AD_HitAir};
		FTrAnimationState StateDead = FTrAnimationState{A_Dead,WorldtTimeSecond,WorldtTimeSecond + AD_Dead};
		// const auto Applicator = Mechanism->CreateDeferredsApplicator();
		Mechanism->Operate(Filter,
		[&](FSolidSubjectHandle Subject,const FRendering& Rendering, FAS_InAir* InAir,FHealth* Health)
		{
			{
				SCOPE_CYCLE_COUNTER(STAT_SurvivorAnimateStateHit);
				if (!Health && !InAir)
				{
					Subject.SetTraitDeferred(StateDead);
					TraitRenderer->SetCustomDataValue(Rendering.InstanceId,0,StateDead.Current);
				}
				else if (!InAir)
				{
					Subject.SetTraitDeferred(StateHit);
					TraitRenderer->SetCustomDataValue(Rendering.InstanceId,0,StateHit.Current);
				}
				else 
				{
					Subject.SetTraitDeferred(StateHitAir);
					TraitRenderer->SetCustomDataValue(Rendering.InstanceId,0,StateHitAir.Current);
				}
				TraitRenderer->SetCustomDataValue(Rendering.InstanceId,1,WorldtTimeSecond);
				Subject.RemoveTraitDeferred<FAC_Hit>();
				RemoveOtherState(Subject);
				// State->StartTime += DeltaTime * TimeMultiplier;
			}

		});
	}

	//击飞
	Filter = FFilter::Make<FAC_HitUp>();
	Filter += TraitRenderer->TraitType;
	//Filter.Exclude<FAS_InAir>();
	WorldtTimeSecond = GetWorld()->GetTimeSeconds();
	{
		// const auto Applicator = Mechanism->CreateDeferredsApplicator();
		FTrAnimationState StateHitUp;
		StateHitUp.Current = A_HitUp;
		StateHitUp.EndTime = WorldtTimeSecond + AD_HitUp;
		StateHitUp.StartTime = WorldtTimeSecond;
		Mechanism->Operate(Filter,
		[&](FSolidSubjectHandle Subject,const FRendering& Rendering)
		{
			RemoveOtherState(Subject);
			Subject.RemoveTraitDeferred<FAC_Hit>();
			Subject.RemoveTraitDeferred<FAC_HitUp>();
			Subject.SetTraitDeferred(StateHitUp);
			TraitRenderer->SetCustomDataValue(Rendering.InstanceId,0,StateHitUp.Current);
			TraitRenderer->SetCustomDataValue(Rendering.InstanceId,1,WorldtTimeSecond);
		});
	}
	//落地
	Filter = FFilter::Make<FAC_Landed>();
	Filter += TraitRenderer->TraitType;
	Filter.Exclude<FAC_Hit,FAC_HitUp>();
	{
		// const auto Applicator = Mechanism->CreateDeferredsApplicator();
		Mechanism->Operate(Filter,
	   [&](FSolidSubjectHandle Subject,const FRendering& Rendering,const FLocated& Located)
		{
			RemoveOtherState(Subject);
			const auto State = Subject.GetTraitPtr<FTrAnimationState, EParadigm::Unsafe>();
			if (Subject.HasTrait<FAS_InAir_Hitted>()){
				State->Current = A_Landing_Hitted;
				State->StartTime = WorldtTimeSecond;
				State->EndTime = WorldtTimeSecond + AD_Landing_Hitted;
				PlayFallFX(Located.Location);
			}
			else
			{
				State->Current = A_Landing;
				State->StartTime = WorldtTimeSecond;
				State->EndTime = WorldtTimeSecond + AD_Landing;
				PlayLandFX(Located.Location);
			}
			Subject.RemoveTraitDeferred<FAC_Landed>();
			TraitRenderer->SetCustomDataValue(Rendering.InstanceId,0,State->Current);
			TraitRenderer->SetCustomDataValue(Rendering.InstanceId,1,WorldtTimeSecond);
	   });
	}
	//攻击
	Filter = FFilter::Make<FAC_Attack>();
	Filter += TraitRenderer->TraitType;
	{
		
		// const auto Applicator = Mechanism->CreateDeferredsApplicator();
		FTrAnimationState StateAttack;
		StateAttack.Current = A_Attack;
		StateAttack.EndTime = WorldtTimeSecond + AD_Attack;
		StateAttack.StartTime = WorldtTimeSecond;
		Mechanism->Operate(Filter,
	   [&](FSolidSubjectHandle Subject,const FRendering& Rendering)
		{
			   RemoveOtherState(Subject);
			Subject.SetTraitDeferred(StateAttack);
			   TraitRenderer->SetCustomDataValue(Rendering.InstanceId,0,StateAttack.Current);
			TraitRenderer->SetCustomDataValue(Rendering.InstanceId,1,WorldtTimeSecond);
	   });
	}
	{
		//起跑、结束
		Filter = FFilter::Make<FRendering>();
		Filter += TraitRenderer->TraitType;
		Mechanism->Operate(Filter,
		   [&](FSolidSubjectHandle Subject,const FRendering& Rendering,FTrAnimationState& State)
		{
		   	bool IsChanged = false;
		   	if (Subject.HasTrait<FAC_Run>())
		   	{
		   		// const auto State = Subject.GetTraitPtr<FTrAnimationState, EParadigm::Unsafe>();
				State.Current = A_Run;
				Subject.RemoveTraitDeferred<FAC_Run>();
				Subject.RemoveTraitDeferred<FAS_Idle>();
		   		Subject.SetTraitDeferred(FAS_Run());
		   		State.EndTime = -1;
		   		IsChanged = true;
		   	}
		   	else if (Subject.HasTrait<FAC_Idle>())
		   	{
		   		State.Current = A_Idle;
				Subject.RemoveTraitDeferred<FAC_Idle>();
				Subject.RemoveTraitDeferred<FAS_Run>();
		   		Subject.SetTraitDeferred(FAS_Idle());
		   		State.EndTime = -1;
		   		IsChanged = true;
		   	}
		   	if (IsChanged)
		   	{
		   		State.StartTime = WorldtTimeSecond;
				   TraitRenderer->SetCustomDataValue(Rendering.InstanceId,0,State.Current);
		   	}
			// TraitRenderer->SetCustomDataValue(Rendering.InstanceId,1,WorldtTimeSecond);
		});
	}
	//循环状态 AirLoop，AirLoop_Hitted,Move,Idle
	// warning : 如果有不对，可以往前移，作为保底状态
	Filter = FFilter::Make<FRendering>();
	Filter += TraitRenderer->TraitType;
	Filter.Exclude<FAC_Hit,FAC_HitUp,FAC_Landed>();
	{
		// const auto Applicator = Mechanism->CreateDeferredsApplicator();
		SCOPE_CYCLE_COUNTER(STAT_SurvivorAnimateStateHitBack);
		Mechanism->Operate(Filter,
	   [&](FSolidSubjectHandle Subject,const FRendering& Rendering,const FLocated& Located,const FBubbleSphere& Sphere)
		{
			const auto State = Subject.GetTraitPtr<FTrAnimationState, EParadigm::Unsafe>();
			bool IsLoop = true;
			if(State->EndTime>0 && State->EndTime<WorldtTimeSecond)
			{
				if (Subject.HasFlag(EFlagmarkBit::D) && !Subject.HasTrait<FAS_InAir>())
				{
					Subject.SetFlag(EFlagmarkBit::C);
				}
				if (Subject.HasTrait<FAS_InAir_Hitted>())
				{
					if (Subject.HasTrait<FAS_InAir>())
					{
 						State->Current = A_AirLoop_Hitted;
					}
					else
					{
						State->Current = A_Lie;
						State->EndTime = WorldtTimeSecond + AD_Lie;
						IsLoop = false;
						Subject.SetTraitDeferred(FAS_Lie{});
						TraitRenderer->SetCustomDataValue(Rendering.InstanceId,1,State->EndTime);
						Subject.RemoveTraitDeferred<FAS_InAir_Hitted>();
					}
				}
				else if (Subject.HasTrait<FAS_Lie>())
				{
					State->Current = A_Getup;
					State->EndTime = WorldtTimeSecond + AD_Getup;
					IsLoop = false;
					Subject.RemoveTraitDeferred<FAS_Lie>();
					TraitRenderer->SetCustomDataValue(Rendering.InstanceId,1,State->EndTime);
				}
				else if (Subject.HasTrait<FAS_InAir>())
				{
					State->Current = A_AirLoop;
				}
				else if (Subject.HasTrait<FAS_Run>())
				{
					State->Current = A_Run;
					Subject.SetTraitDeferred(FAS_Run());
				}
				else
				{
					RemoveOtherState(Subject);
					State->Current = A_Idle;
					Subject.SetTraitDeferred(FAS_Idle());
				}
				if (IsLoop)
				{
					State->EndTime = -1;
				}
				State->StartTime = WorldtTimeSecond;
				TraitRenderer->SetCustomDataValue(Rendering.InstanceId,0,State->Current);
				// TraitRenderer->SetCustomDataValue(Rendering.InstanceId,1,WorldtTimeSecond);
			}
	   });
	}
	//死亡
	{
		Filter = FFilter::Make<FRendering>(EFlagmarkBit::C);
		Filter += TraitRenderer->TraitType;
		Filter.Exclude<FAC_Hit,FAC_HitUp,FAC_Landed>();
		// const auto Applicator = Mechanism->CreateDeferredsApplicator();
		SCOPE_CYCLE_COUNTER(STAT_SurvivorAnimateStateHitBack);
		Mechanism->Operate(Filter,
		[&](FSolidSubjectHandle Subject,const FLocated& Located,const FBubbleSphere& Sphere)
		{
			PlayDeadFX(Located.Location + FVector::UpVector * Sphere.Radius);
			AEnemySpawner::Instance->OnEnemyDead(Subject);
			Subject.DespawnDeferred();
		});
	}
	if (TimeToRunFaceTo)
	{
		TimeToRunFaceTo = false;
		Mechanism->OperateConcurrently([&](FSolidSubjectHandle Subject,const FLocated& Locate,FRotated& Rotate,FAC_FaceTo& FaceTo)
		{
			SCOPE_CYCLE_COUNTER(STAT_Survivor_FaceTo);
			FVector TargetPos;
			FAC_Approaching* Approaching = Subject.GetTraitPtr<FAC_Approaching, EParadigm::Unsafe>();
			if (Approaching)
			{
				TargetPos = Approaching->TargetLocation;
			}
			else
			{
				TargetPos = FaceTo.Position;
			}
			Rotate.Rotation = FRotator(0.f, (Locate.Location-TargetPos).RotateAngleAxis(90, FVector::UpVector).Rotation().Yaw, 0.f).Quaternion();;
		},ThreadsCount,BaseBatchSize);
	}
	// void UECSDamageLib::RotateToForceComing(FSubjectHandle& Subject, FVector Force)
	// {
	// 	FRotated* Rotate = Subject.GetTraitPtr<FRotated, EParadigm::Unsafe>();
	// 	Rotate->Rotation = FRotator(0.f, Force.RotateAngleAxis(90, FVector::UpVector).Rotation().Yaw, 0.f).Quaternion();;
	// }
	//
	// 	RotateToForceComing(Subject,ForceMoveInfo.Velocity);
}

void AAwEnemyRenderer::RemoveOtherState(const FSolidSubjectHandle& Subject)
{
	Subject.RemoveTraitDeferred<FAS_Idle>();
	Subject.RemoveTraitDeferred<FAS_Run>();
	Subject.RemoveTraitDeferred<FAC_Approaching>();
}
