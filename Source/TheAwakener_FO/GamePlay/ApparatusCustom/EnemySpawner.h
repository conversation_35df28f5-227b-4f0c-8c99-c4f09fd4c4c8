// The copyright of this project belongs to Shanghai Minghuan Software Technology Co., Ltd

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "Traits/RenderBatches.h"
#include "EnemySpawner.generated.h"

USTRUCT(BlueprintType)
//同种怪物共享且不会变动的值
struct THEAWAKENER_FO_API FEnemyMobProp
{
	GENERATED_BODY()
	UPROPERTY(BlueprintReadOnly,EditAnywhere, Category = Enemy)
	FString ID;
	UPROPERTY(BlueprintReadOnly,EditAnywhere, Category = Enemy)
	UScriptStruct* Trait;
	UPROPERTY(BlueprintReadOnly,EditAnywhere, Category = Enemy, meta=(ToolTip="从模型脚底到怪物坐标中心的高度。传统怪物使用Capsule Collision，模型脚底比坐标要低，每个模型会有不一样。Character mesh的设置"))
	float HeightOffset;
	UPROPERTY(BlueprintReadOnly,EditAnywhere, Category = Enemy, meta=(ToolTip="传统怪物有额外旋转，每个模型会有不一样。Character mesh的设置"))
	float RotOffset;
	UPROPERTY(BlueprintReadOnly,EditAnywhere, Category = Enemy)
	float ReplaceRange;
	UPROPERTY(BlueprintReadOnly,EditAnywhere, Category = Enemy)
	float AimingRange;
	UPROPERTY(BlueprintReadOnly,EditAnywhere, Category = Enemy)
	float ApproachRange;
	UPROPERTY(BlueprintReadOnly,EditAnywhere, Category = Enemy, meta=(ToolTip="这个距离以内不走，只要比StopRadius小，就可以避免反复起步"))
	float ApproachRangeMin;
	UPROPERTY(BlueprintReadOnly,EditAnywhere, Category = Enemy, meta=(ToolTip="走到这个距离停"))
	float ApproachRangeStopRadius;
	UPROPERTY(BlueprintReadOnly,EditAnywhere, Category = Enemy)
	//怪物变体ID，生成怪物时注册FStringPool
	FString AlterId;
	UPROPERTY(BlueprintReadOnly,EditAnywhere, Category = Enemy)
	int MaxActorNum = 5;
};
USTRUCT(BlueprintType)
struct THEAWAKENER_FO_API FEnemyConfig
{
    GENERATED_BODY()
 
  public:

	/**
	 * The chance of spawning the enemy.
	 */
	UPROPERTY(EditAnywhere, Category = Enemy)
	float SpawnWeight = 1;

	/**
	 * The actual adjusted spawn weight,
	 * resolved at the start of the 
	 */
	float ActualSpawnWeight;

	/**
	 * The cost of spawning this enemy.
	 */
	UPROPERTY(EditAnywhere, Category = Enemy)
	int32 Cost = 1;

	/**
	 * The main enemy composition prefab.
	 */
	UPROPERTY(EditAnywhere, Category = Enemy)
	FSubjectRecord EnemyRecord;
};

UCLASS()
class THEAWAKENER_FO_API AEnemySpawner : public AActor
{
	GENERATED_BODY()
	
public:	
	// Sets default values for this actor's properties
	AEnemySpawner();
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	float SpawnTestNum = 10;
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	float SpawnRadius;
	static AEnemySpawner* Instance;
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	int SpawnPointIterationsMax = 128;

	/**
	 * All of the available enemy configurations.
	 */
	UPROPERTY(BlueprintReadOnly,EditAnywhere, Category = Enemies)
	TArray<FEnemyConfig> EnemyConfigs;
	UPROPERTY(BlueprintReadOnly,EditAnywhere, Category = Enemies)
	TArray<FEnemyMobProp> EnemyMobProps;
	uint32 SpawnId = 0;
	int32 CurrentBatchIndex = 0;
protected:
	// Called when the game starts or when spawned
	virtual void BeginPlay() override
	{
		Instance = this;
		Super::BeginPlay();
	}
	virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override
	{
		if (Instance == this)
		{
			Instance = nullptr;
		}

		Super::EndPlay(EndPlayReason);
	}

public:	
	// Called every frame
	virtual void Tick(float DeltaTime) override;
	UFUNCTION(BlueprintCallable,DisplayName="SpawnIn00")
	void SpawnEnemy(int Index);
	UFUNCTION(BlueprintCallable)
	void SpawnEnemyComplex(int Index,FVector SpawnPoint2D,float Radius,FString AlterId);
	UFUNCTION(BlueprintCallable)
	void TestSpawn();
	/**
	 * Get the current instance of the spawner.
	 */
	UFUNCTION(BlueprintCallable, BlueprintPure)
	static AEnemySpawner*
	GetInstance()
	{
		return Instance;
	}

	static FVector2D VRand2D()
	{
		FVector2D Result;
		float Length;

		do
		{
			Result.X = FMath::FRand() * 2.0f - 1.0f;
			Result.Y = FMath::FRand() * 2.0f - 1.0f;
			Length = Result.SizeSquared();
		}
		while ((Length > 1.0f) || (Length < KINDA_SMALL_NUMBER));

		return Result * (1.0f / FMath::Sqrt(Length));
	}
	static FVector2D VRandomCircle();
	void UpdateRenderState(std::function<void(void)> UpdatedCallback);
	//因为EnemySpawn存了StringID和TraitId的关系，所以先用这边处理
	UFUNCTION()
    void OnEnemyDead(FSolidSubjectHandle& Handle);
	UFUNCTION(BlueprintImplementableEvent)
	void CallBPOnEnemyDead(const FString& MobId, FVector Location, const FString& AlterId);
	UFUNCTION(BlueprintCallable,BlueprintPure)
	FString GetMobIdByTrait(UScriptStruct* Trait);
	UFUNCTION(BlueprintCallable,BlueprintPure)
	FEnemyMobProp GetMobPropById(FString MobId);
	UFUNCTION(BlueprintCallable,BlueprintPure)
	int GetMobIndexById(FString MobId);
};
