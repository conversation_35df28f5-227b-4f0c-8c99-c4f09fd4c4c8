#pragma once
 
#include "CoreMinimal.h"
#include "Speed.generated.h"
 
/**
 * The movement speed factor.
 */
USTRUCT(BlueprintType)
struct THEAWAKENER_FO_API FTrSpeed
{
	GENERATED_BODY()
 
  public:

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FVector Value = FVector::Zero();
};

USTRUCT(BlueprintType)
struct THEAWAKENER_FO_API FTrGravity
{
	GENERATED_BODY()
 
  public:

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	float Value = 9.81f;
};

USTRUCT(BlueprintType)
struct THEAWAKENER_FO_API FTrGroundCheck
{
	GENERATED_BODY()
 
public:
	int cooldown = 0;
	const static int CooldownMax = 3;
};
