#pragma once
 
#include "CoreMinimal.h"
#include "Math/Vector.h"

#include "Move.generated.h"
 
/**
 * The move command.
 */
USTRUCT(BlueprintType)
struct THEAWAKENER_FO_API FMove
{
	GENERATED_BODY()
 
  public:
 
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FVector Velocity = FVector::ZeroVector;
};
//
// USTRUCT(BlueprintType)
// struct THEAWAKENER_FO_API FApproachRange
// {
// 	GENERATED_BODY()
//  
// public:
// 	UPROPERTY(BlueprintReadWrite, EditAnywhere,meta=(ToolTip ="未启用"))
// 	float Min = 0.0f;
// 	UPROPERTY(BlueprintReadWrite, EditAnywhere)
// 	float Max = 0.0f;
// };
// USTRUCT(BlueprintType)
// struct THEAWAKENER_FO_API FAimingRange
// {
// 	GENERATED_BODY()
//  
// public:
// 	UPROPERTY(BlueprintReadWrite, EditAnywhere,meta=(ToolTip ="未启用"))
// 	float Min = 0.0f;
// 	UPROPERTY(BlueprintReadWrite, EditAnywhere)
// 	float Max = 0.0f;
// };
// USTRUCT(BlueprintType)
// //换成Actor 怪物
// struct THEAWAKENER_FO_API FReplaceRange
// {
// 	GENERATED_BODY()
// public:
// 	UPROPERTY(BlueprintReadWrite, EditAnywhere,meta=(ToolTip ="未启用"))
// 	float Min = 0.0f;
// 	UPROPERTY(BlueprintReadWrite, EditAnywhere)
// 	float Max = 0.0f;
// };
