#pragma once
 
#include "CoreMinimal.h"
#include "TrAnimation.generated.h"
 
/**
 * The main enemy trait.
 */
USTRUCT(BlueprintType)
struct THEAWAKENER_FO_API FTrAnimation
{
	GENERATED_BODY()
 
  public:
	// index of animation in texture renderer
	UPROPERTY(EditAnywhere);
	int8 Idle = 0;
	UPROPERTY(EditAnywhere);
	int8 Run = 1;
	UPROPERTY(EditAnywhere);
	int8 Fall_Loop = 2;
	UPROPERTY(EditAnywhere);
	int8 Fall_Land = 3;
	UPROPERTY(EditAnywhere);
	int8 Hit = 4;
	UPROPERTY(EditAnywhere);
	int8 Hit_Up = 5;
	UPROPERTY(EditAnywhere);
	int8 Hit_Air = 6;
	UPROPERTY(EditAnywhere);
	int8 Hit_Air_Loop = 7;
	UPROPERTY(EditAnywhere);
	int8 Hit_Air_Land = 8;
	UPROPERTY(EditAnywhere);
	int8 Dead = 9;
	UPROPERTY(EditAnywhere);
	int8 Attack = 10;

};

USTRUCT(BlueprintType)
struct THEAWAKENER_FO_API FTrAnimationState
{
	GENERATED_BODY();
public:
	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	float Current = 0;
	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	double StartTime = 0;
	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	double EndTime = 0;
};
// USTRUCT(BlueprintType)
// struct THEAWAKENER_FO_API FTrHitting
// {
// 	GENERATED_BODY();
// public:
// 	UPROPERTY(EditAnyWhere)
// 	float Duration;
// };
// USTRUCT(BlueprintType)
// struct THEAWAKENER_FO_API FTrLanding
// {
// 	GENERATED_BODY();
// public:
// 	UPROPERTY(EditAnyWhere)
// 	float Duration;
// };
// USTRUCT(BlueprintType)
// struct THEAWAKENER_FO_API FTrAttacking
// {
// 	GENERATED_BODY();
// public:
// 	UPROPERTY(EditAnyWhere)
// 	float Duration;
// };
// USTRUCT(BlueprintType,Category="Animation State")
// struct THEAWAKENER_FO_API FAS_Idle
// {
// 	GENERATED_BODY();
// };
USTRUCT(BlueprintType,Category="Animation State")
struct THEAWAKENER_FO_API FAS_Idle
{
	GENERATED_BODY();
};
USTRUCT(BlueprintType,Category="Animation State")
struct THEAWAKENER_FO_API FAS_Run
{
	GENERATED_BODY();
};
USTRUCT(BlueprintType,Category="Animation State")
struct THEAWAKENER_FO_API FAS_InAir
{
	GENERATED_BODY();
	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	float Duration;
};
USTRUCT(BlueprintType,Category="Animation State")
struct THEAWAKENER_FO_API FAS_InAir_Hitted
{
	GENERATED_BODY();
};
USTRUCT(BlueprintType,Category="Animation State")
struct THEAWAKENER_FO_API FAS_Lie
{
	GENERATED_BODY();
};
USTRUCT(BlueprintType,Category="Animation State")
struct THEAWAKENER_FO_API FAS_OnGround
{
	GENERATED_BODY();
	float GroundZ;
};