#pragma once
 
#include "CoreMinimal.h"

#include "RenderBatches.generated.h"


/**
 * Should be batched.
 */
USTRUCT(BlueprintType)
struct THEAWAKENER_FO_API FRenderBatched
{
	GENERATED_BODY()
 
  public:
};

/**
 * The first render batch.
 */
USTRUCT(BlueprintType)
struct THEAWAKENER_FO_API FRenderBatch_GoblinFighter
{
	GENERATED_BODY()
 
  public:
};

/**
 * The second render batch.
 */
USTRUCT(BlueprintType)
struct THEAWAKENER_FO_API FRenderBatchB
{
	GENERATED_BODY()
 
  public:
};


/**
 * The third render batch.
 */
USTRUCT(BlueprintType)
struct THEAWAKENER_FO_API FRenderBatchC
{
	GENERATED_BODY()
 
  public:
};

/**
 * The fourth render batch.
 */
USTRUCT(BlueprintType)
struct THEAWAKENER_FO_API FRenderBatchD
{
	GENERATED_BODY()
 
  public:
};
