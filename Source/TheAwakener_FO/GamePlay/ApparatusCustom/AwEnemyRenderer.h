 // The copyright of this project belongs to Shanghai Minghuan Software Technology Co., Ltd

#pragma once

#include "CoreMinimal.h"
#include "MechanicalActor.h"
#include "TraitRendererComponent.h"
#include "AwEnemyRenderer.generated.h"
DECLARE_STATS_GROUP(TEXT("TheAwakenerSurvivorAnim"), STATGROUP_TheAwakenerSurvivorAnim, STATCAT_Advanced);
DECLARE_CYCLE_STAT_EXTERN(TEXT("Animate State Hit"),
						  STAT_SurvivorAnimateStateHit,
						  STATGROUP_TheAwakenerSurvivorAnim,
						  THEAWAKENER_FO_API);
DECLARE_CYCLE_STAT_EXTERN(TEXT("Animate State Hitback"),
						  STAT_SurvivorAnimateStateHitBack,
						  STATGROUP_TheAwakenerSurvivorAnim,
						  THEAWAKENER_FO_API);
DECLARE_CYCLE_STAT_EXTERN(TEXT("Animate State"),
						  STAT_SurvivorAnimateState,
						  STATGROUP_TheAwakenerSurvivorAnim,
						  THEAWAKENER_FO_API);

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API AAwEnemyRenderer : public AMechanicalActor
{
	GENERATED_BODY()
public:
	/**
	 * The number of threads for concurrent operations.
	 */
	UPROPERTY(EditAnywhere, Category = Performance)
	int32 ThreadsCount = 4;

	/**
	 * The number of entities to batch as part of a separate thread.
	 */
	UPROPERTY(EditAnywhere, Category = Performance)
	int32 BaseBatchSize = 128;
	
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	float TimeMultiplier = 30;
	bool TimeToRunFaceTo;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int RunFaceToInterval = 3;
	int RunFaceToTimeCount = 0;
	void ProcessTimeCount();
	virtual void Tick(float DeltaTime) override;
	UFUNCTION(BlueprintImplementableEvent)
	void PlayDeadFX(FVector Location);
	UFUNCTION(BlueprintImplementableEvent)
	void PlayLandFX(FVector Location);
	UFUNCTION(BlueprintImplementableEvent)
	void PlayFallFX(FVector Location);
	void RemoveOtherState(const FSolidSubjectHandle& Subject);
	UPROPERTY(EditAnywhere, BlueprintReadOnly)
	UTraitRendererComponent* TraitRenderer = nullptr;
	UPROPERTY(BlueprintReadWrite, EditAnywhere,Category="Animation Index")
	float A_Idle;
	UPROPERTY(BlueprintReadWrite, EditAnywhere,Category="Animation Index")
	float A_Run;
	UPROPERTY(BlueprintReadWrite, EditAnywhere,Category="Animation Index")
	float A_AirLoop;
	UPROPERTY(BlueprintReadWrite, EditAnywhere,Category="Animation Index")
	float A_Landing;
	UPROPERTY(BlueprintReadWrite, EditAnywhere,Category="Animation Index")
	float A_Hit;
	UPROPERTY(BlueprintReadWrite, EditAnywhere,Category="Animation Index")
	float A_HitUp;
	UPROPERTY(BlueprintReadWrite, EditAnywhere,Category="Animation Index")
	float A_HitAir;
	UPROPERTY(BlueprintReadWrite, EditAnywhere,Category="Animation Index")
	float A_AirLoop_Hitted;
	UPROPERTY(BlueprintReadWrite, EditAnywhere,Category="Animation Index")
	float A_Landing_Hitted;
	UPROPERTY(BlueprintReadWrite, EditAnywhere,Category="Animation Index")
	float A_Dead;
	UPROPERTY(BlueprintReadWrite, EditAnywhere,Category="Animation Index")
	float A_Attack;
	UPROPERTY(BlueprintReadWrite, EditAnywhere,Category="Animation Index")
	float A_Lie;
	UPROPERTY(BlueprintReadWrite, EditAnywhere,Category="Animation Index")
	float A_Getup;

	UPROPERTY(BlueprintReadWrite, EditAnywhere,Category="Animation Duration")
	float AD_Attack;
	UPROPERTY(BlueprintReadWrite, EditAnywhere,Category="Animation Duration")
	float AD_Hit;
	UPROPERTY(BlueprintReadWrite, EditAnywhere,Category="Animation Duration")
	float AD_HitAir;
	UPROPERTY(BlueprintReadWrite, EditAnywhere,Category="Animation Duration")
	float AD_HitUp;
	UPROPERTY(BlueprintReadWrite, EditAnywhere,Category="Animation Duration")
	float AD_Landing;
	UPROPERTY(BlueprintReadWrite, EditAnywhere,Category="Animation Duration")
	float AD_Landing_Hitted;
	UPROPERTY(BlueprintReadWrite, EditAnywhere,Category="Animation Duration")
	float AD_Dead;
	UPROPERTY(BlueprintReadWrite, EditAnywhere,Category="Animation Duration")
	float AD_Getup;
	UPROPERTY(BlueprintReadWrite, EditAnywhere,Category="Animation Duration")
	float AD_Lie;
	AAwEnemyRenderer()
	{
		TraitRenderer = CreateDefaultSubobject<UTraitRendererComponent>(TEXT("TraitRenderer"));
		// TraitRenderer->TraitType = TraitType;
		// TraitRenderer->SetStaticMesh(StaticMesh);
	}

	double WorldtTimeSecond;
};
