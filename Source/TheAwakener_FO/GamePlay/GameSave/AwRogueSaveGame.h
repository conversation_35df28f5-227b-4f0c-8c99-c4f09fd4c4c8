// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/SaveGame.h"
#include "TheAwakener_FO/GameFramework/Manager/AwRogueDataSystem.h"
#include "AwRogueSaveGame.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UAwRogueSaveGame : public USaveGame
{
	GENERATED_BODY()

public:
	// 玩家存档
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FRogueDataInfo> RogueDataInfos;
	
	// 设置存档
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FSettingDataInfo SettingDataInfo;

	// 进入游戏次数
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int EnterGameTime = 0;
};
