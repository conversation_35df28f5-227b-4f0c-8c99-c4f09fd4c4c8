// Fill out your copyright notice in the Description page of Project Settings.


#include "AwakerSaveData.h"

#include "Kismet/GameplayStatics.h"

UAwakerSaveData::UAwakerSaveData()
{
	SaveFileName = TEXT("GameSaveData");
	SlotIndex = 0;
}

void UAwakerSaveData::Save() const
{
	//USaveGame* SGame = UGameplayStatics::LoadGameFromSlot(SaveFileName, SlotIndex);
	//if (!SGame) SGame = UGameplayStatics::CreateSaveGameObject(UAwakerSaveData::StaticClass());
	//UGameplayStatics::SaveGameToSlot(this, SaveFileName, SlotIndex);
}