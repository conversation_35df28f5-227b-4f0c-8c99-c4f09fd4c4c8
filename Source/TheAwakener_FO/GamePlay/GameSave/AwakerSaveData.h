// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/SaveGame.h"
#include "TheAwakener_FO/GamePlay/Player/AwPlayerInfo.h"
#include "AwakerSaveData.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UAwakerSaveData : public USaveGame
{
	GENERATED_BODY()
public:

	/**
	 * @brief 
	 */
	UPROPERTY(BlueprintReadWrite)
	TArray<FAwPlayerInfo> Players;

	UPROPERTY()
	FString SaveFileName;

	UPROPERTY()
	uint32 SlotIndex;

	UAwakerSaveData();

	void Save() const;
};
