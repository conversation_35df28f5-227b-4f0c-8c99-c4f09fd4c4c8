# Monster Setup Tool

怪物场景设置工具是一个基于组件系统的轻量级解决方案，让您可以轻松在关卡中设置和管理怪物分布。

## 文件结构

```
MonsterSetupTool/
├── Core/                           # 核心功能
│   ├── MonsterSetupTypes.h         # 类型定义和枚举
│   ├── MonsterSetupComponent.h/.cpp # 主要组件类
│   └── MonsterWeightGroupAsset.h/.cpp # 权重组资产
├── Editor/                         # 编辑器功能
│   ├── MonsterSetupToolEditorModule.h/.cpp # 编辑器模块
│   └── MonsterSetupComponentVisualizer.h/.cpp # 组件可视化器
├── BlueprintLibrary/               # 蓝图函数库
│   └── MonsterSetupBlueprintLibrary.h/.cpp # 蓝图接口
├── Documentation/                  # 文档
│   └── MonsterSetupTool_QuickStart.md # 快速开始指南
└── README.md                       # 本文件
```

## 核心特性

✅ **简单易用** - 基于熟悉的组件系统和Details面板操作
✅ **快速开发** - 5-6周开发周期，维护成本低
✅ **权重随机** - 支持7种预设权重组和自定义配置
✅ **可视化** - 方格密度显示和分组颜色边框
✅ **蓝图友好** - 完整的蓝图函数库支持

## 预设权重组

### 基础权重组
- **基础**: 地面基本怪物（骷髅士兵、兽人战士、兽人弓箭手）
- **飞行**: 空中怪物
- **肉盾**: 高血量防御型怪物
- **自爆**: 自爆型怪物

### 组合权重组
- **基础+飞行**: 地面和空中怪物混合
- **基础+肉盾**: 普通怪物配少量坦克
- **基础+自爆**: 普通怪物配少量自爆兵

## 快速开始

### 1. 添加组件
在Actor上添加`MonsterSetupComponent`组件，或使用蓝图函数库：

```cpp
UMonsterSetupComponent* Component = UMonsterSetupBlueprintLibrary::CreateMonsterSetupAtLocation(
    MyActor,
    FVector(100, 200, 0),
    "GuardGroup",
    "基础"
);
```

### 2. 配置参数
在Details面板中设置：
- **怪物分组名称**: "GuardPost"
- **权重组名称**: "基础"
- **怪物密度**: 0.7
- **影响半径**: 300.0
- **分组颜色**: 红色

### 3. 使用笔刷工具
```cpp
Component->BrushSize = 150.0f;        // 笔刷大小
Component->BrushStrength = 1.2f;      // 笔刷强度
Component->BrushMode = EMonsterBrushMode::Paint;  // 绘制模式

// 在指定位置绘制怪物密度
Component->PaintMonstersAtLocation(FVector(500, 300, 0));
```

### 4. 生成怪物
```cpp
// 生成所有怪物
int32 TotalGenerated = UMonsterSetupBlueprintLibrary::GenerateAllMonsters(GetWorld());

// 生成指定分组的怪物
int32 GroupGenerated = UMonsterSetupBlueprintLibrary::GenerateMonstersForGroup(GetWorld(), "GuardPost");
```

## 编辑器功能

### 可视化显示
- 密度点显示为彩色圆点，颜色深浅表示密度强度
- 影响范围显示为圆圈边框
- 分组边框使用分组颜色
- 密度网格显示为方格，颜色深浅表示密度分布

### 交互操作
- **Ctrl+点击**: 添加新的密度点
- **Shift+点击**: 使用笔刷绘制
- **选择密度点**: 点击密度点进行选择和编辑
- **Delete键**: 删除选中的密度点

## 常用蓝图节点

### 查询节点
- `Get All Monster Setup Components` - 获取所有组件
- `Get Monster Setup Components By Group` - 按分组获取组件
- `Get All Group Names` - 获取所有分组名称
- `Get World Statistics` - 获取世界统计信息

### 操作节点
- `Create Monster Setup At Location` - 在位置创建设置
- `Copy Monster Setup To Locations` - 复制设置到多个位置
- `Generate All Monsters` - 生成所有怪物
- `Clear All Monsters` - 清除所有怪物

### 工具节点
- `Is Valid Monster Location` - 检查位置有效性
- `Find Nearest Valid Location` - 查找最近有效位置
- `Get Available Monster Types` - 获取可用怪物类型

## 性能优化建议

1. **合理设置影响半径** - 避免过大的影响范围
2. **控制密度点数量** - 每个组件建议不超过100个密度点
3. **限制同时生成的怪物数量** - 使用MaxMonsterCount限制
4. **定期清理无效引用** - 调用CleanupInvalidMonsterReferences()

## 调试功能

```cpp
// 打印组件信息
UMonsterSetupBlueprintLibrary::PrintComponentInfo(MyComponent);

// 打印世界摘要
UMonsterSetupBlueprintLibrary::PrintWorldMonsterSetupSummary(GetWorld());

// 绘制调试信息
UMonsterSetupBlueprintLibrary::DrawDebugInfo(GetWorld(), 10.0f);

// 验证配置
TArray<FString> Errors = UMonsterSetupBlueprintLibrary::ValidateAllComponentConfigurations(GetWorld());
```

## 集成说明

这个工具设计为快速开发和易于维护，可以根据项目需求进行扩展和定制。所有文件都位于`TheAwakener_FO/GamePlay/MonsterSetupTool`目录下，遵循项目的代码组织规范。

详细的使用说明请参考 `Documentation/MonsterSetupTool_QuickStart.md` 文件。
