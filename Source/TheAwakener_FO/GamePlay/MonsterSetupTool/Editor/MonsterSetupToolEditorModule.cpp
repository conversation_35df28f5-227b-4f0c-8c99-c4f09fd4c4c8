// The copyright of this project belongs to Shanghai Minghuan Software Technology Co., Ltd

#include "MonsterSetupToolEditorModule.h"

#if WITH_EDITOR

#include "Engine/Engine.h"
#include "UnrealEd.h"
#include "ComponentVisualizers.h"
#include "MonsterSetupComponentVisualizer.h"
#include "TheAwakener_FO/GamePlay/MonsterSetupTool/Core/MonsterSetupComponent.h"
#include "AssetToolsModule.h"
#include "IAssetTools.h"

void FMonsterSetupToolEditorModule::StartupModule()
{
    UE_LOG(LogTemp, Log, TEXT("MonsterSetupToolEditorModule: Starting up"));

    RegisterComponentVisualizers();
    RegisterEditorTools();
    RegisterAssetTypes();

    UE_LOG(LogTemp, Log, TEXT("MonsterSetupToolEditorModule: Startup complete"));
}

void FMonsterSetupToolEditorModule::ShutdownModule()
{
    UE_LOG(LogTemp, Log, TEXT("MonsterSetupToolEditorModule: Shutting down"));

    UnregisterAssetTypes();
    UnregisterEditorTools();
    UnregisterComponentVisualizers();

    UE_LOG(LogTemp, Log, TEXT("MonsterSetupToolEditorModule: Shutdown complete"));
}

void FMonsterSetupToolEditorModule::RegisterComponentVisualizers()
{
    if (GUnrealEd)
    {
        // 注册MonsterSetupComponent的可视化器
        TSharedPtr<FMonsterSetupComponentVisualizer> MonsterSetupVisualizer = MakeShareable(new FMonsterSetupComponentVisualizer);
        
        if (MonsterSetupVisualizer.IsValid())
        {
            GUnrealEd->RegisterComponentVisualizer(UMonsterSetupComponent::StaticClass()->GetFName(), MonsterSetupVisualizer);
            RegisteredVisualizerNames.Add(UMonsterSetupComponent::StaticClass()->GetFName());
            
            UE_LOG(LogTemp, Log, TEXT("MonsterSetupToolEditorModule: Registered MonsterSetupComponent visualizer"));
        }
    }
}

void FMonsterSetupToolEditorModule::UnregisterComponentVisualizers()
{
    if (GUnrealEd)
    {
        for (const FName& VisualizerName : RegisteredVisualizerNames)
        {
            GUnrealEd->UnregisterComponentVisualizer(VisualizerName);
        }
        RegisteredVisualizerNames.Empty();
        
        UE_LOG(LogTemp, Log, TEXT("MonsterSetupToolEditorModule: Unregistered component visualizers"));
    }
}

void FMonsterSetupToolEditorModule::RegisterEditorTools()
{
    // 这里可以注册自定义的编辑器工具
    // 例如工具栏按钮、菜单项等
    
    UE_LOG(LogTemp, Log, TEXT("MonsterSetupToolEditorModule: Registered editor tools"));
}

void FMonsterSetupToolEditorModule::UnregisterEditorTools()
{
    // 注销编辑器工具
    
    UE_LOG(LogTemp, Log, TEXT("MonsterSetupToolEditorModule: Unregistered editor tools"));
}

void FMonsterSetupToolEditorModule::RegisterAssetTypes()
{
    // 注册MonsterWeightGroupAsset的资产类型操作
    IAssetTools& AssetTools = FModuleManager::LoadModuleChecked<FAssetToolsModule>("AssetTools").Get();
    
    // 这里可以添加自定义的资产类型操作
    // 例如为MonsterWeightGroupAsset创建专门的编辑器
    
    UE_LOG(LogTemp, Log, TEXT("MonsterSetupToolEditorModule: Registered asset types"));
}

void FMonsterSetupToolEditorModule::UnregisterAssetTypes()
{
    if (FModuleManager::Get().IsModuleLoaded("AssetTools"))
    {
        IAssetTools& AssetTools = FModuleManager::GetModuleChecked<FAssetToolsModule>("AssetTools").Get();
        
        for (auto& AssetTypeAction : RegisteredAssetTypeActions)
        {
            if (AssetTypeAction.IsValid())
            {
                AssetTools.UnregisterAssetTypeActions(AssetTypeAction.ToSharedRef());
            }
        }
        RegisteredAssetTypeActions.Empty();
    }
    
    UE_LOG(LogTemp, Log, TEXT("MonsterSetupToolEditorModule: Unregistered asset types"));
}

// 注意：这个模块不需要单独注册，编辑器功能集成在主模块中
// IMPLEMENT_MODULE(FMonsterSetupToolEditorModule, MonsterSetupToolEditor)

#endif // WITH_EDITOR
