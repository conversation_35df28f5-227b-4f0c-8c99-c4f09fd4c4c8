// The copyright of this project belongs to Shanghai Minghuan Software Technology Co., Ltd

#pragma once

#include "CoreMinimal.h"

#if WITH_EDITOR

#include "ComponentVisualizer.h"
#include "TheAwakener_FO/GamePlay/MonsterSetupTool/Core/MonsterSetupComponent.h"

/**
 * MonsterSetupComponent的可视化器
 * 在编辑器中提供可视化显示和交互功能
 */
class THEAWAKENER_FO_API FMonsterSetupComponentVisualizer : public FComponentVisualizer
{
public:
    FMonsterSetupComponentVisualizer();
    virtual ~FMonsterSetupComponentVisualizer();

    // FComponentVisualizer interface
    virtual void DrawVisualization(const UActorComponent* Component, const FSceneView* View, FPrimitiveDrawInterface* PDI) override;
    virtual bool VisProxyHandleClick(FEditorViewportClient* InViewportClient, HHitProxy* HitProxy, const FViewportClick& Click);
    virtual void EndEditing() override;
    virtual bool GetWidgetLocation(const FEditorViewportClient* ViewportClient, FVector& OutLocation) const override;
    virtual bool GetCustomInputCoordinateSystem(const FEditorViewportClient* ViewportClient, FMatrix& OutMatrix) const override;
    virtual bool HandleInputDelta(FEditorViewportClient* ViewportClient, FViewport* Viewport, FVector& DeltaTranslate, FRotator& DeltaRotate, FVector& DeltaScale) override;
    virtual bool HandleInputKey(FEditorViewportClient* ViewportClient, FViewport* Viewport, FKey Key, EInputEvent Event) override;

protected:
    /**
     * 绘制组件的基本可视化
     * @param Component 组件
     * @param PDI 绘制接口
     */
    void DrawComponentVisualization(const UMonsterSetupComponent* Component, FPrimitiveDrawInterface* PDI);

    /**
     * 绘制密度点
     * @param Component 组件
     * @param PDI 绘制接口
     */
    void DrawDensityPoints(const UMonsterSetupComponent* Component, FPrimitiveDrawInterface* PDI);

    /**
     * 绘制影响范围
     * @param Component 组件
     * @param PDI 绘制接口
     */
    void DrawInfluenceRadius(const UMonsterSetupComponent* Component, FPrimitiveDrawInterface* PDI);

    /**
     * 绘制分组边框
     * @param Component 组件
     * @param PDI 绘制接口
     */
    void DrawGroupBorder(const UMonsterSetupComponent* Component, FPrimitiveDrawInterface* PDI);

    /**
     * 绘制密度网格
     * @param Component 组件
     * @param PDI 绘制接口
     */
    void DrawDensityGrid(const UMonsterSetupComponent* Component, FPrimitiveDrawInterface* PDI);

    /**
     * 绘制生成的怪物位置
     * @param Component 组件
     * @param PDI 绘制接口
     */
    void DrawSpawnedMonsters(const UMonsterSetupComponent* Component, FPrimitiveDrawInterface* PDI);

    /**
     * 绘制笔刷预览
     * @param Component 组件
     * @param PDI 绘制接口
     * @param BrushLocation 笔刷位置
     */
    void DrawBrushPreview(const UMonsterSetupComponent* Component, FPrimitiveDrawInterface* PDI, const FVector& BrushLocation);

    /**
     * 获取密度点的颜色
     * @param Density 密度值
     * @param GroupColor 分组颜色
     */
    FLinearColor GetDensityPointColor(float Density, const FLinearColor& GroupColor) const;

    /**
     * 处理密度点的点击
     * @param Component 组件
     * @param ClickLocation 点击位置
     * @param bIsCtrlPressed 是否按下Ctrl键
     * @param bIsShiftPressed 是否按下Shift键
     */
    void HandleDensityPointClick(UMonsterSetupComponent* Component, const FVector& ClickLocation, bool bIsCtrlPressed, bool bIsShiftPressed);

    /**
     * 获取鼠标在世界中的位置
     * @param ViewportClient 视口客户端
     * @param Viewport 视口
     */
    FVector GetMouseWorldLocation(FEditorViewportClient* ViewportClient, FViewport* Viewport) const;

private:
    /** 当前编辑的组件 */
    TWeakObjectPtr<UMonsterSetupComponent> EditingComponent;

    /** 是否正在编辑模式 */
    bool bIsEditing;

    /** 当前选中的密度点索引 */
    int32 SelectedDensityPointIndex;

    /** 笔刷预览位置 */
    FVector BrushPreviewLocation;

    /** 是否显示笔刷预览 */
    bool bShowBrushPreview;
};

/**
 * 密度点的HitProxy
 */
struct HDensityPointProxy : public HHitProxy
{
    DECLARE_HIT_PROXY();

    HDensityPointProxy(int32 InPointIndex) : HHitProxy(HPP_UI), PointIndex(InPointIndex) {}

    int32 PointIndex;
};

/**
 * 组件中心的HitProxy
 */
struct HMonsterSetupComponentProxy : public HHitProxy
{
    DECLARE_HIT_PROXY();

    HMonsterSetupComponentProxy() : HHitProxy(HPP_UI) {}
};

#endif // WITH_EDITOR
