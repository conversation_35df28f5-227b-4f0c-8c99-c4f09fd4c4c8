// The copyright of this project belongs to Shanghai Minghuan Software Technology Co., Ltd

#include "MonsterSetupComponentVisualizer.h"

#if WITH_EDITOR

#include "Engine/Engine.h"
#include "SceneManagement.h"
#include "EditorViewportClient.h"
#include "Framework/Application/SlateApplication.h"
#include "Engine/HitResult.h"
#include "SceneView.h"
#include "UnrealEdGlobals.h"
#include "Editor/UnrealEdEngine.h"
#include "Engine/Scene.h"

IMPLEMENT_HIT_PROXY(HDensityPointProxy, HHitProxy);
IMPLEMENT_HIT_PROXY(HMonsterSetupComponentProxy, HHitProxy);

FMonsterSetupComponentVisualizer::FMonsterSetupComponentVisualizer()
    : FComponentVisualizer()
    , bIsEditing(false)
    , SelectedDensityPointIndex(-1)
    , BrushPreviewLocation(FVector::ZeroVector)
    , bShowBrushPreview(false)
{
}

FMonsterSetupComponentVisualizer::~FMonsterSetupComponentVisualizer()
{
}

void FMonsterSetupComponentVisualizer::DrawVisualization(const UActorComponent* Component, const FSceneView* View, FPrimitiveDrawInterface* PDI)
{
    const UMonsterSetupComponent* MonsterSetupComponent = Cast<const UMonsterSetupComponent>(Component);
    if (!MonsterSetupComponent)
    {
        return;
    }

    DrawComponentVisualization(MonsterSetupComponent, PDI);
}

void FMonsterSetupComponentVisualizer::DrawComponentVisualization(const UMonsterSetupComponent* Component, FPrimitiveDrawInterface* PDI)
{
    if (!Component || !Component->GetOwner())
    {
        return;
    }

    // 绘制组件中心
    FVector ComponentLocation = Component->GetOwner()->GetActorLocation();
    PDI->SetHitProxy(new HMonsterSetupComponentProxy());
    PDI->DrawPoint(ComponentLocation, Component->GroupColor.ToFColor(true), 10.0f, SDPG_World);
    PDI->SetHitProxy(nullptr);

    // 绘制影响范围
    if (Component->bShowGroupBorder)
    {
        DrawInfluenceRadius(Component, PDI);
    }

    // 绘制密度点
    if (Component->bShowDensityVisualization)
    {
        DrawDensityPoints(Component, PDI);
        DrawDensityGrid(Component, PDI);
    }

    // 绘制生成的怪物
    DrawSpawnedMonsters(Component, PDI);

    // 绘制笔刷预览
    if (bShowBrushPreview)
    {
        DrawBrushPreview(Component, PDI, BrushPreviewLocation);
    }
}

void FMonsterSetupComponentVisualizer::DrawDensityPoints(const UMonsterSetupComponent* Component, FPrimitiveDrawInterface* PDI)
{
    for (int32 i = 0; i < Component->DensityPoints.Num(); i++)
    {
        const FMonsterDensityPoint& DensityPoint = Component->DensityPoints[i];
        
        FLinearColor PointColor = GetDensityPointColor(DensityPoint.Density, Component->GroupColor);
        FColor DrawColor = PointColor.ToFColor(true);

        // 设置HitProxy用于点击检测
        PDI->SetHitProxy(new HDensityPointProxy(i));

        // 绘制密度点
        float PointSize = FMath::Lerp(5.0f, 15.0f, DensityPoint.Density);
        PDI->DrawPoint(DensityPoint.Location, DrawColor, PointSize, SDPG_World);

        // 绘制密度点影响范围
        const int32 CircleSegments = 16;
        TArray<FVector> CirclePoints;
        for (int32 j = 0; j <= CircleSegments; j++)
        {
            float Angle = (j / float(CircleSegments)) * 2.0f * PI;
            FVector Offset = FVector(FMath::Cos(Angle), FMath::Sin(Angle), 0.0f) * DensityPoint.InfluenceRadius;
            CirclePoints.Add(DensityPoint.Location + Offset);
        }

        for (int32 j = 0; j < CirclePoints.Num() - 1; j++)
        {
            PDI->DrawLine(CirclePoints[j], CirclePoints[j + 1], DrawColor, SDPG_World, 1.0f);
        }

        PDI->SetHitProxy(nullptr);

        // 如果是选中的密度点，绘制特殊标记
        if (i == SelectedDensityPointIndex)
        {
            PDI->DrawPoint(DensityPoint.Location, FColor::White, PointSize + 5.0f, SDPG_Foreground);
        }
    }
}

void FMonsterSetupComponentVisualizer::DrawInfluenceRadius(const UMonsterSetupComponent* Component, FPrimitiveDrawInterface* PDI)
{
    if (!Component->GetOwner())
    {
        return;
    }

    FVector ComponentLocation = Component->GetOwner()->GetActorLocation();
    FColor BorderColor = Component->GroupColor.ToFColor(true);

    // 绘制影响半径圆圈
    const int32 CircleSegments = 32;
    TArray<FVector> CirclePoints;
    for (int32 i = 0; i <= CircleSegments; i++)
    {
        float Angle = (i / float(CircleSegments)) * 2.0f * PI;
        FVector Offset = FVector(FMath::Cos(Angle), FMath::Sin(Angle), 0.0f) * Component->InfluenceRadius;
        CirclePoints.Add(ComponentLocation + Offset);
    }

    for (int32 i = 0; i < CirclePoints.Num() - 1; i++)
    {
        PDI->DrawLine(CirclePoints[i], CirclePoints[i + 1], BorderColor, SDPG_World, 2.0f);
    }
}

void FMonsterSetupComponentVisualizer::DrawDensityGrid(const UMonsterSetupComponent* Component, FPrimitiveDrawInterface* PDI)
{
    if (!Component->GetOwner())
    {
        return;
    }

    FVector ComponentLocation = Component->GetOwner()->GetActorLocation();
    float GridSize = Component->VisualizationGridSize;
    float HalfRadius = Component->InfluenceRadius;
    
    // 计算网格范围
    int32 GridCount = FMath::CeilToInt(HalfRadius / GridSize);
    
    for (int32 x = -GridCount; x <= GridCount; x++)
    {
        for (int32 y = -GridCount; y <= GridCount; y++)
        {
            FVector GridCenter = ComponentLocation + FVector(x * GridSize, y * GridSize, 0.0f);
            
            // 检查是否在影响范围内
            if (FVector::Dist2D(GridCenter, ComponentLocation) > HalfRadius)
            {
                continue;
            }

            // 获取该位置的密度值
            float Density = Component->GetDensityAtLocation(GridCenter);
            
            if (Density > 0.01f)
            {
                FLinearColor GridColor = GetDensityPointColor(Density, Component->GroupColor);
                GridColor.A = 0.3f; // 半透明
                
                // 绘制方格
                float HalfGrid = GridSize * 0.4f;
                TArray<FVector> SquarePoints = {
                    GridCenter + FVector(-HalfGrid, -HalfGrid, 0),
                    GridCenter + FVector(HalfGrid, -HalfGrid, 0),
                    GridCenter + FVector(HalfGrid, HalfGrid, 0),
                    GridCenter + FVector(-HalfGrid, HalfGrid, 0),
                    GridCenter + FVector(-HalfGrid, -HalfGrid, 0)
                };

                for (int32 i = 0; i < SquarePoints.Num() - 1; i++)
                {
                    PDI->DrawLine(SquarePoints[i], SquarePoints[i + 1], GridColor.ToFColor(true), SDPG_World, 1.0f);
                }
            }
        }
    }
}

void FMonsterSetupComponentVisualizer::DrawSpawnedMonsters(const UMonsterSetupComponent* Component, FPrimitiveDrawInterface* PDI)
{
    for (const FSpawnedMonsterInfo& MonsterInfo : Component->SpawnedMonsters)
    {
        if (MonsterInfo.MonsterActor.IsValid())
        {
            FVector MonsterLocation = MonsterInfo.MonsterActor->GetActorLocation();
            
            // 绘制怪物位置标记
            PDI->DrawPoint(MonsterLocation, FColor::Yellow, 8.0f, SDPG_World);
            
            // 绘制连接线到组件
            if (Component->GetOwner())
            {
                FVector ComponentLocation = Component->GetOwner()->GetActorLocation();
                PDI->DrawLine(ComponentLocation, MonsterLocation, FColor::Yellow, SDPG_World, 0.5f);
            }
        }
    }
}

void FMonsterSetupComponentVisualizer::DrawBrushPreview(const UMonsterSetupComponent* Component, FPrimitiveDrawInterface* PDI, const FVector& BrushLocation)
{
    FColor BrushColor = FColor::White;
    
    switch (Component->BrushMode)
    {
    case EMonsterBrushMode::Paint:
        BrushColor = FColor::Green;
        break;
    case EMonsterBrushMode::Erase:
        BrushColor = FColor::Red;
        break;
    case EMonsterBrushMode::Smooth:
        BrushColor = FColor::Blue;
        break;
    }

    // 绘制笔刷圆圈
    const int32 CircleSegments = 24;
    TArray<FVector> CirclePoints;
    for (int32 i = 0; i <= CircleSegments; i++)
    {
        float Angle = (i / float(CircleSegments)) * 2.0f * PI;
        FVector Offset = FVector(FMath::Cos(Angle), FMath::Sin(Angle), 0.0f) * Component->BrushSize;
        CirclePoints.Add(BrushLocation + Offset);
    }

    for (int32 i = 0; i < CirclePoints.Num() - 1; i++)
    {
        PDI->DrawLine(CirclePoints[i], CirclePoints[i + 1], BrushColor, SDPG_Foreground, 2.0f);
    }

    // 绘制笔刷中心点
    PDI->DrawPoint(BrushLocation, BrushColor, 6.0f, SDPG_Foreground);
}

FLinearColor FMonsterSetupComponentVisualizer::GetDensityPointColor(float Density, const FLinearColor& GroupColor) const
{
    // 根据密度值调整颜色强度
    FLinearColor BaseColor = GroupColor;
    BaseColor.A = FMath::Clamp(Density, 0.2f, 1.0f);
    
    // 密度越高，颜色越亮
    float Brightness = FMath::Lerp(0.3f, 1.0f, Density);
    BaseColor.R *= Brightness;
    BaseColor.G *= Brightness;
    BaseColor.B *= Brightness;
    
    return BaseColor;
}

bool FMonsterSetupComponentVisualizer::VisProxyHandleClick(FEditorViewportClient* InViewportClient, HHitProxy* HitProxy, const FViewportClick& Click)
{
    if (HitProxy)
    {
        if (HitProxy->IsA(HDensityPointProxy::StaticGetType()))
        {
            HDensityPointProxy* DensityPointProxy = static_cast<HDensityPointProxy*>(HitProxy);
            SelectedDensityPointIndex = DensityPointProxy->PointIndex;
            
            UE_LOG(LogTemp, Log, TEXT("Selected density point: %d"), SelectedDensityPointIndex);
            return true;
        }
        else if (HitProxy->IsA(HMonsterSetupComponentProxy::StaticGetType()))
        {
            SelectedDensityPointIndex = -1;
            UE_LOG(LogTemp, Log, TEXT("Selected monster setup component"));
            return true;
        }
    }

    return false;
}

void FMonsterSetupComponentVisualizer::EndEditing()
{
    bIsEditing = false;
    SelectedDensityPointIndex = -1;
    EditingComponent.Reset();
}

bool FMonsterSetupComponentVisualizer::GetWidgetLocation(const FEditorViewportClient* ViewportClient, FVector& OutLocation) const
{
    if (EditingComponent.IsValid() && EditingComponent->GetOwner())
    {
        if (SelectedDensityPointIndex >= 0 && SelectedDensityPointIndex < EditingComponent->DensityPoints.Num())
        {
            OutLocation = EditingComponent->DensityPoints[SelectedDensityPointIndex].Location;
        }
        else
        {
            OutLocation = EditingComponent->GetOwner()->GetActorLocation();
        }
        return true;
    }
    return false;
}

bool FMonsterSetupComponentVisualizer::GetCustomInputCoordinateSystem(const FEditorViewportClient* ViewportClient, FMatrix& OutMatrix) const
{
    OutMatrix = FMatrix::Identity;
    return false;
}

bool FMonsterSetupComponentVisualizer::HandleInputDelta(FEditorViewportClient* ViewportClient, FViewport* Viewport, FVector& DeltaTranslate, FRotator& DeltaRotate, FVector& DeltaScale)
{
    if (EditingComponent.IsValid() && SelectedDensityPointIndex >= 0 && SelectedDensityPointIndex < EditingComponent->DensityPoints.Num())
    {
        // 移动选中的密度点
        EditingComponent->DensityPoints[SelectedDensityPointIndex].Location += DeltaTranslate;
        return true;
    }
    
    return false;
}

bool FMonsterSetupComponentVisualizer::HandleInputKey(FEditorViewportClient* ViewportClient, FViewport* Viewport, FKey Key, EInputEvent Event)
{
    if (Event == IE_Pressed)
    {
        if (Key == EKeys::LeftMouseButton)
        {
            // 简化处理：在组件位置附近添加密度点
            if (EditingComponent.IsValid() && EditingComponent->GetOwner())
            {
                bool bIsCtrlPressed = ViewportClient->IsCtrlPressed();
                bool bIsShiftPressed = ViewportClient->IsShiftPressed();

                FVector ComponentLocation = EditingComponent->GetOwner()->GetActorLocation();
                // 在组件位置附近随机偏移添加密度点
                FVector RandomOffset = FMath::VRand() * 100.0f;
                RandomOffset.Z = 0.0f;
                FVector ClickLocation = ComponentLocation + RandomOffset;

                HandleDensityPointClick(EditingComponent.Get(), ClickLocation, bIsCtrlPressed, bIsShiftPressed);
                return true;
            }
        }
        else if (Key == EKeys::Delete && SelectedDensityPointIndex >= 0)
        {
            // 删除选中的密度点
            if (EditingComponent.IsValid() && SelectedDensityPointIndex < EditingComponent->DensityPoints.Num())
            {
                EditingComponent->DensityPoints.RemoveAt(SelectedDensityPointIndex);
                SelectedDensityPointIndex = -1;
                return true;
            }
        }
    }

    return false;
}

void FMonsterSetupComponentVisualizer::HandleDensityPointClick(UMonsterSetupComponent* Component, const FVector& ClickLocation, bool bIsCtrlPressed, bool bIsShiftPressed)
{
    if (!Component)
    {
        return;
    }

    if (bIsCtrlPressed)
    {
        // Ctrl+点击：添加新的密度点
        Component->AddDensityPoint(ClickLocation, Component->MonsterDensity, Component->BrushSize * 0.5f);
        UE_LOG(LogTemp, Log, TEXT("Added density point at %s"), *ClickLocation.ToString());
    }
    else if (bIsShiftPressed)
    {
        // Shift+点击：使用笔刷绘制
        Component->PaintMonstersAtLocation(ClickLocation);
        UE_LOG(LogTemp, Log, TEXT("Painted monsters at %s"), *ClickLocation.ToString());
    }
}

FVector FMonsterSetupComponentVisualizer::GetMouseWorldLocation(FEditorViewportClient* ViewportClient, FViewport* Viewport) const
{
    FVector MouseWorldLocation = FVector::ZeroVector;

    if (ViewportClient && Viewport)
    {
        // 简化实现：使用当前编辑组件的位置作为基准
        if (EditingComponent.IsValid() && EditingComponent->GetOwner())
        {
            MouseWorldLocation = EditingComponent->GetOwner()->GetActorLocation();
        }
        else
        {
            // 如果没有编辑组件，返回原点
            MouseWorldLocation = FVector::ZeroVector;
        }
    }

    return MouseWorldLocation;
}

#endif // WITH_EDITOR
