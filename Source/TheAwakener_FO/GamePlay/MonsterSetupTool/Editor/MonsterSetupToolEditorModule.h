// The copyright of this project belongs to Shanghai Minghuan Software Technology Co., Ltd

#pragma once

#include "CoreMinimal.h"

#if WITH_EDITOR

#include "Modules/ModuleInterface.h"
#include "Modules/ModuleManager.h"

/**
 * 怪物设置工具编辑器模块
 * 提供编辑器相关功能，如可视化和工具栏集成
 */
class THEAWAKENER_FO_API FMonsterSetupToolEditorModule : public IModuleInterface
{
public:
    /** IModuleInterface implementation */
    virtual void StartupModule() override;
    virtual void ShutdownModule() override;

    /**
     * 获取模块实例
     */
    static FMonsterSetupToolEditorModule& Get()
    {
        return FModuleManager::LoadModuleChecked<FMonsterSetupToolEditorModule>("MonsterSetupToolEditor");
    }

    /**
     * 检查模块是否已加载
     */
    static bool IsAvailable()
    {
        return FModuleManager::Get().IsModuleLoaded("MonsterSetupToolEditor");
    }

private:
    /**
     * 注册组件可视化器
     */
    void RegisterComponentVisualizers();

    /**
     * 注销组件可视化器
     */
    void UnregisterComponentVisualizers();

    /**
     * 注册编辑器工具
     */
    void RegisterEditorTools();

    /**
     * 注销编辑器工具
     */
    void UnregisterEditorTools();

    /**
     * 注册资产类型
     */
    void RegisterAssetTypes();

    /**
     * 注销资产类型
     */
    void UnregisterAssetTypes();

private:
    /** 注册的可视化器名称列表 */
    TArray<FName> RegisteredVisualizerNames;

    /** 注册的资产类型操作 */
    TArray<TSharedPtr<class IAssetTypeActions>> RegisteredAssetTypeActions;
};

#endif // WITH_EDITOR
