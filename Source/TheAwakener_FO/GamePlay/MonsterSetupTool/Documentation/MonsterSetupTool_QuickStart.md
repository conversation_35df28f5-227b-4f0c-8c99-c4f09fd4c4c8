# 怪物场景设置工具 - 快速开始指南

## 概述

怪物场景设置工具（方案B）是一个基于组件系统的轻量级解决方案，让您可以轻松在关卡中设置和管理怪物分布。

## 核心特性

✅ **简单易用** - 基于熟悉的组件系统和Details面板操作  
✅ **快速开发** - 5-6周开发周期，维护成本低  
✅ **权重随机** - 支持7种预设权重组和自定义配置  
✅ **可视化** - 方格密度显示和分组颜色边框  
✅ **蓝图友好** - 完整的蓝图函数库支持  

## 快速开始

### 1. 基础使用

#### 添加怪物设置组件
```cpp
// 在C++中创建
UMonsterSetupComponent* Component = CreateDefaultSubobject<UMonsterSetupComponent>(TEXT("MonsterSetup"));

// 或使用蓝图函数库
UMonsterSetupComponent* Component = UMonsterSetupBlueprintLibrary::CreateMonsterSetupAtLocation(
    MyA<PERSON>, 
    FVector(100, 200, 0), 
    "GuardGroup", 
    "基础"
);
```

#### 在编辑器中配置
1. 选择Actor并添加 `MonsterSetupComponent`
2. 在Details面板中设置：
   - **怪物分组名称**: "GuardPost"
   - **权重组名称**: "基础" 
   - **怪物密度**: 0.7
   - **影响半径**: 300.0
   - **分组颜色**: 红色

### 2. 笔刷工具使用

#### 配置笔刷参数
```cpp
Component->BrushSize = 150.0f;        // 笔刷大小
Component->BrushStrength = 1.2f;      // 笔刷强度
Component->BrushMode = EMonsterBrushMode::Paint;  // 绘制模式
```

#### 使用笔刷绘制
```cpp
// 在指定位置绘制怪物密度
Component->PaintMonstersAtLocation(FVector(500, 300, 0));

// 使用当前组件位置绘制
Component->PaintWithBrush();

// 擦除怪物密度
Component->EraseWithBrush();
```

### 3. 权重组配置

#### 使用预设权重组
```cpp
// 创建预设权重组
UMonsterWeightGroupAsset* BasicGroup = UMonsterSetupBlueprintLibrary::CreatePresetWeightGroup(
    EPresetWeightGroupType::Basic
);

UMonsterWeightGroupAsset* FlyingGroup = UMonsterSetupBlueprintLibrary::CreatePresetWeightGroup(
    EPresetWeightGroupType::Flying
);
```

#### 创建自定义权重组
```cpp
TArray<FMonsterWeightConfig> CustomWeights;
CustomWeights.Add(FMonsterWeightConfig(0, 0.4f));  // 骷髅士兵 40%
CustomWeights.Add(FMonsterWeightConfig(1, 0.3f));  // 兽人战士 30%
CustomWeights.Add(FMonsterWeightConfig(2, 0.3f));  // 兽人弓箭手 30%

UMonsterWeightGroupAsset* CustomGroup = UMonsterSetupBlueprintLibrary::CreateCustomWeightGroup(
    "MyCustomGroup",
    CustomWeights,
    "自定义混合怪物组"
);
```

### 4. 批量操作

#### 批量创建怪物设置
```cpp
TArray<FVector> Locations = {
    FVector(100, 100, 0),
    FVector(200, 200, 0),
    FVector(300, 300, 0)
};

TArray<UMonsterSetupComponent*> Components = UMonsterSetupBlueprintLibrary::BatchCreateMonsterSetups(
    Locations,
    "DefenseGroup",
    "基础+飞行"
);
```

#### 批量生成怪物
```cpp
// 生成所有怪物
int32 TotalGenerated = UMonsterSetupBlueprintLibrary::GenerateAllMonsters(GetWorld());

// 生成指定分组的怪物
int32 GroupGenerated = UMonsterSetupBlueprintLibrary::GenerateMonstersForGroup(GetWorld(), "GuardPost");

// 清除所有怪物
int32 TotalCleared = UMonsterSetupBlueprintLibrary::ClearAllMonsters(GetWorld());
```

## 预设权重组说明

### 基础权重组
- **基础**: 地面基本怪物（骷髅士兵、兽人战士、兽人弓箭手）
- **飞行**: 空中怪物
- **肉盾**: 高血量防御型怪物
- **自爆**: 自爆型怪物

### 组合权重组
- **基础+飞行**: 地面和空中怪物混合
- **基础+肉盾**: 普通怪物配少量坦克
- **基础+自爆**: 普通怪物配少量自爆兵

## 常用蓝图节点

### 查询节点
- `Get All Monster Setup Components` - 获取所有组件
- `Get Monster Setup Components By Group` - 按分组获取组件
- `Get All Group Names` - 获取所有分组名称
- `Get World Statistics` - 获取世界统计信息

### 操作节点
- `Create Monster Setup At Location` - 在位置创建设置
- `Copy Monster Setup To Locations` - 复制设置到多个位置
- `Generate All Monsters` - 生成所有怪物
- `Clear All Monsters` - 清除所有怪物

### 工具节点
- `Is Valid Monster Location` - 检查位置有效性
- `Find Nearest Valid Location` - 查找最近有效位置
- `Get Available Monster Types` - 获取可用怪物类型

## 实际应用示例

### 示例1：设置守卫哨所
```cpp
void SetupGuardPost()
{
    // 创建守卫组件
    UMonsterSetupComponent* GuardComponent = UMonsterSetupBlueprintLibrary::CreateMonsterSetupAtLocation(
        MyActor,
        FVector(1000, 1000, 0),
        "GuardPost",
        "基础+肉盾"
    );
    
    // 配置参数
    GuardComponent->InfluenceRadius = 400.0f;
    GuardComponent->MonsterDensity = 0.8f;
    GuardComponent->GroupColor = FLinearColor::Yellow;
    
    // 在周围绘制密度点
    TArray<FVector> GuardPositions = {
        FVector(1000, 1000, 0),
        FVector(1100, 1000, 0),
        FVector(1000, 1100, 0),
        FVector(900, 1000, 0),
        FVector(1000, 900, 0)
    };
    
    for (const FVector& Pos : GuardPositions)
    {
        GuardComponent->PaintMonstersAtLocation(Pos);
    }
    
    // 生成怪物
    GuardComponent->GenerateMonstersFromDensity();
}
```

### 示例2：设置区域防守
```cpp
void SetupAreaDefense()
{
    TArray<FVector> DefensePoints;

    // 创建防守点位
    for (int32 i = 0; i < 5; i++)
    {
        for (int32 j = 0; j < 5; j++)
        {
            DefensePoints.Add(FVector(i * 300, j * 300, 0));
        }
    }

    // 批量创建防守组件
    TArray<UMonsterSetupComponent*> DefenseComponents = UMonsterSetupBlueprintLibrary::BatchCreateMonsterSetups(
        DefensePoints,
        "AreaDefense",
        "基础+肉盾"
    );

    // 配置每个组件
    for (UMonsterSetupComponent* Component : DefenseComponents)
    {
        Component->InfluenceRadius = 200.0f;
        Component->MonsterDensity = 0.6f;
        Component->GroupColor = FLinearColor::Blue;
        Component->PaintWithBrush();
    }

    // 生成防守怪物
    UMonsterSetupBlueprintLibrary::GenerateMonstersForGroup(GetWorld(), "AreaDefense");
}
```

### 示例3：动态怪物管理
```cpp
void ManageDynamicMonsters()
{
    // 获取统计信息
    FString WorldStats = UMonsterSetupBlueprintLibrary::GetWorldStatistics(GetWorld());
    UE_LOG(LogTemp, Log, TEXT("%s"), *WorldStats);
    
    // 检查是否需要重新生成
    int32 CurrentMonsterCount = UMonsterSetupBlueprintLibrary::GetTotalSpawnedMonsterCount(GetWorld());
    
    if (CurrentMonsterCount < 50)
    {
        // 怪物数量不足，重新生成
        UMonsterSetupBlueprintLibrary::GenerateAllMonsters(GetWorld());
    }
    else if (CurrentMonsterCount > 200)
    {
        // 怪物过多，清除一些
        UMonsterSetupBlueprintLibrary::ClearMonstersForGroup(GetWorld(), "DefenseArea");
    }
}
```

## 调试和优化

### 调试功能
```cpp
// 打印组件信息
UMonsterSetupBlueprintLibrary::PrintComponentInfo(MyComponent);

// 打印世界摘要
UMonsterSetupBlueprintLibrary::PrintWorldMonsterSetupSummary(GetWorld());

// 绘制调试信息
UMonsterSetupBlueprintLibrary::DrawDebugInfo(GetWorld(), 10.0f);

// 验证配置
TArray<FString> Errors = UMonsterSetupBlueprintLibrary::ValidateAllComponentConfigurations(GetWorld());
```

### 性能优化建议
1. **合理设置影响半径** - 避免过大的影响范围
2. **控制密度点数量** - 每个组件建议不超过100个密度点
3. **限制同时生成的怪物数量** - 使用MaxMonsterCount限制
4. **定期清理无效引用** - 调用CleanupInvalidMonsterReferences()

## 下一步

1. **编译项目** - 确保所有文件正确编译
2. **创建测试关卡** - 在测试关卡中验证功能
3. **配置权重组** - 根据项目需求创建自定义权重组
4. **集成到工作流程** - 将工具集成到现有的关卡设计流程中

这个工具设计为快速开发和易于维护，您可以根据项目需求进行扩展和定制。
